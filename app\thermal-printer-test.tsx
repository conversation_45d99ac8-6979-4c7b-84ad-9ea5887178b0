import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import ThermalPrintingTestUtils, { TestSuite } from '../src/utils/ThermalPrintingTestUtils';
import ThermalPrintService from '../src/services/ThermalPrintService';
import EnhancedThermalPrintService from '../src/services/EnhancedThermalPrintService';

export default function ThermalPrinterTestScreen() {
  const router = useRouter();
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<TestSuite[]>([]);
  const [quickTesting, setQuickTesting] = useState(false);

  const handleRunAllTests = async () => {
    setTesting(true);
    try {
      const results = await ThermalPrintingTestUtils.runAllTests();
      setTestResults(results);
    } catch (error) {
      Alert.alert('Test Error', 'Failed to run tests: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setTesting(false);
    }
  };

  const handleQuickTest = async () => {
    setQuickTesting(true);
    try {
      const result = await ThermalPrintingTestUtils.quickConnectivityTest();
      Alert.alert(
        'Quick Test Result',
        result ? 'Connectivity test passed! ✅' : 'Connectivity test failed! ❌'
      );
    } catch (error) {
      Alert.alert('Test Error', 'Failed to run quick test: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setQuickTesting(false);
    }
  };

  const handleTestPrint = async () => {
    try {
      const testData = ThermalPrintingTestUtils.generateTestReceiptData();
      const result = await ThermalPrintService.printThermalReceipt(testData);
      
      Alert.alert(
        'Test Print Result',
        result.success ? 'Test receipt printed successfully! ✅' : `Print failed: ${result.error} ❌`
      );
    } catch (error) {
      Alert.alert('Test Print Error', 'Failed to print test receipt: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const handleScanDevices = async () => {
    try {
      const devices = await ThermalPrintService.scanDevices();
      Alert.alert(
        'Device Scan Result',
        `Found ${devices.length} device(s):\n${devices.map(d => `• ${d.name} (${d.address})`).join('\n')}`
      );
    } catch (error) {
      Alert.alert('Scan Error', 'Failed to scan devices: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const getTestSuiteIcon = (suite: TestSuite) => {
    if (suite.failed === 0) return 'checkmark-circle';
    if (suite.passed === 0) return 'close-circle';
    return 'warning';
  };

  const getTestSuiteColor = (suite: TestSuite) => {
    if (suite.failed === 0) return '#4CAF50';
    if (suite.passed === 0) return '#F44336';
    return '#FF9500';
  };

  const renderTestResults = () => {
    if (testResults.length === 0) return null;

    const totalPassed = testResults.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = testResults.reduce((sum, suite) => sum + suite.failed, 0);
    const successRate = ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1);

    return (
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results</Text>
        
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Overall Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Passed:</Text>
            <Text style={[styles.summaryValue, { color: '#4CAF50' }]}>{totalPassed}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Failed:</Text>
            <Text style={[styles.summaryValue, { color: '#F44336' }]}>{totalFailed}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Success Rate:</Text>
            <Text style={[styles.summaryValue, { color: parseFloat(successRate) >= 80 ? '#4CAF50' : '#F44336' }]}>
              {successRate}%
            </Text>
          </View>
        </View>

        {testResults.map((suite, index) => (
          <View key={index} style={styles.suiteCard}>
            <View style={styles.suiteHeader}>
              <Ionicons 
                name={getTestSuiteIcon(suite)} 
                size={24} 
                color={getTestSuiteColor(suite)} 
              />
              <Text style={styles.suiteName}>{suite.suiteName}</Text>
            </View>
            
            <View style={styles.suiteStats}>
              <Text style={styles.statText}>✅ {suite.passed} passed</Text>
              <Text style={styles.statText}>❌ {suite.failed} failed</Text>
              <Text style={styles.statText}>⏱️ {suite.totalDuration}ms</Text>
            </View>

            {suite.results.filter(r => !r.passed).map((test, testIndex) => (
              <View key={testIndex} style={styles.failedTest}>
                <Text style={styles.failedTestName}>{test.testName}</Text>
                <Text style={styles.failedTestError}>{test.error}</Text>
              </View>
            ))}
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.title}>Thermal Printer Tests</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Tests</Text>
          
          <TouchableOpacity 
            style={[styles.testButton, { backgroundColor: '#4CAF50' }]}
            onPress={handleQuickTest}
            disabled={quickTesting}
          >
            {quickTesting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Ionicons name="flash" size={20} color="#fff" />
            )}
            <Text style={styles.testButtonText}>
              {quickTesting ? 'Testing...' : 'Quick Connectivity Test'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.testButton, { backgroundColor: '#2196F3' }]}
            onPress={handleScanDevices}
          >
            <Ionicons name="bluetooth" size={20} color="#fff" />
            <Text style={styles.testButtonText}>Scan for Devices</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.testButton, { backgroundColor: '#FF6B35' }]}
            onPress={handleTestPrint}
          >
            <Ionicons name="print" size={20} color="#fff" />
            <Text style={styles.testButtonText}>Test Print Receipt</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Comprehensive Tests</Text>
          
          <TouchableOpacity 
            style={[styles.testButton, { backgroundColor: '#9C27B0' }]}
            onPress={handleRunAllTests}
            disabled={testing}
          >
            {testing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Ionicons name="checkmark-done" size={20} color="#fff" />
            )}
            <Text style={styles.testButtonText}>
              {testing ? 'Running Tests...' : 'Run All Tests'}
            </Text>
          </TouchableOpacity>

          <Text style={styles.testDescription}>
            Runs comprehensive tests including permissions, services, printing, and integration tests.
          </Text>
        </View>

        {renderTestResults()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  testDescription: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: -8,
  },
  resultsContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  summaryCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  suiteCard: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  suiteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  suiteName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  suiteStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  statText: {
    fontSize: 12,
    color: '#666',
  },
  failedTest: {
    backgroundColor: '#ffebee',
    borderRadius: 4,
    padding: 8,
    marginTop: 4,
  },
  failedTestName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#d32f2f',
    marginBottom: 2,
  },
  failedTestError: {
    fontSize: 12,
    color: '#666',
  },
});
