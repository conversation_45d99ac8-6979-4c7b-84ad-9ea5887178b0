/**
 * Unified Cart Hook
 *
 * Provides a unified interface that automatically switches between
 * the legacy cart system and the new ticket system based on the current mode.
 * This ensures backward compatibility while enabling new ticket features.
 */

import { useCallback, useEffect, useRef } from "react";
import { useAppDispatch, useAppSelector } from "../store";
import {
  Cart<PERSON>tem,
  CartItemDiscount,
  Customer,
  OrderDiscount,
  Salesperson,
} from "../types/shopify";

// Legacy cart actions
import {
  addToCart,
  addTo<PERSON>art as addToLegacyCart,
  remove<PERSON>rom<PERSON>art as removeFromLegacyCart,
  updateQuantity as updateLegacyCartQuantity,
  applyItemDiscount as applyLegacyCartItemDiscount,
  updateItemNotes as updateLegacyCartItemNotes,
  clearCart as clearLegacyCart,
  set<PERSON>ust<PERSON> as setLegacyCartCustomer,
  set<PERSON><PERSON><PERSON> as setLegacyCartSalesperson,
  addDiscount as addLegacyCartDiscount,
  removeDiscount as removeLegacyCartDiscount,
  setNote as setLegacyCartNote,
  updateInventoryQuantity as updateLegacyCartInventoryQuantity,
  setLoading as setLegacyCartLoading,
  set<PERSON>rror as setLegacy<PERSON><PERSON><PERSON>rror,
  clearError as clearLeg<PERSON><PERSON><PERSON><PERSON><PERSON>r,
  select<PERSON>artItems,
  selectCartTotal,
  selectCartSubtotal,
  selectCartItemCount,
  selectCartCustomer,
  selectCartSalesperson,
  selectCartDiscounts,
  selectCartNote,
  selectCartError,
  selectCartLoading,
} from "../store/slices/cartSlice";

// Ticket system actions
import {
  addItemToTicketWithAutoCreate,
  addItemToActiveTicket,
  removeItemFromActiveTicket,
  updateItemQuantityInActiveTicket,
  applyItemDiscountInActiveTicket,
  updateItemNotesInActiveTicket,
  clearActiveTicket,
  setActiveTicketCustomer,
  setActiveTicketSalesperson,
  addDiscountToActiveTicket,
  removeDiscountFromActiveTicket,
  setActiveTicketNote,
  setLoading as setTicketsLoading,
  setError as setTicketsError,
  clearError as clearTicketsError,
  selectActiveTicket,
  selectActiveTicketId,
  selectActiveTicketItems,
  selectActiveTicketTotal,
  selectActiveTicketSubtotal,
  selectActiveTicketItemCount,
  selectActiveTicketCustomer,
  selectActiveTicketSalesperson,
  selectActiveTicketDiscounts,
  selectActiveTicketNote,
  selectTicketsError,
  selectTicketsLoading,
} from "../store/slices/ticketSlice";

import { selectCurrentUser } from "../store/slices/userSlice";

// Compatibility layer
import { selectIsTicketModeEnabled } from "../store/slices/cartCompatibilitySlice";

// Cart persistence utilities
import {
  saveCartToStorage,
  loadCartFromStorage,
  clearCartFromStorage,
  shouldRecoverCart,
  updateStoredCartItems,
  getStorageStats,
} from "../utils/cartPersistence";

export interface UnifiedCartInterface {
  // State
  items: CartItem[];
  total: number;
  subtotal: number;
  itemCount: number;
  customer?: Customer;
  salesperson?: Salesperson | null;
  discounts: OrderDiscount[];
  note?: string;
  error: string | null;
  isLoading: boolean;
  isTicketMode: boolean;

  // Actions
  addItem: (item: CartItem) => void;
  removeItem: (variantId: string) => void;
  updateQuantity: (variantId: string, quantity: number) => void;
  applyItemDiscount: (
    variantId: string,
    discount: CartItemDiscount | null
  ) => void;
  updateItemNotes: (variantId: string, notes: string) => void;
  clearCart: () => void;
  setCustomer: (customer: Customer | undefined) => void;
  setSalesperson: (salesperson: Salesperson | null) => void;
  addDiscount: (discount: OrderDiscount) => void;
  removeDiscount: (discountId: string) => void;
  setNote: (note: string) => void;
  updateInventoryQuantity: (variantId: string, quantity: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useUnifiedCart = (): UnifiedCartInterface => {
  const dispatch = useAppDispatch();

  // Check which mode we're in
  const isTicketMode = useAppSelector(selectIsTicketModeEnabled);

  // Legacy cart selectors
  const legacyItems = useAppSelector(selectCartItems);
  const legacyTotal = useAppSelector(selectCartTotal);
  const legacySubtotal = useAppSelector(selectCartSubtotal);
  const legacyItemCount = useAppSelector(selectCartItemCount);
  const legacyCustomer = useAppSelector(selectCartCustomer);
  const legacySalesperson = useAppSelector(selectCartSalesperson);
  const legacyDiscounts = useAppSelector(selectCartDiscounts);
  const legacyNote = useAppSelector(selectCartNote);
  const legacyError = useAppSelector(selectCartError);
  const legacyLoading = useAppSelector(selectCartLoading);

  // Ticket system selectors
  const ticketItems = useAppSelector(selectActiveTicketItems);
  const ticketTotal = useAppSelector(selectActiveTicketTotal);
  const ticketSubtotal = useAppSelector(selectActiveTicketSubtotal);
  const ticketItemCount = useAppSelector(selectActiveTicketItemCount);
  const ticketCustomer = useAppSelector(selectActiveTicketCustomer);
  const ticketSalesperson = useAppSelector(selectActiveTicketSalesperson);
  const ticketDiscounts = useAppSelector(selectActiveTicketDiscounts);
  const ticketNote = useAppSelector(selectActiveTicketNote);
  const ticketError = useAppSelector(selectTicketsError);
  const ticketLoading = useAppSelector(selectTicketsLoading);

  // User information for ticket creation
  const currentUser = useAppSelector(selectCurrentUser);

  // Cart recovery state
  const lastTicketIdRef = useRef<string | null>(null);
  const recoveryAttemptedRef = useRef(false);

  // Get current active ticket ID and ticket for recovery detection
  const activeTicketId = useAppSelector(selectActiveTicketId);
  const activeTicket = useAppSelector(selectActiveTicket);

  // CART RECOVERY LOGIC - Detect and recover from empty cart after ticket ID changes
  useEffect(() => {
    if (!isTicketMode) return;

    const currentItems = ticketItems;
    const currentTicketId = activeTicketId;

    // Detect ticket ID change
    const ticketIdChanged =
      lastTicketIdRef.current && lastTicketIdRef.current !== currentTicketId;

    // Check if we should recover cart from localStorage
    if (
      shouldRecoverCart(currentItems, currentTicketId || undefined) &&
      !recoveryAttemptedRef.current
    ) {
      const storedCart = loadCartFromStorage();

      if (storedCart && storedCart.items.length > 0) {
        console.log(
          `🔄 RECOVERING CART: ${storedCart.items.length} items from localStorage`
        );

        // Restore items to current ticket
        storedCart.items.forEach((item) => {
          dispatch(
            addItemToTicketWithAutoCreate({
              ...item,
              staffId: currentUser?.id,
              terminalId: (currentUser as any)?.terminalId || "terminal-1",
              locationId: (currentUser as any)?.locationId || "location-1",
            })
          );
        });

        // Restore other cart data if available
        if (storedCart.customer) {
          dispatch(setActiveTicketCustomer(storedCart.customer));
        }
        if (storedCart.salesperson) {
          dispatch(setActiveTicketSalesperson(storedCart.salesperson));
        }
        if (storedCart.note) {
          dispatch(setActiveTicketNote(storedCart.note));
        }
        // Restore order-level discounts
        if (storedCart.discounts && storedCart.discounts.length > 0) {
          storedCart.discounts.forEach((discount) => {
            dispatch(addDiscountToActiveTicket(discount));
          });
          console.log(
            `🏷️ Auto-restored ${storedCart.discounts.length} order discounts from localStorage`
          );
        }

        recoveryAttemptedRef.current = true;

        // Clear recovery flag after a delay to allow future recoveries
        setTimeout(() => {
          recoveryAttemptedRef.current = false;
        }, 5000);
      }
    }

    // Update last known ticket ID
    lastTicketIdRef.current = currentTicketId;

    // Log storage stats for debugging
    if (ticketIdChanged) {
      const stats = getStorageStats();
      console.log(`📊 Storage stats after ticket ID change:`, stats);
    }
  }, [isTicketMode, ticketItems, activeTicketId, dispatch, currentUser]);

  // CART PERSISTENCE - Save cart data to localStorage on changes (ONLY FOR ACTIVE TICKETS)
  useEffect(() => {
    if (!isTicketMode) return;

    // CRITICAL FIX: Only save ACTIVE tickets to localStorage, not completed ones
    if (!activeTicket || activeTicket.status !== "active") {
      console.log(
        `🚫 Skipping localStorage save - ticket status: ${
          activeTicket?.status || "null"
        }`
      );
      return;
    }

    // ENHANCED: Clear localStorage when cart becomes empty (consistent behavior)
    if (ticketItems.length === 0) {
      clearCartFromStorage();
      console.log(
        "🗑️ Empty cart detected - cleared localStorage for consistency"
      );
      return;
    }

    // Save current cart state to localStorage
    saveCartToStorage({
      items: ticketItems,
      customer: ticketCustomer,
      salesperson: ticketSalesperson,
      discounts: ticketDiscounts,
      note: ticketNote,
      ticketId: activeTicketId || undefined,
    });
  }, [
    isTicketMode,
    ticketItems,
    ticketCustomer,
    ticketSalesperson,
    ticketDiscounts,
    ticketNote,
    activeTicketId,
    activeTicket,
  ]);

  // Unified actions
  const addItem = useCallback(
    (item: CartItem) => {
      if (isTicketMode) {
        // SIMPLIFIED FIX: Use a custom action that handles ticket creation + item addition atomically
        const itemWithUserInfo = {
          ...item,
          staffId: currentUser?.id,
          terminalId: (currentUser as any)?.terminalId || "terminal-1",
          locationId: (currentUser as any)?.locationId || "location-1",
        };
        dispatch(addItemToTicketWithAutoCreate(itemWithUserInfo));

        // IMMEDIATELY save to localStorage as backup
        const currentStoredCart = loadCartFromStorage();
        const existingItems = currentStoredCart?.items || [];

        // Add or update item in stored cart
        const existingItemIndex = existingItems.findIndex(
          (existingItem) => existingItem.variantId === item.variantId
        );

        let updatedItems;
        if (existingItemIndex >= 0) {
          // Update existing item quantity
          updatedItems = [...existingItems];
          updatedItems[existingItemIndex] = {
            ...updatedItems[existingItemIndex],
            quantity: updatedItems[existingItemIndex].quantity + item.quantity,
          };
        } else {
          // Add new item
          updatedItems = [...existingItems, item];
        }

        updateStoredCartItems(updatedItems);
        console.log(`💾 Item immediately saved to localStorage: ${item.title}`);
      } else {
        dispatch(addToLegacyCart(item));
      }
    },
    [dispatch, isTicketMode, currentUser]
  );

  const removeItem = useCallback(
    (variantId: string) => {
      if (isTicketMode) {
        dispatch(removeItemFromActiveTicket(variantId));

        // IMMEDIATELY update localStorage as backup
        const currentStoredCart = loadCartFromStorage();
        if (currentStoredCart?.items) {
          const updatedItems = currentStoredCart.items.filter(
            (item) => item.variantId !== variantId
          );
          updateStoredCartItems(updatedItems);
          console.log(`🗑️ Item removed from localStorage: ${variantId}`);
        }
      } else {
        dispatch(removeFromLegacyCart(variantId));
      }
    },
    [dispatch, isTicketMode]
  );

  const updateQuantity = useCallback(
    (variantId: string, quantity: number) => {
      if (isTicketMode) {
        dispatch(updateItemQuantityInActiveTicket({ variantId, quantity }));

        // IMMEDIATELY update localStorage as backup
        const currentStoredCart = loadCartFromStorage();
        if (currentStoredCart?.items) {
          const updatedItems = currentStoredCart.items.map((item) =>
            item.variantId === variantId ? { ...item, quantity } : item
          );
          updateStoredCartItems(updatedItems);
          console.log(
            `📝 Item quantity updated in localStorage: ${variantId} → ${quantity}`
          );
        }
      } else {
        dispatch(updateLegacyCartQuantity({ variantId, quantity }));
      }
    },
    [dispatch, isTicketMode]
  );

  const applyItemDiscount = useCallback(
    (variantId: string, discount: CartItemDiscount | null) => {
      if (isTicketMode) {
        dispatch(applyItemDiscountInActiveTicket({ variantId, discount }));

        // IMMEDIATELY update localStorage as backup
        const currentStoredCart = loadCartFromStorage();
        if (currentStoredCart?.items) {
          const updatedItems = currentStoredCart.items.map((item) =>
            item.variantId === variantId
              ? { ...item, discount: discount || undefined }
              : item
          );
          updateStoredCartItems(updatedItems);
          console.log(`🏷️ Item discount updated in localStorage: ${variantId}`);
        }
      } else {
        dispatch(applyLegacyCartItemDiscount({ variantId, discount }));
      }
    },
    [dispatch, isTicketMode]
  );

  const updateItemNotes = useCallback(
    (variantId: string, notes: string) => {
      if (isTicketMode) {
        dispatch(updateItemNotesInActiveTicket({ variantId, notes }));
      } else {
        dispatch(updateLegacyCartItemNotes({ variantId, notes }));
      }
    },
    [dispatch, isTicketMode]
  );

  const clearCart = useCallback(() => {
    if (isTicketMode) {
      dispatch(clearActiveTicket());
      // Also clear localStorage when cart is explicitly cleared
      clearCartFromStorage();
      console.log("🗑️ Cart and localStorage cleared");
    } else {
      dispatch(clearLegacyCart());
    }
  }, [dispatch, isTicketMode]);

  const setCustomer = useCallback(
    (customer: Customer | undefined) => {
      if (isTicketMode) {
        dispatch(setActiveTicketCustomer(customer));
      } else {
        dispatch(setLegacyCartCustomer(customer));
      }
    },
    [dispatch, isTicketMode]
  );

  const setSalesperson = useCallback(
    (salesperson: Salesperson | null) => {
      if (isTicketMode) {
        dispatch(setActiveTicketSalesperson(salesperson));
      } else {
        dispatch(setLegacyCartSalesperson(salesperson));
      }
    },
    [dispatch, isTicketMode]
  );

  const addDiscount = useCallback(
    (discount: OrderDiscount) => {
      if (isTicketMode) {
        dispatch(addDiscountToActiveTicket(discount));

        // IMMEDIATELY update localStorage as backup
        const currentStoredCart = loadCartFromStorage();
        if (currentStoredCart) {
          const updatedDiscounts = [
            ...(currentStoredCart.discounts || []),
            discount,
          ];
          saveCartToStorage({
            ...currentStoredCart,
            discounts: updatedDiscounts,
          });
          console.log(
            `🏷️ Order discount added to localStorage: ${discount.type}`
          );
        }
      } else {
        dispatch(addLegacyCartDiscount(discount));
      }
    },
    [dispatch, isTicketMode]
  );

  const removeDiscount = useCallback(
    (discountId: string) => {
      if (isTicketMode) {
        dispatch(removeDiscountFromActiveTicket(discountId));

        // IMMEDIATELY update localStorage as backup
        const currentStoredCart = loadCartFromStorage();
        if (currentStoredCart) {
          const updatedDiscounts = (currentStoredCart.discounts || []).filter(
            (discount) => discount.id !== discountId
          );
          saveCartToStorage({
            ...currentStoredCart,
            discounts: updatedDiscounts,
          });
          console.log(
            `🗑️ Order discount removed from localStorage: ${discountId}`
          );
        }
      } else {
        dispatch(removeLegacyCartDiscount(discountId));
      }
    },
    [dispatch, isTicketMode]
  );

  const setNote = useCallback(
    (note: string) => {
      if (isTicketMode) {
        dispatch(setActiveTicketNote(note));
      } else {
        dispatch(setLegacyCartNote(note));
      }
    },
    [dispatch, isTicketMode]
  );

  const updateInventoryQuantity = useCallback(
    (variantId: string, quantity: number) => {
      if (isTicketMode) {
        // For ticket mode, we'll need to implement inventory updates across all tickets
        // For now, this is a placeholder
        console.warn(
          "Inventory quantity updates in ticket mode not yet implemented"
        );
      } else {
        dispatch(updateLegacyCartInventoryQuantity({ variantId, quantity }));
      }
    },
    [dispatch, isTicketMode]
  );

  const setLoading = useCallback(
    (loading: boolean) => {
      if (isTicketMode) {
        dispatch(setTicketsLoading(loading));
      } else {
        dispatch(setLegacyCartLoading(loading));
      }
    },
    [dispatch, isTicketMode]
  );

  const setError = useCallback(
    (error: string | null) => {
      if (isTicketMode) {
        dispatch(setTicketsError(error));
      } else {
        dispatch(setLegacyCartError(error));
      }
    },
    [dispatch, isTicketMode]
  );

  const clearError = useCallback(() => {
    if (isTicketMode) {
      dispatch(clearTicketsError());
    } else {
      dispatch(clearLegacyCartError());
    }
  }, [dispatch, isTicketMode]);

  // Debug utility for manual cart recovery
  const recoverCartFromStorage = useCallback(() => {
    if (!isTicketMode) return false;

    const storedCart = loadCartFromStorage();
    if (!storedCart || storedCart.items.length === 0) {
      console.log("🔍 No stored cart data to recover");
      return false;
    }

    console.log(
      `🔄 MANUAL RECOVERY: ${storedCart.items.length} items from localStorage`
    );

    // Clear current cart first
    dispatch(clearActiveTicket());

    // Restore items
    storedCart.items.forEach((item) => {
      dispatch(
        addItemToTicketWithAutoCreate({
          ...item,
          staffId: currentUser?.id,
          terminalId: (currentUser as any)?.terminalId || "terminal-1",
          locationId: (currentUser as any)?.locationId || "location-1",
        })
      );
    });

    // Restore other cart data if available
    if (storedCart.customer) {
      dispatch(setActiveTicketCustomer(storedCart.customer));
    }
    if (storedCart.salesperson) {
      dispatch(setActiveTicketSalesperson(storedCart.salesperson));
    }
    if (storedCart.note) {
      dispatch(setActiveTicketNote(storedCart.note));
    }
    // Restore order-level discounts
    if (storedCart.discounts && storedCart.discounts.length > 0) {
      storedCart.discounts.forEach((discount) => {
        dispatch(addDiscountToActiveTicket(discount));
      });
      console.log(
        `🏷️ Restored ${storedCart.discounts.length} order discounts from localStorage`
      );
    }

    return true;
  }, [isTicketMode, dispatch, currentUser]);

  // Return unified interface
  return {
    // State (automatically switches based on mode)
    items: isTicketMode ? ticketItems : legacyItems,
    total: isTicketMode ? ticketTotal : legacyTotal,
    subtotal: isTicketMode ? ticketSubtotal : legacySubtotal,
    itemCount: isTicketMode ? ticketItemCount : legacyItemCount,
    customer: isTicketMode ? ticketCustomer : legacyCustomer,
    salesperson: isTicketMode ? ticketSalesperson : legacySalesperson,
    discounts: isTicketMode ? ticketDiscounts : legacyDiscounts,
    note: isTicketMode ? ticketNote : legacyNote,
    error: isTicketMode ? ticketError : legacyError,
    isLoading: isTicketMode ? ticketLoading : legacyLoading,
    isTicketMode,

    // Actions (automatically dispatch to correct system)
    addItem,
    removeItem,
    updateQuantity,
    applyItemDiscount,
    updateItemNotes,
    clearCart,
    setCustomer,
    setSalesperson,
    addDiscount,
    removeDiscount,
    setNote,
    updateInventoryQuantity,
    setLoading,
    setError,
    clearError,

    // Debug utilities
    recoverCartFromStorage,
  };
};
