// Test direct Shopify connection
const axios = require("axios");

async function testShopifyDirect() {
  console.log("🧪 Testing Direct Shopify Connection...\n");

  const baseURL = "http://192.168.1.8:3020/api";

  try {
    // Test store info
    console.log("1️⃣ Testing store connection...");
    const storeResponse = await axios.get(`${baseURL}/store/info`);

    if (storeResponse.data.success) {
      console.log("✅ Store connection successful!");
      console.log(`   Store: ${storeResponse.data.data.store.name}`);
      console.log(`   Domain: ${storeResponse.data.data.store.domain}`);
      console.log(`   Currency: ${storeResponse.data.data.store.currency}`);
      console.log(`   Plan: ${storeResponse.data.data.store.plan}`);
    } else {
      console.log("❌ Store connection failed:", storeResponse.data.error);
      return;
    }

    // Test products
    console.log("\n2️⃣ Testing products fetch...");
    const productsResponse = await axios.get(
      `${baseURL}/store/products?limit=5`
    );

    if (productsResponse.data.success) {
      console.log("✅ Products fetch successful!");
      console.log(
        `   Found ${productsResponse.data.data.products.length} products`
      );

      if (productsResponse.data.data.products.length > 0) {
        const product = productsResponse.data.data.products[0];
        console.log(`   Sample product: ${product.title}`);
        console.log(`   Variants: ${product.variants.length}`);
        console.log(`   Price: KSh ${product.variants[0].price}`);
      }
    } else {
      console.log("❌ Products fetch failed:", productsResponse.data.error);
    }

    // Test customers
    console.log("\n3️⃣ Testing customers fetch...");
    const customersResponse = await axios.get(
      `${baseURL}/store/customers?limit=5`
    );

    if (customersResponse.data.success) {
      console.log("✅ Customers fetch successful!");
      console.log(
        `   Found ${customersResponse.data.data.customers.length} customers`
      );
    } else {
      console.log("❌ Customers fetch failed:", customersResponse.data.error);
    }
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("Response:", error.response.data);
    }
  }

  console.log("\n🎯 Direct Shopify Integration Summary:");
  console.log(
    "   - Backend connects directly to Shopify using app credentials"
  );
  console.log("   - No OAuth required for POS operations");
  console.log("   - Store data, products, and customers accessible via API");
  console.log("\n✅ Ready for POS integration!");
}

testShopifyDirect();
