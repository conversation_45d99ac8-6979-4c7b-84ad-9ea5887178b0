/**
 * POS Authentication Routes - MySQL Implementation
 * Handles staff login, logout, and token verification with database persistence
 */

require("dotenv").config();
const express = require("express");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const { authService } = require("../middleware/auth-mysql");

const router = express.Router();

// Staff login endpoint
router.post("/login", async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        error: "Username and password are required",
      });
    }

    // Get staff by username
    const staff = await authService.getStaffByUsername(username);

    if (!staff) {
      return res.status(401).json({
        success: false,
        error: "Invalid credentials",
      });
    }

    // Check if account is active
    if (!staff.isActive) {
      return res.status(401).json({
        success: false,
        error: "Account is disabled",
      });
    }

    // Check if account is locked
    if (staff.lockedUntil && new Date() < new Date(staff.lockedUntil)) {
      return res.status(423).json({
        success: false,
        error: "Account temporarily locked due to failed login attempts",
      });
    }

    // Verify password
    const passwordValid = await bcrypt.compare(password, staff.passwordHash);

    if (!passwordValid) {
      // Update failed login attempts
      await authService.updateLoginAttempt(staff.id, false);

      return res.status(401).json({
        success: false,
        error: "Invalid credentials",
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: staff.id,
        username: staff.username,
        role: staff.role,
        storeId: staff.storeId,
        permissions: staff.permissions,
        shopifyStaffId: null, // POS staff are not Shopify staff
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: process.env.JWT_EXPIRES_IN || "24h" }
    );

    // Create session
    const tokenHash = crypto.createHash("sha256").update(token).digest("hex");
    const sessionId = await authService.createSession(staff.id, tokenHash, {
      terminalId: req.body.terminalId,
      locationId: req.body.locationId,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
    });

    // Update successful login
    await authService.updateLoginAttempt(staff.id, true);

    res.json({
      success: true,
      data: {
        token,
        sessionId,
        user: {
          id: staff.id,
          username: staff.username,
          name: staff.name,
          email: staff.email,
          role: staff.role,
          storeId: staff.storeId,
          commissionRate: staff.commissionRate,
          permissions: staff.permissions,
          has_pin: staff.hasPin, // Add PIN status for frontend
        },
      },
      message: `Welcome back, ${staff.name}! Logged in from MySQL database.`,
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      error: "Internal server error during login",
    });
  }
});

// Token verification endpoint
router.get("/verify", async (req, res) => {
  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({
        success: false,
        error: "No token provided",
      });
    }

    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "your-secret-key"
    );

    // Create token hash for session lookup
    const tokenHash = crypto.createHash("sha256").update(token).digest("hex");

    // Validate session
    const session = await authService.validateSession(tokenHash);
    if (!session) {
      return res.status(401).json({
        success: false,
        error: "Invalid or expired session",
      });
    }

    // Get fresh user data
    const user = await authService.getStaffWithPermissions(decoded.userId);
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: "User not found or inactive",
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          email: user.email,
          role: user.role,
          storeId: user.storeId,
          commissionRate: user.commissionRate,
          permissions: user.permissions,
          lastLogin: user.lastLogin,
          has_pin: user.hasPin, // Add PIN status for frontend
        },
        session: {
          terminalId: session.terminal_id,
          locationId: session.location_id,
        },
      },
      message: "Token verified successfully from MySQL database",
    });
  } catch (error) {
    console.error("Token verification error:", error);
    res.status(401).json({
      success: false,
      error: "Invalid token",
    });
  }
});

// Staff logout endpoint
router.post("/logout", async (req, res) => {
  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (token) {
      const tokenHash = crypto.createHash("sha256").update(token).digest("hex");
      await authService.removeSession(tokenHash);
    }

    res.json({
      success: true,
      message: "Logged out successfully",
    });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({
      success: false,
      error: "Internal server error during logout",
    });
  }
});

// Get current user profile
router.get("/profile", async (req, res) => {
  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({
        success: false,
        error: "No token provided",
      });
    }

    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "your-secret-key"
    );
    const user = await authService.getStaffWithPermissions(decoded.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "User not found",
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          email: user.email,
          role: user.role,
          storeId: user.storeId,
          commissionRate: user.commissionRate,
          permissions: user.permissions,
          lastLogin: user.lastLogin,
          has_pin: user.hasPin, // Add PIN status for frontend
        },
      },
    });
  } catch (error) {
    console.error("Profile error:", error);
    res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
});

module.exports = router;
