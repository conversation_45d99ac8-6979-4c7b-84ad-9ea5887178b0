/**
 * Sales Agent Service
 * 
 * Manages sales agents who bring customers and earn commissions
 * Separate from POS staff who operate the system
 * 
 * Sales Agent vs Staff Distinction:
 * - Staff: Operate POS system (cashiers, managers)
 * - Sales Agents: Bring customers, earn commissions on sales
 * - Orders require BOTH: Staff member (processes) + Sales Agent (brought customer)
 */

class SalesAgentService {
  constructor() {
    // In-memory sales agent database (replace with actual database in production)
    this.salesAgentsDatabase = new Map([
      ['agent-001', {
        id: 'agent-001',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+254700111111',
        commissionRate: 8.0, // Higher rate for sales agents
        active: true,
        territory: 'Nairobi Central',
        region: 'Nairobi',
        joinDate: new Date('2024-01-15'),
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
        // Performance tracking
        totalSales: 0,
        totalCommission: 0,
        customerCount: 0,
        // Customer acquisition tracking
        customers: new Set(['8095960301705']) // Customer IDs brought by this agent
      }],
      ['agent-002', {
        id: 'agent-002',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+254700222222',
        commissionRate: 7.5,
        active: true,
        territory: 'Westlands',
        region: 'Nairobi',
        joinDate: new Date('2024-02-01'),
        createdAt: new Date('2024-02-01'),
        updatedAt: new Date('2024-02-01'),
        totalSales: 0,
        totalCommission: 0,
        customerCount: 0,
        customers: new Set()
      }],
      ['agent-003', {
        id: 'agent-003',
        name: 'Carol Wanjiku',
        email: '<EMAIL>',
        phone: '+254700333333',
        commissionRate: 9.0, // Top performer
        active: true,
        territory: 'Karen',
        region: 'Nairobi',
        joinDate: new Date('2023-12-01'),
        createdAt: new Date('2023-12-01'),
        updatedAt: new Date('2023-12-01'),
        totalSales: 0,
        totalCommission: 0,
        customerCount: 0,
        customers: new Set()
      }],
      ['agent-004', {
        id: 'agent-004',
        name: 'David Mwangi',
        email: '<EMAIL>',
        phone: '+254700444444',
        commissionRate: 6.5,
        active: false, // Inactive agent
        territory: 'Mombasa',
        region: 'Coast',
        joinDate: new Date('2024-01-01'),
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-03-15'),
        totalSales: 0,
        totalCommission: 0,
        customerCount: 0,
        customers: new Set()
      }]
    ]);
  }

  // Get all active sales agents
  async getAllSalesAgents(includeInactive = false) {
    try {
      const agents = Array.from(this.salesAgentsDatabase.values())
        .filter(agent => includeInactive || agent.active)
        .map(agent => ({
          id: agent.id,
          name: agent.name,
          email: agent.email,
          phone: agent.phone,
          commissionRate: agent.commissionRate,
          active: agent.active,
          territory: agent.territory,
          region: agent.region,
          joinDate: agent.joinDate,
          totalSales: agent.totalSales,
          totalCommission: agent.totalCommission,
          customerCount: agent.customers.size
        }));

      return {
        success: true,
        salesAgents: agents
      };
    } catch (error) {
      console.error('Get all sales agents error:', error);
      return {
        success: false,
        error: 'Failed to fetch sales agents'
      };
    }
  }

  // Get sales agent by ID
  async getSalesAgentById(agentId) {
    try {
      const agent = this.salesAgentsDatabase.get(agentId);
      
      if (!agent) {
        return {
          success: false,
          error: 'Sales agent not found'
        };
      }

      return {
        success: true,
        salesAgent: {
          id: agent.id,
          name: agent.name,
          email: agent.email,
          phone: agent.phone,
          commissionRate: agent.commissionRate,
          active: agent.active,
          territory: agent.territory,
          region: agent.region,
          joinDate: agent.joinDate,
          totalSales: agent.totalSales,
          totalCommission: agent.totalCommission,
          customerCount: agent.customers.size,
          customers: Array.from(agent.customers)
        }
      };
    } catch (error) {
      console.error('Get sales agent by ID error:', error);
      return {
        success: false,
        error: 'Failed to fetch sales agent'
      };
    }
  }

  // Create new sales agent
  async createSalesAgent(agentData) {
    try {
      // Validate required fields
      const { name, email, phone, commissionRate, territory, region } = agentData;
      
      if (!name || !email || !phone || commissionRate === undefined || !territory || !region) {
        return {
          success: false,
          error: 'Missing required fields: name, email, phone, commissionRate, territory, region'
        };
      }

      // Validate commission rate
      if (typeof commissionRate !== 'number' || commissionRate < 0 || commissionRate > 100) {
        return {
          success: false,
          error: 'Commission rate must be a number between 0 and 100'
        };
      }

      // Check for duplicate email
      const existingAgent = Array.from(this.salesAgentsDatabase.values())
        .find(agent => agent.email.toLowerCase() === email.toLowerCase());
      
      if (existingAgent) {
        return {
          success: false,
          error: 'Sales agent with this email already exists'
        };
      }

      // Generate new agent ID
      const agentId = `agent-${String(this.salesAgentsDatabase.size + 1).padStart(3, '0')}`;
      
      const newAgent = {
        id: agentId,
        name: name.trim(),
        email: email.toLowerCase().trim(),
        phone: phone.trim(),
        commissionRate: parseFloat(commissionRate),
        active: agentData.active !== undefined ? agentData.active : true,
        territory: territory.trim(),
        region: region.trim(),
        joinDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        totalSales: 0,
        totalCommission: 0,
        customerCount: 0,
        customers: new Set()
      };

      this.salesAgentsDatabase.set(agentId, newAgent);

      return {
        success: true,
        salesAgent: {
          id: newAgent.id,
          name: newAgent.name,
          email: newAgent.email,
          phone: newAgent.phone,
          commissionRate: newAgent.commissionRate,
          active: newAgent.active,
          territory: newAgent.territory,
          region: newAgent.region,
          joinDate: newAgent.joinDate
        }
      };
    } catch (error) {
      console.error('Create sales agent error:', error);
      return {
        success: false,
        error: 'Failed to create sales agent'
      };
    }
  }

  // Update sales agent
  async updateSalesAgent(agentId, updateData) {
    try {
      const agent = this.salesAgentsDatabase.get(agentId);
      
      if (!agent) {
        return {
          success: false,
          error: 'Sales agent not found'
        };
      }

      // Validate commission rate if provided
      if (updateData.commissionRate !== undefined) {
        if (typeof updateData.commissionRate !== 'number' || 
            updateData.commissionRate < 0 || 
            updateData.commissionRate > 100) {
          return {
            success: false,
            error: 'Commission rate must be a number between 0 and 100'
          };
        }
      }

      // Check for duplicate email if email is being updated
      if (updateData.email && updateData.email !== agent.email) {
        const existingAgent = Array.from(this.salesAgentsDatabase.values())
          .find(a => a.id !== agentId && a.email.toLowerCase() === updateData.email.toLowerCase());
        
        if (existingAgent) {
          return {
            success: false,
            error: 'Sales agent with this email already exists'
          };
        }
      }

      // Update allowed fields
      const allowedFields = ['name', 'email', 'phone', 'commissionRate', 'active', 'territory', 'region'];
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          if (field === 'email') {
            agent[field] = updateData[field].toLowerCase().trim();
          } else if (typeof updateData[field] === 'string') {
            agent[field] = updateData[field].trim();
          } else {
            agent[field] = updateData[field];
          }
        }
      });

      agent.updatedAt = new Date();

      return {
        success: true,
        salesAgent: {
          id: agent.id,
          name: agent.name,
          email: agent.email,
          phone: agent.phone,
          commissionRate: agent.commissionRate,
          active: agent.active,
          territory: agent.territory,
          region: agent.region,
          joinDate: agent.joinDate,
          updatedAt: agent.updatedAt
        }
      };
    } catch (error) {
      console.error('Update sales agent error:', error);
      return {
        success: false,
        error: 'Failed to update sales agent'
      };
    }
  }

  // Link customer to sales agent (customer acquisition tracking)
  async linkCustomerToAgent(agentId, customerId) {
    try {
      const agent = this.salesAgentsDatabase.get(agentId);
      
      if (!agent) {
        return {
          success: false,
          error: 'Sales agent not found'
        };
      }

      if (!agent.active) {
        return {
          success: false,
          error: 'Cannot link customer to inactive sales agent'
        };
      }

      // Add customer to agent's customer set
      agent.customers.add(customerId);
      agent.customerCount = agent.customers.size;
      agent.updatedAt = new Date();

      return {
        success: true,
        message: `Customer ${customerId} linked to sales agent ${agent.name}`,
        customerCount: agent.customerCount
      };
    } catch (error) {
      console.error('Link customer to agent error:', error);
      return {
        success: false,
        error: 'Failed to link customer to sales agent'
      };
    }
  }

  // Get customers brought by a sales agent
  async getAgentCustomers(agentId) {
    try {
      const agent = this.salesAgentsDatabase.get(agentId);
      
      if (!agent) {
        return {
          success: false,
          error: 'Sales agent not found'
        };
      }

      return {
        success: true,
        agentInfo: {
          id: agent.id,
          name: agent.name,
          territory: agent.territory,
          region: agent.region
        },
        customers: Array.from(agent.customers),
        customerCount: agent.customers.size
      };
    } catch (error) {
      console.error('Get agent customers error:', error);
      return {
        success: false,
        error: 'Failed to fetch agent customers'
      };
    }
  }

  // Find sales agent by customer ID
  async findAgentByCustomer(customerId) {
    try {
      const agent = Array.from(this.salesAgentsDatabase.values())
        .find(agent => agent.customers.has(customerId));
      
      if (!agent) {
        return {
          success: false,
          error: 'No sales agent found for this customer'
        };
      }

      return {
        success: true,
        salesAgent: {
          id: agent.id,
          name: agent.name,
          email: agent.email,
          commissionRate: agent.commissionRate,
          territory: agent.territory,
          region: agent.region
        }
      };
    } catch (error) {
      console.error('Find agent by customer error:', error);
      return {
        success: false,
        error: 'Failed to find sales agent for customer'
      };
    }
  }
}

module.exports = new SalesAgentService();
