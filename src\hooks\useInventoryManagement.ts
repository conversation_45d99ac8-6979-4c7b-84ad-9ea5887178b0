import { useCallback } from 'react';
import { useAppDispatch } from '../store';
import { 
  invalidateProductCache, 
  updateProductInventory,
  updateInventoryQuantities,
  markProductsStale 
} from '../store/slices/productSlice';
import { updateInventoryQuantity } from '../store/slices/cartSlice';
import { CartItem } from '../types/shopify';

export interface InventoryUpdate {
  variantId: string;
  quantityReduced: number;
  newQuantity: number;
}

export const useInventoryManagement = () => {
  const dispatch = useAppDispatch();

  // Calculate inventory updates from cart items
  const calculateInventoryUpdates = useCallback((cartItems: CartItem[]): InventoryUpdate[] => {
    return cartItems.map(item => ({
      variantId: item.variantId,
      quantityReduced: item.quantity,
      newQuantity: Math.max(0, item.inventoryQuantity - item.quantity)
    }));
  }, []);

  // Update inventory after successful order
  const updateInventoryAfterOrder = useCallback(async (cartItems: CartItem[]) => {
    try {
      // Calculate inventory updates
      const inventoryUpdates = calculateInventoryUpdates(cartItems);
      
      // Update product inventory in product slice (immediate UI update)
      const productUpdates = inventoryUpdates.map(update => ({
        variantId: update.variantId,
        newQuantity: update.newQuantity
      }));
      
      dispatch(updateProductInventory(productUpdates));
      
      // Update cart inventory quantities (for validation)
      inventoryUpdates.forEach(update => {
        dispatch(updateInventoryQuantity({
          variantId: update.variantId,
          quantity: update.newQuantity
        }));
      });

      console.log('✅ Inventory updated locally:', inventoryUpdates);
      
      return { success: true, updates: inventoryUpdates };
    } catch (error) {
      console.error('❌ Failed to update inventory:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, [dispatch, calculateInventoryUpdates]);

  // Invalidate all product cache and refresh data
  const invalidateAndRefreshProducts = useCallback(async () => {
    try {
      console.log('🔄 Invalidating product cache and refreshing...');
      await dispatch(invalidateProductCache()).unwrap();
      console.log('✅ Product cache invalidated and refreshed');
      return { success: true };
    } catch (error) {
      console.error('❌ Failed to invalidate product cache:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, [dispatch]);

  // Mark products as stale (for background refresh)
  const markInventoryStale = useCallback(() => {
    dispatch(markProductsStale());
    console.log('⚠️ Products marked as stale - will refresh on next access');
  }, [dispatch]);

  // Complete inventory management after order
  const handlePostOrderInventoryUpdate = useCallback(async (cartItems: CartItem[]) => {
    console.log('🔄 Starting post-order inventory update...');
    
    // Step 1: Update inventory immediately for UI responsiveness
    const immediateUpdate = await updateInventoryAfterOrder(cartItems);
    
    if (!immediateUpdate.success) {
      console.error('❌ Immediate inventory update failed:', immediateUpdate.error);
      return immediateUpdate;
    }

    // Step 2: Schedule cache invalidation for fresh data
    // This happens in the background to ensure we have the latest data
    setTimeout(async () => {
      const refreshResult = await invalidateAndRefreshProducts();
      if (refreshResult.success) {
        console.log('✅ Background product refresh completed');
      } else {
        console.error('❌ Background product refresh failed:', refreshResult.error);
      }
    }, 1000); // 1 second delay to allow order processing to complete

    console.log('✅ Post-order inventory update completed');
    return immediateUpdate;
  }, [updateInventoryAfterOrder, invalidateAndRefreshProducts]);

  // Validate cart against current inventory
  const validateCartInventory = useCallback((cartItems: CartItem[]): {
    isValid: boolean;
    errors: string[];
    outOfStockItems: CartItem[];
  } => {
    const errors: string[] = [];
    const outOfStockItems: CartItem[] = [];

    cartItems.forEach(item => {
      if (item.quantity > item.inventoryQuantity) {
        const error = `${item.title}: Requested ${item.quantity}, but only ${item.inventoryQuantity} available`;
        errors.push(error);
        outOfStockItems.push(item);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      outOfStockItems
    };
  }, []);

  // Check if any items in cart are out of stock
  const hasOutOfStockItems = useCallback((cartItems: CartItem[]): boolean => {
    return cartItems.some(item => item.quantity > item.inventoryQuantity);
  }, []);

  // Get low stock items (less than 5 units)
  const getLowStockItems = useCallback((cartItems: CartItem[]): CartItem[] => {
    return cartItems.filter(item => item.inventoryQuantity < 5 && item.inventoryQuantity > 0);
  }, []);

  // Prevent overselling by adjusting cart quantities
  const adjustCartForInventory = useCallback((cartItems: CartItem[]): {
    adjustedItems: CartItem[];
    adjustmentsMade: boolean;
    adjustments: string[];
  } => {
    const adjustments: string[] = [];
    let adjustmentsMade = false;

    const adjustedItems = cartItems.map(item => {
      if (item.quantity > item.inventoryQuantity) {
        const originalQuantity = item.quantity;
        const adjustedQuantity = Math.max(0, item.inventoryQuantity);
        
        adjustments.push(
          `${item.title}: Reduced from ${originalQuantity} to ${adjustedQuantity} (available stock)`
        );
        adjustmentsMade = true;

        return {
          ...item,
          quantity: adjustedQuantity
        };
      }
      return item;
    }).filter(item => item.quantity > 0); // Remove items with 0 quantity

    return {
      adjustedItems,
      adjustmentsMade,
      adjustments
    };
  }, []);

  return {
    // Main functions
    updateInventoryAfterOrder,
    invalidateAndRefreshProducts,
    handlePostOrderInventoryUpdate,
    
    // Validation functions
    validateCartInventory,
    hasOutOfStockItems,
    getLowStockItems,
    adjustCartForInventory,
    
    // Utility functions
    calculateInventoryUpdates,
    markInventoryStale,
  };
};

export default useInventoryManagement;
