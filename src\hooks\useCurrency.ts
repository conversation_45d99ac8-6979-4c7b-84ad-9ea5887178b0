import {
  calculateDiscount,
  CurrencyFormatOptions,
  CurrencyFormatter,
  formatCompactCurrency,
  formatCurrency,
  parseCurrency,
  validateCurrencyAmount,
} from "@/src/utils/currencyUtils";
import { useCallback } from "react";

/**
 * Hook for currency formatting and operations
 * Provides easy access to currency utilities in React components
 */
export const useCurrency = () => {
  // Format currency with default options
  const format = useCallback(
    (amount: number, options?: CurrencyFormatOptions) => {
      return formatCurrency(amount, options);
    },
    []
  );

  // Format currency in compact form (1.2K, 1.5M)
  const formatCompact = useCallback(
    (amount: number, options?: CurrencyFormatOptions) => {
      return formatCompactCurrency(amount, options);
    },
    []
  );

  // Parse currency string to number
  const parse = useCallback((currencyString: string) => {
    return parseCurrency(currencyString);
  }, []);

  // Validate currency amount
  const validate = useCallback(
    (
      amount: number,
      options?: {
        min?: number;
        max?: number;
        allowZero?: boolean;
        allowNegative?: boolean;
      }
    ) => {
      return validateCurrencyAmount(amount, options);
    },
    []
  );

  // Tax calculation removed - POS system no longer calculates taxes

  // Calculate discount
  const calcDiscount = useCallback(
    (amount: number, discountPercentage: number) => {
      return calculateDiscount(amount, discountPercentage);
    },
    []
  );

  // Specialized formatters
  const formatters = {
    product: useCallback(
      (amount: number) => CurrencyFormatter.product(amount),
      []
    ),
    total: useCallback((amount: number) => CurrencyFormatter.total(amount), []),
    receipt: useCallback(
      (amount: number) => CurrencyFormatter.receipt(amount),
      []
    ),
    compact: useCallback(
      (amount: number) => CurrencyFormatter.compact(amount),
      []
    ),
    placeholder: useCallback(
      (amount: number = 0) => CurrencyFormatter.placeholder(amount),
      []
    ),
    change: useCallback(
      (amount: number) => CurrencyFormatter.change(amount),
      []
    ),
    // Tax formatter removed - POS system no longer displays taxes
    discount: useCallback(
      (amount: number) => CurrencyFormatter.discount(amount),
      []
    ),
  };

  return {
    format,
    formatCompact,
    parse,
    validate,
    calcDiscount,
    formatters,
  };
};

/**
 * Hook for POS-specific currency operations
 * Provides common POS currency calculations and formatting
 */
export const usePOSCurrency = () => {
  const { format, calcDiscount, formatters } = useCurrency();

  // Calculate order totals without tax
  const calculateOrderTotal = useCallback(
    (
      items: Array<{ price: string; quantity: number }>,
      discountPercentage: number = 0
    ) => {
      const subtotal = items.reduce((sum, item) => {
        return sum + parseFloat(item.price) * item.quantity;
      }, 0);

      const { discountAmount, finalAmount } = calcDiscount(
        subtotal,
        discountPercentage
      );

      return {
        subtotal: Math.round(subtotal * 100) / 100,
        discountAmount: Math.round(discountAmount * 100) / 100,
        subtotalAfterDiscount: Math.round(finalAmount * 100) / 100,
        total: Math.round(finalAmount * 100) / 100, // Total equals subtotal after discount (no tax)
      };
    },
    [calcDiscount]
  );

  // Format order totals for display (without tax)
  const formatOrderTotals = useCallback(
    (totals: { subtotal: number; discountAmount?: number; total: number }) => {
      return {
        subtotal: formatters.total(totals.subtotal),
        discount: totals.discountAmount
          ? formatters.discount(totals.discountAmount)
          : null,
        total: formatters.total(totals.total),
      };
    },
    [formatters]
  );

  // Calculate change for cash payments
  const calculateChange = useCallback(
    (amountDue: number, amountTendered: number) => {
      const change = Math.max(0, amountTendered - amountDue);
      return {
        change: Math.round(change * 100) / 100,
        formatted: formatters.change(change),
      };
    },
    [formatters]
  );

  // Validate cash tender
  const validateCashTender = useCallback(
    (amountDue: number, amountTendered: number) => {
      if (amountTendered < amountDue) {
        const shortage = amountDue - amountTendered;
        return {
          valid: false,
          error: `Insufficient amount. Need ${formatters.total(
            shortage
          )} more.`,
          shortage: Math.round(shortage * 100) / 100,
        };
      }

      const { change, formatted } = calculateChange(amountDue, amountTendered);
      return {
        valid: true,
        change,
        changeFormatted: formatted,
      };
    },
    [calculateChange, formatters]
  );

  // Format item price for display
  const formatItemPrice = useCallback(
    (price: string | number, quantity: number = 1) => {
      const numPrice = typeof price === "string" ? parseFloat(price) : price;
      const total = numPrice * quantity;

      return {
        unitPrice: formatters.product(numPrice),
        totalPrice: formatters.total(total),
        unitPriceRaw: Math.round(numPrice * 100) / 100,
        totalPriceRaw: Math.round(total * 100) / 100,
      };
    },
    [formatters]
  );

  return {
    calculateOrderTotal,
    formatOrderTotals,
    calculateChange,
    validateCashTender,
    formatItemPrice,
    format,
    formatters,
  };
};

export default useCurrency;
