# 🧪 **TESTING & SETUP GUIDE**
## **Before Database Migration - Complete Testing Plan**

---

## **🎯 OVERVIEW**

This guide walks you through testing the current Dukalink POS system before migrating from in-memory storage to MySQL database. **DO NOT PROCEED WITH MIGRATION** until all tests pass.

---

## **📋 STEP 1: ENVIRONMENT SETUP**

### **1.1 Install Required Dependencies**
```bash
cd backend
npm install mysql2
```

### **1.2 Setup MySQL Database**
```bash
# Install MySQL 8.0+ if not already installed
# On Ubuntu/Debian:
sudo apt update
sudo apt install mysql-server

# On macOS:
brew install mysql

# On Windows: Download from https://dev.mysql.com/downloads/mysql/
```

### **1.3 Create Database and User**
```bash
# Login as MySQL root
mysql -u root -p

# Run the setup script
npm run db:setup
# OR manually run:
# mysql -u root -p < migrations/setup_database.sql
```

### **1.4 Configure Environment Variables**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings:
DB_HOST=localhost
DB_PORT=3306
DB_USER=dukalink
DB_PASSWORD=dukalink_secure_password_2024
DB_NAME=dukalink_pos_test
JWT_SECRET=your_super_secure_jwt_secret_minimum_32_characters
```

---

## **🚀 STEP 2: START THE CURRENT SYSTEM**

### **2.1 Start Backend Server**
```bash
cd backend
npm run dev
```

**Expected Output:**
```
✅ Initialized 4 default sales agents
✅ Initialized 2 default staff members  
✅ Initialized 3 default POS terminals
🚀 Server running on port 3020
```

### **2.2 Verify Server is Running**
```bash
curl http://localhost:3020/api/health
# Should return: {"status":"ok","timestamp":"..."}
```

---

## **🧪 STEP 3: RUN COMPREHENSIVE TESTS**

### **3.1 Test Current System**
```bash
cd backend
npm test
```

**Expected Results:**
```
🧪 Testing: Staff Login - Cashier
✅ PASSED: Staff Login - Cashier
🧪 Testing: Token Verification  
✅ PASSED: Token Verification
🧪 Testing: Get All Sales Agents
✅ PASSED: Get All Sales Agents
🧪 Testing: Get Sales Agent by ID
✅ PASSED: Get Sales Agent by ID
🧪 Testing: Get All Staff
✅ PASSED: Get All Staff
🧪 Testing: Get Discount Configuration
✅ PASSED: Get Discount Configuration

📊 TEST RESULTS:
✅ Passed: 6
❌ Failed: 0
📈 Success Rate: 100.0%

🎉 All tests passed! System is ready for migration.
```

### **3.2 Manual API Testing**

#### **Test Staff Authentication**
```bash
# Test cashier login
curl -X POST http://localhost:3020/api/pos/login \
  -H "Content-Type: application/json" \
  -d '{"username":"cashier1","password":"password123"}'

# Expected: {"success":true,"data":{"token":"...","user":{"role":"cashier",...}}}
```

#### **Test Sales Agent Management**
```bash
# Get all sales agents (use token from login)
curl -X GET http://localhost:3020/api/sales-agents \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Expected: {"success":true,"data":{"salesAgents":[...]}} with 4 agents
```

#### **Test Staff Management**
```bash
# Get all staff
curl -X GET http://localhost:3020/api/staff \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Expected: {"success":true,"data":{"staffMembers":[...]}} with 2 staff
```

---

## **📊 STEP 4: PERFORMANCE BASELINE**

### **4.1 Measure Response Times**
```bash
# Test API response times
time curl -X GET http://localhost:3020/api/sales-agents \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Record the response time for comparison after migration
```

### **4.2 Memory Usage Baseline**
```bash
# Check Node.js memory usage
ps aux | grep node
# Record RSS memory usage for comparison
```

---

## **🔧 STEP 5: DATABASE PREPARATION**

### **5.1 Test Database Connection**
```bash
# Test connection to test database
mysql -u dukalink -p dukalink_pos_test -e "SELECT 'Connection successful!' as Status;"
```

### **5.2 Create Schema (Test Run)**
```bash
# Test schema creation on test database
mysql -u dukalink -p dukalink_pos_test < migrations/001_sales_agents_schema.sql
mysql -u dukalink -p dukalink_pos_test < migrations/002_pos_staff_schema.sql

# Verify tables were created
mysql -u dukalink -p dukalink_pos_test -e "SHOW TABLES;"
```

**Expected Output:**
```
+---------------------------+
| Tables_in_dukalink_pos_test |
+---------------------------+
| agent_customers           |
| agent_performance_history |
| pos_sessions             |
| pos_staff                |
| sales_agents             |
| staff_permissions        |
+---------------------------+
```

---

## **⚠️ STEP 6: VALIDATION CHECKLIST**

### **6.1 Current System Validation**
- [ ] Backend server starts without errors
- [ ] All 6 automated tests pass
- [ ] Staff login works (cashier1/password123)
- [ ] Staff login works (manager1/manager123)
- [ ] JWT token verification works
- [ ] Sales agents API returns 4 agents
- [ ] Staff API returns 2 staff members
- [ ] Commission configuration accessible
- [ ] No console errors in server logs

### **6.2 Database Validation**
- [ ] MySQL server running
- [ ] Database `dukalink_pos_test` created
- [ ] User `dukalink` can connect
- [ ] Schema files execute without errors
- [ ] All expected tables created
- [ ] Sample data inserted correctly

### **6.3 Environment Validation**
- [ ] All environment variables set
- [ ] JWT_SECRET is secure (32+ characters)
- [ ] Database credentials work
- [ ] No sensitive data in logs

---

## **🚨 FAILURE SCENARIOS**

### **If Tests Fail:**
1. **Check server logs** for error messages
2. **Verify environment variables** are set correctly
3. **Ensure all dependencies** are installed (`npm install`)
4. **Check database connection** manually
5. **Review API endpoints** are responding

### **Common Issues:**
- **Port 3020 already in use**: Change PORT in .env
- **Database connection failed**: Check MySQL is running
- **JWT errors**: Verify JWT_SECRET is set
- **Permission denied**: Check database user privileges

---

## **✅ SUCCESS CRITERIA**

**Proceed with migration ONLY if:**
- [ ] All automated tests pass (6/6)
- [ ] Manual API tests work
- [ ] Database schema creates successfully
- [ ] No errors in server logs
- [ ] Performance baseline established

---

## **🎯 NEXT STEPS**

Once all tests pass:

1. **Document baseline performance** metrics
2. **Create production database** backup
3. **Schedule maintenance window** for migration
4. **Proceed with migration** using the migration scripts
5. **Run post-migration tests** to verify functionality

---

## **📞 TROUBLESHOOTING**

### **Server Won't Start**
```bash
# Check if port is in use
lsof -i :3020

# Check environment variables
cat .env

# Check dependencies
npm list
```

### **Database Connection Issues**
```bash
# Test MySQL connection
mysql -u dukalink -p -e "SELECT 1;"

# Check MySQL status
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS
```

### **API Tests Failing**
```bash
# Check server logs
tail -f logs/dukalink-pos.log

# Test individual endpoints
curl -v http://localhost:3020/api/health
```

---

## **📋 TESTING COMPLETION SIGN-OFF**

**Testing Completed**: _______________  
**All Tests Passed**: _______________  
**Performance Baseline**: _______________  
**Database Ready**: _______________  
**Approved for Migration**: _______________  

**Tested By**: _______________  
**Date**: _______________
