/**
 * Payment Flow Manager Component
 *
 * Unified modal experience for payment processing with step-based navigation
 * Supports single payments and split payments with real-time balance tracking
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  TextInput,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/contexts/ThemeContext";
import { PaymentMethod, PAYMENT_METHODS } from "@/src/types/payment";
import { formatCurrency } from "@/src/utils/currencyUtils";
import SplitPaymentModal, { SplitPaymentResult } from "./SplitPaymentModal";
import { LoyaltyDiscountCalculator } from "@/src/components/loyalty/LoyaltyDiscountCalculator";

export interface PaymentFlowManagerProps {
  totalAmount: number;
  currency?: string;
  onPaymentComplete: (result: PaymentFlowResult) => void;
  orderData?: any;
  customerId?: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  staffId?: string;
  terminalId?: string;
  locationId?: string;
  allowSplitPayment?: boolean;
  disabled?: boolean;
  onPaymentMethodChange?: (
    selection: PaymentMethodSelection | null,
    isValid: boolean
  ) => void;
}

export interface PaymentMethodSelection {
  method: PaymentMethod;
  amount: number;
  status: "pending" | "processing" | "completed" | "failed";
  transactionId?: string;
  metadata?: any;
}

export interface PaymentFlowResult {
  success: boolean;
  transactionId: string;
  totalAmount: number;
  paymentMethods: PaymentMethodSelection[];
  isSplitPayment: boolean;
  shopifyOrderId?: string;
  error?: string;
}

type PaymentStep = "inline_payment" | "split_payment_setup";

type PaymentTabType = "cash" | "mpesa" | "credit" | "absa_till";

const PaymentFlowManager: React.FC<PaymentFlowManagerProps> = ({
  totalAmount,
  currency = "KES",
  onPaymentComplete,
  orderData,
  customerId,
  customerName,
  customerPhone,
  customerEmail,
  staffId,
  terminalId,
  locationId,
  allowSplitPayment = true,
  disabled = false,
  onPaymentMethodChange,
}) => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState<PaymentStep>("inline_payment");
  const [activeTab, setActiveTab] = useState<PaymentTabType>("cash");
  const [selectedMethods, setSelectedMethods] = useState<
    PaymentMethodSelection[]
  >([]);
  const [remainingAmount, setRemainingAmount] = useState(totalAmount);
  const [error, setError] = useState<string | null>(null);

  // Loyalty discount state
  const [loyaltyDiscount, setLoyaltyDiscount] = useState<{
    amount: number;
    type: "tier" | "points";
    metadata?: any;
  } | null>(null);
  const [adjustedTotal, setAdjustedTotal] = useState(totalAmount);
  const [paymentData, setPaymentData] = useState<Record<string, any>>({});
  const [splitModalVisible, setSplitModalVisible] = useState(false);
  const [splitPaymentResult, setSplitPaymentResult] =
    useState<SplitPaymentResult | null>(null);

  // Reset state when totalAmount changes
  useEffect(() => {
    setCurrentStep("inline_payment");
    setActiveTab("cash");
    setSelectedMethods([]);
    setError(null);
    setPaymentData({});
    setSplitModalVisible(false);
    setSplitPaymentResult(null);
    setLoyaltyDiscount(null);
  }, [totalAmount]);

  // Update adjusted total and remaining amount when loyalty discount changes
  useEffect(() => {
    const newAdjustedTotal = totalAmount - (loyaltyDiscount?.amount || 0);
    setAdjustedTotal(newAdjustedTotal);
    setRemainingAmount(newAdjustedTotal);
  }, [totalAmount, loyaltyDiscount]);

  // Loyalty discount handlers
  const handleLoyaltyDiscountApplied = (
    discountAmount: number,
    discountType: "tier" | "points",
    metadata?: any
  ) => {
    setLoyaltyDiscount({
      amount: discountAmount,
      type: discountType,
      metadata,
    });
  };

  const handleLoyaltyDiscountRemoved = () => {
    setLoyaltyDiscount(null);
  };

  const handleAddPaymentMethod = (method: PaymentMethod, amount: number) => {
    if (amount <= 0 || amount > remainingAmount) {
      Alert.alert("Invalid Amount", "Please enter a valid amount");
      return;
    }

    const newMethod: PaymentMethodSelection = {
      method,
      amount,
      status: "pending",
    };

    const updatedMethods = [...selectedMethods, newMethod];
    setSelectedMethods(updatedMethods);
    setRemainingAmount(remainingAmount - amount);

    // Note: Split payment processing is now handled externally
  };

  const handleRemovePaymentMethod = (index: number) => {
    const methodToRemove = selectedMethods[index];
    const updatedMethods = selectedMethods.filter((_, i) => i !== index);
    setSelectedMethods(updatedMethods);
    setRemainingAmount(remainingAmount + methodToRemove.amount);
  };

  // Validate if the current payment method configuration is complete
  const isPaymentMethodValid = () => {
    switch (activeTab) {
      case "cash":
        // Cash payment is always valid (exact amount assumed)
        return true;
      case "mpesa":
        // M-Pesa only requires customer phone number, transaction code is optional
        const mpesaData = paymentData[activeTab] || {};
        const hasCustomerPhone = !!(customerPhone || mpesaData.customerPhone);
        return hasCustomerPhone;
      case "credit":
        // Credit payment requires customer to be selected
        return !!customerName;
      case "absa_till":
        // ABSA Till transaction code is now optional
        return true;
      default:
        return false;
    }
  };

  // Get current payment method selection for external use
  const getCurrentPaymentSelection = () => {
    if (!isPaymentMethodValid()) {
      return null;
    }

    const paymentMethod = PAYMENT_METHODS.find(
      (method) => method.type === activeTab
    );

    if (!paymentMethod) {
      return null;
    }

    return {
      method: paymentMethod,
      amount: adjustedTotal,
      status: "pending" as const,
      metadata: {
        ...(paymentData[activeTab] || {}),
        loyaltyDiscount: loyaltyDiscount,
        originalAmount: totalAmount,
        // Include customer information in metadata
        customerId: customerId,
        customerName: customerName,
        customerPhone: customerPhone,
        customerEmail: customerEmail,
      },
    };
  };

  // Notify parent when payment method or validation changes
  useEffect(() => {
    if (onPaymentMethodChange) {
      const selection = getCurrentPaymentSelection();
      const isValid = isPaymentMethodValid();
      onPaymentMethodChange(selection, isValid);
    }
  }, [activeTab, paymentData, totalAmount]);

  // Get enabled payment methods for tabs (excluding split payment)
  const getTabPaymentMethods = () => {
    return PAYMENT_METHODS.filter(
      (method) =>
        method.enabled && !method.placeholder && method.type !== "card" // Exclude card for now as it's not in tab design
    );
  };

  const renderTabNavigation = () => {
    const tabMethods = getTabPaymentMethods();

    return (
      <View>
        {/* Payment method tabs */}
        <View
          style={[
            styles.tabContainer,
            { backgroundColor: theme.colors.surface },
          ]}
        >
          {tabMethods.map((method) => (
            <TouchableOpacity
              key={method.id}
              style={[
                styles.tab,
                {
                  backgroundColor:
                    activeTab === method.type
                      ? theme.colors.primary
                      : theme.colors.card,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={() =>
                !disabled && setActiveTab(method.type as PaymentTabType)
              }
              disabled={disabled}
            >
              <Text
                style={[
                  styles.tabIcon,
                  {
                    color:
                      activeTab === method.type
                        ? theme.colors.background
                        : theme.colors.text,
                  },
                ]}
              >
                {method.icon}
              </Text>
              <Text
                style={[
                  styles.tabText,
                  {
                    color:
                      activeTab === method.type
                        ? theme.colors.background
                        : theme.colors.text,
                  },
                ]}
              >
                {method.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Second row: Split payment button (if enabled) */}
        {allowSplitPayment && (
          <View style={styles.splitButtonContainer}>
            <TouchableOpacity
              style={[
                styles.splitPaymentButton,
                { backgroundColor: theme.colors.secondary },
              ]}
              onPress={() => !disabled && setSplitModalVisible(true)}
              disabled={disabled}
            >
              <Ionicons
                name="layers-outline"
                size={20}
                color={theme.colors.background}
                style={styles.splitButtonIcon}
              />
              <Text
                style={[
                  styles.splitPaymentText,
                  { color: theme.colors.background },
                ]}
              >
                Split Payment
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const renderCashPayment = () => {
    const cashData = paymentData["cash"] || {};
    const amountTendered = cashData.amountTendered || adjustedTotal;

    return (
      <View style={styles.paymentMethodContainer}>
        <Text style={[styles.paymentMethodTitle, { color: theme.colors.text }]}>
          Cash Payment
        </Text>
        <Text
          style={[
            styles.paymentMethodSubtitle,
            { color: theme.colors.textSecondary },
          ]}
        >
          Enter the amount tendered by the customer
        </Text>

        <View style={styles.inputContainer}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
            Amount Tendered
          </Text>
          <View
            style={[styles.inputWrapper, { borderColor: theme.colors.border }]}
          >
            <Text
              style={[
                styles.currencySymbol,
                { color: theme.colors.textSecondary },
              ]}
            >
              KSh
            </Text>
            <TextInput
              style={[styles.amountInput, { color: theme.colors.text }]}
              value={amountTendered.toString()}
              onChangeText={(text) => {
                const amount = parseFloat(text) || adjustedTotal;
                setPaymentData((prev) => ({
                  ...prev,
                  cash: { ...prev.cash, amountTendered: amount },
                }));
              }}
              placeholder={adjustedTotal.toString()}
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>
        </View>

        {amountTendered > adjustedTotal && (
          <View style={styles.changeContainer}>
            <Text
              style={[
                styles.changeLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Change:
            </Text>
            <Text
              style={[styles.changeAmount, { color: theme.colors.success }]}
            >
              {formatCurrency(amountTendered - adjustedTotal)}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderMpesaPayment = () => (
    <View style={styles.paymentMethodContainer}>
      <Text style={[styles.paymentMethodTitle, { color: theme.colors.text }]}>
        M-Pesa Payment
      </Text>
      <Text
        style={[
          styles.paymentMethodSubtitle,
          { color: theme.colors.textSecondary },
        ]}
      >
        Send STK Push or enter transaction code
      </Text>

      {/* Customer Phone Number Input */}
      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Customer Phone Number
        </Text>
        <View
          style={[styles.inputWrapper, { borderColor: theme.colors.border }]}
        >
          <TextInput
            style={[styles.placeholderInput, { color: theme.colors.text }]}
            value={paymentData["mpesa"]?.customerPhone || customerPhone || ""}
            onChangeText={(text: string) => {
              setPaymentData((prev) => ({
                ...prev,
                mpesa: {
                  ...prev.mpesa,
                  customerPhone: text,
                },
              }));
            }}
            placeholder="254712345678"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="phone-pad"
          />
        </View>
        {!customerPhone && !paymentData["mpesa"]?.customerPhone && (
          <Text
            style={[styles.inputHint, { color: theme.colors.textSecondary }]}
          >
            Required for M-Pesa payments
          </Text>
        )}
      </View>

      <View style={styles.mpesaOptions}>
        <TouchableOpacity
          style={[
            styles.mpesaOptionButton,
            { backgroundColor: theme.colors.primary },
          ]}
          onPress={() => {
            const phoneToUse =
              paymentData["mpesa"]?.customerPhone || customerPhone;
            if (!phoneToUse) {
              Alert.alert(
                "Phone Number Required",
                "Please enter a customer phone number for STK Push."
              );
              return;
            }
            setPaymentData((prev) => ({
              ...prev,
              mpesa: { ...prev.mpesa, stkPushSent: true, transactionCode: "" },
            }));
            Alert.alert(
              "STK Push",
              `STK Push initiated to ${phoneToUse}. Customer will receive a prompt on their phone.`
            );
          }}
        >
          <Ionicons
            name="phone-portrait"
            size={20}
            color={theme.colors.background}
          />
          <Text
            style={[styles.mpesaOptionText, { color: theme.colors.background }]}
          >
            Send STK Push
          </Text>
        </TouchableOpacity>

        <Text style={[styles.orText, { color: theme.colors.textSecondary }]}>
          OR
        </Text>

        <View style={styles.inputContainer}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
            M-Pesa Transaction Code
          </Text>
          <View
            style={[styles.inputWrapper, { borderColor: theme.colors.border }]}
          >
            <TextInput
              style={[styles.placeholderInput, { color: theme.colors.text }]}
              value={paymentData["mpesa"]?.transactionCode || ""}
              onChangeText={(text: string) => {
                setPaymentData((prev) => ({
                  ...prev,
                  mpesa: {
                    ...prev.mpesa,
                    transactionCode: text,
                    stkPushSent: false,
                  },
                }));
              }}
              placeholder="Enter transaction code..."
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        </View>
      </View>

      {(paymentData["mpesa"]?.stkPushSent ||
        paymentData["mpesa"]?.transactionCode) && (
        <View style={styles.validationContainer}>
          <Ionicons
            name="checkmark-circle"
            size={20}
            color={theme.colors.success}
          />
          <Text
            style={[styles.validationText, { color: theme.colors.success }]}
          >
            {paymentData["mpesa"]?.stkPushSent
              ? "STK Push sent successfully"
              : "Transaction code entered"}
          </Text>
        </View>
      )}
    </View>
  );

  const renderCreditPayment = () => (
    <View style={styles.paymentMethodContainer}>
      <Text style={[styles.paymentMethodTitle, { color: theme.colors.text }]}>
        Credit Payment
      </Text>
      <Text
        style={[
          styles.paymentMethodSubtitle,
          { color: theme.colors.textSecondary },
        ]}
      >
        Customer credit account payment
      </Text>

      <View style={styles.creditInfo}>
        <View style={styles.creditInfoRow}>
          <Text
            style={[styles.creditLabel, { color: theme.colors.textSecondary }]}
          >
            Customer:
          </Text>
          <Text style={[styles.creditValue, { color: theme.colors.text }]}>
            {customerName || "No customer selected"}
          </Text>
        </View>
        <View style={styles.creditInfoRow}>
          <Text
            style={[styles.creditLabel, { color: theme.colors.textSecondary }]}
          >
            Credit Limit:
          </Text>
          <Text style={[styles.creditValue, { color: theme.colors.success }]}>
            {formatCurrency(50000)}
          </Text>
        </View>
        <View style={styles.creditInfoRow}>
          <Text
            style={[styles.creditLabel, { color: theme.colors.textSecondary }]}
          >
            Amount:
          </Text>
          <Text style={[styles.creditValue, { color: theme.colors.primary }]}>
            {formatCurrency(adjustedTotal)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderAbsaTillPayment = () => (
    <View style={styles.paymentMethodContainer}>
      <Text style={[styles.paymentMethodTitle, { color: theme.colors.text }]}>
        ABSA Till Payment
      </Text>
      <Text
        style={[
          styles.paymentMethodSubtitle,
          { color: theme.colors.textSecondary },
        ]}
      >
        Enter the transaction code from ABSA Till
      </Text>

      <View style={styles.tillInfo}>
        <Text style={[styles.tillLabel, { color: theme.colors.textSecondary }]}>
          Till Number: 123456
        </Text>
        <Text style={[styles.tillAmount, { color: theme.colors.primary }]}>
          Amount: {formatCurrency(adjustedTotal)}
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Transaction Code
        </Text>
        <View
          style={[styles.inputWrapper, { borderColor: theme.colors.border }]}
        >
          <TextInput
            style={[styles.placeholderInput, { color: theme.colors.text }]}
            value={paymentData["absa_till"]?.transactionCode || ""}
            onChangeText={(text: string) => {
              setPaymentData((prev) => ({
                ...prev,
                absa_till: { ...prev.absa_till, transactionCode: text },
              }));
            }}
            placeholder="Enter ABSA transaction code..."
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>
      </View>

      {paymentData["absa_till"]?.transactionCode && (
        <View style={styles.validationContainer}>
          <Ionicons
            name="checkmark-circle"
            size={20}
            color={theme.colors.success}
          />
          <Text
            style={[styles.validationText, { color: theme.colors.success }]}
          >
            Transaction code entered
          </Text>
        </View>
      )}
    </View>
  );

  const renderPaymentMethodContent = () => {
    switch (activeTab) {
      case "cash":
        return renderCashPayment();
      case "mpesa":
        return renderMpesaPayment();
      case "credit":
        return renderCreditPayment();
      case "absa_till":
        return renderAbsaTillPayment();
      default:
        return renderCashPayment();
    }
  };

  const renderSplitPaymentSummary = () => {
    if (!splitPaymentResult || !splitPaymentResult.success) return null;

    return (
      <View style={styles.splitSummaryContainer}>
        <Text style={[styles.splitSummaryTitle, { color: theme.colors.text }]}>
          Split Payment Breakdown
        </Text>
        <View
          style={[
            styles.splitSummaryCard,
            { backgroundColor: theme.colors.card },
          ]}
        >
          <View style={styles.splitSummaryMethods}>
            {splitPaymentResult.methods.map((method, index) => (
              <View key={method.method.id} style={styles.splitSummaryMethod}>
                <Text style={styles.methodIcon}>{method.method.icon}</Text>
                <Text
                  style={[styles.splitMethodName, { color: theme.colors.text }]}
                >
                  {method.method.name}:
                </Text>
                <Text
                  style={[
                    styles.splitMethodAmount,
                    { color: theme.colors.primary },
                  ]}
                >
                  {formatCurrency(method.amount)}
                </Text>
                {index < splitPaymentResult.methods.length - 1 && (
                  <Text
                    style={[
                      styles.splitSeparator,
                      { color: theme.colors.textSecondary },
                    ]}
                  >
                    |
                  </Text>
                )}
              </View>
            ))}
          </View>
          <View style={styles.splitSummaryTotal}>
            <Text
              style={[
                styles.splitTotalLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Total:
            </Text>
            <Text
              style={[styles.splitTotalAmount, { color: theme.colors.success }]}
            >
              {formatCurrency(splitPaymentResult.totalAmount)}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderInlinePayment = () => (
    <View style={styles.stepContainer}>
      {/* Loyalty Discount Calculator */}
      <LoyaltyDiscountCalculator
        customerId={customerId || null}
        orderTotal={totalAmount}
        onDiscountApplied={handleLoyaltyDiscountApplied}
        onDiscountRemoved={handleLoyaltyDiscountRemoved}
        disabled={false}
      />

      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Payment Method
      </Text>
      <View style={{ marginBottom: 24 }}>
        {loyaltyDiscount ? (
          <View>
            <Text
              style={[
                styles.amountText,
                {
                  color: theme.colors.textSecondary,
                  textDecorationLine: "line-through",
                  fontSize: 16,
                },
              ]}
            >
              Original: {formatCurrency(totalAmount)}
            </Text>
            <Text style={[styles.amountText, { color: theme.colors.primary }]}>
              Total: {formatCurrency(adjustedTotal)}
            </Text>
            <Text
              style={[
                {
                  color: theme.colors.success,
                  fontSize: 14,
                  textAlign: "center",
                },
              ]}
            >
              Loyalty discount: -{formatCurrency(loyaltyDiscount.amount)}
            </Text>
          </View>
        ) : (
          <Text style={[styles.amountText, { color: theme.colors.primary }]}>
            Total: {formatCurrency(totalAmount)}
          </Text>
        )}
      </View>

      {renderTabNavigation()}

      <View
        style={[
          styles.paymentContentArea,
          { backgroundColor: theme.colors.card },
        ]}
      >
        {renderPaymentMethodContent()}
      </View>

      {/* Payment method validation indicator */}
      {isPaymentMethodValid() && (
        <View style={styles.validationContainer}>
          <Ionicons
            name="checkmark-circle"
            size={16}
            color={theme.colors.success}
          />
          <Text
            style={[styles.validationText, { color: theme.colors.success }]}
          >
            Payment method configured
          </Text>
        </View>
      )}
    </View>
  );

  const renderSplitPaymentSetup = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Split Payment Setup
      </Text>

      <View style={styles.balanceCard}>
        <Text
          style={[styles.balanceLabel, { color: theme.colors.textSecondary }]}
        >
          Remaining Balance
        </Text>
        <Text style={[styles.balanceAmount, { color: theme.colors.primary }]}>
          {formatCurrency(remainingAmount)}
        </Text>
      </View>

      {selectedMethods.length > 0 && (
        <View style={styles.selectedMethodsList}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Selected Methods
          </Text>
          {selectedMethods.map((selection, index) => (
            <View
              key={index}
              style={[
                styles.selectedMethodCard,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.primary,
                  borderWidth: 2,
                },
              ]}
            >
              <View style={styles.selectedMethodInfo}>
                <Text style={styles.methodIcon}>{selection.method.icon}</Text>
                <Text style={[styles.methodName, { color: theme.colors.text }]}>
                  {selection.method.name}
                </Text>
                <Text
                  style={[styles.methodAmount, { color: theme.colors.primary }]}
                >
                  {formatCurrency(selection.amount)}
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => handleRemovePaymentMethod(index)}
                style={styles.removeButton}
              >
                <Ionicons name="close" size={20} color={theme.colors.error} />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}

      {remainingAmount > 0 && (
        <View style={styles.addMethodSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Add Payment Method
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {PAYMENT_METHODS.filter((m) => m.enabled).map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.quickMethodCard,
                  { backgroundColor: theme.colors.card },
                ]}
                onPress={() => {
                  // TODO: Show amount input modal for this method
                  handleAddPaymentMethod(method, remainingAmount);
                }}
              >
                <Text style={styles.methodIcon}>{method.icon}</Text>
                <Text
                  style={[styles.quickMethodName, { color: theme.colors.text }]}
                >
                  {method.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {remainingAmount === 0 && (
        <TouchableOpacity
          style={[
            styles.proceedButton,
            { backgroundColor: theme.colors.primary },
          ]}
          onPress={() => {
            // Split payment processing is now handled externally
            console.log("Split payment setup completed");
          }}
        >
          <Text
            style={[
              styles.proceedButtonText,
              { color: theme.colors.background },
            ]}
          >
            Proceed to Payment
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "inline_payment":
        return renderInlinePayment();
      case "split_payment_setup":
        return renderSplitPaymentSetup();
      default:
        return renderInlinePayment();
    }
  };

  return (
    <View style={[styles.inlineContainer, { opacity: disabled ? 0.5 : 1 }]}>
      {disabled && (
        <View
          style={[
            styles.errorContainer,
            { backgroundColor: theme.colors.warning + "20" },
          ]}
        >
          <Text style={[styles.errorText, { color: theme.colors.warning }]}>
            Complete shipping decision above to proceed with payment
          </Text>
        </View>
      )}

      {error && (
        <View
          style={[
            styles.errorContainer,
            { backgroundColor: theme.colors.errorBackground },
          ]}
        >
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {error}
          </Text>
        </View>
      )}

      {!disabled && renderCurrentStep()}
      {!disabled && splitPaymentResult && renderSplitPaymentSummary()}

      <SplitPaymentModal
        visible={splitModalVisible && !disabled}
        totalAmount={totalAmount}
        currency={currency}
        customerName={customerName}
        onComplete={(result) => {
          setSplitPaymentResult(result);
          setSplitModalVisible(false);
          if (result.success && onPaymentMethodChange) {
            // Notify parent that split payment configuration is ready
            onPaymentMethodChange(
              {
                method: {
                  id: "split",
                  name: "Split Payment",
                  type: "split",
                  enabled: true,
                  placeholder: false,
                  icon: "💳",
                  description: "Split payment across multiple methods",
                },
                amount: totalAmount,
                status: "pending" as const,
                metadata: {
                  isSplitPayment: true,
                  splitMethods: result.methods,
                  splitConfig: result.methods.map((method) => ({
                    method: method.method,
                    amount: method.amount,
                    config: method.config,
                  })),
                },
              },
              true
            );
          }
        }}
        onCancel={() => setSplitModalVisible(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    marginVertical: 8,
    padding: 16,
    elevation: 1,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  inlineContainer: {
    // Minimal styling for inline use within ModernCard
    // No padding, margins, or card styling since ModernCard provides that
  },
  errorContainer: {
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
    textAlign: "center",
  },
  stepContainer: {
    padding: 0,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  amountText: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 24,
  },
  methodsList: {
    marginBottom: 16,
  },
  methodCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  methodInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  methodIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  methodDetails: {
    flex: 1,
  },
  methodName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 14,
  },
  comingSoonText: {
    fontSize: 12,
    fontWeight: "500",
    marginTop: 2,
  },
  splitPaymentButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  splitPaymentText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  balanceCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: "center",
  },
  balanceLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: "bold",
  },
  selectedMethodsList: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  selectedMethodCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: "transparent", // Default transparent border
  },
  selectedMethodInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  methodAmount: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: "auto",
    marginRight: 12,
  },
  removeButton: {
    padding: 4,
  },
  addMethodSection: {
    marginBottom: 24,
  },
  quickMethodCard: {
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    marginRight: 12,
    minWidth: 80,
  },
  quickMethodName: {
    fontSize: 12,
    textAlign: "center",
    marginTop: 4,
  },
  proceedButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  proceedButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },

  transactionId: {
    fontSize: 14,
    textAlign: "center",
  },
  // Tab Navigation Styles - Horizontal Layout
  tabContainer: {
    flexDirection: "row",
    marginBottom: 24,
    borderRadius: 12,
    padding: 8,
    gap: 8,
    justifyContent: "space-around",
  },
  tab: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderWidth: 2,
    borderRadius: 12,
    elevation: 1,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    flex: 1,
    minHeight: 80,
    // Transform scale for selected state
    transform: [{ scale: 1 }],
  },
  tabIcon: {
    fontSize: 28,
    marginBottom: 6,
    textAlign: "center",
  },
  tabText: {
    fontSize: 12,
    fontWeight: "600",
    textAlign: "center",
    lineHeight: 14,
  },
  // Split Payment Button Styles - Second Row
  splitButtonContainer: {
    marginTop: 8, // Small gap between rows
    marginBottom: 24,
    alignItems: "center",
  },
  splitPaymentButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "rgba(0, 0, 0, 0.15)",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    minWidth: 160,
  },
  splitButtonIcon: {
    marginRight: 8, // Icon on the left with spacing
  },
  splitPaymentText: {
    fontSize: 16,
    fontWeight: "600",
  },
  // Payment Content Area
  paymentContentArea: {
    minHeight: 200,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 1,
    shadowColor: "rgba(0, 0, 0, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  placeholderText: {
    fontSize: 16,
    textAlign: "center",
  },
  // Payment Method Content Styles
  paymentMethodContainer: {
    padding: 16,
    alignItems: "stretch",
  },
  paymentMethodTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  paymentMethodSubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 16,
  },
  currencySymbol: {
    fontSize: 16,
    marginRight: 8,
  },
  amountInput: {
    fontSize: 18,
    fontWeight: "600",
    flex: 1,
  },
  placeholderInput: {
    fontSize: 16,
    flex: 1,
  },
  inputHint: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: "italic",
  },

  // M-Pesa Specific Styles
  mpesaOptions: {
    marginBottom: 20,
  },
  mpesaOptionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 10,
    marginBottom: 16,
    elevation: 2,
    shadowColor: "rgba(0, 0, 0, 0.15)",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  mpesaOptionText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  orText: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 16,
    fontWeight: "500",
  },
  // Credit Payment Styles
  creditInfo: {
    marginBottom: 20,
  },
  creditInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  creditLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  creditValue: {
    fontSize: 14,
    fontWeight: "600",
  },
  // ABSA Till Styles
  tillInfo: {
    marginBottom: 20,
    alignItems: "center",
  },
  tillLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  tillAmount: {
    fontSize: 18,
    fontWeight: "600",
  },
  // Split Payment Summary Styles
  splitSummaryContainer: {
    padding: 16,
    marginTop: 16,
  },
  splitSummaryTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
    textAlign: "center",
  },
  splitSummaryCard: {
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  splitSummaryMethods: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
  },
  splitSummaryMethod: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 4,
  },
  splitMethodName: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 4,
    marginRight: 4,
  },
  splitMethodAmount: {
    fontSize: 14,
    fontWeight: "bold",
    marginRight: 8,
  },
  splitSeparator: {
    fontSize: 14,
    marginHorizontal: 8,
  },
  splitSummaryTotal: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  splitTotalLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  splitTotalAmount: {
    fontSize: 18,
    fontWeight: "bold",
  },
  // Change Display Styles
  changeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  changeLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  changeAmount: {
    fontSize: 18,
    fontWeight: "bold",
  },

  // Validation Container Styles
  validationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  validationText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 8,
  },
});

export default PaymentFlowManager;
