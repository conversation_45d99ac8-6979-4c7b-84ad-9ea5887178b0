/**
 * Fulfillment Management Routes
 * 
 * Handles POS-specific fulfillment workflows including:
 * - Staff fulfillment management interface
 * - Shipping fee calculation and management
 * - Delivery address management
 * - Fulfillment status tracking
 */

const express = require("express");
const router = express.Router();
const fulfillmentService = require("../services/fulfillment-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");

// Create fulfillment for an order
router.post("/fulfillments", authenticateToken, async (req, res) => {
  try {
    const { fulfillmentData } = req.body;
    const staffId = req.user.id;

    if (!fulfillmentData || !fulfillmentData.orderId) {
      return ResponseFormatter.badRequest(
        res,
        "Fulfillment data and order ID are required"
      );
    }

    const result = await fulfillmentService.createFulfillment(
      fulfillmentData,
      staffId
    );

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.fulfillment,
        "Fulfillment created successfully"
      );
    } else {
      return ResponseFormatter.serverError(res, result.error);
    }
  } catch (error) {
    console.error("Create fulfillment error:", error);
    return ResponseFormatter.serverError(res, "Failed to create fulfillment");
  }
});

// Get fulfillment by ID
router.get("/fulfillments/:fulfillmentId", authenticateToken, async (req, res) => {
  try {
    const { fulfillmentId } = req.params;

    const result = await fulfillmentService.getFulfillmentById(fulfillmentId);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.fulfillment,
        "Fulfillment retrieved successfully"
      );
    } else {
      return ResponseFormatter.notFound(res, result.error);
    }
  } catch (error) {
    console.error("Get fulfillment error:", error);
    return ResponseFormatter.serverError(res, "Failed to retrieve fulfillment");
  }
});

// Get fulfillments for an order
router.get("/orders/:orderId/fulfillments", authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;

    const result = await fulfillmentService.getFulfillmentsByOrderId(orderId);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.fulfillments,
        "Fulfillments retrieved successfully"
      );
    } else {
      return ResponseFormatter.serverError(res, result.error);
    }
  } catch (error) {
    console.error("Get order fulfillments error:", error);
    return ResponseFormatter.serverError(res, "Failed to retrieve fulfillments");
  }
});

// Update delivery details
router.put("/fulfillments/:fulfillmentId/delivery", authenticateToken, async (req, res) => {
  try {
    const { fulfillmentId } = req.params;
    const { updateData } = req.body;
    const staffId = req.user.id;

    if (!updateData) {
      return ResponseFormatter.badRequest(res, "Update data is required");
    }

    const result = await fulfillmentService.updateDeliveryDetails(
      fulfillmentId,
      updateData,
      staffId
    );

    if (result.success) {
      return ResponseFormatter.success(res, null, result.message);
    } else {
      return ResponseFormatter.serverError(res, result.error);
    }
  } catch (error) {
    console.error("Update delivery details error:", error);
    return ResponseFormatter.serverError(res, "Failed to update delivery details");
  }
});

// Update fulfillment status
router.put("/fulfillments/:fulfillmentId/status", authenticateToken, async (req, res) => {
  try {
    const { fulfillmentId } = req.params;
    const { status } = req.body;
    const staffId = req.user.id;

    if (!status) {
      return ResponseFormatter.badRequest(res, "Status is required");
    }

    const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'failed'];
    if (!validStatuses.includes(status)) {
      return ResponseFormatter.badRequest(res, "Invalid status value");
    }

    const result = await fulfillmentService.updateFulfillmentStatus(
      fulfillmentId,
      status,
      staffId
    );

    if (result.success) {
      return ResponseFormatter.success(res, null, result.message);
    } else {
      return ResponseFormatter.serverError(res, result.error);
    }
  } catch (error) {
    console.error("Update fulfillment status error:", error);
    return ResponseFormatter.serverError(res, "Failed to update fulfillment status");
  }
});

// Calculate shipping fee
router.post("/shipping/calculate", authenticateToken, async (req, res) => {
  try {
    const { shippingData } = req.body;

    if (!shippingData || !shippingData.deliveryMethod) {
      return ResponseFormatter.badRequest(
        res,
        "Shipping data with delivery method is required"
      );
    }

    const result = await fulfillmentService.calculateShippingFee(shippingData);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          shippingFee: result.shippingFee,
          deliveryMethod: result.deliveryMethod,
          currency: result.currency,
          breakdown: result.breakdown
        },
        "Shipping fee calculated successfully"
      );
    } else {
      return ResponseFormatter.serverError(res, result.error);
    }
  } catch (error) {
    console.error("Calculate shipping fee error:", error);
    return ResponseFormatter.serverError(res, "Failed to calculate shipping fee");
  }
});

// Get shipping rates
router.get("/shipping/rates", authenticateToken, async (req, res) => {
  try {
    const result = await fulfillmentService.getShippingRates();

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.shippingRates,
        "Shipping rates retrieved successfully"
      );
    } else {
      return ResponseFormatter.serverError(res, result.error);
    }
  } catch (error) {
    console.error("Get shipping rates error:", error);
    return ResponseFormatter.serverError(res, "Failed to retrieve shipping rates");
  }
});

// Create or update shipping rate (manager+ only)
router.post("/shipping/rates", 
  authenticateToken, 
  requirePermission("manage_shipping_rates"), 
  async (req, res) => {
    try {
      const { rateData } = req.body;
      const staffId = req.user.id;

      if (!rateData || !rateData.deliveryMethod || !rateData.baseFee) {
        return ResponseFormatter.badRequest(
          res,
          "Rate data with delivery method and base fee is required"
        );
      }

      const result = await fulfillmentService.upsertShippingRate(rateData, staffId);

      if (result.success) {
        return ResponseFormatter.success(
          res,
          { rateId: result.rateId },
          result.message
        );
      } else {
        return ResponseFormatter.serverError(res, result.error);
      }
    } catch (error) {
      console.error("Upsert shipping rate error:", error);
      return ResponseFormatter.serverError(res, "Failed to save shipping rate");
    }
  }
);

// Update shipping rate (manager+ only)
router.put("/shipping/rates/:rateId", 
  authenticateToken, 
  requirePermission("manage_shipping_rates"), 
  async (req, res) => {
    try {
      const { rateId } = req.params;
      const { rateData } = req.body;
      const staffId = req.user.id;

      if (!rateData) {
        return ResponseFormatter.badRequest(res, "Rate data is required");
      }

      // Add the ID to the rate data
      rateData.id = rateId;

      const result = await fulfillmentService.upsertShippingRate(rateData, staffId);

      if (result.success) {
        return ResponseFormatter.success(res, null, result.message);
      } else {
        return ResponseFormatter.serverError(res, result.error);
      }
    } catch (error) {
      console.error("Update shipping rate error:", error);
      return ResponseFormatter.serverError(res, "Failed to update shipping rate");
    }
  }
);

// Get fulfillment statistics (manager+ only)
router.get("/fulfillments/stats", 
  authenticateToken, 
  requirePermission("view_fulfillment_reports"), 
  async (req, res) => {
    try {
      // This would be implemented to provide fulfillment statistics
      // For now, return a placeholder response
      const stats = {
        totalFulfillments: 0,
        pendingFulfillments: 0,
        completedFulfillments: 0,
        averageDeliveryTime: 0,
        topDeliveryMethods: []
      };

      return ResponseFormatter.success(
        res,
        stats,
        "Fulfillment statistics retrieved successfully"
      );
    } catch (error) {
      console.error("Get fulfillment stats error:", error);
      return ResponseFormatter.serverError(res, "Failed to retrieve fulfillment statistics");
    }
  }
);

module.exports = router;
