import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useCalculateShippingFee } from "@/src/hooks/useFulfillment";
import { Customer, CustomerAddress } from "@/src/types/shopify";
import { SelectedCustomer } from "@/src/store/slices/checkoutSlice";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

export interface ShippingData {
  includeShipping: boolean;
  deliveryMethod?: string;
  deliveryAddress?: CustomerAddress | NewAddress;
  shippingFee?: number;
  breakdown?: {
    baseFee: number;
    distanceFee: number;
    weightFee: number;
  };
}

interface NewAddress {
  firstName: string;
  lastName: string;
  address1: string;
  address2?: string;
  city: string;
  province?: string;
  country: string;
  zip: string;
  phone?: string;
}

interface ShippingOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (shippingData: ShippingData) => void;
  customer: SelectedCustomer;
  orderTotal: number;
  cartWeight?: number; // Optional weight for calculation
}

type ModalStep =
  | "shipping_choice"
  | "address_selection"
  | "delivery_method"
  | "confirmation";

const DELIVERY_METHODS = [
  {
    id: "standard",
    name: "Standard Delivery",
    description: "3-5 business days",
    baseFee: 200,
  },
  {
    id: "express",
    name: "Express Delivery",
    description: "Same day delivery",
    baseFee: 500,
  },
  {
    id: "local_pickup",
    name: "Local Pickup",
    description: "Customer pickup at store",
    baseFee: 0,
  },
  {
    id: "upcountry",
    name: "Upcountry Delivery",
    description: "5-7 business days",
    baseFee: 800,
  },
  {
    id: "custom",
    name: "Custom Delivery",
    description: "Enter custom delivery method and fee",
    baseFee: 0,
  },
];

export function ShippingOptionsModal({
  visible,
  onClose,
  onConfirm,
  customer,
  orderTotal,
  cartWeight = 1, // Default 1kg
}: ShippingOptionsModalProps) {
  const theme = useTheme();
  const backgroundColor = useThemeColor({}, "surface");

  // Simple currency formatter
  const formatCurrency = (amount: number) => `KSh ${amount.toFixed(2)}`;
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");

  const [currentStep, setCurrentStep] = useState<ModalStep>("shipping_choice");
  const [includeShipping, setIncludeShipping] = useState(false);
  const [selectedAddress, setSelectedAddress] =
    useState<CustomerAddress | null>(null);
  const [newAddress, setNewAddress] = useState<NewAddress>({
    firstName: customer.firstName || "",
    lastName: customer.lastName || "",
    address1: "",
    address2: "",
    city: "",
    province: "",
    country: "Kenya",
    zip: "",
    phone: "",
  });
  const [selectedDeliveryMethod, setSelectedDeliveryMethod] =
    useState<string>("");
  const [isAddingNewAddress, setIsAddingNewAddress] = useState(false);

  // Custom delivery method state
  const [customDeliveryName, setCustomDeliveryName] = useState("");
  const [customDeliveryDescription, setCustomDeliveryDescription] =
    useState("");
  const [customDeliveryFee, setCustomDeliveryFee] = useState("");

  const calculateShippingMutation = useCalculateShippingFee();

  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      setCurrentStep("shipping_choice");
      setIncludeShipping(false);
      setSelectedAddress(null);
      setSelectedDeliveryMethod("");
      setIsAddingNewAddress(false);

      // Reset custom delivery fields
      setCustomDeliveryName("");
      setCustomDeliveryDescription("");
      setCustomDeliveryFee("");

      // Reset new address form with customer data
      setNewAddress({
        firstName: customer.firstName || "",
        lastName: customer.lastName || "",
        address1: "",
        address2: "",
        city: "",
        province: "",
        country: "Kenya",
        zip: "",
        phone: "",
      });

      // Pre-select default address if available
      // Note: SelectedCustomer might not have addresses, so we need to handle this
      const customerAddresses = (customer as any).addresses;
      if (customerAddresses) {
        const defaultAddress = customerAddresses.find(
          (addr: CustomerAddress) => addr.isDefault
        );
        if (defaultAddress) {
          setSelectedAddress(defaultAddress);
        }
      }
    }
  }, [visible, customer]);

  const handleShippingChoice = (choice: boolean) => {
    setIncludeShipping(choice);
    if (!choice) {
      // No shipping, proceed directly to confirmation
      onConfirm({
        includeShipping: false,
      });
    } else {
      // Check if customer has addresses
      const customerAddresses = (customer as any).addresses;
      if (customerAddresses && customerAddresses.length > 0) {
        setCurrentStep("address_selection");
      } else {
        // No addresses, go to add new address
        setIsAddingNewAddress(true);
        setCurrentStep("address_selection");
      }
    }
  };

  const handleAddressSelection = (address: CustomerAddress) => {
    setSelectedAddress(address);
    setIsAddingNewAddress(false);
    setCurrentStep("delivery_method");
  };

  const handleNewAddressSubmit = () => {
    if (!newAddress.address1 || !newAddress.city) {
      return; // Validation
    }
    setCurrentStep("delivery_method");
  };

  const handleDeliveryMethodSelection = (methodId: string) => {
    setSelectedDeliveryMethod(methodId);

    // For custom delivery, don't proceed to confirmation yet
    // User needs to fill in custom details first
    if (methodId === "custom") {
      return;
    }

    // Calculate shipping fee for predefined methods
    const shippingData = {
      deliveryMethod: methodId,
      distanceKm: 10, // Default distance, could be calculated based on address
      weightKg: cartWeight,
    };

    calculateShippingMutation.mutate(shippingData, {
      onSuccess: (result) => {
        setCurrentStep("confirmation");
      },
      onError: (error) => {
        console.error("Failed to calculate shipping:", error);
        // Continue anyway with base fee
        setCurrentStep("confirmation");
      },
    });
  };

  const handleCustomDeliverySubmit = () => {
    if (!customDeliveryName.trim() || !customDeliveryFee.trim()) {
      return; // Validation - require name and fee
    }

    // Proceed to confirmation with custom delivery data
    setCurrentStep("confirmation");
  };

  const handleConfirm = () => {
    const finalAddress = selectedAddress || newAddress;
    const shippingResult = calculateShippingMutation.data;

    // Handle custom delivery method
    if (selectedDeliveryMethod === "custom") {
      const customFee = parseFloat(customDeliveryFee) || 0;
      onConfirm({
        includeShipping: true,
        deliveryMethod: customDeliveryName || "Custom Delivery",
        deliveryAddress: finalAddress,
        shippingFee: customFee,
        breakdown: {
          baseFee: customFee,
          distanceFee: 0,
          weightFee: 0,
        },
      });
      return;
    }

    // Handle predefined delivery methods
    onConfirm({
      includeShipping: true,
      deliveryMethod: selectedDeliveryMethod,
      deliveryAddress: finalAddress,
      shippingFee:
        shippingResult?.shippingFee ||
        DELIVERY_METHODS.find((m) => m.id === selectedDeliveryMethod)
          ?.baseFee ||
        0,
      breakdown: shippingResult?.breakdown,
    });
  };

  const renderShippingChoice = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: textColor }]}>
        Include Shipping?
      </Text>
      <Text style={[styles.stepDescription, { color: textSecondary }]}>
        Would you like to add shipping to this order?
      </Text>

      <View style={styles.choiceContainer}>
        <TouchableOpacity
          style={[styles.choiceButton, { borderColor: primaryColor }]}
          onPress={() => handleShippingChoice(true)}
        >
          <IconSymbol name="truck.box" size={32} color={primaryColor} />
          <Text style={[styles.choiceTitle, { color: textColor }]}>
            Yes, Add Shipping
          </Text>
          <Text style={[styles.choiceDescription, { color: textSecondary }]}>
            Deliver to customer address
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.choiceButton, { borderColor: borderColor }]}
          onPress={() => handleShippingChoice(false)}
        >
          <IconSymbol name="bag" size={32} color={textSecondary} />
          <Text style={[styles.choiceTitle, { color: textColor }]}>
            No Shipping
          </Text>
          <Text style={[styles.choiceDescription, { color: textSecondary }]}>
            Customer pickup or other arrangement
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAddressSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: textColor }]}>
        Select Delivery Address
      </Text>

      {!isAddingNewAddress &&
        (customer as any).addresses &&
        (customer as any).addresses.length > 0 && (
          <View style={styles.addressList}>
            {(customer as any).addresses.map((address: CustomerAddress) => (
              <TouchableOpacity
                key={address.id}
                style={[
                  styles.addressCard,
                  {
                    borderColor:
                      selectedAddress?.id === address.id
                        ? primaryColor
                        : borderColor,
                    backgroundColor:
                      selectedAddress?.id === address.id
                        ? primaryColor + "10"
                        : backgroundColor,
                  },
                ]}
                onPress={() => handleAddressSelection(address)}
              >
                <View style={styles.addressInfo}>
                  <Text style={[styles.addressName, { color: textColor }]}>
                    {address.firstName} {address.lastName}
                  </Text>
                  <Text style={[styles.addressText, { color: textSecondary }]}>
                    {address.address1}
                    {address.address2 && `, ${address.address2}`}
                  </Text>
                  <Text style={[styles.addressText, { color: textSecondary }]}>
                    {address.city}, {address.province} {address.zip}
                  </Text>
                  {address.phone && (
                    <Text
                      style={[styles.addressText, { color: textSecondary }]}
                    >
                      📞 {address.phone}
                    </Text>
                  )}
                </View>
                {address.isDefault && (
                  <View
                    style={[
                      styles.defaultBadge,
                      { backgroundColor: primaryColor },
                    ]}
                  >
                    <Text style={styles.defaultBadgeText}>Default</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}

      <ModernButton
        title={isAddingNewAddress ? "Cancel" : "Add New Address"}
        onPress={() => setIsAddingNewAddress(!isAddingNewAddress)}
        variant="outline"
        style={styles.addAddressButton}
      />

      {isAddingNewAddress && (
        <View style={styles.newAddressForm}>
          <Text style={[styles.formTitle, { color: textColor }]}>
            New Delivery Address
          </Text>

          <View style={styles.inputRow}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.inputLabel, { color: textSecondary }]}>
                First Name
              </Text>
              <TextInput
                style={[styles.input, { borderColor, color: textColor }]}
                value={newAddress.firstName}
                onChangeText={(text) =>
                  setNewAddress((prev) => ({ ...prev, firstName: text }))
                }
                placeholder="First name"
                placeholderTextColor={textSecondary}
              />
            </View>
            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.inputLabel, { color: textSecondary }]}>
                Last Name
              </Text>
              <TextInput
                style={[styles.input, { borderColor, color: textColor }]}
                value={newAddress.lastName}
                onChangeText={(text) =>
                  setNewAddress((prev) => ({ ...prev, lastName: text }))
                }
                placeholder="Last name"
                placeholderTextColor={textSecondary}
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Address Line 1 *
            </Text>
            <TextInput
              style={[styles.input, { borderColor, color: textColor }]}
              value={newAddress.address1}
              onChangeText={(text) =>
                setNewAddress((prev) => ({ ...prev, address1: text }))
              }
              placeholder="Street address"
              placeholderTextColor={textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Address Line 2
            </Text>
            <TextInput
              style={[styles.input, { borderColor, color: textColor }]}
              value={newAddress.address2}
              onChangeText={(text) =>
                setNewAddress((prev) => ({ ...prev, address2: text }))
              }
              placeholder="Apartment, suite, etc."
              placeholderTextColor={textSecondary}
            />
          </View>

          <View style={styles.inputRow}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.inputLabel, { color: textSecondary }]}>
                City *
              </Text>
              <TextInput
                style={[styles.input, { borderColor, color: textColor }]}
                value={newAddress.city}
                onChangeText={(text) =>
                  setNewAddress((prev) => ({ ...prev, city: text }))
                }
                placeholder="City"
                placeholderTextColor={textSecondary}
              />
            </View>
            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.inputLabel, { color: textSecondary }]}>
                Postal Code
              </Text>
              <TextInput
                style={[styles.input, { borderColor, color: textColor }]}
                value={newAddress.zip}
                onChangeText={(text) =>
                  setNewAddress((prev) => ({ ...prev, zip: text }))
                }
                placeholder="00100"
                placeholderTextColor={textSecondary}
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Phone Number
            </Text>
            <TextInput
              style={[styles.input, { borderColor, color: textColor }]}
              value={newAddress.phone}
              onChangeText={(text) =>
                setNewAddress((prev) => ({ ...prev, phone: text }))
              }
              placeholder="+254 700 000 000"
              placeholderTextColor={textSecondary}
              keyboardType="phone-pad"
            />
          </View>

          <ModernButton
            title="Use This Address"
            onPress={handleNewAddressSubmit}
            disabled={!newAddress.address1 || !newAddress.city}
            style={styles.useAddressButton}
          />
        </View>
      )}

      {selectedAddress && !isAddingNewAddress && (
        <ModernButton
          title="Continue"
          onPress={() => setCurrentStep("delivery_method")}
          style={styles.continueButton}
        />
      )}
    </View>
  );

  const renderDeliveryMethodSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: textColor }]}>
        Select Delivery Method
      </Text>
      <Text style={[styles.stepDescription, { color: textSecondary }]}>
        Choose how you'd like to deliver this order
      </Text>

      <View style={styles.deliveryMethodList}>
        {DELIVERY_METHODS.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.deliveryMethodCard,
              {
                borderColor:
                  selectedDeliveryMethod === method.id
                    ? primaryColor
                    : borderColor,
                backgroundColor:
                  selectedDeliveryMethod === method.id
                    ? primaryColor + "10"
                    : backgroundColor,
              },
            ]}
            onPress={() => handleDeliveryMethodSelection(method.id)}
          >
            <View style={styles.deliveryMethodInfo}>
              <Text style={[styles.deliveryMethodName, { color: textColor }]}>
                {method.name}
              </Text>
              <Text
                style={[
                  styles.deliveryMethodDescription,
                  { color: textSecondary },
                ]}
              >
                {method.description}
              </Text>
            </View>
            <View style={styles.deliveryMethodPrice}>
              {method.id === "custom" ? (
                <Text
                  style={[styles.deliveryMethodFee, { color: primaryColor }]}
                >
                  Custom
                </Text>
              ) : (
                <Text
                  style={[styles.deliveryMethodFee, { color: primaryColor }]}
                >
                  {method.baseFee === 0
                    ? "Free"
                    : formatCurrency(method.baseFee)}
                </Text>
              )}
              {method.id === "express" && (
                <Text
                  style={[styles.deliveryMethodExtra, { color: textSecondary }]}
                >
                  + distance fee
                </Text>
              )}
              {method.id === "upcountry" && (
                <Text
                  style={[styles.deliveryMethodExtra, { color: textSecondary }]}
                >
                  + distance & weight
                </Text>
              )}
              {method.id === "custom" && (
                <Text
                  style={[styles.deliveryMethodExtra, { color: textSecondary }]}
                >
                  Enter details below
                </Text>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {calculateShippingMutation.isPending && (
        <View style={styles.calculatingContainer}>
          <ActivityIndicator size="small" color={primaryColor} />
          <Text style={[styles.calculatingText, { color: textSecondary }]}>
            Calculating shipping fee...
          </Text>
        </View>
      )}

      {/* Custom Delivery Form */}
      {selectedDeliveryMethod === "custom" && (
        <View
          style={[
            styles.customDeliveryForm,
            {
              borderColor: borderColor,
              backgroundColor: theme.colors.surface,
              borderRadius: theme.borderRadius.md,
            },
          ]}
        >
          <Text style={[styles.formTitle, { color: textColor }]}>
            Custom Delivery Details
          </Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Delivery Method Name *
            </Text>
            <TextInput
              style={[styles.input, { borderColor, color: textColor }]}
              value={customDeliveryName}
              onChangeText={setCustomDeliveryName}
              placeholder="e.g., Motorcycle Delivery, Special Courier"
              placeholderTextColor={textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Description (Optional)
            </Text>
            <TextInput
              style={[styles.input, { borderColor, color: textColor }]}
              value={customDeliveryDescription}
              onChangeText={setCustomDeliveryDescription}
              placeholder="e.g., Same day delivery via motorcycle"
              placeholderTextColor={textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Delivery Fee (KSh) *
            </Text>
            <TextInput
              style={[styles.input, { borderColor, color: textColor }]}
              value={customDeliveryFee}
              onChangeText={setCustomDeliveryFee}
              placeholder="0"
              placeholderTextColor={textSecondary}
              keyboardType="numeric"
            />
          </View>

          <ModernButton
            title="Continue with Custom Delivery"
            onPress={handleCustomDeliverySubmit}
            disabled={!customDeliveryName.trim() || !customDeliveryFee.trim()}
            style={styles.customDeliveryButton}
          />
        </View>
      )}
    </View>
  );

  const renderConfirmation = () => {
    const finalAddress = selectedAddress || newAddress;
    const shippingResult = calculateShippingMutation.data;

    // Calculate shipping fee based on method type
    let shippingFee = 0;
    let deliveryMethodName = "";
    let deliveryMethodDescription = "";

    if (selectedDeliveryMethod === "custom") {
      shippingFee = parseFloat(customDeliveryFee) || 0;
      deliveryMethodName = customDeliveryName || "Custom Delivery";
      deliveryMethodDescription =
        customDeliveryDescription || "Custom delivery method";
    } else {
      shippingFee =
        shippingResult?.shippingFee ||
        DELIVERY_METHODS.find((m) => m.id === selectedDeliveryMethod)
          ?.baseFee ||
        0;
      const method = DELIVERY_METHODS.find(
        (m) => m.id === selectedDeliveryMethod
      );
      deliveryMethodName = method?.name || "";
      deliveryMethodDescription = method?.description || "";
    }

    const newTotal = orderTotal + shippingFee;

    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.stepTitle, { color: textColor }]}>
          Order Summary
        </Text>
        <Text style={[styles.stepDescription, { color: textSecondary }]}>
          Review your order with shipping details
        </Text>

        <ModernCard
          style={[styles.summaryCard, { backgroundColor }]}
          variant="outlined"
        >
          <View style={styles.summarySection}>
            <Text style={[styles.summaryTitle, { color: textColor }]}>
              Delivery Address
            </Text>
            <Text style={[styles.summaryText, { color: textSecondary }]}>
              {finalAddress.firstName} {finalAddress.lastName}
            </Text>
            <Text style={[styles.summaryText, { color: textSecondary }]}>
              {finalAddress.address1}
              {finalAddress.address2 && `, ${finalAddress.address2}`}
            </Text>
            <Text style={[styles.summaryText, { color: textSecondary }]}>
              {finalAddress.city}, {finalAddress.province || ""}{" "}
              {finalAddress.zip}
            </Text>
            {finalAddress.phone && (
              <Text style={[styles.summaryText, { color: textSecondary }]}>
                📞 {finalAddress.phone}
              </Text>
            )}
          </View>

          <View
            style={[
              styles.summarySection,
              { borderTopWidth: 1, borderTopColor: borderColor },
            ]}
          >
            <Text style={[styles.summaryTitle, { color: textColor }]}>
              Delivery Method
            </Text>
            <Text style={[styles.summaryText, { color: textSecondary }]}>
              {deliveryMethodName}
            </Text>
            <Text style={[styles.summaryText, { color: textSecondary }]}>
              {deliveryMethodDescription}
            </Text>
            {selectedDeliveryMethod === "custom" && (
              <Text
                style={[
                  styles.summaryText,
                  { color: primaryColor, fontWeight: "500" },
                ]}
              >
                Custom delivery method
              </Text>
            )}
          </View>

          <View
            style={[
              styles.summarySection,
              { borderTopWidth: 1, borderTopColor: borderColor },
            ]}
          >
            <Text style={[styles.summaryTitle, { color: textColor }]}>
              Order Total
            </Text>
            <View style={styles.totalBreakdown}>
              <View style={styles.totalRow}>
                <Text style={[styles.totalLabel, { color: textSecondary }]}>
                  Subtotal
                </Text>
                <Text style={[styles.totalValue, { color: textColor }]}>
                  {formatCurrency(orderTotal)}
                </Text>
              </View>
              <View style={styles.totalRow}>
                <Text style={[styles.totalLabel, { color: textSecondary }]}>
                  Shipping Fee
                </Text>
                <Text style={[styles.totalValue, { color: textColor }]}>
                  {shippingFee === 0 ? "Free" : formatCurrency(shippingFee)}
                </Text>
              </View>
              <View style={[styles.totalRow, styles.finalTotalRow]}>
                <Text style={[styles.finalTotalLabel, { color: textColor }]}>
                  Total
                </Text>
                <Text style={[styles.finalTotalValue, { color: primaryColor }]}>
                  {formatCurrency(newTotal)}
                </Text>
              </View>
            </View>
          </View>
        </ModernCard>

        <View style={styles.confirmationActions}>
          <ModernButton
            title="Back"
            onPress={() => setCurrentStep("delivery_method")}
            variant="outline"
            style={styles.backButton}
          />
          <ModernButton
            title="Confirm Shipping"
            onPress={handleConfirm}
            style={styles.confirmButton}
          />
        </View>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor }]}>
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={textColor} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: textColor }]}>
            Shipping Options
          </Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {currentStep === "shipping_choice" && renderShippingChoice()}
          {currentStep === "address_selection" && renderAddressSelection()}
          {currentStep === "delivery_method" && renderDeliveryMethodSelection()}
          {currentStep === "confirmation" && renderConfirmation()}
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  stepContainer: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 8,
    textAlign: "center",
  },
  stepDescription: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 32,
  },
  choiceContainer: {
    gap: 16,
  },
  choiceButton: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 24,
    alignItems: "center",
    gap: 8,
  },
  choiceTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  choiceDescription: {
    fontSize: 14,
    textAlign: "center",
  },
  addressList: {
    gap: 12,
    marginBottom: 16,
  },
  addressCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    position: "relative",
  },
  addressInfo: {
    gap: 4,
  },
  addressName: {
    fontSize: 16,
    fontWeight: "600",
  },
  addressText: {
    fontSize: 14,
  },
  defaultBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  defaultBadgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  addAddressButton: {
    marginBottom: 16,
  },
  newAddressForm: {
    gap: 16,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
  },
  inputRow: {
    flexDirection: "row",
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  useAddressButton: {
    marginTop: 8,
  },
  continueButton: {
    marginTop: 16,
  },
  deliveryMethodList: {
    gap: 12,
    marginBottom: 16,
  },
  deliveryMethodCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  deliveryMethodInfo: {
    flex: 1,
    gap: 4,
  },
  deliveryMethodName: {
    fontSize: 16,
    fontWeight: "600",
  },
  deliveryMethodDescription: {
    fontSize: 14,
  },
  deliveryMethodPrice: {
    alignItems: "flex-end",
    gap: 2,
  },
  deliveryMethodFee: {
    fontSize: 16,
    fontWeight: "700",
  },
  deliveryMethodExtra: {
    fontSize: 12,
  },
  calculatingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    padding: 16,
  },
  calculatingText: {
    fontSize: 14,
  },
  summaryCard: {
    marginBottom: 24,
  },
  summarySection: {
    padding: 16,
    gap: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 14,
    lineHeight: 20,
  },
  totalBreakdown: {
    gap: 8,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalLabel: {
    fontSize: 14,
  },
  totalValue: {
    fontSize: 14,
    fontWeight: "500",
  },
  finalTotalRow: {
    borderTopWidth: 1,
    borderTopColor: "#E5E7EB",
    paddingTop: 8,
    marginTop: 8,
  },
  finalTotalLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  finalTotalValue: {
    fontSize: 18,
    fontWeight: "700",
  },
  confirmationActions: {
    flexDirection: "row",
    gap: 12,
  },
  backButton: {
    flex: 1,
  },
  confirmButton: {
    flex: 2,
  },
  customDeliveryForm: {
    marginTop: 16,
    padding: 16,
    borderWidth: 1,
    gap: 16,
    // borderColor, backgroundColor, and borderRadius will be set dynamically using theme
    // Using consistent spacing and styling with the rest of the app
  },
  customDeliveryButton: {
    marginTop: 8,
  },
});
