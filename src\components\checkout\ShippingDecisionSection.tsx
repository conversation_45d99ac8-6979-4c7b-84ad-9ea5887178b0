import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/contexts/ThemeContext";

interface ShippingData {
  fee: number;
  data: {
    address: string;
    deliveryMethod: string;
    customerName: string;
    phoneNumber: string;
  } | null;
}

interface ShippingDecisionSectionProps {
  cartTotal: number;
  onShippingUpdate: (shipping: ShippingData) => void;
  disabled?: boolean;
  selectedCustomer?: any;
}

// Simplified to only use custom delivery
const DELIVERY_METHODS = [
  { id: "custom", name: "Custom Delivery", baseFee: 0 },
];

export const ShippingDecisionSection: React.FC<
  ShippingDecisionSectionProps
> = ({ cartTotal, onShippingUpdate, disabled = false, selectedCustomer }) => {
  const theme = useTheme();
  const [shippingChoice, setShippingChoice] = useState<
    "include" | "none" | null
  >(null);
  const [shippingForm, setShippingForm] = useState({
    address: "",
    deliveryMethod: "custom", // Always use custom delivery
    customFee: 0,
    customerName: "",
    phoneNumber: "",
  });

  // Update customer info when selectedCustomer changes and prefill if available
  useEffect(() => {
    if (selectedCustomer) {
      // Get customer display name or fallback to firstName
      const customerDisplayName = 
        selectedCustomer.displayName || 
        selectedCustomer.firstName || 
        `${selectedCustomer.firstName || ""} ${selectedCustomer.lastName || ""}`.trim() ||
        "Customer";
      
      // Get customer phone
      const customerPhone = selectedCustomer.phone || "";
      
      // Check if customer has a default delivery address
      let defaultAddress = "";
      if (selectedCustomer.addresses && selectedCustomer.addresses.length > 0) {
        const primaryAddress = selectedCustomer.addresses.find((addr: any) => addr.default) || 
                              selectedCustomer.addresses[0];
        if (primaryAddress) {
          defaultAddress = `${primaryAddress.address1 || ""}${primaryAddress.address2 ? `, ${primaryAddress.address2}` : ""}${primaryAddress.city ? `, ${primaryAddress.city}` : ""}${primaryAddress.province ? `, ${primaryAddress.province}` : ""}`.trim().replace(/^,\s*/, '');
        }
      }

      setShippingForm((prev) => ({
        ...prev,
        customerName: customerDisplayName,
        phoneNumber: customerPhone,
        address: defaultAddress, // Prefill address if available
      }));
    } else {
      // Clear form when no customer is selected
      setShippingForm((prev) => ({
        ...prev,
        customerName: "",
        phoneNumber: "",
        address: "",
      }));
    }
  }, [selectedCustomer]);

  const calculateShippingFee = () => {
    if (shippingChoice !== "include") return 0;

    const selectedMethod = DELIVERY_METHODS.find(
      (m) => m.id === shippingForm.deliveryMethod
    );
    if (!selectedMethod) return 0;

    if (selectedMethod.id === "custom") {
      return shippingForm.customFee;
    }

    return selectedMethod.baseFee;
  };

  const handleShippingChoice = (choice: "include" | "none") => {
    setShippingChoice(choice);

    if (choice === "none") {
      // No shipping - immediately update with zero fee
      onShippingUpdate({
        fee: 0,
        data: null,
      });
    } else {
      // Reset form for shipping inclusion
      setShippingForm((prev) => ({
        ...prev,
        address: "",
        deliveryMethod: "custom", // Always set to custom
        customFee: 0,
      }));
    }
  };

  const handleFormUpdate = (field: string, value: string | number) => {
    const updatedForm = { ...shippingForm, [field]: value };
    setShippingForm(updatedForm);

    // Auto-update shipping data when form changes
    if (
      shippingChoice === "include" &&
      updatedForm.address &&
      updatedForm.customerName &&
      updatedForm.customFee > 0
    ) {
      onShippingUpdate({
        fee: updatedForm.customFee,
        data: {
          address: updatedForm.address,
          deliveryMethod: "custom",
          customerName: updatedForm.customerName,
          phoneNumber: updatedForm.phoneNumber,
        },
      });
    }
  };

  const isFormComplete = () => {
    if (shippingChoice === "none") return true;
    if (shippingChoice === "include") {
      return (
        shippingForm.address &&
        shippingForm.customerName &&
        shippingForm.customFee > 0 // Require a delivery fee greater than 0
      );
    }
    return false;
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: 16,
      marginVertical: 8,
      opacity: disabled ? 0.5 : 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 16,
    },
    title: {
      fontSize: 18,
      fontWeight: "600",
      color: theme.colors.text,
      marginLeft: 8,
    },
    choiceContainer: {
      flexDirection: "row",
      marginBottom: 16,
      gap: 12,
    },
    choiceButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      borderWidth: 2,
      alignItems: "center",
    },
    choiceButtonSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primary + "20",
    },
    choiceButtonUnselected: {
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.background,
    },
    choiceText: {
      fontSize: 14,
      fontWeight: "500",
    },
    choiceTextSelected: {
      color: theme.colors.primary,
    },
    choiceTextUnselected: {
      color: theme.colors.textSecondary,
    },
    formContainer: {
      marginTop: 16,
    },
    inputGroup: {
      marginBottom: 12,
    },
    label: {
      fontSize: 14,
      fontWeight: "500",
      color: theme.colors.text,
      marginBottom: 4,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: theme.colors.text,
      backgroundColor: theme.colors.background,
    },
    pickerContainer: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      backgroundColor: theme.colors.background,
    },
    totalDisplay: {
      marginTop: 16,
      padding: 12,
      backgroundColor: theme.colors.primary + "10",
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    totalText: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.primary,
      textAlign: "center",
    },
    statusIndicator: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 8,
    },
    statusText: {
      marginLeft: 4,
      fontSize: 14,
      fontWeight: "500",
    },
    statusComplete: {
      color: theme.colors.success,
    },
    statusIncomplete: {
      color: theme.colors.warning,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="location" size={20} color={theme.colors.primary} />
        <Text style={styles.title}>Shipping Decision</Text>
        <Text style={{ color: theme.colors.error, marginLeft: 4 }}>*</Text>
      </View>

      {disabled && (
        <Text style={{ color: theme.colors.textSecondary, marginBottom: 12 }}>
          Please select a customer first
        </Text>
      )}

      <View style={styles.choiceContainer}>
        <TouchableOpacity
          style={[
            styles.choiceButton,
            shippingChoice === "none"
              ? styles.choiceButtonSelected
              : styles.choiceButtonUnselected,
          ]}
          onPress={() => handleShippingChoice("none")}
          disabled={disabled}
        >
          <Ionicons
            name="storefront"
            size={20}
            color={
              shippingChoice === "none"
                ? theme.colors.primary
                : theme.colors.textSecondary
            }
          />
          <Text
            style={[
              styles.choiceText,
              shippingChoice === "none"
                ? styles.choiceTextSelected
                : styles.choiceTextUnselected,
            ]}
          >
            No Shipping
          </Text>
          <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
            (Pickup/Local)
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.choiceButton,
            shippingChoice === "include"
              ? styles.choiceButtonSelected
              : styles.choiceButtonUnselected,
          ]}
          onPress={() => handleShippingChoice("include")}
          disabled={disabled}
        >
          <Ionicons
            name="car"
            size={20}
            color={
              shippingChoice === "include"
                ? theme.colors.primary
                : theme.colors.textSecondary
            }
          />
          <Text
            style={[
              styles.choiceText,
              shippingChoice === "include"
                ? styles.choiceTextSelected
                : styles.choiceTextUnselected,
            ]}
          >
            Include Shipping
          </Text>
          <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
            (Delivery)
          </Text>
        </TouchableOpacity>
      </View>

      {shippingChoice === "include" && (
        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Customer Name *</Text>
            <TextInput
              style={styles.input}
              value={shippingForm.customerName}
              onChangeText={(value) => handleFormUpdate("customerName", value)}
              placeholder="Enter customer name"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Phone Number</Text>
            <TextInput
              style={styles.input}
              value={shippingForm.phoneNumber}
              onChangeText={(value) => handleFormUpdate("phoneNumber", value)}
              placeholder="Enter phone number"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Delivery Address *</Text>
            <TextInput
              style={styles.input}
              value={shippingForm.address}
              onChangeText={(value) => handleFormUpdate("address", value)}
              placeholder="Enter delivery address"
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={2}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Delivery Fee *</Text>
            <TextInput
              style={styles.input}
              value={shippingForm.customFee.toString()}
              onChangeText={(value) =>
                handleFormUpdate("customFee", parseFloat(value) || 0)
              }
              placeholder="Enter delivery fee (KSh)"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>
        </View>
      )}

      {shippingChoice && (
        <View style={styles.totalDisplay}>
          <Text style={styles.totalText}>
            Order Total: KSh {(cartTotal + calculateShippingFee()).toFixed(2)}
          </Text>
          {calculateShippingFee() > 0 && (
            <Text
              style={{
                fontSize: 14,
                color: theme.colors.textSecondary,
                textAlign: "center",
                marginTop: 4,
              }}
            >
              (Cart: KSh {cartTotal.toFixed(2)} + Shipping: KSh{" "}
              {calculateShippingFee().toFixed(2)})
            </Text>
          )}
        </View>
      )}

      <View style={styles.statusIndicator}>
        <Ionicons
          name={isFormComplete() ? "checkmark-circle" : "alert-circle"}
          size={16}
          color={isFormComplete() ? theme.colors.success : theme.colors.warning}
        />
        <Text
          style={[
            styles.statusText,
            isFormComplete() ? styles.statusComplete : styles.statusIncomplete,
          ]}
        >
          {isFormComplete()
            ? "Shipping decision complete"
            : "Complete shipping decision to proceed"}
        </Text>
      </View>
    </View>
  );
};
