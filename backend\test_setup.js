/**
 * Test Setup Script
 * Verifies that the complete setup worked correctly
 */

require('dotenv').config();
const mysql = require('mysql2/promise');
const axios = require('axios');

class SetupTester {
  constructor() {
    this.connection = null;
    this.baseURL = 'http://localhost:3001/api';
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async test(name, testFunction) {
    try {
      console.log(`🧪 Testing: ${name}`);
      await testFunction();
      console.log(`✅ PASSED: ${name}`);
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED' });
    } catch (error) {
      console.log(`❌ FAILED: ${name}`);
      console.log(`   Error: ${error.message}`);
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', error: error.message });
    }
  }

  async connectToDatabase() {
    this.connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'dukalink',
      password: process.env.DB_PASSWORD || 'dukalink_secure_password_2024',
      database: process.env.DB_NAME || 'dukalink_pos',
      charset: 'utf8mb4'
    });
  }

  async testDatabaseConnection() {
    await this.connectToDatabase();
    const [rows] = await this.connection.execute('SELECT 1 as test');
    if (rows[0].test !== 1) {
      throw new Error('Database connection test failed');
    }
  }

  async testTablesExist() {
    const tables = ['pos_staff', 'staff_permissions', 'sales_agents', 'pos_terminals'];
    
    for (const table of tables) {
      const [rows] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
        [process.env.DB_NAME || 'dukalink_pos', table]
      );
      
      if (rows[0].count === 0) {
        throw new Error(`Table ${table} does not exist`);
      }
    }
  }

  async testSeededData() {
    // Test staff data
    const [staffRows] = await this.connection.execute('SELECT COUNT(*) as count FROM pos_staff');
    if (staffRows[0].count < 3) {
      throw new Error(`Expected at least 3 staff members, found ${staffRows[0].count}`);
    }

    // Test sales agents data
    const [agentRows] = await this.connection.execute('SELECT COUNT(*) as count FROM sales_agents');
    if (agentRows[0].count < 1) {
      throw new Error(`Expected at least 1 sales agent, found ${agentRows[0].count}`);
    }

    // Test permissions data
    const [permRows] = await this.connection.execute('SELECT COUNT(*) as count FROM staff_permissions');
    if (permRows[0].count < 10) {
      throw new Error(`Expected at least 10 permissions, found ${permRows[0].count}`);
    }
  }

  async testAPIHealth() {
    const response = await axios.get(`${this.baseURL}/health`);
    if (response.status !== 200) {
      throw new Error(`Health check failed with status ${response.status}`);
    }
  }

  async testAuthentication() {
    // Test admin login
    const response = await axios.post(`${this.baseURL}/pos/login`, {
      username: 'admin1',
      password: 'admin123'
    });

    if (!response.data.success) {
      throw new Error('Admin login failed');
    }

    if (!response.data.data.token) {
      throw new Error('No token returned from login');
    }

    // Test token verification
    const verifyResponse = await axios.get(`${this.baseURL}/pos/verify`, {
      headers: { Authorization: `Bearer ${response.data.data.token}` }
    });

    if (!verifyResponse.data.success) {
      throw new Error('Token verification failed');
    }
  }

  async testStaffEndpoints() {
    // Login as admin
    const loginResponse = await axios.post(`${this.baseURL}/pos/login`, {
      username: 'admin1',
      password: 'admin123'
    });

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };

    // Test staff list
    const staffResponse = await axios.get(`${this.baseURL}/staff`, { headers });
    if (!staffResponse.data.success || !Array.isArray(staffResponse.data.data.staffMembers)) {
      throw new Error('Staff list endpoint failed');
    }

    if (staffResponse.data.data.staffMembers.length < 3) {
      throw new Error(`Expected at least 3 staff members, found ${staffResponse.data.data.staffMembers.length}`);
    }
  }

  async testSalesAgentEndpoints() {
    // Login as admin
    const loginResponse = await axios.post(`${this.baseURL}/pos/login`, {
      username: 'admin1',
      password: 'admin123'
    });

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };

    // Test sales agents list
    const agentsResponse = await axios.get(`${this.baseURL}/sales-agents`, { headers });
    if (!agentsResponse.data.success || !Array.isArray(agentsResponse.data.data.salesAgents)) {
      throw new Error('Sales agents list endpoint failed');
    }
  }

  async runAllTests() {
    console.log('🚀 Starting setup verification tests...\n');

    try {
      // Database tests
      await this.test('Database Connection', () => this.testDatabaseConnection());
      await this.test('Database Tables Exist', () => this.testTablesExist());
      await this.test('Seeded Data Present', () => this.testSeededData());

      // API tests (requires server to be running)
      try {
        await this.test('API Health Check', () => this.testAPIHealth());
        await this.test('Authentication System', () => this.testAuthentication());
        await this.test('Staff Endpoints', () => this.testStaffEndpoints());
        await this.test('Sales Agent Endpoints', () => this.testSalesAgentEndpoints());
      } catch (error) {
        console.log('\n⚠️  API tests failed - make sure server is running with "npm run dev"');
      }

      // Print results
      console.log('\n📊 Test Results:');
      console.log(`✅ Passed: ${this.results.passed}`);
      console.log(`❌ Failed: ${this.results.failed}`);
      console.log(`📈 Success Rate: ${Math.round((this.results.passed / (this.results.passed + this.results.failed)) * 100)}%`);

      if (this.results.failed === 0) {
        console.log('\n🎉 All tests passed! Setup is complete and working correctly.');
        console.log('\n📋 Next steps:');
        console.log('1. Start the server: npm run dev');
        console.log('2. Test the React Native app');
        console.log('3. Verify RBAC functionality');
      } else {
        console.log('\n⚠️  Some tests failed. Please check the errors above.');
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Make sure MySQL is running');
        console.log('2. Check .env configuration');
        console.log('3. Re-run setup: npm run setup');
        console.log('4. Start server: npm run dev');
      }

    } catch (error) {
      console.error('\n❌ Test suite failed:', error.message);
    } finally {
      if (this.connection) {
        await this.connection.end();
      }
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new SetupTester();
  tester.runAllTests().catch(console.error);
}

module.exports = SetupTester;
