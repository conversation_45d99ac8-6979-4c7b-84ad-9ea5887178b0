/**
 * Discount Management Dashboard Screen
 *
 * Comprehensive discount management interface for managers and admins.
 * Provides discount rule creation/editing, staff permission assignment,
 * usage analytics, and commission-based discount configuration with proper RBAC.
 */

import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { PermissionGate } from "@/src/components/rbac";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface DiscountRule {
  id: string;
  name: string;
  description: string;
  discountType: "percentage" | "fixed_amount" | "bogo" | "loyalty_points";
  discountValue: number;
  minPurchaseAmount?: number;
  maxDiscountAmount?: number;
  customerEligibility: "all" | "loyalty_tier" | "new_customers";
  loyaltyTierRequired?: string;
  maxUsesPerCustomer?: number;
  maxUsesTotal?: number;
  usageCount: number;
  isActive: boolean;
  validFrom?: string;
  validTo?: string;
  createdAt: string;
  updatedAt: string;
}

interface DiscountAnalytics {
  totalRules: number;
  activeRules: number;
  totalUsage: number;
  totalSavings: number;
  averageDiscount: number;
  topRules: Array<{
    ruleId: string;
    ruleName: string;
    usageCount: number;
    totalSavings: number;
  }>;
}

interface StaffPermission {
  staffId: string;
  staffName: string;
  ruleId: string;
  ruleName: string;
  permissionType: "apply" | "modify";
  maxDiscountPercentage?: number;
  maxDiscountAmount?: number;
  grantedAt: string;
  grantedBy: string;
}

export default function DiscountManagementScreen() {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const { hasPermission } = useRBAC();
  const apiClient = getAPIClient();

  // Check permissions
  const canViewDiscounts =
    hasPermission("view_discounts") || hasPermission("apply_discounts");
  const canManageDiscounts = hasPermission("manage_discounts");
  const canViewAnalytics = hasPermission("view_analytics");

  // Create theme-aware styles
  const styles = createStyles(theme);

  // State management
  const [activeTab, setActiveTab] = useState<
    "rules" | "permissions" | "analytics" | "create"
  >("rules");
  const [discountRules, setDiscountRules] = useState<DiscountRule[]>([]);
  const [staffPermissions, setStaffPermissions] = useState<StaffPermission[]>(
    []
  );
  const [analytics, setAnalytics] = useState<DiscountAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [selectedRule, setSelectedRule] = useState<DiscountRule | null>(null);

  // Form states
  const [newRule, setNewRule] = useState({
    name: "",
    description: "",
    discountType: "percentage" as const,
    discountValue: "",
    minPurchaseAmount: "",
    maxDiscountAmount: "",
    customerEligibility: "all" as const,
  });

  // Fetch data
  const fetchData = useCallback(async () => {
    try {
      setError(null);

      const promises = [];

      if (canViewDiscounts || canManageDiscounts) {
        promises.push(apiClient.getDiscountRules());
      }

      if (canManageDiscounts) {
        promises.push(apiClient.getDiscountPermissions());
      }

      if (canViewAnalytics) {
        promises.push(apiClient.getDiscountAnalytics());
      }

      const results = await Promise.allSettled(promises);

      let ruleIndex = 0;
      if (canViewDiscounts || canManageDiscounts) {
        const rulesResult = results[ruleIndex];
        if (rulesResult.status === "fulfilled" && rulesResult.value.success) {
          setDiscountRules(rulesResult.value.data.rules || []);
        }
        ruleIndex++;
      }

      if (canManageDiscounts) {
        const permissionsResult = results[ruleIndex];
        if (
          permissionsResult.status === "fulfilled" &&
          permissionsResult.value.success
        ) {
          setStaffPermissions(permissionsResult.value.data.permissions || []);
        }
        ruleIndex++;
      }

      if (canViewAnalytics) {
        const analyticsResult = results[ruleIndex];
        if (
          analyticsResult.status === "fulfilled" &&
          analyticsResult.value.success
        ) {
          setAnalytics(analyticsResult.value.data || null);
        }
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch discount data"
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [canViewDiscounts, canManageDiscounts, canViewAnalytics, apiClient]);

  useEffect(() => {
    if (canViewDiscounts || canManageDiscounts || canViewAnalytics) {
      fetchData();
    }
  }, [fetchData]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchData();
  }, [fetchData]);

  // Create new discount rule
  const handleCreateRule = async () => {
    try {
      const ruleData = {
        ...newRule,
        discountValue: parseFloat(newRule.discountValue),
        minPurchaseAmount: newRule.minPurchaseAmount
          ? parseFloat(newRule.minPurchaseAmount)
          : undefined,
        maxDiscountAmount: newRule.maxDiscountAmount
          ? parseFloat(newRule.maxDiscountAmount)
          : undefined,
      };

      const response = await apiClient.createDiscountRule(ruleData);

      if (response.success) {
        setShowCreateModal(false);
        setNewRule({
          name: "",
          description: "",
          discountType: "percentage",
          discountValue: "",
          minPurchaseAmount: "",
          maxDiscountAmount: "",
          customerEligibility: "all",
        });
        fetchData();
        Alert.alert("Success", "Discount rule created successfully");
      } else {
        Alert.alert(
          "Error",
          response.error || "Failed to create discount rule"
        );
      }
    } catch (error) {
      Alert.alert("Error", "Failed to create discount rule");
    }
  };

  // Toggle rule active status
  const handleToggleRule = async (rule: DiscountRule) => {
    try {
      const response = await apiClient.updateDiscountRule(rule.id, {
        isActive: !rule.isActive,
      });

      if (response.success) {
        fetchData();
      } else {
        Alert.alert(
          "Error",
          response.error || "Failed to update discount rule"
        );
      }
    } catch (error) {
      Alert.alert("Error", "Failed to update discount rule");
    }
  };

  // Permission check
  if (!canViewDiscounts && !canManageDiscounts && !canViewAnalytics) {
    return (
      <ScreenWrapper title="Discount Management" showBackButton>
        <View style={styles.centerContent}>
          <IconSymbol
            name="lock.fill"
            size={48}
            color={theme.colors.textSecondary}
          />
          <ThemedText
            variant="body"
            color="secondary"
            style={styles.noAccessText}
          >
            You don&apos;t have permission to access discount management
            features.
          </ThemedText>
        </View>
      </ScreenWrapper>
    );
  }

  // Render discount rules list
  const renderRulesList = () => (
    <FlatList
      style={styles.tabContent}
      data={discountRules}
      keyExtractor={(item) => item.id}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      renderItem={({ item }) => (
        <ModernCard style={styles.ruleCard}>
          <View style={styles.ruleHeader}>
            <View style={styles.ruleInfo}>
              <ThemedText variant="body" style={styles.ruleName}>
                {item.name}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                {item.description}
              </ThemedText>
            </View>
            <View style={styles.ruleActions}>
              <TouchableOpacity
                style={[
                  styles.statusBadge,
                  item.isActive ? styles.activeBadge : styles.inactiveBadge,
                ]}
                onPress={() => canManageDiscounts && handleToggleRule(item)}
              >
                <ThemedText variant="small" style={styles.statusText}>
                  {item.isActive ? "Active" : "Inactive"}
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.ruleDetails}>
            <View style={styles.ruleMetric}>
              <ThemedText variant="small" color="secondary">
                Type
              </ThemedText>
              <ThemedText variant="body">
                {item.discountType === "percentage"
                  ? `${item.discountValue}%`
                  : item.discountType === "fixed_amount"
                  ? formatCurrency(item.discountValue)
                  : item.discountType}
              </ThemedText>
            </View>

            <View style={styles.ruleMetric}>
              <ThemedText variant="small" color="secondary">
                Usage
              </ThemedText>
              <ThemedText variant="body">{item.usageCount}</ThemedText>
            </View>

            {item.minPurchaseAmount && (
              <View style={styles.ruleMetric}>
                <ThemedText variant="small" color="secondary">
                  Min Purchase
                </ThemedText>
                <ThemedText variant="body">
                  {formatCurrency(item.minPurchaseAmount)}
                </ThemedText>
              </View>
            )}
          </View>
        </ModernCard>
      )}
      ListEmptyComponent={
        <View style={styles.emptyState}>
          <IconSymbol name="tag" size={48} color={theme.colors.textSecondary} />
          <ThemedText variant="body" color="secondary">
            No discount rules found
          </ThemedText>
          {canManageDiscounts && (
            <ModernButton
              title="Create First Rule"
              onPress={() => setShowCreateModal(true)}
              style={styles.createButton}
            />
          )}
        </View>
      }
    />
  );

  // Render analytics overview
  const renderAnalytics = () => (
    <ScrollView
      style={styles.tabContent}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      showsVerticalScrollIndicator={false}
    >
      {analytics && (
        <>
          {/* Key Metrics */}
          <View style={styles.metricsGrid}>
            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="tag.fill"
                size={24}
                color={theme.colors.primary}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {analytics.totalRules}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Total Rules
              </ThemedText>
            </ModernCard>

            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="checkmark.circle.fill"
                size={24}
                color={theme.colors.success}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {analytics.activeRules}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Active Rules
              </ThemedText>
            </ModernCard>

            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="chart.bar.fill"
                size={24}
                color={theme.colors.primary}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {analytics.totalUsage}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Total Usage
              </ThemedText>
            </ModernCard>

            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="dollarsign.circle.fill"
                size={24}
                color={theme.colors.warning}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {formatCurrency(analytics.totalSavings)}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Total Savings
              </ThemedText>
            </ModernCard>
          </View>

          {/* Top Performing Rules */}
          <ModernCard style={styles.chartCard}>
            <ThemedText variant="h3" style={styles.cardTitle}>
              Top Performing Rules
            </ThemedText>
            {analytics.topRules.map((rule, index) => (
              <View key={rule.ruleId} style={styles.topRuleItem}>
                <View style={styles.topRuleRank}>
                  <ThemedText variant="h3" color="primary">
                    #{index + 1}
                  </ThemedText>
                </View>
                <View style={styles.topRuleInfo}>
                  <ThemedText variant="body" style={styles.topRuleName}>
                    {rule.ruleName}
                  </ThemedText>
                  <ThemedText variant="small" color="secondary">
                    {rule.usageCount} uses
                  </ThemedText>
                </View>
                <View style={styles.topRuleStats}>
                  <ThemedText variant="h3">
                    {formatCurrency(rule.totalSavings)}
                  </ThemedText>
                  <ThemedText variant="small" color="secondary">
                    saved
                  </ThemedText>
                </View>
              </View>
            ))}
          </ModernCard>
        </>
      )}
    </ScrollView>
  );

  // Render tab bar
  const renderTabBar = () => {
    const tabs = [{ key: "rules", label: "Rules", icon: "tag.fill" }];

    if (canManageDiscounts) {
      tabs.push({
        key: "permissions",
        label: "Permissions",
        icon: "person.badge.key.fill",
      });
    }

    if (canViewAnalytics) {
      tabs.push({
        key: "analytics",
        label: "Analytics",
        icon: "chart.bar.fill",
      });
    }

    return (
      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              activeTab === tab.key && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <IconSymbol
              name={tab.icon as any}
              size={20}
              color={
                activeTab === tab.key
                  ? theme.colors.primary
                  : theme.colors.textSecondary
              }
            />
            <ThemedText
              variant="small"
              color={activeTab === tab.key ? "primary" : "secondary"}
              style={styles.tabLabel}
            >
              {tab.label}
            </ThemedText>
          </TouchableOpacity>
        ))}

        {canManageDiscounts && (
          <TouchableOpacity
            style={styles.createRuleButton}
            onPress={() => setShowCreateModal(true)}
          >
            <IconSymbol name="plus" size={20} color={theme.colors.background} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <ScreenWrapper title="Discount Management" showBackButton>
        <View style={styles.centerContent}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <ThemedText
            variant="body"
            color="secondary"
            style={styles.loadingText}
          >
            Loading discount data...
          </ThemedText>
        </View>
      </ScreenWrapper>
    );
  }

  if (error) {
    return (
      <ScreenWrapper title="Discount Management" showBackButton>
        <View style={styles.centerContent}>
          <IconSymbol
            name="exclamationmark.triangle.fill"
            size={48}
            color={theme.colors.error}
          />
          <ThemedText variant="body" color="error" style={styles.errorText}>
            {error}
          </ThemedText>
          <ModernButton
            title="Retry"
            onPress={fetchData}
            style={styles.retryButton}
          />
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper title="Discount Management" showBackButton>
      <View style={styles.container}>
        {/* Tab Bar */}
        {renderTabBar()}

        {/* Tab Content */}
        {activeTab === "rules" && renderRulesList()}
        {activeTab === "permissions" && (
          <PermissionGate permissions={["manage_discounts"]}>
            <ThemedText variant="body" style={styles.comingSoon}>
              Staff Permissions Coming Soon
            </ThemedText>
          </PermissionGate>
        )}
        {activeTab === "analytics" && (
          <PermissionGate permissions={["view_analytics"]}>
            {renderAnalytics()}
          </PermissionGate>
        )}

        {/* Create Rule Modal */}
        <Modal
          visible={showCreateModal}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <ThemedView style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCreateModal(false)}
              >
                <IconSymbol name="xmark" size={24} color={theme.colors.text} />
              </TouchableOpacity>
              <ThemedText variant="h2">Create Discount Rule</ThemedText>
              <View style={styles.modalHeaderSpacer} />
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.formGroup}>
                <ThemedText variant="body" style={styles.formLabel}>
                  Rule Name
                </ThemedText>
                <TextInput
                  style={[
                    styles.formInput,
                    {
                      color: theme.colors.text,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  value={newRule.name}
                  onChangeText={(text) =>
                    setNewRule((prev) => ({ ...prev, name: text }))
                  }
                  placeholder="Enter rule name"
                  placeholderTextColor={theme.colors.textSecondary}
                />
              </View>

              <View style={styles.formGroup}>
                <ThemedText variant="body" style={styles.formLabel}>
                  Description
                </ThemedText>
                <TextInput
                  style={[
                    styles.formInput,
                    {
                      color: theme.colors.text,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  value={newRule.description}
                  onChangeText={(text) =>
                    setNewRule((prev) => ({ ...prev, description: text }))
                  }
                  placeholder="Enter description"
                  placeholderTextColor={theme.colors.textSecondary}
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.formGroup}>
                <ThemedText variant="body" style={styles.formLabel}>
                  Discount Value
                </ThemedText>
                <TextInput
                  style={[
                    styles.formInput,
                    {
                      color: theme.colors.text,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  value={newRule.discountValue}
                  onChangeText={(text) =>
                    setNewRule((prev) => ({ ...prev, discountValue: text }))
                  }
                  placeholder="Enter discount value"
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="numeric"
                />
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <ModernButton
                title="Cancel"
                onPress={() => setShowCreateModal(false)}
                variant="secondary"
                style={styles.modalButton}
              />
              <ModernButton
                title="Create Rule"
                onPress={handleCreateRule}
                style={styles.modalButton}
              />
            </View>
          </ThemedView>
        </Modal>
      </View>
    </ScreenWrapper>
  );
}

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },

    centerContent: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 32,
    },
    noAccessText: {
      textAlign: "center",
      marginTop: 16,
    },
    loadingText: {
      marginTop: 16,
    },
    errorText: {
      textAlign: "center",
      marginTop: 16,
      marginBottom: 24,
    },
    retryButton: {
      minWidth: 120,
    },
    tabBar: {
      flexDirection: "row",
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 16,
      paddingVertical: 8,
      alignItems: "center",
    },
    tabButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      paddingHorizontal: 8,
      borderRadius: 8,
      marginHorizontal: 4,
    },
    activeTabButton: {
      backgroundColor: theme.colors.surfaceSecondary,
    },
    tabLabel: {
      marginLeft: 6,
    },
    createRuleButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      width: 40,
      height: 40,
      alignItems: "center",
      justifyContent: "center",
      marginLeft: 8,
    },
    tabContent: {
      flex: 1,
      paddingHorizontal: 16,
    },
    ruleCard: {
      marginVertical: 4,
      padding: 16,
    },
    ruleHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: 12,
    },
    ruleInfo: {
      flex: 1,
    },
    ruleName: {
      fontWeight: "600",
      marginBottom: 4,
    },
    ruleActions: {
      marginLeft: 12,
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 4,
      borderRadius: 12,
    },
    activeBadge: {
      backgroundColor: theme.colors.successBackground,
    },
    inactiveBadge: {
      backgroundColor: theme.colors.warningBackground,
    },
    statusText: {
      fontWeight: "600",
    },
    ruleDetails: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    ruleMetric: {
      alignItems: "center",
    },
    emptyState: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: 64,
    },
    createButton: {
      marginTop: 16,
    },
    metricsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginVertical: 16,
      gap: 12,
    },
    metricCard: {
      flex: 1,
      minWidth: "45%",
      alignItems: "center",
      paddingVertical: 20,
    },
    metricValue: {
      marginVertical: 8,
    },
    chartCard: {
      marginVertical: 8,
      padding: 16,
    },
    cardTitle: {
      marginBottom: 16,
    },
    topRuleItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.borderLight,
    },
    topRuleRank: {
      width: 40,
      alignItems: "center",
    },
    topRuleInfo: {
      flex: 1,
      marginLeft: 16,
    },
    topRuleName: {
      fontWeight: "600",
    },
    topRuleStats: {
      alignItems: "flex-end",
    },
    comingSoon: {
      textAlign: "center",
      marginTop: 64,
      fontStyle: "italic",
    },
    modalContainer: {
      flex: 1,
    },
    modalHeader: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    modalCloseButton: {
      padding: 8,
    },
    modalHeaderSpacer: {
      width: 40,
    },
    modalContent: {
      flex: 1,
      paddingHorizontal: 16,
      paddingVertical: 16,
    },
    formGroup: {
      marginBottom: 20,
    },
    formLabel: {
      marginBottom: 8,
      fontWeight: "600",
    },
    formInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 12,
      fontSize: 16,
    },
    modalFooter: {
      flexDirection: "row",
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      gap: 12,
    },
    modalButton: {
      flex: 1,
    },
  });
