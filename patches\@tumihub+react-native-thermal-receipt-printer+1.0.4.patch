diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build.gradle b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build.gradle
index 3bd16c7..f5dba7d 100644
--- a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build.gradle
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build.gradle
@@ -1,12 +1,12 @@
 apply plugin: 'com.android.library'
 
 android {
-    compileSdkVersion = 29
-    buildToolsVersion = "29.0.2"
+    compileSdkVersion = 33
+    buildToolsVersion = "33.0.0"
 
     defaultConfig {
-        minSdkVersion 16
-        targetSdkVersion 29
+        minSdkVersion 21
+        targetSdkVersion 33
         versionCode 1
         versionName "1.0"
 
@@ -22,12 +22,12 @@ android {
 }
 
 dependencies {
-    api 'com.google.zxing:core:3.3.0'
+    api 'com.google.zxing:core:3.5.1'
     api fileTree(dir: 'libs', include: ['*.jar'])
-    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
+    androidTestImplementation('androidx.test.espresso:espresso-core:3.5.1', {
         exclude group: 'com.android.support', module: 'support-annotations'
     })
-    api 'androidx.appcompat:appcompat:1.0.0'
+    api 'androidx.appcompat:appcompat:1.6.1'
     api "com.facebook.react:react-native:+"
 
     testImplementation 'junit:junit:4.12'
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/results.bin b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/results.bin
new file mode 100644
index 0000000..7ed749e
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/results.bin
@@ -0,0 +1 @@
+o/bundleLibRuntimeToDirDebug
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/BuildConfig.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/BuildConfig.dex
new file mode 100644
index 0000000..1687e03
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/BuildConfig.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNBLEPrinterModule.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNBLEPrinterModule.dex
new file mode 100644
index 0000000..255899d
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNBLEPrinterModule.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNNetPrinterModule.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNNetPrinterModule.dex
new file mode 100644
index 0000000..6b86776
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNNetPrinterModule.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNPrinterModule.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNPrinterModule.dex
new file mode 100644
index 0000000..7e40335
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNPrinterModule.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNPrinterPackage.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNPrinterPackage.dex
new file mode 100644
index 0000000..5bdf16a
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNPrinterPackage.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNUSBPrinterModule.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNUSBPrinterModule.dex
new file mode 100644
index 0000000..c7620d8
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/RNUSBPrinterModule.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterAdapter.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterAdapter.dex
new file mode 100644
index 0000000..0a69c88
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterAdapter.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterDevice.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterDevice.dex
new file mode 100644
index 0000000..ebb34d0
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterDevice.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.dex
new file mode 100644
index 0000000..dcede8c
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEThreadWrite.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEThreadWrite.dex
new file mode 100644
index 0000000..b3c4178
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/BLEThreadWrite.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.dex
new file mode 100644
index 0000000..395586c
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterAdapter.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterAdapter.dex
new file mode 100644
index 0000000..4cad90f
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterAdapter.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterDevice.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterDevice.dex
new file mode 100644
index 0000000..b2f75fc
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterDevice.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterDeviceId.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterDeviceId.dex
new file mode 100644
index 0000000..ff1143d
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetPrinterDeviceId.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetThreadWrite.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetThreadWrite.dex
new file mode 100644
index 0000000..f799589
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/NetThreadWrite.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterAdapter.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterAdapter.dex
new file mode 100644
index 0000000..9c8a04f
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterAdapter.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterDevice.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterDevice.dex
new file mode 100644
index 0000000..1151596
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterDevice.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterDeviceId.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterDeviceId.dex
new file mode 100644
index 0000000..7ec8dab
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/PrinterDeviceId.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.dex
new file mode 100644
index 0000000..cb28340
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterAdapter.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterAdapter.dex
new file mode 100644
index 0000000..ae751ee
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterAdapter.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterDevice.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterDevice.dex
new file mode 100644
index 0000000..df88f1a
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterDevice.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterDeviceId.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterDeviceId.dex
new file mode 100644
index 0000000..9694b09
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBPrinterDeviceId.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBThreadWrite.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBThreadWrite.dex
new file mode 100644
index 0000000..9261996
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/bundleLibRuntimeToDirDebug_dex/com/pinmi/react/printer/adapter/USBThreadWrite.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin
new file mode 100644
index 0000000..ad20abb
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/********************************/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/b82662f24782782c372a1e57232ca2ba/results.bin b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/b82662f24782782c372a1e57232ca2ba/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/b82662f24782782c372a1e57232ca2ba/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/b82662f24782782c372a1e57232ca2ba/transformed/classes/classes_dex/classes.dex b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/b82662f24782782c372a1e57232ca2ba/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..c835569
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/.transforms/b82662f24782782c372a1e57232ca2ba/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/generated/source/buildConfig/debug/com/pinmi/react/printer/BuildConfig.java b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/generated/source/buildConfig/debug/com/pinmi/react/printer/BuildConfig.java
new file mode 100644
index 0000000..e037878
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/generated/source/buildConfig/debug/com/pinmi/react/printer/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.pinmi.react.printer;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.pinmi.react.printer";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..c04cb83
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,13 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.pinmi.react.printer" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+    <uses-permission android:name="android.hardware.usb.UsbAccessory" />
+    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..30215ef
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.pinmi.react.printer",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar
new file mode 100644
index 0000000..a1bd69e
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..f08e397
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..a9551e5
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
@@ -0,0 +1 @@
+int string app_name 0x0
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..752487c
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Sat Apr 19 19:45:19 EAT 2025
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..88fa3fc
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">RNPrinter</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..bef5a39
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/res"><file path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">RNPrinter</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..9017014
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..2670746
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..bec1827
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/BuildConfig.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/BuildConfig.class
new file mode 100644
index 0000000..a355c37
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/BuildConfig.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNBLEPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNBLEPrinterModule.class
new file mode 100644
index 0000000..36feae3
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNBLEPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNNetPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNNetPrinterModule.class
new file mode 100644
index 0000000..e7b94fd
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNNetPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNPrinterModule.class
new file mode 100644
index 0000000..754e507
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNPrinterPackage.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNPrinterPackage.class
new file mode 100644
index 0000000..5eb6d2a
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNPrinterPackage.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNUSBPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNUSBPrinterModule.class
new file mode 100644
index 0000000..47aa346
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/RNUSBPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class
new file mode 100644
index 0000000..e7437f7
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterDevice.class
new file mode 100644
index 0000000..5dbd4a6
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class
new file mode 100644
index 0000000..3e11793
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEThreadWrite.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEThreadWrite.class
new file mode 100644
index 0000000..91f1029
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/BLEThreadWrite.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class
new file mode 100644
index 0000000..ea79a68
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter.class
new file mode 100644
index 0000000..f108ec6
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterDevice.class
new file mode 100644
index 0000000..3add4c1
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class
new file mode 100644
index 0000000..d7bec49
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetThreadWrite.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetThreadWrite.class
new file mode 100644
index 0000000..174d48e
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/NetThreadWrite.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterAdapter.class
new file mode 100644
index 0000000..7fd9560
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterDevice.class
new file mode 100644
index 0000000..dc5e0c1
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterDeviceId.class
new file mode 100644
index 0000000..045b7b8
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/PrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class
new file mode 100644
index 0000000..230e927
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter.class
new file mode 100644
index 0000000..b7c25cb
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterDevice.class
new file mode 100644
index 0000000..f65c44b
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class
new file mode 100644
index 0000000..fcf8894
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBThreadWrite.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBThreadWrite.class
new file mode 100644
index 0000000..4959073
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/pinmi/react/printer/adapter/USBThreadWrite.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..34ca0ec
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,3 @@
+R_DEF: Internal format may change without notice
+local
+string app_name
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..7142565
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,23 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.pinmi.react.printer" >
+4
+5    <uses-sdk android:minSdkVersion="21" />
+6
+7    <uses-permission android:name="android.permission.INTERNET" />
+7-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:4:5-67
+7-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:4:22-64
+8    <uses-permission android:name="android.permission.BLUETOOTH" />
+8-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:5:5-68
+8-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:5:22-65
+9    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+9-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:6:5-74
+9-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:6:22-71
+10    <uses-permission android:name="android.hardware.usb.UsbAccessory" />
+10-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:7:5-73
+10-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:7:22-70
+11    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
+11-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:8:5-76
+11-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:8:22-73
+12
+13</manifest>
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..c04cb83
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,13 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.pinmi.react.printer" >
+
+    <uses-sdk android:minSdkVersion="21" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+    <uses-permission android:name="android.hardware.usb.UsbAccessory" />
+    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
new file mode 100644
index 0000000..88fa3fc
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">RNPrinter</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/BuildConfig.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/BuildConfig.class
new file mode 100644
index 0000000..a355c37
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/BuildConfig.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNBLEPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNBLEPrinterModule.class
new file mode 100644
index 0000000..36feae3
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNBLEPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNNetPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNNetPrinterModule.class
new file mode 100644
index 0000000..e7b94fd
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNNetPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNPrinterModule.class
new file mode 100644
index 0000000..754e507
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNPrinterPackage.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNPrinterPackage.class
new file mode 100644
index 0000000..5eb6d2a
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNPrinterPackage.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNUSBPrinterModule.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNUSBPrinterModule.class
new file mode 100644
index 0000000..47aa346
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/RNUSBPrinterModule.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class
new file mode 100644
index 0000000..e7437f7
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterDevice.class
new file mode 100644
index 0000000..5dbd4a6
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class
new file mode 100644
index 0000000..3e11793
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEThreadWrite.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEThreadWrite.class
new file mode 100644
index 0000000..91f1029
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/BLEThreadWrite.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class
new file mode 100644
index 0000000..ea79a68
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterAdapter.class
new file mode 100644
index 0000000..f108ec6
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterDevice.class
new file mode 100644
index 0000000..3add4c1
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class
new file mode 100644
index 0000000..d7bec49
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetThreadWrite.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetThreadWrite.class
new file mode 100644
index 0000000..174d48e
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/NetThreadWrite.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterAdapter.class
new file mode 100644
index 0000000..7fd9560
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterDevice.class
new file mode 100644
index 0000000..dc5e0c1
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterDeviceId.class
new file mode 100644
index 0000000..045b7b8
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/PrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class
new file mode 100644
index 0000000..230e927
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterAdapter.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterAdapter.class
new file mode 100644
index 0000000..b7c25cb
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterAdapter.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterDevice.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterDevice.class
new file mode 100644
index 0000000..f65c44b
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterDevice.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class
new file mode 100644
index 0000000..fcf8894
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBThreadWrite.class b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBThreadWrite.class
new file mode 100644
index 0000000..4959073
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/com/pinmi/react/printer/adapter/USBThreadWrite.class differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar
new file mode 100644
index 0000000..2b3eeb2
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..b08a4fe
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1,2 @@
+com.pinmi.react.printer
+string app_name
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..8f485ec
--- /dev/null
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,37 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:1:1-10:12
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:1:1-10:12
+	package
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:2:5-38
+		INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:1:11-69
+uses-permission#android.permission.INTERNET
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:4:5-67
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:4:22-64
+uses-permission#android.permission.BLUETOOTH
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:5:5-68
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:5:22-65
+uses-permission#android.permission.BLUETOOTH_ADMIN
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:6:5-74
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:6:22-71
+uses-permission#android.hardware.usb.UsbAccessory
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:7:5-73
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:7:22-70
+uses-permission#android.permission.ACCESS_WIFI_STATE
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:8:5-76
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml:8:22-73
+uses-sdk
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNUSBPrinterModule.class.uniqueId1 b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNUSBPrinterModule.class.uniqueId1
new file mode 100644
index 0000000..47aa346
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNUSBPrinterModule.class.uniqueId1 differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/USBPrinterAdapter$1.class.uniqueId2 b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/USBPrinterAdapter$1.class.uniqueId2
new file mode 100644
index 0000000..230e927
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/USBPrinterAdapter$1.class.uniqueId2 differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/USBPrinterAdapter.class.uniqueId0 b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/USBPrinterAdapter.class.uniqueId0
new file mode 100644
index 0000000..9def183
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/USBPrinterAdapter.class.uniqueId0 differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..8c85794
Binary files /dev/null and b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
index 994865b..20bee4a 100644
--- a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/AndroidManifest.xml
@@ -4,6 +4,9 @@
     <uses-permission android:name="android.permission.INTERNET" />
     <uses-permission android:name="android.permission.BLUETOOTH" />
     <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+    <!-- For Android 12+ (API level 31+) -->
+    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
+    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
     <uses-permission android:name="android.hardware.usb.UsbAccessory" />
     <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
 
diff --git a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/java/com/pinmi/react/printer/adapter/USBPrinterAdapter.java b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/java/com/pinmi/react/printer/adapter/USBPrinterAdapter.java
index fe3a5e7..07dfa9b 100644
--- a/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/java/com/pinmi/react/printer/adapter/USBPrinterAdapter.java
+++ b/node_modules/@tumihub/react-native-thermal-receipt-printer/android/src/main/java/com/pinmi/react/printer/adapter/USBPrinterAdapter.java
@@ -103,12 +103,26 @@ public class USBPrinterAdapter implements PrinterAdapter {
     public void init(ReactApplicationContext reactContext, Callback successCallback, Callback errorCallback) {
         this.mContext = reactContext;
         this.mUSBManager = (UsbManager) this.mContext.getSystemService(Context.USB_SERVICE);
-        this.mPermissionIndent = PendingIntent.getBroadcast(mContext, 0, new Intent(ACTION_USB_PERMISSION), 0);
+
+        // Use FLAG_IMMUTABLE for Android 12 (API 31) and above compatibility
+        int flags = 0;
+        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
+            flags = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S
+                ? PendingIntent.FLAG_IMMUTABLE
+                : 0;
+        }
+
+        this.mPermissionIndent = PendingIntent.getBroadcast(mContext, 0, new Intent(ACTION_USB_PERMISSION), flags);
         IntentFilter filter = new IntentFilter(ACTION_USB_PERMISSION);
         filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
         filter.addAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
         filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
-        mContext.registerReceiver(mUsbDeviceReceiver, filter);
+        // Use Context.RECEIVER_NOT_EXPORTED flag for Android 12+ compatibility
+        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
+            mContext.registerReceiver(mUsbDeviceReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
+        } else {
+            mContext.registerReceiver(mUsbDeviceReceiver, filter);
+        }
         Log.v(LOG_TAG, "RNUSBPrinter initialized");
         successCallback.invoke();
     }
