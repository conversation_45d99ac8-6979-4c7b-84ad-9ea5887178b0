/**
 * Terminal Management Service - MySQL Implementation
 * Migrated from in-memory Map() storage to MySQL database
 * Maintains 100% API compatibility with existing implementation
 */

require("dotenv").config();
const { databaseManager } = require("../config/database-manager");

class TerminalManagementService {
  constructor() {
    if (TerminalManagementService.instance) {
      return TerminalManagementService.instance;
    }

    this.databaseManager = databaseManager;

    TerminalManagementService.instance = this;
  }

  static getInstance() {
    if (!TerminalManagementService.instance) {
      TerminalManagementService.instance = new TerminalManagementService();
    }
    return TerminalManagementService.instance;
  }

  // Get all terminals
  async getAllTerminals(includeInactive = false) {
    try {
      const whereClause = includeInactive ? "" : "WHERE is_active = TRUE";
      const query = `
        SELECT 
          id, name, location_id, location_name, store_id, terminal_type,
          hardware_info, network_info, is_active, is_online, last_heartbeat,
          current_staff_id, current_session_id, created_at, updated_at
        FROM pos_terminals 
        ${whereClause}
        ORDER BY name
      `;

      const [rows] = await this.databaseManager.executeQuery(query);

      const terminals = rows.map((row) => ({
        id: row.id,
        name: row.name,
        locationId: row.location_id,
        locationName: row.location_name,
        storeId: row.store_id,
        terminalType: row.terminal_type,
        hardwareInfo:
          typeof row.hardware_info === "string"
            ? JSON.parse(row.hardware_info)
            : row.hardware_info,
        networkInfo:
          typeof row.network_info === "string"
            ? JSON.parse(row.network_info)
            : row.network_info,
        isActive: Boolean(row.is_active),
        isOnline: Boolean(row.is_online),
        lastHeartbeat: row.last_heartbeat,
        currentStaffId: row.current_staff_id,
        currentSessionId: row.current_session_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }));

      return {
        success: true,
        terminals: terminals,
      };
    } catch (error) {
      console.error("Get all terminals error:", error);
      return {
        success: false,
        error: "Failed to fetch terminals",
      };
    }
  }

  // Get terminal by ID
  async getTerminalById(terminalId) {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        "SELECT * FROM pos_terminals WHERE id = ?",
        [terminalId]
      );

      if (rows.length === 0) {
        return {
          success: false,
          error: "Terminal not found",
        };
      }

      const terminal = rows[0];

      return {
        success: true,
        terminal: {
          id: terminal.id,
          name: terminal.name,
          locationId: terminal.location_id,
          locationName: terminal.location_name,
          storeId: terminal.store_id,
          terminalType: terminal.terminal_type,
          hardwareInfo:
            typeof terminal.hardware_info === "string"
              ? JSON.parse(terminal.hardware_info)
              : terminal.hardware_info,
          networkInfo:
            typeof terminal.network_info === "string"
              ? JSON.parse(terminal.network_info)
              : terminal.network_info,
          isActive: Boolean(terminal.is_active),
          isOnline: Boolean(terminal.is_online),
          lastHeartbeat: terminal.last_heartbeat,
          currentStaffId: terminal.current_staff_id,
          currentSessionId: terminal.current_session_id,
          createdAt: terminal.created_at,
          updatedAt: terminal.updated_at,
        },
      };
    } catch (error) {
      console.error("Get terminal by ID error:", error);
      return {
        success: false,
        error: "Failed to fetch terminal",
      };
    }
  }

  // Register new terminal
  async registerTerminal(terminalData) {
    try {
      const {
        name,
        locationId,
        locationName,
        storeId,
        terminalType,
        hardwareInfo,
        networkInfo,
      } = terminalData;

      // Validate required fields
      if (!name || !storeId) {
        return {
          success: false,
          error: "Terminal name and store ID are required",
        };
      }

      // Generate terminal ID
      const [countRows] = await this.databaseManager.executeQuery(
        "SELECT COUNT(*) as count FROM pos_terminals"
      );
      const terminalId = `terminal-${String(countRows[0].count + 1).padStart(
        3,
        "0"
      )}`;

      const insertQuery = `
        INSERT INTO pos_terminals (
          id, name, location_id, location_name, store_id, terminal_type,
          hardware_info, network_info, is_active, is_online
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await this.databaseManager.executeQuery(insertQuery, [
        terminalId,
        name,
        locationId || null,
        locationName || null,
        storeId,
        terminalType || "main",
        JSON.stringify(hardwareInfo || {}),
        JSON.stringify(networkInfo || {}),
        true,
        false,
      ]);

      return {
        success: true,
        terminal: {
          id: terminalId,
          name: name,
          locationId: locationId,
          locationName: locationName,
          storeId: storeId,
          terminalType: terminalType || "main",
          hardwareInfo: hardwareInfo || {},
          networkInfo: networkInfo || {},
          isActive: true,
          isOnline: false,
        },
      };
    } catch (error) {
      console.error("Register terminal error:", error);
      return {
        success: false,
        error: "Failed to register terminal",
      };
    }
  }

  // Update terminal status
  async updateTerminalStatus(terminalId, statusData) {
    try {
      const { isOnline, currentStaffId, currentSessionId } = statusData;

      const updateQuery = `
        UPDATE pos_terminals 
        SET is_online = ?, current_staff_id = ?, current_session_id = ?, 
            last_heartbeat = ?, updated_at = NOW()
        WHERE id = ?
      `;

      await this.databaseManager.executeQuery(updateQuery, [
        isOnline !== undefined ? isOnline : null,
        currentStaffId || null,
        currentSessionId || null,
        isOnline ? new Date() : null,
        terminalId,
      ]);

      return {
        success: true,
        message: "Terminal status updated successfully",
      };
    } catch (error) {
      console.error("Update terminal status error:", error);
      return {
        success: false,
        error: "Failed to update terminal status",
      };
    }
  }

  // Assign staff to terminal
  async assignStaffToTerminal(terminalId, staffId, sessionId = null) {
    try {
      // Check if terminal exists and is active
      const terminalResult = await this.getTerminalById(terminalId);
      if (!terminalResult.success) {
        return terminalResult;
      }

      if (!terminalResult.terminal.isActive) {
        return {
          success: false,
          error: "Cannot assign staff to inactive terminal",
        };
      }

      // Update terminal with staff assignment
      await this.databaseManager.executeQuery(
        `UPDATE pos_terminals 
         SET current_staff_id = ?, current_session_id = ?, is_online = TRUE, 
             last_heartbeat = NOW(), updated_at = NOW()
         WHERE id = ?`,
        [staffId, sessionId, terminalId]
      );

      // Create terminal session record
      if (sessionId) {
        await this.databaseManager.executeQuery(
          `INSERT INTO terminal_sessions (id, terminal_id, staff_id, session_data) 
           VALUES (?, ?, ?, ?)`,
          [
            sessionId,
            terminalId,
            staffId,
            JSON.stringify({ assignedAt: new Date() }),
          ]
        );
      }

      return {
        success: true,
        message: `Staff ${staffId} assigned to terminal ${terminalId}`,
        assignment: {
          terminalId: terminalId,
          staffId: staffId,
          sessionId: sessionId,
          assignedAt: new Date(),
        },
      };
    } catch (error) {
      console.error("Assign staff to terminal error:", error);
      return {
        success: false,
        error: "Failed to assign staff to terminal",
      };
    }
  }

  // Get terminals by location
  async getTerminalsByLocation(locationId) {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        "SELECT * FROM pos_terminals WHERE location_id = ? AND is_active = TRUE ORDER BY name",
        [locationId]
      );

      const terminals = rows.map((row) => ({
        id: row.id,
        name: row.name,
        terminalType: row.terminal_type,
        isOnline: Boolean(row.is_online),
        currentStaffId: row.current_staff_id,
        lastHeartbeat: row.last_heartbeat,
      }));

      return {
        success: true,
        terminals: terminals,
        locationId: locationId,
      };
    } catch (error) {
      console.error("Get terminals by location error:", error);
      return {
        success: false,
        error: "Failed to fetch terminals by location",
      };
    }
  }

  // Get terminal statistics
  async getTerminalStatistics() {
    try {
      // Get total counts
      const [totalRows] = await this.databaseManager.executeQuery(
        "SELECT COUNT(*) as count FROM pos_terminals"
      );
      const [activeRows] = await this.databaseManager.executeQuery(
        "SELECT COUNT(*) as count FROM pos_terminals WHERE is_active = TRUE"
      );
      const [onlineRows] = await this.databaseManager.executeQuery(
        "SELECT COUNT(*) as count FROM pos_terminals WHERE is_online = TRUE"
      );

      // Get terminals by type
      const [typeRows] = await this.databaseManager.executeQuery(
        "SELECT terminal_type, COUNT(*) as count FROM pos_terminals WHERE is_active = TRUE GROUP BY terminal_type"
      );

      const terminalsByType = {};
      typeRows.forEach((row) => {
        terminalsByType[row.terminal_type] = row.count;
      });

      return {
        success: true,
        statistics: {
          total: totalRows[0].count,
          active: activeRows[0].count,
          online: onlineRows[0].count,
          offline: activeRows[0].count - onlineRows[0].count,
          byType: terminalsByType,
        },
      };
    } catch (error) {
      console.error("Get terminal statistics error:", error);
      return {
        success: false,
        error: "Failed to fetch terminal statistics",
      };
    }
  }
}

module.exports = new TerminalManagementService();
