import { useNavigation } from "@/src/contexts/NavigationContext";
import React, { useEffect } from "react";
import { GlobalLayout } from "./GlobalLayout";

interface ScreenWrapperProps {
  children: React.ReactNode;
  title?: string; // Made optional since navigation context can auto-detect
  showBackButton?: boolean;
  forceTitle?: boolean; // Force override of auto-detected title
}

export function ScreenWrapper({
  children,
  title,
  showBackButton = false,
  forceTitle = false,
}: ScreenWrapperProps) {
  const { setCurrentTitle, currentTitle } = useNavigation();

  useEffect(() => {
    // Only set title manually if provided and either forcing or no auto-detected title
    if (
      title &&
      (forceTitle || !currentTitle || currentTitle === "Dashboard")
    ) {
      setCurrentTitle(title);
    }
  }, [title, setCurrentTitle, forceTitle, currentTitle]);

  return (
    <GlobalLayout showBackButton={showBackButton}>{children}</GlobalLayout>
  );
}
