/**
 * Responsive Spacing Components for Dukalink POS
 * Provides responsive spacing utilities that scale with screen size
 */

import React from "react";
import { View, ViewProps, ViewStyle } from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import {
  getResponsiveSpacing,
  getResponsivePadding,
  getResponsiveMargin,
} from "@/src/utils/responsiveUtils";

interface ResponsiveSpacerProps {
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  horizontal?: boolean;
  vertical?: boolean;
}

export const ResponsiveSpacer: React.FC<ResponsiveSpacerProps> = ({
  size = "md",
  horizontal = false,
  vertical = true,
}) => {
  const theme = useTheme();
  const responsiveLayout = useResponsiveLayout();
  const screenSize = responsiveLayout?.screenSize || "mobile";

  const baseSpacing = theme?.spacing?.[size] || 16; // Fallback to 16 if undefined
  const responsiveSpacing = getResponsiveSpacing(
    baseSpacing,
    screenSize || "mobile"
  );

  const spacerStyle: ViewStyle = {
    width: horizontal ? responsiveSpacing : undefined,
    height: vertical ? responsiveSpacing : undefined,
  };

  return <View style={spacerStyle} />;
};

interface ResponsivePaddingProps extends ViewProps {
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  horizontal?: "xs" | "sm" | "md" | "lg" | "xl";
  vertical?: "xs" | "sm" | "md" | "lg" | "xl";
  top?: "xs" | "sm" | "md" | "lg" | "xl";
  bottom?: "xs" | "sm" | "md" | "lg" | "xl";
  left?: "xs" | "sm" | "md" | "lg" | "xl";
  right?: "xs" | "sm" | "md" | "lg" | "xl";
}

export const ResponsivePadding: React.FC<ResponsivePaddingProps> = ({
  children,
  style,
  size,
  horizontal,
  vertical,
  top,
  bottom,
  left,
  right,
  ...props
}) => {
  const theme = useTheme();
  const responsiveLayout = useResponsiveLayout();
  const screenSize = responsiveLayout?.screenSize || "mobile";

  const getPaddingStyle = (): ViewStyle => {
    const paddingStyle: ViewStyle = {};

    if (size) {
      const responsivePadding = getResponsivePadding(theme, screenSize, size);
      Object.assign(paddingStyle, responsivePadding);
    }

    if (horizontal) {
      const spacing = getResponsiveSpacing(
        theme?.spacing?.[horizontal] || 16,
        screenSize || "mobile"
      );
      paddingStyle.paddingHorizontal = spacing;
    }

    if (vertical) {
      const spacing = getResponsiveSpacing(
        theme?.spacing?.[vertical] || 16,
        screenSize || "mobile"
      );
      paddingStyle.paddingVertical = spacing;
    }

    if (top) {
      const spacing = getResponsiveSpacing(
        theme?.spacing?.[top] || 16,
        screenSize || "mobile"
      );
      paddingStyle.paddingTop = spacing;
    }

    if (bottom) {
      const spacing = getResponsiveSpacing(
        theme?.spacing?.[bottom] || 16,
        screenSize || "mobile"
      );
      paddingStyle.paddingBottom = spacing;
    }

    if (left) {
      const spacing = getResponsiveSpacing(
        theme?.spacing?.[left] || 16,
        screenSize || "mobile"
      );
      paddingStyle.paddingLeft = spacing;
    }

    if (right) {
      const spacing = getResponsiveSpacing(
        theme?.spacing?.[right] || 16,
        screenSize || "mobile"
      );
      paddingStyle.paddingRight = spacing;
    }

    return paddingStyle;
  };

  return (
    <View style={[getPaddingStyle(), style]} {...props}>
      {children}
    </View>
  );
};

interface ResponsiveMarginProps extends ViewProps {
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  horizontal?: "xs" | "sm" | "md" | "lg" | "xl";
  vertical?: "xs" | "sm" | "md" | "lg" | "xl";
  top?: "xs" | "sm" | "md" | "lg" | "xl";
  bottom?: "xs" | "sm" | "md" | "lg" | "xl";
  left?: "xs" | "sm" | "md" | "lg" | "xl";
  right?: "xs" | "sm" | "md" | "lg" | "xl";
}

export const ResponsiveMargin: React.FC<ResponsiveMarginProps> = ({
  children,
  style,
  size,
  horizontal,
  vertical,
  top,
  bottom,
  left,
  right,
  ...props
}) => {
  const theme = useTheme();
  const responsiveLayout = useResponsiveLayout();
  const screenSize = responsiveLayout?.screenSize || "mobile";

  const getMarginStyle = (): ViewStyle => {
    const marginStyle: ViewStyle = {};

    if (size) {
      const responsiveMargin = getResponsiveMargin(theme, screenSize, size);
      Object.assign(marginStyle, responsiveMargin);
    }

    if (horizontal) {
      const spacing = getResponsiveSpacing(
        theme.spacing[horizontal],
        screenSize
      );
      marginStyle.marginHorizontal = spacing;
    }

    if (vertical) {
      const spacing = getResponsiveSpacing(theme.spacing[vertical], screenSize);
      marginStyle.marginVertical = spacing;
    }

    if (top) {
      const spacing = getResponsiveSpacing(theme.spacing[top], screenSize);
      marginStyle.marginTop = spacing;
    }

    if (bottom) {
      const spacing = getResponsiveSpacing(theme.spacing[bottom], screenSize);
      marginStyle.marginBottom = spacing;
    }

    if (left) {
      const spacing = getResponsiveSpacing(theme.spacing[left], screenSize);
      marginStyle.marginLeft = spacing;
    }

    if (right) {
      const spacing = getResponsiveSpacing(theme.spacing[right], screenSize);
      marginStyle.marginRight = spacing;
    }

    return marginStyle;
  };

  return (
    <View style={[getMarginStyle(), style]} {...props}>
      {children}
    </View>
  );
};

// Convenience components for common spacing patterns
export const ResponsiveSection: React.FC<ViewProps> = ({
  children,
  style,
  ...props
}) => (
  <ResponsivePadding size="lg" style={style} {...props}>
    {children}
  </ResponsivePadding>
);

export const ResponsiveCard: React.FC<ViewProps> = ({
  children,
  style,
  ...props
}) => {
  const theme = useTheme();

  return (
    <ResponsivePadding
      size="md"
      style={[
        {
          backgroundColor: theme?.colors?.surface || "#ffffff",
          borderRadius: theme?.borderRadius?.lg || 8,
          ...(theme?.shadows?.md || {}),
        },
        style,
      ]}
      {...props}
    >
      {children}
    </ResponsivePadding>
  );
};

export const ResponsiveStack: React.FC<
  ViewProps & { spacing?: "xs" | "sm" | "md" | "lg" | "xl" }
> = ({ children, spacing = "md", style, ...props }) => {
  const theme = useTheme();
  const responsiveLayout = useResponsiveLayout();
  const screenSize = responsiveLayout?.screenSize || "mobile";

  const gap = getResponsiveSpacing(
    theme?.spacing?.[spacing] || 16,
    screenSize || "mobile"
  );

  return (
    <View style={[{ gap }, style]} {...props}>
      {children}
    </View>
  );
};
