/**
 * Sales Agent Service - MySQL Implementation
 * Migrated from in-memory Map() storage to MySQL database
 * Maintains 100% API compatibility with existing implementation
 */

const { databaseManager } = require("../config/database-manager");

class SalesAgentService {
  constructor() {
    if (SalesAgentService.instance) {
      return SalesAgentService.instance;
    }

    this.databaseManager = databaseManager;

    SalesAgentService.instance = this;
  }

  static getInstance() {
    if (!SalesAgentService.instance) {
      SalesAgentService.instance = new SalesAgentService();
    }
    return SalesAgentService.instance;
  }

  // Get all active sales agents
  async getAllSalesAgents(includeInactive = false) {
    try {
      const whereClause = includeInactive ? "" : "WHERE active = TRUE";
      const query = `
        SELECT id, name, email, phone, commission_rate, active, territory, region,
               join_date, total_sales, total_commission, customer_count
        FROM sales_agents 
        ${whereClause}
        ORDER BY name
      `;

      const [rows] = await this.databaseManager.executeQuery(query);

      const agents = rows.map((row) => ({
        id: row.id,
        name: row.name,
        email: row.email,
        phone: row.phone,
        commissionRate: parseFloat(row.commission_rate),
        active: Boolean(row.active),
        territory: row.territory,
        region: row.region,
        joinDate: row.join_date,
        totalSales: parseFloat(row.total_sales),
        totalCommission: parseFloat(row.total_commission),
        customerCount: row.customer_count,
      }));

      return {
        success: true,
        salesAgents: agents,
      };
    } catch (error) {
      console.error("Get all sales agents error:", error);
      return {
        success: false,
        error: "Failed to fetch sales agents",
      };
    }
  }

  // Get sales agent by ID
  async getSalesAgentById(agentId) {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        "SELECT * FROM sales_agents WHERE id = ?",
        [agentId]
      );

      if (rows.length === 0) {
        return {
          success: false,
          error: "Sales agent not found",
        };
      }

      const agent = rows[0];

      // Get customer list
      const [customerRows] = await this.databaseManager.executeQuery(
        "SELECT customer_id FROM agent_customers WHERE agent_id = ?",
        [agentId]
      );

      const customers = customerRows.map((row) => row.customer_id);

      return {
        success: true,
        salesAgent: {
          id: agent.id,
          name: agent.name,
          email: agent.email,
          phone: agent.phone,
          commissionRate: parseFloat(agent.commission_rate),
          active: Boolean(agent.active),
          territory: agent.territory,
          region: agent.region,
          joinDate: agent.join_date,
          totalSales: parseFloat(agent.total_sales),
          totalCommission: parseFloat(agent.total_commission),
          customerCount: agent.customer_count,
          customers: customers,
        },
      };
    } catch (error) {
      console.error("Get sales agent by ID error:", error);
      return {
        success: false,
        error: "Failed to fetch sales agent",
      };
    }
  }

  // Create new sales agent
  async createSalesAgent(agentData) {
    try {
      // Validate required fields
      const { name, email, phone, commissionRate, territory, region } =
        agentData;

      if (
        !name ||
        !email ||
        !phone ||
        commissionRate === undefined ||
        !territory ||
        !region
      ) {
        return {
          success: false,
          error:
            "Missing required fields: name, email, phone, commissionRate, territory, region",
        };
      }

      // Validate commission rate
      if (
        typeof commissionRate !== "number" ||
        commissionRate < 0 ||
        commissionRate > 100
      ) {
        return {
          success: false,
          error: "Commission rate must be a number between 0 and 100",
        };
      }

      // Check for duplicate email
      const [existingRows] = await this.databaseManager.executeQuery(
        "SELECT id FROM sales_agents WHERE email = ?",
        [email.toLowerCase().trim()]
      );

      if (existingRows.length > 0) {
        return {
          success: false,
          error: "Sales agent with this email already exists",
        };
      }

      // Generate new agent ID
      const [countRows] = await this.databaseManager.executeQuery(
        "SELECT COUNT(*) as count FROM sales_agents"
      );
      const agentId = `agent-${String(countRows[0].count + 1).padStart(
        3,
        "0"
      )}`;

      const insertQuery = `
        INSERT INTO sales_agents (
          id, name, email, phone, commission_rate, active, territory, region, join_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await this.databaseManager.executeQuery(insertQuery, [
        agentId,
        name.trim(),
        email.toLowerCase().trim(),
        phone.trim(),
        parseFloat(commissionRate),
        agentData.active !== undefined ? agentData.active : true,
        territory.trim(),
        region.trim(),
        new Date(),
      ]);

      return {
        success: true,
        salesAgent: {
          id: agentId,
          name: name.trim(),
          email: email.toLowerCase().trim(),
          phone: phone.trim(),
          commissionRate: parseFloat(commissionRate),
          active: agentData.active !== undefined ? agentData.active : true,
          territory: territory.trim(),
          region: region.trim(),
          joinDate: new Date(),
        },
      };
    } catch (error) {
      console.error("Create sales agent error:", error);
      return {
        success: false,
        error: "Failed to create sales agent",
      };
    }
  }

  // Update sales agent
  async updateSalesAgent(agentId, updateData) {
    try {
      // Check if agent exists
      const [existingRows] = await this.databaseManager.executeQuery(
        "SELECT id FROM sales_agents WHERE id = ?",
        [agentId]
      );

      if (existingRows.length === 0) {
        return {
          success: false,
          error: "Sales agent not found",
        };
      }

      // Validate commission rate if provided
      if (updateData.commissionRate !== undefined) {
        if (
          typeof updateData.commissionRate !== "number" ||
          updateData.commissionRate < 0 ||
          updateData.commissionRate > 100
        ) {
          return {
            success: false,
            error: "Commission rate must be a number between 0 and 100",
          };
        }
      }

      // Check for duplicate email if email is being updated
      if (updateData.email) {
        const [duplicateRows] = await this.databaseManager.executeQuery(
          "SELECT id FROM sales_agents WHERE email = ? AND id != ?",
          [updateData.email.toLowerCase().trim(), agentId]
        );

        if (duplicateRows.length > 0) {
          return {
            success: false,
            error: "Sales agent with this email already exists",
          };
        }
      }

      // Build update query dynamically
      const allowedFields = {
        name: "name",
        email: "email",
        phone: "phone",
        commissionRate: "commission_rate",
        active: "active",
        territory: "territory",
        region: "region",
      };

      const updateFields = [];
      const updateValues = [];

      Object.keys(allowedFields).forEach((field) => {
        if (updateData[field] !== undefined) {
          updateFields.push(`${allowedFields[field]} = ?`);
          let value = updateData[field];

          if (field === "email") {
            value = value.toLowerCase().trim();
          } else if (typeof value === "string") {
            value = value.trim();
          }

          updateValues.push(value);
        }
      });

      if (updateFields.length === 0) {
        return {
          success: false,
          error: "No valid fields to update",
        };
      }

      updateFields.push("updated_at = ?");
      updateValues.push(new Date());
      updateValues.push(agentId);

      const updateQuery = `
        UPDATE sales_agents 
        SET ${updateFields.join(", ")} 
        WHERE id = ?
      `;

      await this.databaseManager.executeQuery(updateQuery, updateValues);

      // Return updated agent data
      return await this.getSalesAgentById(agentId);
    } catch (error) {
      console.error("Update sales agent error:", error);
      return {
        success: false,
        error: "Failed to update sales agent",
      };
    }
  }

  // Link customer to sales agent (customer acquisition tracking)
  async linkCustomerToAgent(agentId, customerId) {
    try {
      // Check if agent exists and is active
      const [agentRows] = await this.databaseManager.executeQuery(
        "SELECT name, active FROM sales_agents WHERE id = ?",
        [agentId]
      );

      if (agentRows.length === 0) {
        return {
          success: false,
          error: "Sales agent not found",
        };
      }

      if (!agentRows[0].active) {
        return {
          success: false,
          error: "Cannot link customer to inactive sales agent",
        };
      }

      // Insert customer relationship (ignore if already exists)
      await this.databaseManager.executeQuery(
        "INSERT IGNORE INTO agent_customers (agent_id, customer_id) VALUES (?, ?)",
        [agentId, customerId]
      );

      // Get updated customer count (trigger will update this automatically)
      const [countRows] = await this.databaseManager.executeQuery(
        "SELECT customer_count FROM sales_agents WHERE id = ?",
        [agentId]
      );

      return {
        success: true,
        message: `Customer ${customerId} linked to sales agent ${agentRows[0].name}`,
        customerCount: countRows[0].customer_count,
      };
    } catch (error) {
      console.error("Link customer to agent error:", error);
      return {
        success: false,
        error: "Failed to link customer to sales agent",
      };
    }
  }

  // Get customers brought by a sales agent
  async getAgentCustomers(agentId) {
    try {
      const [agentRows] = await this.databaseManager.executeQuery(
        "SELECT id, name, territory, region FROM sales_agents WHERE id = ?",
        [agentId]
      );

      if (agentRows.length === 0) {
        return {
          success: false,
          error: "Sales agent not found",
        };
      }

      const [customerRows] = await this.databaseManager.executeQuery(
        "SELECT customer_id FROM agent_customers WHERE agent_id = ?",
        [agentId]
      );

      const customers = customerRows.map((row) => row.customer_id);
      const agent = agentRows[0];

      return {
        success: true,
        agentInfo: {
          id: agent.id,
          name: agent.name,
          territory: agent.territory,
          region: agent.region,
        },
        customers: customers,
        customerCount: customers.length,
      };
    } catch (error) {
      console.error("Get agent customers error:", error);
      return {
        success: false,
        error: "Failed to fetch agent customers",
      };
    }
  }

  // Find sales agent by customer ID
  async findAgentByCustomer(customerId) {
    try {
      const query = `
        SELECT sa.id, sa.name, sa.email, sa.commission_rate, sa.territory, sa.region
        FROM sales_agents sa
        JOIN agent_customers ac ON sa.id = ac.agent_id
        WHERE ac.customer_id = ?
        LIMIT 1
      `;

      const [rows] = await this.databaseManager.executeQuery(query, [
        customerId,
      ]);

      if (rows.length === 0) {
        return {
          success: false,
          error: "No sales agent found for this customer",
        };
      }

      const agent = rows[0];
      return {
        success: true,
        salesAgent: {
          id: agent.id,
          name: agent.name,
          email: agent.email,
          commissionRate: parseFloat(agent.commission_rate),
          territory: agent.territory,
          region: agent.region,
        },
      };
    } catch (error) {
      console.error("Find agent by customer error:", error);
      return {
        success: false,
        error: "Failed to find sales agent for customer",
      };
    }
  }
}

module.exports = new SalesAgentService();
