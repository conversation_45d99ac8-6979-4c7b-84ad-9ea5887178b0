/**
 * Debug SQL Query Issue
 */

require("dotenv").config();
const mysql = require("mysql2/promise");

async function debugSQLQuery() {
  let connection;
  
  try {
    console.log("🔗 Connecting to MySQL database...");
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "dukalink",
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || "dukalink_shopify_pos",
      charset: "utf8mb4",
    });

    console.log("✅ Connected to MySQL database");

    // Test different variations of the query
    const staffId = "pos-003";
    const status = "active";
    const limit = 50;
    const offset = 0;

    console.log("🔧 Testing simple query first...");
    try {
      const simpleQuery = "SELECT COUNT(*) as count FROM pos_tickets WHERE staff_id = ?";
      const [simpleResult] = await connection.execute(simpleQuery, [staffId]);
      console.log(`✅ Simple query works - found ${simpleResult[0].count} tickets for staff ${staffId}`);
    } catch (error) {
      console.error("❌ Simple query failed:", error.message);
    }

    console.log("🔧 Testing query with status filter...");
    try {
      const statusQuery = "SELECT COUNT(*) as count FROM pos_tickets WHERE staff_id = ? AND status = ?";
      const [statusResult] = await connection.execute(statusQuery, [staffId, status]);
      console.log(`✅ Status query works - found ${statusResult[0].count} tickets`);
    } catch (error) {
      console.error("❌ Status query failed:", error.message);
    }

    console.log("🔧 Testing query with LIMIT and OFFSET...");
    try {
      const limitQuery = "SELECT id, name FROM pos_tickets WHERE staff_id = ? AND status = ? LIMIT ? OFFSET ?";
      const [limitResult] = await connection.execute(limitQuery, [staffId, status, limit, offset]);
      console.log(`✅ Limit query works - found ${limitResult.length} tickets`);
    } catch (error) {
      console.error("❌ Limit query failed:", error.message);
    }

    console.log("🔧 Testing the full problematic query...");
    try {
      const fullQuery = `
        SELECT 
          t.id,
          t.name,
          t.status,
          s.name as staff_name,
          COUNT(ti.id) as item_count
        FROM pos_tickets t
        LEFT JOIN pos_staff s ON t.staff_id = s.id
        LEFT JOIN ticket_items ti ON t.id = ti.ticket_id
        WHERE t.staff_id = ? AND t.status = ? AND (t.expires_at IS NULL OR t.expires_at > NOW())
        GROUP BY t.id
        ORDER BY t.updated_at DESC
        LIMIT ? OFFSET ?
      `;

      console.log("Parameters:", [staffId, status, limit, offset]);
      console.log("Parameter types:", [typeof staffId, typeof status, typeof limit, typeof offset]);
      
      const [fullResult] = await connection.execute(fullQuery, [staffId, status, limit, offset]);
      console.log(`✅ Full query works - found ${fullResult.length} tickets`);
      if (fullResult.length > 0) {
        console.log("Sample result:", fullResult[0]);
      }
    } catch (error) {
      console.error("❌ Full query failed:", error.message);
      console.error("Error code:", error.code);
      console.error("SQL State:", error.sqlState);
    }

    console.log("🔧 Testing without sales_agents join...");
    try {
      const noSalesAgentQuery = `
        SELECT 
          t.id,
          t.name,
          t.status,
          s.name as staff_name,
          COUNT(ti.id) as item_count
        FROM pos_tickets t
        LEFT JOIN pos_staff s ON t.staff_id = s.id
        LEFT JOIN ticket_items ti ON t.id = ti.ticket_id
        WHERE t.staff_id = ? AND t.status = ? AND (t.expires_at IS NULL OR t.expires_at > NOW())
        GROUP BY t.id
        ORDER BY t.updated_at DESC
        LIMIT ? OFFSET ?
      `;

      const [noSalesResult] = await connection.execute(noSalesAgentQuery, [staffId, status, limit, offset]);
      console.log(`✅ Query without sales_agents works - found ${noSalesResult.length} tickets`);
    } catch (error) {
      console.error("❌ Query without sales_agents failed:", error.message);
    }

  } catch (error) {
    console.error("❌ Debug failed:", error);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

debugSQLQuery();
