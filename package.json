{"name": "dukalink-shopify", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "postinstall": "patch-package", "api:status": "node scripts/switch-api-env.js status", "api:local": "node scripts/switch-api-env.js local", "api:dev": "node scripts/switch-api-env.js development", "api:prod": "node scripts/switch-api-env.js production", "analyze:bundle": "EXPO_ATLAS=true npx expo export --platform android", "analyze:size": "npx expo export --source-maps --platform android --no-bytecode", "optimize:apk": "node scripts/optimize-apk.js"}, "dependencies": {"@expo-google-fonts/noto-serif-jp": "^0.4.1", "@expo-google-fonts/poppins": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.80.7", "@tumihub/react-native-thermal-receipt-printer": "^1.0.4", "axios": "^1.9.0", "expo": "~53.0.10", "expo-auth-session": "^6.2.0", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-crypto": "^14.1.5", "expo-dev-client": "^5.2.0", "expo-device": "~7.1.4", "expo-file-system": "^18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.0", "expo-linking": "~7.1.5", "expo-print": "^14.1.4", "expo-router": "~5.0.7", "expo-secure-store": "^14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.9", "expo-sqlite": "^15.2.11", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.57.0", "react-native": "0.79.3", "react-native-base64": "^0.2.1", "react-native-bluetooth-escpos-printer": "^0.0.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.0.11", "react-native-svg": "^15.12.0", "react-native-view-shot": "^4.0.3", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "patch-package": "^8.0.0", "typescript": "~5.8.3"}, "expo": {"autolinking": {"exclude": ["expo-blur", "expo-sharing", "expo-system-ui"]}}, "private": true}