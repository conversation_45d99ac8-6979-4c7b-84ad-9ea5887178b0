/**
 * Authentication utilities for the Dukalink POS system
 * Handles token storage, retrieval, and validation
 */

import { CrossPlatformStorage } from './storage';

// Storage keys
const AUTH_TOKEN_KEY = 'session_token';
const USER_DATA_KEY = 'user_data';
const REFRESH_TOKEN_KEY = 'refresh_token';

export interface UserData {
  id: string;
  username: string;
  name: string;
  role: string;
  permissions: string[];
  locationId?: string;
  terminalId?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number;
}

/**
 * Get the current authentication token
 */
export const getAuthToken = async (): Promise<string | null> => {
  try {
    const token = await CrossPlatformStorage.getItemAsync(AUTH_TOKEN_KEY);
    return token;
  } catch (error) {
    console.warn('Failed to get auth token:', error);
    return null;
  }
};

/**
 * Store authentication token
 */
export const setAuthToken = async (token: string): Promise<void> => {
  try {
    await CrossPlatformStorage.setItemAsync(AUTH_TOKEN_KEY, token);
  } catch (error) {
    console.error('Failed to store auth token:', error);
    throw error;
  }
};

/**
 * Get stored user data
 */
export const getUserData = async (): Promise<UserData | null> => {
  try {
    const userData = await CrossPlatformStorage.getItemAsync(USER_DATA_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.warn('Failed to get user data:', error);
    return null;
  }
};

/**
 * Store user data
 */
export const setUserData = async (userData: UserData): Promise<void> => {
  try {
    await CrossPlatformStorage.setItemAsync(USER_DATA_KEY, JSON.stringify(userData));
  } catch (error) {
    console.error('Failed to store user data:', error);
    throw error;
  }
};

/**
 * Get refresh token
 */
export const getRefreshToken = async (): Promise<string | null> => {
  try {
    const token = await CrossPlatformStorage.getItemAsync(REFRESH_TOKEN_KEY);
    return token;
  } catch (error) {
    console.warn('Failed to get refresh token:', error);
    return null;
  }
};

/**
 * Store refresh token
 */
export const setRefreshToken = async (token: string): Promise<void> => {
  try {
    await CrossPlatformStorage.setItemAsync(REFRESH_TOKEN_KEY, token);
  } catch (error) {
    console.error('Failed to store refresh token:', error);
    throw error;
  }
};

/**
 * Store authentication tokens and user data
 */
export const storeAuthData = async (
  tokens: AuthTokens,
  userData: UserData
): Promise<void> => {
  try {
    await setAuthToken(tokens.accessToken);
    await setUserData(userData);
    
    if (tokens.refreshToken) {
      await setRefreshToken(tokens.refreshToken);
    }
  } catch (error) {
    console.error('Failed to store auth data:', error);
    throw error;
  }
};

/**
 * Clear all authentication data
 */
export const clearAuthData = async (): Promise<void> => {
  try {
    await CrossPlatformStorage.deleteItemAsync(AUTH_TOKEN_KEY);
    await CrossPlatformStorage.deleteItemAsync(USER_DATA_KEY);
    await CrossPlatformStorage.deleteItemAsync(REFRESH_TOKEN_KEY);
  } catch (error) {
    console.error('Failed to clear auth data:', error);
    throw error;
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const token = await getAuthToken();
    const userData = await getUserData();
    return !!(token && userData);
  } catch (error) {
    console.warn('Failed to check authentication status:', error);
    return false;
  }
};

/**
 * Get current user's role
 */
export const getCurrentUserRole = async (): Promise<string | null> => {
  try {
    const userData = await getUserData();
    return userData?.role || null;
  } catch (error) {
    console.warn('Failed to get user role:', error);
    return null;
  }
};

/**
 * Check if current user has specific permission
 */
export const hasPermission = async (permission: string): Promise<boolean> => {
  try {
    const userData = await getUserData();
    return userData?.permissions?.includes(permission) || false;
  } catch (error) {
    console.warn('Failed to check permission:', error);
    return false;
  }
};

/**
 * Get current user's location and terminal info
 */
export const getCurrentUserContext = async (): Promise<{
  locationId?: string;
  terminalId?: string;
  userId: string;
} | null> => {
  try {
    const userData = await getUserData();
    if (!userData) return null;
    
    return {
      locationId: userData.locationId,
      terminalId: userData.terminalId,
      userId: userData.id,
    };
  } catch (error) {
    console.warn('Failed to get user context:', error);
    return null;
  }
};

/**
 * Validate token format (basic JWT structure check)
 */
export const isValidTokenFormat = (token: string): boolean => {
  if (!token || typeof token !== 'string') return false;
  
  // Basic JWT format check (3 parts separated by dots)
  const parts = token.split('.');
  return parts.length === 3;
};

/**
 * Check if token is expired (requires token to have exp claim)
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    if (!isValidTokenFormat(token)) return true;
    
    // Decode JWT payload (basic decode, no verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const exp = payload.exp;
    
    if (!exp) return false; // No expiration claim
    
    const now = Math.floor(Date.now() / 1000);
    return exp < now;
  } catch (error) {
    console.warn('Failed to check token expiration:', error);
    return true; // Assume expired if we can't parse
  }
};

/**
 * Get token expiration time
 */
export const getTokenExpiration = (token: string): Date | null => {
  try {
    if (!isValidTokenFormat(token)) return null;
    
    const payload = JSON.parse(atob(token.split('.')[1]));
    const exp = payload.exp;
    
    return exp ? new Date(exp * 1000) : null;
  } catch (error) {
    console.warn('Failed to get token expiration:', error);
    return null;
  }
};
