/**
 * Enhanced Payment Service
 *
 * Integrates with the new backend payment processing APIs
 * Supports single payments, split payments, and real-time status tracking
 */

import { API_BASE_URL } from "@/src/config/api";
import { getAuthToken } from "@/src/utils/auth";

export interface PaymentTransactionRequest {
  orderId?: string;
  shopifyOrderId?: string;
  customerId?: string;
  terminalId?: string;
  locationId?: string;
  totalAmount: number;
  currency?: string;
  paymentMethods?: PaymentMethodRequest[];
  notes?: string;
  metadata?: any;
}

export interface PaymentMethodRequest {
  methodType: "cash" | "mpesa" | "absa_till" | "card" | "credit";
  methodName?: string;
  amount: number;
  amountTendered?: number;
  referenceCode?: string;
  transactionCode?: string;
  metadata?: any;
}

export interface PaymentProcessingRequest {
  paymentMethod?: "stk_push" | "manual_code" | "stk_status_check";
  phoneNumber?: string;
  transactionCode?: string;
  checkoutRequestId?: string;
  accountReference?: string;
  transactionDesc?: string;
  confirmed?: boolean;
  cardType?: string;
  lastFourDigits?: string;
  authorizationCode?: string;
  customerName?: string;
  customerPhone?: string;
  creditLimit?: number;
  dueDate?: string;
  notes?: string;
}

export interface PaymentTransactionResponse {
  success: boolean;
  data?: {
    transactionId: string;
    status: string;
    totalAmount: number;
    isSplitPayment: boolean;
    remainingAmount: number;
    paymentMethods: number;
  };
  error?: string;
}

export interface PaymentMethodResponse {
  success: boolean;
  data?: {
    methodId: string;
    status: string;
    metadata?: any;
  };
  error?: string;
}

export interface PaymentStatusResponse {
  success: boolean;
  data?: {
    transactionId: string;
    status: string;
    totalAmount: number;
    completedAmount: number;
    remainingAmount: number;
    isSplitPayment: boolean;
    summary: {
      totalMethods: number;
      completed: number;
      failed: number;
      pending: number;
      processing: number;
    };
    paymentMethods: {
      id: string;
      type: string;
      name: string;
      amount: number;
      status: string;
      processedAt?: string;
      errorMessage?: string;
    }[];
  };
  error?: string;
}

export interface SplitPaymentSummaryResponse {
  success: boolean;
  data?: {
    transaction: {
      id: string;
      totalAmount: number;
      status: string;
      isSplitPayment: boolean;
      createdAt: string;
      completedAt?: string;
    };
    balance: {
      totalAmount: number;
      completedAmount: number;
      pendingAmount: number;
      remainingAmount: number;
      isFullyPaid: boolean;
      isPartiallyPaid: boolean;
    };
    paymentMethods: {
      total: number;
      byStatus: {
        completed: number;
        pending: number;
        processing: number;
        failed: number;
        cancelled: number;
      };
      details: {
        id: string;
        type: string;
        name: string;
        amount: number;
        status: string;
        processedAt?: string;
        metadata: any;
      }[];
    };
  };
  error?: string;
}

class EnhancedPaymentService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/payments`;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = await getAuthToken();

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Initiate a new payment transaction
   */
  async initiateTransaction(
    request: PaymentTransactionRequest
  ): Promise<PaymentTransactionResponse> {
    try {
      const response = await this.makeRequest<PaymentTransactionResponse>(
        "/initiate",
        {
          method: "POST",
          body: JSON.stringify(request),
        }
      );

      return response;
    } catch (error) {
      console.error("Error initiating payment transaction:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to initiate transaction",
      };
    }
  }

  /**
   * Add a payment method to an existing transaction
   */
  async addPaymentMethod(
    transactionId: string,
    method: PaymentMethodRequest
  ): Promise<PaymentMethodResponse> {
    try {
      const response = await this.makeRequest<PaymentMethodResponse>(
        `/${transactionId}/methods`,
        {
          method: "POST",
          body: JSON.stringify(method),
        }
      );

      return response;
    } catch (error) {
      console.error("Error adding payment method:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to add payment method",
      };
    }
  }

  /**
   * Process a specific payment method
   */
  async processPaymentMethod(
    methodId: string,
    processingData: PaymentProcessingRequest
  ): Promise<PaymentMethodResponse> {
    try {
      const response = await this.makeRequest<PaymentMethodResponse>(
        `/methods/${methodId}/process`,
        {
          method: "POST",
          body: JSON.stringify(processingData),
        }
      );

      return response;
    } catch (error) {
      console.error("Error processing payment method:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to process payment method",
      };
    }
  }

  /**
   * Get transaction status and details
   */
  async getTransactionStatus(
    transactionId: string
  ): Promise<PaymentStatusResponse> {
    try {
      const response = await this.makeRequest<PaymentStatusResponse>(
        `/${transactionId}/status`
      );

      return response;
    } catch (error) {
      console.error("Error getting transaction status:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get transaction status",
      };
    }
  }

  /**
   * Get split payment summary
   */
  async getSplitPaymentSummary(
    transactionId: string
  ): Promise<SplitPaymentSummaryResponse> {
    try {
      const response = await this.makeRequest<SplitPaymentSummaryResponse>(
        `/${transactionId}/split-summary`
      );

      return response;
    } catch (error) {
      console.error("Error getting split payment summary:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get split payment summary",
      };
    }
  }

  /**
   * Validate split payment amounts
   */
  async validateSplitPayment(
    transactionId: string,
    paymentMethods: PaymentMethodRequest[]
  ): Promise<{ success: boolean; isValid: boolean; error?: string }> {
    try {
      const response = await this.makeRequest<{
        success: boolean;
        data: { isValid: boolean; totalAmount: number; methodCount: number };
        error?: string;
      }>(`/${transactionId}/validate-split`, {
        method: "POST",
        body: JSON.stringify({ paymentMethods }),
      });

      return {
        success: response.success,
        isValid: response.data?.isValid || false,
        error: response.error,
      };
    } catch (error) {
      console.error("Error validating split payment:", error);
      return {
        success: false,
        isValid: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to validate split payment",
      };
    }
  }

  /**
   * Get remaining balance for transaction
   */
  async getRemainingBalance(transactionId: string): Promise<{
    success: boolean;
    totalAmount?: number;
    completedAmount?: number;
    pendingAmount?: number;
    remainingAmount?: number;
    isFullyPaid?: boolean;
    isPartiallyPaid?: boolean;
    error?: string;
  }> {
    try {
      const response = await this.makeRequest<{
        success: boolean;
        data: {
          totalAmount: number;
          completedAmount: number;
          pendingAmount: number;
          remainingAmount: number;
          isFullyPaid: boolean;
          isPartiallyPaid: boolean;
        };
        error?: string;
      }>(`/${transactionId}/balance`);

      return {
        success: response.success,
        ...response.data,
        error: response.error,
      };
    } catch (error) {
      console.error("Error getting remaining balance:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get remaining balance",
      };
    }
  }

  /**
   * Cancel a payment transaction
   */
  async cancelTransaction(
    transactionId: string,
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this.makeRequest<{
        success: boolean;
        data: { status: string };
        error?: string;
      }>(`/${transactionId}/cancel`, {
        method: "POST",
        body: JSON.stringify({ reason }),
      });

      return {
        success: response.success,
        error: response.error,
      };
    } catch (error) {
      console.error("Error cancelling transaction:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to cancel transaction",
      };
    }
  }

  /**
   * Complete transaction with Shopify order creation
   */
  async completeTransactionWithShopify(
    transactionId: string,
    orderData: any
  ): Promise<{
    success: boolean;
    shopifyOrderId?: string;
    orderNumber?: string;
    paymentsCreated?: number;
    financialStatus?: string;
    loyaltyResult?: {
      success: boolean;
      pointsAdded: number;
      newBalance: number;
      tierChanged: boolean;
      newTier?: string;
      transactionId: string;
    };
    error?: string;
  }> {
    try {
      const response = await this.makeRequest<{
        success: boolean;
        data: {
          shopifyOrderId: string;
          orderNumber: string;
          paymentsCreated: number;
          financialStatus: string;
          paymentDetails: any[];
          loyaltyResult?: {
            success: boolean;
            pointsAdded: number;
            newBalance: number;
            tierChanged: boolean;
            newTier?: string;
            transactionId: string;
          };
          message?: string;
        };
        error?: string;
      }>(`/${transactionId}/complete-with-shopify`, {
        method: "POST",
        body: JSON.stringify({ orderData }),
      });

      return {
        success: response.success,
        shopifyOrderId: response.data?.shopifyOrderId,
        orderNumber: response.data?.orderNumber,
        paymentsCreated: response.data?.paymentsCreated,
        financialStatus: response.data?.financialStatus,
        loyaltyResult: response.data?.loyaltyResult,
        error: response.error,
      };
    } catch (error) {
      console.error("Error completing transaction with Shopify:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to complete transaction with Shopify",
      };
    }
  }
}

export const enhancedPaymentService = new EnhancedPaymentService();
export default enhancedPaymentService;
