# Browser Print Testing Guide for T220MD Thermal Printer

## 🎯 **Testing Browser Print Dialog with T220MD**

This guide helps you test the **browser print dialog** with your **T220MD thermal printer** to ensure proper **80mm formatting**, **centering**, and **styling**.

---

## 🔧 **Setup Requirements**

### **1. T220MD Printer Setup:**
- ✅ **T220MD connected** to computer via USB
- ✅ **Printer drivers installed** (Windows/Mac/Linux)
- ✅ **Printer set as default** or available in print dialog
- ✅ **80mm thermal paper** loaded

### **2. Browser Requirements:**
- ✅ **Chrome, Firefox, Safari, or Edge**
- ✅ **JavaScript enabled**
- ✅ **Pop-up blocker disabled** for print dialogs

---

## 🧪 **Testing Steps**

### **Step 1: Access Print Function**
1. **Navigate to Orders page** in Dukalink POS
2. **Click on any order** to view details
3. **Click "Print Options"** button
4. **Browser print dialog** should open automatically

### **Step 2: Configure Print Dialog**
1. **Select your T220MD printer** from the dropdown
2. **Set paper size** to "Custom" or "80mm" if available
3. **Set margins** to "Minimum" or "None"
4. **Orientation**: Portrait
5. **Scale**: 100% (no scaling)

### **Step 3: Preview Check**
Before printing, verify in the **print preview**:
- ✅ **Content is centered** on the page
- ✅ **Text is readable** and properly sized
- ✅ **Store name is bold** and centered
- ✅ **Items are properly aligned**
- ✅ **Total section is bold** with border
- ✅ **Footer is centered**

### **Step 4: Print Test**
1. **Click "Print"** in the dialog
2. **Receipt should print** with proper formatting
3. **Check the physical receipt** for:
   - ✅ **Proper centering** on 80mm paper
   - ✅ **No content cut off** on sides
   - ✅ **Bold text** appears bold
   - ✅ **Lines/borders** are visible
   - ✅ **Receipt feeds out completely**

---

## 🎯 **Expected Results**

### **✅ Correct Formatting:**
```
        TREASURED SCENTS
      Ngong Road, Nairobi, KE
         +254722924706

    ═══════════════════════════

         SALES RECEIPT
    
    Receipt: 105
    6/14/2025 01:21 PM
    Customer: Walk-in Customer
    
    ───────────────────────────
    
    18K Gold Plated Necklace
    1 x KSh 3500.00    KSh 3500.00
    
    001 Abc Test
    1 x KSh 2250.00    KSh 2250.00
    
    ═══════════════════════════
    
    TOTAL              KSh 5750.00
    
    Payment: Cash
    
    ───────────────────────────
    
      Thank you for your business!
       Powered by Dukalink POS
    
    [Receipt feeds out completely]
```

### **❌ Issues to Look For:**
- **Left-aligned content** instead of centered
- **Content cut off** on right side
- **Poor font rendering** or wrong font
- **Missing borders** or lines
- **Receipt stops mid-print** (poor paper feed)
- **Text too small** or too large

---

## 🔧 **Troubleshooting**

### **Issue: Content Left-Aligned**
**Solution:**
- Ensure **T220MD is selected** as printer
- Check **paper size** is set to 80mm or custom
- Verify **margins are minimal**

### **Issue: Content Cut Off**
**Solution:**
- Set **scale to 100%** (no scaling)
- Choose **"Fit to page"** if available
- Check **paper width** in printer settings

### **Issue: Poor Font Rendering**
**Solution:**
- Ensure **monospace font** is supported
- Check **printer driver** is up to date
- Try **different browser** (Chrome recommended)

### **Issue: Missing Borders/Lines**
**Solution:**
- Enable **"Print backgrounds"** in browser
- Check **"Print graphics"** option
- Ensure **color printing** is enabled

### **Issue: Receipt Doesn't Feed Out**
**Solution:**
- Check **paper feed** settings in printer
- Verify **paper is loaded** correctly
- Ensure **printer has enough paper**

---

## 🎯 **Browser-Specific Settings**

### **Chrome:**
1. **Print dialog** → **More settings**
2. **Paper size**: Custom (80mm width)
3. **Margins**: Minimum
4. **Options**: ✅ Background graphics

### **Firefox:**
1. **Print dialog** → **Properties**
2. **Paper size**: Custom
3. **Margins**: 0mm all sides
4. **Options**: ✅ Print backgrounds

### **Safari:**
1. **Print dialog** → **Show Details**
2. **Paper size**: Custom
3. **Scale**: 100%
4. **Options**: ✅ Print backgrounds

### **Edge:**
1. **Print dialog** → **More settings**
2. **Paper size**: Custom
3. **Margins**: Minimum
4. **Options**: ✅ Background graphics

---

## 🚀 **Optimization Tips**

### **For Best Results:**
1. **Set T220MD as default printer**
2. **Create custom paper size** (80mm x continuous)
3. **Save print preferences** for future use
4. **Test with different browsers** to find best compatibility
5. **Keep printer drivers updated**

### **Performance Tips:**
1. **Close unnecessary browser tabs** before printing
2. **Ensure stable USB connection** to T220MD
3. **Use high-quality thermal paper**
4. **Regular printer maintenance**

---

## 📞 **Support**

If you continue to experience issues:

1. **Check printer status** in system settings
2. **Restart printer** and reconnect USB
3. **Try different USB port**
4. **Update browser** to latest version
5. **Contact support** with specific error details

---

## ✅ **Success Checklist**

- [ ] T220MD printer connected and recognized
- [ ] Browser print dialog opens correctly
- [ ] Print preview shows centered content
- [ ] Physical receipt prints with proper formatting
- [ ] Content is not cut off on sides
- [ ] Receipt feeds out completely
- [ ] Bold text appears bold
- [ ] Lines and borders are visible

**When all items are checked, your browser print setup is working perfectly!** 🎉
