/**
 * Standardized API Response Formatter
 * Provides consistent response format across all backend endpoints
 */

class ResponseFormatter {
  /**
   * Success response with data
   * @param {Object} res - Express response object
   * @param {*} data - Response data
   * @param {string} message - Optional success message
   * @param {number} statusCode - HTTP status code (default: 200)
   * @param {Object} meta - Optional metadata (pagination, etc.)
   */
  static success(res, data = null, message = null, statusCode = 200, meta = null) {
    const response = {
      success: true,
      data,
    };

    if (message) {
      response.message = message;
    }

    if (meta) {
      response.meta = meta;
    }

    return res.status(statusCode).json(response);
  }

  /**
   * Error response
   * @param {Object} res - Express response object
   * @param {string|Object} error - Error message or error object
   * @param {number} statusCode - HTTP status code (default: 400)
   * @param {Object} details - Optional error details
   */
  static error(res, error, statusCode = 400, details = null) {
    const response = {
      success: false,
      error: typeof error === 'string' ? error : error.message || 'An error occurred',
    };

    if (details) {
      response.details = details;
    }

    // Log error for debugging
    console.error(`API Error [${statusCode}]:`, error);

    return res.status(statusCode).json(response);
  }

  /**
   * Validation error response
   * @param {Object} res - Express response object
   * @param {Array|Object} validationErrors - Validation error details
   * @param {string} message - Optional error message
   */
  static validationError(res, validationErrors, message = 'Validation failed') {
    return this.error(res, message, 422, {
      validation: validationErrors,
    });
  }

  /**
   * Not found response
   * @param {Object} res - Express response object
   * @param {string} resource - Resource that was not found
   */
  static notFound(res, resource = 'Resource') {
    return this.error(res, `${resource} not found`, 404);
  }

  /**
   * Unauthorized response
   * @param {Object} res - Express response object
   * @param {string} message - Optional error message
   */
  static unauthorized(res, message = 'Unauthorized access') {
    return this.error(res, message, 401);
  }

  /**
   * Forbidden response
   * @param {Object} res - Express response object
   * @param {string} message - Optional error message
   */
  static forbidden(res, message = 'Access forbidden') {
    return this.error(res, message, 403);
  }

  /**
   * Internal server error response
   * @param {Object} res - Express response object
   * @param {string|Error} error - Error message or error object
   */
  static serverError(res, error = 'Internal server error') {
    return this.error(res, error, 500);
  }

  /**
   * Paginated response
   * @param {Object} res - Express response object
   * @param {Array} items - Array of items
   * @param {Object} pagination - Pagination info
   * @param {string} message - Optional success message
   */
  static paginated(res, items, pagination, message = null) {
    const meta = {
      pagination: {
        total: pagination.total || items.length,
        page: pagination.page || 1,
        limit: pagination.limit || items.length,
        hasNext: pagination.hasNext || false,
        hasPrev: pagination.hasPrev || false,
        ...pagination,
      },
    };

    return this.success(res, items, message, 200, meta);
  }

  /**
   * Created response (for POST requests)
   * @param {Object} res - Express response object
   * @param {*} data - Created resource data
   * @param {string} message - Optional success message
   */
  static created(res, data, message = 'Resource created successfully') {
    return this.success(res, data, message, 201);
  }

  /**
   * Updated response (for PUT/PATCH requests)
   * @param {Object} res - Express response object
   * @param {*} data - Updated resource data
   * @param {string} message - Optional success message
   */
  static updated(res, data, message = 'Resource updated successfully') {
    return this.success(res, data, message, 200);
  }

  /**
   * Deleted response (for DELETE requests)
   * @param {Object} res - Express response object
   * @param {string} message - Optional success message
   */
  static deleted(res, message = 'Resource deleted successfully') {
    return this.success(res, null, message, 200);
  }

  /**
   * No content response
   * @param {Object} res - Express response object
   */
  static noContent(res) {
    return res.status(204).send();
  }

  /**
   * Handle Shopify service responses
   * @param {Object} res - Express response object
   * @param {Object} result - Shopify service result
   * @param {string} successMessage - Optional success message
   * @param {string} errorMessage - Optional error message
   */
  static handleShopifyResult(res, result, successMessage = null, errorMessage = 'Operation failed') {
    if (result.success) {
      const data = result.data || result.products || result.customers || result.orders || result;
      const meta = result.pagination ? { pagination: result.pagination } : null;
      return this.success(res, data, successMessage, 200, meta);
    } else {
      return this.error(res, result.error || errorMessage, 400);
    }
  }
}

module.exports = ResponseFormatter;
