/**
 * Advanced Conflict Resolution Service
 * 
 * Provides sophisticated conflict resolution strategies for concurrent ticket edits,
 * including field-level merging, user preference learning, and intelligent
 * conflict prevention mechanisms.
 */

import { Ticket } from '@/src/store/slices/ticketSlice';
import { store } from '@/src/store';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ConflictField {
  field: string;
  localValue: any;
  serverValue: any;
  lastModified: {
    local: string;
    server: string;
  };
  confidence: number; // 0-1, how confident we are in auto-resolution
  userPreference?: 'local' | 'server' | 'merge';
}

export interface DetailedConflict {
  ticketId: string;
  conflictType: 'field-level' | 'structural' | 'concurrent-edit' | 'version-mismatch';
  severity: 'low' | 'medium' | 'high' | 'critical';
  fields: ConflictField[];
  localTicket: Ticket;
  serverTicket: Ticket;
  resolutionStrategy: 'auto' | 'user-guided' | 'manual';
  metadata: {
    detectedAt: string;
    localLastModified: string;
    serverLastModified: string;
    editDistance: number;
    userContext?: string;
  };
}

export interface ResolutionResult {
  success: boolean;
  resolvedTicket?: Ticket;
  remainingConflicts: ConflictField[];
  appliedStrategy: string;
  confidence: number;
  userActionRequired: boolean;
}

export interface ConflictResolutionConfig {
  autoResolveThreshold: number; // 0-1, confidence threshold for auto-resolution
  preferLocalChanges: boolean;
  enableUserLearning: boolean;
  maxConflictAge: number; // in minutes
  fieldPriorities: Record<string, number>; // field importance weights
}

class AdvancedConflictResolver {
  private config: ConflictResolutionConfig;
  private userPreferences: Map<string, 'local' | 'server' | 'merge'> = new Map();
  private resolutionHistory: Array<{
    conflictType: string;
    resolution: string;
    timestamp: Date;
    success: boolean;
  }> = [];

  constructor(config: Partial<ConflictResolutionConfig> = {}) {
    this.config = {
      autoResolveThreshold: 0.8,
      preferLocalChanges: true,
      enableUserLearning: true,
      maxConflictAge: 30, // 30 minutes
      fieldPriorities: {
        items: 1.0,
        total: 0.9,
        customer: 0.8,
        salesperson: 0.8,
        note: 0.6,
        discounts: 0.7,
        name: 0.5,
        status: 0.9,
      },
      ...config,
    };

    this.loadUserPreferences();
  }

  /**
   * Analyze conflicts with detailed field-level detection
   */
  async analyzeConflicts(localTicket: Ticket, serverTicket: Ticket): Promise<DetailedConflict> {
    const fields = this.detectFieldConflicts(localTicket, serverTicket);
    const severity = this.calculateConflictSeverity(fields);
    const conflictType = this.determineConflictType(fields, localTicket, serverTicket);
    const editDistance = this.calculateEditDistance(localTicket, serverTicket);

    const conflict: DetailedConflict = {
      ticketId: localTicket.id,
      conflictType,
      severity,
      fields,
      localTicket,
      serverTicket,
      resolutionStrategy: this.determineResolutionStrategy(fields, severity),
      metadata: {
        detectedAt: new Date().toISOString(),
        localLastModified: localTicket.updatedAt,
        serverLastModified: serverTicket.updatedAt,
        editDistance,
        userContext: await this.getUserContext(),
      },
    };

    return conflict;
  }

  /**
   * Resolve conflict using advanced strategies
   */
  async resolveConflict(conflict: DetailedConflict): Promise<ResolutionResult> {
    try {
      switch (conflict.resolutionStrategy) {
        case 'auto':
          return await this.autoResolveConflict(conflict);
        case 'user-guided':
          return await this.userGuidedResolution(conflict);
        case 'manual':
          return this.prepareManualResolution(conflict);
        default:
          throw new Error(`Unknown resolution strategy: ${conflict.resolutionStrategy}`);
      }
    } catch (error) {
      console.error('Conflict resolution failed:', error);
      return {
        success: false,
        remainingConflicts: conflict.fields,
        appliedStrategy: 'failed',
        confidence: 0,
        userActionRequired: true,
      };
    }
  }

  /**
   * Automatic conflict resolution using AI-like strategies
   */
  private async autoResolveConflict(conflict: DetailedConflict): Promise<ResolutionResult> {
    const resolvedFields: Record<string, any> = {};
    const remainingConflicts: ConflictField[] = [];
    let totalConfidence = 0;

    for (const field of conflict.fields) {
      const resolution = await this.resolveField(field, conflict);
      
      if (resolution.confidence >= this.config.autoResolveThreshold) {
        resolvedFields[field.field] = resolution.value;
        totalConfidence += resolution.confidence * this.config.fieldPriorities[field.field];
      } else {
        remainingConflicts.push(field);
      }
    }

    if (remainingConflicts.length === 0) {
      // Full auto-resolution successful
      const resolvedTicket = this.mergeResolvedFields(
        conflict.localTicket,
        conflict.serverTicket,
        resolvedFields
      );

      this.recordResolution(conflict.conflictType, 'auto-resolved', true);

      return {
        success: true,
        resolvedTicket,
        remainingConflicts: [],
        appliedStrategy: 'auto-resolution',
        confidence: totalConfidence / conflict.fields.length,
        userActionRequired: false,
      };
    }

    // Partial resolution
    return {
      success: false,
      remainingConflicts,
      appliedStrategy: 'partial-auto-resolution',
      confidence: totalConfidence / conflict.fields.length,
      userActionRequired: true,
    };
  }

  /**
   * User-guided resolution with smart suggestions
   */
  private async userGuidedResolution(conflict: DetailedConflict): Promise<ResolutionResult> {
    // This would typically show a UI for user input
    // For now, we'll use learned preferences and heuristics
    
    const suggestions = this.generateResolutionSuggestions(conflict);
    const autoApplicable = suggestions.filter(s => s.confidence > 0.9);

    if (autoApplicable.length === conflict.fields.length) {
      // All suggestions are high-confidence, apply automatically
      const resolvedFields: Record<string, any> = {};
      
      autoApplicable.forEach(suggestion => {
        resolvedFields[suggestion.field] = suggestion.suggestedValue;
      });

      const resolvedTicket = this.mergeResolvedFields(
        conflict.localTicket,
        conflict.serverTicket,
        resolvedFields
      );

      return {
        success: true,
        resolvedTicket,
        remainingConflicts: [],
        appliedStrategy: 'user-guided-auto',
        confidence: 0.9,
        userActionRequired: false,
      };
    }

    // Require user interaction
    return {
      success: false,
      remainingConflicts: conflict.fields,
      appliedStrategy: 'user-guided-manual',
      confidence: 0.5,
      userActionRequired: true,
    };
  }

  /**
   * Prepare manual resolution data
   */
  private prepareManualResolution(conflict: DetailedConflict): ResolutionResult {
    return {
      success: false,
      remainingConflicts: conflict.fields,
      appliedStrategy: 'manual-required',
      confidence: 0,
      userActionRequired: true,
    };
  }

  /**
   * Detect field-level conflicts
   */
  private detectFieldConflicts(localTicket: Ticket, serverTicket: Ticket): ConflictField[] {
    const conflicts: ConflictField[] = [];
    const fieldsToCheck = ['name', 'items', 'customer', 'salesperson', 'note', 'discounts', 'total', 'status'];

    fieldsToCheck.forEach(field => {
      const localValue = (localTicket as any)[field];
      const serverValue = (serverTicket as any)[field];

      if (JSON.stringify(localValue) !== JSON.stringify(serverValue)) {
        const confidence = this.calculateFieldConfidence(field, localValue, serverValue, localTicket, serverTicket);
        
        conflicts.push({
          field,
          localValue,
          serverValue,
          lastModified: {
            local: localTicket.updatedAt,
            server: serverTicket.updatedAt,
          },
          confidence,
          userPreference: this.userPreferences.get(`${field}_preference`),
        });
      }
    });

    return conflicts;
  }

  /**
   * Calculate field resolution confidence
   */
  private calculateFieldConfidence(
    field: string,
    localValue: any,
    serverValue: any,
    localTicket: Ticket,
    serverTicket: Ticket
  ): number {
    let confidence = 0.5; // Base confidence

    // Time-based confidence (prefer newer changes)
    const localTime = new Date(localTicket.updatedAt).getTime();
    const serverTime = new Date(serverTicket.updatedAt).getTime();
    const timeDiff = Math.abs(localTime - serverTime);
    
    if (timeDiff > 60000) { // More than 1 minute apart
      confidence += localTime > serverTime ? 0.3 : -0.3;
    }

    // Field-specific logic
    switch (field) {
      case 'items':
        // Prefer the version with more items (likely more complete)
        if (Array.isArray(localValue) && Array.isArray(serverValue)) {
          confidence += localValue.length > serverValue.length ? 0.2 : -0.2;
        }
        break;
      
      case 'total':
        // Prefer non-zero totals
        if (typeof localValue === 'number' && typeof serverValue === 'number') {
          if (localValue > 0 && serverValue === 0) confidence += 0.3;
          if (serverValue > 0 && localValue === 0) confidence -= 0.3;
        }
        break;
      
      case 'customer':
      case 'salesperson':
        // Prefer non-null values
        if (localValue && !serverValue) confidence += 0.2;
        if (serverValue && !localValue) confidence -= 0.2;
        break;
    }

    // User preference learning
    const userPref = this.userPreferences.get(`${field}_preference`);
    if (userPref === 'local') confidence += 0.2;
    if (userPref === 'server') confidence -= 0.2;

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Calculate conflict severity
   */
  private calculateConflictSeverity(fields: ConflictField[]): 'low' | 'medium' | 'high' | 'critical' {
    const criticalFields = ['items', 'total', 'status'];
    const highPriorityFields = ['customer', 'salesperson', 'discounts'];

    const hasCritical = fields.some(f => criticalFields.includes(f.field));
    const hasHighPriority = fields.some(f => highPriorityFields.includes(f.field));
    const fieldCount = fields.length;

    if (hasCritical || fieldCount > 5) return 'critical';
    if (hasHighPriority || fieldCount > 3) return 'high';
    if (fieldCount > 1) return 'medium';
    return 'low';
  }

  /**
   * Determine conflict type
   */
  private determineConflictType(
    fields: ConflictField[],
    localTicket: Ticket,
    serverTicket: Ticket
  ): DetailedConflict['conflictType'] {
    const hasStructuralChanges = fields.some(f => ['items', 'discounts'].includes(f.field));
    const hasVersionMismatch = Math.abs(
      new Date(localTicket.updatedAt).getTime() - new Date(serverTicket.updatedAt).getTime()
    ) > 300000; // 5 minutes

    if (hasVersionMismatch) return 'version-mismatch';
    if (hasStructuralChanges) return 'structural';
    if (fields.length > 3) return 'concurrent-edit';
    return 'field-level';
  }

  /**
   * Determine resolution strategy
   */
  private determineResolutionStrategy(
    fields: ConflictField[],
    severity: DetailedConflict['severity']
  ): DetailedConflict['resolutionStrategy'] {
    const avgConfidence = fields.reduce((sum, f) => sum + f.confidence, 0) / fields.length;

    if (severity === 'critical') return 'manual';
    if (avgConfidence >= this.config.autoResolveThreshold) return 'auto';
    if (severity === 'low' && avgConfidence >= 0.6) return 'user-guided';
    return 'manual';
  }

  /**
   * Calculate edit distance between tickets
   */
  private calculateEditDistance(localTicket: Ticket, serverTicket: Ticket): number {
    // Simplified edit distance calculation
    const localStr = JSON.stringify(localTicket);
    const serverStr = JSON.stringify(serverTicket);
    
    // Levenshtein distance approximation
    const maxLen = Math.max(localStr.length, serverStr.length);
    const minLen = Math.min(localStr.length, serverStr.length);
    
    return (maxLen - minLen) / maxLen;
  }

  /**
   * Get user context for better resolution
   */
  private async getUserContext(): Promise<string> {
    try {
      const state = store.getState();
      const user = state.auth.user;
      return user?.role || 'unknown';
    } catch {
      return 'unknown';
    }
  }

  /**
   * Resolve individual field
   */
  private async resolveField(
    field: ConflictField,
    conflict: DetailedConflict
  ): Promise<{ value: any; confidence: number }> {
    // Use field confidence and user preferences
    let confidence = field.confidence;
    let value = field.localValue;

    // Apply user preference if available
    if (field.userPreference === 'server') {
      value = field.serverValue;
      confidence = Math.max(confidence, 0.8);
    } else if (field.userPreference === 'merge' && this.canMergeField(field)) {
      value = this.mergeFieldValues(field);
      confidence = Math.max(confidence, 0.7);
    }

    return { value, confidence };
  }

  /**
   * Check if field can be merged
   */
  private canMergeField(field: ConflictField): boolean {
    const mergableFields = ['note', 'items', 'discounts'];
    return mergableFields.includes(field.field);
  }

  /**
   * Merge field values intelligently
   */
  private mergeFieldValues(field: ConflictField): any {
    switch (field.field) {
      case 'note':
        // Concatenate notes
        const localNote = field.localValue || '';
        const serverNote = field.serverValue || '';
        return localNote && serverNote ? `${localNote}\n${serverNote}` : localNote || serverNote;
      
      case 'items':
        // Merge item arrays (prefer local quantities, server for new items)
        return this.mergeItemArrays(field.localValue, field.serverValue);
      
      case 'discounts':
        // Merge discount arrays
        return this.mergeDiscountArrays(field.localValue, field.serverValue);
      
      default:
        return field.localValue; // Default to local
    }
  }

  /**
   * Merge item arrays intelligently
   */
  private mergeItemArrays(localItems: any[], serverItems: any[]): any[] {
    const merged = [...localItems];
    
    serverItems.forEach(serverItem => {
      const existingIndex = merged.findIndex(item => item.variantId === serverItem.variantId);
      
      if (existingIndex === -1) {
        // New item from server, add it
        merged.push(serverItem);
      }
      // Keep local version for existing items (prefer local changes)
    });

    return merged;
  }

  /**
   * Merge discount arrays
   */
  private mergeDiscountArrays(localDiscounts: any[], serverDiscounts: any[]): any[] {
    // Simple merge - combine unique discounts
    const merged = [...localDiscounts];
    
    serverDiscounts.forEach(serverDiscount => {
      const exists = merged.some(discount => 
        discount.type === serverDiscount.type && discount.value === serverDiscount.value
      );
      
      if (!exists) {
        merged.push(serverDiscount);
      }
    });

    return merged;
  }

  /**
   * Merge resolved fields into final ticket
   */
  private mergeResolvedFields(
    localTicket: Ticket,
    serverTicket: Ticket,
    resolvedFields: Record<string, any>
  ): Ticket {
    return {
      ...localTicket,
      ...serverTicket,
      ...resolvedFields,
      updatedAt: new Date().toISOString(),
      isDirty: true,
    };
  }

  /**
   * Generate resolution suggestions
   */
  private generateResolutionSuggestions(conflict: DetailedConflict): Array<{
    field: string;
    suggestedValue: any;
    confidence: number;
    reason: string;
  }> {
    return conflict.fields.map(field => {
      const suggestion = this.getSuggestionForField(field, conflict);
      return {
        field: field.field,
        suggestedValue: suggestion.value,
        confidence: suggestion.confidence,
        reason: suggestion.reason,
      };
    });
  }

  /**
   * Get suggestion for specific field
   */
  private getSuggestionForField(field: ConflictField, conflict: DetailedConflict): {
    value: any;
    confidence: number;
    reason: string;
  } {
    // Time-based suggestion
    const localTime = new Date(field.lastModified.local).getTime();
    const serverTime = new Date(field.lastModified.server).getTime();
    
    if (localTime > serverTime + 60000) { // Local is significantly newer
      return {
        value: field.localValue,
        confidence: 0.9,
        reason: 'Local version is more recent',
      };
    }
    
    if (serverTime > localTime + 60000) { // Server is significantly newer
      return {
        value: field.serverValue,
        confidence: 0.9,
        reason: 'Server version is more recent',
      };
    }

    // Content-based suggestion
    if (field.field === 'items' && Array.isArray(field.localValue) && Array.isArray(field.serverValue)) {
      if (field.localValue.length > field.serverValue.length) {
        return {
          value: field.localValue,
          confidence: 0.8,
          reason: 'Local version has more items',
        };
      }
    }

    // Default to user preference or local
    return {
      value: field.userPreference === 'server' ? field.serverValue : field.localValue,
      confidence: 0.6,
      reason: field.userPreference ? 'Based on user preference' : 'Default to local changes',
    };
  }

  /**
   * Record resolution for learning
   */
  private recordResolution(conflictType: string, resolution: string, success: boolean): void {
    this.resolutionHistory.push({
      conflictType,
      resolution,
      timestamp: new Date(),
      success,
    });

    // Keep only recent history
    if (this.resolutionHistory.length > 100) {
      this.resolutionHistory = this.resolutionHistory.slice(-50);
    }
  }

  /**
   * Load user preferences from storage
   */
  private async loadUserPreferences(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('conflict_resolution_preferences');
      if (stored) {
        const preferences = JSON.parse(stored);
        this.userPreferences = new Map(Object.entries(preferences));
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    }
  }

  /**
   * Save user preferences to storage
   */
  async saveUserPreferences(): Promise<void> {
    try {
      const preferences = Object.fromEntries(this.userPreferences);
      await AsyncStorage.setItem('conflict_resolution_preferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  }

  /**
   * Learn from user resolution choice
   */
  async learnFromUserChoice(field: string, choice: 'local' | 'server' | 'merge'): Promise<void> {
    if (this.config.enableUserLearning) {
      this.userPreferences.set(`${field}_preference`, choice);
      await this.saveUserPreferences();
    }
  }

  /**
   * Get resolution statistics
   */
  getResolutionStats(): {
    totalResolutions: number;
    successRate: number;
    autoResolutionRate: number;
    commonConflictTypes: Array<{ type: string; count: number }>;
  } {
    const total = this.resolutionHistory.length;
    const successful = this.resolutionHistory.filter(r => r.success).length;
    const autoResolved = this.resolutionHistory.filter(r => r.resolution.includes('auto')).length;

    const conflictTypeCounts = this.resolutionHistory.reduce((acc, r) => {
      acc[r.conflictType] = (acc[r.conflictType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const commonTypes = Object.entries(conflictTypeCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalResolutions: total,
      successRate: total > 0 ? successful / total : 0,
      autoResolutionRate: total > 0 ? autoResolved / total : 0,
      commonConflictTypes: commonTypes,
    };
  }
}

// Export singleton instance
export const advancedConflictResolver = new AdvancedConflictResolver();
export default AdvancedConflictResolver;
