// Test the shop service integration
const axios = require("axios");

// Mock the shop service functionality
class TestShopService {
  static async getShopInfo() {
    try {
      // Simulate the API call that the mobile app would make
      const response = await axios.get(
        "http://***********:3020/api/store/info"
      );

      if (!response.data.success) {
        throw new Error(
          response.data.error || "Failed to fetch shop information"
        );
      }

      const store = response.data.data.store;

      // Transform the response to match our ShopInfo interface
      const shopInfo = {
        name: store.name || "Dukalink Store",
        email: store.email || "",
        phone: store.phone || undefined,
        address: store.address
          ? {
              address1: store.address.address1 || undefined,
              address2: store.address.address2 || undefined,
              city: store.address.city || "Nairobi",
              province: store.address.province || undefined,
              country: store.address.country || "Kenya",
              zip: store.address.zip || undefined,
              formatted: [
                store.address.address1,
                store.address.city || "Nairobi",
                store.address.country || "Kenya",
              ].filter(Boolean),
            }
          : {
              city: "Nairobi",
              country: "Kenya",
              formatted: ["Nairobi, Kenya"],
            },
        domain: store.domain || "",
        currencyCode: store.currency || "KES",
      };

      return shopInfo;
    } catch (error) {
      console.error("Error fetching shop info:", error.message);

      // Return fallback shop info if API fails
      return {
        name: "Dukalink Store",
        email: "<EMAIL>",
        phone: "+254 700 000 000",
        address: {
          address1: "123 Business Street",
          city: "Nairobi",
          country: "Kenya",
          formatted: ["123 Business Street", "Nairobi, Kenya"],
        },
        domain: "dukalink.myshopify.com",
        currencyCode: "KES",
      };
    }
  }

  static async getFormattedStoreAddress() {
    const shopInfo = await this.getShopInfo();

    if (shopInfo.address?.formatted && shopInfo.address.formatted.length > 0) {
      return shopInfo.address.formatted.join(", ");
    }

    return "Nairobi, Kenya";
  }

  static async getStoreContactInfo() {
    const shopInfo = await this.getShopInfo();

    return {
      name: shopInfo.name,
      phone: shopInfo.phone,
      email: shopInfo.email,
    };
  }
}

async function testShopService() {
  console.log("🧪 Testing Shop Service Integration...\n");

  try {
    // Test 1: Get shop info
    console.log("1️⃣ Testing shop info retrieval...");
    const shopInfo = await TestShopService.getShopInfo();

    console.log("✅ Shop info retrieved successfully:");
    console.log(`   Store Name: ${shopInfo.name}`);
    console.log(`   Email: ${shopInfo.email}`);
    console.log(`   Phone: ${shopInfo.phone || "Not provided"}`);
    console.log(`   Domain: ${shopInfo.domain}`);
    console.log(`   Currency: ${shopInfo.currencyCode}`);

    if (shopInfo.address) {
      console.log("   Address:");
      console.log(
        `     Street: ${shopInfo.address.address1 || "Not provided"}`
      );
      console.log(`     City: ${shopInfo.address.city}`);
      console.log(`     Country: ${shopInfo.address.country}`);
      console.log(`     Formatted: ${shopInfo.address.formatted?.join(", ")}`);
    }

    // Test 2: Get formatted address
    console.log("\n2️⃣ Testing formatted address...");
    const formattedAddress = await TestShopService.getFormattedStoreAddress();
    console.log(`✅ Formatted address: ${formattedAddress}`);

    // Test 3: Get contact info
    console.log("\n3️⃣ Testing contact info...");
    const contactInfo = await TestShopService.getStoreContactInfo();
    console.log("✅ Contact info retrieved:");
    console.log(`   Name: ${contactInfo.name}`);
    console.log(`   Phone: ${contactInfo.phone || "Not provided"}`);
    console.log(`   Email: ${contactInfo.email}`);

    // Test 4: Simulate receipt generation with real store data
    console.log("\n4️⃣ Testing receipt generation with real store data...");

    const mockOrder = {
      id: "test-order-001",
      orderNumber: "ORD-001",
      customer: {
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
      },
      lineItems: [{ title: "Test Product", quantity: 1, price: "25.00" }],
      totalPrice: "25.00",
      createdAt: new Date().toISOString(),
    };

    // Simulate what the receipt generator would do
    const receiptData = {
      orderNumber: mockOrder.orderNumber,
      orderDate: new Date(mockOrder.createdAt).toLocaleDateString(),
      customer: {
        name: `${mockOrder.customer.firstName} ${mockOrder.customer.lastName}`,
        email: mockOrder.customer.email,
      },
      items: mockOrder.lineItems.map((item) => ({
        name: item.title,
        quantity: item.quantity,
        price: parseFloat(item.price),
        total: item.quantity * parseFloat(item.price),
      })),
      subtotal: parseFloat(mockOrder.totalPrice),
      tax: 0,
      total: parseFloat(mockOrder.totalPrice),
      paymentMethod: "Cash",
      store: {
        name: shopInfo.name,
        address: formattedAddress,
        phone: shopInfo.phone,
      },
    };

    console.log("✅ Receipt data generated with real store info:");
    console.log(`   Store: ${receiptData.store.name}`);
    console.log(`   Address: ${receiptData.store.address}`);
    console.log(`   Phone: ${receiptData.store.phone || "Not provided"}`);
    console.log(`   Order: ${receiptData.orderNumber}`);
    console.log(
      `   Total: ${shopInfo.currencyCode} ${receiptData.total.toFixed(2)}`
    );

    console.log(
      "\n🎉 All tests passed! Shop service is working correctly with real Shopify data."
    );
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("Response:", error.response.data);
    }
  }
}

// Run the test
testShopService();
