/**
 * Test script to check if PIN status is being returned correctly
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function testPinStatus() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4'
    });
    
    console.log('🔍 Testing PIN status for all users...\n');
    
    // Test the exact query used in auth service
    const [users] = await connection.execute(`
      SELECT
        s.id,
        s.username,
        s.name,
        s.email,
        s.role,
        s.store_id,
        s.commission_rate,
        s.is_active,
        s.last_login,
        s.failed_login_attempts,
        s.locked_until,
        CASE WHEN s.pin IS NOT NULL THEN 1 ELSE 0 END as has_pin,
        GROUP_CONCAT(sp.permission) as permissions
      FROM pos_staff s
      LEFT JOIN staff_permissions sp ON s.id = sp.staff_id
      WHERE s.is_active = TRUE
      GROUP BY s.id
      ORDER BY s.role DESC
    `);
    
    console.log('📊 User PIN Status:');
    console.log('='.repeat(80));
    
    users.forEach(user => {
      console.log(`👤 ${user.name} (${user.username})`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Has PIN: ${user.has_pin ? '✅ YES' : '❌ NO'}`);
      console.log(`   Permissions: ${user.permissions || 'None'}`);
      console.log('');
    });
    
    // Test specific user lookup
    console.log('🔍 Testing specific user lookup...\n');
    
    const testUsername = 'admin'; // Change this to test different users
    const [specificUser] = await connection.execute(`
      SELECT
        s.id,
        s.username,
        s.name,
        s.role,
        CASE WHEN s.pin IS NOT NULL THEN 1 ELSE 0 END as has_pin,
        s.pin IS NOT NULL as pin_check
      FROM pos_staff s
      WHERE s.username = ? AND s.is_active = TRUE
    `, [testUsername]);
    
    if (specificUser.length > 0) {
      const user = specificUser[0];
      console.log(`🎯 Testing user: ${user.name} (${user.username})`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Has PIN (CASE): ${user.has_pin}`);
      console.log(`   Has PIN (IS NOT NULL): ${user.pin_check}`);
      console.log(`   Should show PIN modal: ${user.has_pin ? 'YES' : 'NO'}`);
    } else {
      console.log(`❌ User '${testUsername}' not found`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testPinStatus().then(() => {
  console.log('\n✅ PIN status test completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
