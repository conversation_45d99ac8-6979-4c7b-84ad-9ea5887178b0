{"name": "dukalink-pos-backend", "version": "1.0.0", "description": "Backend API server for Dukalink POS with Shopify integration", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "setup": "node migrations/setup_database.js"}, "dependencies": {"@shopify/shopify-api": "^8.1.1", "axios": "^1.6.2", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "node-cron": "^4.1.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["pos", "shopify", "api", "dukalink"], "author": "Dukalink Team", "license": "MIT"}