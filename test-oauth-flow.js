// Test OAuth flow
const axios = require("axios");

async function testOAuthFlow() {
  console.log("🧪 Testing Shopify OAuth Flow...\n");

  const baseURL = "http://192.168.1.8:3020/api";
  const shopDomain = "0ssy5g-hg.myshopify.com";

  try {
    // Step 1: Check current status
    console.log("1️⃣ Checking connection status...");
    const statusResponse = await axios.get(`${baseURL}/shopify/status`);
    console.log("Status:", statusResponse.data);

    // Step 2: Generate OAuth URL
    console.log("\n2️⃣ Generating OAuth URL...");
    const authResponse = await axios.post(`${baseURL}/shopify/auth-url`, {
      shopDomain: shopDomain,
    });

    if (authResponse.data.success) {
      console.log("✅ OAuth URL generated successfully!");
      console.log("🔗 OAuth URL:", authResponse.data.data.authUrl);
      console.log("🔑 State:", authResponse.data.data.state);

      console.log("\n📋 Next Steps:");
      console.log("1. Open the OAuth URL in your browser");
      console.log("2. Login to your Shopify store admin");
      console.log("3. Authorize the app");
      console.log("4. The callback will store the access token");
      console.log("5. Then test the store API endpoints");
    } else {
      console.log("❌ Failed to generate OAuth URL:", authResponse.data.error);
    }

    // Step 3: Test if we can access store without OAuth (should fail)
    console.log("\n3️⃣ Testing store access without OAuth...");
    try {
      const testResponse = await axios.get(`${baseURL}/shopify/test`);
      console.log(
        "✅ Store test successful (already authorized):",
        testResponse.data
      );
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log("✅ Store test correctly failed - OAuth required");
        console.log("   Error:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.message);
      }
    }
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("Response:", error.response.data);
    }
  }

  console.log("\n🎯 OAuth Flow Summary:");
  console.log("   - OAuth URL generation: Working");
  console.log("   - Store admin needs to complete OAuth in browser");
  console.log("   - After OAuth: Access token will be stored");
  console.log("   - Then: All Shopify API calls will work");
  console.log("\n✅ OAuth flow is ready for store admin!");
}

testOAuthFlow();
