import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { Colors } from "@/constants/Colors";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useAppDispatch, useAppSelector } from "@/src/store";
import {
  clearError,
  exchangeAuthCode,
  getShopifyAuthURL,
} from "@/src/store/slices/authSlice";
import * as AuthSession from "expo-auth-session";
import * as Linking from "expo-linking";
import { useRouter } from "expo-router";
import * as WebBrowser from "expo-web-browser";
import React, { useEffect, useState } from "react";
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const AuthScreen: React.FC = () => {
  const [shopDomain, setShopDomain] = useState("");
  const [isValidating, setIsValidating] = useState(false);

  const dispatch = useAppDispatch();
  const router = useRouter();
  const { isLoading, error, authUrl, isAuthenticated } = useAppSelector(
    (state) => state.auth
  );
  const { isShopifyAuthenticated, setShopifyAuth } = useSession();

  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const surfaceColor = useThemeColor({}, "surface");

  useEffect(() => {
    if (isAuthenticated || isShopifyAuthenticated) {
      router.replace("/(tabs)");
    }
  }, [isAuthenticated, isShopifyAuthenticated, router]);

  useEffect(() => {
    if (authUrl) {
      handleOAuthFlow();
    }
  }, [authUrl]);

  useEffect(() => {
    const handleDeepLink = (url: string) => {
      const parsedUrl = Linking.parse(url);

      if (parsedUrl.path === "auth" && parsedUrl.queryParams) {
        const {
          success,
          token,
          storeId,
          error: authError,
        } = parsedUrl.queryParams;

        if (success === "true" && token && storeId) {
          Alert.alert("Success", "Successfully connected to Shopify store!");
          router.replace("/(tabs)");
        } else if (authError) {
          Alert.alert(
            "Authentication Error",
            decodeURIComponent(authError as string)
          );
        }
      }
    };

    const subscription = Linking.addEventListener("url", ({ url }) => {
      handleDeepLink(url);
    });

    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url);
      }
    });

    return () => subscription?.remove();
  }, [router]);

  const validateShopDomain = (domain: string): boolean => {
    const cleanDomain = domain.replace(/^https?:\/\//, "").replace(/\/$/, "");
    const shopifyDomainRegex = /^[a-zA-Z0-9][a-zA-Z0-9\-]*\.myshopify\.com$/;
    return shopifyDomainRegex.test(cleanDomain);
  };

  const formatShopDomain = (domain: string): string => {
    let cleanDomain = domain.replace(/^https?:\/\//, "").replace(/\/$/, "");

    if (!cleanDomain.endsWith(".myshopify.com")) {
      cleanDomain = cleanDomain.replace(/\.myshopify.*$/, "");
      cleanDomain = `${cleanDomain}.myshopify.com`;
    }

    return cleanDomain;
  };

  const handleConnect = async () => {
    if (!shopDomain.trim()) {
      Alert.alert("Error", "Please enter your shop domain");
      return;
    }

    const formattedDomain = formatShopDomain(shopDomain.trim());

    if (!validateShopDomain(formattedDomain)) {
      Alert.alert(
        "Invalid Domain",
        "Please enter a valid Shopify domain (e.g., your-shop.myshopify.com)"
      );
      return;
    }

    setIsValidating(true);
    dispatch(clearError());

    try {
      await dispatch(getShopifyAuthURL(formattedDomain)).unwrap();
    } catch (error: any) {
      Alert.alert("Connection Error", error || "Failed to connect to shop");
    } finally {
      setIsValidating(false);
    }
  };

  const handleOAuthFlow = async () => {
    if (!authUrl) return;

    try {
      const redirectUrl = AuthSession.makeRedirectUri({
        scheme: "dukalink",
        path: "auth",
      });

      const result = await WebBrowser.openAuthSessionAsync(
        authUrl,
        redirectUrl
      );

      if (result.type === "success" && result.url) {
        const url = new URL(result.url);
        const code = url.searchParams.get("code");
        const state = url.searchParams.get("state");

        if (code && state) {
          const result = await dispatch(
            exchangeAuthCode({ code, state })
          ).unwrap();
          // Update the session context with the authentication data
          setShopifyAuth(result.sessionToken, result.store);
        } else {
          throw new Error("Authorization code not received");
        }
      } else if (result.type === "cancel") {
        throw new Error("Authorization cancelled");
      } else {
        throw new Error("Authorization failed");
      }
    } catch (error: any) {
      Alert.alert(
        "Authentication Error",
        error.message || "Failed to authenticate"
      );
    }
  };

  const handleDomainChange = (text: string) => {
    setShopDomain(text.toLowerCase());
    if (error) {
      dispatch(clearError());
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <StatusBar barStyle="light-content" backgroundColor={backgroundColor} />

      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <View style={[styles.logo, { backgroundColor: primaryColor }]}>
              <IconSymbol name="bag.fill" size={32} color="#1A1A1A" />
            </View>
            <Text style={[styles.title, { color: primaryColor }]}>
              Dukalink POS
            </Text>
            <Text style={[styles.subtitle, { color: textSecondary }]}>
              Connect your Shopify store to get started
            </Text>
          </View>

          <ModernCard style={styles.form} variant="elevated">
            <Text style={[styles.label, { color: textColor }]}>
              Shop Domain
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: surfaceColor,
                  borderColor: error ? Colors.light.error : Colors.light.border,
                  color: textColor,
                },
              ]}
              value={shopDomain}
              onChangeText={handleDomainChange}
              placeholder="your-shop.myshopify.com"
              placeholderTextColor={textSecondary}
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
              editable={!isLoading && !isValidating}
            />

            {error && (
              <Text style={[styles.errorText, { color: Colors.light.error }]}>
                {error}
              </Text>
            )}

            <ModernButton
              title={
                isLoading || isValidating ? "Connecting..." : "Connect Store"
              }
              onPress={handleConnect}
              loading={isLoading || isValidating}
              disabled={isLoading || isValidating || !shopDomain.trim()}
              style={styles.connectButton}
              icon={<IconSymbol name="link" size={20} color="#1A1A1A" />}
            />
          </ModernCard>

          <ModernCard style={styles.info} variant="outlined">
            <View style={styles.infoHeader}>
              <IconSymbol name="info.circle" size={24} color={primaryColor} />
              <Text style={[styles.infoTitle, { color: textColor }]}>
                What happens next?
              </Text>
            </View>

            <View style={styles.infoSteps}>
              <View style={styles.infoStep}>
                <View
                  style={[styles.stepNumber, { backgroundColor: primaryColor }]}
                >
                  <Text style={styles.stepNumberText}>1</Text>
                </View>
                <Text style={[styles.infoText, { color: textSecondary }]}>
                  You&apos;ll be redirected to Shopify to authorize Dukalink POS
                </Text>
              </View>

              <View style={styles.infoStep}>
                <View
                  style={[styles.stepNumber, { backgroundColor: primaryColor }]}
                >
                  <Text style={styles.stepNumberText}>2</Text>
                </View>
                <Text style={[styles.infoText, { color: textSecondary }]}>
                  Grant permissions to access your products, orders, and
                  customers
                </Text>
              </View>

              <View style={styles.infoStep}>
                <View
                  style={[styles.stepNumber, { backgroundColor: primaryColor }]}
                >
                  <Text style={styles.stepNumberText}>3</Text>
                </View>
                <Text style={[styles.infoText, { color: textSecondary }]}>
                  Return to the app to start using your POS system
                </Text>
              </View>
            </View>
          </ModernCard>

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="chevron.left" size={20} color={textSecondary} />
            <Text style={[styles.backButtonText, { color: textSecondary }]}>
              Back to Login
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: "center",
    padding: 24,
  },
  header: {
    alignItems: "center",
    marginBottom: 48,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
  },
  form: {
    marginBottom: 32,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 16,
  },
  connectButton: {
    marginTop: 16,
  },
  info: {
    marginBottom: 32,
  },
  infoHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
    gap: 8,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  infoSteps: {
    gap: 16,
  },
  infoStep: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 16,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: 14,
    color: "#1A1A1A",
    fontWeight: "600",
  },
  infoText: {
    fontSize: 16,
    flex: 1,
    lineHeight: 22,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    padding: 16,
  },
  backButtonText: {
    fontSize: 16,
  },
});

export default AuthScreen;
