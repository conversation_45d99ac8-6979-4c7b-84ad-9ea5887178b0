/**
 * Loyalty Synchronization Service
 * Handles real-time synchronization between custom backend loyalty data and Shopify customer metafields
 */

const loyaltyService = require("./loyalty-service");
const shopifyMetafieldsService = require("./shopify-metafields-service");
const shopifyService = require("./shopify-service");

class LoyaltySyncService {
  constructor() {
    this.syncQueue = [];
    this.isProcessing = false;
    this.batchSize = 10;
    this.syncInterval = 30000; // 30 seconds
    this.retryAttempts = 3;
    this.retryDelay = 5000; // 5 seconds
    
    // Start background sync processor
    this.startSyncProcessor();
  }

  // Add customer to sync queue
  async queueCustomerSync(customerId, priority = 'normal') {
    try {
      const existingIndex = this.syncQueue.findIndex(item => item.customerId === customerId);
      
      if (existingIndex !== -1) {
        // Update existing queue item with higher priority if needed
        if (priority === 'high' && this.syncQueue[existingIndex].priority !== 'high') {
          this.syncQueue[existingIndex].priority = 'high';
          this.syncQueue[existingIndex].timestamp = new Date();
        }
      } else {
        // Add new item to queue
        this.syncQueue.push({
          customerId: customerId,
          priority: priority,
          timestamp: new Date(),
          attempts: 0
        });
      }

      // Sort queue by priority and timestamp
      this.syncQueue.sort((a, b) => {
        if (a.priority === 'high' && b.priority !== 'high') return -1;
        if (a.priority !== 'high' && b.priority === 'high') return 1;
        return a.timestamp - b.timestamp;
      });

      console.log(`Customer ${customerId} queued for loyalty sync with ${priority} priority`);
      return { success: true };
    } catch (error) {
      console.error("Queue customer sync error:", error);
      return { success: false, error: error.message };
    }
  }

  // Sync individual customer loyalty data
  async syncCustomerLoyalty(customerId, retryCount = 0) {
    try {
      // Get customer loyalty data from backend
      const loyaltyResult = await loyaltyService.getCustomerSummary(customerId);
      
      if (!loyaltyResult.success) {
        console.error(`Failed to get loyalty data for customer ${customerId}:`, loyaltyResult.error);
        return { success: false, error: loyaltyResult.error };
      }

      const loyaltyData = loyaltyResult.summary;

      // Prepare metafields data
      const metafieldsData = {
        points: loyaltyData.loyaltyPoints,
        tier: loyaltyData.tier,
        totalPurchases: loyaltyData.totalPurchases,
        totalOrders: loyaltyData.totalOrders,
        lastPurchase: loyaltyData.lastPurchase,
        tierUpdated: loyaltyData.tierUpdatedAt || new Date().toISOString()
      };

      // Sync to Shopify metafields
      const syncResult = await shopifyMetafieldsService.setCustomerLoyaltyData(
        `gid://shopify/Customer/${customerId}`,
        metafieldsData
      );

      if (syncResult.success) {
        // Update customer tags if needed
        await this.updateCustomerTags(customerId, loyaltyData.tier);
        
        console.log(`Successfully synced loyalty data for customer ${customerId}`);
        return { 
          success: true, 
          metafieldsUpdated: syncResult.metafieldsUpdated,
          loyaltyData: loyaltyData
        };
      } else {
        throw new Error(syncResult.error);
      }
    } catch (error) {
      console.error(`Sync customer loyalty error for ${customerId}:`, error);
      
      // Retry logic
      if (retryCount < this.retryAttempts) {
        console.log(`Retrying sync for customer ${customerId}, attempt ${retryCount + 1}`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        return this.syncCustomerLoyalty(customerId, retryCount + 1);
      }
      
      return { success: false, error: error.message };
    }
  }

  // Update customer tags based on loyalty tier
  async updateCustomerTags(customerId, tier) {
    try {
      const tierTags = {
        bronze: 'loyalty-bronze',
        silver: 'loyalty-silver',
        gold: 'loyalty-gold',
        platinum: 'loyalty-platinum'
      };

      const newTag = tierTags[tier];
      if (!newTag) return { success: false, error: 'Invalid tier' };

      // Get current customer data
      const customerResult = await shopifyService.makeRequest('GET', `/customers/${customerId}.json`);
      
      if (!customerResult.success) {
        return { success: false, error: 'Failed to get customer data' };
      }

      const customer = customerResult.data.customer;
      let tags = customer.tags ? customer.tags.split(', ') : [];

      // Remove old loyalty tier tags
      tags = tags.filter(tag => !Object.values(tierTags).includes(tag));
      
      // Add new tier tag and loyalty member tag
      if (!tags.includes(newTag)) {
        tags.push(newTag);
      }
      if (!tags.includes('loyalty-member')) {
        tags.push('loyalty-member');
      }

      // Update customer tags
      const updateResult = await shopifyService.makeRequest('PUT', `/customers/${customerId}.json`, {
        customer: {
          id: customerId,
          tags: tags.join(', ')
        }
      });

      return updateResult;
    } catch (error) {
      console.error("Update customer tags error:", error);
      return { success: false, error: error.message };
    }
  }

  // Process sync queue
  async processSyncQueue() {
    if (this.isProcessing || this.syncQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log(`Processing ${this.syncQueue.length} items in loyalty sync queue`);

    try {
      // Process items in batches
      const batch = this.syncQueue.splice(0, this.batchSize);
      const syncPromises = batch.map(async (item) => {
        try {
          const result = await this.syncCustomerLoyalty(item.customerId);
          if (!result.success) {
            // Re-queue failed items with incremented attempt count
            item.attempts++;
            if (item.attempts < this.retryAttempts) {
              this.syncQueue.push(item);
            } else {
              console.error(`Max retry attempts reached for customer ${item.customerId}`);
            }
          }
          return result;
        } catch (error) {
          console.error(`Sync error for customer ${item.customerId}:`, error);
          return { success: false, error: error.message };
        }
      });

      await Promise.allSettled(syncPromises);
    } catch (error) {
      console.error("Process sync queue error:", error);
    } finally {
      this.isProcessing = false;
    }
  }

  // Start background sync processor
  startSyncProcessor() {
    setInterval(() => {
      this.processSyncQueue();
    }, this.syncInterval);
    
    console.log("Loyalty sync processor started");
  }

  // Bulk sync all customers
  async bulkSyncAllCustomers() {
    try {
      console.log("Starting bulk sync of all customers");
      
      // Get all customers with loyalty data
      const [rows] = await loyaltyService.pool.execute(
        `SELECT DISTINCT shopify_customer_id FROM customer_loyalty WHERE shopify_customer_id IS NOT NULL`
      );

      const customerIds = rows.map(row => row.shopify_customer_id);
      console.log(`Found ${customerIds.length} customers to sync`);

      // Queue all customers for sync
      for (const customerId of customerIds) {
        await this.queueCustomerSync(customerId, 'normal');
      }

      return {
        success: true,
        message: `Queued ${customerIds.length} customers for bulk sync`,
        customerCount: customerIds.length
      };
    } catch (error) {
      console.error("Bulk sync all customers error:", error);
      return {
        success: false,
        error: "Failed to start bulk sync"
      };
    }
  }

  // Handle order completion webhook
  async handleOrderCompleted(orderData) {
    try {
      console.log(`Processing order completion for order ${orderData.id}`);
      
      const customerId = orderData.customer?.id;
      if (!customerId) {
        console.log("No customer ID found in order data");
        return { success: true, message: "No customer to sync" };
      }

      const orderTotal = parseFloat(orderData.total_price || 0);
      const staffId = this.extractStaffIdFromOrder(orderData);
      const salesAgentId = this.extractSalesAgentIdFromOrder(orderData);

      // Add loyalty points for the purchase
      const pointsResult = await loyaltyService.addPoints(
        customerId,
        orderTotal,
        orderData.id,
        staffId,
        salesAgentId
      );

      if (pointsResult.success) {
        // Queue customer for high-priority sync
        await this.queueCustomerSync(customerId, 'high');
        
        console.log(`Added ${pointsResult.pointsAdded} points for customer ${customerId}, order ${orderData.id}`);
        return {
          success: true,
          pointsAdded: pointsResult.pointsAdded,
          newBalance: pointsResult.newBalance,
          tierChanged: pointsResult.tierChanged
        };
      } else {
        console.error("Failed to add loyalty points:", pointsResult.error);
        return { success: false, error: pointsResult.error };
      }
    } catch (error) {
      console.error("Handle order completed error:", error);
      return { success: false, error: error.message };
    }
  }

  // Extract staff ID from order data
  extractStaffIdFromOrder(orderData) {
    // Check note attributes first
    const staffAttribute = orderData.note_attributes?.find(attr => attr.name === 'staff_id');
    if (staffAttribute) {
      return staffAttribute.value;
    }

    // Check metafields
    const staffMetafield = orderData.metafields?.find(
      metafield => metafield.namespace === 'dukalink_pos' && metafield.key === 'staff_id'
    );
    if (staffMetafield) {
      return staffMetafield.value;
    }

    return null;
  }

  // Extract sales agent ID from order data
  extractSalesAgentIdFromOrder(orderData) {
    // Check note attributes first
    const agentAttribute = orderData.note_attributes?.find(attr => attr.name === 'sales_agent_id');
    if (agentAttribute) {
      return agentAttribute.value;
    }

    // Check metafields
    const agentMetafield = orderData.metafields?.find(
      metafield => metafield.namespace === 'dukalink_pos' && metafield.key === 'sales_agent_id'
    );
    if (agentMetafield) {
      return agentMetafield.value;
    }

    return null;
  }

  // Get sync queue status
  getSyncQueueStatus() {
    return {
      queueLength: this.syncQueue.length,
      isProcessing: this.isProcessing,
      highPriorityItems: this.syncQueue.filter(item => item.priority === 'high').length,
      normalPriorityItems: this.syncQueue.filter(item => item.priority === 'normal').length,
      oldestItem: this.syncQueue.length > 0 ? this.syncQueue[0].timestamp : null
    };
  }

  // Clear sync queue
  clearSyncQueue() {
    const clearedCount = this.syncQueue.length;
    this.syncQueue = [];
    console.log(`Cleared ${clearedCount} items from sync queue`);
    return { success: true, clearedCount: clearedCount };
  }
}

module.exports = new LoyaltySyncService();
