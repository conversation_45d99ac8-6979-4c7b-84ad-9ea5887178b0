# Database Migrations - Single Source of Truth

This directory contains the **CONSOLIDATED** database setup for the Dukalink POS system.

## ✅ **Single Command Setup**

```bash
npm run setup
```

This **ONE COMMAND** creates everything you need:
- ✅ Drops all existing tables (fresh start)
- ✅ Creates ALL 22 required tables with proper relationships
- ✅ Seeds 4 staff users with passwords and PINs
- ✅ Seeds 4 sales agents with territories
- ✅ Seeds loyalty data (customers, transactions, discount rules)
- ✅ Sets up complete permission system (75 permissions)
- ✅ Verifies setup with table counts

## 🔑 **Default Users Created**

| Username | Password | Role | PIN | Description |
|----------|----------|------|-----|-------------|
| `admin1` | `admin123` | super_admin | 9999 | Full system access |
| `companyadmin1` | `company123` | company_admin | 7777 | Company management |
| `manager1` | `manager123` | manager | 5678 | Store management |
| `cashier1` | `password123` | cashier | 1234 | Basic POS access |

## 🗂️ **Complete Database Structure (22 Tables)**

### Core System (6 tables)
- `pos_staff` - Staff users with PIN support
- `staff_permissions` - Role-based permissions (RBAC)
- `pos_sessions` - User sessions
- `sales_agents` - Sales agent management
- `agent_customers` - Customer-agent relationships
- `agent_performance_history` - Performance tracking

### Ticket Management (4 tables)
- `pos_tickets` - Multi-session cart management
- `ticket_items` - Cart items with persistence
- `ticket_discounts` - Applied discounts
- `ticket_audit_log` - Complete audit trail

### Payment System (4 tables)
- `payment_transactions` - Payment processing
- `payment_methods_used` - Multi-method support
- `credit_payments` - Credit tracking
- `payment_audit_log` - Payment audit trail

### User Management & Security (2 tables)
- `pos_user_switches` - PIN-based user switching
- `pos_security_events` - Security audit trail

### Loyalty & Discounts (6 tables)
- `customer_loyalty` - Customer loyalty data
- `loyalty_transactions` - Points transactions
- `loyalty_redemptions` - Points redemption
- `discount_rules` - Discount configuration
- `staff_discount_permissions` - Staff access control
- `discount_usage_log` - Usage tracking

## 🚀 **Features Enabled**

### Core Features
- Multi-session cart management (tickets)
- Persistent cart storage & resume
- Enhanced order editing with discounts
- PIN-based user switching
- Company admin role with elevated permissions
- Comprehensive audit trail

### Payment System
- Multi-payment method support (Cash, M-Pesa, ABSA Till, Card, Credit)
- Split payment functionality
- Credit payment tracking with customer profiles

### Loyalty & Discounts
- Customer loyalty points system
- Tier-based loyalty benefits (bronze/silver/gold/platinum)
- Advanced discount rules with staff permissions
- Loyalty points redemption
- Commission-based discount integration

## 📊 **Sample Data Included**

- **4 Staff Users** with different roles and permissions
- **4 Sales Agents** with territories and commission rates
- **3 Loyalty Customers** with different tiers and points
- **3 Discount Rules** (tier-based, staff, points redemption)
- **Complete Permission System** for role-based access control

## 🌍 **Environment Variables**

Make sure your `.env` file contains:
```
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=dukalink_shopify_pos
```

## 🔄 **For New Server Deployment**

Simply run:
```bash
cd backend
npm install
npm run setup
```

This will create a complete, fully-functional database ready for production use!

## ✅ **Verification After Setup**

You should see output like:
```
📋 Created tables: [22 tables listed]
👥 Staff members: 4
🤝 Sales agents: 4
🔐 Permissions: 75
🎫 Tickets: 0
🏆 Loyalty customers: 3
💰 Discount rules: 3
📊 Loyalty transactions: 2
💳 Payment transactions: 0
```

## Testing the Setup

After running the setup, you can test authentication:

```bash
# Test login
curl -X POST http://localhost:3020/api/pos/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin1","password":"admin123"}'

# Test protected endpoint (use token from login response)
curl -X GET http://localhost:3020/api/staff \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Files in this Directory

- `setup_database.js` - **THE ONLY SCRIPT YOU NEED** - Complete database setup
- `README.md` - This documentation

All other migration files have been removed to avoid confusion.
