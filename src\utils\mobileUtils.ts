import { AppState, Dimensions, PixelRatio, Platform } from "react-native";

// Dynamic device dimensions that update when app returns from background
let screenDimensions = Dimensions.get("window");
let screenWidth = screenDimensions.width;
let screenHeight = screenDimensions.height;

// Listen for dimension changes (when app returns from background)
const dimensionSubscription = Dimensions.addEventListener(
  "change",
  ({ window }) => {
    screenDimensions = window;
    screenWidth = window.width;
    screenHeight = window.height;
  }
);

// Listen for app state changes to recalculate dimensions
const appStateSubscription = AppState.addEventListener(
  "change",
  (nextAppState) => {
    if (nextAppState === "active") {
      // Recalculate dimensions when app becomes active
      const newDimensions = Dimensions.get("window");
      screenDimensions = newDimensions;
      screenWidth = newDimensions.width;
      screenHeight = newDimensions.height;
    }
  }
);

// Device type detection with dynamic dimensions
export const DeviceInfo = {
  isIOS: Platform.OS === "ios",
  isAndroid: Platform.OS === "android",
  get screenWidth() {
    return screenWidth;
  },
  get screenHeight() {
    return screenHeight;
  },
  get isTablet() {
    return screenWidth >= 768;
  },
  get isSmallScreen() {
    return screenWidth < 375;
  },
  get isLargeScreen() {
    return screenWidth >= 414;
  },
  pixelRatio: PixelRatio.get(),
  fontScale: PixelRatio.getFontScale(),
};

// Responsive dimensions with dynamic values
export const ResponsiveDimensions = {
  // Touch target sizes (minimum 44pt on iOS, 48dp on Android)
  minTouchTarget: DeviceInfo.isIOS ? 44 : 48,

  // Recommended touch target sizes for POS
  touchTargetSmall: 48,
  touchTargetMedium: 56,
  touchTargetLarge: 64,

  // Grid and layout with dynamic values
  get gridColumns() {
    return DeviceInfo.isTablet ? 3 : 2;
  },
  get maxCardWidth() {
    return DeviceInfo.isTablet ? 300 : 180;
  },

  // Spacing based on screen size with dynamic values
  get spacing() {
    return {
      xs: DeviceInfo.isSmallScreen ? 4 : 6,
      sm: DeviceInfo.isSmallScreen ? 6 : 8,
      md: DeviceInfo.isSmallScreen ? 12 : 16,
      lg: DeviceInfo.isSmallScreen ? 18 : 24,
      xl: DeviceInfo.isSmallScreen ? 24 : 32,
      xxl: DeviceInfo.isSmallScreen ? 36 : 48,
    };
  },

  // Typography scaling
  fontSize: {
    xs: DeviceInfo.isSmallScreen ? 12 : 13,
    sm: DeviceInfo.isSmallScreen ? 14 : 15,
    base: DeviceInfo.isSmallScreen ? 16 : 17,
    lg: DeviceInfo.isSmallScreen ? 18 : 20,
    xl: DeviceInfo.isSmallScreen ? 22 : 24,
    "2xl": DeviceInfo.isSmallScreen ? 26 : 30,
    "3xl": DeviceInfo.isSmallScreen ? 32 : 36,
  },
};

// Accessibility helpers
export const AccessibilityHelpers = {
  // Minimum contrast ratios for WCAG compliance
  contrastRatios: {
    normal: 4.5,
    large: 3.0,
    enhanced: 7.0,
  },

  // Touch target accessibility
  getTouchTargetStyle: (size: "small" | "medium" | "large" = "medium") => {
    const sizes = {
      small: ResponsiveDimensions.touchTargetSmall,
      medium: ResponsiveDimensions.touchTargetMedium,
      large: ResponsiveDimensions.touchTargetLarge,
    };

    return {
      minWidth: sizes[size],
      minHeight: sizes[size],
      justifyContent: "center" as const,
      alignItems: "center" as const,
    };
  },

  // Accessible text sizing
  getAccessibleFontSize: (baseSize: number) => {
    return Math.max(baseSize * DeviceInfo.fontScale, 16); // Minimum 16px for readability
  },

  // Focus indicators for keyboard navigation
  focusStyle: {
    borderWidth: 2,
    borderColor: "#007AFF", // iOS blue
    borderRadius: 4,
  },
};

// Responsive breakpoints
export const Breakpoints = {
  small: 0,
  medium: 375,
  large: 414,
  tablet: 768,
  desktop: 1024,
};

// Responsive utility functions
export const ResponsiveUtils = {
  // Get responsive value based on screen width
  getResponsiveValue: <T>(values: {
    small?: T;
    medium?: T;
    large?: T;
    tablet?: T;
    default: T;
  }): T => {
    if (screenWidth >= Breakpoints.tablet && values.tablet !== undefined) {
      return values.tablet;
    }
    if (screenWidth >= Breakpoints.large && values.large !== undefined) {
      return values.large;
    }
    if (screenWidth >= Breakpoints.medium && values.medium !== undefined) {
      return values.medium;
    }
    if (values.small !== undefined) {
      return values.small;
    }
    return values.default;
  },

  // Scale value based on screen size
  scale: (size: number): number => {
    const scale = screenWidth / 375; // Base on iPhone X width
    return Math.round(PixelRatio.roundToNearestPixel(size * scale));
  },

  // Moderate scale for fonts
  moderateScale: (size: number, factor: number = 0.5): number => {
    const scale = screenWidth / 375;
    return Math.round(
      PixelRatio.roundToNearestPixel(size + (scale - 1) * factor)
    );
  },

  // Get grid item width
  getGridItemWidth: (
    columns: number = ResponsiveDimensions.gridColumns,
    spacing: number = 16
  ): number => {
    const totalSpacing = spacing * (columns + 1);
    return (screenWidth - totalSpacing) / columns;
  },

  // Check if device is in landscape
  isLandscape: (): boolean => screenWidth > screenHeight,

  // Get safe area padding for different devices
  // @deprecated Use useSafeAreaInsets from react-native-safe-area-context instead
  getSafeAreaPadding: () => {
    console.warn(
      "getSafeAreaPadding is deprecated. Use useSafeAreaInsets from react-native-safe-area-context instead."
    );
    if (DeviceInfo.isIOS) {
      // iOS safe area handling
      return {
        top: screenHeight >= 812 ? 44 : 20, // iPhone X and newer
        bottom: screenHeight >= 812 ? 34 : 0,
      };
    } else {
      // Android status bar
      return {
        top: 24,
        bottom: 0,
      };
    }
  },
};

// POS-specific mobile optimizations
export const POSOptimizations = {
  // Optimal button sizes for POS operations
  buttonSizes: {
    small: {
      height: ResponsiveDimensions.touchTargetSmall,
      paddingHorizontal: ResponsiveDimensions.spacing.md,
      fontSize: ResponsiveDimensions.fontSize.sm,
    },
    medium: {
      height: ResponsiveDimensions.touchTargetMedium,
      paddingHorizontal: ResponsiveDimensions.spacing.lg,
      fontSize: ResponsiveDimensions.fontSize.base,
    },
    large: {
      height: ResponsiveDimensions.touchTargetLarge,
      paddingHorizontal: ResponsiveDimensions.spacing.xl,
      fontSize: ResponsiveDimensions.fontSize.lg,
    },
  },

  // Card sizes for product grids
  cardSizes: {
    compact: {
      width: ResponsiveUtils.getGridItemWidth(3, 12),
      height: 160,
    },
    comfortable: {
      width: ResponsiveUtils.getGridItemWidth(2, 16),
      height: 200,
    },
    spacious: {
      width: ResponsiveUtils.getGridItemWidth(1, 24),
      height: 120,
    },
  },

  // Input field optimizations
  inputSizes: {
    height: ResponsiveDimensions.touchTargetMedium,
    fontSize: ResponsiveDimensions.fontSize.base,
    paddingHorizontal: ResponsiveDimensions.spacing.md,
    borderRadius: 8,
  },

  // Modal and overlay sizes
  modalSizes: {
    small: {
      width: Math.min(screenWidth * 0.9, 400),
      maxHeight: screenHeight * 0.7,
    },
    medium: {
      width: Math.min(screenWidth * 0.95, 500),
      maxHeight: screenHeight * 0.8,
    },
    large: {
      width: Math.min(screenWidth * 0.98, 600),
      maxHeight: screenHeight * 0.9,
    },
  },
};

// Haptic feedback helpers
export const HapticHelpers = {
  // Light haptic for button presses
  light: () => {
    if (DeviceInfo.isIOS) {
      // iOS haptic feedback would go here
      // import { HapticFeedback } from 'expo-haptics';
      // HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
    }
  },

  // Medium haptic for confirmations
  medium: () => {
    if (DeviceInfo.isIOS) {
      // iOS haptic feedback would go here
    }
  },

  // Heavy haptic for errors or important actions
  heavy: () => {
    if (DeviceInfo.isIOS) {
      // iOS haptic feedback would go here
    }
  },

  // Success haptic
  success: () => {
    if (DeviceInfo.isIOS) {
      // iOS haptic feedback would go here
    }
  },

  // Error haptic
  error: () => {
    if (DeviceInfo.isIOS) {
      // iOS haptic feedback would go here
    }
  },
};

// Performance optimizations for mobile
export const PerformanceHelpers = {
  // Debounce function for search inputs
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: ReturnType<typeof setTimeout>;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Throttle function for scroll events
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  },

  // Optimize image loading
  getOptimizedImageSize: (
    originalWidth: number,
    originalHeight: number,
    maxWidth: number = 300
  ) => {
    const aspectRatio = originalWidth / originalHeight;
    const width = Math.min(originalWidth, maxWidth);
    const height = width / aspectRatio;

    return {
      width: Math.round(width),
      height: Math.round(height),
    };
  },
};

// Cleanup function to remove listeners (call this when app unmounts)
export const cleanupMobileUtils = () => {
  dimensionSubscription?.remove?.();
  appStateSubscription?.remove?.();
};
