import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useCurrency, usePOSCurrency } from "@/src/hooks/useCurrency";
import React from "react";
import { StyleSheet } from "react-native";

/**
 * Example component demonstrating the new currency system
 * This shows how to use the centralized currency utilities
 */
export const CurrencyExamples: React.FC = () => {
  const { format, formatCompact, validate, formatters } = useCurrency();
  const { calculateOrderTotal, formatOrderTotals, validateCashTender } =
    usePOSCurrency();

  // Example data
  const sampleItems = [
    { price: "1250.00", quantity: 2 },
    { price: "750.50", quantity: 1 },
    { price: "2100.75", quantity: 3 },
  ];

  const orderTotals = calculateOrderTotal(sampleItems, 10); // 10% discount, no tax
  const formattedTotals = formatOrderTotals(orderTotals);

  const cashValidation = validateCashTender(orderTotals.total, 8000);

  return (
    <ThemedView style={styles.container}>
      <ThemedText variant="h1" style={styles.title}>
        Currency System Examples
      </ThemedText>

      {/* Basic Formatting */}
      <ThemedView style={styles.section}>
        <ThemedText variant="h3" style={styles.sectionTitle}>
          Basic Currency Formatting
        </ThemedText>

        <ThemedView style={styles.example}>
          <ThemedText variant="body">Standard: {format(1234.56)}</ThemedText>
          <ThemedText variant="body">
            Compact: {formatCompact(1234567)}
          </ThemedText>
          <ThemedText variant="body">
            No Symbol: {format(1234.56, { showSymbol: false })}
          </ThemedText>
          <ThemedText variant="body">
            With Code: {format(1234.56, { showCode: true })}
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Specialized Formatters */}
      <ThemedView style={styles.section}>
        <ThemedText variant="h3" style={styles.sectionTitle}>
          Specialized Formatters
        </ThemedText>

        <ThemedView style={styles.example}>
          <ThemedText variant="body">
            Product: {formatters.product(1250.0)}
          </ThemedText>
          <ThemedText variant="body">
            Total: {formatters.total(4567.89)}
          </ThemedText>
          <ThemedText variant="body">
            Receipt: {formatters.receipt(1250.0)}
          </ThemedText>
          <ThemedText variant="body">
            Compact: {formatters.compact(1250000)}
          </ThemedText>
          <ThemedText variant="body">
            Change: {formatters.change(123.45)}
          </ThemedText>
          {/* Tax formatter removed - POS system no longer displays taxes */}
          <ThemedText variant="body">
            Discount: {formatters.discount(150.0)}
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Validation Examples */}
      <ThemedView style={styles.section}>
        <ThemedText variant="h3" style={styles.sectionTitle}>
          Amount Validation
        </ThemedText>

        <ThemedView style={styles.example}>
          <ThemedText
            variant="body"
            color={validate(1250.0).valid ? "success" : "error"}
          >
            Valid Amount (1250):{" "}
            {validate(1250.0).valid ? "✓ Valid" : "✗ Invalid"}
          </ThemedText>
          <ThemedText
            variant="body"
            color={validate(-50).valid ? "success" : "error"}
          >
            Negative Amount (-50):{" "}
            {validate(-50).valid ? "✓ Valid" : "✗ Invalid"}
          </ThemedText>
          <ThemedText
            variant="body"
            color={validate(0).valid ? "success" : "error"}
          >
            Zero Amount (0): {validate(0).valid ? "✓ Valid" : "✗ Invalid"}
          </ThemedText>
          <ThemedText
            variant="body"
            color={validate(15000000).valid ? "success" : "error"}
          >
            Too Large (15M):{" "}
            {validate(15000000).valid ? "✓ Valid" : "✗ Invalid"}
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* POS Order Calculation */}
      <ThemedView style={styles.section}>
        <ThemedText variant="h3" style={styles.sectionTitle}>
          POS Order Calculation
        </ThemedText>

        <ThemedView style={styles.example}>
          <ThemedText variant="body">Items:</ThemedText>
          {sampleItems.map((item, index) => (
            <ThemedText
              key={index}
              variant="caption"
              color="secondary"
              style={styles.indent}
            >
              {item.quantity}x @ {formatters.product(parseFloat(item.price))} ={" "}
              {formatters.total(parseFloat(item.price) * item.quantity)}
            </ThemedText>
          ))}

          <ThemedView style={styles.divider} />

          <ThemedText variant="body">
            Subtotal: {formattedTotals.subtotal}
          </ThemedText>
          {formattedTotals.discount && (
            <ThemedText variant="body" color="success">
              Discount (10%): -{formattedTotals.discount}
            </ThemedText>
          )}
          {/* Tax display removed - POS system no longer calculates taxes */}
          <ThemedText variant="bodyMedium" color="accent">
            Total: {formattedTotals.total}
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Cash Payment Validation */}
      <ThemedView style={styles.section}>
        <ThemedText variant="h3" style={styles.sectionTitle}>
          Cash Payment Validation
        </ThemedText>

        <ThemedView style={styles.example}>
          <ThemedText variant="body">
            Amount Due: {formatters.total(orderTotals.total)}
          </ThemedText>
          <ThemedText variant="body">
            Amount Tendered: {formatters.total(8000)}
          </ThemedText>

          <ThemedText
            variant="body"
            color={cashValidation.valid ? "success" : "error"}
            style={styles.validationResult}
          >
            {cashValidation.valid
              ? `✓ Valid - Change: ${cashValidation.changeFormatted}`
              : `✗ ${cashValidation.error}`}
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Currency Conversion (Future) */}
      <ThemedView style={styles.section}>
        <ThemedText variant="h3" style={styles.sectionTitle}>
          Future Features
        </ThemedText>

        <ThemedView style={styles.example}>
          <ThemedText variant="caption" color="secondary">
            • Multi-currency support
          </ThemedText>
          <ThemedText variant="caption" color="secondary">
            • Real-time exchange rates
          </ThemedText>
          <ThemedText variant="caption" color="secondary">
            • Currency conversion for international customers
          </ThemedText>
          <ThemedText variant="caption" color="secondary">
            • Regional formatting preferences
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    marginBottom: 24,
    textAlign: "center",
  },
  section: {
    marginBottom: 24,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  example: {
    gap: 8,
  },
  indent: {
    marginLeft: 16,
  },
  divider: {
    height: 1,
    backgroundColor: "rgba(0,0,0,0.1)",
    marginVertical: 8,
  },
  validationResult: {
    marginTop: 8,
    fontWeight: "600",
  },
});
