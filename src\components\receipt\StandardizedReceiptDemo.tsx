/**
 * Standardized Receipt Demo Component
 *
 * Demonstrates the new unified receipt system with all available formats
 */

import React, { useState } from "react";
import { View, Text, ScrollView, StyleSheet, Alert } from "react-native";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { UnifiedReceiptManager } from "@/src/services/UnifiedReceiptManager";

// Sample order data for demonstration with shipping
const sampleOrderData = {
  id: "ORDER_12345",
  orderNumber: "2025-12069",
  totalPrice: "5340.00", // Updated to include shipping
  createdAt: new Date().toISOString(),
  salespersonName: "Mercy Chelangat",
  paymentMethod: "M-PESA",
  paymentTransactionId: "TXN_MPESA_12345",
  customer: {
    firstName: "<PERSON>",
    lastName: "Akoth",
    phone: "0700572061",
    email: "<EMAIL>",
  },
  lineItems: [
    {
      id: "1",
      title: "Eucalyptus Spearmint Wallflower Refill",
      quantity: 1,
      price: "1293.10",
      sku: "ESW001",
    },
    {
      id: "2",
      title: "Sweetest Song Wallflower Refill",
      quantity: 1,
      price: "1293.10",
      sku: "SSW002",
    },
    {
      id: "3",
      title: "Fiji White Sands Wallflower",
      quantity: 1,
      price: "1293.10",
      sku: "FWS003",
    },
  ],
  // Add shipping data for testing
  shippingData: {
    selectedMethod: "Standard Delivery",
    fee: 200.0,
    address: {
      street: "123 Test Street",
      city: "Nairobi",
      postalCode: "00100",
      country: "Kenya",
    },
    contact: {
      name: "Joyce Akoth",
      phone: "0700572061",
    },
  },
  shipping_lines: [
    {
      title: "Standard Delivery",
      price: "200.00",
      code: "standard_delivery",
    },
  ],
};

// Sample payment data for split payment demonstration
// Updated to match the current enhanced payment service data structure and include shipping
const samplePaymentData = {
  transactionId: "TXN_SPLIT_12345",
  totalAmount: 5340.0, // Updated to include shipping
  completedAmount: 5340.0,
  remainingAmount: 0,
  status: "completed",
  isSplitPayment: true,
  paymentMethods: [
    {
      id: "1",
      type: "cash",
      name: "Cash",
      amount: 1000.0,
      status: "completed",
      processedAt: new Date().toISOString(),
      metadata: {
        amountTendered: 1000.0,
        change: 0,
      },
    },
    {
      id: "2",
      type: "mpesa",
      name: "M-PESA",
      amount: 4340.0, // Updated to include shipping
      status: "completed",
      processedAt: new Date().toISOString(),
      metadata: {
        transactionCode: "QHX7Y8Z9",
        mpesaReceiptNumber: "QHX7Y8Z9AB",
        phoneNumber: "254700572061",
      },
    },
  ],
};

export function StandardizedReceiptDemo() {
  const [receiptContent, setReceiptContent] = useState<string>("");
  const [currentFormat, setCurrentFormat] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");

  const generateReceipt = async (
    format: string,
    useSplitPayment: boolean = false
  ) => {
    setIsLoading(true);
    setCurrentFormat(format);

    try {
      const paymentData = useSplitPayment ? samplePaymentData : undefined;

      const result = await UnifiedReceiptManager.generateReceipt(
        sampleOrderData,
        {
          format: format as any,
          autoPrint: false, // Don't auto-print in demo
        },
        paymentData
      );

      if (result.success && result.content) {
        setReceiptContent(result.content);
      } else {
        Alert.alert("Error", result.error || "Failed to generate receipt");
      }
    } catch (error) {
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Unknown error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const testPrinting = async (printerType: string) => {
    setIsLoading(true);

    try {
      const result = await UnifiedReceiptManager.generateReceipt(
        sampleOrderData,
        {
          format: "thermal",
          autoPrint: true,
          printerType: printerType as any,
        }
      );

      if (result.success) {
        Alert.alert(
          "Print Test",
          result.printed
            ? `Receipt printed successfully using ${printerType} printer!`
            : `Receipt generated but printing failed. Error: ${result.error}`
        );
      } else {
        Alert.alert("Print Error", result.error || "Failed to print receipt");
      }
    } catch (error) {
      Alert.alert(
        "Print Error",
        error instanceof Error ? error.message : "Unknown error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const testSharing = async (shareMethod: string) => {
    setIsLoading(true);

    try {
      const result = await UnifiedReceiptManager.generateReceipt(
        sampleOrderData,
        {
          format: "whatsapp",
          shareVia: shareMethod as any,
          recipientPhone: "+254700572061",
          recipientEmail: "<EMAIL>",
        }
      );

      if (result.success) {
        Alert.alert(
          "Share Test",
          result.shared
            ? `Receipt shared successfully via ${shareMethod}!`
            : `Receipt generated but sharing failed. Error: ${result.error}`
        );
      } else {
        Alert.alert("Share Error", result.error || "Failed to share receipt");
      }
    } catch (error) {
      Alert.alert(
        "Share Error",
        error instanceof Error ? error.message : "Unknown error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <Text style={[styles.title, { color: textColor }]}>
        Standardized Receipt System Demo
      </Text>

      <Text style={[styles.subtitle, { color: textSecondary }]}>
        Test the new unified receipt generation system with official Treasured
        Scents formatting
      </Text>

      {/* Receipt Format Generation */}
      <ModernCard style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Receipt Formats
        </Text>

        <View style={styles.buttonGrid}>
          <ModernButton
            title="HTML Receipt"
            onPress={() => generateReceipt("html")}
            variant="outline"
            style={styles.button}
            disabled={isLoading}
          />

          <ModernButton
            title="Thermal (32 char)"
            onPress={() => generateReceipt("thermal")}
            variant="outline"
            style={styles.button}
            disabled={isLoading}
          />

          <ModernButton
            title="WhatsApp Format"
            onPress={() => generateReceipt("whatsapp")}
            variant="outline"
            style={styles.button}
            disabled={isLoading}
          />

          <ModernButton
            title="Email Format"
            onPress={() => generateReceipt("email")}
            variant="outline"
            style={styles.button}
            disabled={isLoading}
          />
        </View>
      </ModernCard>

      {/* Split Payment Demo */}
      <ModernCard style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Split Payment Demo
        </Text>

        <Text style={[styles.description, { color: textSecondary }]}>
          Test receipts with split payment breakdown (Cash + M-PESA)
        </Text>

        <View style={styles.buttonGrid}>
          <ModernButton
            title="Split Payment HTML"
            onPress={() => generateReceipt("html", true)}
            variant="outline"
            style={styles.button}
            disabled={isLoading}
          />

          <ModernButton
            title="Split Payment Thermal"
            onPress={() => generateReceipt("thermal", true)}
            variant="outline"
            style={styles.button}
            disabled={isLoading}
          />

          <ModernButton
            title="Split Payment WhatsApp"
            onPress={() => generateReceipt("whatsapp", true)}
            variant="outline"
            style={styles.button}
            disabled={isLoading}
          />
        </View>
      </ModernCard>

      {/* Printing Tests */}
      <ModernCard style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Printing Tests
        </Text>

        <View style={styles.buttonGrid}>
          <ModernButton
            title="Test Thermal Print"
            onPress={() => testPrinting("thermal")}
            variant="primary"
            style={styles.button}
            disabled={isLoading}
          />

          <ModernButton
            title="Test Web Print"
            onPress={() => testPrinting("web")}
            variant="primary"
            style={styles.button}
            disabled={isLoading}
          />
        </View>
      </ModernCard>

      {/* Sharing Tests */}
      <ModernCard style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Sharing Tests
        </Text>

        <View style={styles.buttonGrid}>
          <ModernButton
            title="Share via WhatsApp"
            onPress={() => testSharing("whatsapp")}
            variant="primary"
            style={styles.button}
            disabled={isLoading}
          />

          <ModernButton
            title="Share via Email"
            onPress={() => testSharing("email")}
            variant="primary"
            style={styles.button}
            disabled={isLoading}
          />
        </View>
      </ModernCard>

      {/* Receipt Preview */}
      {receiptContent && (
        <ModernCard style={styles.section}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Receipt Preview ({currentFormat})
          </Text>

          <ScrollView style={styles.previewContainer} horizontal>
            <Text style={[styles.receiptText, { color: textColor }]}>
              {receiptContent}
            </Text>
          </ScrollView>
        </ModernCard>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: "center",
    lineHeight: 22,
  },
  section: {
    marginBottom: 20,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  buttonGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  button: {
    flex: 1,
    minWidth: 140,
    marginBottom: 8,
  },
  previewContainer: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 12,
    maxHeight: 300,
  },
  receiptText: {
    fontFamily: "monospace",
    fontSize: 12,
    lineHeight: 16,
  },
});
