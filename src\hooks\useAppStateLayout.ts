import { useCallback, useEffect, useState } from "react";
import { AppState, Dimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

interface LayoutDimensions {
  width: number;
  height: number;
  isLandscape: boolean;
}

interface AppStateLayoutData {
  dimensions: LayoutDimensions;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  appState: string;
  isActive: boolean;
  forceRefresh: () => void;
}

/**
 * Hook to handle app state changes and layout updates
 * Automatically recalculates dimensions and safe area when app returns from background
 */
export const useAppStateLayout = (): AppStateLayoutData => {
  const insets = useSafeAreaInsets();
  const [appState, setAppState] = useState(AppState.currentState);
  const [refreshKey, setRefreshKey] = useState(0);
  const [dimensions, setDimensions] = useState<LayoutDimensions>(() => {
    const { width, height } = Dimensions.get("window");
    return {
      width,
      height,
      isLandscape: width > height,
    };
  });

  const forceRefresh = useCallback(() => {
    setRefreshKey((prev) => prev + 1);
    const { width, height } = Dimensions.get("window");
    setDimensions({
      width,
      height,
      isLandscape: width > height,
    });
  }, []);

  useEffect(() => {
    // Handle app state changes
    const handleAppStateChange = (nextAppState: string) => {
      setAppState(nextAppState);

      if (nextAppState === "active") {
        // Force refresh when app becomes active to fix safe area issues
        setTimeout(() => {
          forceRefresh();
        }, 100); // Small delay to ensure proper layout calculation
      }
    };

    // Handle dimension changes
    const handleDimensionChange = ({
      window,
    }: {
      window: { width: number; height: number };
    }) => {
      setDimensions({
        width: window.width,
        height: window.height,
        isLandscape: window.width > window.height,
      });
    };

    // Subscribe to app state changes
    const appStateSubscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    // Subscribe to dimension changes
    const dimensionSubscription = Dimensions.addEventListener(
      "change",
      handleDimensionChange
    );

    // Cleanup subscriptions
    return () => {
      appStateSubscription?.remove();
      dimensionSubscription?.remove();
    };
  }, [forceRefresh]);

  return {
    dimensions,
    safeAreaInsets: {
      top: insets.top,
      bottom: insets.bottom,
      left: insets.left,
      right: insets.right,
    },
    appState,
    isActive: appState === "active",
    forceRefresh,
  };
};

/**
 * Hook to get dynamic safe area styles
 * Updates automatically when app returns from background
 */
export const useDynamicSafeArea = (
  edges: ("top" | "bottom" | "left" | "right")[] = ["top", "bottom"]
) => {
  const { safeAreaInsets } = useAppStateLayout();

  const getSafeAreaStyle = () => {
    const style: any = {};

    if (edges.includes("top")) {
      style.paddingTop = safeAreaInsets.top;
    }
    if (edges.includes("bottom")) {
      style.paddingBottom = safeAreaInsets.bottom;
    }
    if (edges.includes("left")) {
      style.paddingLeft = safeAreaInsets.left;
    }
    if (edges.includes("right")) {
      style.paddingRight = safeAreaInsets.right;
    }

    return style;
  };

  return {
    safeAreaInsets,
    safeAreaStyle: getSafeAreaStyle(),
  };
};

/**
 * Hook to get dynamic screen dimensions
 * Updates automatically when app returns from background or device rotates
 */
export const useDynamicDimensions = () => {
  const { dimensions } = useAppStateLayout();

  return {
    ...dimensions,
    isTablet: dimensions.width >= 768,
    isSmallScreen: dimensions.width < 375,
    isLargeScreen: dimensions.width >= 414,
  };
};
