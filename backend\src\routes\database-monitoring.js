/**
 * Database Monitoring Routes
 * 
 * Provides endpoints for monitoring database connection pool health,
 * detecting connection leaks, and viewing database statistics.
 */

const express = require('express');
const { databaseManager } = require('../config/database-manager');
const { authenticateToken } = require('../middleware/auth-mysql');

const router = express.Router();

/**
 * Get database connection pool statistics
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = databaseManager.getStats();
    
    res.json({
      success: true,
      data: {
        connectionStats: stats.connectionStats,
        poolConfig: stats.poolConfig,
        poolState: stats.poolState,
        healthStatus: determineHealthStatus(stats),
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error getting database stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get database statistics',
    });
  }
});

/**
 * Get detailed database health report
 */
router.get('/health', authenticateToken, async (req, res) => {
  try {
    const stats = databaseManager.getStats();
    const healthStatus = determineHealthStatus(stats);
    
    // Perform a test query to verify connectivity
    let connectionTest = { success: false, responseTime: null };
    const startTime = Date.now();
    
    try {
      await databaseManager.executeQuery('SELECT 1 as test');
      connectionTest = {
        success: true,
        responseTime: Date.now() - startTime,
      };
    } catch (testError) {
      connectionTest = {
        success: false,
        error: testError.message,
        responseTime: Date.now() - startTime,
      };
    }
    
    const healthReport = {
      overall: healthStatus.status,
      timestamp: new Date().toISOString(),
      connectionPool: {
        status: healthStatus.status,
        totalConnections: stats.connectionStats.totalConnections,
        activeConnections: stats.connectionStats.activeConnections,
        queuedRequests: stats.connectionStats.queuedRequests,
        connectionLimit: stats.poolConfig?.connectionLimit || 0,
        utilizationPercentage: stats.poolConfig?.connectionLimit 
          ? Math.round((stats.connectionStats.activeConnections / stats.poolConfig.connectionLimit) * 100)
          : 0,
      },
      performance: {
        totalQueries: stats.connectionStats.totalQueries,
        failedQueries: stats.connectionStats.failedQueries,
        successRate: stats.connectionStats.totalQueries > 0 
          ? Math.round(((stats.connectionStats.totalQueries - stats.connectionStats.failedQueries) / stats.connectionStats.totalQueries) * 100)
          : 100,
        lastHealthCheck: stats.connectionStats.lastHealthCheck,
      },
      connectivity: connectionTest,
      warnings: healthStatus.warnings,
      recommendations: generateRecommendations(stats),
    };
    
    res.json({
      success: true,
      data: healthReport,
    });
  } catch (error) {
    console.error('Error getting database health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get database health report',
    });
  }
});

/**
 * Get connection leak detection report
 */
router.get('/leaks', authenticateToken, async (req, res) => {
  try {
    const stats = databaseManager.getStats();
    
    // Detect potential connection leaks
    const leakDetection = {
      timestamp: new Date().toISOString(),
      potentialLeaks: [],
      metrics: {
        activeConnections: stats.connectionStats.activeConnections,
        connectionLimit: stats.poolConfig?.connectionLimit || 0,
        queuedRequests: stats.connectionStats.queuedRequests,
        freeConnections: stats.poolState?.freeConnections || 0,
      },
      analysis: {
        highUtilization: false,
        queueBuildup: false,
        possibleLeak: false,
      },
    };
    
    // Analyze for potential issues
    const utilizationRate = stats.poolConfig?.connectionLimit 
      ? (stats.connectionStats.activeConnections / stats.poolConfig.connectionLimit)
      : 0;
    
    if (utilizationRate > 0.8) {
      leakDetection.analysis.highUtilization = true;
      leakDetection.potentialLeaks.push({
        type: 'high_utilization',
        severity: 'warning',
        message: `High connection utilization: ${Math.round(utilizationRate * 100)}%`,
        recommendation: 'Monitor for connection leaks or consider increasing connection limit',
      });
    }
    
    if (stats.connectionStats.queuedRequests > 5) {
      leakDetection.analysis.queueBuildup = true;
      leakDetection.potentialLeaks.push({
        type: 'queue_buildup',
        severity: 'error',
        message: `High number of queued requests: ${stats.connectionStats.queuedRequests}`,
        recommendation: 'Check for connection leaks or increase connection pool size',
      });
    }
    
    if (utilizationRate > 0.9 && stats.connectionStats.queuedRequests > 0) {
      leakDetection.analysis.possibleLeak = true;
      leakDetection.potentialLeaks.push({
        type: 'possible_leak',
        severity: 'critical',
        message: 'Possible connection leak detected: high utilization with queued requests',
        recommendation: 'Investigate services for unreleased connections',
      });
    }
    
    res.json({
      success: true,
      data: leakDetection,
    });
  } catch (error) {
    console.error('Error detecting connection leaks:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to detect connection leaks',
    });
  }
});

/**
 * Force garbage collection and connection cleanup (admin only)
 */
router.post('/cleanup', authenticateToken, async (req, res) => {
  try {
    // This is a placeholder for cleanup operations
    // In a real implementation, you might:
    // 1. Force garbage collection
    // 2. Close idle connections
    // 3. Reset connection pool statistics
    
    console.log('🧹 Manual database cleanup requested');
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
      console.log('✅ Garbage collection completed');
    }
    
    res.json({
      success: true,
      message: 'Database cleanup completed',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error during database cleanup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform database cleanup',
    });
  }
});

/**
 * Determine overall health status based on statistics
 */
function determineHealthStatus(stats) {
  const warnings = [];
  let status = 'healthy';
  
  // Check connection utilization
  const utilizationRate = stats.poolConfig?.connectionLimit 
    ? (stats.connectionStats.activeConnections / stats.poolConfig.connectionLimit)
    : 0;
  
  if (utilizationRate > 0.9) {
    status = 'critical';
    warnings.push('Very high connection utilization (>90%)');
  } else if (utilizationRate > 0.7) {
    status = 'warning';
    warnings.push('High connection utilization (>70%)');
  }
  
  // Check queued requests
  if (stats.connectionStats.queuedRequests > 10) {
    status = 'critical';
    warnings.push('High number of queued requests');
  } else if (stats.connectionStats.queuedRequests > 5) {
    if (status === 'healthy') status = 'warning';
    warnings.push('Moderate number of queued requests');
  }
  
  // Check error rate
  const errorRate = stats.connectionStats.totalQueries > 0 
    ? (stats.connectionStats.failedQueries / stats.connectionStats.totalQueries)
    : 0;
  
  if (errorRate > 0.1) {
    status = 'critical';
    warnings.push('High query error rate (>10%)');
  } else if (errorRate > 0.05) {
    if (status === 'healthy') status = 'warning';
    warnings.push('Elevated query error rate (>5%)');
  }
  
  return { status, warnings };
}

/**
 * Generate recommendations based on current statistics
 */
function generateRecommendations(stats) {
  const recommendations = [];
  
  const utilizationRate = stats.poolConfig?.connectionLimit 
    ? (stats.connectionStats.activeConnections / stats.poolConfig.connectionLimit)
    : 0;
  
  if (utilizationRate > 0.8) {
    recommendations.push({
      type: 'performance',
      priority: 'high',
      message: 'Consider increasing the connection pool limit',
      action: 'Update DB_CONNECTION_LIMIT environment variable',
    });
  }
  
  if (stats.connectionStats.queuedRequests > 0) {
    recommendations.push({
      type: 'performance',
      priority: 'medium',
      message: 'Monitor for connection leaks in application code',
      action: 'Review database query patterns and ensure proper connection release',
    });
  }
  
  if (stats.connectionStats.failedQueries > 100) {
    recommendations.push({
      type: 'reliability',
      priority: 'high',
      message: 'High number of failed queries detected',
      action: 'Review application logs and database connectivity',
    });
  }
  
  return recommendations;
}

module.exports = router;
