/**
 * Test Switch History Endpoint
 */

const axios = require("axios");

async function testSwitchHistory() {
  try {
    console.log("🔐 Login...");
    const loginResponse = await axios.post("http://localhost:3020/api/pos/login", {
      username: "admin1",
      password: "admin123"
    });

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log("✅ Login successful");

    console.log("📋 Testing switch history...");
    const historyResponse = await axios.get("http://localhost:3020/api/pos/user-switching/switch-history", { headers });

    if (historyResponse.data.success) {
      console.log("✅ Switch history successful");
      console.log(`   Found ${historyResponse.data.data.switches.length} switch records`);
      if (historyResponse.data.data.switches.length > 0) {
        console.log("   Sample switch:", historyResponse.data.data.switches[0]);
      }
    } else {
      console.log("❌ Switch history failed:", historyResponse.data.error);
    }

  } catch (error) {
    console.error("❌ Test failed:", error.response?.data?.error || error.message);
  }
}

testSwitchHistory();
