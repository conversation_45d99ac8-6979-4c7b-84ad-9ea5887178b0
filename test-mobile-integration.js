// Test mobile integration with new POS system
const axios = require("axios");

async function testMobileIntegration() {
  console.log("🧪 Testing Mobile Integration with POS System...\n");

  const baseURL = "http://192.168.1.8:3020/api";
  let authToken = null;

  try {
    // Step 1: Test POS login
    console.log("1️⃣ Testing POS login...");
    const loginResponse = await axios.post(`${baseURL}/pos/login`, {
      username: "cashier1",
      password: "password123",
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.data.token;
      console.log("✅ POS login successful!");
      console.log(`   User: ${loginResponse.data.data.user.name}`);
      console.log(`   Role: ${loginResponse.data.data.user.role}`);
      console.log(`   Token: ${authToken.substring(0, 20)}...`);
    } else {
      console.log("❌ POS login failed:", loginResponse.data.error);
      return;
    }

    // Step 2: Test store info
    console.log("\n2️⃣ Testing store info...");
    const storeResponse = await axios.get(`${baseURL}/store/info`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });

    if (storeResponse.data.success) {
      console.log("✅ Store info retrieved!");
      console.log(`   Store: ${storeResponse.data.data.store.name}`);
      console.log(`   Domain: ${storeResponse.data.data.store.domain}`);
      console.log(`   Currency: ${storeResponse.data.data.store.currency}`);
    } else {
      console.log("❌ Store info failed:", storeResponse.data.error);
    }

    // Step 3: Test products
    console.log("\n3️⃣ Testing products...");
    const productsResponse = await axios.get(
      `${baseURL}/store/products?limit=3`,
      {
        headers: { Authorization: `Bearer ${authToken}` },
      }
    );

    if (productsResponse.data.success) {
      console.log("✅ Products retrieved!");
      console.log(
        `   Found ${productsResponse.data.data.products.length} products`
      );

      if (productsResponse.data.data.products.length > 0) {
        const product = productsResponse.data.data.products[0];
        console.log(`   Sample: ${product.title}`);
        console.log(`   Price: $${product.variants[0].price}`);
        console.log(`   Stock: ${product.variants[0].inventoryQuantity}`);
      }
    } else {
      console.log("❌ Products failed:", productsResponse.data.error);
    }

    // Step 4: Test customers
    console.log("\n4️⃣ Testing customers...");
    const customersResponse = await axios.get(
      `${baseURL}/store/customers?limit=3`,
      {
        headers: { Authorization: `Bearer ${authToken}` },
      }
    );

    if (customersResponse.data.success) {
      console.log("✅ Customers retrieved!");
      console.log(
        `   Found ${customersResponse.data.data.customers.length} customers`
      );
    } else {
      console.log("❌ Customers failed:", customersResponse.data.error);
    }

    // Step 5: Test token verification
    console.log("\n5️⃣ Testing token verification...");
    const verifyResponse = await axios.get(`${baseURL}/pos/verify`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });

    if (verifyResponse.data.success) {
      console.log("✅ Token verification successful!");
      console.log(`   Verified user: ${verifyResponse.data.data.user.name}`);
    } else {
      console.log("❌ Token verification failed");
    }
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("Response:", error.response.data);
    }
  }

  console.log("\n🎯 Mobile Integration Summary:");
  console.log("   ✅ POS authentication working");
  console.log("   ✅ Store data accessible");
  console.log("   ✅ Real Shopify products available");
  console.log("   ✅ Customer data accessible");
  console.log("   ✅ Token-based security working");
  console.log("\n🚀 Mobile app should now work with real data!");
  console.log("\n📱 Expected mobile flow:");
  console.log("   1. Open mobile app");
  console.log("   2. See POS login screen");
  console.log("   3. Login with cashier1/password123");
  console.log("   4. See real Shopify products");
  console.log("   5. Add products to cart");
  console.log("   6. Create real orders");
}

testMobileIntegration();
