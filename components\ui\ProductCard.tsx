import React from 'react';
import { StyleSheet, Image, ViewStyle } from 'react-native';
import { ThemedView, ThemedText, ThemedButton } from '@/src/components/themed/ThemedComponents';
import { FadeInView, AnimatedCard } from '@/src/components/animated/AnimatedComponents';
import { useTheme } from '@/src/contexts/ThemeContext';
import { createStyleUtils } from '@/src/utils/themeUtils';

interface ProductVariant {
  id: string;
  title: string;
  price: string;
  inventoryQuantity: number;
  sku?: string;
}

interface Product {
  id: string;
  title: string;
  variants: ProductVariant[];
  images?: Array<{ src: string }>;
}

interface ProductCardProps {
  product: Product;
  onAddToCart: (product: Product, variant?: ProductVariant) => void;
  style?: ViewStyle;
  index?: number;
}

export function ProductCard({ 
  product, 
  onAddToCart, 
  style,
  index = 0 
}: ProductCardProps) {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  
  const selectedVariant = product.variants[0];
  const hasMultipleVariants = product.variants.length > 1;
  const isOutOfStock = !selectedVariant || selectedVariant.inventoryQuantity <= 0;

  const handleAddToCart = () => {
    onAddToCart(product, selectedVariant);
  };

  return (
    <FadeInView delay={index * 50}>
      <AnimatedCard 
        style={[styles.card, style]}
        elevateOnPress={true}
        scaleOnPress={false}
      >
        <ThemedView style={styles.cardContent}>
          {/* Product Image */}
          {product.images?.[0]?.src && (
            <ThemedView style={styles.imageContainer}>
              <Image
                source={{ uri: product.images[0].src }}
                style={[styles.productImage, { borderRadius: theme.borderRadius.md }]}
                resizeMode="cover"
              />
              {isOutOfStock && (
                <ThemedView style={[styles.outOfStockOverlay, { backgroundColor: theme.colors.overlay }]}>
                  <ThemedText variant="caption" color="error" style={styles.outOfStockText}>
                    Out of Stock
                  </ThemedText>
                </ThemedView>
              )}
            </ThemedView>
          )}

          {/* Product Info */}
          <ThemedView style={styles.productInfo}>
            <ThemedText
              variant="bodyMedium"
              style={[styles.productTitle, utils.mb('sm')]}
              numberOfLines={2}
            >
              {product.title}
            </ThemedText>

            {selectedVariant && (
              <ThemedView style={styles.variantInfo}>
                <ThemedText 
                  variant="h3" 
                  color="accent"
                  style={utils.mb('xs')}
                >
                  KES {parseFloat(selectedVariant.price).toFixed(2)}
                </ThemedText>

                {hasMultipleVariants && (
                  <ThemedText
                    variant="caption"
                    color="secondary"
                    style={utils.mb('xs')}
                    numberOfLines={1}
                  >
                    {selectedVariant.title}
                  </ThemedText>
                )}

                <ThemedText
                  variant="small"
                  color={isOutOfStock ? "error" : "success"}
                  style={utils.mb('md')}
                >
                  {isOutOfStock
                    ? "Out of stock"
                    : `${selectedVariant.inventoryQuantity} in stock`}
                </ThemedText>
              </ThemedView>
            )}

            {/* Add to Cart Button */}
            <ThemedButton
              title="Add to Cart"
              onPress={handleAddToCart}
              disabled={isOutOfStock}
              size="small"
              variant={isOutOfStock ? "outline" : "primary"}
              style={styles.addButton}
            />
          </ThemedView>
        </ThemedView>
      </AnimatedCard>
    </FadeInView>
  );
}

const styles = StyleSheet.create({
  card: {
    flex: 1,
    margin: 8,
    maxWidth: '48%',
  },
  cardContent: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  productImage: {
    width: '100%',
    height: 120,
    marginBottom: 8,
  },
  outOfStockOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  outOfStockText: {
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  productInfo: {
    flex: 1,
  },
  productTitle: {
    minHeight: 40,
    lineHeight: 20,
  },
  variantInfo: {
    marginBottom: 16,
  },
  addButton: {
    marginTop: 'auto',
  },
});
