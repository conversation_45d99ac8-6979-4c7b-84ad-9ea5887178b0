// Mock store service for demo purposes
// This simulates Shopify data so POS can work immediately

class MockStoreService {
  constructor() {
    this.isConnected = true;
    this.store = {
      id: "demo-store",
      name: "Demo Store",
      domain: "0ssy5g-hg.myshopify.com",
      email: "<EMAIL>",
      currency: "KES",
      timezone: "America/New_York",
      plan: "Basic Shopify",
    };

    this.products = [
      {
        id: "1",
        title: "Classic T-Shirt",
        description: "Comfortable cotton t-shirt",
        vendor: "Demo Brand",
        productType: "Apparel",
        tags: ["clothing", "casual", "cotton"],
        status: "active",
        images: [
          {
            id: "1",
            url: "https://via.placeholder.com/300x300/2ecc71/ffffff?text=T-Shirt",
            altText: "Classic T-Shirt",
          },
        ],
        variants: [
          {
            id: "1",
            title: "Small / Black",
            price: "19.99",
            compareAtPrice: "24.99",
            sku: "TSHIRT-S-BLK",
            barcode: "123456789012",
            inventoryQuantity: 25,
            weight: 0.2,
            weightUnit: "kg",
          },
          {
            id: "2",
            title: "Medium / Black",
            price: "19.99",
            compareAtPrice: "24.99",
            sku: "TSHIRT-M-BLK",
            barcode: "123456789013",
            inventoryQuantity: 30,
            weight: 0.2,
            weightUnit: "kg",
          },
          {
            id: "3",
            title: "Large / Black",
            price: "19.99",
            compareAtPrice: "24.99",
            sku: "TSHIRT-L-BLK",
            barcode: "123456789014",
            inventoryQuantity: 20,
            weight: 0.2,
            weightUnit: "kg",
          },
        ],
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "2",
        title: "Wireless Headphones",
        description: "High-quality wireless headphones with noise cancellation",
        vendor: "Tech Brand",
        productType: "Electronics",
        tags: ["electronics", "audio", "wireless"],
        status: "active",
        images: [
          {
            id: "2",
            url: "https://via.placeholder.com/300x300/3498db/ffffff?text=Headphones",
            altText: "Wireless Headphones",
          },
        ],
        variants: [
          {
            id: "4",
            title: "Black",
            price: "99.99",
            compareAtPrice: "129.99",
            sku: "HEADPHONES-BLK",
            barcode: "123456789015",
            inventoryQuantity: 15,
            weight: 0.3,
            weightUnit: "kg",
          },
          {
            id: "5",
            title: "White",
            price: "99.99",
            compareAtPrice: "129.99",
            sku: "HEADPHONES-WHT",
            barcode: "123456789016",
            inventoryQuantity: 12,
            weight: 0.3,
            weightUnit: "kg",
          },
        ],
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "3",
        title: "Coffee Mug",
        description: "Ceramic coffee mug with company logo",
        vendor: "Demo Brand",
        productType: "Home & Garden",
        tags: ["drinkware", "ceramic", "branded"],
        status: "active",
        images: [
          {
            id: "3",
            url: "https://via.placeholder.com/300x300/e74c3c/ffffff?text=Coffee+Mug",
            altText: "Coffee Mug",
          },
        ],
        variants: [
          {
            id: "6",
            title: "Default Title",
            price: "12.99",
            compareAtPrice: "15.99",
            sku: "MUG-001",
            barcode: "123456789017",
            inventoryQuantity: 50,
            weight: 0.4,
            weightUnit: "kg",
          },
        ],
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ];

    this.customers = [
      {
        id: "1",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        phone: "+1234567890",
        addresses: [],
        ordersCount: 5,
        totalSpent: "150.00",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
      {
        id: "2",
        email: "<EMAIL>",
        firstName: "Jane",
        lastName: "Smith",
        phone: "+1234567891",
        addresses: [],
        ordersCount: 3,
        totalSpent: "89.99",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      },
    ];

    this.orders = [];
  }

  // Test connection
  async testConnection() {
    return {
      success: true,
      shop: this.store,
    };
  }

  // Get products
  async getProducts(limit = 50, page = 1) {
    const start = (page - 1) * limit;
    const end = start + limit;
    const paginatedProducts = this.products.slice(start, end);

    return {
      success: true,
      products: paginatedProducts,
    };
  }

  // Search products
  async searchProducts(query, limit = 20) {
    const filteredProducts = this.products.filter(
      (product) =>
        product.title.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase()) ||
        product.tags.some((tag) =>
          tag.toLowerCase().includes(query.toLowerCase())
        )
    );

    return {
      success: true,
      products: filteredProducts.slice(0, limit),
    };
  }

  // Get customers
  async getCustomers(limit = 50, page = 1) {
    const start = (page - 1) * limit;
    const end = start + limit;
    const paginatedCustomers = this.customers.slice(start, end);

    return {
      success: true,
      customers: paginatedCustomers,
    };
  }

  // Create order
  async createOrder(orderData) {
    const newOrder = {
      id: (this.orders.length + 1).toString(),
      ...orderData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: "pending",
    };

    this.orders.push(newOrder);

    return {
      success: true,
      order: newOrder,
    };
  }

  // Get orders
  async getOrders(limit = 50, status = "any") {
    let filteredOrders = this.orders;

    if (status !== "any") {
      filteredOrders = this.orders.filter((order) => order.status === status);
    }

    return {
      success: true,
      orders: filteredOrders.slice(0, limit),
    };
  }

  // Update inventory
  async updateInventory(variantId, quantity) {
    // Find the product variant and update inventory
    for (let product of this.products) {
      const variant = product.variants.find((v) => v.id === variantId);
      if (variant) {
        variant.inventoryQuantity = quantity;
        return {
          success: true,
          inventory_level: {
            inventory_item_id: variantId,
            available: quantity,
          },
        };
      }
    }

    return {
      success: false,
      error: "Variant not found",
    };
  }

  // Check if store is connected
  isStoreConnected() {
    return this.isConnected;
  }
}

module.exports = new MockStoreService();
