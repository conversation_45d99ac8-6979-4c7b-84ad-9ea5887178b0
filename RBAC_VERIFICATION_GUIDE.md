# RBAC Verification Guide

## Overview

This document provides a comprehensive guide for verifying role-based access control (RBAC) implementation across all loyalty and discount management features in the Dukalink POS system.

## Role Definitions

### Staff (Cashier)
- **Permissions**: `view_loyalty`, `apply_discounts`, `view_customers`, `create_orders`
- **Access Level**: Basic operational access
- **Restrictions**: Cannot manage configurations or view analytics

### Manager
- **Permissions**: All Staff permissions + `manage_loyalty`, `manage_discounts`, `view_analytics`, `manage_customers`, `view_orders`
- **Access Level**: Full operational and management access
- **Restrictions**: Cannot manage staff or system settings

### Company Admin
- **Permissions**: All Manager permissions + `manage_staff`
- **Access Level**: Full company management access
- **Restrictions**: Cannot access super admin system features

### Super Admin
- **Permissions**: All permissions including `manage_system`
- **Access Level**: Full system access
- **Restrictions**: None

## Feature Access Matrix

| Feature | Staff | Manager | Company Admin | Super Admin |
|---------|-------|---------|---------------|-------------|
| **Loyalty Management Dashboard** |
| View Dashboard | ✅ | ✅ | ✅ | ✅ |
| View Customer Loyalty Data | ✅ | ✅ | ✅ | ✅ |
| View Leaderboards | ✅ | ✅ | ✅ | ✅ |
| Manage Loyalty Configuration | ❌ | ✅ | ✅ | ✅ |
| **Discount Management Dashboard** |
| View Dashboard | ✅ | ✅ | ✅ | ✅ |
| Apply Discounts | ✅ | ✅ | ✅ | ✅ |
| Create/Edit Discount Rules | ❌ | ✅ | ✅ | ✅ |
| Manage Staff Permissions | ❌ | ✅ | ✅ | ✅ |
| **Analytics** |
| View Loyalty Analytics | ❌ | ✅ | ✅ | ✅ |
| View Discount Analytics | ❌ | ✅ | ✅ | ✅ |
| **Navigation** |
| Loyalty Management Link | ✅ | ✅ | ✅ | ✅ |
| Discount Management Link | ✅ | ✅ | ✅ | ✅ |
| **Dashboard Cards** |
| Loyalty Management Card | ✅ | ✅ | ✅ | ✅ |
| Discount Management Card | ✅ | ✅ | ✅ | ✅ |

## Testing Methods

### 1. Automated RBAC Testing

Use the built-in RBAC test runner:

```typescript
// Access the RBAC test screen (development only)
// Navigate to /rbac-test in the app
```

The test runner will:
- Verify permissions for each role
- Test navigation access
- Validate dashboard card visibility
- Check feature-specific access controls

### 2. Manual Testing Checklist

#### For Each Role:

1. **Login with role-specific credentials**
2. **Verify Navigation Access**
   - Check if loyalty management link appears in sidebar
   - Check if discount management link appears in sidebar
   - Verify links are clickable and navigate correctly

3. **Verify Dashboard Cards**
   - Check if loyalty management card appears on main dashboard
   - Check if discount management card appears on main dashboard
   - Verify cards are clickable and navigate correctly

4. **Test Loyalty Management Screen**
   - Navigate to `/loyalty-management`
   - Verify screen loads without errors
   - Check tab visibility (Overview, Leaderboard, Analytics, Config)
   - Test tab functionality based on role permissions

5. **Test Discount Management Screen**
   - Navigate to `/discount-management`
   - Verify screen loads without errors
   - Check tab visibility (Rules, Permissions, Analytics)
   - Test create discount functionality (managers+ only)

6. **Test Customer Screens**
   - Navigate to customer list
   - Verify loyalty data displays correctly
   - Navigate to customer details
   - Check loyalty tab functionality

### 3. Permission Verification

#### Backend API Testing

Test API endpoints with different role tokens:

```bash
# Test loyalty endpoints
GET /api/loyalty/customers/:customerId
GET /api/loyalty/analytics
POST /api/loyalty/customers/:customerId/points/add

# Test discount endpoints
GET /api/discounts/rules
POST /api/discounts/rules
PUT /api/discounts/rules/:ruleId

# Test customer endpoints with loyalty data
GET /api/store/customers?includeLoyalty=true
```

#### Frontend Component Testing

Verify PermissionGate components:

```typescript
// Check if components render based on permissions
<PermissionGate requiredPermissions={['manage_loyalty']}>
  <LoyaltyConfigurationComponent />
</PermissionGate>

<PermissionGate requiredPermissions={['view_analytics']}>
  <AnalyticsComponent />
</PermissionGate>
```

## Implementation Files

### RBAC Configuration
- `src/config/rbac.ts` - Role and permission definitions
- `src/hooks/useRBAC.ts` - RBAC hook implementation

### Screen Access Control
- `app/loyalty-management.tsx` - Loyalty management screen with RBAC
- `app/discount-management.tsx` - Discount management screen with RBAC
- `app/customer-details.tsx` - Customer details with loyalty tabs

### Navigation Components
- `components/layout/SlidingSidebar.tsx` - Sidebar with role-based links
- `components/layout/SidebarLayout.tsx` - Layout with role-based navigation
- `app/(tabs)/index.tsx` - Dashboard with role-based cards

### Backend Routes
- `backend/src/routes/customer-loyalty.js` - Loyalty API with auth middleware
- `backend/src/routes/commission-discounts.js` - Discount API with auth middleware
- `backend/src/routes/store-api.js` - Customer API with loyalty data

## Common Issues and Solutions

### 1. Permission Not Working
- Check role assignment in user profile
- Verify permission mapping in RBAC configuration
- Ensure middleware is applied to backend routes

### 2. Navigation Links Not Appearing
- Check hasPermission calls in navigation components
- Verify permission names match RBAC configuration
- Test with different user roles

### 3. Screen Access Denied
- Check screen-level permission requirements
- Verify PermissionGate implementation
- Test navigation flow from different entry points

### 4. API Access Denied
- Check backend middleware implementation
- Verify token includes correct role information
- Test API endpoints with different role tokens

## Test Data Setup

### Create Test Users

```sql
-- Staff user
INSERT INTO staff (name, email, role, permissions) VALUES 
('Test Staff', '<EMAIL>', 'staff', '["view_loyalty", "apply_discounts"]');

-- Manager user
INSERT INTO staff (name, email, role, permissions) VALUES 
('Test Manager', '<EMAIL>', 'manager', '["view_loyalty", "manage_loyalty", "apply_discounts", "manage_discounts", "view_analytics"]');

-- Company Admin user
INSERT INTO staff (name, email, role, permissions) VALUES 
('Test Admin', '<EMAIL>', 'company_admin', '["view_loyalty", "manage_loyalty", "apply_discounts", "manage_discounts", "view_analytics", "manage_staff"]');
```

### Test Scenarios

1. **Staff User Journey**
   - Login as staff
   - View loyalty dashboard (should work)
   - Try to access loyalty config (should be hidden/disabled)
   - Apply discount during checkout (should work)
   - Try to create discount rule (should be hidden/disabled)

2. **Manager User Journey**
   - Login as manager
   - Access all loyalty features (should work)
   - Create and manage discount rules (should work)
   - View analytics (should work)
   - Try to manage staff (should be restricted)

3. **Cross-Role Testing**
   - Switch between roles during session
   - Verify permission changes take effect immediately
   - Test edge cases and boundary conditions

## Reporting Issues

When reporting RBAC issues, include:
- User role and permissions
- Specific feature or screen affected
- Expected vs actual behavior
- Steps to reproduce
- Browser/device information
- Console errors (if any)

## Maintenance

### Adding New Permissions
1. Update RBAC configuration
2. Add permission checks to components
3. Update backend middleware
4. Add test cases
5. Update this documentation

### Modifying Role Access
1. Update role definitions in RBAC config
2. Test all affected features
3. Update test cases
4. Verify backward compatibility
