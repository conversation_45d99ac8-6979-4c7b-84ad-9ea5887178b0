import { AnimatedButton } from "@/src/components/animated/AnimatedComponents";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  AccessibilityHelpers,
  HapticHelpers,
  ResponsiveDimensions,
} from "@/src/utils/mobileUtils";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { ReactNode } from "react";
import {
  AccessibilityRole,
  AccessibilityState,
  TextInput,
  TextInputProps,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
} from "react-native";

// Accessible Button Component
interface AccessibleButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: "primary" | "secondary" | "outline" | "danger";
  size?: "small" | "medium" | "large";
  icon?: ReactNode;
  loading?: boolean;
  hapticFeedback?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  children?: ReactNode;
}

export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  title,
  variant = "primary",
  size = "medium",
  icon,
  loading = false,
  hapticFeedback = true,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = "button",
  onPress,
  disabled,
  style,
  children,
  ...props
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const handlePress = (event: any) => {
    if (hapticFeedback) {
      HapticHelpers.light();
    }

    if (onPress) {
      onPress(event);
    }
  };

  const getButtonStyle = (): ViewStyle => {
    const touchTarget = AccessibilityHelpers.getTouchTargetStyle(size);
    const baseStyle = {
      ...touchTarget,
      borderRadius: theme.borderRadius.md,
      flexDirection: "row" as const,
      gap: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg,
    };

    switch (variant) {
      case "primary":
        return {
          ...baseStyle,
          backgroundColor: theme.colors.primary,
        };
      case "secondary":
        return {
          ...baseStyle,
          backgroundColor: theme.colors.secondary,
        };
      case "outline":
        return {
          ...baseStyle,
          backgroundColor: "transparent",
          borderWidth: 2,
          borderColor: theme.colors.primary,
        };
      case "danger":
        return {
          ...baseStyle,
          backgroundColor: theme.colors.error,
        };
      default:
        return baseStyle;
    }
  };

  const getTextColor = (): string => {
    switch (variant) {
      case "primary":
        return theme.colors.primaryForeground;
      case "secondary":
        return theme.colors.secondaryForeground;
      case "outline":
        return theme.colors.primary;
      case "danger":
        return theme.colors.textInverse;
      default:
        return theme.colors.primaryForeground;
    }
  };

  const accessibilityState: AccessibilityState = {
    disabled: disabled || loading,
    busy: loading,
  };

  return (
    <AnimatedButton
      style={[getButtonStyle(), disabled && { opacity: 0.6 }, style]}
      onPress={handlePress}
      disabled={disabled || loading}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityState={accessibilityState}
      scaleOnPress={true}
      {...props}
    >
      {icon}
      <ThemedText
        variant={
          size === "small"
            ? "buttonSmall"
            : size === "large"
            ? "buttonLarge"
            : "button"
        }
        style={{ color: getTextColor() }}
      >
        {title}
      </ThemedText>
      {children}
    </AnimatedButton>
  );
};

// Accessible Text Input Component
interface AccessibleTextInputProps extends TextInputProps {
  label: string;
  error?: string;
  hint?: string;
  required?: boolean;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
}

export const AccessibleTextInput: React.FC<AccessibleTextInputProps> = ({
  label,
  error,
  hint,
  required = false,
  containerStyle,
  labelStyle,
  errorStyle,
  style,
  ...props
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const inputId = `input-${label.toLowerCase().replace(/\s+/g, "-")}`;
  const errorId = error ? `${inputId}-error` : undefined;
  const hintId = hint ? `${inputId}-hint` : undefined;

  const accessibilityDescribedBy = [errorId, hintId].filter(Boolean).join(" ");

  return (
    <ThemedView style={[styles.inputContainer, containerStyle]}>
      {/* Label */}
      <ThemedText
        variant="label"
        style={[styles.label, { color: theme.colors.text }, labelStyle]}
        accessibilityRole="text"
        nativeID={`${inputId}-label`}
      >
        {label}
        {required && (
          <ThemedText style={{ color: theme.colors.error }}> *</ThemedText>
        )}
      </ThemedText>

      {/* Input */}
      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: theme.colors.input,
            borderColor: error ? theme.colors.error : theme.colors.inputBorder,
            color: theme.colors.text,
            fontSize: AccessibilityHelpers.getAccessibleFontSize(
              theme.typography.body.fontSize
            ),
            minHeight: ResponsiveDimensions.touchTargetMedium,
          },
          style,
        ]}
        placeholderTextColor={theme.colors.placeholder}
        accessibilityLabel={label}
        accessibilityRequired={required}
        accessibilityInvalid={!!error}
        accessibilityDescribedBy={accessibilityDescribedBy || undefined}
        accessibilityLabelledBy={`${inputId}-label`}
        nativeID={inputId}
        {...props}
      />

      {/* Hint */}
      {hint && (
        <ThemedText
          variant="small"
          color="secondary"
          style={styles.hint}
          nativeID={hintId}
          accessibilityRole="text"
        >
          {hint}
        </ThemedText>
      )}

      {/* Error */}
      {error && (
        <ThemedText
          variant="small"
          color="error"
          style={[styles.error, errorStyle]}
          nativeID={errorId}
          accessibilityRole="alert"
          accessibilityLiveRegion="polite"
        >
          {error}
        </ThemedText>
      )}
    </ThemedView>
  );
};

// Accessible Card Component
interface AccessibleCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  onPress?: () => void;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  style?: ViewStyle;
  hapticFeedback?: boolean;
}

export const AccessibleCard: React.FC<AccessibleCardProps> = ({
  children,
  title,
  subtitle,
  onPress,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = "button",
  style,
  hapticFeedback = true,
}) => {
  const theme = useTheme();

  const handlePress = () => {
    if (hapticFeedback) {
      HapticHelpers.light();
    }

    if (onPress) {
      onPress();
    }
  };

  const cardContent = (
    <ThemedView
      variant="card"
      elevated={true}
      style={[
        styles.card,
        AccessibilityHelpers.getTouchTargetStyle("medium"),
        style,
      ]}
    >
      {title && (
        <ThemedText variant="h3" style={styles.cardTitle}>
          {title}
        </ThemedText>
      )}
      {subtitle && (
        <ThemedText
          variant="caption"
          color="secondary"
          style={styles.cardSubtitle}
        >
          {subtitle}
        </ThemedText>
      )}
      {children}
    </ThemedView>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        accessibilityRole={accessibilityRole}
        accessibilityLabel={accessibilityLabel || title}
        accessibilityHint={accessibilityHint}
        activeOpacity={0.8}
        style={styles.touchableCard}
      >
        {cardContent}
      </TouchableOpacity>
    );
  }

  return cardContent;
};

// Accessible Loading Indicator
interface AccessibleLoadingProps {
  message?: string;
  size?: "small" | "large";
  style?: ViewStyle;
}

export const AccessibleLoading: React.FC<AccessibleLoadingProps> = ({
  message = "Loading...",
  size = "large",
  style,
}) => {
  const theme = useTheme();

  return (
    <ThemedView
      style={[styles.loadingContainer, style]}
      accessibilityRole="progressbar"
      accessibilityLabel={message}
      accessibilityLiveRegion="polite"
    >
      <ThemedText variant="body" color="secondary">
        {message}
      </ThemedText>
    </ThemedView>
  );
};

// Accessible Alert/Notification
interface AccessibleAlertProps {
  type: "success" | "warning" | "error" | "info";
  title: string;
  message?: string;
  onDismiss?: () => void;
  style?: ViewStyle;
}

export const AccessibleAlert: React.FC<AccessibleAlertProps> = ({
  type,
  title,
  message,
  onDismiss,
  style,
}) => {
  const theme = useTheme();

  const getAlertStyle = (): ViewStyle => {
    const baseStyle = {
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      borderLeftWidth: 4,
    };

    switch (type) {
      case "success":
        return {
          ...baseStyle,
          backgroundColor: theme.colors.successLight,
          borderLeftColor: theme.colors.success,
        };
      case "warning":
        return {
          ...baseStyle,
          backgroundColor: theme.colors.warningLight,
          borderLeftColor: theme.colors.warning,
        };
      case "error":
        return {
          ...baseStyle,
          backgroundColor: theme.colors.errorLight,
          borderLeftColor: theme.colors.error,
        };
      case "info":
        return {
          ...baseStyle,
          backgroundColor: theme.colors.infoLight,
          borderLeftColor: theme.colors.info,
        };
      default:
        return baseStyle;
    }
  };

  return (
    <ThemedView
      style={[getAlertStyle(), style]}
      accessibilityRole="alert"
      accessibilityLiveRegion="assertive"
    >
      <ThemedText variant="bodyMedium" style={styles.alertTitle}>
        {title}
      </ThemedText>
      {message && (
        <ThemedText
          variant="body"
          color="secondary"
          style={styles.alertMessage}
        >
          {message}
        </ThemedText>
      )}
      {onDismiss && (
        <AccessibleButton
          title="Dismiss"
          variant="outline"
          size="small"
          onPress={onDismiss}
          style={styles.dismissButton}
          accessibilityLabel={`Dismiss ${type} alert`}
        />
      )}
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  hint: {
    marginTop: 4,
  },
  error: {
    marginTop: 4,
  },
  card: {
    padding: 16,
  },
  touchableCard: {
    // No additional styles needed
  },
  cardTitle: {
    marginBottom: 4,
  },
  cardSubtitle: {
    marginBottom: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  alertTitle: {
    fontWeight: "600",
    marginBottom: 4,
  },
  alertMessage: {
    marginBottom: 8,
  },
  dismissButton: {
    alignSelf: "flex-end",
    marginTop: 8,
  },
});
