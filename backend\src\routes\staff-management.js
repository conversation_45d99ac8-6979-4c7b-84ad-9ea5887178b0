const express = require("express");
const router = express.Router();
const staffAttributionService = require("../services/staff-attribution-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const bcrypt = require("bcrypt");
const { v4: uuidv4 } = require("uuid");

// Get roles that current user can assign
router.get(
  "/assignable-roles",
  authenticateToken,
  requirePermission("manage_staff"),
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const roleHierarchy = {
        cashier: 1,
        manager: 2,
        company_admin: 3,
        super_admin: 4,
      };

      const userLevel = roleHierarchy[userRole] || 0;
      let assignableRoles = [];

      if (userRole === "super_admin") {
        // Super admin can assign any role
        assignableRoles = [
          "cashier",
          "manager",
          "company_admin",
          "super_admin",
        ];
      } else if (userRole === "company_admin") {
        // Company admin can assign roles below their level
        assignableRoles = ["cashier", "manager"];
      } else {
        // Other users can only assign roles below their level
        assignableRoles = Object.entries(roleHierarchy)
          .filter(([_, level]) => level < userLevel)
          .map(([role, _]) => role);
      }

      res.json({
        success: true,
        data: {
          assignableRoles,
          userRole,
          userLevel,
        },
      });
    } catch (error) {
      console.error("Get assignable roles error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get assignable roles",
      });
    }
  }
);

// Get all staff members with commission rates
router.get("/staff", authenticateToken, async (req, res) => {
  try {
    const result = await staffAttributionService.getAllStaff();

    if (result.success) {
      res.json({
        success: true,
        data: {
          staffMembers: result.staffMembers,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get staff members error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch staff members",
    });
  }
});

// Get specific staff member by ID
router.get("/staff/:staffId", authenticateToken, async (req, res) => {
  try {
    const { staffId } = req.params;
    const result = await staffAttributionService.getStaffById(staffId);

    if (result.success) {
      res.json({
        success: true,
        data: {
          staffMember: result.staffMember,
        },
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get staff member error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch staff member",
    });
  }
});

// Create new staff member (requires manage_staff permission)
router.post(
  "/staff",
  authenticateToken,
  requirePermission("manage_staff"),
  async (req, res) => {
    try {
      const {
        username,
        name,
        email,
        password,
        role,
        commissionRate,
        permissions,
      } = req.body;

      // Validate required fields
      if (!username || !name || !password || !role) {
        return res.status(400).json({
          success: false,
          error: "Username, name, password, and role are required",
        });
      }

      // Validate role
      const validRoles = ["cashier", "manager", "company_admin", "super_admin"];
      if (!validRoles.includes(role)) {
        return res.status(400).json({
          success: false,
          error: "Invalid role. Must be one of: " + validRoles.join(", "),
        });
      }

      // Role hierarchy validation - prevent privilege escalation
      const userRole = req.user.role;
      const roleHierarchy = {
        cashier: 1,
        manager: 2,
        company_admin: 3,
        super_admin: 4,
      };

      const userLevel = roleHierarchy[userRole] || 0;
      const assigningLevel = roleHierarchy[role] || 0;

      // Users can only assign roles at or below their level, but not their own level (except super_admin)
      if (userRole !== "super_admin" && assigningLevel >= userLevel) {
        return res.status(403).json({
          success: false,
          error: `Permission denied: ${userRole} cannot assign '${role}' role. You can only assign roles below your level.`,
        });
      }

      // Validate commission rate
      const commission = parseFloat(commissionRate) || 0;
      if (commission < 0 || commission > 100) {
        return res.status(400).json({
          success: false,
          error: "Commission rate must be between 0 and 100",
        });
      }

      // Validate password strength
      if (password.length < 6) {
        return res.status(400).json({
          success: false,
          error: "Password must be at least 6 characters long",
        });
      }

      const result = await staffAttributionService.createStaff({
        username: username.trim(),
        name: name.trim(),
        email: email ? email.trim().toLowerCase() : null,
        password,
        role,
        commissionRate: commission,
        permissions: permissions || [],
      });

      if (result.success) {
        res.status(201).json({
          success: true,
          data: {
            staffMember: result.staffMember,
            message: "Staff member created successfully",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Create staff member error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create staff member",
      });
    }
  }
);

// Set commission rate for a staff member
router.put(
  "/staff/:staffId/commission",
  authenticateToken,
  requirePermission("manage_staff"),
  async (req, res) => {
    try {
      const { staffId } = req.params;
      const { commissionRate } = req.body;

      // Validate commission rate
      if (
        typeof commissionRate !== "number" ||
        commissionRate < 0 ||
        commissionRate > 100
      ) {
        return res.status(400).json({
          success: false,
          error: "Commission rate must be a number between 0 and 100",
        });
      }

      const result = await staffAttributionService.setStaffCommissionRate(
        staffId,
        commissionRate
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            message: "Commission rate updated successfully",
            metafield: result.metafield,
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Set commission rate error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to set commission rate",
      });
    }
  }
);

// Update staff permissions
router.put(
  "/staff/:staffId/permissions",
  authenticateToken,
  requirePermission("manage_staff"),
  async (req, res) => {
    try {
      const { staffId } = req.params;
      const { permissions } = req.body;

      if (!Array.isArray(permissions)) {
        return res.status(400).json({
          success: false,
          error: "Permissions must be an array",
        });
      }

      const result = await staffAttributionService.updateStaffPermissions(
        staffId,
        permissions
      );

      if (result.success) {
        res.json({
          success: true,
          message: "Staff permissions updated successfully",
          data: result.staffMember,
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Error updating staff permissions:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update staff permissions",
      });
    }
  }
);

// Create order with staff attribution
router.post("/orders/with-staff", authenticateToken, async (req, res) => {
  try {
    const { orderData, staffId } = req.body;

    // Validate required fields
    if (!orderData || !staffId) {
      return res.status(400).json({
        success: false,
        error: "Order data and staff ID are required",
      });
    }

    // Use the current user's ID if no staffId provided or if user doesn't have permission to create orders for others
    const effectiveStaffId = req.user.permissions.includes(
      "create_orders_for_others"
    )
      ? staffId
      : req.user.id;

    const result =
      await staffAttributionService.createOrderWithStaffAttribution(
        orderData,
        effectiveStaffId
      );

    if (result.success) {
      res.json({
        success: true,
        data: {
          order: result.order,
          message: "Order created successfully with staff attribution",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Create order with staff error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to create order",
    });
  }
});

// Create order with dual attribution (staff + sales agent) - NEW REQUIRED APPROACH
router.post(
  "/orders/with-dual-attribution",
  authenticateToken,
  async (req, res) => {
    try {
      const orderData = req.body;
      const {
        staffId,
        salesAgentId,
        paymentMethod,
        paymentTransactionId,
        paymentTimestamp,
        financialStatus,
        locationId,
        locationName,
      } = orderData;

      // Validate required fields
      if (!orderData || !staffId || !salesAgentId) {
        return res.status(400).json({
          success: false,
          error: "Order data, staff ID, and sales agent ID are all required",
        });
      }

      // Add payment information to order data if provided
      if (paymentMethod && paymentTransactionId) {
        orderData.note = orderData.note || "";
        orderData.note += ` | Payment: ${paymentMethod} | Transaction: ${paymentTransactionId}`;

        if (paymentTimestamp) {
          orderData.note += ` | Paid at: ${new Date(
            paymentTimestamp
          ).toLocaleString()}`;
        }

        // Add payment metafields
        orderData.metafields = orderData.metafields || [];
        orderData.metafields.push(
          {
            namespace: "dukalink_payment",
            key: "payment_method",
            value: paymentMethod,
            type: "single_line_text_field",
          },
          {
            namespace: "dukalink_payment",
            key: "transaction_id",
            value: paymentTransactionId,
            type: "single_line_text_field",
          }
        );

        if (paymentTimestamp) {
          orderData.metafields.push({
            namespace: "dukalink_payment",
            key: "payment_timestamp",
            value: paymentTimestamp,
            type: "single_line_text_field",
          });
        }

        // Set financial status if provided
        if (financialStatus) {
          orderData.financialStatus = financialStatus;
        }
      }

      // Add location information if provided
      if (locationId && locationName) {
        orderData.note = orderData.note || "";
        orderData.note += ` | Location: ${locationName}`;

        orderData.metafields = orderData.metafields || [];
        orderData.metafields.push(
          {
            namespace: "dukalink_location",
            key: "location_id",
            value: locationId,
            type: "single_line_text_field",
          },
          {
            namespace: "dukalink_location",
            key: "location_name",
            value: locationName,
            type: "single_line_text_field",
          }
        );
      }

      // Use the current user's ID if no staffId provided or if user doesn't have permission to create orders for others
      const effectiveStaffId = req.user.permissions.includes(
        "create_orders_for_others"
      )
        ? staffId
        : req.user.id;

      const result =
        await staffAttributionService.createOrderWithDualAttribution(
          orderData,
          effectiveStaffId,
          salesAgentId
        );

      if (result.success) {
        res.json({
          success: true,
          data: {
            order: result.order,
            attribution: result.attribution,
            message:
              "Order created successfully with dual attribution (staff + sales agent) using metafields",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Create order with dual attribution error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create order",
      });
    }
  }
);

// Get orders by staff member (for commission tracking)
router.get("/staff/:staffId/orders", authenticateToken, async (req, res) => {
  try {
    const { staffId } = req.params;
    const { limit = 50, status = "any", dateFrom, dateTo } = req.query;

    // Check if user can view other staff's orders
    if (
      staffId !== req.user.id &&
      !req.user.permissions.includes("view_all_orders")
    ) {
      return res.status(403).json({
        success: false,
        error: "Permission denied: Cannot view other staff member orders",
      });
    }

    // Get orders and filter by staff attribution
    const result = await shopifyService.getOrders(limit, status);

    if (result.success) {
      // Filter orders by staff attribution in note_attributes
      const staffOrders = result.orders.filter((order) => {
        const staffAttribute = order.note_attributes?.find(
          (attr) => attr.name === "sales_agent_id" && attr.value === staffId
        );
        return !!staffAttribute;
      });

      // Apply date filtering if provided
      let filteredOrders = staffOrders;
      if (dateFrom || dateTo) {
        filteredOrders = staffOrders.filter((order) => {
          const orderDate = new Date(order.created_at);
          if (dateFrom && orderDate < new Date(dateFrom)) return false;
          if (dateTo && orderDate > new Date(dateTo)) return false;
          return true;
        });
      }

      res.json({
        success: true,
        data: {
          orders: filteredOrders,
          totalCount: filteredOrders.length,
          staffId: staffId,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get staff orders error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch staff orders",
    });
  }
});

// Get staff performance summary
router.get(
  "/staff/:staffId/performance",
  authenticateToken,
  async (req, res) => {
    try {
      const { staffId } = req.params;
      const { dateFrom, dateTo } = req.query;

      // Check permissions
      if (
        staffId !== req.user.id &&
        !req.user.permissions.includes("view_staff_performance")
      ) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot view staff performance",
        });
      }

      // Get staff member info
      const staffResult = await staffAttributionService.getStaffById(staffId);
      if (!staffResult.success) {
        return res.status(404).json({
          success: false,
          error: "Staff member not found",
        });
      }

      // Get staff orders
      const ordersResult = await shopifyService.getOrders(250, "any"); // Get more orders for analysis

      if (ordersResult.success) {
        // Filter orders by staff and date range
        let staffOrders = ordersResult.orders.filter((order) => {
          const staffAttribute = order.note_attributes?.find(
            (attr) => attr.name === "sales_agent_id" && attr.value === staffId
          );
          return !!staffAttribute;
        });

        // Apply date filtering
        if (dateFrom || dateTo) {
          staffOrders = staffOrders.filter((order) => {
            const orderDate = new Date(order.created_at);
            if (dateFrom && orderDate < new Date(dateFrom)) return false;
            if (dateTo && orderDate > new Date(dateTo)) return false;
            return true;
          });
        }

        // Calculate performance metrics
        const totalSales = staffOrders.reduce(
          (sum, order) => sum + parseFloat(order.total_price || 0),
          0
        );

        const totalCommission = staffOrders.reduce((sum, order) => {
          const commissionRateAttr = order.note_attributes?.find(
            (attr) => attr.name === "commission_rate"
          );
          const rate = commissionRateAttr
            ? parseFloat(commissionRateAttr.value)
            : 0;
          return sum + parseFloat(order.total_price || 0) * (rate / 100);
        }, 0);

        const averageOrderValue =
          staffOrders.length > 0 ? totalSales / staffOrders.length : 0;

        res.json({
          success: true,
          data: {
            staffMember: staffResult.staffMember,
            performance: {
              totalSales: totalSales,
              totalCommission: totalCommission,
              orderCount: staffOrders.length,
              averageOrderValue: averageOrderValue,
              commissionRate: staffResult.staffMember.commissionRate,
              dateRange: {
                from: dateFrom,
                to: dateTo,
              },
            },
            orders: staffOrders.slice(0, 10), // Return latest 10 orders for reference
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: ordersResult.error,
        });
      }
    } catch (error) {
      console.error("Get staff performance error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch staff performance",
      });
    }
  }
);

module.exports = router;
