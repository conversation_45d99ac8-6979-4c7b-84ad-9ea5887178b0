const express = require("express");
const router = express.Router();
const salesAgentService = require("../services/sales-agent-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");

// Get all sales agents
router.get("/sales-agents", authenticateToken, async (req, res) => {
  try {
    const { includeInactive = false } = req.query;
    const result = await salesAgentService.getAllSalesAgents(
      includeInactive === "true"
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          salesAgents: result.salesAgents,
          message:
            "Sales agents from custom database (separate from POS staff)",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get sales agents error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch sales agents",
    });
  }
});

// Get specific sales agent by ID
router.get("/sales-agents/:agentId", authenticateToken, async (req, res) => {
  try {
    const { agentId } = req.params;
    const result = await salesAgentService.getSalesAgentById(agentId);

    if (result.success) {
      res.json({
        success: true,
        data: {
          salesAgent: result.salesAgent,
        },
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get sales agent error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch sales agent",
    });
  }
});

// Create new sales agent (requires manage_staff permission)
router.post(
  "/sales-agents",
  authenticateToken,
  requirePermission("manage_staff"),
  async (req, res) => {
    try {
      const agentData = req.body;
      const result = await salesAgentService.createSalesAgent(agentData);

      if (result.success) {
        res.status(201).json({
          success: true,
          data: {
            salesAgent: result.salesAgent,
            message: "Sales agent created successfully",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Create sales agent error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create sales agent",
      });
    }
  }
);

// Update sales agent (requires manage_staff permission)
router.put(
  "/sales-agents/:agentId",
  authenticateToken,
  requirePermission("manage_staff"),
  async (req, res) => {
    try {
      const { agentId } = req.params;
      const updateData = req.body;
      const result = await salesAgentService.updateSalesAgent(
        agentId,
        updateData
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            salesAgent: result.salesAgent,
            message: "Sales agent updated successfully",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Update sales agent error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update sales agent",
      });
    }
  }
);

// Get customers brought by a sales agent
router.get(
  "/sales-agents/:agentId/customers",
  authenticateToken,
  async (req, res) => {
    try {
      const { agentId } = req.params;

      // Check permissions - only managers or the agent themselves can view
      if (
        req.user.id !== agentId &&
        !req.user.permissions.includes("view_staff_performance")
      ) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot view other agent customers",
        });
      }

      const result = await salesAgentService.getAgentCustomers(agentId);

      if (result.success) {
        res.json({
          success: true,
          data: {
            agentInfo: result.agentInfo,
            customers: result.customers,
            customerCount: result.customerCount,
          },
        });
      } else {
        res.status(404).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Get agent customers error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch agent customers",
      });
    }
  }
);

// Link customer to sales agent
router.post(
  "/sales-agents/:agentId/customers/:customerId",
  authenticateToken,
  async (req, res) => {
    try {
      const { agentId, customerId } = req.params;

      // Check permissions - staff can link customers to agents
      if (!req.user.permissions.includes("manage_customers")) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot link customers to agents",
        });
      }

      const result = await salesAgentService.linkCustomerToAgent(
        agentId,
        customerId
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            message: result.message,
            customerCount: result.customerCount,
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Link customer to agent error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to link customer to agent",
      });
    }
  }
);

// Find sales agent by customer ID
router.get(
  "/customers/:customerId/sales-agent",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;
      const result = await salesAgentService.findAgentByCustomer(customerId);

      if (result.success) {
        res.json({
          success: true,
          data: {
            salesAgent: result.salesAgent,
            customerId: customerId,
          },
        });
      } else {
        res.status(404).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Find agent by customer error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to find sales agent for customer",
      });
    }
  }
);

// Get sales agent performance metrics
router.get(
  "/sales-agents/:agentId/performance",
  authenticateToken,
  async (req, res) => {
    try {
      const { agentId } = req.params;
      const { dateFrom, dateTo } = req.query;

      // Check permissions
      if (
        req.user.id !== agentId &&
        !req.user.permissions.includes("view_staff_performance")
      ) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot view agent performance",
        });
      }

      // Get agent info
      const agentResult = await salesAgentService.getSalesAgentById(agentId);
      if (!agentResult.success) {
        return res.status(404).json({
          success: false,
          error: "Sales agent not found",
        });
      }

      const agent = agentResult.salesAgent;

      // TODO: Implement actual performance calculation from orders
      // For now, return basic agent info with placeholder metrics
      const performance = {
        salesAgent: {
          id: agent.id,
          name: agent.name,
          territory: agent.territory,
          region: agent.region,
          commissionRate: agent.commissionRate,
        },
        metrics: {
          totalSales: agent.totalSales,
          totalCommission: agent.totalCommission,
          customerCount: agent.customerCount,
          averageOrderValue:
            agent.customerCount > 0
              ? agent.totalSales / agent.customerCount
              : 0,
          period: {
            from: dateFrom,
            to: dateTo,
          },
        },
        customers: agent.customers,
      };

      res.json({
        success: true,
        data: {
          performance: performance,
          message:
            "Performance metrics (will be calculated from actual orders in production)",
        },
      });
    } catch (error) {
      console.error("Get agent performance error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch agent performance",
      });
    }
  }
);

module.exports = router;
