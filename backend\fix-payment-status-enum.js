/**
 * Fix Payment Status ENUM to include 'authorized'
 * 
 * This script adds 'authorized' to the payment_methods_used.status ENUM
 * to support credit payment authorization workflow.
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixPaymentStatusEnum() {
  let connection;
  
  try {
    console.log('🔧 Connecting to database...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'dukalink_pos',
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database');

    // Check current ENUM values
    console.log('🔍 Checking current ENUM values...');
    const [rows] = await connection.execute(`
      SELECT COLUMN_TYPE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME = 'payment_methods_used' 
      AND COLUMN_NAME = 'status'
    `, [process.env.DB_NAME || 'dukalink_pos']);

    if (rows.length > 0) {
      console.log('Current status ENUM:', rows[0].COLUMN_TYPE);
      
      // Check if 'authorized' is already in the ENUM
      if (rows[0].COLUMN_TYPE.includes('authorized')) {
        console.log('✅ ENUM already contains "authorized" - no changes needed');
        return;
      }
    }

    console.log('🔧 Adding "authorized" to payment_methods_used.status ENUM...');
    
    // Modify the ENUM to include 'authorized'
    await connection.execute(`
      ALTER TABLE payment_methods_used 
      MODIFY COLUMN status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'authorized') 
      DEFAULT 'pending'
    `);

    console.log('✅ Successfully added "authorized" to status ENUM');

    // Verify the change
    const [verifyRows] = await connection.execute(`
      SELECT COLUMN_TYPE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME = 'payment_methods_used' 
      AND COLUMN_NAME = 'status'
    `, [process.env.DB_NAME || 'dukalink_pos']);

    if (verifyRows.length > 0) {
      console.log('✅ Updated status ENUM:', verifyRows[0].COLUMN_TYPE);
    }

    console.log('🎉 Payment status ENUM fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing payment status ENUM:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the fix
if (require.main === module) {
  fixPaymentStatusEnum()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fixPaymentStatusEnum };
