/**
 * Unified Receipt Manager
 *
 * Central hub for all receipt generation and printing operations.
 * Replaces fragmented receipt services with a single, standardized interface.
 */

import {
  StandardizedReceiptService,
  StandardizedReceiptData,
} from "./StandardizedReceiptService";
import { formatCurrency } from "@/src/utils/currencyUtils";

export interface UnifiedReceiptOptions {
  // Output format
  format: "html" | "thermal" | "whatsapp" | "email" | "text";

  // Thermal printer options
  thermalWidth?: number; // 32 or 48 characters

  // Print options
  autoPrint?: boolean;
  printerType?: "thermal" | "t220md" | "web";

  // Sharing options
  shareVia?: "whatsapp" | "email" | "sms";
  recipientPhone?: string;
  recipientEmail?: string;
}

export interface UnifiedReceiptResult {
  success: boolean;
  content?: string;
  error?: string;
  printed?: boolean;
  shared?: boolean;
  receiptData?: StandardizedReceiptData;
}

export class UnifiedReceiptManager {
  /**
   * Generate receipt from order data using standardized format
   */
  static async generateReceipt(
    orderData: any,
    options: UnifiedReceiptOptions,
    paymentData?: any
  ): Promise<UnifiedReceiptResult> {
    try {
      // Step 1: Convert to standardized receipt data
      const receiptData =
        await StandardizedReceiptService.generateStandardizedReceipt(
          orderData,
          paymentData
        );

      // Step 2: Generate content based on format
      let content: string;

      switch (options.format) {
        case "html":
          content = StandardizedReceiptService.generateHTMLReceipt(receiptData);
          break;

        case "thermal":
          content = StandardizedReceiptService.generateThermalReceipt(
            receiptData,
            options.thermalWidth || 32
          );
          break;

        case "whatsapp":
          content =
            StandardizedReceiptService.generateWhatsAppReceipt(receiptData);
          break;

        case "email":
          content = this.generateEmailReceipt(receiptData);
          break;

        case "text":
          content = StandardizedReceiptService.generateThermalReceipt(
            receiptData,
            80
          );
          break;

        default:
          throw new Error(`Unsupported format: ${options.format}`);
      }

      // Step 3: Handle printing if requested
      let printed = false;
      if (options.autoPrint) {
        printed = await this.handlePrinting(receiptData, content, options);
      }

      // Step 4: Handle sharing if requested
      let shared = false;
      if (options.shareVia) {
        shared = await this.handleSharing(content, options);
      }

      return {
        success: true,
        content,
        printed,
        shared,
        receiptData,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Generate receipt by order ID (for reprinting)
   */
  static async generateReceiptById(
    orderId: string,
    options: UnifiedReceiptOptions
  ): Promise<UnifiedReceiptResult> {
    try {
      // Fetch order data from API
      const orderData = await this.fetchOrderData(orderId);

      if (!orderData) {
        throw new Error(`Order not found: ${orderId}`);
      }

      // Try to fetch payment data if available
      let paymentData;
      if (orderData.paymentTransactionId) {
        try {
          const { enhancedPaymentService } = await import(
            "./enhanced-payment-service"
          );
          const statusResponse =
            await enhancedPaymentService.getTransactionStatus(
              orderData.paymentTransactionId
            );

          if (statusResponse.success) {
            paymentData = statusResponse.data;
          }
        } catch (error) {
          console.warn("Failed to fetch payment data for receipt:", error);
        }
      }

      return await this.generateReceipt(orderData, options, paymentData);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Print receipt using appropriate printer service
   */
  static async printReceipt(
    receiptData: StandardizedReceiptData,
    printerType: "thermal" | "t220md" | "web" = "thermal"
  ): Promise<boolean> {
    try {
      switch (printerType) {
        case "thermal":
          return await this.printThermalReceipt(receiptData);

        case "t220md":
          return await this.printT220MDReceipt(receiptData);

        case "web":
          return await this.printWebReceipt(receiptData);

        default:
          throw new Error(`Unsupported printer type: ${printerType}`);
      }
    } catch (error) {
      console.error("Print failed:", error);
      return false;
    }
  }

  /**
   * Share receipt via specified method
   */
  static async shareReceipt(
    content: string,
    method: "whatsapp" | "email" | "sms",
    recipient?: string
  ): Promise<boolean> {
    try {
      switch (method) {
        case "whatsapp":
          return await this.shareViaWhatsApp(content, recipient);

        case "email":
          return await this.shareViaEmail(content, recipient);

        case "sms":
          return await this.shareViaSMS(content, recipient);

        default:
          throw new Error(`Unsupported sharing method: ${method}`);
      }
    } catch (error) {
      console.error("Share failed:", error);
      return false;
    }
  }

  /**
   * Get all available receipt formats for an order
   */
  static async getAllReceiptFormats(
    orderData: any,
    paymentData?: any
  ): Promise<{ [format: string]: string }> {
    const receiptData =
      await StandardizedReceiptService.generateStandardizedReceipt(
        orderData,
        paymentData
      );

    return {
      html: StandardizedReceiptService.generateHTMLReceipt(receiptData),
      thermal32: StandardizedReceiptService.generateThermalReceipt(
        receiptData,
        32
      ),
      thermal48: StandardizedReceiptService.generateThermalReceipt(
        receiptData,
        48
      ),
      whatsapp: StandardizedReceiptService.generateWhatsAppReceipt(receiptData),
      email: this.generateEmailReceipt(receiptData),
      text: StandardizedReceiptService.generateThermalReceipt(receiptData, 80),
    };
  }

  // Private helper methods

  private static async fetchOrderData(orderId: string): Promise<any> {
    try {
      const { getAPIClient } = await import("./api/dukalink-client");
      const apiClient = getAPIClient();
      const response = await apiClient.getOrder("default", orderId);

      if (response.success && response.data) {
        return response.data;
      }

      return null;
    } catch (error) {
      console.error("Failed to fetch order data:", error);
      return null;
    }
  }

  private static async handlePrinting(
    receiptData: StandardizedReceiptData,
    content: string,
    options: UnifiedReceiptOptions
  ): Promise<boolean> {
    if (!options.printerType) {
      return false;
    }

    return await this.printReceipt(receiptData, options.printerType);
  }

  private static async handleSharing(
    content: string,
    options: UnifiedReceiptOptions
  ): Promise<boolean> {
    if (!options.shareVia) {
      return false;
    }

    const recipient =
      options.shareVia === "email"
        ? options.recipientEmail
        : options.recipientPhone;

    return await this.shareReceipt(content, options.shareVia, recipient);
  }

  private static async printThermalReceipt(
    receiptData: StandardizedReceiptData
  ): Promise<boolean> {
    try {
      const { default: EnhancedThermalPrintService } = await import(
        "./EnhancedThermalPrintService"
      );

      // Convert standardized data back to legacy format for thermal printing
      const legacyReceiptData = this.convertToLegacyFormat(receiptData);

      const result = await EnhancedThermalPrintService.printThermalReceipt(
        legacyReceiptData
      );
      return result.success;
    } catch (error) {
      console.error("Thermal print failed:", error);
      return false;
    }
  }

  private static async printT220MDReceipt(
    receiptData: StandardizedReceiptData
  ): Promise<boolean> {
    try {
      const { T220MDPrintService } = await import("./T220MDPrintService");

      // Convert standardized data back to legacy format
      const legacyReceiptData = this.convertToLegacyFormat(receiptData);

      const result = await T220MDPrintService.printReceipt(legacyReceiptData);
      return result.success;
    } catch (error) {
      console.error("T220MD print failed:", error);
      return false;
    }
  }

  private static async printWebReceipt(
    receiptData: StandardizedReceiptData
  ): Promise<boolean> {
    try {
      const htmlContent =
        StandardizedReceiptService.generateHTMLReceipt(receiptData);

      // Use hidden iframe for printing (better UX - no new window/tab)
      return await this.printHTMLWithIframe(htmlContent);
    } catch (error) {
      console.error("Web print failed:", error);
      return false;
    }
  }

  /**
   * Print HTML content using hidden iframe (same-page printing)
   */
  private static async printHTMLWithIframe(html: string): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        // Create a hidden iframe for printing
        const printFrame = document.createElement("iframe");
        printFrame.style.position = "absolute";
        printFrame.style.top = "-9999px";
        printFrame.style.left = "-9999px";
        printFrame.style.width = "0px";
        printFrame.style.height = "0px";
        printFrame.style.border = "none";

        document.body.appendChild(printFrame);

        const printDocument =
          printFrame.contentDocument || printFrame.contentWindow?.document;
        if (!printDocument) {
          throw new Error("Could not access print document");
        }

        printDocument.open();
        printDocument.write(html);
        printDocument.close();

        // Wait for content to load then print
        printFrame.onload = () => {
          try {
            // Use a small delay to ensure content is fully loaded
            setTimeout(() => {
              try {
                printFrame.contentWindow?.focus();
                printFrame.contentWindow?.print();

                // Clean up after print dialog
                setTimeout(() => {
                  if (document.body.contains(printFrame)) {
                    document.body.removeChild(printFrame);
                  }
                }, 1000);

                resolve(true);
              } catch (printError) {
                console.error("Print dialog failed:", printError);
                if (document.body.contains(printFrame)) {
                  document.body.removeChild(printFrame);
                }
                resolve(false);
              }
            }, 100); // Small delay to ensure iframe is ready
          } catch (error) {
            console.error("Print setup failed:", error);
            if (document.body.contains(printFrame)) {
              document.body.removeChild(printFrame);
            }
            resolve(false);
          }
        };

        // Fallback timeout
        setTimeout(() => {
          if (document.body.contains(printFrame)) {
            document.body.removeChild(printFrame);
            console.warn("Print timeout - cleaning up iframe");
            resolve(false);
          }
        }, 5000);
      } catch (error) {
        console.error("Print setup failed:", error);
        resolve(false);
      }
    });
  }

  private static async shareViaWhatsApp(
    content: string,
    phoneNumber?: string
  ): Promise<boolean> {
    try {
      const encodedText = encodeURIComponent(content);
      const whatsappUrl = phoneNumber
        ? `https://wa.me/${phoneNumber}?text=${encodedText}`
        : `https://wa.me/?text=${encodedText}`;

      window.open(whatsappUrl, "_blank");
      return true;
    } catch (error) {
      console.error("WhatsApp share failed:", error);
      return false;
    }
  }

  private static async shareViaEmail(
    content: string,
    email?: string
  ): Promise<boolean> {
    try {
      const subject = "Your Receipt - Treasured Scents";
      const body = encodeURIComponent(content);
      const mailtoUrl = `mailto:${email || ""}?subject=${subject}&body=${body}`;

      window.open(mailtoUrl);
      return true;
    } catch (error) {
      console.error("Email share failed:", error);
      return false;
    }
  }

  private static async shareViaSMS(
    content: string,
    phoneNumber?: string
  ): Promise<boolean> {
    try {
      const encodedText = encodeURIComponent(content);
      const smsUrl = `sms:${phoneNumber || ""}?body=${encodedText}`;

      window.open(smsUrl);
      return true;
    } catch (error) {
      console.error("SMS share failed:", error);
      return false;
    }
  }

  private static generateEmailReceipt(
    receiptData: StandardizedReceiptData
  ): string {
    // Use HTML format for email with enhanced styling
    return StandardizedReceiptService.generateHTMLReceipt(receiptData);
  }

  private static convertToLegacyFormat(
    receiptData: StandardizedReceiptData
  ): any {
    // Convert standardized format back to legacy format for backward compatibility
    return {
      orderNumber: receiptData.receiptNumber,
      orderDate: `${receiptData.date} ${receiptData.time}`,
      customer: {
        name: receiptData.customer.name,
        email: receiptData.customer.email,
        phone: receiptData.customer.mobile,
      },
      staff: receiptData.staff,
      salesAgent: receiptData.salesAgent,
      items: receiptData.items.map((item) => ({
        id: item.number.toString(),
        title: item.name,
        quantity: item.quantity,
        price: formatCurrency(item.unitPrice),
        sku: item.sku,
        variantTitle: item.variant,
      })),
      subtotal: receiptData.totals.subtotal,
      tax: receiptData.totals.tax,
      total: receiptData.totals.grandTotal,
      paymentMethod: receiptData.payment.methods[0]?.method || "Cash",
      paymentDetails: {
        transactionId:
          receiptData.payment.methods[0]?.transactionCode ||
          receiptData.receiptNumber,
      },
      paymentBreakdown: receiptData.payment.isSplitPayment
        ? {
            isSplitPayment: true,
            totalAmount: receiptData.totals.grandTotal,
            paymentMethods: receiptData.payment.methods,
            transactionId: receiptData.receiptNumber,
            paymentStatus: "completed",
            completedAmount: receiptData.payment.totalPaid,
          }
        : undefined,
      // CRITICAL FIX: Include loyalty data for thermal printing
      loyaltyPoints: receiptData.loyalty
        ? {
            balance: receiptData.loyalty.totalPoints,
            earned: receiptData.loyalty.pointsEarned,
            membershipId: `TS${
              receiptData.customer.name?.slice(-8) || "GUEST"
            }`,
          }
        : undefined,
      store: receiptData.store,
    };
  }
}
