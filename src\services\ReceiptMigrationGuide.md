# Receipt System Migration Guide

## Overview

This guide explains how to migrate from the fragmented receipt system to the new **Standardized Receipt System** that follows the official Treasured Scents receipt format.

## Key Benefits

### ✅ Before vs After

**Before (Fragmented):**
- Multiple receipt generators scattered across codebase
- Inconsistent formatting between platforms
- Different data structures for each service
- Hard to maintain and update
- No standardized branding

**After (Standardized):**
- Single source of truth for all receipts
- Consistent formatting across all platforms
- Unified data structure
- Easy to maintain and update
- Official Treasured Scents branding

## New Architecture

### Core Services

1. **StandardizedReceiptService** - Core receipt generation logic
2. **UnifiedReceiptManager** - Central hub for all receipt operations
3. **Legacy adapters** - Backward compatibility with existing services

### Supported Formats

- **HTML** - Web printing and email
- **Thermal** - Bluetooth thermal printers (32/48 char width)
- **WhatsApp** - Text with emojis for mobile sharing
- **Email** - Enhanced HTML with professional styling
- **Text** - Plain text for SMS and other uses

## Migration Steps

### Step 1: Replace Direct Receipt Generation

**Old Way:**
```typescript
// Multiple different services
import { ReceiptGenerator } from './ReceiptGenerator';
import { ThermalPrintService } from './ThermalPrintService';
import { CrossPlatformPrintService } from './CrossPlatformPrintService';

// Different data formats for each
const htmlReceipt = ReceiptGenerator.generateHTMLReceipt(orderData);
const thermalReceipt = ThermalPrintService.generateReceipt(thermalData);
```

**New Way:**
```typescript
// Single unified service
import { UnifiedReceiptManager } from './UnifiedReceiptManager';

// Single data format, multiple outputs
const result = await UnifiedReceiptManager.generateReceipt(orderData, {
  format: 'html', // or 'thermal', 'whatsapp', 'email'
  autoPrint: true,
  printerType: 'thermal'
});
```

### Step 2: Update Receipt Components

**Old Component Pattern:**
```typescript
// Multiple imports and complex logic
import { ReceiptGenerator } from './services/ReceiptGenerator';
import { ThermalPrintService } from './services/ThermalPrintService';

const handlePrint = async () => {
  const receiptData = transformOrderData(order);
  const htmlContent = ReceiptGenerator.generateHTMLReceipt(receiptData);
  // Complex printing logic...
};
```

**New Component Pattern:**
```typescript
// Single import, simple usage
import { UnifiedReceiptManager } from './services/UnifiedReceiptManager';

const handlePrint = async () => {
  const result = await UnifiedReceiptManager.generateReceipt(order, {
    format: 'html',
    autoPrint: true,
    printerType: 'web'
  });
  
  if (!result.success) {
    console.error('Print failed:', result.error);
  }
};
```

### Step 3: Update Sharing Functions

**Old Sharing:**
```typescript
// Manual WhatsApp formatting
const formatForWhatsApp = (order) => {
  return `Receipt: ${order.number}\nTotal: ${order.total}...`;
};

const shareViaWhatsApp = (text, phone) => {
  const url = `https://wa.me/${phone}?text=${encodeURIComponent(text)}`;
  window.open(url);
};
```

**New Sharing:**
```typescript
// Automatic formatting and sharing
const result = await UnifiedReceiptManager.generateReceipt(order, {
  format: 'whatsapp',
  shareVia: 'whatsapp',
  recipientPhone: customer.phone
});
```

## Component Updates

### 1. Checkout Component

**File:** `src/app/checkout/page.tsx`

**Update the print handler:**
```typescript
const handlePrintReceipt = async (orderId: string) => {
  const result = await UnifiedReceiptManager.generateReceiptById(orderId, {
    format: 'thermal',
    autoPrint: true,
    printerType: 'thermal'
  });
  
  if (!result.success) {
    // Fallback to web printing
    await UnifiedReceiptManager.generateReceiptById(orderId, {
      format: 'html',
      autoPrint: true,
      printerType: 'web'
    });
  }
};
```

### 2. Receipt History Component

**File:** `src/components/ReceiptHistory.tsx`

**Update the reprint functionality:**
```typescript
const handleReprint = async (orderId: string, format: string) => {
  const result = await UnifiedReceiptManager.generateReceiptById(orderId, {
    format: format as any,
    autoPrint: format === 'thermal',
    printerType: format === 'thermal' ? 'thermal' : 'web'
  });
  
  if (result.success && result.content) {
    // Show preview or handle success
    setReceiptContent(result.content);
  }
};
```

### 3. WhatsApp Sharing

**Update sharing components:**
```typescript
const handleWhatsAppShare = async (orderId: string, phoneNumber: string) => {
  const result = await UnifiedReceiptManager.generateReceiptById(orderId, {
    format: 'whatsapp',
    shareVia: 'whatsapp',
    recipientPhone: phoneNumber
  });
  
  return result.shared;
};
```

## Data Format Standardization

### New Standardized Format

The new system uses a single, comprehensive data structure:

```typescript
interface StandardizedReceiptData {
  receiptNumber: string;
  date: string;
  time: string;
  store: StoreInfo;
  staff: StaffInfo;
  customer: CustomerInfo;
  loyalty?: LoyaltyInfo;
  items: StandardizedReceiptItem[];
  totals: TotalsInfo;
  payment: PaymentInfo;
  salesAgent?: SalesAgentInfo;
}
```

### Automatic Data Conversion

The system automatically converts from various input formats:
- Shopify order data
- POS order data
- Legacy receipt data
- Enhanced payment data

## Testing the Migration

### 1. Test All Receipt Formats

```typescript
// Generate all formats for testing
const formats = await UnifiedReceiptManager.getAllReceiptFormats(orderData);

console.log('HTML:', formats.html);
console.log('Thermal 32:', formats.thermal32);
console.log('WhatsApp:', formats.whatsapp);
```

### 2. Test Printing

```typescript
// Test thermal printing
const thermalResult = await UnifiedReceiptManager.generateReceipt(orderData, {
  format: 'thermal',
  autoPrint: true,
  printerType: 'thermal'
});

// Test web printing
const webResult = await UnifiedReceiptManager.generateReceipt(orderData, {
  format: 'html',
  autoPrint: true,
  printerType: 'web'
});
```

### 3. Test Sharing

```typescript
// Test WhatsApp sharing
const whatsappResult = await UnifiedReceiptManager.generateReceipt(orderData, {
  format: 'whatsapp',
  shareVia: 'whatsapp',
  recipientPhone: '+254700000000'
});
```

## Backward Compatibility

The new system maintains backward compatibility with existing services:

- **ThermalPrintService** - Still works, but internally uses standardized format
- **CrossPlatformPrintService** - Redirects to UnifiedReceiptManager
- **ReceiptGenerator** - Legacy methods still available

## Performance Benefits

- **Reduced Bundle Size** - Single service instead of multiple
- **Faster Loading** - Optimized receipt generation
- **Better Caching** - Standardized data structures
- **Improved Memory Usage** - Unified data processing

## Maintenance Benefits

- **Single Source of Truth** - All receipt logic in one place
- **Easy Updates** - Change format once, applies everywhere
- **Consistent Branding** - Official Treasured Scents format
- **Better Testing** - Centralized test coverage

## Next Steps

1. **Update checkout component** to use UnifiedReceiptManager
2. **Update receipt history** to use new format
3. **Update sharing components** to use new sharing methods
4. **Test all receipt formats** with real order data
5. **Update thermal printer integration** to use standardized format
6. **Remove old receipt services** after migration is complete

## Support

For any issues during migration, check:
1. Console logs for detailed error messages
2. Receipt data format compatibility
3. Printer connection status
4. Network connectivity for sharing features
