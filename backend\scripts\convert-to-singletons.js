#!/usr/bin/env node

/**
 * Convert Services to Singletons Script
 * 
 * This script automatically converts all services to singleton patterns
 * to prevent multiple instances from being created.
 */

const fs = require('fs');
const path = require('path');

// Services to convert to singletons
const servicesToConvert = [
  'backend/src/services/ticket-management-service.js',
  'backend/src/services/staff-service-mysql.js',
  'backend/src/services/multi-user-session-service.js',
  'backend/src/services/terminal-management-service.js',
  'backend/src/services/terminal-management-service-mysql.js',
  'backend/src/services/sales-agent-service.js',
  'backend/src/services/sales-agent-service-mysql.js',
  'backend/src/services/commission-service-mysql.js',
  'backend/src/services/commission-discount-service.js',
  'backend/src/services/fulfillment-service.js',
];

function convertToSingleton(filePath) {
  console.log(`\n🔧 Converting ${filePath} to singleton...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Extract class name from file
  const classMatch = content.match(/class\s+(\w+)\s*{/);
  if (!classMatch) {
    console.log(`⚠️ No class found in ${filePath}`);
    return;
  }
  
  const className = classMatch[1];
  console.log(`  📝 Found class: ${className}`);
  
  // 1. Add singleton pattern to constructor
  const constructorRegex = new RegExp(`(class\\s+${className}\\s*{\\s*constructor\\s*\\([^)]*\\)\\s*{)`, 's');
  if (constructorRegex.test(content)) {
    content = content.replace(constructorRegex, (match, constructorStart) => {
      return `${constructorStart}
    if (${className}.instance) {
      return ${className}.instance;
    }
`;
    });
    
    // Add instance assignment at end of constructor
    const constructorEndRegex = new RegExp(`(constructor\\s*\\([^)]*\\)\\s*{[^}]*)(\\s*})`);
    content = content.replace(constructorEndRegex, (match, constructorBody, closingBrace) => {
      if (!constructorBody.includes(`${className}.instance = this`)) {
        return `${constructorBody}
    
    ${className}.instance = this;${closingBrace}`;
      }
      return match;
    });
    
    modified = true;
    console.log('  ✅ Added singleton pattern to constructor');
  }
  
  // 2. Add getInstance static method
  if (!content.includes(`static getInstance()`)) {
    const classEndRegex = new RegExp(`(class\\s+${className}\\s*{[\\s\\S]*?)(\\s*}\\s*(?:module\\.exports|$))`, 'm');
    content = content.replace(classEndRegex, (match, classBody, ending) => {
      return `${classBody}
  
  static getInstance() {
    if (!${className}.instance) {
      ${className}.instance = new ${className}();
    }
    return ${className}.instance;
  }
${ending}`;
    });
    modified = true;
    console.log('  ✅ Added getInstance static method');
  }
  
  // 3. Update module.exports to include singleton instance
  const moduleExportsRegex = /module\.exports\s*=\s*(\w+);?$/m;
  if (moduleExportsRegex.test(content)) {
    content = content.replace(moduleExportsRegex, (match, exportedClass) => {
      const camelCaseName = exportedClass.charAt(0).toLowerCase() + exportedClass.slice(1);
      return `// Export singleton instance
const ${camelCaseName} = ${exportedClass}.getInstance();

module.exports = ${exportedClass};
module.exports.${camelCaseName} = ${camelCaseName};`;
    });
    modified = true;
    console.log('  ✅ Updated module.exports with singleton instance');
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Successfully converted ${filePath} to singleton`);
  } else {
    console.log(`  ℹ️ No changes needed for ${filePath}`);
  }
}

function main() {
  console.log('🚀 Starting singleton conversion...\n');
  
  servicesToConvert.forEach(convertToSingleton);
  
  console.log('\n✅ Singleton conversion completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Update service imports to use singleton instances');
  console.log('2. Test all services to ensure they work correctly');
  console.log('3. Restart the server to use singleton instances');
}

if (require.main === module) {
  main();
}

module.exports = { convertToSingleton };
