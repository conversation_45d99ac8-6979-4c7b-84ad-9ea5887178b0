#!/usr/bin/env node

/**
 * API Environment Switcher
 * Quickly switch between local, development, and production API configurations
 * Now supports environment variables for cleaner configuration management
 */

const fs = require("fs");
const path = require("path");

const API_CONFIG_PATH = path.join(__dirname, "../src/constants/Api.ts");
const ENV_FILE_PATH = path.join(__dirname, "../.env.local");

const ENVIRONMENTS = {
  local: {
    name: "Local Development",
    envValue: "local",
    url: "http://localhost:3020/api (web) / http://*************:3020/api (mobile)",
    description: "Local backend server for development",
  },
  development: {
    name: "Development Online",
    envValue: "development",
    url: "https://treasuredposdev.dukalink.com/api",
    description: "Online development server",
  },
  production: {
    name: "Production",
    envValue: "production",
    url: "https://shopify.dukalink.com/api",
    description: "Live production API",
  },
};

function getCurrentEnvironment() {
  try {
    // First check .env.local file
    if (fs.existsSync(ENV_FILE_PATH)) {
      const envContent = fs.readFileSync(ENV_FILE_PATH, "utf8");
      const envMatch = envContent.match(/EXPO_PUBLIC_API_ENV=(\w+)/);
      if (envMatch) {
        return envMatch[1];
      }
    }

    // Fallback to checking the API config file for forced environment
    const content = fs.readFileSync(API_CONFIG_PATH, "utf8");
    const forceMatch = content.match(
      /const FORCE_API_ENV: ApiEnvironment \| null = ['"](\w+)['"]/
    );
    if (forceMatch) {
      return forceMatch[1];
    }

    // Default fallback
    return "local";
  } catch (error) {
    console.error("❌ Error reading configuration:", error.message);
    process.exit(1);
  }
}

function switchEnvironment(targetEnv) {
  try {
    if (!ENVIRONMENTS[targetEnv]) {
      console.error(`❌ Invalid environment: ${targetEnv}`);
      console.log(
        "Available environments:",
        Object.keys(ENVIRONMENTS).join(", ")
      );
      process.exit(1);
    }

    // Create or update .env.local file
    const envContent = `# Auto-generated by switch-api-env script
# Last updated: ${new Date().toISOString()}

# API Environment Selection
EXPO_PUBLIC_API_ENV=${targetEnv}

# Debug Settings
EXPO_PUBLIC_DEBUG_API=true
EXPO_PUBLIC_LOG_REQUESTS=true

# App Configuration
EXPO_PUBLIC_APP_NAME=Dukalink POS
EXPO_PUBLIC_APP_VERSION=1.0.0

# Feature Flags
EXPO_PUBLIC_ENABLE_LOYALTY=true
EXPO_PUBLIC_ENABLE_THERMAL_PRINTING=true
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=false
`;

    fs.writeFileSync(ENV_FILE_PATH, envContent);

    console.log(
      `✅ Successfully switched to ${ENVIRONMENTS[targetEnv].name} environment`
    );
    console.log(`📍 API URL: ${ENVIRONMENTS[targetEnv].url}`);
    console.log(`📝 Description: ${ENVIRONMENTS[targetEnv].description}`);
    console.log(
      "\n🔄 Please restart your development server for changes to take effect:"
    );
    console.log("   npx expo start --clear");
  } catch (error) {
    console.error("❌ Error switching environment:", error.message);
    process.exit(1);
  }
}

function showStatus() {
  const currentEnv = getCurrentEnvironment();

  console.log("🔧 API Environment Configuration\n");
  console.log(
    `📍 Current Environment: ${ENVIRONMENTS[currentEnv]?.name || "Unknown"}`
  );
  console.log(`🌐 API URL: ${ENVIRONMENTS[currentEnv]?.url || "Unknown"}`);
  console.log(
    `📝 Description: ${ENVIRONMENTS[currentEnv]?.description || "Unknown"}\n`
  );

  console.log("Available Environments:");
  Object.entries(ENVIRONMENTS).forEach(([key, env]) => {
    const indicator = key === currentEnv ? "👉" : "  ";
    console.log(`${indicator} ${env.name}: ${env.url}`);
  });
}

function showHelp() {
  console.log("🔧 API Environment Switcher\n");
  console.log("Usage:");
  console.log("  node scripts/switch-api-env.js [command]\n");
  console.log("Commands:");
  console.log("  status      Show current environment configuration");
  console.log("  local       Switch to local development API");
  console.log("  development Switch to development online API");
  console.log("  production  Switch to production API");
  console.log("  dev         Alias for development");
  console.log("  prod        Alias for production");
  console.log("  help        Show this help message\n");
  console.log("Examples:");
  console.log("  node scripts/switch-api-env.js status");
  console.log("  node scripts/switch-api-env.js local");
  console.log("  node scripts/switch-api-env.js development");
  console.log("  node scripts/switch-api-env.js production");
}

// Main execution
const command = process.argv[2];

switch (command) {
  case "status":
    showStatus();
    break;

  case "production":
  case "prod":
    switchEnvironment("production");
    break;

  case "development":
  case "dev":
    switchEnvironment("development");
    break;

  case "help":
  case "--help":
  case "-h":
    showHelp();
    break;

  default:
    if (command) {
      console.error(`❌ Unknown command: ${command}\n`);
    }
    showHelp();
    process.exit(1);
}
