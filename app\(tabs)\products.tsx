import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { TicketSwitcher } from "@/components/ui/TicketSwitcher";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useCurrency } from "@/src/hooks/useCurrency";
import { useUnifiedCart } from "@/src/hooks/useUnifiedCart";
import { useAppDispatch, useAppSelector } from "@/src/store";
import {
  fetchProducts,
  searchProducts,
  setSearchQuery,
  clearProducts,
} from "@/src/store/slices/productSlice";
import { Product, ProductVariant } from "@/src/types/shopify";
import { useFocusEffect, useRouter } from "expo-router";
import React, { useCallback, useEffect, useState, useRef } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Platform,
} from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

import { ErrorModal } from "@/components/ui/ErrorModal";

// Create styles function that uses theme
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      // Add layout stability constraints
      minHeight: 0,
      maxHeight: "100%",
      overflow: "hidden",
    },
    // Ticket section (matches image - with margins)
    ticketContainer: {
      marginHorizontal: theme.spacing.md,
      marginTop: theme.spacing.sm,
      marginBottom: theme.spacing.md,
      padding: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
    },
    ticketSummary: {
      alignItems: "center",
    },
    ticketTotalLabel: {
      fontSize: theme.typography.bodyMedium.fontSize,
      color: "white",
      marginBottom: theme.spacing.xs,
      fontWeight: "600",
    },
    ticketTotalAmount: {
      fontSize: theme.typography.h1.fontSize,
      fontWeight: "bold",
      color: "white",
      marginBottom: theme.spacing.sm,
    },
    ticketStatusContainer: {
      backgroundColor: "rgba(255, 255, 255, 0.2)",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    ticketStatus: {
      fontSize: theme.typography.body.fontSize,
      color: "white",
      fontWeight: "500",
    },
    // Search section
    searchSection: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      paddingBottom: theme.spacing.md,
      gap: theme.spacing.sm,
    },
    searchContainer: {
      flex: 1,
      position: "relative",
    },
    searchInput: {
      height: 48,
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
      paddingHorizontal: 40,
      paddingVertical: theme.spacing.sm,
      fontSize: theme.typography.body.fontSize,
    },
    searchIcon: {
      position: "absolute",
      left: theme.spacing.md,
      top: "50%",
      transform: [{ translateY: -10 }],
    },
    scannerButton: {
      width: 48,
      height: 48,
      borderRadius: theme.borderRadius.sm,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
    },
    storeInfo: {
      padding: theme.spacing.md,
      marginHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
    },
    storeTitle: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
      marginBottom: 4,
    },
    storeSubtitle: {
      fontSize: theme.typography.caption.fontSize,
      opacity: 0.7,
    },
    productsList: {
      flex: 1,
      // Prevent list expansion beyond container
      minHeight: 0,
      maxHeight: "100%",
    },
    listContent: {
      paddingBottom: 100, // Extra padding for compact cart button
    },
    // Card wrapper (matches image layout)
    cardTouchable: {
      marginHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    productCard: {
      padding: theme.spacing.md,
    },
    // Product layout: left content, right content
    productContent: {
      flexDirection: "row",
      alignItems: "flex-start",
      justifyContent: "space-between",
    },
    // Left side content
    productLeftContent: {
      flex: 1,
      paddingRight: theme.spacing.md,
    },
    productTitle: {
      fontSize: theme.typography.h3.fontSize,
      fontWeight: "600",
      marginBottom: theme.spacing.xs,
      lineHeight: 22,
    },
    // Category with background (like "General" in the image)
    productCategoryContainer: {
      alignSelf: "flex-start",
      backgroundColor: "rgba(128, 128, 128, 0.2)",
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.xs,
      marginBottom: theme.spacing.xs,
    },
    productCategory: {
      fontSize: theme.typography.caption.fontSize,
      fontWeight: "500",
    },
    // SKU
    productSku: {
      fontSize: theme.typography.caption.fontSize,
      opacity: 0.8,
    },
    // Right side content
    productRightContent: {
      alignItems: "flex-end",
      justifyContent: "space-between",
      minHeight: 80,
    },
    // Price
    productPrice: {
      fontSize: theme.typography.h3.fontSize,
      fontWeight: "700",
      marginBottom: theme.spacing.sm,
    },
    // Add to Cart container (smaller size)
    addToCartContainer: {
      marginBottom: theme.spacing.xs,
    },
    addToCartButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
      minWidth: 80,
    },
    // Stock text (bottom right)
    stockText: {
      fontSize: theme.typography.caption.fontSize,
      fontWeight: "500",
      textAlign: "right",
    },
    disabledButton: {
      opacity: 0.5,
    },
    // Cart controls container (when item is in cart)
    cartControlsContainer: {
      alignItems: "flex-end",
    },
    // Quantity controls (horizontal layout with circular buttons)
    quantityControls: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: theme.spacing.xs,
    },
    // Circular buttons for quantity controls
    circularButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      alignItems: "center",
      justifyContent: "center",
    },
    minusButton: {
      backgroundColor: "#FF3B30", // Red color for minus
    },
    plusButton: {
      backgroundColor: "#34C759", // Green color for plus
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    loadingText: {
      fontSize: theme.typography.body.fontSize,
      marginTop: theme.spacing.md,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    emptyTitle: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    emptySubtitle: {
      fontSize: theme.typography.body.fontSize,
      textAlign: "center",
    },
    loadMoreContainer: {
      padding: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
    },
    loadMoreText: {
      fontSize: theme.typography.caption.fontSize,
      marginTop: theme.spacing.sm,
    },
    // New dynamic header styles
    headerContainer: {
      padding: theme.spacing.md,
      marginHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
    },
    normalHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    storeInfoSection: {
      flex: 1,
    },
    headerActions: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.sm,
    },
    searchIconButton: {
      width: 40,
      height: 40,
      borderRadius: theme.borderRadius.sm,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    cartButton: {
      width: 40,
      height: 40,
      borderRadius: theme.borderRadius.sm,
      alignItems: "center",
      justifyContent: "center",
      position: "relative",
    },
    cartBadge: {
      position: "absolute",
      top: -6,
      right: -6,
      backgroundColor: "red",
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      alignItems: "center",
      justifyContent: "center",
    },
    cartBadgeText: {
      fontSize: theme.typography.small.fontSize,
      color: "white",
      fontWeight: "bold",
    },
    // Search mode styles
    searchModeHeader: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.sm,
    },
    searchInputWrapper: {
      flex: 1,
      position: "relative",
    },
    searchModeInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      paddingRight: 40,
      fontSize: theme.typography.body.fontSize,
    },
    searchModeIcon: {
      position: "absolute",
      right: theme.spacing.sm,
      top: "50%",
      transform: [{ translateY: -10 }],
    },
    cancelButton: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.sm,
    },
    cancelButtonText: {
      fontSize: theme.typography.bodyMedium.fontSize,
      fontWeight: "600",
    },
    // Inventory-related styles
    imageContainer: {
      position: "relative",
    },
    outOfStockCard: {
      opacity: 0.7,
    },
    outOfStockImage: {
      opacity: 0.5,
    },
    inventoryBadge: {
      position: "absolute",
      top: 8,
      right: 8,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
      minWidth: 50,
      alignItems: "center",
    },
    inventoryBadgeText: {
      fontSize: 10,
      fontWeight: "700",
      color: "white",
      textAlign: "center",
    },

    // Product quantity controls styles
    quantityControlsContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      marginTop: "auto",
    },
    quantityButton: {
      width: 32,
      height: 32,
      borderWidth: 1,
      borderRadius: 16,
      alignItems: "center",
      justifyContent: "center",
    },
    quantityText: {
      ...theme.typography.bodyMedium,
      marginHorizontal: theme.spacing.md,
      minWidth: 30,
      textAlign: "center",
      fontWeight: "600",
    },
    // Compact cart button styles
    compactCartContainer: {
      position: "absolute",
      bottom: theme.spacing.lg,
      left: theme.spacing.md,
      right: theme.spacing.md,
      zIndex: 1000,
    },
    compactCartButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      borderWidth: 2,
      minHeight: 56,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    compactCartText: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
      fontWeight: "600",
    },
  });

const ProductsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { setCurrentTitle } = useNavigation();
  const { isPosAuthenticated: isAuthenticated, isLoading: authLoading } =
    useSession();
  const { products, isLoading, searchQuery, pagination } = useAppSelector(
    (state) => state.products
  );
  const cart = useUnifiedCart();

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
    productTitle?: string;
  }>({ title: "", message: "" });

  // Theme system
  const theme = useTheme();
  const { formatters } = useCurrency();

  const styles = createStyles(theme);

  // Get inventory status for a product
  const getInventoryStatus = (product: Product) => {
    const stock = product.variants[0]?.inventoryQuantity || 0;
    return {
      inStock: stock > 0,
      quantity: stock,
      isLowStock: stock > 0 && stock <= 5, // Consider <= 5 as low stock
    };
  };

  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");

  const borderColor = useThemeColor({}, "border");
  const responsiveLayout = useResponsiveLayout();
  const isDesktop = responsiveLayout?.isDesktop || false;

  const loadProducts = useCallback(() => {
    dispatch(fetchProducts({}) as any);
  }, [dispatch]);

  // Add a ref to track if we're currently loading more to prevent duplicate requests
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const lastLoadTimeRef = useRef(0);

  const loadMoreProducts = useCallback(async () => {
    const now = Date.now();
    const timeSinceLastLoad = now - lastLoadTimeRef.current;

    if (__DEV__) {
      console.log("🔄 loadMoreProducts called:", {
        hasNext: pagination.hasNext,
        isLoading,
        isLoadingMore,
        currentPage: pagination.page,
        productsCount: products.length,
        endCursor: pagination.endCursor,
        timeSinceLastLoad,
      });
    }

    // Debounce: prevent calls within 1 second of each other
    if (timeSinceLastLoad < 1000) {
      if (__DEV__) console.log("⏰ Debounced - too soon since last load");
      return;
    }

    if (pagination.hasNext && !isLoading && !isLoadingMore) {
      if (__DEV__)
        console.log("✅ Loading more products - page:", pagination.page + 1);
      lastLoadTimeRef.current = now;
      setIsLoadingMore(true);

      try {
        await dispatch(
          fetchProducts({
            page: pagination.page + 1,
            cursor: pagination.endCursor,
          }) as any
        );
        if (__DEV__) console.log("✅ Successfully loaded more products");
      } catch (error) {
        if (__DEV__) console.error("❌ Error loading more products:", error);
      } finally {
        setIsLoadingMore(false);
      }
    } else {
      if (__DEV__) {
        console.log("❌ Cannot load more:", {
          hasNext: pagination.hasNext,
          isLoading,
          isLoadingMore,
        });
      }
    }
  }, [
    dispatch,
    pagination.hasNext,
    pagination.page,
    pagination.endCursor,
    isLoading,
    isLoadingMore,
    products.length,
  ]);

  const refreshData = useCallback(async () => {
    // Reset pagination state and load fresh products
    dispatch(clearProducts());
    loadProducts();
  }, [loadProducts, dispatch]);

  // Reset isLoadingMore when main loading state changes
  useEffect(() => {
    if (!isLoading) {
      setIsLoadingMore(false);
    }
  }, [isLoading]);

  useEffect(() => {
    setCurrentTitle("Tickets");
  }, [setCurrentTitle]);

  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refreshData();
    }
  }, [isAuthenticated, authLoading, refreshData]);

  // Only refresh products on focus if we have no products or there's an error
  // This prevents breaking pagination when navigating back to the screen
  useFocusEffect(
    useCallback(() => {
      if (isAuthenticated && !authLoading && products.length === 0) {
        loadProducts();
      }
    }, [isAuthenticated, authLoading, products.length, loadProducts])
  );

  const handleSearch = (query: string) => {
    dispatch(setSearchQuery(query));
    if (query.trim()) {
      dispatch(searchProducts({ query }) as any);
    } else {
      dispatch(fetchProducts({}) as any);
    }
  };

  const handleSearchFocus = () => {
    // Search focus handler - can be used for analytics or other actions
  };

  const handleAddToCart = (product: Product, variant?: ProductVariant) => {
    const selectedVariant = variant || product.variants[0];

    if (!selectedVariant) {
      setModalData({
        title: "Error",
        message: "Please select a variant",
      });
      setShowErrorModal(true);
      return;
    }

    cart.addItem({
      variantId: selectedVariant.id,
      productId: product.id,
      title: product.title,
      variantTitle: selectedVariant.title,
      price: selectedVariant.price,
      quantity: 1,
      image: product.images?.[0]?.src,
      sku: selectedVariant.sku || "",
      inventoryQuantity: selectedVariant.inventoryQuantity || 0,
    });

    // No modal popup - direct add to cart
  };

  // Cart management functions
  const handleUpdateQuantity = (variantId: string, quantity: number) => {
    if (quantity <= 0) {
      cart.removeItem(variantId);
    } else {
      cart.updateQuantity(variantId, quantity);
    }
  };

  const canAddMore = (variantId: string) => {
    const cartItem = getCartItem(variantId);
    const product = products.find((p: Product) =>
      p.variants.some((v: ProductVariant) => v.id === variantId)
    );
    const variant = product?.variants.find(
      (v: ProductVariant) => v.id === variantId
    );

    if (!cartItem || !variant) return true;
    return cartItem.quantity < variant.inventoryQuantity;
  };

  // Get cart item by variant ID for quantity display
  const getCartItem = (variantId: string) => {
    return cart.items.find((item) => item.variantId === variantId);
  };

  const renderProduct = ({ item: product }: { item: Product }) => {
    const selectedVariant = product.variants[0];

    const inventoryStatus = getInventoryStatus(product);
    const cartItem = getCartItem(selectedVariant?.id || "");
    const isInCart = !!cartItem;

    // Dynamic card styling based on layout
    const cardStyle = isDesktop
      ? [
          styles.cardTouchable,
          {
            opacity: inventoryStatus.inStock ? 1 : 0.7,
            marginHorizontal: theme.spacing.xs, // Reduced margin for grid
            flex: 1,
          },
        ]
      : [
          styles.cardTouchable,
          {
            opacity: inventoryStatus.inStock ? 1 : 0.7,
            marginHorizontal: theme.spacing.md, // Full margin for mobile
          },
        ];

    return (
      <View style={cardStyle}>
        <ModernCard style={styles.productCard} variant="outlined">
          <View style={styles.productContent}>
            {/* Left Content */}
            <View style={styles.productLeftContent}>
              {/* Product Title */}
              <Text
                style={[styles.productTitle, { color: textColor }]}
                numberOfLines={2}
              >
                {product.title}
              </Text>

              {/* Category with background */}
              {product.vendor && (
                <View style={styles.productCategoryContainer}>
                  <Text
                    style={[styles.productCategory, { color: textSecondary }]}
                  >
                    {product.vendor}
                  </Text>
                </View>
              )}

              {/* SKU */}
              {selectedVariant?.sku && (
                <Text style={[styles.productSku, { color: textSecondary }]}>
                  SKU: {selectedVariant.sku}
                </Text>
              )}
            </View>

            {/* Right Content */}
            <View style={styles.productRightContent}>
              {/* Price */}
              {selectedVariant && (
                <Text style={[styles.productPrice, { color: primaryColor }]}>
                  {formatters.product(parseFloat(selectedVariant.price))}
                </Text>
              )}

              {/* Add to Cart Button */}
              <View style={styles.addToCartContainer}>
                {!isInCart ? (
                  <ModernButton
                    title="Add to Cart"
                    onPress={() => handleAddToCart(product, selectedVariant)}
                    disabled={!inventoryStatus.inStock}
                    style={styles.addToCartButton}
                    size="sm"
                  />
                ) : (
                  <View style={styles.cartControlsContainer}>
                    {/* Quantity controls only - no duplicate price */}
                    <View style={styles.quantityControls}>
                      <TouchableOpacity
                        style={[styles.circularButton, styles.minusButton]}
                        onPress={() =>
                          handleUpdateQuantity(
                            selectedVariant.id,
                            cartItem.quantity - 1
                          )
                        }
                      >
                        <IconSymbol name="minus" size={18} color="white" />
                      </TouchableOpacity>

                      <Text style={[styles.quantityText, { color: textColor }]}>
                        {cartItem.quantity}
                      </Text>

                      <TouchableOpacity
                        style={[
                          styles.circularButton,
                          styles.plusButton,
                          !canAddMore(selectedVariant.id) &&
                            styles.disabledButton,
                        ]}
                        onPress={() =>
                          handleUpdateQuantity(
                            selectedVariant.id,
                            cartItem.quantity + 1
                          )
                        }
                        disabled={!canAddMore(selectedVariant.id)}
                      >
                        <IconSymbol name="plus" size={18} color="white" />
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </View>

              {/* Stock Info */}
              <Text
                style={[
                  styles.stockText,
                  {
                    color: inventoryStatus.inStock
                      ? inventoryStatus.isLowStock
                        ? "#FF9500"
                        : "#34C759"
                      : "#FF3B30",
                  },
                ]}
              >
                {inventoryStatus.inStock
                  ? `In stock: ${selectedVariant?.inventoryQuantity || 0}`
                  : "Out of stock"}
              </Text>
            </View>
          </View>
        </ModernCard>
      </View>
    );
  };

  if (authLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={primaryColor} />
        <Text style={[styles.loadingText, { color: textColor }]}>
          Loading...
        </Text>
      </View>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Ticket Switcher - Enhanced ticket management */}
      <TicketSwitcher
        compactMode={true}
        onCheckout={() => {
          // Only navigate to checkout if there are items in the cart
          if (cart.items.length > 0) {
            router.push("/checkout");
          }
        }}
        style={{
          marginHorizontal: theme.spacing.md,
          marginTop: theme.spacing.sm,
        }}
      />

      {/* Search Bar Section */}
      <View style={[styles.searchSection, { backgroundColor }]}>
        <View style={styles.searchContainer}>
          <IconSymbol
            name="magnifyingglass"
            size={20}
            color={textSecondary}
            style={styles.searchIcon}
          />
          <TextInput
            style={[
              styles.searchInput,
              {
                backgroundColor: surfaceColor,
                borderColor: borderColor,
                color: textColor,
              },
            ]}
            placeholder="Search name/SKU"
            placeholderTextColor={textSecondary}
            value={searchQuery}
            onChangeText={handleSearch}
            onFocus={handleSearchFocus}
          />
        </View>
        <TouchableOpacity
          style={[
            styles.scannerButton,
            { backgroundColor: surfaceColor, borderColor: borderColor },
          ]}
        >
          <IconSymbol
            name="barcode.viewfinder"
            size={24}
            color={primaryColor}
          />
        </TouchableOpacity>
      </View>

      {/* Products List */}
      {isLoading && products.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            Loading products...
          </Text>
        </View>
      ) : products.length === 0 ? (
        <View style={styles.emptyContainer}>
          <IconSymbol name="bag.fill" size={64} color={textSecondary} />
          <Text style={[styles.emptyTitle, { color: textColor }]}>
            No products found
          </Text>
          <Text style={[styles.emptySubtitle, { color: textSecondary }]}>
            {searchQuery
              ? "Try a different search term"
              : "No products available in this store"}
          </Text>
        </View>
      ) : isDesktop ? (
        <FlatList
          data={products}
          renderItem={renderProduct}
          keyExtractor={(item, index) => `product-${item.id}-${index}`}
          numColumns={2}
          key="desktop-grid-2-columns" // Force re-render when switching to desktop
          style={styles.productsList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoading && !isLoadingMore}
              onRefresh={refreshData}
              colors={[primaryColor]}
              tintColor={primaryColor}
            />
          }
          onEndReached={loadMoreProducts}
          onEndReachedThreshold={0.3}
          removeClippedSubviews={false}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          ListFooterComponent={
            pagination.hasNext && (isLoading || isLoadingMore) ? (
              <View style={styles.loadMoreContainer}>
                <ActivityIndicator size="small" color={primaryColor} />
                <Text style={[styles.loadMoreText, { color: textSecondary }]}>
                  Loading more products...
                </Text>
              </View>
            ) : pagination.hasNext ? (
              <View style={styles.loadMoreContainer}>
                <Text style={[styles.loadMoreText, { color: textSecondary }]}>
                  Scroll to load more...
                </Text>
              </View>
            ) : (
              <View style={styles.loadMoreContainer}>
                <Text style={[styles.loadMoreText, { color: textSecondary }]}>
                  No more products
                </Text>
              </View>
            )
          }
          contentContainerStyle={styles.listContent}
          columnWrapperStyle={{
            justifyContent: "space-between",
            paddingHorizontal: theme.spacing.md,
            gap: theme.spacing.sm, // Add gap between columns
          }}
        />
      ) : (
        <FlatList
          data={products}
          renderItem={renderProduct}
          keyExtractor={(item, index) => `product-${item.id}-${index}`}
          style={styles.productsList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoading && !isLoadingMore}
              onRefresh={refreshData}
              colors={[primaryColor]}
              tintColor={primaryColor}
            />
          }
          onEndReached={loadMoreProducts}
          onEndReachedThreshold={0.3}
          removeClippedSubviews={false}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          ListFooterComponent={
            pagination.hasNext && (isLoading || isLoadingMore) ? (
              <View style={styles.loadMoreContainer}>
                <ActivityIndicator size="small" color={primaryColor} />
                <Text style={[styles.loadMoreText, { color: textSecondary }]}>
                  Loading more products...
                </Text>
              </View>
            ) : pagination.hasNext ? (
              <View style={styles.loadMoreContainer}>
                <Text style={[styles.loadMoreText, { color: textSecondary }]}>
                  Scroll to load more...
                </Text>
              </View>
            ) : (
              <View style={styles.loadMoreContainer}>
                <Text style={[styles.loadMoreText, { color: textSecondary }]}>
                  No more products
                </Text>
              </View>
            )
          }
          contentContainerStyle={styles.listContent}
        />
      )}

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title={modalData.title}
        message={modalData.message}
        showRetryButton={
          modalData.title === "Error" && modalData.message.includes("store")
        }
        onRetry={() => refreshData()}
      />
    </View>
  );
};

// Styles are now created using createStyles function with theme

export default ProductsScreen;
