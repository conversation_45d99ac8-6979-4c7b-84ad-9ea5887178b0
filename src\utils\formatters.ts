/**
 * Formatters Utility Module
 * 
 * Re-exports currency and other formatting utilities for consistent imports.
 * This module provides a centralized location for all formatting functions.
 */

// Re-export currency utilities
export {
  formatCurrency,
  formatCompactCurrency,
  parseCurrency,
  validateCurrencyAmount,
  calculateDiscount,
  CurrencyFormatter,
  CurrencyConverter,
  CURRENCY_CONFIG,
  type CurrencyFormatOptions,
} from './currencyUtils';

// Date formatting utilities
export const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }

  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options,
  };

  return dateObj.toLocaleDateString('en-KE', defaultOptions);
};

export const formatTime = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Time';
  }

  const defaultOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    ...options,
  };

  return dateObj.toLocaleTimeString('en-KE', defaultOptions);
};

export const formatDateTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid DateTime';
  }

  return `${formatDate(dateObj)} ${formatTime(dateObj)}`;
};

// Number formatting utilities
export const formatNumber = (
  value: number,
  options?: Intl.NumberFormatOptions
): string => {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0';
  }

  const defaultOptions: Intl.NumberFormatOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options,
  };

  return value.toLocaleString('en-KE', defaultOptions);
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0%';
  }

  return `${value.toFixed(decimals)}%`;
};

// Text formatting utilities
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format Kenyan phone numbers
  if (cleaned.startsWith('254')) {
    // +254 format
    return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`;
  } else if (cleaned.startsWith('0')) {
    // 0xxx format
    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
  }
  
  return phone;
};

export const formatName = (firstName?: string, lastName?: string): string => {
  const parts = [firstName, lastName].filter(Boolean);
  return parts.join(' ').trim();
};

export const formatInitials = (name: string): string => {
  if (!name) return '';
  
  return name
    .split(' ')
    .map(part => part.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2);
};

// Quantity and inventory formatting
export const formatQuantity = (quantity: number, unit?: string): string => {
  if (typeof quantity !== 'number' || isNaN(quantity)) {
    return '0';
  }

  const formattedQty = quantity % 1 === 0 ? quantity.toString() : quantity.toFixed(2);
  return unit ? `${formattedQty} ${unit}` : formattedQty;
};

export const formatStockLevel = (current: number, total?: number): string => {
  if (typeof current !== 'number' || isNaN(current)) {
    return 'Unknown';
  }

  if (total && typeof total === 'number' && !isNaN(total)) {
    return `${current} / ${total}`;
  }

  return current.toString();
};

// Status formatting
export const formatStatus = (status: string): string => {
  if (!status) return '';
  
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

// File size formatting
export const formatFileSize = (bytes: number): string => {
  if (typeof bytes !== 'number' || isNaN(bytes) || bytes === 0) {
    return '0 B';
  }

  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

// Duration formatting
export const formatDuration = (seconds: number): string => {
  if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
    return '0s';
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};

// Address formatting
export const formatAddress = (address: {
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}): string => {
  const parts = [
    address.line1,
    address.line2,
    address.city,
    address.state,
    address.country,
    address.postalCode,
  ].filter(Boolean);

  return parts.join(', ');
};

// Default export with all formatters
export default {
  // Currency
  currency: formatCurrency,
  compactCurrency: formatCompactCurrency,
  
  // Date/Time
  date: formatDate,
  time: formatTime,
  dateTime: formatDateTime,
  
  // Numbers
  number: formatNumber,
  percentage: formatPercentage,
  
  // Text
  phoneNumber: formatPhoneNumber,
  name: formatName,
  initials: formatInitials,
  
  // Inventory
  quantity: formatQuantity,
  stockLevel: formatStockLevel,
  
  // Status
  status: formatStatus,
  
  // File/Duration
  fileSize: formatFileSize,
  duration: formatDuration,
  
  // Address
  address: formatAddress,
};
