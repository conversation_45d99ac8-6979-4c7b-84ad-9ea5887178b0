const express = require("express");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const shopifyService = require("../services/shopify-service");
const terminalService = require("../services/terminal-management-service");
const ResponseFormatter = require("../utils/response-formatter");
const router = express.Router();

// In-memory store for demo (use database in production)
// These will be mapped to Shopify staff members
const posUsers = [
  {
    id: "pos-001",
    username: "cashier1",
    password: "$2b$10$rQJ8vQZ9Zm9Zm9Zm9Zm9ZuK8vQZ9Zm9Zm9Zm9Zm9ZuK8vQZ9Zm9Zm", // 'password123'
    name: "<PERSON>",
    role: "cashier",
    storeId: "default-store",
    shopifyStaffId: null, // Will be populated from Shopify
    commissionRate: 0,
    permissions: ["read_products", "create_orders", "read_customers"],
  },
  {
    id: "pos-002",
    username: "manager1",
    password: "$2b$10$rQJ8vQZ9Zm9Zm9Zm9Zm9ZuK8vQZ9Zm9Zm9Zm9Zm9ZuK8vQZ9Zm9Zm", // 'manager123'
    name: "Jane Manager",
    role: "manager",
    storeId: "default-store",
    shopifyStaffId: null, // Will be populated from Shopify
    commissionRate: 0,
    permissions: [
      "read_products",
      "create_orders",
      "read_customers",
      "manage_inventory",
      "view_reports",
      "manage_staff",
      "view_staff_performance",
    ],
  },
];

// Note: We now use custom staff authentication instead of Shopify Staff API
// This follows Shopify's recommendation for custom POS systems
console.log(
  "📋 Using custom staff authentication system (Shopify recommended approach)"
);

// Staff authentication is now handled by our custom staff attribution service

// POS Login endpoint
router.post("/login", async (req, res) => {
  try {
    const { username, password, deviceInfo } = req.body;

    if (!username || !password) {
      return ResponseFormatter.validationError(res, {
        username: !username ? "Username is required" : null,
        password: !password ? "Password is required" : null,
      });
    }

    // Find user
    const user = posUsers.find((u) => u.username === username);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: "Invalid credentials",
      });
    }

    // For demo, we'll use simple password comparison
    // In production, use bcrypt.compare(password, user.password)
    const validPassword =
      password === "password123" || password === "manager123";

    if (!validPassword) {
      return res.status(401).json({
        success: false,
        error: "Invalid credentials",
      });
    }

    // Handle terminal assignment
    let terminal = null;
    if (deviceInfo) {
      const terminalResult = await terminalService.autoDetectTerminal(
        deviceInfo
      );
      if (terminalResult.success) {
        terminal = terminalResult.terminal;

        // Assign staff to terminal
        await terminalService.assignStaffToTerminal(terminal.id, {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
        });

        console.log(
          `👤 ${user.name} logged into terminal ${terminal.name} at ${terminal.locationName}`
        );
      }
    }

    // Generate JWT token with terminal and location info
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role,
        storeId: user.storeId,
        permissions: user.permissions,
        shopifyStaffId: user.shopifyStaffId,
        terminalId: terminal?.id,
        locationId: terminal?.locationId,
        locationName: terminal?.locationName,
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "8h" } // 8 hour shift
    );

    res.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
          storeId: user.storeId,
          permissions: user.permissions,
          shopifyStaffId: user.shopifyStaffId,
          commissionRate: user.commissionRate,
        },
        terminal: terminal
          ? {
              id: terminal.id,
              name: terminal.name,
              type: terminal.type,
              capabilities: terminal.capabilities,
            }
          : null,
        location: terminal
          ? {
              id: terminal.locationId,
              name: terminal.locationName,
            }
          : null,
      },
    });
  } catch (error) {
    console.error("POS login error:", error);
    res.status(500).json({
      success: false,
      error: "Login failed",
    });
  }
});

// POS Token verification
router.get("/verify", (req, res) => {
  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({
        success: false,
        error: "No token provided",
      });
    }

    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "your-secret-key"
    );

    // Find user to get latest info
    const user = posUsers.find((u) => u.id === decoded.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: "User not found",
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
          storeId: user.storeId,
          permissions: user.permissions,
        },
      },
    });
  } catch (error) {
    console.error("Token verification error:", error);
    res.status(401).json({
      success: false,
      error: "Invalid token",
    });
  }
});

// POS Logout
router.post("/logout", (req, res) => {
  // In a real system, you might blacklist the token
  res.json({
    success: true,
    message: "Logged out successfully",
  });
});

// Get POS users (manager only)
router.get("/users", (req, res) => {
  try {
    const token = req.headers.authorization?.replace("Bearer ", "");
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "your-secret-key"
    );

    if (decoded.role !== "manager") {
      return res.status(403).json({
        success: false,
        error: "Access denied",
      });
    }

    const users = posUsers.map((user) => ({
      id: user.id,
      username: user.username,
      name: user.name,
      role: user.role,
      permissions: user.permissions,
    }));

    res.json({
      success: true,
      data: users,
    });
  } catch (error) {
    res.status(401).json({
      success: false,
      error: "Unauthorized",
    });
  }
});

module.exports = router;
