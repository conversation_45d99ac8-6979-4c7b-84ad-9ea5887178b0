import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';
import { createMpesaService } from '@/src/services/mpesa-service';
import { Colors } from '@/constants/Colors';

const MpesaPaymentStatusScreen: React.FC = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  
  const [paymentStatus, setPaymentStatus] = useState<'processing' | 'success' | 'failed'>('processing');
  const [statusMessage, setStatusMessage] = useState('Processing M-Pesa payment...');
  const [transactionId, setTransactionId] = useState<string>('');

  // Theme colors
  const backgroundColor = useThemeColor({}, 'background');
  const surfaceColor = useThemeColor({}, 'surface');
  const textColor = useThemeColor({}, 'text');
  const textSecondary = useThemeColor({}, 'textSecondary');
  const primaryColor = useThemeColor({}, 'primary');
  const successColor = useThemeColor({}, 'success');
  const errorColor = useThemeColor({}, 'error');

  // Get params
  const checkoutRequestId = params.checkoutRequestId as string;
  const amount = parseFloat(params.amount as string) || 0;
  const phoneNumber = params.phoneNumber as string;
  const customerMessage = params.customerMessage as string;

  useEffect(() => {
    if (customerMessage) {
      setStatusMessage(customerMessage);
    }

    // Start polling for payment status
    if (checkoutRequestId) {
      pollPaymentStatus();
    } else {
      // For simulation without checkout request ID, auto-complete after delay
      setTimeout(() => {
        setPaymentStatus('success');
        setStatusMessage('Payment completed successfully!');
        setTransactionId(`MPESA_${Date.now()}`);
      }, 5000);
    }
  }, [checkoutRequestId, customerMessage]);

  const pollPaymentStatus = async () => {
    const mpesaService = createMpesaService('sandbox');
    const maxAttempts = 30; // Poll for 5 minutes (30 * 10 seconds)
    let attempts = 0;

    const poll = async () => {
      try {
        // Use simulation for development
        const isDevelopment = process.env.NODE_ENV === 'development' || __DEV__;
        const status = isDevelopment
          ? await mpesaService.simulatePaymentStatus(checkoutRequestId)
          : await mpesaService.checkPaymentStatus(checkoutRequestId);
        
        if (status.success) {
          setPaymentStatus('success');
          setStatusMessage('Payment completed successfully!');
          setTransactionId(status.mpesaReceiptNumber || checkoutRequestId);
          
          // Navigate to success screen after delay
          setTimeout(() => {
            router.replace({
              pathname: '/payment-success',
              params: {
                amount: amount.toString(),
                transactionId: status.mpesaReceiptNumber || checkoutRequestId,
                paymentMethod: 'M-Pesa',
                phoneNumber,
              },
            });
          }, 2000);
          
          return;
        }
        
        attempts++;
        if (attempts >= maxAttempts) {
          setPaymentStatus('failed');
          setStatusMessage('Payment timeout. Please check your M-Pesa messages or try again.');
          return;
        }
        
        // Continue polling
        setTimeout(poll, 10000); // Poll every 10 seconds
        
      } catch (error) {
        console.error('Error polling payment status:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 10000);
        } else {
          setPaymentStatus('failed');
          setStatusMessage('Unable to verify payment status. Please check your M-Pesa messages.');
        }
      }
    };

    // Start polling after initial delay
    setTimeout(poll, 5000);
  };

  const handleRetry = () => {
    router.back();
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel Payment',
      'Are you sure you want to cancel the payment process?',
      [
        { text: 'No', style: 'cancel' },
        { 
          text: 'Yes', 
          style: 'destructive',
          onPress: () => router.replace('/(tabs)/products')
        },
      ]
    );
  };

  const renderStatusIcon = () => {
    switch (paymentStatus) {
      case 'processing':
        return <ActivityIndicator size="large" color={primaryColor} />;
      case 'success':
        return <Ionicons name="checkmark-circle" size={64} color={successColor} />;
      case 'failed':
        return <Ionicons name="close-circle" size={64} color={errorColor} />;
    }
  };

  const getStatusColor = () => {
    switch (paymentStatus) {
      case 'processing':
        return primaryColor;
      case 'success':
        return successColor;
      case 'failed':
        return errorColor;
    }
  };

  const getStatusTitle = () => {
    switch (paymentStatus) {
      case 'processing':
        return 'Processing Payment';
      case 'success':
        return 'Payment Successful!';
      case 'failed':
        return 'Payment Failed';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleCancel} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={textColor} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: textColor }]}>
          M-Pesa Payment
        </Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        <View style={[styles.statusContainer, { backgroundColor: surfaceColor }]}>
          {renderStatusIcon()}
          
          <Text style={[styles.statusTitle, { color: getStatusColor() }]}>
            {getStatusTitle()}
          </Text>
          
          <Text style={[styles.statusMessage, { color: textSecondary }]}>
            {statusMessage}
          </Text>

          <View style={styles.paymentDetails}>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: textSecondary }]}>Amount:</Text>
              <Text style={[styles.detailValue, { color: textColor }]}>
                KSh {amount.toLocaleString()}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: textSecondary }]}>Phone:</Text>
              <Text style={[styles.detailValue, { color: textColor }]}>
                {phoneNumber}
              </Text>
            </View>

            {transactionId && (
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: textSecondary }]}>Transaction ID:</Text>
                <Text style={[styles.detailValue, { color: textColor }]}>
                  {transactionId}
                </Text>
              </View>
            )}
          </View>

          {paymentStatus === 'processing' && (
            <Text style={[styles.helpText, { color: textSecondary }]}>
              Please complete the payment on your phone. This may take a few moments.
            </Text>
          )}

          {paymentStatus === 'failed' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.retryButton, { backgroundColor: primaryColor }]}
                onPress={handleRetry}
              >
                <Text style={styles.retryButtonText}>Try Again</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.cancelButton, { borderColor: Colors.light.border }]}
                onPress={handleCancel}
              >
                <Text style={[styles.cancelButtonText, { color: textSecondary }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  statusContainer: {
    alignItems: 'center',
    padding: 32,
    borderRadius: 12,
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  statusMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  paymentDetails: {
    width: '100%',
    marginBottom: 24,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  helpText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  retryButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default MpesaPaymentStatusScreen;
