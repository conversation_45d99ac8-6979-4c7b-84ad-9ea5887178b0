/**
 * Responsive Utilities for Dukalink POS
 * Helper functions for responsive design and styling
 */

import { ViewStyle, TextStyle } from "react-native";
import { Theme } from "@/src/contexts/ThemeContext";
import {
  ScreenSize,
  SPACING_MULTIPLIERS,
  TYPOGRAPHY_SCALES,
} from "@/src/constants/ResponsiveConstants";

// Responsive spacing utility
export const getResponsiveSpacing = (
  baseSpacing: number,
  screenSize: ScreenSize
): number => {
  return baseSpacing * SPACING_MULTIPLIERS[screenSize];
};

// Responsive font size utility
export const getResponsiveFontSize = (
  baseFontSize: number,
  screenSize: ScreenSize
): number => {
  return baseFontSize * TYPOGRAPHY_SCALES[screenSize];
};

// Create responsive style object
export const createResponsiveStyle = <T extends ViewStyle | TextStyle>(
  styles: {
    mobile: T;
    tablet?: Partial<T>;
    desktop?: Partial<T>;
    large?: Partial<T>;
  },
  screenSize: ScreenSize
): T => {
  let result = { ...styles.mobile };

  if (screenSize === "tablet" && styles.tablet) {
    result = { ...result, ...styles.tablet };
  } else if (screenSize === "desktop" && styles.desktop) {
    result = { ...result, ...styles.desktop };
  } else if (screenSize === "large" && styles.large) {
    result = { ...result, ...styles.large };
  }

  return result;
};

// Responsive padding utility
export const getResponsivePadding = (
  theme: Theme,
  screenSize: ScreenSize,
  size: "xs" | "sm" | "md" | "lg" | "xl" = "md"
): ViewStyle => {
  const baseSpacing = theme?.spacing?.[size] || 16;
  const multiplier = SPACING_MULTIPLIERS[screenSize] || 1;

  return {
    paddingHorizontal: baseSpacing * multiplier,
    paddingVertical: baseSpacing * multiplier * 0.8,
  };
};

// Responsive margin utility
export const getResponsiveMargin = (
  theme: Theme,
  screenSize: ScreenSize,
  size: "xs" | "sm" | "md" | "lg" | "xl" = "md"
): ViewStyle => {
  const baseSpacing = theme?.spacing?.[size] || 16;
  const multiplier = SPACING_MULTIPLIERS[screenSize] || 1;

  return {
    marginHorizontal: baseSpacing * multiplier,
    marginVertical: baseSpacing * multiplier * 0.8,
  };
};

// Responsive border radius utility
export const getResponsiveBorderRadius = (
  theme: Theme,
  screenSize: ScreenSize,
  size: "sm" | "md" | "lg" = "md"
): number => {
  const baseRadius = theme.borderRadius[size];
  const multiplier = SPACING_MULTIPLIERS[screenSize];

  return baseRadius * multiplier;
};

// Responsive shadow utility
export const getResponsiveShadow = (
  theme: Theme,
  screenSize: ScreenSize,
  intensity: "sm" | "md" | "lg" = "md"
): ViewStyle => {
  const baseShadow = theme.shadows[intensity];
  const multiplier = SPACING_MULTIPLIERS[screenSize];

  return {
    ...baseShadow,
    shadowRadius: (baseShadow.shadowRadius || 0) * multiplier,
    elevation: (baseShadow.elevation || 0) * multiplier,
  };
};

// Responsive typography utility
export const getResponsiveTypography = (
  theme: Theme,
  screenSize: ScreenSize,
  variant: keyof Theme["typography"]
): TextStyle => {
  // Defensive programming - handle undefined values
  if (!theme || !theme.typography || !variant || !screenSize) {
    return { fontSize: 16 }; // Fallback typography
  }

  const baseTypography = theme.typography[variant];
  const scale = TYPOGRAPHY_SCALES[screenSize];

  // Handle undefined baseTypography
  if (!baseTypography) {
    return { fontSize: 16 * (scale || 1) };
  }

  // Handle undefined scale
  if (!scale) {
    return baseTypography;
  }

  return {
    ...baseTypography,
    fontSize: (baseTypography.fontSize || 16) * scale,
    lineHeight: baseTypography.lineHeight
      ? baseTypography.lineHeight * scale
      : undefined,
  };
};

// Responsive flex utility
export const getResponsiveFlex = (screenSize: ScreenSize): ViewStyle => {
  switch (screenSize) {
    case "desktop":
    case "large":
      return {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "space-between",
      };
    case "tablet":
      return {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "flex-start",
      };
    default:
      return {
        flexDirection: "column",
      };
  }
};

// Responsive grid utility
export const getResponsiveGrid = (
  screenSize: ScreenSize,
  itemCount: number
): { itemWidth: string; spacing: number } => {
  let columns: number;

  switch (screenSize) {
    case "large":
      columns = Math.min(4, itemCount);
      break;
    case "desktop":
      columns = Math.min(3, itemCount);
      break;
    case "tablet":
      columns = Math.min(2, itemCount);
      break;
    default:
      columns = 1;
  }

  const spacing = SPACING_MULTIPLIERS[screenSize] * 16;
  const itemWidth = `${100 / columns}%`;

  return { itemWidth, spacing };
};
