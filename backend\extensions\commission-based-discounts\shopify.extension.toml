api_version = "2025-04"

[[extensions]]
name = "Enhanced Commission & Loyalty Discounts"
handle = "commission-based-discounts"
type = "function"
description = "Advanced discount management with commission rates, staff performance, loyalty tiers, and points redemption"

  [[extensions.targeting]]
  target = "purchase.cart-lines.discounts.generate.run"
  input_query = "src/run.graphql"
  export = "run"

  [extensions.build]
  command = "cargo build --target=wasm32-wasip1 --release"
  path = "target/wasm32-wasip1/release/commission-based-discounts.wasm"
  watch = [ "src/**/*.rs" ]

  [extensions.ui.paths]
  create = "/"
  details = "/"
