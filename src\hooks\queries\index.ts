/**
 * Query Hooks Index
 * 
 * Central export point for all TanStack React Query hooks.
 * Provides easy imports and consistent API access.
 */

// Product hooks
export {
  useProducts,
  useInfiniteProducts,
  useProduct,
  useProductSearch,
  useProductInventory,
  useUpdateProductInventory,
  usePrefetchProducts,
} from './useProducts';

// Customer hooks
export {
  useCustomers,
  useInfiniteCustomers,
  useCustomer,
  useCustomerSearch,
  useCreateCustomer,
  useUpdateCustomer,
  useDeleteCustomer,
  useCustomerOrderHistory,
  usePrefetchCustomers,
} from './useCustomers';

// Loyalty hooks
export {
  useCustomerLoyalty,
  useLoyaltyTransactions,
  useLoyaltyDiscounts,
  useLoyaltyTiers,
  useLoyaltyLeaderboard,
  useLoyaltyAnalytics,
  useRedeemLoyaltyPoints,
  useAddLoyaltyPoints,
  useAdjustLoyaltyPoints,
  useLoyaltyCacheInvalidation,
  usePrefetchLoyalty,
} from './useLoyalty';

// Re-export query keys for direct access
export { queryKeys, getInvalidationKeys } from '@/src/lib/queryKeys';

// Re-export QueryClient utilities
export { getQueryClientInstance } from '@/src/providers/QueryClientProvider';
