import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { useRBAC } from "@/src/hooks/useRBAC";
import { Customer, LoyaltyTransaction } from "@/src/types/shopify";
import { loyaltyService } from "@/src/services/loyalty-service";
import { CustomerLoyaltyData } from "@/src/components/loyalty/CustomerLoyaltyCard";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { CustomerLoyaltyOverview } from "@/src/components/customer/CustomerLoyaltyOverview";
import { CustomerLoyaltyTransactions } from "@/src/components/customer/CustomerLoyaltyTransactions";

interface CustomerDetailsData extends Customer {
  displayName?: string;
  ordersCount?: number;
  totalSpent?: string;
  lastOrderDate?: string;
  averageOrderValue?: string;
}

export default function CustomerDetailsScreen() {
  const router = useRouter();
  const { customerId } = useLocalSearchParams<{ customerId: string }>();
  const { setCurrentTitle } = useNavigation();
  const { canManageCustomers } = useRBAC();

  const [customer, setCustomer] = useState<CustomerDetailsData | null>(null);
  const [loyaltyData, setLoyaltyData] = useState<CustomerLoyaltyData | null>(
    null
  );
  const [loyaltyTransactions, setLoyaltyTransactions] = useState<
    LoyaltyTransaction[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [loadingLoyalty, setLoadingLoyalty] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "overview" | "loyalty" | "transactions"
  >("overview");

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = useThemeColor({}, "success");
  const errorColor = useThemeColor({}, "error");

  useEffect(() => {
    if (customerId) {
      loadCustomerDetails();
      loadLoyaltyData();
    }
  }, [customerId]);

  const loadLoyaltyData = async () => {
    if (!customerId) return;

    try {
      setLoadingLoyalty(true);

      // Load loyalty summary and transactions in parallel
      const [loyaltySummary, transactions] = await Promise.all([
        loyaltyService.getCustomerLoyaltySummary(customerId),
        loyaltyService.getCustomerLoyaltyTransactions(customerId, {
          limit: 50,
        }),
      ]);

      if (loyaltySummary) {
        setLoyaltyData(loyaltySummary);
      }

      if (transactions) {
        setLoyaltyTransactions(transactions.transactions);
      }
    } catch (error) {
      console.error("Failed to load loyalty data:", error);
    } finally {
      setLoadingLoyalty(false);
    }
  };

  const loadCustomerDetails = async () => {
    if (!customerId) return;

    try {
      setLoading(true);
      const apiClient = getAPIClient();

      // For now, we'll use the store customers endpoint and find the specific customer
      // In the future, you might want to create a dedicated getCustomerById endpoint
      const response = await apiClient.getStoreCustomers({
        limit: 1000,
        includeLoyalty: true, // Include loyalty data for customer details
      });

      if (response.success && response.data) {
        const foundCustomer = response.data.customers.find(
          (c) => c.id === customerId
        );
        if (foundCustomer) {
          setCustomer(foundCustomer);
          setCurrentTitle(
            foundCustomer.displayName ||
              `${foundCustomer.firstName} ${foundCustomer.lastName}`.trim()
          );
        } else {
          Alert.alert("Error", "Customer not found");
          router.back();
        }
      } else {
        Alert.alert(
          "Error",
          response.error || "Failed to load customer details"
        );
        router.back();
      }
    } catch (error) {
      console.error("Load customer error:", error);
      Alert.alert("Error", "Failed to load customer details");
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleEditCustomer = () => {
    // TODO: Navigate to customer edit screen or show edit modal
    Alert.alert(
      "Edit Customer",
      `Edit functionality for ${customer?.displayName} will be implemented soon.`
    );
  };

  const handleDeleteCustomer = () => {
    if (!customer) return;

    Alert.alert(
      "Delete Customer",
      `Are you sure you want to delete ${customer.displayName}? This action cannot be undone.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            // TODO: Implement delete API call
            Alert.alert(
              "Delete Customer",
              `Delete functionality for ${customer.displayName} will be implemented soon.`
            );
          },
        },
      ]
    );
  };

  const formatCurrency = (amount: string | number) => {
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    return `KSh ${numAmount.toLocaleString("en-KE", {
      minimumFractionDigits: 2,
    })}`;
  };

  // Helper function to get tier badge color
  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case "bronze":
        return "#CD7F32";
      case "silver":
        return "#C0C0C0";
      case "gold":
        return "#FFD700";
      case "platinum":
        return "#E5E4E2";
      default:
        return textSecondary;
    }
  };

  // Helper function to format transaction type
  const formatTransactionType = (type: string) => {
    switch (type) {
      case "earned":
        return "Points Earned";
      case "redeemed":
        return "Points Redeemed";
      case "expired":
        return "Points Expired";
      case "adjusted":
        return "Points Adjusted";
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <ScreenWrapper title="Customer Details" showBackButton>
        <View style={[styles.loadingContainer, { backgroundColor }]}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            Loading customer details...
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  if (!customer) {
    return (
      <ScreenWrapper title="Customer Details" showBackButton>
        <View style={[styles.errorContainer, { backgroundColor }]}>
          <Text style={[styles.errorText, { color: textColor }]}>
            Customer not found
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper
      title={customer.displayName || "Customer Details"}
      showBackButton
    >
      <View style={[styles.container, { backgroundColor }]}>
        {/* Customer Profile Card */}
        <ModernCard
          style={[styles.profileCard, { backgroundColor: surfaceColor }]}
        >
          <View style={styles.profileHeader}>
            <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
              <Text style={styles.avatarText}>
                {(customer.firstName || "").charAt(0)}
                {(customer.lastName || "").charAt(0)}
              </Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={[styles.profileName, { color: textColor }]}>
                {customer.displayName ||
                  `${customer.firstName} ${customer.lastName}`.trim()}
              </Text>
              {customer.email && (
                <Text style={[styles.profileEmail, { color: textSecondary }]}>
                  {customer.email}
                </Text>
              )}
              {customer.phone && (
                <Text style={[styles.profilePhone, { color: textSecondary }]}>
                  {customer.phone}
                </Text>
              )}
            </View>
          </View>

          {/* Customer Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: primaryColor }]}>
                {customer.ordersCount || 0}
              </Text>
              <Text style={[styles.statLabel, { color: textSecondary }]}>
                Orders
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: successColor }]}>
                {formatCurrency(customer.totalSpent || "0")}
              </Text>
              <Text style={[styles.statLabel, { color: textSecondary }]}>
                Total Spent
              </Text>
            </View>
            {customer.averageOrderValue && (
              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: textColor }]}>
                  {formatCurrency(customer.averageOrderValue)}
                </Text>
                <Text style={[styles.statLabel, { color: textSecondary }]}>
                  Avg Order
                </Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          {canManageCustomers && (
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  {
                    backgroundColor: primaryColor + "20",
                    borderColor: primaryColor,
                  },
                ]}
                onPress={handleEditCustomer}
              >
                <Ionicons name="pencil" size={16} color={primaryColor} />
                <Text
                  style={[styles.actionButtonText, { color: primaryColor }]}
                >
                  Edit
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  {
                    backgroundColor: errorColor + "20",
                    borderColor: errorColor,
                  },
                ]}
                onPress={handleDeleteCustomer}
              >
                <Ionicons name="trash" size={16} color={errorColor} />
                <Text style={[styles.actionButtonText, { color: errorColor }]}>
                  Delete
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </ModernCard>

        {/* Additional Information Card */}
        <ModernCard
          style={[styles.infoCard, { backgroundColor: surfaceColor }]}
        >
          <Text style={[styles.cardTitle, { color: textColor }]}>
            Customer Information
          </Text>

          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: textSecondary }]}>
              Customer ID:
            </Text>
            <Text style={[styles.infoValue, { color: textColor }]}>
              {customer.id}
            </Text>
          </View>

          {customer.lastOrderDate && (
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: textSecondary }]}>
                Last Order:
              </Text>
              <Text style={[styles.infoValue, { color: textColor }]}>
                {new Date(customer.lastOrderDate).toLocaleDateString()}
              </Text>
            </View>
          )}

          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: textSecondary }]}>
              Status:
            </Text>
            <Text style={[styles.infoValue, { color: successColor }]}>
              Active
            </Text>
          </View>
        </ModernCard>

        {/* Tab Bar */}
        <View style={[styles.tabBar, { backgroundColor: surfaceColor }]}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === "overview" && [
                styles.activeTabButton,
                { backgroundColor: primaryColor + "20" },
              ],
            ]}
            onPress={() => setActiveTab("overview")}
          >
            <IconSymbol
              name="person.fill"
              size={18}
              color={activeTab === "overview" ? primaryColor : textSecondary}
            />
            <Text
              style={[
                styles.tabLabel,
                {
                  color:
                    activeTab === "overview" ? primaryColor : textSecondary,
                },
              ]}
            >
              Overview
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === "loyalty" && [
                styles.activeTabButton,
                { backgroundColor: primaryColor + "20" },
              ],
            ]}
            onPress={() => setActiveTab("loyalty")}
          >
            <IconSymbol
              name="star.fill"
              size={18}
              color={activeTab === "loyalty" ? primaryColor : textSecondary}
            />
            <Text
              style={[
                styles.tabLabel,
                {
                  color: activeTab === "loyalty" ? primaryColor : textSecondary,
                },
              ]}
            >
              Loyalty
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === "transactions" && [
                styles.activeTabButton,
                { backgroundColor: primaryColor + "20" },
              ],
            ]}
            onPress={() => setActiveTab("transactions")}
          >
            <IconSymbol
              name="list.bullet"
              size={18}
              color={
                activeTab === "transactions" ? primaryColor : textSecondary
              }
            />
            <Text
              style={[
                styles.tabLabel,
                {
                  color:
                    activeTab === "transactions" ? primaryColor : textSecondary,
                },
              ]}
            >
              Transactions
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContent}>
          {activeTab === "overview" && (
            <ScrollView
              style={styles.tabScrollView}
              showsVerticalScrollIndicator={false}
            >
              <View style={styles.tabContentPadding}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>
                  Additional Information
                </Text>
              </View>
            </ScrollView>
          )}

          {activeTab === "loyalty" && (
            <ScrollView
              style={styles.tabScrollView}
              showsVerticalScrollIndicator={false}
            >
              <View style={styles.tabContentPadding}>
                <CustomerLoyaltyOverview
                  loyaltyData={loyaltyData}
                  loading={loadingLoyalty}
                />
              </View>
            </ScrollView>
          )}

          {activeTab === "transactions" && (
            <ScrollView
              style={styles.tabScrollView}
              showsVerticalScrollIndicator={false}
            >
              <View style={styles.tabContentPadding}>
                <CustomerLoyaltyTransactions
                  transactions={loyaltyTransactions}
                  loading={loadingLoyalty}
                  useScrollView={true}
                />
              </View>
            </ScrollView>
          )}
        </View>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 16,
  },
  profileCard: {
    padding: 20,
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    marginBottom: 2,
  },
  profilePhone: {
    fontSize: 14,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: "#E5E5E5",
    marginBottom: 16,
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: "500",
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  infoCard: {
    padding: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  infoValue: {
    fontSize: 14,
  },
  tabBar: {
    flexDirection: "row",
    marginVertical: 16,
    borderRadius: 8,
    overflow: "hidden",
  },
  tabButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    gap: 6,
  },
  activeTabButton: {
    borderRadius: 6,
    margin: 2,
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: "600",
  },
  tabContent: {
    flex: 1,
  },
  tabScrollView: {
    flex: 1,
  },
  tabContentPadding: {
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
});
