/**
 * Universal Receipt Styler
 *
 * Device-agnostic receipt styling that works consistently across:
 * - Thermal printers (58mm, 80mm)
 * - Web browsers
 * - Mobile devices
 * - Inkjet/laser printers
 *
 * Uses only basic CSS properties supported by all devices
 */

import { TREASURED_LOGO_TEXT } from "../constants/logoConstants";

export interface UniversalReceiptOptions {
  width?: "thermal-58mm" | "thermal-80mm" | "standard" | "mobile";
  format?: "html" | "text";
  deviceType?: "thermal" | "web" | "mobile" | "standard";
}

export class UniversalReceiptStyler {
  /**
   * Generate universal CSS that works across all devices
   */
  static generateUniversalCSS(options: UniversalReceiptOptions = {}): string {
    const { width = "thermal-80mm", deviceType = "thermal" } = options;

    // Base measurements for different widths
    const measurements = {
      "thermal-58mm": { maxWidth: "58mm", fontSize: "10px", padding: "2mm" },
      "thermal-80mm": { maxWidth: "80mm", fontSize: "11px", padding: "3mm" },
      standard: { maxWidth: "400px", fontSize: "12px", padding: "20px" },
      mobile: { maxWidth: "100%", fontSize: "14px", padding: "16px" },
    };

    const config = measurements[width];

    return `
/* Universal Receipt CSS - Compatible with all devices */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
}

body {
  font-family: 'Courier New', 'Courier', monospace;
  font-size: ${config.fontSize};
  line-height: 1.3;
  color: #000 !important;
  background: #fff;
  padding: ${config.padding};
  max-width: ${config.maxWidth};
  margin: 0 auto;
  text-align: center;
  font-weight: 600 !important; /* Stronger font weight for better visibility */
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

/* Basic layout classes */
.center { text-align: center; }
.left { text-align: left; }
.right { text-align: right; }

.bold { font-weight: 900 !important; color: #000 !important; } /* Extra bold for better visibility */
.normal { font-weight: 600 !important; color: #000 !important; } /* Medium weight for normal text */

/* Size classes */
.small { font-size: 9px; }
.medium { font-size: ${config.fontSize}; }
.large { font-size: 14px; }
.xlarge { font-size: 16px; }

/* Spacing classes */
.mb-1 { margin-bottom: 2px; }
.mb-2 { margin-bottom: 4px; }
.mb-3 { margin-bottom: 8px; }
.mb-4 { margin-bottom: 12px; }

.mt-1 { margin-top: 2px; }
.mt-2 { margin-top: 4px; }
.mt-3 { margin-top: 8px; }
.mt-4 { margin-top: 12px; }

/* Receipt sections */
.receipt-header {
  text-align: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #000;
}

.receipt-body {
  text-align: left;
  margin-bottom: 12px;
}

.receipt-footer {
  text-align: center;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #000;
}

/* Store logo - increased size for better visibility */
.store-logo {
  text-align: center;
  margin-bottom: 12px;
  padding: 0 8px; /* Leave print margins */
}

.logo-image {
  max-width: 140px;
  max-height: 140px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 6px;
  /* Make logo span most of the receipt width while respecting margins */
  min-width: 120px;
  min-height: 80px;
}

/* Store info */
.store-name {
  font-weight: 900 !important;
  font-size: 16px !important;
  margin-bottom: 4px;
  color: #000 !important;
  text-transform: uppercase;
}

.store-info {
  font-size: 11px !important;
  margin-bottom: 2px;
  font-weight: 600 !important;
  color: #000 !important;
}

/* Receipt details */
.receipt-details {
  margin-bottom: 8px;
}

.detail-line {
  margin-bottom: 2px;
}

/* Items section */
.items-section {
  margin-bottom: 8px;
}

.item {
  margin-bottom: 6px;
  text-align: left;
}

.item-name {
  font-weight: 800 !important;
  margin-bottom: 2px;
  color: #000 !important;
}

.item-details {
  font-size: 11px !important;
  margin-bottom: 2px;
  font-weight: 600 !important;
  color: #000 !important;
}

/* Two-column layout using table (most compatible) */
.two-column {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 4px;
}

.two-column td {
  padding: 2px 0;
  vertical-align: top;
}

.two-column .left-col {
  text-align: left;
  width: 65%;
  padding-right: 8px;
}

.two-column .right-col {
  text-align: right;
  width: 35%;
  font-weight: bold;
}

/* Enhanced table layouts for consistent left-right styling */
.receipt-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 8px;
}

.receipt-table td {
  padding: 2px 0;
  vertical-align: top;
}

.receipt-table .label-col {
  text-align: left;
  width: 65%;
  padding-right: 8px;
}

.receipt-table .value-col {
  text-align: right;
  width: 35%;
  font-weight: bold;
}

/* Totals section */
.totals-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #000;
}

.total-line {
  margin-bottom: 2px;
}

.grand-total {
  font-weight: 900 !important;
  font-size: 15px !important;
  margin-top: 4px;
  padding-top: 4px;
  border-top: 2px solid #000 !important;
  color: #000 !important;
}

/* Payment section */
.payment-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed #000;
}

/* Loyalty section */
.loyalty-section {
  margin: 8px 0;
  padding: 6px 0;
  border-top: 2px dashed #000 !important;
  border-bottom: 2px dashed #000 !important;
  text-align: center;
  background: #f9f9f9;
}

.loyalty-title {
  font-weight: 900 !important;
  margin-bottom: 4px;
  color: #000 !important;
  font-size: 13px !important;
}

/* Separators */
.separator {
  border-top: 1px solid #000;
  margin: 8px 0;
  height: 1px;
}

.dashed-separator {
  border-top: 1px dashed #000;
  margin: 8px 0;
  height: 1px;
}

/* Print-specific styles */
@media print {
  html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: ${config.maxWidth} !important;
    max-width: ${config.maxWidth} !important;
  }
  
  body {
    padding: ${config.padding} !important;
  }
  
  .no-print {
    display: none !important;
  }
  
  /* Logo adjustments for thermal printing */
  .logo-image {
    max-width: 100px !important;
    max-height: 100px !important;
    min-width: 80px !important;
    min-height: 60px !important;
  }

  /* Ensure borders print */
  .separator, .dashed-separator, .receipt-header, .receipt-footer, .totals-section, .grand-total {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
}

/* Page setup for thermal printing */
@page {
  size: ${config.maxWidth} auto;
  margin: 0;
}

/* Mobile adjustments */
@media screen and (max-width: 480px) {
  body {
    padding: 16px;
    max-width: 100%;
  }
  
  .store-name {
    font-size: 16px;
  }
  
  .medium {
    font-size: 14px;
  }
}
`;
  }

  /**
   * Generate text-based receipt with proper alignment
   */
  static generateTextReceipt(data: any, width: number = 32): string {
    const lines: string[] = [];

    // Helper functions
    const centerText = (text: string): string => {
      const padding = Math.max(0, Math.floor((width - text.length) / 2));
      return " ".repeat(padding) + text;
    };

    const leftRightAlign = (left: string, right: string): string => {
      const maxLeft = width - right.length - 2; // Reserve 2 chars minimum spacing
      const truncatedLeft =
        left.length > maxLeft ? left.substring(0, maxLeft - 1) + "…" : left;
      const spacing = Math.max(2, width - truncatedLeft.length - right.length);
      return truncatedLeft + " ".repeat(spacing) + right;
    };

    const separator = (char: string = "-"): string => char.repeat(width);

    // Store header
    lines.push(centerText(TREASURED_LOGO_TEXT)); // Add logo from constants
    lines.push(centerText(data.storeName || "TREASURED SCENTS"));
    lines.push(centerText(data.storeAddress || "Greenhouse Mall, Ngong Road"));
    lines.push(centerText(`Mobile: ${data.storePhone || "+254 111 443 993"}`));
    lines.push(
      centerText(`Email: ${data.storeEmail || "<EMAIL>"}`)
    );
    lines.push("");

    // Receipt details
    lines.push(separator());
    lines.push(centerText("SALES RECEIPT"));
    lines.push(separator());
    lines.push(leftRightAlign("Receipt No:", data.receiptNumber || "N/A"));
    lines.push(
      leftRightAlign("Date:", data.date || new Date().toLocaleDateString())
    );
    lines.push(
      leftRightAlign("Time:", data.time || new Date().toLocaleTimeString())
    );
    lines.push(leftRightAlign("Served by:", data.staffName || "POS Staff"));

    if (data.customerName) {
      lines.push(leftRightAlign("Customer:", data.customerName));
    }

    if (data.customerPhone) {
      lines.push(leftRightAlign("Mobile:", data.customerPhone));
    }

    lines.push("");

    // Items
    lines.push(separator());
    (data.items || []).forEach((item: any, index: number) => {
      lines.push(`${index + 1}. ${item.name || "Item"}`);
      const qtyPrice = `${item.quantity || 1} x KSh ${(
        item.unitPrice || 0
      ).toFixed(2)}`;
      const total = `KSh ${(
        (item.quantity || 1) * (item.unitPrice || 0)
      ).toFixed(2)}`;
      lines.push(leftRightAlign(qtyPrice, total));
      if (item.sku) {
        lines.push(`   SKU: ${item.sku}`);
      }
      lines.push("");
    });

    // Totals
    lines.push(separator());
    lines.push(
      leftRightAlign("Subtotal:", `KSh ${(data.subtotal || 0).toFixed(2)}`)
    );

    // Add shipping fee if present
    if (data.shippingFee && data.shippingFee > 0) {
      lines.push(
        leftRightAlign("Delivery Fee:", `KSh ${data.shippingFee.toFixed(2)}`)
      );
    }

    // Add tax if present
    if (data.tax && data.tax > 0) {
      lines.push(leftRightAlign("Tax:", `KSh ${data.tax.toFixed(2)}`));
    }

    lines.push(separator("="));
    lines.push(
      leftRightAlign("GRAND TOTAL:", `KSh ${(data.grandTotal || 0).toFixed(2)}`)
    );
    lines.push(separator("="));
    lines.push("");

    // Payment
    if (data.paymentMethods && data.paymentMethods.length > 0) {
      lines.push("PAYMENT DETAILS:");
      data.paymentMethods.forEach((method: any) => {
        lines.push(
          leftRightAlign(`${method.method}:`, `KSh ${method.amount.toFixed(2)}`)
        );
      });
      lines.push("");
    }

    // Loyalty (simplified)
    if (data.loyalty && data.loyalty.totalPoints) {
      lines.push(separator("-"));
      lines.push(centerText("LOYALTY REWARDS"));
      lines.push(
        leftRightAlign("Total TS Points:", data.loyalty.totalPoints.toString())
      );
      lines.push(separator("-"));
      lines.push("");
    }

    // Footer
    lines.push(centerText("You Are Treasured"));
    lines.push("");

    return lines.join("\n");
  }
}
