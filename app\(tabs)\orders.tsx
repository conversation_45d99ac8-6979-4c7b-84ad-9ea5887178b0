import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { PermissionGate } from "@/src/components/rbac";
import { Colors } from "@/constants/Colors";
import { useThemeColor } from "@/hooks/useThemeColor";
import { UnifiedReceiptManager } from "@/src/services/UnifiedReceiptManager";
import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useScreenNavigation } from "@/src/hooks/useScreenNavigation";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { Order } from "@/src/types/shopify";
import { router } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const OrdersScreen: React.FC = () => {
  const { isPosAuthenticated: isAuthenticated } = useSession();

  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
  }>({ title: "", message: "" });

  // Use enhanced navigation hook
  useScreenNavigation({
    title: "Orders",
    forceTitle: true,
  });

  // RBAC permissions
  const { hasPermission } = useRBAC();

  // Theme system
  const theme = useTheme();
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const surfaceColor = useThemeColor({}, "surface");
  const successColor = useThemeColor({}, "success");
  const warningColor = useThemeColor({}, "warning");
  const errorColor = useThemeColor({}, "error");

  // Create styles using theme
  const styles = createStyles(theme);

  const loadOrders = useCallback(async () => {
    setIsLoading(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreOrders({ limit: 50 });

      if (response.success && response.data) {
        setOrders(response.data.orders || []);
      } else {
        setModalData({
          title: "Error",
          message: "Failed to load orders",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Failed to load orders:", error);
      setModalData({
        title: "Error",
        message: "Failed to load orders",
      });
      setShowErrorModal(true);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      loadOrders();
    }
  }, [isAuthenticated, loadOrders]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  // Handle print receipt - uses unified receipt system for consistent data display
  const handlePrintReceipt = async (order: any) => {
    try {
      // Use UnifiedReceiptManager for consistent receipt generation with all data
      const unifiedResult = await UnifiedReceiptManager.generateReceipt(order, {
        format: "thermal",
        autoPrint: true,
        printerType: "thermal",
      });

      if (unifiedResult.success && unifiedResult.printed) {
        // Unified printing successful
        setModalData({
          title: "Print Successful",
          message:
            "Receipt printed successfully with all order details including discounts, loyalty points, and shipping fees.",
        });
        setShowSuccessModal(true);
      } else {
        // Unified printing failed
        setModalData({
          title: "Print Error",
          message: `Failed to print receipt: ${
            unifiedResult.error || "Unknown error"
          }\n\nPlease check your printer connection and try again.`,
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Print receipt error:", error);
      setModalData({
        title: "Print Error",
        message:
          "Failed to print receipt. Please check your printer connection and try again.",
      });
      setShowErrorModal(true);
    }
  };

  // Handle share receipt
  const handleShareReceipt = async (order: any) => {
    try {
      const result = await ReceiptGenerator.shareReceipt(order);

      if (result.success) {
        setModalData({
          title: "Success",
          message: "Receipt shared successfully!",
        });
        setShowSuccessModal(true);
      } else {
        setModalData({
          title: "Share Error",
          message: result.error || "Failed to share receipt",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Share receipt error:", error);
      setModalData({
        title: "Error",
        message: "Failed to share receipt",
      });
      setShowErrorModal(true);
    }
  };

  // Handle view receipt details
  const handleViewReceipt = (order: any) => {
    // Navigate to receipt preview with order data
    const orderDataString = encodeURIComponent(JSON.stringify(order));
    router.push(`/order-receipt?orderData=${orderDataString}`);
  };

  // Handle fulfillment management
  const handleManageFulfillment = (order: any) => {
    // Navigate to fulfillment details with order ID
    const orderId = order.id || order.order_id;
    router.push(`/fulfillment-details?orderId=${orderId}`);
  };

  // Handle edit order - for modifying order details
  const handleEditOrder = (order: any) => {
    // Navigate to edit order page with order data
    const orderDataString = encodeURIComponent(JSON.stringify(order));
    router.push(`/edit-order?orderData=${orderDataString}`);
  };

  // Handle collect payment - for payment collection on existing orders
  const handleCollectPayment = (order: any) => {
    // Navigate to collect payment page with order data
    const orderDataString = encodeURIComponent(JSON.stringify(order));
    router.push(`/collect-payment?orderData=${orderDataString}`);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "paid":
      case "fulfilled":
        return successColor;
      case "pending":
      case "partial":
        return warningColor;
      case "refunded":
      case "cancelled":
        return errorColor;
      default:
        return textSecondary;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  };

  const renderOrder = ({ item: order }: { item: any }) => {
    // Handle both camelCase (frontend) and snake_case (backend) field names
    const lineItems = order.lineItems || order.line_items || [];
    const orderNumber = order.orderNumber || order.number || order.name;
    const totalPrice = order.totalPrice || order.total_price;
    const financialStatus = order.financialStatus || order.financial_status;
    const fulfillmentStatus =
      order.fulfillmentStatus || order.fulfillment_status;
    const createdAt = order.createdAt || order.created_at;

    return (
      <ModernCard style={styles.orderCard} variant="elevated">
        <View style={styles.orderHeader}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderNumber, { color: textColor }]}>
              Order #{orderNumber}
            </Text>
            <Text style={[styles.orderDate, { color: textSecondary }]}>
              {formatDate(createdAt)}
            </Text>
          </View>

          <View style={styles.orderStatus}>
            <View
              style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(financialStatus) },
              ]}
            >
              <Text style={styles.statusText}>
                {financialStatus?.toUpperCase() || "UNKNOWN"}
              </Text>
            </View>
            <Text style={[styles.orderTotal, { color: primaryColor }]}>
              KSh {parseFloat(totalPrice || "0").toFixed(2)}
            </Text>
          </View>
        </View>

        {/* Customer Info */}
        {order.customer && (
          <View style={styles.customerInfo}>
            <IconSymbol name="person.fill" size={16} color={textSecondary} />
            <Text style={[styles.customerName, { color: textColor }]}>
              {order.customer.firstName} {order.customer.lastName}
            </Text>
            {order.customer.email && (
              <Text style={[styles.customerEmail, { color: textSecondary }]}>
                {order.customer.email}
              </Text>
            )}
          </View>
        )}

        {/* Staff Info */}
        <View style={styles.staffInfo}>
          {order.salespersonName && (
            <View style={styles.staffItem}>
              <IconSymbol
                name="person.badge.key"
                size={16}
                color={textSecondary}
              />
              <Text style={[styles.staffText, { color: textSecondary }]}>
                Staff: {order.salespersonName}
              </Text>
            </View>
          )}

          {order.salespersonId && (
            <View style={styles.staffItem}>
              <IconSymbol
                name="person.badge.plus"
                size={16}
                color={textSecondary}
              />
              <Text style={[styles.staffText, { color: textSecondary }]}>
                Agent: {order.salespersonName}
              </Text>
            </View>
          )}
        </View>

        {/* Line Items */}
        <View style={styles.lineItems}>
          <Text style={[styles.itemsTitle, { color: textColor }]}>
            Items ({lineItems.length})
          </Text>
          {lineItems.slice(0, 3).map((item: any) => (
            <View key={item.id} style={styles.lineItem}>
              <Text
                style={[styles.itemTitle, { color: textColor }]}
                numberOfLines={1}
              >
                {item.title}
                {item.variantTitle && item.variantTitle !== "Default Title" && (
                  <Text style={[styles.itemVariant, { color: textSecondary }]}>
                    {" "}
                    ({item.variantTitle})
                  </Text>
                )}
              </Text>
              <Text style={[styles.itemDetails, { color: textSecondary }]}>
                {item.quantity} × KSh {parseFloat(item.price).toFixed(2)}
              </Text>
            </View>
          ))}
          {lineItems.length > 3 && (
            <Text style={[styles.moreItems, { color: textSecondary }]}>
              +{lineItems.length - 3} more items
            </Text>
          )}
        </View>

        {/* Fulfillment Status */}
        {fulfillmentStatus && (
          <View style={styles.fulfillmentStatus}>
            <Text style={[styles.fulfillmentLabel, { color: textSecondary }]}>
              Fulfillment:
            </Text>
            <View
              style={[
                styles.statusBadge,
                { backgroundColor: getStatusColor(fulfillmentStatus) },
              ]}
            >
              <Text style={styles.statusText}>
                {fulfillmentStatus?.toUpperCase() || "PENDING"}
              </Text>
            </View>
          </View>
        )}

        {/* Receipt Actions */}
        <View style={styles.receiptActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: primaryColor }]}
            onPress={() => handleViewReceipt(order)}
          >
            <IconSymbol name="eye.fill" size={16} color="white" />
            <Text style={styles.actionButtonText}>View</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: Colors.light.success },
            ]}
            onPress={() => handlePrintReceipt(order)}
          >
            <IconSymbol name="printer.fill" size={16} color="white" />
            <Text style={styles.actionButtonText}>Print</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: "#3498db" }]}
            onPress={() => handleShareReceipt(order)}
          >
            <IconSymbol
              name="square.and.arrow.up.fill"
              size={16}
              color="white"
            />
            <Text style={styles.actionButtonText}>Share</Text>
          </TouchableOpacity>

          {/* Fulfillment Action */}
          <PermissionGate requiredPermissions={["view_fulfillments"]}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: "#FF9500" }]}
              onPress={() => handleManageFulfillment(order)}
            >
              <IconSymbol name="shippingbox.fill" size={16} color="white" />
              <Text style={styles.actionButtonText}>Fulfill</Text>
            </TouchableOpacity>
          </PermissionGate>
        </View>

        {/* Order Management Actions */}
        <View style={styles.orderManagementActions}>
          {/* Edit Order Button */}
          <PermissionGate requiredPermissions={["edit_orders"]}>
            <TouchableOpacity
              style={[styles.managementButton, { backgroundColor: "#8E44AD" }]}
              onPress={() => handleEditOrder(order)}
            >
              <IconSymbol name="pencil.circle.fill" size={16} color="white" />
              <Text style={styles.managementButtonText}>Edit Order</Text>
            </TouchableOpacity>
          </PermissionGate>

          {/* Collect Payment Button - Show only for unpaid/partially paid orders */}
          {(financialStatus === "pending" ||
            financialStatus === "partially_paid" ||
            financialStatus === "authorized") && (
            <PermissionGate requiredPermissions={["collect_payments"]}>
              <TouchableOpacity
                style={[styles.managementButton, { backgroundColor: "#27AE60" }]}
                onPress={() => handleCollectPayment(order)}
              >
                <IconSymbol name="creditcard.fill" size={16} color="white" />
                <Text style={styles.managementButtonText}>Collect Payment</Text>
              </TouchableOpacity>
            </PermissionGate>
          )}
        </View>
      </ModernCard>
    );
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: surfaceColor }]}>
        <Text style={[styles.headerTitle, { color: textColor }]}>
          Order History
        </Text>
        <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
          {orders.length} {orders.length === 1 ? "order" : "orders"}
        </Text>
      </View>

      {/* Orders List */}
      {isLoading && orders.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            Loading orders...
          </Text>
        </View>
      ) : orders.length === 0 ? (
        <View style={styles.emptyContainer}>
          <IconSymbol name="list.bullet" size={64} color={textSecondary} />
          <Text style={[styles.emptyTitle, { color: textColor }]}>
            No orders found
          </Text>
          <Text style={[styles.emptySubtitle, { color: textSecondary }]}>
            Orders will appear here once customers make purchases
          </Text>
        </View>
      ) : (
        <FlatList
          data={orders}
          renderItem={renderOrder}
          keyExtractor={(item) => item.id}
          style={styles.ordersList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[primaryColor]}
              tintColor={primaryColor}
            />
          }
          contentContainerStyle={styles.listContent}
        />
      )}

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowErrorModal(false)}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowSuccessModal(false)}
      />
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      // Add layout stability constraints
      minHeight: 0,
      maxHeight: "100%",
      overflow: "hidden",
    },
    header: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: Colors.light.border,
    },
    headerTitle: {
      ...theme.typography.h3,
      marginBottom: 4,
    },
    headerSubtitle: {
      ...theme.typography.caption,
    },
    ordersList: {
      flex: 1,
      padding: theme.spacing.md,
      // Prevent list expansion beyond container
      minHeight: 0,
      maxHeight: "100%",
    },
    listContent: {
      paddingBottom: theme.spacing.lg,
    },
    orderCard: {
      marginBottom: theme.spacing.md,
    },
    orderHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: theme.spacing.md,
    },
    orderInfo: {
      flex: 1,
    },
    orderNumber: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
    },
    orderDate: {
      ...theme.typography.caption,
    },
    orderStatus: {
      alignItems: "flex-end",
    },
    statusBadge: {
      borderRadius: 12,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      marginBottom: 4,
    },
    statusText: {
      ...theme.typography.small,
      color: "#FFFFFF",
      fontWeight: "600",
    },
    orderTotal: {
      ...theme.typography.h3,
    },
    customerInfo: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.md,
      gap: theme.spacing.sm,
    },
    customerName: {
      ...theme.typography.bodyMedium,
    },
    customerEmail: {
      ...theme.typography.caption,
      marginLeft: "auto",
    },
    staffInfo: {
      marginBottom: theme.spacing.md,
    },
    staffItem: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.sm,
      marginBottom: 4,
    },
    staffText: {
      ...theme.typography.caption,
    },
    lineItems: {
      marginBottom: theme.spacing.md,
    },
    itemsTitle: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.sm,
    },
    lineItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 4,
    },
    itemTitle: {
      ...theme.typography.body,
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    itemVariant: {
      ...theme.typography.caption,
    },
    itemDetails: {
      ...theme.typography.caption,
    },
    moreItems: {
      ...theme.typography.caption,
      fontStyle: "italic",
      marginTop: 4,
    },
    fulfillmentStatus: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    fulfillmentLabel: {
      ...theme.typography.caption,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    loadingText: {
      ...theme.typography.body,
      marginTop: theme.spacing.md,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    emptyTitle: {
      ...theme.typography.h3,
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    emptySubtitle: {
      ...theme.typography.body,
      textAlign: "center",
    },
    receiptActions: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: theme.spacing.sm,
      marginTop: theme.spacing.md,
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: Colors.light.border,
    },
    actionButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.sm,
      borderRadius: 6,
      gap: 4,
    },
    actionButtonText: {
      ...theme.typography.small,
      color: "white",
      fontWeight: "600",
    },
    orderManagementActions: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: theme.spacing.sm,
      marginTop: theme.spacing.sm,
      paddingTop: theme.spacing.sm,
      borderTopWidth: 1,
      borderTopColor: Colors.light.border,
    },
    managementButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.sm,
      borderRadius: 6,
      gap: 4,
    },
    managementButtonText: {
      ...theme.typography.small,
      color: "white",
      fontWeight: "600",
    },
  });

export default OrdersScreen;
