// Shopify data types for the mobile app
export interface ShopifyStore {
  id: string;
  domain: string;
  name: string;
  email: string;
  currency: string;
  timezone: string;
  isConnected: boolean;
  lastSyncAt?: string;
  phone?: string;
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    country?: string;
    zip?: string;
  };
}

export interface Product {
  id: string;
  shopifyId: string;
  title: string;
  description: string;
  handle: string;
  productType: string;
  vendor: string;
  tags: string[];
  images: ProductImage[];
  variants: ProductVariant[];
  createdAt: string;
  updatedAt: string;
  lastSyncedAt?: string;
}

export interface ProductVariant {
  id: string;
  shopifyId: string;
  productId: string;
  title: string;
  price: string;
  compareAtPrice?: string;
  sku?: string;
  barcode?: string;
  inventoryQuantity: number;
  weight?: number;
  weightUnit?: string;
  requiresShipping: boolean;
  taxable: boolean;
  inventoryPolicy: "deny" | "continue";
  fulfillmentService: string;
  inventoryManagement?: string;
  position: number;
  createdAt: string;
  updatedAt: string;
}

export interface ProductImage {
  id: string;
  shopifyId: string;
  productId: string;
  src: string;
  url: string; // Add url property for compatibility
  altText?: string;
  width?: number;
  height?: number;
  position: number;
}

export interface Customer {
  id: string;
  shopifyId?: string;
  firstName: string;
  lastName?: string; // Can be null from backend
  displayName?: string; // Provided by backend
  email?: string;
  phone?: string;
  addresses: CustomerAddress[];
  ordersCount: number | string; // Backend returns string
  totalSpent: string;
  tags: string[];
  acceptsMarketing?: boolean; // Not always provided
  createdAt: string;
  updatedAt: string;
  lastSyncedAt?: string;
  note?: string; // Backend provides note
  verifiedEmail?: boolean; // Backend provides this
  state?: string; // Backend provides state
  loyaltyData?: CustomerLoyaltyData; // Optional loyalty data from API
}

export interface CustomerAddress {
  id: string;
  firstName?: string;
  lastName?: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  province?: string;
  country: string;
  zip: string;
  phone?: string;
  isDefault: boolean;
}

export interface Order {
  id: string;
  shopifyId?: string;
  orderNumber?: string;
  name?: string; // Add name property for Shopify order names
  customer?: Customer;
  lineItems: OrderLineItem[];
  shippingAddress?: CustomerAddress;
  billingAddress?: CustomerAddress;
  subtotalPrice: string;
  totalTax: string;
  totalPrice: string;
  currency: string;
  financialStatus: OrderFinancialStatus;
  fulfillmentStatus: OrderFulfillmentStatus;
  discounts: OrderDiscount[];
  customAttributes: OrderAttribute[];
  note?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  isSynced: boolean;
  salespersonId?: string;
  salespersonName?: string;
  commissionRate?: number;
}

export interface OrderLineItem {
  id: string;
  variantId: string;
  productId: string;
  title: string;
  variantTitle?: string;
  sku?: string;
  quantity: number;
  price: string;
  totalDiscount: string;
  taxLines: TaxLine[];
  customAttributes: OrderAttribute[];
}

export interface OrderDiscount {
  id: string;
  code?: string;
  amount: string;
  type: "percentage" | "fixed_amount";
  description?: string;
}

export interface OrderAttribute {
  key: string;
  value: string;
}

export interface TaxLine {
  title: string;
  price: string;
  rate: number;
}

export type OrderFinancialStatus =
  | "pending"
  | "authorized"
  | "partially_paid"
  | "paid"
  | "partially_refunded"
  | "refunded"
  | "voided";

export type OrderFulfillmentStatus =
  | "fulfilled"
  | "null"
  | "partial"
  | "restocked";

// POS specific types
export interface CartItemDiscount {
  type: "percentage" | "fixed_amount";
  amount: number;
  description?: string;
}

export interface CartItem {
  variantId: string;
  productId: string;
  title: string;
  variantTitle?: string;
  price: string;
  originalPrice?: number; // Store original price when price is changed
  quantity: number;
  image?: string;
  sku?: string;
  inventoryQuantity: number;
  discount?: CartItemDiscount;
  notes?: string;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  tax: number;
  total: number;
  discounts: OrderDiscount[];
  customer?: Customer;
  note?: string;
}

export interface Salesperson {
  id: string;
  name: string;
  email: string;
  commissionRate: number;
  isActive: boolean;
}

// API Response types
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Loyalty System Types
export type LoyaltyTier = "bronze" | "silver" | "gold" | "platinum";

export interface CustomerLoyaltyData {
  customerId: string;
  loyaltyPoints: number;
  tier: LoyaltyTier;
  tierBenefits: {
    multiplier: number;
    pointsPerKsh: number;
  };
  totalPurchases: number;
  totalOrders: number;
  lastPurchase?: string;
  memberSince: string;
  progressToNextTier?: {
    currentAmount: number;
    requiredAmount: number;
    percentage: number;
  };
  redemptionInfo: {
    pointsPerKsh: number;
    availableDiscount: number;
    minRedemption: number;
  };
}

export interface LoyaltyTransaction {
  id: string;
  customerId: string;
  type: "earned" | "redeemed" | "expired" | "adjusted";
  points: number;
  description: string;
  orderId?: string;
  ticketId?: string;
  staffId?: string;
  staffName?: string; // Enriched staff name from API
  agentName?: string; // Enriched agent name from API
  createdAt: string;
  expiresAt?: string | null; // Expiration date if applicable
  metadata?: {
    orderTotal?: number;
    discountAmount?: number;
    previousBalance?: number;
    newBalance?: number;
  };
}

export interface LoyaltyDiscountCalculation {
  tier: {
    percentage: number;
    amount: number;
    tierName: LoyaltyTier;
  };
  points: {
    availablePoints: number;
    maxRedeemablePoints: number;
    maxDiscount: number;
    exchangeRate: number;
  };
  combined: {
    maxTotalDiscount: number;
    maxDiscountPercentage: number;
  };
}

export interface PointsRedemptionRequest {
  pointsToRedeem: number;
  orderId?: string;
  ticketId?: string;
}

export interface PointsRedemptionResult {
  pointsRedeemed: number;
  discountAmount: number;
  newBalance: number;
  redemptionId: string;
  transactionId: string;
}

export interface LoyaltyLeaderboardEntry {
  customerId: string;
  loyaltyPoints: number;
  loyaltyTier: LoyaltyTier;
  totalPurchases: number;
  totalOrders: number;
  lastPurchaseAt?: string;
  createdAt: string;
}

// Sync related types
export interface SyncOperation {
  id: string;
  type: "product_sync" | "order_sync" | "customer_sync" | "inventory_sync";
  status: "pending" | "in_progress" | "completed" | "failed";
  data: any;
  createdAt: string;
  completedAt?: string;
  error?: string;
  retryCount: number;
}

export interface SyncStatus {
  isOnline: boolean;
  lastSyncAt?: string;
  pendingOperations: number;
  failedOperations: number;
}
