/**
 * Test Actual User Switching
 */

const axios = require("axios");

async function testSwitchUser() {
  try {
    console.log("🔐 Logging in...");
    const loginResponse = await axios.post("http://localhost:3020/api/pos/login", {
      username: "admin1",
      password: "admin123"
    });

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log("✅ Login successful");

    // Get available staff
    const staffResponse = await axios.get("http://localhost:3020/api/pos/user-switching/available-staff", { headers });
    const targetStaff = staffResponse.data.data.staff[0]; // manager1

    console.log(`🔄 Testing user switch to ${targetStaff.username}...`);
    const switchResponse = await axios.post("http://localhost:3020/api/pos/user-switching/switch-user", {
      targetStaffId: targetStaff.id,
      pin: "1234",
      reason: "testing"
    }, { headers });

    if (switchResponse.data.success) {
      console.log("✅ User switch successful");
      console.log(`   Current user: ${switchResponse.data.data.currentUser.name}`);
    } else {
      console.log("❌ User switch failed:", switchResponse.data.error);
      return;
    }

    // Test session context
    console.log("📋 Testing session context...");
    const contextResponse = await axios.get("http://localhost:3020/api/pos/user-switching/session-context", { headers });

    if (contextResponse.data.success) {
      console.log("✅ Session context retrieved");
      console.log(`   Has active switch: ${contextResponse.data.data.hasActiveSwitch}`);
      console.log(`   Can switch back: ${contextResponse.data.data.canSwitchBack}`);
    } else {
      console.log("❌ Session context failed:", contextResponse.data.error);
    }

    // Test switch back
    console.log("🔙 Testing switch back...");
    const switchBackResponse = await axios.post("http://localhost:3020/api/pos/user-switching/switch-back", {}, { headers });

    if (switchBackResponse.data.success) {
      console.log("✅ Switch back successful");
      console.log(`   Back to user: ${switchBackResponse.data.data.currentUser.name}`);
    } else {
      console.log("❌ Switch back failed:", switchBackResponse.data.error);
    }

    console.log("🎉 User switching advanced tests completed!");

  } catch (error) {
    console.error("❌ Test failed:", error.response?.data?.error || error.message);
  }
}

testSwitchUser();
