/**
 * Customer Loyalty Overview Component
 *
 * Displays comprehensive loyalty information for a customer including
 * tier status, points balance, tier progression, and redemption info.
 */

import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import { ModernCard } from "@/components/ui/ModernCard";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { CustomerLoyaltyData } from "@/src/components/loyalty/CustomerLoyaltyCard";
import { formatCurrency } from "@/src/utils/currencyUtils";

interface CustomerLoyaltyOverviewProps {
  loyaltyData: CustomerLoyaltyData | null;
  loading?: boolean;
}

export const CustomerLoyaltyOverview: React.FC<
  CustomerLoyaltyOverviewProps
> = ({ loyaltyData, loading = false }) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  // Helper function to get tier badge color
  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case "bronze":
        return "#CD7F32";
      case "silver":
        return "#C0C0C0";
      case "gold":
        return "#FFD700";
      case "platinum":
        return "#E5E4E2";
      default:
        return theme.colors.textSecondary;
    }
  };

  if (loading) {
    return (
      <ModernCard style={styles.card}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Loyalty Information
        </Text>
        <Text
          style={[styles.loadingText, { color: theme.colors.textSecondary }]}
        >
          Loading loyalty data...
        </Text>
      </ModernCard>
    );
  }

  if (!loyaltyData) {
    return (
      <ModernCard style={styles.card}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Loyalty Information
        </Text>
        <Text
          style={[styles.noDataText, { color: theme.colors.textSecondary }]}
        >
          No loyalty data available for this customer.
        </Text>
      </ModernCard>
    );
  }

  const progressPercentage = loyaltyData.progressToNextTier?.percentage || 0;

  return (
    <>
      {/* Loyalty Status Card */}
      <ModernCard style={styles.loyaltyCard}>
        <View style={styles.loyaltyHeader}>
          <View style={styles.loyaltyTierBadge}>
            <View
              style={[
                styles.tierBadge,
                { backgroundColor: getTierBadgeColor(loyaltyData.tier) },
              ]}
            >
              <IconSymbol name="star.fill" size={16} color="#FFFFFF" />
              <Text style={styles.tierBadgeText}>
                {loyaltyData.tier.charAt(0).toUpperCase() +
                  loyaltyData.tier.slice(1)}
              </Text>
            </View>
          </View>
          <View style={styles.loyaltyPoints}>
            <Text style={[styles.pointsValue, { color: theme.colors.primary }]}>
              {(loyaltyData.loyaltyPoints || 0).toLocaleString()}
            </Text>
            <Text
              style={[
                styles.pointsLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Loyalty Points
            </Text>
          </View>
        </View>

        {/* Tier Progress */}
        {loyaltyData.progressToNextTier && (
          <View style={styles.progressSection}>
            <Text style={[styles.progressTitle, { color: theme.colors.text }]}>
              Progress to Next Tier
            </Text>
            <View
              style={[
                styles.progressBar,
                { backgroundColor: theme.colors.border },
              ]}
            >
              <View
                style={[
                  styles.progressFill,
                  {
                    backgroundColor: theme.colors.primary,
                    width: `${progressPercentage}%`,
                  },
                ]}
              />
            </View>
            <Text
              style={[
                styles.progressText,
                { color: theme.colors.textSecondary },
              ]}
            >
              {formatCurrency(loyaltyData.progressToNextTier.currentAmount)} /{" "}
              {formatCurrency(loyaltyData.progressToNextTier.requiredAmount)}
            </Text>
          </View>
        )}

        {/* Loyalty Stats */}
        <View style={styles.loyaltyStats}>
          <View style={styles.loyaltyStatItem}>
            <Text
              style={[styles.loyaltyStatValue, { color: theme.colors.text }]}
            >
              {loyaltyData.totalOrders}
            </Text>
            <Text
              style={[
                styles.loyaltyStatLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Total Orders
            </Text>
          </View>
          <View style={styles.loyaltyStatItem}>
            <Text
              style={[styles.loyaltyStatValue, { color: theme.colors.success }]}
            >
              {formatCurrency(loyaltyData.totalPurchases)}
            </Text>
            <Text
              style={[
                styles.loyaltyStatLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Total Spent
            </Text>
          </View>
          <View style={styles.loyaltyStatItem}>
            <Text
              style={[styles.loyaltyStatValue, { color: theme.colors.primary }]}
            >
              {loyaltyData.tierBenefits.multiplier}x
            </Text>
            <Text
              style={[
                styles.loyaltyStatLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Points Multiplier
            </Text>
          </View>
        </View>
      </ModernCard>

      {/* Redemption Info Card */}
      <ModernCard style={styles.card}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Points Redemption
        </Text>
        <View style={styles.infoRow}>
          <Text
            style={[styles.infoLabel, { color: theme.colors.textSecondary }]}
          >
            Available Discount:
          </Text>
          <Text style={[styles.infoValue, { color: theme.colors.success }]}>
            {formatCurrency(loyaltyData.redemptionInfo.availableDiscount)}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text
            style={[styles.infoLabel, { color: theme.colors.textSecondary }]}
          >
            Points per KSh:
          </Text>
          <Text style={[styles.infoValue, { color: theme.colors.text }]}>
            {loyaltyData.redemptionInfo.pointsPerKsh}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text
            style={[styles.infoLabel, { color: theme.colors.textSecondary }]}
          >
            Minimum Redemption:
          </Text>
          <Text style={[styles.infoValue, { color: theme.colors.text }]}>
            {formatCurrency(loyaltyData.redemptionInfo.minRedemption)}
          </Text>
        </View>
      </ModernCard>
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    padding: 20,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  loadingText: {
    textAlign: "center",
    fontStyle: "italic",
  },
  noDataText: {
    textAlign: "center",
    fontStyle: "italic",
  },
  loyaltyCard: {
    padding: 20,
    marginBottom: 16,
  },
  loyaltyHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  loyaltyTierBadge: {
    flex: 1,
  },
  tierBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
    alignSelf: "flex-start",
  },
  tierBadgeText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  loyaltyPoints: {
    alignItems: "flex-end",
  },
  pointsValue: {
    fontSize: 24,
    fontWeight: "700",
  },
  pointsLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  progressSection: {
    marginBottom: 20,
  },
  progressTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 6,
  },
  progressFill: {
    height: "100%",
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    textAlign: "center",
  },
  loyaltyStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
  },
  loyaltyStatItem: {
    alignItems: "center",
  },
  loyaltyStatValue: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  loyaltyStatLabel: {
    fontSize: 11,
    textAlign: "center",
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  infoValue: {
    fontSize: 14,
    fontWeight: "600",
  },
});
