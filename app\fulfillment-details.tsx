/**
 * Fulfillment Details Screen
 *
 * Detailed view and management interface for individual fulfillments.
 * Allows staff to view, edit, and update fulfillment information with proper RBAC.
 */

import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { PermissionGate } from "@/src/components/rbac";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  useCalculateShippingFee,
  useCreateFulfillment,
  useFulfillment,
  useShippingRates,
  useUpdateDeliveryDetails,
  useUpdateFulfillmentStatus,
} from "@/src/hooks/useFulfillment";
import { useRBAC } from "@/src/hooks/useRBAC";
import { createStyleUtils } from "@/src/utils/themeUtils";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

export default function FulfillmentDetailsScreen() {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const router = useRouter();
  const { hasPermission } = useRBAC();
  const { fulfillmentId, orderId } = useLocalSearchParams<{
    fulfillmentId?: string;
    orderId?: string;
  }>();

  // Check permissions
  const canViewFulfillments = hasPermission("view_fulfillments");
  const canManageFulfillments = hasPermission("manage_fulfillments");
  const canCreateFulfillments = hasPermission("create_fulfillments");
  const canManageDelivery = hasPermission("manage_delivery_details");

  // State management
  const [isEditing, setIsEditing] = useState(!fulfillmentId);
  const [deliveryMethod, setDeliveryMethod] = useState("standard");
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [deliveryPhone, setDeliveryPhone] = useState("");
  const [deliveryInstructions, setDeliveryInstructions] = useState("");
  const [calculatedFee, setCalculatedFee] = useState<number>(0);

  // Modal state
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalData, setModalData] = useState({
    title: "",
    message: "",
  });

  // React Query hooks
  const { data: fulfillment, isLoading: fulfillmentLoading } = useFulfillment(
    fulfillmentId || ""
  );
  const { data: shippingRates, isLoading: ratesLoading } = useShippingRates();
  const createFulfillmentMutation = useCreateFulfillment();
  const updateDeliveryMutation = useUpdateDeliveryDetails();
  const updateStatusMutation = useUpdateFulfillmentStatus();
  const calculateShippingMutation = useCalculateShippingFee();

  // Create theme-aware styles
  const styles = createStyles(theme);

  if (!canViewFulfillments) {
    return (
      <ScreenWrapper title="Fulfillment Details" showBackButton>
        <ThemedView style={styles.container}>
          <View style={styles.errorContainer}>
            <IconSymbol
              name="exclamationmark.triangle"
              size={48}
              color={theme.colors.error}
            />
            <ThemedText variant="h3" style={utils.mt("md")}>
              Access Denied
            </ThemedText>
            <ThemedText variant="body" color="secondary" style={utils.mt("sm")}>
              You don't have permission to view fulfillment details.
            </ThemedText>
          </View>
        </ThemedView>
      </ScreenWrapper>
    );
  }

  const handleCalculateShipping = async () => {
    try {
      const result = await calculateShippingMutation.mutateAsync({
        deliveryMethod,
        distanceKm: 10, // Default distance
        weightKg: 1, // Default weight
      });
      setCalculatedFee(result.shippingFee);
    } catch (error) {
      setModalData({
        title: "Error",
        message: "Failed to calculate shipping fee",
      });
      setShowErrorModal(true);
    }
  };

  const handleCreateFulfillment = async () => {
    if (!orderId) {
      setModalData({
        title: "Error",
        message: "Order ID is required",
      });
      setShowErrorModal(true);
      return;
    }

    try {
      await createFulfillmentMutation.mutateAsync({
        orderId,
        deliveryMethod,
        deliveryAddress: {
          street: deliveryAddress,
          city: "Nairobi",
          country: "Kenya",
        },
        deliveryContactPhone: deliveryPhone,
        deliveryInstructions,
        shippingFee: calculatedFee,
        shippingFeeCurrency: "KES",
      });
      setModalData({
        title: "Success",
        message: "Fulfillment created successfully",
      });
      setShowSuccessModal(true);
    } catch (error) {
      setModalData({
        title: "Error",
        message: "Failed to create fulfillment",
      });
      setShowErrorModal(true);
    }
  };

  const handleUpdateDelivery = async () => {
    if (!fulfillmentId) return;

    try {
      await updateDeliveryMutation.mutateAsync({
        fulfillmentId,
        updateData: {
          deliveryAddress: {
            street: deliveryAddress,
            city: "Nairobi",
            country: "Kenya",
          },
          deliveryContactPhone: deliveryPhone,
          deliveryInstructions,
        },
      });
      setIsEditing(false);
      Alert.alert("Success", "Delivery details updated successfully");
    } catch (error) {
      Alert.alert("Error", "Failed to update delivery details");
    }
  };

  const handleStatusUpdate = async (newStatus: string) => {
    if (!fulfillmentId) return;

    try {
      await updateStatusMutation.mutateAsync({
        fulfillmentId,
        status: newStatus,
      });
      Alert.alert("Success", `Status updated to ${newStatus}`);
    } catch (error) {
      Alert.alert("Error", "Failed to update status");
    }
  };

  if (fulfillmentLoading || ratesLoading) {
    return (
      <ScreenWrapper title="Fulfillment Details" showBackButton>
        <ThemedView style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <ThemedText variant="body" style={utils.mt("md")}>
              Loading fulfillment details...
            </ThemedText>
          </View>
        </ThemedView>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper title="Fulfillment Details" showBackButton>
      <ThemedView style={styles.container}>
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <ModernCard style={utils.mb("lg")}>
            <View style={styles.header}>
              <View>
                <ThemedText variant="h2">
                  {fulfillmentId ? "Fulfillment Details" : "Create Fulfillment"}
                </ThemedText>
                {fulfillmentId && (
                  <ThemedText variant="small" color="secondary">
                    ID: {fulfillmentId.slice(-8)}
                  </ThemedText>
                )}
              </View>
              {fulfillment && (
                <View style={styles.statusBadge}>
                  <ThemedText variant="small" style={styles.statusText}>
                    {fulfillment.fulfillmentStatus?.toUpperCase() || "PENDING"}
                  </ThemedText>
                </View>
              )}
            </View>
          </ModernCard>

          {/* Delivery Method Selection */}
          <ModernCard style={utils.mb("lg")}>
            <ThemedText variant="h3" style={utils.mb("md")}>
              Delivery Method
            </ThemedText>
            <View style={styles.methodGrid}>
              {shippingRates?.map((rate) => (
                <TouchableOpacity
                  key={rate.id}
                  style={[
                    styles.methodCard,
                    deliveryMethod === rate.deliveryMethod &&
                      styles.selectedMethod,
                  ]}
                  onPress={() => setDeliveryMethod(rate.deliveryMethod)}
                  disabled={!isEditing}
                >
                  <ThemedText variant="bodyMedium">
                    {rate.deliveryMethod?.toUpperCase() || "STANDARD"}
                  </ThemedText>
                  <ThemedText variant="small" color="primary">
                    KES {rate.baseFee}
                  </ThemedText>
                  <ThemedText variant="small" color="secondary">
                    {rate.description}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </View>
          </ModernCard>

          {/* Shipping Fee Calculation */}
          <ModernCard style={utils.mb("lg")}>
            <View style={styles.feeHeader}>
              <ThemedText variant="h3">Shipping Fee</ThemedText>
              <TouchableOpacity
                style={styles.calculateButton}
                onPress={handleCalculateShipping}
                disabled={calculateShippingMutation.isPending || !isEditing}
              >
                <ThemedText variant="small" style={styles.calculateButtonText}>
                  {calculateShippingMutation.isPending
                    ? "Calculating..."
                    : "Calculate"}
                </ThemedText>
              </TouchableOpacity>
            </View>
            {calculatedFee > 0 && (
              <ThemedText variant="h2" color="success" style={utils.mt("md")}>
                KES {calculatedFee.toLocaleString()}
              </ThemedText>
            )}
          </ModernCard>

          {/* Delivery Details */}
          <ModernCard style={utils.mb("lg")}>
            <View style={styles.sectionHeader}>
              <ThemedText variant="h3">Delivery Details</ThemedText>
              <PermissionGate requiredPermissions={["manage_delivery_details"]}>
                {!isEditing && fulfillmentId && (
                  <TouchableOpacity
                    style={styles.editButton}
                    onPress={() => setIsEditing(true)}
                  >
                    <IconSymbol
                      name="pencil"
                      size={16}
                      color={theme.colors.primary}
                    />
                    <ThemedText
                      variant="small"
                      color="primary"
                      style={utils.ml("xs")}
                    >
                      Edit
                    </ThemedText>
                  </TouchableOpacity>
                )}
              </PermissionGate>
            </View>

            <View style={styles.inputGroup}>
              <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
                Delivery Address
              </ThemedText>
              <TextInput
                style={[styles.input, { color: theme.colors.text }]}
                value={deliveryAddress}
                onChangeText={setDeliveryAddress}
                placeholder="Enter delivery address"
                placeholderTextColor={theme.colors.textSecondary}
                editable={isEditing}
                multiline
              />
            </View>

            <View style={styles.inputGroup}>
              <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
                Contact Phone
              </ThemedText>
              <TextInput
                style={[styles.input, { color: theme.colors.text }]}
                value={deliveryPhone}
                onChangeText={setDeliveryPhone}
                placeholder="Enter contact phone"
                placeholderTextColor={theme.colors.textSecondary}
                editable={isEditing}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
                Delivery Instructions
              </ThemedText>
              <TextInput
                style={[styles.input, { color: theme.colors.text }]}
                value={deliveryInstructions}
                onChangeText={setDeliveryInstructions}
                placeholder="Enter delivery instructions"
                placeholderTextColor={theme.colors.textSecondary}
                editable={isEditing}
                multiline
              />
            </View>
          </ModernCard>

          {/* Status Management */}
          {fulfillment && (
            <PermissionGate requiredPermissions={["manage_fulfillments"]}>
              <ModernCard style={utils.mb("lg")}>
                <ThemedText variant="h3" style={utils.mb("md")}>
                  Status Management
                </ThemedText>
                <View style={styles.statusButtons}>
                  {["processing", "shipped", "delivered"].map((status) => (
                    <TouchableOpacity
                      key={status}
                      style={[
                        styles.statusButton,
                        fulfillment.fulfillmentStatus === status &&
                          styles.currentStatus,
                      ]}
                      onPress={() => handleStatusUpdate(status)}
                      disabled={fulfillment.fulfillmentStatus === status}
                    >
                      <ThemedText
                        variant="small"
                        style={[
                          styles.statusButtonText,
                          fulfillment.fulfillmentStatus === status &&
                            styles.currentStatusText,
                        ]}
                      >
                        {status.toUpperCase()}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </View>
              </ModernCard>
            </PermissionGate>
          )}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {isEditing && (
              <>
                {fulfillmentId ? (
                  <PermissionGate
                    requiredPermissions={["manage_delivery_details"]}
                  >
                    <TouchableOpacity
                      style={styles.primaryButton}
                      onPress={handleUpdateDelivery}
                      disabled={updateDeliveryMutation.isPending}
                    >
                      <ThemedText
                        variant="bodyMedium"
                        style={styles.primaryButtonText}
                      >
                        {updateDeliveryMutation.isPending
                          ? "Updating..."
                          : "Update Details"}
                      </ThemedText>
                    </TouchableOpacity>
                  </PermissionGate>
                ) : (
                  <PermissionGate requiredPermissions={["create_fulfillments"]}>
                    <TouchableOpacity
                      style={styles.primaryButton}
                      onPress={handleCreateFulfillment}
                      disabled={createFulfillmentMutation.isPending}
                    >
                      <ThemedText
                        variant="bodyMedium"
                        style={styles.primaryButtonText}
                      >
                        {createFulfillmentMutation.isPending
                          ? "Creating..."
                          : "Create Fulfillment"}
                      </ThemedText>
                    </TouchableOpacity>
                  </PermissionGate>
                )}
                <TouchableOpacity
                  style={styles.secondaryButton}
                  onPress={() => setIsEditing(false)}
                >
                  <ThemedText
                    variant="bodyMedium"
                    style={styles.secondaryButtonText}
                  >
                    Cancel
                  </ThemedText>
                </TouchableOpacity>
              </>
            )}
          </View>
        </ScrollView>

        {/* Success Modal */}
        <SuccessModal
          visible={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          title={modalData.title}
          message={modalData.message}
          actions={[
            {
              title: "Continue",
              onPress: () => {
                setShowSuccessModal(false);
                router.back();
              },
              variant: "primary",
              icon: "arrow.right",
            },
          ]}
        />

        {/* Error Modal */}
        <ErrorModal
          visible={showErrorModal}
          onClose={() => setShowErrorModal(false)}
          title={modalData.title}
          message={modalData.message}
        />
      </ThemedView>
    </ScreenWrapper>
  );
}

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    statusBadge: {
      backgroundColor: theme.colors.surfaceSecondary,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    statusText: {
      color: theme.colors.primary,
      fontWeight: "600",
    },
    methodGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 12,
    },
    methodCard: {
      flex: 1,
      minWidth: 150,
      padding: 12,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      borderWidth: 2,
      borderColor: "transparent",
      alignItems: "center",
    },
    selectedMethod: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.surfaceSecondary,
    },
    feeHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    calculateButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 6,
    },
    calculateButtonText: {
      color: "#FFFFFF",
      fontWeight: "600",
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 16,
    },
    editButton: {
      flexDirection: "row",
      alignItems: "center",
      padding: 8,
    },
    inputGroup: {
      marginBottom: 16,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      backgroundColor: theme.colors.surface,
    },
    statusButtons: {
      flexDirection: "row",
      gap: 12,
    },
    statusButton: {
      flex: 1,
      padding: 12,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      alignItems: "center",
    },
    currentStatus: {
      backgroundColor: theme.colors.successBackground,
    },
    statusButtonText: {
      fontWeight: "600",
    },
    currentStatusText: {
      color: theme.colors.success,
    },
    actionButtons: {
      gap: 12,
      marginBottom: 32,
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
      padding: 16,
      borderRadius: 8,
      alignItems: "center",
    },
    primaryButtonText: {
      color: "#FFFFFF",
      fontWeight: "600",
    },
    secondaryButton: {
      backgroundColor: theme.colors.surface,
      padding: 16,
      borderRadius: 8,
      alignItems: "center",
    },
    secondaryButtonText: {
      color: theme.colors.textSecondary,
      fontWeight: "600",
    },
  });
