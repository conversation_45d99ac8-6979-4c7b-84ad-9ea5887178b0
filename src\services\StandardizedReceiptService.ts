/**
 * Standardized Receipt Service
 *
 * Centralized receipt generation system that follows the official Treasured Scents receipt format.
 * This service handles all receipt types (HTML, thermal, text) from a single source of truth.
 */

import { formatCurrency } from "@/src/utils/currencyUtils";
import {
  TREASURED_LOGO_TEXT,
  TREASURED_LOGO_BASE64,
} from "../constants/logoConstants";

export interface StandardizedReceiptData {
  // Receipt identification
  receiptNumber: string;
  date: string;
  time: string;

  // Store information
  store: {
    name: string;
    address: string;
    mobile: string;
    email: string;
    website: string;
  };

  // Staff information
  staff: {
    name: string;
    role: string;
  };

  // Customer information
  customer: {
    name: string;
    mobile?: string;
    email?: string;
  };

  // Loyalty information
  loyalty?: {
    pointsEarned: number;
    totalPoints: number;
    previousPoints: number;
    tier: string;
    tierDiscount?: {
      percentage: number;
      amount: number;
    };
    pointsDiscount?: {
      pointsRedeemed: number;
      discountAmount: number;
    };

    nextTierProgress?: {
      nextTier: string;
      progressPercentage: number;
      amountNeeded: number;
    };
  };

  // Fulfillment and delivery information
  fulfillment?: {
    deliveryMethod: string;
    shippingFee: number;
    deliveryAddress?: {
      street?: string;
      city?: string;
      postalCode?: string;
      country?: string;
    };
    deliveryContact?: {
      name: string;
      phone?: string;
    };
    deliveryInstructions?: string;
    estimatedDeliveryDate?: string;
    trackingNumber?: string;
    carrier?: string;
    fulfillmentStatus?: string;
  };

  // Items
  items: StandardizedReceiptItem[];

  // Totals
  totals: {
    itemCount: number;
    subtotal: number;
    totalItemDiscounts: number; // Total of all item-level discounts
    totalOrderDiscounts: number; // Total of all order-level discounts
    totalDiscounts: number; // Combined total of all discounts
    subtotalAfterDiscounts: number; // Subtotal minus all discounts
    shippingCharges: number;
    deliveryFee?: number; // Separate delivery fee from shipping charges
    tax: number;
    grandTotal: number;
    grandTotalInWords: string;
  };

  // Payment information
  payment: {
    isSplitPayment: boolean;
    methods: PaymentMethodInfo[];
    totalPaid: number;
    change?: number;
    remainingBalance?: number;
  };

  // Sales agent (if applicable)
  salesAgent?: {
    name: string;
    id?: string;
  };
}

export interface StandardizedReceiptItem {
  number: number;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  variant?: string;
  // ✅ CRITICAL FIX: Add discount support to standardized receipt items
  discount?: {
    type: "percentage" | "fixed_amount";
    amount: number;
    discountAmount: number;
  };
}

export interface PaymentMethodInfo {
  method: string;
  amount: number;
  date?: string;
  time?: string;
  transactionCode?: string;
  referenceNumber?: string;
  phoneNumber?: string;
  tillNumber?: string;
  cardType?: string;
  lastFourDigits?: string;
  authorizationCode?: string;
  dueDate?: string;
  paymentTerms?: string;
  amountTendered?: number;
  change?: number;
}

export class StandardizedReceiptService {
  private static readonly STORE_INFO = {
    name: "TREASURED SCENTS",
    address: "Greenhouse Mall, Ngong Road, Kenya",
    mobile: "+254 111 443 993",
    email: "<EMAIL>",
    website: "www.treasuredscents.co.ke",
  };

  /**
   * Convert order data to standardized receipt format
   */
  static async generateStandardizedReceipt(
    orderData: any,
    paymentData?: any
  ): Promise<StandardizedReceiptData> {
    // Extract basic order information
    const orderNumber =
      orderData.orderNumber ||
      orderData.number ||
      orderData.name ||
      orderData.id;
    const orderDate = new Date(
      orderData.createdAt || orderData.created_at || new Date()
    );

    // Format date and time
    const dateStr = orderDate.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
    const timeStr = orderDate.toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });

    // Extract customer information
    const customer = orderData.customer || {};
    const customerName =
      customer.firstName && customer.lastName
        ? `${customer.firstName} ${customer.lastName}`.trim()
        : customer.displayName || customer.name || "Walk-in Customer";

    // Extract staff information
    const staffName =
      orderData.salespersonName ||
      (orderData.note?.includes("Staff:")
        ? orderData.note.split("Staff:")[1]?.split("|")[0]?.trim()
        : "POS Staff");

    // Process line items with discount information
    const lineItems = orderData.lineItems || orderData.line_items || [];
    const items: StandardizedReceiptItem[] = lineItems.map(
      (item: any, index: number) => {
        const quantity = item.quantity || 1;
        const unitPrice = parseFloat(item.price || "0");
        const lineTotal = quantity * unitPrice;

        // ✅ CRITICAL FIX: Calculate discount information
        let discount = null;
        if (item.discount && item.discount.amount > 0) {
          let discountAmount = 0;
          if (item.discount.type === "percentage") {
            discountAmount = (lineTotal * item.discount.amount) / 100;
          } else {
            discountAmount = Math.min(item.discount.amount, lineTotal);
          }

          discount = {
            type: item.discount.type,
            amount: item.discount.amount,
            discountAmount: discountAmount,
          };
        }

        return {
          number: index + 1,
          name: item.title || item.name,
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: discount
            ? lineTotal - discount.discountAmount
            : lineTotal,
          variant: item.variantTitle || item.variant_title,
          // ✅ CRITICAL FIX: Include discount information
          discount: discount,
        };
      }
    );

    // Calculate totals with discount information
    const subtotalBeforeDiscounts = items.reduce((sum, item) => {
      const quantity = item.quantity || 1;
      const unitPrice = item.unitPrice || 0;
      return sum + quantity * unitPrice;
    }, 0);

    // Calculate total item-level discounts
    const totalItemDiscounts = items.reduce((sum, item) => {
      return sum + (item.discount?.discountAmount || 0);
    }, 0);

    // Extract and calculate order-level discounts
    const totalOrderDiscounts = this.extractOrderLevelDiscounts(orderData);

    // Calculate totals
    const totalDiscounts = totalItemDiscounts + totalOrderDiscounts;
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0); // Already discounted prices
    const subtotalAfterDiscounts = subtotalBeforeDiscounts - totalDiscounts;

    // Extract fulfillment information first to get shipping data
    const fulfillment = this.extractFulfillmentInformation(orderData);

    // Calculate shipping charges from fulfillment data or shipping lines
    const shippingCharges = this.extractShippingCharges(orderData, fulfillment);
    const deliveryFee = fulfillment?.shippingFee || 0;

    const tax = 0; // POS system doesn't calculate tax
    const grandTotal =
      subtotalAfterDiscounts + Math.max(shippingCharges, deliveryFee) + tax;

    // Convert amount to words
    const grandTotalInWords = this.convertAmountToWords(grandTotal);

    // Process payment information
    const payment = await this.processPaymentInformation(
      orderData,
      paymentData,
      grandTotal
    );

    // Extract loyalty information
    const loyalty = this.extractLoyaltyInformation(orderData, customer);

    return {
      receiptNumber: orderNumber,
      date: dateStr,
      time: timeStr,
      store: this.STORE_INFO,
      staff: {
        name: staffName,
        role: "Sales Associate",
      },
      customer: {
        name: customerName,
        mobile: customer.phone || customer.phoneNumber,
        email: customer.email,
      },
      loyalty,
      fulfillment,
      items,
      totals: {
        itemCount: items.length,
        subtotal: subtotalBeforeDiscounts,
        totalItemDiscounts,
        totalOrderDiscounts,
        totalDiscounts,
        subtotalAfterDiscounts,
        shippingCharges,
        deliveryFee,
        tax,
        grandTotal,
        grandTotalInWords,
      },
      payment,
      salesAgent: orderData.salesAgent
        ? {
            name: orderData.salesAgent.name,
            id: orderData.salesAgent.id,
          }
        : undefined,
    };
  }

  /**
   * Process payment information from enhanced payment system
   */
  private static async processPaymentInformation(
    orderData: any,
    paymentData: any,
    grandTotal: number
  ): Promise<any> {
    let paymentMethods: PaymentMethodInfo[] = [];
    let isSplitPayment = false;
    let totalPaid = grandTotal;

    // Try to get payment data from enhanced payment system
    if (orderData.paymentTransactionId && !paymentData) {
      try {
        const { enhancedPaymentService } = await import(
          "./enhanced-payment-service"
        );
        const statusResponse =
          await enhancedPaymentService.getTransactionStatus(
            orderData.paymentTransactionId
          );

        if (statusResponse.success && statusResponse.data) {
          paymentData = statusResponse.data;
        }
      } catch (error) {
        console.warn("Failed to fetch payment data:", error);
      }
    }

    if (paymentData && paymentData.paymentMethods) {
      // Enhanced payment system data
      console.log("🔍 Payment data structure for receipt:", {
        keys: Object.keys(paymentData),
        hasTransaction: !!paymentData.transaction,
        completedAmount: paymentData.completedAmount,
        transactionCompletedAmount: paymentData.transaction?.completed_amount,
        paymentMethodsCount: paymentData.paymentMethods.length,
      });

      isSplitPayment = paymentData.paymentMethods.length > 1;

      // Access completedAmount from the correct location in the data structure
      totalPaid = parseFloat(
        paymentData.completedAmount ||
          paymentData.transaction?.completed_amount ||
          paymentData.transaction?.completedAmount ||
          grandTotal
      );

      paymentMethods = paymentData.paymentMethods.map((method: any) => {
        const metadata = method.metadata || {};
        // Ensure we have a proper method name
        let methodName =
          method.method_name || method.method_type || method.name || "Payment";

        // Convert method types to display names
        switch (method.method_type) {
          case "mpesa":
            methodName = "M-Pesa";
            break;
          case "absa_till":
            methodName = "ABSA Till";
            break;
          case "cash":
            methodName = "Cash";
            break;
          case "card":
            methodName = "Card";
            break;
          case "credit":
            methodName = "Credit";
            break;
          default:
            // Use the original name if no specific mapping
            methodName = methodName || "Payment";
        }

        const methodInfo: PaymentMethodInfo = {
          method: methodName,
          amount: parseFloat(method.amount),
        };

        // Add method-specific details
        if (method.processed_at) {
          const processedDate = new Date(method.processed_at);
          methodInfo.date = processedDate.toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
          });
          methodInfo.time = processedDate.toLocaleTimeString("en-GB", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });
        }

        // Add metadata based on payment type
        switch (method.method_type) {
          case "mpesa":
            methodInfo.transactionCode = metadata.transactionCode;
            methodInfo.phoneNumber = metadata.phoneNumber;
            methodInfo.referenceNumber = metadata.mpesaReceiptNumber;
            break;
          case "cash":
            methodInfo.amountTendered = metadata.amountTendered;
            methodInfo.change = metadata.change;
            break;
          case "absa_till":
            methodInfo.transactionCode = metadata.transactionCode;
            methodInfo.tillNumber = metadata.tillNumber;
            break;
          case "card":
            methodInfo.cardType = metadata.cardType;
            methodInfo.lastFourDigits = metadata.lastFourDigits;
            methodInfo.authorizationCode = metadata.authorizationCode;
            break;
          case "credit":
            methodInfo.dueDate = metadata.dueDate;
            methodInfo.paymentTerms = metadata.paymentTerms;
            break;
        }

        return methodInfo;
      });
    } else {
      // Fallback to basic payment method
      const paymentMethod = orderData.paymentMethod || "Cash";
      paymentMethods = [
        {
          method: paymentMethod,
          amount: grandTotal,
          date: new Date().toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
          }),
        },
      ];
    }

    return {
      isSplitPayment,
      methods: paymentMethods,
      totalPaid,
      change: totalPaid > grandTotal ? totalPaid - grandTotal : undefined,
      remainingBalance:
        totalPaid < grandTotal ? grandTotal - totalPaid : undefined,
    };
  }

  /**
   * Extract loyalty information from order completion data
   */
  private static extractLoyaltyInformation(orderData: any, customer: any): any {
    // Check if loyalty completion data is available from the order
    const loyaltyCompletion = orderData.loyaltyCompletion;
    const loyaltyDiscount = orderData.loyaltyDiscount;

    console.log("🔍 Loyalty Data Debug:", {
      loyaltyCompletion,
      loyaltyDiscount,
      customerLoyaltyData: customer.loyaltyData,
      customerId: customer.id,
      orderDataKeys: Object.keys(orderData),
      customerKeys: Object.keys(customer),
    });

    if (!loyaltyCompletion && !customer.loyaltyData) {
      // No loyalty data available
      console.log("❌ No loyalty data found - returning null");
      return null;
    }

    // Extract loyalty information - PRIORITIZE BACKEND DATA
    const loyaltyData = customer.loyaltyData || {};
    const pointsEarned =
      loyaltyCompletion?.pointsAdded ||
      Math.floor((orderData.totalPrice || 0) / 100);

    // PRIORITY 1: Use backend loyalty completion data (newBalance from order creation)
    let totalPoints = 0;
    let previousPoints = 0;
    if (loyaltyCompletion?.newBalance) {
      totalPoints = loyaltyCompletion.newBalance;
      previousPoints = totalPoints - pointsEarned;
      console.log("✅ Using BACKEND loyalty data:", {
        backendNewBalance: loyaltyCompletion.newBalance,
        pointsAdded: loyaltyCompletion.pointsAdded,
        tier: loyaltyCompletion.newTier,
      });
    } else {
      // FALLBACK: Enhanced loyalty points extraction with fallback to note field
      const { getEffectiveLoyaltyPoints } = require("@/src/utils/loyaltyUtils");
      const effectiveBalance = getEffectiveLoyaltyPoints(customer);
      previousPoints = effectiveBalance || loyaltyData.loyaltyPoints || 0;
      totalPoints = previousPoints + pointsEarned;
      console.log("⚠️ Using FALLBACK loyalty data (backend data missing):", {
        effectiveBalance,
        previousPoints,
        pointsEarned,
        calculatedTotal: totalPoints,
      });
    }

    const tier = loyaltyCompletion?.newTier || loyaltyData.tier || "bronze";

    console.log("🔍 Final loyalty extraction result:", {
      customerId: customer.id,
      finalTotalPoints: totalPoints,
      tier,
      source: loyaltyCompletion?.newBalance ? "BACKEND" : "FALLBACK",
    });

    // Extract discount information
    let tierDiscount = null;
    let pointsDiscount = null;

    if (loyaltyDiscount) {
      if (loyaltyDiscount.type === "tier" && loyaltyDiscount.amount > 0) {
        tierDiscount = {
          percentage: loyaltyDiscount.metadata?.percentage || 0,
          amount: loyaltyDiscount.amount,
        };
      } else if (
        loyaltyDiscount.type === "points" &&
        loyaltyDiscount.amount > 0
      ) {
        pointsDiscount = {
          pointsRedeemed: loyaltyDiscount.metadata?.pointsRedeemed || 0,
          discountAmount: loyaltyDiscount.amount,
        };
      }
    }

    // Calculate next tier progress
    let nextTierProgress = null;
    if (loyaltyData.progressToNextTier) {
      nextTierProgress = {
        nextTier: loyaltyData.progressToNextTier.nextTier,
        progressPercentage:
          loyaltyData.progressToNextTier.purchaseProgress || 0,
        amountNeeded: loyaltyData.progressToNextTier.amountNeeded || 0,
      };
    }

    const loyaltyResult = {
      pointsEarned,
      totalPoints,
      previousPoints,
      tier,
      tierDiscount,
      pointsDiscount,
      membershipId: customer.loyaltyMembershipId || customer.id,
      nextTierProgress,
    };

    console.log("✅ Loyalty data extracted:", loyaltyResult);
    return loyaltyResult;
  }

  /**
   * Extract order-level discounts from order data
   */
  private static extractOrderLevelDiscounts(orderData: any): number {
    let totalOrderDiscounts = 0;

    // Check for order-level discounts in various formats

    // Format 1: orderDiscount field (single order discount)
    if (orderData.orderDiscount && orderData.orderDiscount.discountAmount > 0) {
      totalOrderDiscounts += parseFloat(orderData.orderDiscount.discountAmount);
      console.log(
        "✅ Found order discount:",
        orderData.orderDiscount.discountAmount
      );
    }

    // Format 2: discounts array (multiple order discounts)
    if (orderData.discounts && Array.isArray(orderData.discounts)) {
      orderData.discounts.forEach((discount: any) => {
        if (discount.discountAmount && discount.discountAmount > 0) {
          totalOrderDiscounts += parseFloat(discount.discountAmount);
        } else if (discount.amount && discount.amount > 0) {
          // Calculate discount amount if not pre-calculated
          const subtotal = parseFloat(orderData.totalPrice || "0");
          if (discount.type === "percentage") {
            totalOrderDiscounts +=
              (subtotal * parseFloat(discount.amount)) / 100;
          } else {
            totalOrderDiscounts += parseFloat(discount.amount);
          }
        }
      });
      console.log("✅ Found order discounts array:", totalOrderDiscounts);
    }

    // Format 3: total_discounts field (Shopify format)
    if (
      orderData.total_discounts &&
      parseFloat(orderData.total_discounts) > 0
    ) {
      totalOrderDiscounts += parseFloat(orderData.total_discounts);
      console.log(
        "✅ Found Shopify total_discounts:",
        orderData.total_discounts
      );
    }

    // Format 4: discount_applications array (Shopify format)
    if (
      orderData.discount_applications &&
      Array.isArray(orderData.discount_applications)
    ) {
      orderData.discount_applications.forEach((discount: any) => {
        if (discount.value && discount.target_type === "line_item") {
          // Skip line item discounts (already handled in item-level)
          return;
        }
        if (discount.value && discount.target_type === "shipping_line") {
          // Skip shipping discounts (handled separately)
          return;
        }
        // Order-level discount
        if (discount.value) {
          totalOrderDiscounts += parseFloat(discount.value);
        }
      });
      console.log(
        "✅ Found Shopify discount_applications:",
        totalOrderDiscounts
      );
    }

    console.log(
      "🔍 Total order-level discounts extracted:",
      totalOrderDiscounts
    );
    return totalOrderDiscounts;
  }

  /**
   * Extract shipping charges from order data
   */
  private static extractShippingCharges(
    orderData: any,
    fulfillment?: StandardizedReceiptData["fulfillment"]
  ): number {
    // Priority 1: Check shipping_lines array (Shopify format)
    if (orderData.shipping_lines && Array.isArray(orderData.shipping_lines)) {
      const totalShipping = orderData.shipping_lines.reduce(
        (total: number, line: any) => {
          return total + parseFloat(line.price || "0");
        },
        0
      );
      if (totalShipping > 0) {
        console.log(
          "✅ Found shipping charges in shipping_lines:",
          totalShipping
        );
        return totalShipping;
      }
    }

    // Priority 2: Check fulfillment data
    if (fulfillment?.shippingFee && fulfillment.shippingFee > 0) {
      console.log(
        "✅ Found shipping charges in fulfillment data:",
        fulfillment.shippingFee
      );
      return fulfillment.shippingFee;
    }

    // Priority 3: Check shippingData (from checkout process)
    if (
      orderData.shippingData?.shippingFee &&
      orderData.shippingData.shippingFee > 0
    ) {
      console.log(
        "✅ Found shipping charges in shippingData:",
        orderData.shippingData.shippingFee
      );
      return orderData.shippingData.shippingFee;
    }

    // Priority 4: Check line items for shipping items
    const lineItems = orderData.lineItems || orderData.line_items || [];
    const shippingLineItem = lineItems.find(
      (item: any) =>
        item.title?.toLowerCase().includes("shipping") ||
        item.title?.toLowerCase().includes("delivery")
    );
    if (shippingLineItem) {
      const shippingFee = parseFloat(shippingLineItem.price || "0");
      console.log("✅ Found shipping charges in line items:", shippingFee);
      return shippingFee;
    }

    console.log("ℹ️ No shipping charges found in order data");
    return 0;
  }

  /**
   * Extract fulfillment and delivery information from order data
   */
  private static extractFulfillmentInformation(
    orderData: any
  ): StandardizedReceiptData["fulfillment"] {
    // Check for fulfillment data in various locations
    const fulfillmentData =
      orderData.fulfillment || orderData.fulfillmentData || orderData.delivery;

    // Priority 1: Check shippingData from checkout process
    if (orderData.shippingData?.includeShipping) {
      const shippingData = orderData.shippingData;
      return {
        deliveryMethod: shippingData.deliveryMethod || "standard",
        shippingFee: shippingData.shippingFee || 0,
        deliveryAddress: shippingData.deliveryAddress
          ? {
              street: shippingData.deliveryAddress.address1,
              city: shippingData.deliveryAddress.city,
              postalCode: shippingData.deliveryAddress.zip,
              country: shippingData.deliveryAddress.country || "Kenya",
            }
          : undefined,
        deliveryContact: shippingData.deliveryAddress
          ? {
              name: `${shippingData.deliveryAddress.firstName} ${shippingData.deliveryAddress.lastName}`,
              phone: shippingData.deliveryAddress.phone,
            }
          : undefined,
        fulfillmentStatus: "pending",
      };
    }

    // Priority 2: Check shipping_lines array (Shopify format)
    if (
      orderData.shipping_lines &&
      Array.isArray(orderData.shipping_lines) &&
      orderData.shipping_lines.length > 0
    ) {
      const shippingLine = orderData.shipping_lines[0]; // Use first shipping line
      return {
        deliveryMethod: shippingLine.title || "Standard Delivery",
        shippingFee: parseFloat(shippingLine.price || "0"),
        fulfillmentStatus: "pending",
      };
    }

    if (!fulfillmentData) {
      // Check for shipping line items that might contain delivery information
      const shippingLineItem = orderData.line_items?.find(
        (item: any) =>
          item.title?.toLowerCase().includes("shipping") ||
          item.title?.toLowerCase().includes("delivery")
      );

      if (shippingLineItem) {
        // Extract delivery method from shipping line item title
        const deliveryMethod = this.extractDeliveryMethodFromTitle(
          shippingLineItem.title
        );

        return {
          deliveryMethod,
          shippingFee: parseFloat(shippingLineItem.price || "0"),
          fulfillmentStatus: "pending",
        };
      }

      return undefined;
    }

    // Extract delivery address
    const deliveryAddress =
      fulfillmentData.deliveryAddress ||
      fulfillmentData.delivery_address ||
      orderData.shipping_address;
    const formattedAddress = deliveryAddress
      ? {
          street: deliveryAddress.address1 || deliveryAddress.street,
          city: deliveryAddress.city,
          postalCode: deliveryAddress.zip || deliveryAddress.postal_code,
          country: deliveryAddress.country || "Kenya",
        }
      : undefined;

    // Extract delivery contact
    const deliveryContact = {
      name:
        fulfillmentData.deliveryContactName ||
        fulfillmentData.delivery_contact_name ||
        orderData.customer?.name ||
        `${orderData.customer?.first_name || ""} ${
          orderData.customer?.last_name || ""
        }`.trim(),
      phone:
        fulfillmentData.deliveryContactPhone ||
        fulfillmentData.delivery_contact_phone ||
        orderData.customer?.phone,
    };

    return {
      deliveryMethod:
        fulfillmentData.deliveryMethod ||
        fulfillmentData.delivery_method ||
        "standard",
      shippingFee: parseFloat(
        fulfillmentData.shippingFee || fulfillmentData.shipping_fee || "0"
      ),
      deliveryAddress: formattedAddress,
      deliveryContact,
      deliveryInstructions:
        fulfillmentData.deliveryInstructions ||
        fulfillmentData.delivery_instructions,
      estimatedDeliveryDate:
        fulfillmentData.estimatedDeliveryDate ||
        fulfillmentData.estimated_delivery_date,
      trackingNumber:
        fulfillmentData.trackingNumber || fulfillmentData.tracking_number,
      carrier: fulfillmentData.carrier,
      fulfillmentStatus:
        fulfillmentData.fulfillmentStatus ||
        fulfillmentData.fulfillment_status ||
        "pending",
    };
  }

  /**
   * Extract delivery method from shipping line item title
   */
  private static extractDeliveryMethodFromTitle(title: string): string {
    const lowerTitle = title.toLowerCase();

    if (lowerTitle.includes("express")) return "express";
    if (lowerTitle.includes("standard")) return "standard";
    if (lowerTitle.includes("pickup") || lowerTitle.includes("collection"))
      return "local_pickup";
    if (
      lowerTitle.includes("upcountry") ||
      lowerTitle.includes("outside nairobi")
    )
      return "upcountry";

    return "standard"; // Default
  }

  /**
   * Convert amount to words (Kenyan format)
   */
  private static convertAmountToWords(amount: number): string {
    // Simple implementation - can be enhanced with a proper number-to-words library
    const thousands = Math.floor(amount / 1000);
    const hundreds = Math.floor((amount % 1000) / 100);
    const remainder = amount % 100;

    let words = "";

    if (thousands > 0) {
      words += `${this.numberToWords(thousands)} thousand`;
    }

    if (hundreds > 0) {
      if (words) words += " ";
      words += `${this.numberToWords(hundreds)} hundred`;
    }

    if (remainder > 0) {
      if (words) words += " ";
      words += this.numberToWords(remainder);
    }

    return words.charAt(0).toUpperCase() + words.slice(1);
  }

  /**
   * Convert number to words (basic implementation)
   */
  private static numberToWords(num: number): string {
    const ones = [
      "",
      "one",
      "two",
      "three",
      "four",
      "five",
      "six",
      "seven",
      "eight",
      "nine",
    ];
    const teens = [
      "ten",
      "eleven",
      "twelve",
      "thirteen",
      "fourteen",
      "fifteen",
      "sixteen",
      "seventeen",
      "eighteen",
      "nineteen",
    ];
    const tens = [
      "",
      "",
      "twenty",
      "thirty",
      "forty",
      "fifty",
      "sixty",
      "seventy",
      "eighty",
      "ninety",
    ];

    if (num < 10) return ones[num];
    if (num < 20) return teens[num - 10];
    if (num < 100)
      return (
        tens[Math.floor(num / 10)] + (num % 10 ? " " + ones[num % 10] : "")
      );

    return num.toString(); // Fallback for larger numbers
  }

  /**
   * Generate HTML receipt following the standardized format using universal template
   */
  static generateHTMLReceipt(receiptData: StandardizedReceiptData): string {
    // Use the new universal template for better device compatibility
    try {
      console.log(
        "🔄 Attempting to use Universal Receipt Template for HTML..."
      );
      const {
        UniversalReceiptTemplate,
      } = require("./UniversalReceiptTemplate");
      console.log("✅ Universal Receipt Template imported successfully");

      const templateData =
        UniversalReceiptTemplate.fromStandardizedData(receiptData);
      console.log("✅ Template data converted successfully:", {
        storeName: templateData.storeName,
        customerName: templateData.customerName,
        loyaltyPoints: templateData.loyalty?.totalPoints,
        itemCount: templateData.items.length,
        shippingFee: templateData.shippingFee,
        subtotal: templateData.subtotal,
        grandTotal: templateData.grandTotal,
      });
      console.log("🚢 Shipping data in template:", {
        hasShippingFee: !!templateData.shippingFee,
        shippingFeeAmount: templateData.shippingFee,
        originalShippingCharges: receiptData.totals.shippingCharges,
        originalFulfillmentFee: receiptData.fulfillment?.shippingFee,
      });

      const htmlContent = UniversalReceiptTemplate.generateHTML(templateData, {
        width: "thermal-80mm",
        deviceType: "web", // Use 'web' for browser printing
      });
      console.log("✅ Universal HTML receipt generated successfully");
      return htmlContent;
    } catch (error) {
      console.error(
        "❌ Universal template failed, falling back to legacy HTML:",
        error
      );
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      // Create a simplified device-compatible HTML as final fallback
      return this.generateDeviceCompatibleHTML(receiptData);
    }
  }

  /**
   * Generate device-compatible HTML using table layouts (no flexbox)
   */
  private static generateDeviceCompatibleHTML(
    receiptData: StandardizedReceiptData
  ): string {
    console.log("🔄 Generating device-compatible HTML fallback...");

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Receipt ${receiptData.receiptNumber}</title>
    <style>
        /* Device-compatible CSS - tables only, no flexbox */
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.3;
            margin: 0;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
            color: #000;
            background: #fff;
        }

        .center { text-align: center; }
        .left { text-align: left; }
        .right { text-align: right; }
        .bold { font-weight: bold; }

        .store-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }

        .store-logo {
            text-align: center;
            margin-bottom: 8px;
        }

        .logo-image {
            max-width: 60px;
            max-height: 60px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 4px;
        }

        .receipt-details {
            margin-bottom: 20px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table td {
            padding: 2px 0;
            vertical-align: top;
        }

        .totals-table {
            width: 100%;
            border-collapse: collapse;
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 10px;
        }

        .totals-table td {
            padding: 2px 0;
        }

        .loyalty-section {
            margin: 20px 0;
            padding: 10px;
            border: 1px dashed #000;
            text-align: center;
        }

        .separator {
            border-top: 1px solid #000;
            margin: 10px 0;
        }

        @media print {
            body { margin: 0; padding: 10px; }
            .separator { border-color: #000 !important; }
        }
    </style>
</head>
<body>
    <div class="store-header">
        <div class="store-logo">
            <img src="${TREASURED_LOGO_BASE64}" alt="Treasured Scents" class="logo-image" />
        </div>
        <div class="bold" style="font-size: 16px;">${
          receiptData.store.name
        }</div>
        <div>${receiptData.store.address}</div>
        <div>Mobile: ${receiptData.store.mobile}</div>
        <div>Email: ${receiptData.store.email}</div>
    </div>

    <div class="center bold" style="font-size: 14px; margin-bottom: 10px;">SALES RECEIPT</div>

    <div class="receipt-details">
        <div>Receipt No: ${receiptData.receiptNumber}</div>
        <div>Date: ${receiptData.date} ${receiptData.time}</div>
        <div>Served by: ${receiptData.staff.name}</div>
        ${
          receiptData.customer.name
            ? `<div>Customer: ${receiptData.customer.name}</div>`
            : ""
        }
        ${
          receiptData.customer.mobile
            ? `<div>Mobile: ${receiptData.customer.mobile}</div>`
            : ""
        }
    </div>

    <div class="separator"></div>

    <table class="items-table">
        ${receiptData.items
          .map(
            (item) => `
        <tr>
            <td colspan="2" class="bold">${item.name}</td>
        </tr>
        <tr>
            <td class="left">${item.quantity} x KSh ${item.unitPrice.toFixed(
              2
            )}</td>
            <td class="right">KSh ${item.totalPrice.toFixed(2)}</td>
        </tr>
        `
          )
          .join("")}
    </table>

    <table class="totals-table">
        <tr>
            <td class="left">Subtotal:</td>
            <td class="right">KSh ${receiptData.totals.subtotal.toFixed(2)}</td>
        </tr>
        ${
          receiptData.totals.shippingCharges > 0
            ? `
        <tr>
            <td class="left">Shipping Fees:</td>
            <td class="right">KSh ${receiptData.totals.shippingCharges.toFixed(
              2
            )}</td>
        </tr>
        `
            : ""
        }
        <tr class="bold" style="border-top: 1px solid #000; padding-top: 5px;">
            <td class="left">GRAND TOTAL:</td>
            <td class="right">KSh ${receiptData.totals.grandTotal.toFixed(
              2
            )}</td>
        </tr>
    </table>

    ${
      receiptData.loyalty
        ? `
    <div class="loyalty-section">
        <div class="bold">LOYALTY REWARDS</div>
        <table style="width: 100%; margin-top: 5px;">
            <tr>
                <td class="left">Total TS Points:</td>
                <td class="right bold">${receiptData.loyalty.totalPoints}</td>
            </tr>
        </table>
    </div>
    `
        : ""
    }

    <div class="center" style="margin-top: 20px; font-size: 11px;">
        <div>You Are Treasured</div>
        <div>Powered by Dukalink POS</div>
    </div>
</body>
</html>`;
  }

  /**
   * Legacy HTML receipt generation (fallback)
   */
  private static generateLegacyHTMLReceipt(
    receiptData: StandardizedReceiptData
  ): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - ${receiptData.receiptNumber}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            color: black;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 15px;
        }
        .store-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .store-info {
            font-size: 12px;
            margin-bottom: 3px;
        }
        .receipt-details {
            margin-bottom: 15px;
            font-size: 12px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        .items-section {
            margin-bottom: 15px;
        }
        .item {
            margin-bottom: 8px;
            font-size: 12px;
        }
        .item-header {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .item-details {
            display: flex;
            justify-content: space-between;
            margin-left: 10px;
        }
        .totals-section {
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 12px;
        }
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .amount-words {
            text-align: center;
            font-style: italic;
            font-size: 11px;
            margin-top: 5px;
        }
        .payment-section {
            margin-bottom: 15px;
        }
        .payment-method {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 12px;
        }
        .loyalty-section {
            margin-bottom: 15px;
            border-top: 1px solid #000;
            padding-top: 10px;
        }
        .loyalty-header {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 8px;
            text-align: center;
        }
        .loyalty-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 12px;
        }
        .loyalty-earned {
            color: #28a745;
            font-weight: bold;
        }
        .loyalty-discount {
            color: #dc3545;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            border-top: 2px dotted #000;
            padding-top: 15px;
            margin-top: 20px;
        }
        .footer-message {
            font-size: 14px;
            font-weight: bold;
        }
        @media print {
            body { margin: 0; padding: 10px; }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <div class="store-name">${receiptData.store.name}</div>
        <div class="store-info">${receiptData.store.address}</div>
        <div class="store-info">Mobile: ${receiptData.store.mobile}</div>
        <div class="store-info">Email: ${receiptData.store.email}</div>
        <div class="store-info">${receiptData.store.website}</div>
    </div>

    <!-- Receipt Details -->
    <div class="receipt-details">
        <div class="detail-row">
            <span><strong>Receipt No:</strong></span>
            <span>${receiptData.receiptNumber}</span>
        </div>
        <div class="detail-row">
            <span><strong>Date:</strong></span>
            <span>${receiptData.date} ${receiptData.time}</span>
        </div>
        <div class="detail-row">
            <span><strong>Served by:</strong></span>
            <span>${receiptData.staff.name}</span>
        </div>
        <div class="detail-row">
            <span><strong>Customer:</strong></span>
            <span>${receiptData.customer.name}</span>
        </div>
        ${
          receiptData.customer.mobile
            ? `
        <div class="detail-row">
            <span><strong>Mobile:</strong></span>
            <span>${receiptData.customer.mobile}</span>
        </div>
        `
            : ""
        }
        ${
          receiptData.loyalty
            ? `
        <div class="detail-row">
            <span><strong>Loyalty Tier:</strong></span>
            <span>${receiptData.loyalty.tier.toUpperCase()}</span>
        </div>
        <div class="detail-row">
            <span><strong>Total TS Points:</strong></span>
            <span>${receiptData.loyalty.totalPoints}</span>
        </div>
        `
            : ""
        }
    </div>

    <!-- Items Section -->
    <div class="items-section">
        ${receiptData.items
          .map(
            (item) => `
        <div class="item">
            <div class="item-header">#${item.number}. ${item.name}</div>
            <div class="item-details">
                <span>${item.quantity.toFixed(2)} x ${formatCurrency(
              item.unitPrice
            )}</span>
                <span>${formatCurrency(item.totalPrice)}</span>
            </div>
        </div>
        `
          )
          .join("")}
    </div>

    <!-- Totals Section -->
    <div class="totals-section">
        <div class="total-row">
            <span><strong>Total Items:</strong></span>
            <span>${receiptData.totals.itemCount}</span>
        </div>
        <div class="total-row">
            <span><strong>Subtotal:</strong></span>
            <span>${formatCurrency(receiptData.totals.subtotal)}</span>
        </div>
        ${
          receiptData.totals.shippingCharges > 0 ||
          (receiptData.totals.deliveryFee && receiptData.totals.deliveryFee > 0)
            ? `
        <div class="total-row">
            <span><strong>Delivery Fee:</strong></span>
            <span>${formatCurrency(
              receiptData.totals.deliveryFee ||
                receiptData.totals.shippingCharges
            )}</span>
        </div>
        `
            : ""
        }
        <div class="total-row grand-total">
            <span><strong>Grand Total:</strong></span>
            <span><strong>${formatCurrency(
              receiptData.totals.grandTotal
            )}</strong></span>
        </div>
        <div class="amount-words">(${
          receiptData.totals.grandTotalInWords
        })</div>
    </div>

    <!-- Payment Section -->
    <div class="payment-section">
        ${receiptData.payment.methods
          .map(
            (method) => `
        <div class="payment-method">
            <span><strong>${method.method}${
              method.date ? ` (${method.date})` : ""
            }:</strong></span>
            <span><strong>${formatCurrency(method.amount)}</strong></span>
        </div>
        `
          )
          .join("")}
        <div class="payment-method" style="border-top: 1px solid #000; padding-top: 5px; margin-top: 5px;">
            <span><strong>Total Paid:</strong></span>
            <span><strong>${formatCurrency(
              receiptData.payment.totalPaid
            )}</strong></span>
        </div>
    </div>

    <!-- Delivery Information Section -->
    ${
      receiptData.fulfillment &&
      receiptData.fulfillment.deliveryMethod !== "local_pickup"
        ? `
    <div class="loyalty-section">
        <div class="loyalty-row">
            <span><strong>Delivery Method:</strong></span>
            <span>${receiptData.fulfillment.deliveryMethod.toUpperCase()}</span>
        </div>
        ${
          receiptData.fulfillment.trackingNumber
            ? `
        <div class="loyalty-row">
            <span><strong>Tracking:</strong></span>
            <span>${receiptData.fulfillment.trackingNumber}</span>
        </div>
        `
            : ""
        }
    </div>
    `
        : ""
    }

    <!-- Loyalty Section -->
    ${
      receiptData.loyalty
        ? `
    <div class="loyalty-section">
        <div class="loyalty-row">
            <span><strong>Total TS Points:</strong></span>
            <span>${receiptData.loyalty.totalPoints}</span>
        </div>
    </div>
    `
        : ""
    }

    <!-- Footer -->
    <div class="footer">
        <div class="footer-message">You Are Treasured</div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate thermal receipt text following the standardized format using universal template
   */
  static generateThermalReceipt(
    receiptData: StandardizedReceiptData,
    width: number = 32
  ): string {
    // Use the new universal template for better device compatibility
    try {
      console.log(
        "🔄 Attempting to use Universal Receipt Template for thermal..."
      );
      const {
        UniversalReceiptTemplate,
      } = require("./UniversalReceiptTemplate");
      console.log(
        "✅ Universal Receipt Template imported successfully for thermal"
      );

      const templateData =
        UniversalReceiptTemplate.fromStandardizedData(receiptData);
      console.log("✅ Template data converted successfully for thermal:", {
        storeName: templateData.storeName,
        customerName: templateData.customerName,
        loyaltyPoints: templateData.loyalty?.totalPoints,
        itemCount: templateData.items.length,
      });

      const textContent = UniversalReceiptTemplate.generateText(
        templateData,
        width
      );
      console.log("✅ Universal thermal receipt generated successfully");
      return textContent;
    } catch (error) {
      console.error(
        "❌ Universal template failed, falling back to legacy thermal:",
        error
      );
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      return this.generateLegacyThermalReceipt(receiptData, width);
    }
  }

  /**
   * Legacy thermal receipt generation (fallback)
   */
  private static generateLegacyThermalReceipt(
    receiptData: StandardizedReceiptData,
    width: number = 32
  ): string {
    const lines: string[] = [];

    // Helper function to center text
    const centerText = (text: string): string => {
      const padding = Math.max(0, Math.floor((width - text.length) / 2));
      return " ".repeat(padding) + text;
    };

    // Helper function to create aligned line
    const alignedLine = (left: string, right: string): string => {
      const maxLeft = width - right.length - 1;
      const truncatedLeft =
        left.length > maxLeft ? left.substring(0, maxLeft) : left;
      const spacing = Math.max(1, width - truncatedLeft.length - right.length);
      return truncatedLeft + " ".repeat(spacing) + right;
    };

    // Header with logo
    lines.push(centerText(TREASURED_LOGO_TEXT));
    lines.push(centerText(receiptData.store.name));
    lines.push(centerText(receiptData.store.address));
    lines.push(centerText(`Mobile: ${receiptData.store.mobile}`));
    lines.push(centerText(`Email: ${receiptData.store.email}`));
    lines.push(centerText(receiptData.store.website));
    lines.push("");

    // Receipt details
    lines.push(alignedLine("Receipt No:", receiptData.receiptNumber));
    lines.push(alignedLine("Date:", `${receiptData.date} ${receiptData.time}`));
    lines.push(alignedLine("Served by:", receiptData.staff.name));
    lines.push(alignedLine("Customer:", receiptData.customer.name));

    if (receiptData.customer.mobile) {
      lines.push(alignedLine("Mobile:", receiptData.customer.mobile));
    }

    if (receiptData.loyalty) {
      lines.push(
        alignedLine(
          "Total TS Points:",
          receiptData.loyalty.totalPoints.toString()
        )
      );
    }

    lines.push("");

    // Items
    receiptData.items.forEach((item) => {
      lines.push(`#${item.number}. ${item.name}`);
      lines.push(
        alignedLine(
          `${item.quantity.toFixed(2)} x ${formatCurrency(item.unitPrice)}`,
          formatCurrency(item.totalPrice)
        )
      );
    });

    lines.push("");

    // Totals
    lines.push(
      alignedLine("Total Items:", receiptData.totals.itemCount.toString())
    );
    lines.push(
      alignedLine("Subtotal:", formatCurrency(receiptData.totals.subtotal))
    );

    // Show discount information if any discounts were applied
    if (receiptData.totals.totalDiscounts > 0) {
      if (receiptData.totals.totalItemDiscounts > 0) {
        lines.push(
          alignedLine(
            "Item Discounts:",
            `-${formatCurrency(receiptData.totals.totalItemDiscounts)}`
          )
        );
      }
      if (receiptData.totals.totalOrderDiscounts > 0) {
        lines.push(
          alignedLine(
            "Order Discounts:",
            `-${formatCurrency(receiptData.totals.totalOrderDiscounts)}`
          )
        );
      }
      lines.push(
        alignedLine(
          "Total Savings:",
          `-${formatCurrency(receiptData.totals.totalDiscounts)}`
        )
      );
      lines.push(
        alignedLine(
          "After Discounts:",
          formatCurrency(receiptData.totals.subtotalAfterDiscounts)
        )
      );
    }

    // Single shipping fee line
    const shippingFee =
      receiptData.totals.deliveryFee || receiptData.totals.shippingCharges;
    if (shippingFee > 0) {
      lines.push(alignedLine("Delivery Fee:", formatCurrency(shippingFee)));
    }

    lines.push(
      alignedLine("Grand Total:", formatCurrency(receiptData.totals.grandTotal))
    );
    lines.push(centerText(`(${receiptData.totals.grandTotalInWords})`));
    lines.push("");

    // Payment
    receiptData.payment.methods.forEach((method) => {
      // Fix undefined method display
      const methodName = method.method || "Payment";
      const methodLabel = method.date
        ? `${methodName} (${method.date})`
        : methodName;
      lines.push(alignedLine(methodLabel + ":", formatCurrency(method.amount)));
    });

    lines.push(
      alignedLine("Total Paid:", formatCurrency(receiptData.payment.totalPaid))
    );
    lines.push("");

    // Delivery Information (tracking only, no delivery method display)
    if (receiptData.fulfillment && receiptData.fulfillment.trackingNumber) {
      lines.push(
        alignedLine("Tracking:", receiptData.fulfillment.trackingNumber)
      );
      lines.push("");
    }

    // Footer
    lines.push("-".repeat(width));
    lines.push(centerText("You Are Treasured"));

    return lines.join("\n");
  }

  /**
   * Generate WhatsApp/text receipt following the standardized format
   */
  static generateWhatsAppReceipt(receiptData: StandardizedReceiptData): string {
    const lines: string[] = [];

    // Header with emojis
    lines.push("🏪 *TREASURED SCENTS*");
    lines.push("📍 Greenhouse Mall, Ngong Road, Kenya");
    lines.push(`📱 Mobile: ${receiptData.store.mobile}`);
    lines.push(`📧 Email: ${receiptData.store.email}`);
    lines.push(`🌐 ${receiptData.store.website}`);
    lines.push("");

    // Receipt details
    lines.push("📋 *RECEIPT DETAILS*");
    lines.push(`🧾 Receipt No: ${receiptData.receiptNumber}`);
    lines.push(`📅 Date: ${receiptData.date} ${receiptData.time}`);
    lines.push(`👤 Served by: ${receiptData.staff.name}`);
    lines.push(`🛍️ Customer: ${receiptData.customer.name}`);

    if (receiptData.customer.mobile) {
      lines.push(`📞 Mobile: ${receiptData.customer.mobile}`);
    }

    if (receiptData.loyalty) {
      lines.push(`⭐ Total TS Points: ${receiptData.loyalty.totalPoints}`);
    }

    lines.push("");

    // Items
    lines.push("🛒 *ITEMS PURCHASED*");
    receiptData.items.forEach((item) => {
      lines.push(`${item.number}. *${item.name}*`);
      lines.push(
        `   ${item.quantity.toFixed(2)} x ${formatCurrency(
          item.unitPrice
        )} = *${formatCurrency(item.totalPrice)}*`
      );
    });
    lines.push("");

    // Totals
    lines.push("💰 *TOTALS*");
    lines.push(`📦 Total Items: ${receiptData.totals.itemCount}`);
    lines.push(`💵 Subtotal: ${formatCurrency(receiptData.totals.subtotal)}`);

    // Show discount information if any discounts were applied
    if (receiptData.totals.totalDiscounts > 0) {
      if (receiptData.totals.totalItemDiscounts > 0) {
        lines.push(
          `🏷️ Item Discounts: -${formatCurrency(
            receiptData.totals.totalItemDiscounts
          )}`
        );
      }
      if (receiptData.totals.totalOrderDiscounts > 0) {
        lines.push(
          `🎯 Order Discounts: -${formatCurrency(
            receiptData.totals.totalOrderDiscounts
          )}`
        );
      }
      lines.push(
        `💸 *Total Savings: -${formatCurrency(
          receiptData.totals.totalDiscounts
        )}*`
      );
      lines.push(
        `💰 After Discounts: ${formatCurrency(
          receiptData.totals.subtotalAfterDiscounts
        )}`
      );
    }

    lines.push(
      `💳 *Grand Total: ${formatCurrency(receiptData.totals.grandTotal)}*`
    );
    lines.push(`_(${receiptData.totals.grandTotalInWords})_`);
    lines.push("");

    // Payment
    lines.push("💳 *PAYMENT DETAILS*");
    receiptData.payment.methods.forEach((method) => {
      // Fix undefined method display
      const methodName = method.method || "Payment";
      const methodLabel = method.date
        ? `${methodName} (${method.date})`
        : methodName;
      lines.push(`💰 ${methodLabel}: *${formatCurrency(method.amount)}*`);

      // Add method-specific details
      if (method.transactionCode) {
        lines.push(`   🔗 Transaction Code: ${method.transactionCode}`);
      }
      if (method.referenceNumber) {
        lines.push(`   📄 Reference: ${method.referenceNumber}`);
      }
      if (method.phoneNumber) {
        lines.push(`   📱 Phone: ${method.phoneNumber}`);
      }
    });

    lines.push(
      `💵 *Total Paid: ${formatCurrency(receiptData.payment.totalPaid)}*`
    );

    if (receiptData.payment.change && receiptData.payment.change > 0) {
      lines.push(`💰 Change: ${formatCurrency(receiptData.payment.change)}`);
    }

    lines.push("");

    // Delivery Information (tracking only, no delivery method display)
    if (receiptData.fulfillment && receiptData.fulfillment.trackingNumber) {
      lines.push(`🔍 Tracking: ${receiptData.fulfillment.trackingNumber}`);
      lines.push("");
    }

    // Footer
    lines.push("═══════════════════════");
    lines.push("💖 *You Are Treasured* 💖");
    lines.push("");
    lines.push("Thank you for shopping with us!");
    lines.push("Visit us again soon! 🛍️✨");

    return lines.join("\n");
  }
}
