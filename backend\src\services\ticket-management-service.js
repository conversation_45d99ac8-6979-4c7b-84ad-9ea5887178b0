/**
 * Ticket Management Service
 *
 * Handles all ticket-related operations including:
 * - CRUD operations for tickets
 * - Ticket item management
 * - Discount application
 * - Audit trail logging
 * - Auto-save and persistence
 */

require("dotenv").config();
const { databaseManager } = require("../config/database-manager");
const { v4: uuidv4 } = require("uuid");

class TicketManagementService {
  constructor() {
    if (TicketManagementService.instance) {
      return TicketManagementService.instance;
    }

    this.databaseManager = databaseManager;

    TicketManagementService.instance = this;
  }

  static getInstance() {
    if (!TicketManagementService.instance) {
      TicketManagementService.instance = new TicketManagementService();
    }
    return TicketManagementService.instance;
  }

  // Transform database ticket data to frontend format
  transformTicketData(ticket) {
    if (!ticket) return null;

    return {
      id: ticket.id,
      name: ticket.name,
      staffId: ticket.staff_id,
      terminalId: ticket.terminal_id,
      locationId: ticket.location_id,
      status: ticket.status,
      createdAt: ticket.created_at,
      updatedAt: ticket.updated_at,
      expiresAt: ticket.expires_at,
      subtotal: parseFloat(ticket.subtotal || 0),
      tax: parseFloat(ticket.tax || 0),
      total: parseFloat(ticket.total || 0),
      discounts: ticket.discounts || [],
      customer: ticket.customer_id
        ? {
            id: ticket.customer_id,
            // Add other customer fields if available
          }
        : undefined,
      note: ticket.note,
      items: ticket.items || [],
      // Frontend expects these fields for proper state management
      isLocal: false,
      isDirty: false,
    };
  }

  // Transform database item data to frontend format
  transformItemData(item) {
    if (!item) return null;

    return {
      id: item.id,
      variantId: item.variant_id,
      productId: item.product_id,
      title: item.title,
      variantTitle: item.variant_title,
      sku: item.sku,
      price: parseFloat(item.price),
      quantity: parseInt(item.quantity),
      lineTotal: parseFloat(item.line_total || 0),
      inventoryQuantity: parseInt(item.inventory_quantity || 0),
      notes: item.notes,
      discountAmount: parseFloat(item.discount_amount || 0),
      discountType: item.discount_type,
    };
  }

  // Create a new ticket
  async createTicket(staffId, ticketData = {}) {
    try {
      const ticketId = uuidv4();
      const {
        name = "New Ticket",
        terminalId = null,
        locationId = null,
        customerId = null,
        salesAgentId = null,
        note = null,
        expiresAt = null,
      } = ticketData;

      const query = `
        INSERT INTO pos_tickets (
          id, staff_id, terminal_id, location_id, name, customer_id, 
          sales_agent_id, note, expires_at, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
      `;

      await this.databaseManager.executeQuery(query, [
        ticketId,
        staffId,
        terminalId,
        locationId,
        name,
        customerId,
        salesAgentId,
        note,
        expiresAt,
      ]);

      // Log audit trail
      await this.logAuditEvent(ticketId, staffId, "created", {
        ticketName: name,
        terminalId,
        locationId,
      });

      const rawTicket = await this.getTicketById(ticketId);
      return {
        success: true,
        ticket: this.transformTicketData(rawTicket),
      };
    } catch (error) {
      console.error("Create ticket error:", error);
      return {
        success: false,
        error: error.message || "Failed to create ticket",
      };
    }
  }

  // Get ticket by ID with items and discounts
  async getTicketById(ticketId) {
    try {
      const ticketQuery = `
        SELECT 
          t.*,
          s.name as staff_name,
          sa.name as sales_agent_name
        FROM pos_tickets t
        LEFT JOIN pos_staff s ON t.staff_id = s.id
        LEFT JOIN sales_agents sa ON t.sales_agent_id = sa.id
        WHERE t.id = ?
      `;

      const [ticketRows] = await this.databaseManager.executeQuery(
        ticketQuery,
        [ticketId]
      );

      if (ticketRows.length === 0) {
        return null;
      }

      const ticket = ticketRows[0];

      // Get ticket items
      const itemsQuery = `
        SELECT * FROM ticket_items 
        WHERE ticket_id = ? 
        ORDER BY created_at ASC
      `;
      const [items] = await this.databaseManager.executeQuery(itemsQuery, [
        ticketId,
      ]);

      // Get ticket discounts
      const discountsQuery = `
        SELECT 
          td.*,
          s.name as applied_by_name
        FROM ticket_discounts td
        LEFT JOIN pos_staff s ON td.applied_by = s.id
        WHERE td.ticket_id = ?
        ORDER BY td.created_at ASC
      `;
      const [discounts] = await this.databaseManager.executeQuery(
        discountsQuery,
        [ticketId]
      );

      // Transform items to frontend format
      const transformedItems = (items || []).map((item) =>
        this.transformItemData(item)
      );

      return {
        ...ticket,
        items: transformedItems,
        discounts: discounts || [],
      };
    } catch (error) {
      console.error("Get ticket by ID error:", error);
      return null;
    }
  }

  // Get tickets for a staff member
  async getTicketsByStaff(staffId, options = {}) {
    try {
      const {
        status = "active",
        limit = 50,
        offset = 0,
        includeExpired = false,
      } = options;

      let whereClause = "WHERE t.staff_id = ?";
      let queryParams = [staffId];

      if (status !== "all") {
        whereClause += " AND t.status = ?";
        queryParams.push(status);
      }

      if (!includeExpired) {
        whereClause += " AND (t.expires_at IS NULL OR t.expires_at > NOW())";
      }

      // Use string interpolation for LIMIT and OFFSET to avoid MySQL parameter binding issues
      const query = `
        SELECT
          t.*,
          s.name as staff_name,
          sa.name as sales_agent_name,
          COUNT(ti.id) as item_count
        FROM pos_tickets t
        LEFT JOIN pos_staff s ON t.staff_id = s.id
        LEFT JOIN sales_agents sa ON t.sales_agent_id = sa.id
        LEFT JOIN ticket_items ti ON t.id = ti.ticket_id
        ${whereClause}
        GROUP BY t.id
        ORDER BY t.updated_at DESC
        LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
      `;

      const [tickets] = await this.databaseManager.executeQuery(
        query,
        queryParams
      );

      // Transform tickets to frontend format
      const transformedTickets = (tickets || []).map((ticket) =>
        this.transformTicketData(ticket)
      );

      return {
        success: true,
        tickets: transformedTickets,
      };
    } catch (error) {
      console.error("Get tickets by staff error:", error);
      return {
        success: false,
        error: error.message || "Failed to fetch tickets",
      };
    }
  }

  // Get active tickets by customer ID
  async getTicketsByCustomer(customerId, options = {}) {
    try {
      const {
        status = "active,paused",
        limit = 10,
        includeExpired = false,
      } = options;

      let whereClause = "WHERE t.customer_id = ?";
      let queryParams = [customerId];

      // Add status filter
      if (status && status !== "all") {
        if (status.includes(",")) {
          const statuses = status.split(",").map((s) => s.trim());
          const placeholders = statuses.map(() => "?").join(",");
          whereClause += ` AND t.status IN (${placeholders})`;
          queryParams.push(...statuses);
        } else {
          whereClause += " AND t.status = ?";
          queryParams.push(status);
        }
      }

      // Add expiry filter
      if (!includeExpired) {
        whereClause += " AND (t.expires_at IS NULL OR t.expires_at > NOW())";
      }

      const query = `
        SELECT
          t.*,
          COUNT(ti.id) as item_count,
          COALESCE(SUM(ti.line_total), 0) as calculated_total
        FROM pos_tickets t
        LEFT JOIN ticket_items ti ON t.id = ti.ticket_id
        ${whereClause}
        GROUP BY t.id
        ORDER BY t.updated_at DESC
        LIMIT ${limit}
      `;

      const [tickets] = await this.databaseManager.executeQuery(
        query,
        queryParams
      );

      // Get items for each ticket (limited to first 3 for preview)
      for (const ticket of tickets) {
        const [items] = await this.databaseManager.executeQuery(
          `SELECT * FROM ticket_items WHERE ticket_id = ? ORDER BY created_at ASC LIMIT 3`,
          [ticket.id]
        );
        ticket.items = items;
        ticket.total = parseFloat(ticket.calculated_total) || 0;
      }

      return {
        success: true,
        tickets: tickets,
      };
    } catch (error) {
      console.error("Error fetching tickets by customer:", error);
      return {
        success: false,
        error: error.message || "Failed to fetch customer tickets",
      };
    }
  }

  // Update ticket
  async updateTicket(ticketId, staffId, updateData) {
    try {
      const allowedFields = [
        "name",
        "customer_id",
        "sales_agent_id",
        "note",
        "status",
      ];
      const updates = [];
      const values = [];

      for (const [field, value] of Object.entries(updateData)) {
        if (allowedFields.includes(field)) {
          updates.push(`${field} = ?`);
          values.push(value);
        }
      }

      if (updates.length === 0) {
        return {
          success: false,
          error: "No valid fields to update",
        };
      }

      values.push(ticketId);

      const query = `
        UPDATE pos_tickets 
        SET ${updates.join(", ")}, updated_at = NOW()
        WHERE id = ?
      `;

      await this.databaseManager.executeQuery(query, values);

      // Log audit trail
      await this.logAuditEvent(ticketId, staffId, "updated", updateData);

      const rawTicket = await this.getTicketById(ticketId);
      return {
        success: true,
        ticket: this.transformTicketData(rawTicket),
      };
    } catch (error) {
      console.error("Update ticket error:", error);
      return {
        success: false,
        error: error.message || "Failed to update ticket",
      };
    }
  }

  // Add item to ticket
  async addItemToTicket(ticketId, staffId, itemData) {
    try {
      const itemId = uuidv4();
      const {
        variantId,
        productId,
        title,
        variantTitle = null,
        sku = null,
        price,
        quantity = 1,
        inventoryQuantity = 0,
        notes = null,
      } = itemData;

      const lineTotal = parseFloat(price) * parseInt(quantity);

      const query = `
        INSERT INTO ticket_items (
          id, ticket_id, variant_id, product_id, title, variant_title,
          sku, price, quantity, line_total, inventory_quantity, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await this.databaseManager.executeQuery(query, [
        itemId,
        ticketId,
        variantId,
        productId,
        title,
        variantTitle,
        sku,
        price,
        quantity,
        lineTotal,
        inventoryQuantity,
        notes,
      ]);

      // Update ticket totals
      await this.recalculateTicketTotals(ticketId);

      // Log audit trail
      await this.logAuditEvent(ticketId, staffId, "item_added", {
        itemId,
        title,
        quantity,
        price,
      });

      return {
        success: true,
        item: await this.getTicketItemById(itemId),
      };
    } catch (error) {
      console.error("Add item to ticket error:", error);
      return {
        success: false,
        error: error.message || "Failed to add item to ticket",
      };
    }
  }

  // Update ticket item
  async updateTicketItem(itemId, staffId, updateData) {
    try {
      const allowedFields = [
        "quantity",
        "price",
        "discount_amount",
        "discount_type",
        "notes",
      ];
      const updates = [];
      const values = [];

      for (const [field, value] of Object.entries(updateData)) {
        if (allowedFields.includes(field)) {
          updates.push(`${field} = ?`);
          values.push(value);
        }
      }

      if (updates.length === 0) {
        return {
          success: false,
          error: "No valid fields to update",
        };
      }

      // Recalculate line total if quantity or price changed
      if (updateData.quantity || updateData.price) {
        const item = await this.getTicketItemById(itemId);
        if (item) {
          const newQuantity = updateData.quantity || item.quantity;
          const newPrice = updateData.price || item.price;
          const newLineTotal = parseFloat(newPrice) * parseInt(newQuantity);

          updates.push("line_total = ?");
          values.push(newLineTotal);
        }
      }

      values.push(itemId);

      const query = `
        UPDATE ticket_items 
        SET ${updates.join(", ")}, updated_at = NOW()
        WHERE id = ?
      `;

      await this.databaseManager.executeQuery(query, values);

      // Get ticket ID and recalculate totals
      const item = await this.getTicketItemById(itemId);
      if (item) {
        await this.recalculateTicketTotals(item.ticket_id);

        // Log audit trail
        await this.logAuditEvent(item.ticket_id, staffId, "item_updated", {
          itemId,
          updates: updateData,
        });
      }

      return {
        success: true,
        item: await this.getTicketItemById(itemId),
      };
    } catch (error) {
      console.error("Update ticket item error:", error);
      return {
        success: false,
        error: error.message || "Failed to update ticket item",
      };
    }
  }

  // Remove item from ticket
  async removeItemFromTicket(itemId, staffId) {
    try {
      // Get item details before deletion
      const item = await this.getTicketItemById(itemId);
      if (!item) {
        return {
          success: false,
          error: "Item not found",
        };
      }

      const ticketId = item.ticket_id;

      await this.databaseManager.executeQuery(
        "DELETE FROM ticket_items WHERE id = ?",
        [itemId]
      );

      // Recalculate ticket totals
      await this.recalculateTicketTotals(ticketId);

      // Log audit trail
      await this.logAuditEvent(ticketId, staffId, "item_removed", {
        itemId,
        title: item.title,
        quantity: item.quantity,
      });

      return {
        success: true,
        message: "Item removed successfully",
      };
    } catch (error) {
      console.error("Remove item from ticket error:", error);
      return {
        success: false,
        error: error.message || "Failed to remove item from ticket",
      };
    }
  }

  // Get ticket item by ID
  async getTicketItemById(itemId) {
    try {
      const query = "SELECT * FROM ticket_items WHERE id = ?";
      const [rows] = await this.databaseManager.executeQuery(query, [itemId]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error("Get ticket item by ID error:", error);
      return null;
    }
  }

  // Recalculate ticket totals
  async recalculateTicketTotals(ticketId) {
    try {
      // Calculate subtotal from items
      const itemsQuery = `
        SELECT 
          SUM(line_total - COALESCE(discount_amount, 0)) as subtotal
        FROM ticket_items 
        WHERE ticket_id = ?
      `;
      const [itemRows] = await this.databaseManager.executeQuery(itemsQuery, [
        ticketId,
      ]);
      const subtotal = parseFloat(itemRows[0].subtotal || 0);

      // Calculate total discounts
      const discountsQuery = `
        SELECT SUM(amount) as total_discounts
        FROM ticket_discounts 
        WHERE ticket_id = ?
      `;
      const [discountRows] = await this.databaseManager.executeQuery(
        discountsQuery,
        [ticketId]
      );
      const totalDiscounts = parseFloat(discountRows[0].total_discounts || 0);

      // Tax is always 0 for this POS system
      const tax = 0;
      const total = Math.max(0, subtotal - totalDiscounts + tax);

      // Update ticket totals
      await this.databaseManager.executeQuery(
        `
        UPDATE pos_tickets 
        SET subtotal = ?, tax = ?, discount_total = ?, total = ?, updated_at = NOW()
        WHERE id = ?
      `,
        [subtotal, tax, totalDiscounts, total, ticketId]
      );

      return { subtotal, tax, totalDiscounts, total };
    } catch (error) {
      console.error("Recalculate ticket totals error:", error);
      return null;
    }
  }

  // Log audit event
  async logAuditEvent(ticketId, staffId, action, details = {}) {
    try {
      const auditId = uuidv4();
      await this.databaseManager.executeQuery(
        `
        INSERT INTO ticket_audit_log (id, ticket_id, staff_id, action, details)
        VALUES (?, ?, ?, ?, ?)
      `,
        [auditId, ticketId, staffId, action, JSON.stringify(details)]
      );
    } catch (error) {
      console.error("Log audit event error:", error);
    }
  }

  // Delete ticket (soft delete by setting status to cancelled)
  async deleteTicket(ticketId, staffId) {
    try {
      await this.databaseManager.executeQuery(
        `
        UPDATE pos_tickets
        SET status = 'cancelled', updated_at = NOW()
        WHERE id = ?
      `,
        [ticketId]
      );

      // Log audit trail
      await this.logAuditEvent(ticketId, staffId, "cancelled", {});

      return {
        success: true,
        message: "Ticket cancelled successfully",
      };
    } catch (error) {
      console.error("Delete ticket error:", error);
      return {
        success: false,
        error: error.message || "Failed to delete ticket",
      };
    }
  }

  // Duplicate ticket with all items and discounts
  async duplicateTicket(ticketId, staffId, newName = null) {
    try {
      // Get original ticket
      const originalTicket = await this.getTicketById(ticketId);
      if (!originalTicket) {
        return {
          success: false,
          error: "Original ticket not found",
        };
      }

      const newTicketId = uuidv4();
      const duplicateName = newName || `${originalTicket.name} (Copy)`;

      // Create new ticket with same data
      const ticketQuery = `
        INSERT INTO pos_tickets (
          id, staff_id, terminal_id, location_id, name, customer_id,
          sales_agent_id, note, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')
      `;

      await this.databaseManager.executeQuery(ticketQuery, [
        newTicketId,
        staffId,
        originalTicket.terminal_id,
        originalTicket.location_id,
        duplicateName,
        originalTicket.customer_id,
        originalTicket.sales_agent_id,
        originalTicket.note,
      ]);

      // Duplicate all items
      if (originalTicket.items && originalTicket.items.length > 0) {
        for (const item of originalTicket.items) {
          const itemId = uuidv4();
          const itemQuery = `
            INSERT INTO ticket_items (
              id, ticket_id, variant_id, product_id, title, variant_title,
              sku, price, quantity, line_total, discount_amount, discount_type,
              notes, inventory_quantity
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          await this.databaseManager.executeQuery(itemQuery, [
            itemId,
            newTicketId,
            item.variant_id,
            item.product_id,
            item.title,
            item.variant_title,
            item.sku,
            item.price,
            item.quantity,
            item.line_total,
            item.discount_amount,
            item.discount_type,
            item.notes,
            item.inventory_quantity,
          ]);
        }
      }

      // Duplicate all discounts
      if (originalTicket.discounts && originalTicket.discounts.length > 0) {
        for (const discount of originalTicket.discounts) {
          const discountId = uuidv4();
          const discountQuery = `
            INSERT INTO ticket_discounts (
              id, ticket_id, type, value, description, applied_by
            ) VALUES (?, ?, ?, ?, ?, ?)
          `;

          await this.databaseManager.executeQuery(discountQuery, [
            discountId,
            newTicketId,
            discount.type,
            discount.value,
            discount.description,
            staffId,
          ]);
        }
      }

      // Recalculate totals for the new ticket
      await this.recalculateTicketTotals(newTicketId);

      // Log audit trail
      await this.logAuditEvent(newTicketId, staffId, "duplicated", {
        originalTicketId: ticketId,
        originalTicketName: originalTicket.name,
      });

      const rawTicket = await this.getTicketById(newTicketId);
      return {
        success: true,
        ticket: this.transformTicketData(rawTicket),
      };
    } catch (error) {
      console.error("Duplicate ticket error:", error);
      return {
        success: false,
        error: error.message || "Failed to duplicate ticket",
      };
    }
  }

  // Auto-save ticket with full state
  async autoSaveTicket(ticketId, staffId, ticketState) {
    try {
      const {
        name,
        items = [],
        customer,
        salesperson,
        note,
        discounts = [],
        subtotal = 0,
        tax = 0,
        total = 0,
        status = "active",
      } = ticketState;

      // Start transaction
      const connection = await this.databaseManager.getConnection();
      await connection.beginTransaction();

      try {
        // Update ticket metadata
        await connection.execute(
          `
          UPDATE pos_tickets
          SET name = ?, customer_id = ?, sales_agent_id = ?, note = ?,
              subtotal = ?, tax = ?, total = ?, status = ?,
              updated_at = NOW(), last_auto_save = NOW()
          WHERE id = ?
        `,
          [
            name || null,
            customer?.id || null,
            salesperson?.id || null,
            note || null,
            subtotal || 0,
            tax || 0,
            total || 0,
            status || "active",
            ticketId,
          ]
        );

        // Clear existing items
        await connection.execute(
          `DELETE FROM ticket_items WHERE ticket_id = ?`,
          [ticketId]
        );

        // Insert current items
        if (items.length > 0) {
          const itemValues = items.map((item) => [
            uuidv4(),
            ticketId,
            item.variantId,
            item.productId,
            item.title,
            item.variantTitle || null,
            item.sku || null,
            parseFloat(item.price),
            parseInt(item.quantity),
            parseFloat(item.price) * parseInt(item.quantity),
            item.discountAmount || 0,
            item.discountType || null,
            item.notes || null,
            item.inventoryQuantity || 0,
          ]);

          const placeholders = itemValues
            .map(() => "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
            .join(", ");
          const flatValues = itemValues.flat();

          await connection.execute(
            `
            INSERT INTO ticket_items (
              id, ticket_id, variant_id, product_id, title, variant_title,
              sku, price, quantity, line_total, discount_amount, discount_type,
              notes, inventory_quantity
            ) VALUES ${placeholders}
          `,
            flatValues
          );
        }

        // Clear existing discounts
        await connection.execute(
          `DELETE FROM ticket_discounts WHERE ticket_id = ?`,
          [ticketId]
        );

        // Insert current discounts
        if (discounts.length > 0) {
          const discountValues = discounts.map((discount) => [
            uuidv4(),
            ticketId,
            discount.type,
            discount.value,
            discount.description || null,
          ]);

          const discountPlaceholders = discountValues
            .map(() => "(?, ?, ?, ?, ?)")
            .join(", ");
          const flatDiscountValues = discountValues.flat();

          await connection.execute(
            `
            INSERT INTO ticket_discounts (
              id, ticket_id, type, value, description
            ) VALUES ${discountPlaceholders}
          `,
            flatDiscountValues
          );
        }

        await connection.commit();

        // Log auto-save event
        await this.logAuditEvent(ticketId, staffId, "auto_saved", {
          itemCount: items.length,
          discountCount: discounts.length,
          total: total,
        });

        return {
          success: true,
          message: "Ticket auto-saved successfully",
          timestamp: new Date().toISOString(),
        };
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error("Auto-save ticket error:", error);
      return {
        success: false,
        error: error.message || "Failed to auto-save ticket",
      };
    }
  }

  // Batch auto-save multiple tickets
  async batchAutoSaveTickets(staffId, ticketsData) {
    try {
      const results = [];

      for (const { ticketId, ticketState } of ticketsData) {
        const result = await this.autoSaveTicket(
          ticketId,
          staffId,
          ticketState
        );
        results.push({ ticketId, ...result });
      }

      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.length - successCount;

      return {
        success: failureCount === 0,
        results,
        summary: {
          total: results.length,
          successful: successCount,
          failed: failureCount,
        },
      };
    } catch (error) {
      console.error("Batch auto-save error:", error);
      return {
        success: false,
        error: error.message || "Failed to batch auto-save tickets",
      };
    }
  }

  // Cleanup expired tickets
  async cleanupExpiredTickets() {
    try {
      const expiryHours = process.env.TICKET_EXPIRY_HOURS || 24;

      // Find expired tickets
      const [expiredTickets] = await this.databaseManager.executeQuery(
        `
        SELECT id, name, staff_id
        FROM pos_tickets
        WHERE status IN ('active', 'paused')
        AND (
          expires_at IS NOT NULL AND expires_at < NOW()
          OR (expires_at IS NULL AND updated_at < DATE_SUB(NOW(), INTERVAL ? HOUR))
        )
      `,
        [expiryHours]
      );

      if (expiredTickets.length === 0) {
        return {
          success: true,
          message: "No expired tickets found",
          cleanedCount: 0,
        };
      }

      // Update expired tickets to cancelled status
      const ticketIds = expiredTickets.map((t) => t.id);
      const placeholders = ticketIds.map(() => "?").join(",");

      await this.databaseManager.executeQuery(
        `
        UPDATE pos_tickets
        SET status = 'expired', updated_at = NOW()
        WHERE id IN (${placeholders})
      `,
        ticketIds
      );

      // Log cleanup events
      for (const ticket of expiredTickets) {
        await this.logAuditEvent(ticket.id, "system", "expired", {
          ticketName: ticket.name,
          originalStaffId: ticket.staff_id,
          cleanupReason: "automatic_expiry",
        });
      }

      return {
        success: true,
        message: `Cleaned up ${expiredTickets.length} expired tickets`,
        cleanedCount: expiredTickets.length,
        cleanedTickets: expiredTickets.map((t) => ({ id: t.id, name: t.name })),
      };
    } catch (error) {
      console.error("Cleanup expired tickets error:", error);
      return {
        success: false,
        error: error.message || "Failed to cleanup expired tickets",
      };
    }
  }

  // Cleanup old completed tickets
  async cleanupOldCompletedTickets(daysOld = 30) {
    try {
      // Find old completed tickets
      const [oldTickets] = await this.databaseManager.executeQuery(
        `
        SELECT id, name, staff_id
        FROM pos_tickets
        WHERE status = 'completed'
        AND completed_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      `,
        [daysOld]
      );

      if (oldTickets.length === 0) {
        return {
          success: true,
          message: "No old completed tickets found",
          archivedCount: 0,
        };
      }

      // Archive old tickets (move to archive table or mark as archived)
      const ticketIds = oldTickets.map((t) => t.id);
      const placeholders = ticketIds.map(() => "?").join(",");

      await this.databaseManager.executeQuery(
        `
        UPDATE pos_tickets
        SET status = 'archived', updated_at = NOW()
        WHERE id IN (${placeholders})
      `,
        ticketIds
      );

      return {
        success: true,
        message: `Archived ${oldTickets.length} old completed tickets`,
        archivedCount: oldTickets.length,
      };
    } catch (error) {
      console.error("Cleanup old completed tickets error:", error);
      return {
        success: false,
        error: error.message || "Failed to cleanup old completed tickets",
      };
    }
  }

  // Get ticket recovery data for a staff member
  async getRecoveryTickets(staffId) {
    try {
      const [tickets] = await this.databaseManager.executeQuery(
        `
        SELECT
          t.*,
          COUNT(ti.id) as item_count,
          MAX(t.updated_at) as last_activity
        FROM pos_tickets t
        LEFT JOIN ticket_items ti ON t.id = ti.ticket_id
        WHERE t.staff_id = ?
        AND t.status IN ('active', 'paused')
        AND t.updated_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY t.id
        ORDER BY t.updated_at DESC
      `,
        [staffId]
      );

      const recoveryTickets = [];

      for (const ticket of tickets) {
        const fullTicket = await this.getTicketById(ticket.id);
        if (fullTicket) {
          const transformedTicket = this.transformTicketData(fullTicket);
          recoveryTickets.push({
            ...transformedTicket,
            item_count: ticket.item_count,
            last_activity: ticket.last_activity,
            can_recover: true,
          });
        }
      }

      return {
        success: true,
        tickets: recoveryTickets,
      };
    } catch (error) {
      console.error("Get recovery tickets error:", error);
      return {
        success: false,
        error: error.message || "Failed to get recovery tickets",
      };
    }
  }

  // Get auto-save statistics
  async getAutoSaveStats(staffId = null, days = 7) {
    try {
      let query = `
        SELECT
          DATE(created_at) as date,
          COUNT(*) as auto_save_count,
          COUNT(DISTINCT ticket_id) as unique_tickets
        FROM ticket_audit_log
        WHERE action = 'auto_saved'
        AND created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
      `;

      const params = [days];

      if (staffId) {
        query += ` AND staff_id = ?`;
        params.push(staffId);
      }

      query += ` GROUP BY DATE(created_at) ORDER BY date DESC`;

      const [stats] = await this.databaseManager.executeQuery(query, params);

      return {
        success: true,
        stats,
        summary: {
          totalAutoSaves: stats.reduce((sum, s) => sum + s.auto_save_count, 0),
          uniqueTickets: new Set(stats.map((s) => s.unique_tickets)).size,
          daysWithActivity: stats.length,
        },
      };
    } catch (error) {
      console.error("Get auto-save stats error:", error);
      return {
        success: false,
        error: error.message || "Failed to get auto-save statistics",
      };
    }
  }
}

module.exports = new TicketManagementService();
