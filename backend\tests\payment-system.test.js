/**
 * Comprehensive Payment System Test Suite
 * 
 * Tests all payment methods, split payments, and edge cases
 */

const request = require('supertest');
const app = require('../src/app');
const PaymentTransactionService = require('../src/services/payment-transaction-service');
const MpesaIntegrationService = require('../src/services/mpesa-integration-service');

describe('Payment System Integration Tests', () => {
  let authToken;
  let testStaffId;
  let testCustomerId;
  let testTransactionId;

  beforeAll(async () => {
    // Setup test authentication
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpassword'
      });

    authToken = loginResponse.body.token;
    testStaffId = loginResponse.body.user.id;
  });

  beforeEach(async () => {
    // Clean up any existing test data
    // This would typically reset test database state
  });

  describe('Payment Transaction Initiation', () => {
    test('should initiate single payment transaction', async () => {
      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'cash',
            methodName: 'Cash Payment',
            amount: 1000
          }]
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.transactionId).toBeDefined();
      expect(response.body.data.totalAmount).toBe(1000);
      expect(response.body.data.isSplitPayment).toBe(false);

      testTransactionId = response.body.data.transactionId;
    });

    test('should initiate split payment transaction', async () => {
      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1500,
          currency: 'KES',
          paymentMethods: [
            {
              methodType: 'cash',
              methodName: 'Cash Payment',
              amount: 800
            },
            {
              methodType: 'mpesa',
              methodName: 'M-Pesa Payment',
              amount: 700
            }
          ]
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.isSplitPayment).toBe(true);
      expect(response.body.data.paymentMethods).toBe(2);
    });

    test('should reject invalid payment amounts', async () => {
      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'cash',
            methodName: 'Cash Payment',
            amount: 500 // Amount doesn't match total
          }]
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('amount');
    });
  });

  describe('Cash Payment Processing', () => {
    test('should process cash payment successfully', async () => {
      // First initiate transaction
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'cash',
            methodName: 'Cash Payment',
            amount: 1000
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      // Add payment method
      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'cash',
          methodName: 'Cash Payment',
          amount: 1000
        });

      const methodId = methodResponse.body.data.methodId;

      // Process payment
      const processResponse = await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amountTendered: 1200,
          change: 200
        });

      expect(processResponse.status).toBe(200);
      expect(processResponse.body.success).toBe(true);
      expect(processResponse.body.data.status).toBe('completed');
    });

    test('should handle insufficient cash amount', async () => {
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'cash',
            methodName: 'Cash Payment',
            amount: 1000
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'cash',
          methodName: 'Cash Payment',
          amount: 1000
        });

      const methodId = methodResponse.body.data.methodId;

      const processResponse = await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amountTendered: 800 // Insufficient amount
        });

      expect(processResponse.status).toBe(400);
      expect(processResponse.body.success).toBe(false);
      expect(processResponse.body.error).toContain('insufficient');
    });
  });

  describe('M-Pesa Payment Processing', () => {
    test('should initiate M-Pesa STK Push', async () => {
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'mpesa',
            methodName: 'M-Pesa Payment',
            amount: 1000
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'mpesa',
          methodName: 'M-Pesa Payment',
          amount: 1000
        });

      const methodId = methodResponse.body.data.methodId;

      const processResponse = await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          paymentMethod: 'stk_push',
          phoneNumber: '************',
          accountReference: 'TEST_ORDER',
          transactionDesc: 'Test payment'
        });

      expect(processResponse.status).toBe(200);
      expect(processResponse.body.success).toBe(true);
      expect(processResponse.body.data.metadata.checkoutRequestId).toBeDefined();
    });

    test('should validate M-Pesa transaction code', async () => {
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'mpesa',
            methodName: 'M-Pesa Payment',
            amount: 1000
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'mpesa',
          methodName: 'M-Pesa Payment',
          amount: 1000
        });

      const methodId = methodResponse.body.data.methodId;

      const processResponse = await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          paymentMethod: 'manual_code',
          transactionCode: 'QGH1234567',
          phoneNumber: '************'
        });

      expect(processResponse.status).toBe(200);
      expect(processResponse.body.success).toBe(true);
    });

    test('should reject invalid M-Pesa phone number', async () => {
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'mpesa',
            methodName: 'M-Pesa Payment',
            amount: 1000
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'mpesa',
          methodName: 'M-Pesa Payment',
          amount: 1000
        });

      const methodId = methodResponse.body.data.methodId;

      const processResponse = await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          paymentMethod: 'stk_push',
          phoneNumber: '123456', // Invalid phone number
          accountReference: 'TEST_ORDER',
          transactionDesc: 'Test payment'
        });

      expect(processResponse.status).toBe(400);
      expect(processResponse.body.success).toBe(false);
      expect(processResponse.body.error).toContain('phone');
    });
  });

  describe('ABSA Till Payment Processing', () => {
    test('should process ABSA Till payment', async () => {
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'absa_till',
            methodName: 'ABSA Till Payment',
            amount: 1000
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'absa_till',
          methodName: 'ABSA Till Payment',
          amount: 1000
        });

      const methodId = methodResponse.body.data.methodId;

      const processResponse = await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          transactionCode: 'ABSA1234567',
          tillNumber: '123456'
        });

      expect(processResponse.status).toBe(200);
      expect(processResponse.body.success).toBe(true);
    });

    test('should reject invalid ABSA transaction code', async () => {
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'absa_till',
            methodName: 'ABSA Till Payment',
            amount: 1000
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'absa_till',
          methodName: 'ABSA Till Payment',
          amount: 1000
        });

      const methodId = methodResponse.body.data.methodId;

      const processResponse = await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          transactionCode: '123', // Too short
          tillNumber: '123456'
        });

      expect(processResponse.status).toBe(400);
      expect(processResponse.body.success).toBe(false);
      expect(processResponse.body.error).toContain('format');
    });
  });

  describe('Split Payment Scenarios', () => {
    test('should process complete split payment', async () => {
      // Initiate split payment
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 2000,
          currency: 'KES',
          paymentMethods: [
            {
              methodType: 'cash',
              methodName: 'Cash Payment',
              amount: 1200
            },
            {
              methodType: 'mpesa',
              methodName: 'M-Pesa Payment',
              amount: 800
            }
          ]
        });

      const transactionId = initResponse.body.data.transactionId;

      // Process cash payment
      const cashMethodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'cash',
          methodName: 'Cash Payment',
          amount: 1200
        });

      const cashMethodId = cashMethodResponse.body.data.methodId;

      await request(app)
        .post(`/api/payments/methods/${cashMethodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amountTendered: 1200
        });

      // Process M-Pesa payment
      const mpesaMethodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'mpesa',
          methodName: 'M-Pesa Payment',
          amount: 800
        });

      const mpesaMethodId = mpesaMethodResponse.body.data.methodId;

      await request(app)
        .post(`/api/payments/methods/${mpesaMethodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          paymentMethod: 'manual_code',
          transactionCode: 'QGH7654321',
          phoneNumber: '************'
        });

      // Check final status
      const statusResponse = await request(app)
        .get(`/api/payments/${transactionId}/status`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(statusResponse.status).toBe(200);
      expect(statusResponse.body.data.status).toBe('completed');
      expect(statusResponse.body.data.remainingAmount).toBe(0);
    });

    test('should validate split payment amounts', async () => {
      const response = await request(app)
        .post('/api/payments/validate-split')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1000,
          paymentMethods: [
            {
              methodType: 'cash',
              amount: 600
            },
            {
              methodType: 'mpesa',
              amount: 500 // Total is 1100, exceeds 1000
            }
          ]
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('amount');
    });
  });

  describe('Transaction Status and Balance', () => {
    test('should get transaction balance correctly', async () => {
      // Create partial payment scenario
      const initResponse = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          totalAmount: 1500,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'cash',
            methodName: 'Cash Payment',
            amount: 800
          }]
        });

      const transactionId = initResponse.body.data.transactionId;

      // Process partial payment
      const methodResponse = await request(app)
        .post(`/api/payments/${transactionId}/methods`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          methodType: 'cash',
          methodName: 'Cash Payment',
          amount: 800
        });

      const methodId = methodResponse.body.data.methodId;

      await request(app)
        .post(`/api/payments/methods/${methodId}/process`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amountTendered: 800
        });

      // Check balance
      const balanceResponse = await request(app)
        .get(`/api/payments/${transactionId}/balance`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(balanceResponse.status).toBe(200);
      expect(balanceResponse.body.data.totalAmount).toBe(1500);
      expect(balanceResponse.body.data.completedAmount).toBe(800);
      expect(balanceResponse.body.data.remainingAmount).toBe(700);
      expect(balanceResponse.body.data.isPartiallyPaid).toBe(true);
      expect(balanceResponse.body.data.isFullyPaid).toBe(false);
    });
  });

  afterAll(async () => {
    // Clean up test data
    // Close database connections, etc.
  });
});
