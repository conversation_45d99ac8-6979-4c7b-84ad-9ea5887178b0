/**
 * Shipping Rates Manager Component
 *
 * Management interface for shipping rates configuration.
 * Allows managers to create, edit, and manage shipping rates with proper RBAC.
 */

import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { PermissionGate } from "@/src/components/auth/PermissionGate";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  useShippingRates,
  useUpsertShippingRate,
  useUpdateShippingRate,
} from "@/src/hooks/useFulfillment";
import { useRBAC } from "@/src/hooks/useRBAC";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { useState } from "react";
import {
  ActivityIndicator,
  <PERSON><PERSON>,
  <PERSON>rollView,
  StyleSheet,
  Switch,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface ShippingRateForm {
  id?: string;
  delivery_method: string;
  base_fee: string;
  per_km_fee: string;
  per_kg_fee: string;
  minimum_fee: string;
  maximum_fee: string;
  description: string;
  is_active: boolean;
}

export const ShippingRatesManager: React.FC = () => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const { hasPermission } = useRBAC();

  // State management
  const [isEditing, setIsEditing] = useState(false);
  const [editingRate, setEditingRate] = useState<ShippingRateForm | null>(null);
  const [formData, setFormData] = useState<ShippingRateForm>({
    delivery_method: "",
    base_fee: "",
    per_km_fee: "",
    per_kg_fee: "",
    minimum_fee: "",
    maximum_fee: "",
    description: "",
    is_active: true,
  });

  // React Query hooks
  const { data: shippingRates, isLoading: ratesLoading } = useShippingRates();
  const upsertRateMutation = useUpsertShippingRate();
  const updateRateMutation = useUpdateShippingRate();

  // Check permissions
  const canManageRates = hasPermission("manage_shipping_rates");

  // Create theme-aware styles
  const styles = createStyles(theme);

  if (!canManageRates) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol
            name="exclamationmark.triangle"
            size={48}
            color={theme.colors.error}
          />
          <ThemedText variant="h3" style={utils.mt("md")}>
            Access Denied
          </ThemedText>
          <ThemedText variant="body" color="secondary" style={utils.mt("sm")}>
            You don't have permission to manage shipping rates.
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  const handleEditRate = (rate: any) => {
    setEditingRate(rate);
    setFormData({
      id: rate.id,
      delivery_method: rate.delivery_method,
      base_fee: rate.base_fee.toString(),
      per_km_fee: rate.per_km_fee?.toString() || "",
      per_kg_fee: rate.per_kg_fee?.toString() || "",
      minimum_fee: rate.minimum_fee?.toString() || "",
      maximum_fee: rate.maximum_fee?.toString() || "",
      description: rate.description || "",
      is_active: rate.is_active,
    });
    setIsEditing(true);
  };

  const handleCreateNew = () => {
    setEditingRate(null);
    setFormData({
      delivery_method: "",
      base_fee: "",
      per_km_fee: "",
      per_kg_fee: "",
      minimum_fee: "",
      maximum_fee: "",
      description: "",
      is_active: true,
    });
    setIsEditing(true);
  };

  const handleSaveRate = async () => {
    if (!formData.delivery_method.trim() || !formData.base_fee.trim()) {
      Alert.alert("Error", "Delivery method and base fee are required");
      return;
    }

    try {
      const rateData = {
        delivery_method: formData.delivery_method
          .toLowerCase()
          .replace(/\s+/g, "_"),
        base_fee: parseFloat(formData.base_fee),
        per_km_fee: formData.per_km_fee
          ? parseFloat(formData.per_km_fee)
          : null,
        per_kg_fee: formData.per_kg_fee
          ? parseFloat(formData.per_kg_fee)
          : null,
        minimum_fee: formData.minimum_fee
          ? parseFloat(formData.minimum_fee)
          : null,
        maximum_fee: formData.maximum_fee
          ? parseFloat(formData.maximum_fee)
          : null,
        description: formData.description.trim(),
        is_active: formData.is_active,
      };

      if (editingRate) {
        await updateRateMutation.mutateAsync({
          rateId: editingRate.id!,
          rateData,
        });
      } else {
        await upsertRateMutation.mutateAsync(rateData);
      }

      setIsEditing(false);
      setEditingRate(null);
      Alert.alert("Success", "Shipping rate saved successfully");
    } catch (error) {
      Alert.alert("Error", "Failed to save shipping rate");
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditingRate(null);
  };

  const renderRateForm = () => (
    <ModernCard style={utils.mb("lg")}>
      <ThemedText variant="h3" style={utils.mb("md")}>
        {editingRate ? "Edit Shipping Rate" : "Create New Shipping Rate"}
      </ThemedText>

      <View style={styles.formGroup}>
        <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
          Delivery Method *
        </ThemedText>
        <TextInput
          style={[styles.input, { color: theme.colors.text }]}
          value={formData.delivery_method}
          onChangeText={(text) =>
            setFormData({ ...formData, delivery_method: text })
          }
          placeholder="e.g., Standard, Express, Local Pickup"
          placeholderTextColor={theme.colors.textSecondary}
        />
      </View>

      <View style={styles.formGroup}>
        <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
          Base Fee (KES) *
        </ThemedText>
        <TextInput
          style={[styles.input, { color: theme.colors.text }]}
          value={formData.base_fee}
          onChangeText={(text) => setFormData({ ...formData, base_fee: text })}
          placeholder="200.00"
          placeholderTextColor={theme.colors.textSecondary}
          keyboardType="decimal-pad"
        />
      </View>

      <View style={styles.formRow}>
        <View style={styles.formHalf}>
          <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
            Per KM Fee (KES)
          </ThemedText>
          <TextInput
            style={[styles.input, { color: theme.colors.text }]}
            value={formData.per_km_fee}
            onChangeText={(text) =>
              setFormData({ ...formData, per_km_fee: text })
            }
            placeholder="50.00"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="decimal-pad"
          />
        </View>

        <View style={styles.formHalf}>
          <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
            Per KG Fee (KES)
          </ThemedText>
          <TextInput
            style={[styles.input, { color: theme.colors.text }]}
            value={formData.per_kg_fee}
            onChangeText={(text) =>
              setFormData({ ...formData, per_kg_fee: text })
            }
            placeholder="100.00"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="decimal-pad"
          />
        </View>
      </View>

      <View style={styles.formRow}>
        <View style={styles.formHalf}>
          <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
            Minimum Fee (KES)
          </ThemedText>
          <TextInput
            style={[styles.input, { color: theme.colors.text }]}
            value={formData.minimum_fee}
            onChangeText={(text) =>
              setFormData({ ...formData, minimum_fee: text })
            }
            placeholder="200.00"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="decimal-pad"
          />
        </View>

        <View style={styles.formHalf}>
          <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
            Maximum Fee (KES)
          </ThemedText>
          <TextInput
            style={[styles.input, { color: theme.colors.text }]}
            value={formData.maximum_fee}
            onChangeText={(text) =>
              setFormData({ ...formData, maximum_fee: text })
            }
            placeholder="2000.00"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="decimal-pad"
          />
        </View>
      </View>

      <View style={styles.formGroup}>
        <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
          Description
        </ThemedText>
        <TextInput
          style={[styles.input, styles.textArea, { color: theme.colors.text }]}
          value={formData.description}
          onChangeText={(text) =>
            setFormData({ ...formData, description: text })
          }
          placeholder="Describe this delivery method..."
          placeholderTextColor={theme.colors.textSecondary}
          multiline
          numberOfLines={3}
        />
      </View>

      <View style={styles.switchContainer}>
        <ThemedText variant="bodyMedium">Active</ThemedText>
        <Switch
          value={formData.is_active}
          onValueChange={(value) =>
            setFormData({ ...formData, is_active: value })
          }
          trackColor={{
            false: theme.colors.border,
            true: theme.colors.primary,
          }}
          thumbColor={formData.is_active ? "#FFFFFF" : theme.colors.surface}
        />
      </View>

      <View style={styles.formActions}>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={handleSaveRate}
          disabled={
            upsertRateMutation.isPending || updateRateMutation.isPending
          }
        >
          <ThemedText variant="bodyMedium" style={styles.primaryButtonText}>
            {upsertRateMutation.isPending || updateRateMutation.isPending
              ? "Saving..."
              : "Save Rate"}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity style={styles.secondaryButton} onPress={handleCancel}>
          <ThemedText variant="bodyMedium" style={styles.secondaryButtonText}>
            Cancel
          </ThemedText>
        </TouchableOpacity>
      </View>
    </ModernCard>
  );

  const renderRatesList = () => (
    <View>
      <View style={styles.listHeader}>
        <ThemedText variant="h3">Current Shipping Rates</ThemedText>
        <TouchableOpacity style={styles.addButton} onPress={handleCreateNew}>
          <IconSymbol
            name="plus.circle.fill"
            size={20}
            color={theme.colors.primary}
          />
          <ThemedText variant="small" color="primary" style={utils.ml("xs")}>
            Add New
          </ThemedText>
        </TouchableOpacity>
      </View>

      {ratesLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <ThemedText variant="body" style={utils.mt("md")}>
            Loading shipping rates...
          </ThemedText>
        </View>
      ) : (
        <View style={styles.ratesList}>
          {shippingRates?.map((rate) => (
            <ModernCard key={rate.id} style={styles.rateCard}>
              <View style={styles.rateHeader}>
                <View style={styles.rateInfo}>
                  <ThemedText variant="bodyMedium" style={styles.rateName}>
                    {rate.delivery_method.toUpperCase()}
                  </ThemedText>
                  <ThemedText variant="small" color="secondary">
                    {rate.description}
                  </ThemedText>
                </View>
                <View style={styles.rateActions}>
                  <View
                    style={[
                      styles.statusBadge,
                      rate.is_active
                        ? styles.activeBadge
                        : styles.inactiveBadge,
                    ]}
                  >
                    <ThemedText
                      variant="small"
                      style={[
                        styles.statusText,
                        rate.is_active
                          ? styles.activeText
                          : styles.inactiveText,
                      ]}
                    >
                      {rate.is_active ? "ACTIVE" : "INACTIVE"}
                    </ThemedText>
                  </View>
                  <TouchableOpacity
                    style={styles.editButton}
                    onPress={() => handleEditRate(rate)}
                  >
                    <IconSymbol
                      name="pencil"
                      size={16}
                      color={theme.colors.primary}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.rateDetails}>
                <View style={styles.rateDetail}>
                  <ThemedText variant="small" color="secondary">
                    Base Fee:
                  </ThemedText>
                  <ThemedText variant="small">KES {rate.base_fee}</ThemedText>
                </View>
                {rate.per_km_fee && (
                  <View style={styles.rateDetail}>
                    <ThemedText variant="small" color="secondary">
                      Per KM:
                    </ThemedText>
                    <ThemedText variant="small">
                      KES {rate.per_km_fee}
                    </ThemedText>
                  </View>
                )}
                {rate.per_kg_fee && (
                  <View style={styles.rateDetail}>
                    <ThemedText variant="small" color="secondary">
                      Per KG:
                    </ThemedText>
                    <ThemedText variant="small">
                      KES {rate.per_kg_fee}
                    </ThemedText>
                  </View>
                )}
              </View>
            </ModernCard>
          ))}
        </View>
      )}
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {isEditing ? renderRateForm() : renderRatesList()}
      </ScrollView>
    </ThemedView>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
    },
    scrollContent: {
      padding: 16,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    formGroup: {
      marginBottom: 16,
    },
    formRow: {
      flexDirection: "row",
      gap: 12,
      marginBottom: 16,
    },
    formHalf: {
      flex: 1,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      backgroundColor: theme.colors.surface,
    },
    textArea: {
      height: 80,
      textAlignVertical: "top",
    },
    switchContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 24,
      paddingVertical: 8,
    },
    formActions: {
      gap: 12,
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
      padding: 16,
      borderRadius: 8,
      alignItems: "center",
    },
    primaryButtonText: {
      color: "#FFFFFF",
      fontWeight: "600",
    },
    secondaryButton: {
      backgroundColor: theme.colors.surface,
      padding: 16,
      borderRadius: 8,
      alignItems: "center",
    },
    secondaryButtonText: {
      color: theme.colors.textSecondary,
      fontWeight: "600",
    },
    listHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 16,
    },
    addButton: {
      flexDirection: "row",
      alignItems: "center",
      padding: 8,
    },
    ratesList: {
      gap: 12,
    },
    rateCard: {
      padding: 16,
    },
    rateHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: 12,
    },
    rateInfo: {
      flex: 1,
    },
    rateName: {
      fontWeight: "600",
      marginBottom: 4,
    },
    rateActions: {
      flexDirection: "row",
      alignItems: "center",
      gap: 8,
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    activeBadge: {
      backgroundColor: theme.colors.successBackground,
    },
    inactiveBadge: {
      backgroundColor: theme.colors.warningBackground,
    },
    statusText: {
      fontSize: 10,
      fontWeight: "600",
    },
    activeText: {
      color: theme.colors.success,
    },
    inactiveText: {
      color: theme.colors.warning,
    },
    editButton: {
      padding: 8,
    },
    rateDetails: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 16,
    },
    rateDetail: {
      flexDirection: "row",
      gap: 4,
    },
  });
