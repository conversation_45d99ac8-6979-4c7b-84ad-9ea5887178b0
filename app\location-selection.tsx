import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLocation } from '@/src/contexts/LocationContext';

interface Location {
  id: string;
  name: string;
  address: {
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    country?: string;
    zip?: string;
    phone?: string;
  };
  active: boolean;
}

const LocationSelectionScreen: React.FC = () => {
  const router = useRouter();
  const { 
    availableLocations, 
    setCurrentLocation, 
    loadLocations, 
    isLoading, 
    error 
  } = useLocation();
  
  const [filteredLocations, setFilteredLocations] = useState<Location[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (availableLocations.length === 0) {
      loadLocations();
    }
  }, []);

  useEffect(() => {
    filterLocations();
  }, [searchQuery, availableLocations]);

  const filterLocations = () => {
    if (!searchQuery.trim()) {
      setFilteredLocations(availableLocations);
      return;
    }

    const filtered = availableLocations.filter(location =>
      location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.address.city?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.address.country?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredLocations(filtered);
  };

  const handleSelectLocation = (location: Location) => {
    Alert.alert(
      'Select Location',
      `Set ${location.name} as your POS terminal location?\n\nAll sales will be attributed to this location and inventory will be managed accordingly.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Select',
          onPress: async () => {
            await setCurrentLocation(location);
            router.replace('/(tabs)/products');
          },
        },
      ]
    );
  };

  const formatAddress = (address: Location['address']) => {
    const parts = [];
    if (address.address1) parts.push(address.address1);
    if (address.city) parts.push(address.city);
    if (address.province) parts.push(address.province);
    if (address.country) parts.push(address.country);
    return parts.join(', ') || 'No address provided';
  };

  const renderLocation = ({ item }: { item: Location }) => (
    <TouchableOpacity
      style={styles.locationCard}
      onPress={() => handleSelectLocation(item)}
    >
      <View style={styles.locationHeader}>
        <View style={styles.locationInfo}>
          <Text style={styles.locationName}>{item.name}</Text>
          <Text style={styles.locationAddress}>
            📍 {formatAddress(item.address)}
          </Text>
          {item.address.phone && (
            <Text style={styles.locationPhone}>
              📞 {item.address.phone}
            </Text>
          )}
        </View>
        <View style={styles.locationStatus}>
          <View style={[styles.statusDot, { backgroundColor: '#2ecc71' }]} />
          <Text style={styles.statusText}>Active</Text>
        </View>
      </View>

      <View style={styles.selectButton}>
        <Text style={styles.selectButtonText}>Select Location</Text>
        <Ionicons name="chevron-forward" size={16} color="#2ecc71" />
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2ecc71" />
        <Text style={styles.loadingText}>Loading locations...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={64} color="#e74c3c" />
        <Text style={styles.errorTitle}>Failed to Load Locations</Text>
        <Text style={styles.errorMessage}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={loadLocations}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Select Your Location</Text>
        <Text style={styles.subtitle}>
          Choose the location where this POS terminal will operate
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search locations..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
      </View>

      <View style={styles.infoCard}>
        <Ionicons name="information-circle" size={20} color="#2ecc71" />
        <Text style={styles.infoText}>
          Your selected location will determine which inventory is available for sale 
          and where orders are attributed. You can change this later in settings.
        </Text>
      </View>

      {filteredLocations.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="location" size={64} color="#ccc" />
          <Text style={styles.emptyTitle}>
            {searchQuery ? 'No locations found' : 'No locations available'}
          </Text>
          <Text style={styles.emptySubtitle}>
            {searchQuery
              ? 'Try a different search term'
              : 'Contact your administrator to set up locations'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredLocations}
          renderItem={renderLocation}
          keyExtractor={(item) => item.id}
          style={styles.locationsList}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.locationsListContent}
        />
      )}

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          {filteredLocations.length} location{filteredLocations.length !== 1 ? 's' : ''} available
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e8ed',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e1e8ed',
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: '#f0f9f4',
    marginHorizontal: 16,
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#2ecc71',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#2ecc71',
    marginLeft: 8,
    lineHeight: 20,
  },
  locationsList: {
    flex: 1,
  },
  locationsListContent: {
    padding: 16,
  },
  locationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e1e8ed',
  },
  locationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  locationPhone: {
    fontSize: 14,
    color: '#666',
  },
  locationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#2ecc71',
    fontWeight: '600',
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f9f4',
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#2ecc71',
  },
  selectButtonText: {
    color: '#2ecc71',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 4,
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e1e8ed',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#2ecc71',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default LocationSelectionScreen;
