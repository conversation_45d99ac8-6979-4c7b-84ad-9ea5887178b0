/**
 * Check Database Tables
 * Quick script to verify tables exist in the database
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function checkTables() {
  let connection;
  
  try {
    console.log('🔍 Checking database tables...\n');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'dukalink',
      password: process.env.DB_PASSWORD || 'dukalink_secure_password_2024',
      database: process.env.DB_NAME || 'dukalink_pos',
      charset: 'utf8mb4'
    });

    console.log(`✅ Connected to database: ${process.env.DB_NAME}`);
    
    // Check if tables exist
    const [tables] = await connection.execute('SHOW TABLES');
    
    console.log('\n📋 Tables in database:');
    if (tables.length === 0) {
      console.log('❌ No tables found in database');
    } else {
      tables.forEach((table, index) => {
        const tableName = Object.values(table)[0];
        console.log(`${index + 1}. ${tableName}`);
      });
    }
    
    // Check specific tables we need
    const requiredTables = ['pos_staff', 'staff_permissions', 'sales_agents', 'pos_terminals'];
    
    console.log('\n🔍 Checking required tables:');
    for (const tableName of requiredTables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`✅ ${tableName}: ${rows[0].count} records`);
      } catch (error) {
        console.log(`❌ ${tableName}: Table does not exist`);
      }
    }
    
    // Check staff data specifically
    try {
      const [staff] = await connection.execute('SELECT username, role FROM pos_staff');
      console.log('\n👥 Staff users:');
      staff.forEach(user => {
        console.log(`   • ${user.username} (${user.role})`);
      });
    } catch (error) {
      console.log('\n❌ Could not fetch staff data:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Disconnected from database');
    }
  }
}

checkTables();
