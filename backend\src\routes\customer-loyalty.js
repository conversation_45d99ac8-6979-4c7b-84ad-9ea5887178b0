/**
 * Customer Loyalty API Routes
 * Handles customer loyalty management including points balance, tier status,
 * transaction history, and redemption endpoints
 * Follows patterns from staff-management.js
 */

const express = require("express");
const router = express.Router();
const loyaltyService = require("../services/loyalty-service");
const shopifyMetafieldsService = require("../services/shopify-metafields-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");

// Get customer loyalty summary
router.get(
  "/customers/:customerId/summary",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;
      const { includeShopifyData = false } = req.query;

      // Get loyalty summary from backend
      const result = await loyaltyService.getCustomerSummary(customerId);

      if (result.success) {
        let responseData = result.summary;

        // Optionally include Shopify metafields data
        if (includeShopifyData === "true") {
          const shopifyResult =
            await shopifyMetafieldsService.getCustomerLoyaltyData(
              `gid://shopify/Customer/${customerId}`
            );
          if (shopifyResult.success) {
            responseData.shopifyData = shopifyResult.loyaltyData;
          }
        }

        return ResponseFormatter.success(
          res,
          responseData,
          "Customer loyalty summary retrieved successfully"
        );
      } else {
        return ResponseFormatter.error(res, result.error, 404);
      }
    } catch (error) {
      console.error("Get customer loyalty summary error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to get customer loyalty summary"
      );
    }
  }
);

// Get customer transaction history
router.get(
  "/customers/:customerId/transactions",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;
      const { limit = 20, offset = 0 } = req.query;

      // Ensure limit and offset are valid numbers
      const parsedLimit = parseInt(limit) || 20;
      const parsedOffset = parseInt(offset) || 0;

      const result = await loyaltyService.getTransactionHistory(
        customerId,
        parsedLimit,
        parsedOffset
      );

      if (result.success) {
        return ResponseFormatter.paginated(
          res,
          { transactions: result.transactions },
          result.pagination,
          "Transaction history retrieved successfully"
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Get transaction history error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to get transaction history"
      );
    }
  }
);

// Add loyalty points for a purchase
router.post(
  "/customers/:customerId/points/add",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;
      const { orderTotal, orderId, salesAgentId } = req.body;

      // Validate required fields
      if (!orderTotal || orderTotal <= 0) {
        return ResponseFormatter.validationError(res, {
          orderTotal: "Order total must be a positive number",
        });
      }

      if (!orderId) {
        return ResponseFormatter.validationError(res, {
          orderId: "Order ID is required",
        });
      }

      const result = await loyaltyService.addPoints(
        customerId,
        parseFloat(orderTotal),
        orderId,
        req.user.id, // staffId from authenticated user
        salesAgentId
      );

      if (result.success) {
        // Sync with Shopify metafields if points were added
        if (result.pointsAdded > 0) {
          const loyaltySummary = await loyaltyService.getCustomerSummary(
            customerId
          );
          if (loyaltySummary.success) {
            await shopifyMetafieldsService.setCustomerLoyaltyData(
              `gid://shopify/Customer/${customerId}`,
              {
                points: loyaltySummary.summary.loyaltyPoints,
                tier: loyaltySummary.summary.tier,
                totalPurchases: loyaltySummary.summary.totalPurchases,
                totalOrders: loyaltySummary.summary.totalOrders,
                lastPurchase: new Date().toISOString(),
              }
            );
          }
        }

        return ResponseFormatter.success(
          res,
          {
            pointsAdded: result.pointsAdded,
            newBalance: result.newBalance,
            previousTier: result.previousTier,
            newTier: result.newTier,
            tierChanged: result.tierChanged,
            transactionId: result.transactionId,
          },
          result.message
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Add loyalty points error:", error);
      return ResponseFormatter.serverError(res, "Failed to add loyalty points");
    }
  }
);

// Redeem loyalty points
router.post(
  "/customers/:customerId/points/redeem",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;
      const { pointsToRedeem, orderId, ticketId } = req.body;

      // Validate required fields
      if (
        !pointsToRedeem ||
        pointsToRedeem <= 0 ||
        !Number.isInteger(pointsToRedeem)
      ) {
        return ResponseFormatter.validationError(res, {
          pointsToRedeem: "Points to redeem must be a positive integer",
        });
      }

      const result = await loyaltyService.redeemPoints(
        customerId,
        pointsToRedeem,
        orderId,
        ticketId,
        req.user.id // staffId from authenticated user
      );

      if (result.success) {
        // Sync with Shopify metafields
        const loyaltySummary = await loyaltyService.getCustomerSummary(
          customerId
        );
        if (loyaltySummary.success) {
          await shopifyMetafieldsService.setCustomerLoyaltyData(
            `gid://shopify/Customer/${customerId}`,
            {
              points: loyaltySummary.summary.loyaltyPoints,
              tier: loyaltySummary.summary.tier,
            }
          );
        }

        return ResponseFormatter.success(
          res,
          {
            pointsRedeemed: result.pointsRedeemed,
            discountAmount: result.discountAmount,
            newBalance: result.newBalance,
            redemptionId: result.redemptionId,
            transactionId: result.transactionId,
          },
          result.message
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Redeem loyalty points error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to redeem loyalty points"
      );
    }
  }
);

// Calculate available loyalty discounts for an order
router.post(
  "/customers/:customerId/discounts/calculate",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;
      const { orderTotal } = req.body;

      // Validate required fields
      if (!orderTotal || orderTotal <= 0) {
        return ResponseFormatter.validationError(res, {
          orderTotal: "Order total must be a positive number",
        });
      }

      const result = await loyaltyService.calculateLoyaltyDiscount(
        customerId,
        parseFloat(orderTotal)
      );

      if (result.success) {
        return ResponseFormatter.success(
          res,
          result.discounts,
          "Loyalty discounts calculated successfully"
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Calculate loyalty discounts error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to calculate loyalty discounts"
      );
    }
  }
);

// Get loyalty analytics (requires permission)
router.get(
  "/analytics",
  authenticateToken,
  requirePermission("view_analytics"),
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;

      const dateRange = {};
      if (startDate) dateRange.startDate = startDate;
      if (endDate) dateRange.endDate = endDate;

      const result = await loyaltyService.getLoyaltyAnalytics(dateRange);

      if (result.success) {
        return ResponseFormatter.success(
          res,
          result.analytics,
          "Loyalty analytics retrieved successfully"
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Get loyalty analytics error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to get loyalty analytics"
      );
    }
  }
);

// Get loyalty configuration
router.get("/config", authenticateToken, async (req, res) => {
  try {
    const result = loyaltyService.getLoyaltyConfig();

    return ResponseFormatter.success(
      res,
      result.config,
      "Loyalty configuration retrieved successfully"
    );
  } catch (error) {
    console.error("Get loyalty config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get loyalty configuration"
    );
  }
});

// Update loyalty configuration (requires admin permission)
router.put(
  "/config",
  authenticateToken,
  requirePermission("manage_loyalty_config"),
  async (req, res) => {
    try {
      const newConfig = req.body;

      const result = await loyaltyService.updateLoyaltyConfig(newConfig);

      if (result.success) {
        return ResponseFormatter.success(res, result.config, result.message);
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Update loyalty config error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to update loyalty configuration"
      );
    }
  }
);

// Bulk sync customer loyalty data with Shopify
router.post(
  "/sync/shopify",
  authenticateToken,
  requirePermission("manage_loyalty_sync"),
  async (req, res) => {
    try {
      const { customerIds, forceSync = false } = req.body;

      if (!Array.isArray(customerIds) || customerIds.length === 0) {
        return ResponseFormatter.validationError(res, {
          customerIds: "Must provide an array of customer IDs",
        });
      }

      const results = {
        successful: [],
        failed: [],
        total: customerIds.length,
      };

      for (const customerId of customerIds) {
        try {
          // Get customer loyalty data from backend
          const loyaltyResult = await loyaltyService.getCustomerSummary(
            customerId
          );

          if (loyaltyResult.success) {
            // Sync to Shopify metafields
            const syncResult =
              await shopifyMetafieldsService.setCustomerLoyaltyData(
                `gid://shopify/Customer/${customerId}`,
                {
                  points: loyaltyResult.summary.loyaltyPoints,
                  tier: loyaltyResult.summary.tier,
                  totalPurchases: loyaltyResult.summary.totalPurchases,
                  totalOrders: loyaltyResult.summary.totalOrders,
                  lastPurchase: loyaltyResult.summary.lastPurchase,
                  tierUpdated: loyaltyResult.summary.progressToNextTier
                    ? new Date().toISOString()
                    : null,
                }
              );

            if (syncResult.success) {
              results.successful.push({
                customerId: customerId,
                metafieldsUpdated: syncResult.metafieldsUpdated,
              });
            } else {
              results.failed.push({
                customerId: customerId,
                error: syncResult.error,
              });
            }
          } else {
            results.failed.push({
              customerId: customerId,
              error: loyaltyResult.error,
            });
          }
        } catch (error) {
          results.failed.push({
            customerId: customerId,
            error: error.message,
          });
        }
      }

      return ResponseFormatter.success(
        res,
        results,
        `Sync completed. ${results.successful.length} successful, ${results.failed.length} failed`
      );
    } catch (error) {
      console.error("Bulk sync loyalty data error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to sync loyalty data with Shopify"
      );
    }
  }
);

// Bulk points adjustment (requires admin permission)
router.post(
  "/customers/points/bulk-adjust",
  authenticateToken,
  requirePermission("manage_loyalty_points"),
  async (req, res) => {
    try {
      const { adjustments, reason = "Bulk adjustment" } = req.body;

      if (!Array.isArray(adjustments) || adjustments.length === 0) {
        return ResponseFormatter.validationError(res, {
          adjustments: "Must provide an array of point adjustments",
        });
      }

      // Validate adjustment format
      for (const adjustment of adjustments) {
        if (
          !adjustment.customerId ||
          typeof adjustment.pointsChange !== "number"
        ) {
          return ResponseFormatter.validationError(res, {
            adjustments:
              "Each adjustment must have customerId and pointsChange (number)",
          });
        }
      }

      const results = {
        successful: [],
        failed: [],
        total: adjustments.length,
      };

      for (const adjustment of adjustments) {
        try {
          // Get current customer loyalty data
          const loyaltyResult = await loyaltyService.getCustomerLoyalty(
            adjustment.customerId
          );

          if (loyaltyResult.success) {
            const currentPoints = loyaltyResult.loyalty.loyalty_points;
            const newPoints = Math.max(
              0,
              currentPoints + adjustment.pointsChange
            );

            // Create adjustment transaction
            const transactionResult = await loyaltyService.pool.execute(
              `INSERT INTO loyalty_transactions (
              id, customer_loyalty_id, shopify_customer_id, transaction_type,
              points_amount, description, staff_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
              [
                require("uuid").v4(),
                loyaltyResult.loyalty.id,
                adjustment.customerId,
                "adjusted",
                adjustment.pointsChange,
                `${reason} - ${adjustment.reason || "No specific reason"}`,
                req.user.id,
              ]
            );

            // Update customer points
            await loyaltyService.pool.execute(
              `UPDATE customer_loyalty SET loyalty_points = ?, updated_at = NOW() WHERE id = ?`,
              [newPoints, loyaltyResult.loyalty.id]
            );

            results.successful.push({
              customerId: adjustment.customerId,
              previousPoints: currentPoints,
              pointsChange: adjustment.pointsChange,
              newPoints: newPoints,
            });
          } else {
            results.failed.push({
              customerId: adjustment.customerId,
              error: loyaltyResult.error,
            });
          }
        } catch (error) {
          results.failed.push({
            customerId: adjustment.customerId,
            error: error.message,
          });
        }
      }

      return ResponseFormatter.success(
        res,
        results,
        `Bulk adjustment completed. ${results.successful.length} successful, ${results.failed.length} failed`
      );
    } catch (error) {
      console.error("Bulk points adjustment error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to perform bulk points adjustment"
      );
    }
  }
);

// Get customer loyalty leaderboard
router.get("/leaderboard", authenticateToken, async (req, res) => {
  try {
    const { limit = 50, tier = null, orderBy = "points" } = req.query;

    let query = `
      SELECT
        cl.shopify_customer_id,
        cl.loyalty_points,
        cl.loyalty_tier,
        cl.total_purchases,
        cl.total_orders,
        cl.last_purchase_at,
        cl.created_at
      FROM customer_loyalty cl
    `;

    const params = [];

    if (tier && ["bronze", "silver", "gold", "platinum"].includes(tier)) {
      query += ` WHERE cl.loyalty_tier = ?`;
      params.push(tier);
    }

    // Order by points or total purchases
    if (orderBy === "purchases") {
      query += ` ORDER BY cl.total_purchases DESC`;
    } else {
      query += ` ORDER BY cl.loyalty_points DESC`;
    }

    query += ` LIMIT ?`;
    params.push(parseInt(limit));

    const [rows] = await loyaltyService.pool.execute(query, params);

    const leaderboard = rows.map((row, index) => ({
      rank: index + 1,
      customerId: row.shopify_customer_id,
      loyaltyPoints: row.loyalty_points,
      tier: row.loyalty_tier,
      totalPurchases: parseFloat(row.total_purchases),
      totalOrders: row.total_orders,
      lastPurchase: row.last_purchase_at,
      memberSince: row.created_at,
    }));

    return ResponseFormatter.success(
      res,
      {
        leaderboard: leaderboard,
        criteria: {
          orderBy: orderBy,
          tier: tier,
          limit: parseInt(limit),
        },
      },
      "Loyalty leaderboard retrieved successfully"
    );
  } catch (error) {
    console.error("Get loyalty leaderboard error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get loyalty leaderboard"
    );
  }
});

// Health check for loyalty service
router.get("/health", async (req, res) => {
  try {
    // Test database connectivity
    const [rows] = await loyaltyService.pool.execute("SELECT 1 as test");

    return ResponseFormatter.success(
      res,
      {
        status: "healthy",
        database: "connected",
        loyaltyService: "operational",
        timestamp: new Date().toISOString(),
      },
      "Loyalty service health check completed"
    );
  } catch (error) {
    console.error("Loyalty health check error:", error);
    return ResponseFormatter.error(
      res,
      "Loyalty service health check failed",
      503
    );
  }
});

module.exports = router;
