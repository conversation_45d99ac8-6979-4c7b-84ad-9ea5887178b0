# 🚀 **DUKALINK POS BACKEND SETUP GUIDE**

Complete setup guide for the Dukalink POS backend with database migrations.

---

## **📋 QUICK SETUP (Recommended)**

### **One-Command Setup**
```bash
cd backend
npm install
npm run setup
```

This single command will:
- ✅ Check prerequisites (.env file, MySQL connection)
- ✅ Create database and user
- ✅ Run all schema migrations
- ✅ Seed initial data (staff, sales agents, terminals)
- ✅ Verify everything works

---

## **🔧 MANUAL SETUP (Step by Step)**

### **1. Prerequisites**
```bash
# Install dependencies
cd backend
npm install

# Copy environment template
cp .env.example .env

# Edit .env with your MySQL credentials
# Required variables:
# DB_HOST=localhost
# DB_USER=dukalink
# DB_PASSWORD=dukalink_secure_password_2024
# DB_NAME=dukalink_pos
# MYSQL_ROOT_PASSWORD=your_mysql_root_password
```

### **2. Database Setup**
```bash
# Option A: Complete setup (recommended)
npm run setup

# Option B: Manual step-by-step
npm run db:setup              # Create database and user
npm run db:create-schema      # Create tables
npm run db:migrate-staff      # Migrate staff data
npm run db:migrate-agents     # Migrate sales agents
npm run db:migrate-terminals  # Migrate terminal data
```

### **3. Start Server**
```bash
npm run dev
```

---

## **📋 AVAILABLE COMMANDS**

### **Setup Commands**
| Command | Description |
|---------|-------------|
| `npm run setup` | **Complete one-command setup** |
| `npm run setup:complete` | Same as above (alias) |

### **Database Commands**
| Command | Description |
|---------|-------------|
| `npm run db:setup` | Create database and user (requires MySQL root) |
| `npm run db:create-schema` | Create all database tables |
| `npm run db:migrate-staff` | Migrate staff authentication to MySQL |
| `npm run db:migrate-agents` | Migrate sales agents to MySQL |
| `npm run db:migrate-terminals` | Migrate terminal management to MySQL |

### **Development Commands**
| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with nodemon |
| `npm start` | Start production server |
| `npm test` | Run system tests |

### **Legacy Migration Commands**
| Command | Description |
|---------|-------------|
| `npm run migrate` | Run master migration script |
| `npm run migrate:sales-agents` | Migrate sales agents only |
| `npm run rollback` | Rollback migrations |

---

## **🔍 TROUBLESHOOTING**

### **Common Issues**

#### **1. MySQL Connection Failed**
```bash
# Check if MySQL is running
sudo systemctl status mysql    # Linux
brew services list | grep mysql # macOS

# Set root password in environment
export MYSQL_ROOT_PASSWORD=your_password
npm run setup
```

#### **2. Permission Denied**
```bash
# Make sure MySQL user has proper permissions
mysql -u root -p
GRANT ALL PRIVILEGES ON dukalink_pos.* TO 'dukalink'@'localhost';
FLUSH PRIVILEGES;
```

#### **3. Database Already Exists**
```bash
# Drop and recreate database
mysql -u root -p
DROP DATABASE IF EXISTS dukalink_pos;
npm run setup
```

#### **4. .env File Missing**
```bash
# Copy from template
cp .env.example .env

# Edit with your settings
nano .env
```

### **Environment Variables**
```bash
# Required in .env file:
DB_HOST=localhost
DB_PORT=3306
DB_USER=dukalink
DB_PASSWORD=dukalink_secure_password_2024
DB_NAME=dukalink_pos
JWT_SECRET=your_super_secure_jwt_secret_minimum_32_characters
MYSQL_ROOT_PASSWORD=your_mysql_root_password

# Optional:
DB_NAME_TEST=dukalink_pos_test
NODE_ENV=development
PORT=3001
```

---

## **✅ VERIFICATION**

### **Check Setup Success**
```bash
# 1. Start server
npm run dev

# Expected output:
# ✅ Connected to MySQL database
# ✅ Staff authentication initialized
# ✅ Sales agents initialized
# ✅ Terminal management initialized
# 🚀 Server running on port 3001

# 2. Test API endpoints
curl http://localhost:3001/api/health
curl -X POST http://localhost:3001/api/pos/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin1", "password": "admin123"}'
```

### **Default Users Created**
| Username | Password | Role | Permissions |
|----------|----------|------|-------------|
| `cashier1` | `password123` | cashier | Basic POS operations |
| `manager1` | `manager123` | manager | Staff management, reports |
| `admin1` | `admin123` | super_admin | Full system access |

---

## **🎯 NEXT STEPS**

After successful setup:

1. **Start Development**
   ```bash
   npm run dev
   ```

2. **Test API Endpoints**
   - Authentication: `POST /api/pos/login`
   - Staff Management: `GET /api/staff`
   - Sales Agents: `GET /api/sales-agents`

3. **Frontend Integration**
   - Update React Native app to use MySQL backend
   - Test RBAC functionality
   - Verify all features work

4. **Production Deployment**
   - Set production environment variables
   - Use `npm start` for production
   - Setup proper MySQL backup strategy

---

## **📞 SUPPORT**

If you encounter issues:

1. Check the troubleshooting section above
2. Review server logs for error messages
3. Verify MySQL is running and accessible
4. Ensure all environment variables are set correctly
5. Test with a fresh database if needed

**Happy coding! 🚀**
