/**
 * Staff Discount Manager Component
 * Provides interface for staff to manage and apply discount rules
 * with proper permission validation and usage tracking
 */

import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import {
  ThemedView,
  ThemedText,
} from "@/src/components/themed/ThemedComponents";
import { ModernButton } from "@/src/components/ui/ModernButton";
import { IconSymbol } from "@/src/components/ui/IconSymbol";
import { formatCurrency } from "@/src/utils/currencyUtils";

export interface StaffDiscountRule {
  id: string;
  name: string;
  description: string;
  type: "percentage" | "fixed_amount";
  value: number;
  maxAmount?: number;
  minOrderAmount?: number;
  usageLimit?: number;
  usageCount: number;
  isActive: boolean;
  validFrom?: string;
  validUntil?: string;
  applicableProducts?: string[];
  applicableCategories?: string[];
  staffPermissions: {
    canUse: boolean;
    canModify: boolean;
    reason?: string;
  };
}

export interface DiscountUsageStats {
  totalUsage: number;
  totalSavings: number;
  averageDiscount: number;
  topRules: Array<{
    ruleId: string;
    ruleName: string;
    usageCount: number;
    totalSavings: number;
  }>;
}

interface StaffDiscountManagerProps {
  staffId: string;
  onApplyRule: (rule: StaffDiscountRule) => void;
  onCreateRule?: (rule: Partial<StaffDiscountRule>) => Promise<void>;
  onUpdateRule?: (
    ruleId: string,
    updates: Partial<StaffDiscountRule>
  ) => Promise<void>;
  onDeleteRule?: (ruleId: string) => Promise<void>;
  onLoadRules?: () => Promise<StaffDiscountRule[]>;
  onLoadStats?: () => Promise<DiscountUsageStats>;
  canManageRules?: boolean;
  showStats?: boolean;
}

export const StaffDiscountManager: React.FC<StaffDiscountManagerProps> = ({
  staffId,
  onApplyRule,
  onCreateRule,
  onUpdateRule,
  onDeleteRule,
  onLoadRules,
  onLoadStats,
  canManageRules = false,
  showStats = true,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  // Create theme-aware styles
  const styles = createStyles(theme);

  // State management
  const [rules, setRules] = useState<StaffDiscountRule[]>([]);
  const [stats, setStats] = useState<DiscountUsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"rules" | "stats">("rules");

  // Load data on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      const promises = [];
      if (onLoadRules) promises.push(onLoadRules());
      if (showStats && onLoadStats) promises.push(onLoadStats());

      const results = await Promise.allSettled(promises);

      if (results[0]?.status === "fulfilled") {
        setRules(results[0].value);
      }

      if (results[1]?.status === "fulfilled") {
        setStats(results[1].value);
      }
    } catch (error) {
      console.error("Load discount data error:", error);
      Alert.alert("Error", "Failed to load discount data");
    } finally {
      setLoading(false);
    }
  };

  // Handle rule application
  const handleApplyRule = (rule: StaffDiscountRule) => {
    if (!rule.staffPermissions.canUse) {
      Alert.alert(
        "Permission Denied",
        rule.staffPermissions.reason || "You cannot use this discount rule"
      );
      return;
    }

    if (!rule.isActive) {
      Alert.alert("Rule Inactive", "This discount rule is currently inactive");
      return;
    }

    if (rule.usageLimit && rule.usageCount >= rule.usageLimit) {
      Alert.alert(
        "Usage Limit Reached",
        "This discount rule has reached its usage limit"
      );
      return;
    }

    onApplyRule(rule);
  };

  // Handle rule toggle
  const handleToggleRule = async (rule: StaffDiscountRule) => {
    if (!onUpdateRule || !rule.staffPermissions.canModify) {
      Alert.alert("Permission Denied", "You cannot modify this discount rule");
      return;
    }

    try {
      await onUpdateRule(rule.id, { isActive: !rule.isActive });
      await loadData(); // Refresh data
    } catch (error) {
      Alert.alert("Error", "Failed to update discount rule");
    }
  };

  // Handle rule deletion
  const handleDeleteRule = (rule: StaffDiscountRule) => {
    if (!onDeleteRule || !rule.staffPermissions.canModify) {
      Alert.alert("Permission Denied", "You cannot delete this discount rule");
      return;
    }

    Alert.alert(
      "Delete Discount Rule",
      `Are you sure you want to delete "${rule.name}"?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await onDeleteRule(rule.id);
              await loadData(); // Refresh data
            } catch (error) {
              Alert.alert("Error", "Failed to delete discount rule");
            }
          },
        },
      ]
    );
  };

  // Render discount rule card
  const renderRuleCard = (rule: StaffDiscountRule) => {
    const canUse = rule.staffPermissions.canUse && rule.isActive;
    const usagePercentage = rule.usageLimit
      ? (rule.usageCount / rule.usageLimit) * 100
      : 0;

    return (
      <ThemedView
        key={rule.id}
        variant="card"
        style={[styles.ruleCard, utils.p("md")]}
      >
        <View style={styles.ruleHeader}>
          <View style={styles.ruleInfo}>
            <ThemedText variant="body" style={styles.ruleName}>
              {rule.name}
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              {rule.description}
            </ThemedText>
          </View>

          <View style={styles.ruleValue}>
            <ThemedText variant="h3" color="primary">
              {rule.type === "percentage"
                ? `${rule.value}%`
                : formatCurrency(rule.value)}
            </ThemedText>
            {rule.maxAmount && (
              <ThemedText variant="caption" color="secondary">
                Max: {formatCurrency(rule.maxAmount)}
              </ThemedText>
            )}
          </View>
        </View>

        {/* Rule Status */}
        <View style={styles.ruleStatus}>
          <View style={styles.statusIndicators}>
            <View
              style={[
                styles.statusBadge,
                {
                  backgroundColor: rule.isActive
                    ? theme.colors.success + "20"
                    : theme.colors.error + "20",
                },
              ]}
            >
              <ThemedText
                variant="caption"
                style={{
                  color: rule.isActive
                    ? theme.colors.success
                    : theme.colors.error,
                }}
              >
                {rule.isActive ? "Active" : "Inactive"}
              </ThemedText>
            </View>

            {!canUse && (
              <View
                style={[
                  styles.statusBadge,
                  { backgroundColor: theme.colors.warning + "20" },
                ]}
              >
                <ThemedText
                  variant="caption"
                  style={{ color: theme.colors.warning }}
                >
                  No Permission
                </ThemedText>
              </View>
            )}
          </View>
        </View>

        {/* Usage Information */}
        {rule.usageLimit && (
          <View style={styles.usageInfo}>
            <View style={styles.usageHeader}>
              <ThemedText variant="small" color="secondary">
                Usage: {rule.usageCount} / {rule.usageLimit}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                {usagePercentage.toFixed(0)}%
              </ThemedText>
            </View>
            <View
              style={[
                styles.usageBar,
                { backgroundColor: theme.colors.border },
              ]}
            >
              <View
                style={[
                  styles.usageFill,
                  {
                    backgroundColor:
                      usagePercentage > 80
                        ? theme.colors.warning
                        : theme.colors.primary,
                    width: `${Math.min(usagePercentage, 100)}%`,
                  },
                ]}
              />
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.ruleActions}>
          <ModernButton
            title="Apply Rule"
            onPress={() => handleApplyRule(rule)}
            disabled={!canUse}
            style={styles.actionButton}
            size="sm"
          />

          {rule.staffPermissions.canModify && (
            <>
              <ModernButton
                title={rule.isActive ? "Deactivate" : "Activate"}
                variant="outline"
                onPress={() => handleToggleRule(rule)}
                style={styles.actionButton}
                size="sm"
              />

              <TouchableOpacity
                onPress={() => handleDeleteRule(rule)}
                style={styles.deleteButton}
              >
                <IconSymbol name="trash" size={16} color={theme.colors.error} />
              </TouchableOpacity>
            </>
          )}
        </View>
      </ThemedView>
    );
  };

  // Render statistics tab
  const renderStatsTab = () => {
    if (!stats) {
      return (
        <View style={styles.emptyContainer}>
          <IconSymbol
            name="chart.bar"
            size={48}
            color={theme.colors.textSecondary}
          />
          <ThemedText variant="h3" color="secondary" style={utils.mt("md")}>
            No Statistics Available
          </ThemedText>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.statsContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Overview Stats */}
        <ThemedView variant="card" style={[styles.statsCard, utils.p("lg")]}>
          <ThemedText variant="h3" style={utils.mb("md")}>
            Usage Overview
          </ThemedText>

          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <ThemedText variant="h2" color="primary">
                {stats.totalUsage}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Total Uses
              </ThemedText>
            </View>

            <View style={styles.statItem}>
              <ThemedText variant="h2" color="success">
                {formatCurrency(stats.totalSavings)}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Total Savings
              </ThemedText>
            </View>

            <View style={styles.statItem}>
              <ThemedText variant="h2" color="warning">
                {formatCurrency(stats.averageDiscount)}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Avg Discount
              </ThemedText>
            </View>
          </View>
        </ThemedView>

        {/* Top Rules */}
        {stats.topRules.length > 0 && (
          <ThemedView variant="card" style={[styles.statsCard, utils.p("lg")]}>
            <ThemedText variant="h3" style={utils.mb("md")}>
              Most Used Rules
            </ThemedText>

            {stats.topRules.map((topRule, index) => (
              <View key={topRule.ruleId} style={styles.topRuleItem}>
                <View style={styles.topRuleRank}>
                  <ThemedText variant="h3" color="primary">
                    #{index + 1}
                  </ThemedText>
                </View>
                <View style={styles.topRuleInfo}>
                  <ThemedText variant="body" style={styles.topRuleName}>
                    {topRule.ruleName}
                  </ThemedText>
                  <ThemedText variant="small" color="secondary">
                    {topRule.usageCount} uses •{" "}
                    {formatCurrency(topRule.totalSavings)} saved
                  </ThemedText>
                </View>
              </View>
            ))}
          </ThemedView>
        )}
      </ScrollView>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <ThemedText variant="body" color="secondary" style={utils.mt("md")}>
          Loading discount rules...
        </ThemedText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Tab Bar */}
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "rules" && styles.activeTabButton,
            { borderBottomColor: theme.colors.primary },
          ]}
          onPress={() => setActiveTab("rules")}
        >
          <IconSymbol
            name="tag.fill"
            size={16}
            color={
              activeTab === "rules"
                ? theme.colors.primary
                : theme.colors.textSecondary
            }
          />
          <ThemedText
            variant="body"
            style={{
              color:
                activeTab === "rules"
                  ? theme.colors.primary
                  : theme.colors.textSecondary,
            }}
          >
            Rules ({rules.length})
          </ThemedText>
        </TouchableOpacity>

        {showStats && (
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === "stats" && styles.activeTabButton,
              { borderBottomColor: theme.colors.primary },
            ]}
            onPress={() => setActiveTab("stats")}
          >
            <IconSymbol
              name="chart.bar"
              size={16}
              color={
                activeTab === "stats"
                  ? theme.colors.primary
                  : theme.colors.textSecondary
              }
            />
            <ThemedText
              variant="body"
              style={{
                color:
                  activeTab === "stats"
                    ? theme.colors.primary
                    : theme.colors.textSecondary,
              }}
            >
              Statistics
            </ThemedText>
          </TouchableOpacity>
        )}
      </View>

      {/* Tab Content */}
      {activeTab === "rules" ? (
        <ScrollView
          style={styles.rulesContainer}
          showsVerticalScrollIndicator={false}
        >
          {rules.length === 0 ? (
            <View style={styles.emptyContainer}>
              <IconSymbol
                name="tag"
                size={48}
                color={theme.colors.textSecondary}
              />
              <ThemedText variant="h3" color="secondary" style={utils.mt("md")}>
                No Discount Rules
              </ThemedText>
              <ThemedText
                variant="body"
                color="muted"
                style={[utils.mt("sm"), styles.emptyText]}
              >
                No discount rules are available for your role.
              </ThemedText>
            </View>
          ) : (
            <View style={styles.rulesList}>{rules.map(renderRuleCard)}</View>
          )}
        </ScrollView>
      ) : (
        renderStatsTab()
      )}
    </View>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    tabBar: {
      flexDirection: "row",
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.borderLight,
    },
    tabButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      borderBottomWidth: 2,
      borderBottomColor: "transparent",
      gap: 8,
    },
    activeTabButton: {
      borderBottomWidth: 2,
    },
    rulesContainer: {
      flex: 1,
      padding: 16,
    },
    rulesList: {
      gap: 12,
    },
    ruleCard: {
      marginBottom: 8,
    },
    ruleHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: 12,
    },
    ruleInfo: {
      flex: 1,
      marginRight: 12,
    },
    ruleName: {
      fontWeight: "600",
      marginBottom: 4,
    },
    ruleValue: {
      alignItems: "flex-end",
    },
    ruleStatus: {
      marginBottom: 12,
    },
    statusIndicators: {
      flexDirection: "row",
      gap: 8,
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    usageInfo: {
      marginBottom: 12,
    },
    usageHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 6,
    },
    usageBar: {
      height: 4,
      borderRadius: 2,
      overflow: "hidden",
    },
    usageFill: {
      height: "100%",
      borderRadius: 2,
    },
    ruleActions: {
      flexDirection: "row",
      gap: 8,
      alignItems: "center",
    },
    actionButton: {
      flex: 1,
    },
    deleteButton: {
      padding: 8,
    },
    emptyContainer: {
      alignItems: "center",
      paddingVertical: 48,
    },
    emptyText: {
      textAlign: "center",
      maxWidth: 280,
    },
    statsContainer: {
      flex: 1,
      padding: 16,
    },
    statsCard: {
      marginBottom: 16,
    },
    statsGrid: {
      flexDirection: "row",
      justifyContent: "space-around",
    },
    statItem: {
      alignItems: "center",
    },
    topRuleItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.borderLight,
    },
    topRuleRank: {
      width: 40,
      alignItems: "center",
    },
    topRuleInfo: {
      flex: 1,
      marginLeft: 12,
    },
    topRuleName: {
      fontWeight: "600",
    },
  });
