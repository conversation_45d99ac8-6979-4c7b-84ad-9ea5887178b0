import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, Platform } from "react-native";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { CrossPlatformPrintService } from "@/src/services/CrossPlatformPrintService";
import { WebPrintService } from "@/src/services/WebPrintService";

interface WebPrinterSetupProps {
  onSetupComplete?: () => void;
  onClose?: () => void;
}

export function WebPrinterSetup({
  onSetupComplete,
  onClose,
}: WebPrinterSetupProps) {
  const [printerType, setPrinterType] = useState<
    "standard" | "thermal" | "network"
  >("standard");
  const [thermalEndpoint, setThermalEndpoint] = useState("");
  const [isTestingPrint, setIsTestingPrint] = useState(false);
  const [printStatus, setPrintStatus] = useState<{
    available: boolean;
    type?: string;
    message?: string;
  }>({ available: false });

  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const surfaceColor = useThemeColor({}, "surface");
  const borderColor = useThemeColor({}, "border");

  useEffect(() => {
    checkPrintAvailability();
  }, []);

  const checkPrintAvailability = async () => {
    try {
      const status = await CrossPlatformPrintService.getPrinterStatus();
      setPrintStatus({
        available: status.available,
        type: status.type,
        message: status.available
          ? `${status.type} printing available`
          : "Printing not available in this browser",
      });
    } catch (error) {
      setPrintStatus({
        available: false,
        message: "Error checking print availability",
      });
    }
  };

  const handleConfigurePrinter = async () => {
    try {
      // Configure the web print service
      await CrossPlatformPrintService.configure({
        webPrinter: {
          type: printerType,
          endpoint: printerType === "thermal" ? thermalEndpoint : undefined,
          width: 48, // 80mm thermal width (48 characters)
        },
      });

      // Initialize with new configuration
      await CrossPlatformPrintService.init();

      // Recheck availability
      await checkPrintAvailability();

      onSetupComplete?.();
    } catch (error) {
      console.error("Error configuring web printer:", error);
    }
  };

  const handleTestPrint = async () => {
    setIsTestingPrint(true);
    try {
      const result = await CrossPlatformPrintService.testPrint();

      if (result.success) {
        setPrintStatus({
          available: true,
          type: result.method,
          message: `✅ Test print successful using ${result.method} method`,
        });
      } else {
        setPrintStatus({
          available: false,
          message: `❌ Test print failed: ${result.error}`,
        });
      }
    } catch (error) {
      setPrintStatus({
        available: false,
        message: `❌ Test print error: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      });
    } finally {
      setIsTestingPrint(false);
    }
  };

  // Only show on web platform
  if (Platform.OS !== "web") {
    return (
      <ModernCard style={[styles.container, { backgroundColor: surfaceColor }]}>
        <Text style={[styles.title, { color: textColor }]}>
          Web Printer Setup
        </Text>
        <Text style={[styles.description, { color: textSecondary }]}>
          Web printer setup is only available on web browsers. Use thermal
          printer setup for mobile devices.
        </Text>
      </ModernCard>
    );
  }

  return (
    <ModernCard style={[styles.container, { backgroundColor: surfaceColor }]}>
      <Text style={[styles.title, { color: textColor }]}>
        Web Printer Setup
      </Text>

      <Text style={[styles.description, { color: textSecondary }]}>
        Configure receipt printing for your web browser. Choose the printer type
        that matches your setup.
      </Text>

      {/* Printer Status */}
      <View style={[styles.statusCard, { borderColor }]}>
        <Text style={[styles.statusTitle, { color: textColor }]}>
          Current Status
        </Text>
        <Text
          style={[
            styles.statusMessage,
            { color: printStatus.available ? primaryColor : textSecondary },
          ]}
        >
          {printStatus.message || "Checking..."}
        </Text>
      </View>

      {/* Printer Type Selection */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          Printer Type
        </Text>

        <View style={styles.optionGroup}>
          <ModernButton
            title="🖨️ Standard Printer"
            variant={printerType === "standard" ? "primary" : "outline"}
            onPress={() => setPrinterType("standard")}
            style={styles.optionButton}
          />

          <ModernButton
            title="🧾 Thermal Printer (USB)"
            variant={printerType === "thermal" ? "primary" : "outline"}
            onPress={() => setPrinterType("thermal")}
            style={styles.optionButton}
          />

          <ModernButton
            title="🌐 Network Printer"
            variant={printerType === "network" ? "primary" : "outline"}
            onPress={() => setPrinterType("network")}
            style={styles.optionButton}
          />
        </View>
      </View>

      {/* Thermal Printer Configuration */}
      {printerType === "thermal" && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Thermal Printer Configuration
          </Text>
          <Text style={[styles.description, { color: textSecondary }]}>
            For thermal printers connected via USB, you&apos;ll need a print
            server or browser extension. Contact your system administrator for
            the print endpoint URL.
          </Text>
          {/* Note: TextInput would need to be imported and implemented for endpoint configuration */}
        </View>
      )}

      {/* Network Printer Configuration */}
      {printerType === "network" && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Network Printer Configuration
          </Text>
          <Text style={[styles.description, { color: textSecondary }]}>
            Network printers will use the browser&apos;s standard print dialog.
            Make sure your receipt printer is set as the default printer.
          </Text>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <ModernButton
          title="Test Print"
          variant="outline"
          onPress={handleTestPrint}
          loading={isTestingPrint}
          style={styles.actionButton}
        />

        <ModernButton
          title="Configure"
          variant="primary"
          onPress={handleConfigurePrinter}
          style={styles.actionButton}
        />
      </View>

      {onClose && (
        <ModernButton
          title="Close"
          variant="ghost"
          onPress={onClose}
          style={styles.closeButton}
        />
      )}
    </ModernCard>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    margin: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  statusCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
  },
  statusMessage: {
    fontSize: 13,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  optionGroup: {
    gap: 8,
  },
  optionButton: {
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
  },
  closeButton: {
    marginTop: 12,
  },
});
