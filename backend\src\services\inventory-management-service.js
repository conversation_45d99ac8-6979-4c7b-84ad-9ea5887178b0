/**
 * Inventory Management Service
 *
 * Handles real-time inventory checking, stock validation, and inventory operations
 * for the Dukalink POS system using Shopify's inventory management APIs.
 */

const shopifyService = require("./shopify-service");

class InventoryManagementService {
  constructor() {
    // Default inventory settings
    this.defaultSettings = {
      lowStockThreshold: 10,
      reservationTimeoutMinutes: 15,
      enableRealTimeChecking: true,
      enableLowStockAlerts: true,
      defaultLocationId: null, // Will be set from Shopify
    };
  }

  // Get inventory levels for multiple variants at a location
  async getInventoryLevels(variantIds, locationId = null) {
    try {
      // Use direct variant IDs instead of search query
      const variantGids = variantIds.map(
        (id) => `gid://shopify/ProductVariant/${id}`
      );

      const query = `
        query getInventoryLevels($variantIds: [ID!]!) {
          nodes(ids: $variantIds) {
            ... on ProductVariant {
              id
              sku
              title
              inventoryItem {
                id
                tracked
                inventoryLevels(first: 10) {
                  edges {
                    node {
                      id
                      quantities(names: ["available", "on_hand", "committed", "incoming", "reserved"]) {
                        name
                        quantity
                      }
                      location {
                        id
                        name
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const variables = {
        variantIds: variantGids,
      };

      const response = await shopifyService.graphqlRequest(query, variables);

      if (response.errors) {
        return {
          success: false,
          error: response.errors[0].message,
        };
      }

      const inventoryData = {};

      // Process the nodes response (each node is a ProductVariant)
      const variants = response.data.nodes || [];

      variants.forEach((variant) => {
        if (!variant || !variant.id) {
          return;
        }

        const variantId = variant.id.split("/").pop();
        const inventoryLevels =
          variant.inventoryItem?.inventoryLevels?.edges || [];

        inventoryData[variantId] = {
          sku: variant.sku,
          title: variant.title,
          tracked: variant.inventoryItem?.tracked || false,
          levels: inventoryLevels.map(({ node: level }) => {
            // Parse quantities array into object
            const quantities = {};
            level.quantities.forEach((q) => {
              quantities[q.name] = q.quantity;
            });

            return {
              locationId: level.location.id.split("/").pop(),
              locationName: level.location.name,
              available: quantities.available || 0,
              onHand: quantities.on_hand || 0,
              committed: quantities.committed || 0,
              incoming: quantities.incoming || 0,
              reserved: quantities.reserved || 0,
            };
          }),
        };
      });

      return {
        success: true,
        inventory: inventoryData,
      };
    } catch (error) {
      console.error("Get inventory levels error:", error);
      return {
        success: false,
        error: "Failed to fetch inventory levels",
      };
    }
  }

  // Check if sufficient inventory is available for an order
  // Helper function to normalize variant IDs (extract numeric ID from GID)
  normalizeVariantId(variantId) {
    if (
      typeof variantId === "string" &&
      variantId.includes("gid://shopify/ProductVariant/")
    ) {
      return variantId.split("/").pop();
    }
    return variantId;
  }

  async validateOrderInventory(lineItems, locationId = null) {
    try {
      // Normalize variant IDs to handle both GID and numeric formats
      const normalizedLineItems = lineItems.map((item) => ({
        ...item,
        variantId: this.normalizeVariantId(item.variantId),
      }));

      const variantIds = normalizedLineItems.map((item) => item.variantId);

      const inventoryResult = await this.getInventoryLevels(
        variantIds,
        locationId
      );

      if (!inventoryResult.success) {
        return inventoryResult;
      }

      const validationResults = [];
      const insufficientStock = [];

      for (const item of normalizedLineItems) {
        const variantInventory = inventoryResult.inventory[item.variantId];

        if (!variantInventory) {
          insufficientStock.push({
            variantId: item.variantId,
            title: item.title || "Unknown Product",
            requested: item.quantity,
            available: 0,
            reason: "Product not found",
          });
          continue;
        }

        // If inventory is not tracked, allow the sale
        if (!variantInventory.tracked) {
          validationResults.push({
            variantId: item.variantId,
            title: variantInventory.title,
            requested: item.quantity,
            available: "unlimited",
            status: "available",
            tracked: false,
          });
          continue;
        }

        // Check availability at the specified location or first available location
        const locationLevel =
          variantInventory.levels.find(
            (level) => !locationId || level.locationId === locationId
          ) || variantInventory.levels[0];

        if (!locationLevel) {
          insufficientStock.push({
            variantId: item.variantId,
            title: variantInventory.title,
            requested: item.quantity,
            available: 0,
            reason: "No inventory at location",
          });
          continue;
        }

        if (locationLevel.available < item.quantity) {
          insufficientStock.push({
            variantId: item.variantId,
            title: variantInventory.title,
            requested: item.quantity,
            available: locationLevel.available,
            reason: "Insufficient stock",
          });
        } else {
          validationResults.push({
            variantId: item.variantId,
            title: variantInventory.title,
            requested: item.quantity,
            available: locationLevel.available,
            status: "available",
            tracked: true,
            locationId: locationLevel.locationId,
            locationName: locationLevel.locationName,
          });
        }
      }

      return {
        success: insufficientStock.length === 0,
        valid: insufficientStock.length === 0,
        validationResults: validationResults,
        insufficientStock: insufficientStock,
        message:
          insufficientStock.length === 0
            ? "All items have sufficient inventory"
            : `${insufficientStock.length} items have insufficient stock`,
      };
    } catch (error) {
      console.error("Validate order inventory error:", error);
      return {
        success: false,
        error: "Failed to validate inventory",
      };
    }
  }

  // Check for low stock items at a location
  async checkLowStockItems(locationId = null, threshold = null) {
    try {
      const stockThreshold =
        threshold || this.defaultSettings.lowStockThreshold;

      // Get all products with inventory tracking
      const query = `
        query getLowStockItems($locationId: ID, $first: Int!) {
          location(id: $locationId) {
            id
            name
            inventoryLevels(first: $first) {
              edges {
                node {
                  id
                  available
                  onHand
                  item {
                    id
                    sku
                    variant {
                      id
                      title
                      product {
                        id
                        title
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const variables = {
        locationId: locationId ? `gid://shopify/Location/${locationId}` : null,
        first: 250, // Adjust based on your inventory size
      };

      const response = await shopifyService.graphqlRequest(query, variables);

      if (response.errors) {
        return {
          success: false,
          error: response.errors[0].message,
        };
      }

      const lowStockItems = [];
      const inventoryLevels =
        response.data.location?.inventoryLevels?.edges || [];

      inventoryLevels.forEach(({ node: level }) => {
        if (level.available <= stockThreshold && level.available >= 0) {
          lowStockItems.push({
            variantId: level.item.variant.id.split("/").pop(),
            inventoryItemId: level.item.id.split("/").pop(),
            sku: level.item.sku,
            variantTitle: level.item.variant.title,
            productTitle: level.item.variant.product.title,
            available: level.available,
            onHand: level.onHand,
            threshold: stockThreshold,
            urgency:
              level.available === 0
                ? "critical"
                : level.available <= 5
                ? "high"
                : "medium",
          });
        }
      });

      return {
        success: true,
        lowStockItems: lowStockItems,
        totalItems: lowStockItems.length,
        criticalItems: lowStockItems.filter(
          (item) => item.urgency === "critical"
        ).length,
        threshold: stockThreshold,
      };
    } catch (error) {
      console.error("Check low stock items error:", error);
      return {
        success: false,
        error: "Failed to check low stock items",
      };
    }
  }

  // Adjust inventory quantities (for manual adjustments)
  async adjustInventoryQuantity(
    variantId,
    locationId,
    delta,
    reason = "correction"
  ) {
    try {
      // First get the inventory item ID for the variant
      const variantQuery = `
        query getVariantInventoryItem($variantId: ID!) {
          productVariant(id: $variantId) {
            id
            inventoryItem {
              id
            }
          }
        }
      `;

      const variantResponse = await shopifyService.graphqlRequest(
        variantQuery,
        {
          variantId: `gid://shopify/ProductVariant/${variantId}`,
        }
      );

      if (variantResponse.errors || !variantResponse.data.productVariant) {
        return {
          success: false,
          error: "Variant not found",
        };
      }

      const inventoryItemId =
        variantResponse.data.productVariant.inventoryItem.id;

      // Adjust the inventory
      const adjustMutation = `
        mutation inventoryAdjustQuantities($input: InventoryAdjustQuantitiesInput!) {
          inventoryAdjustQuantities(input: $input) {
            inventoryAdjustmentGroup {
              id
              reason
              changes {
                name
                delta
                quantityAfterChange
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const adjustVariables = {
        input: {
          name: "available",
          reason: reason,
          changes: [
            {
              inventoryItemId: inventoryItemId,
              locationId: `gid://shopify/Location/${locationId}`,
              delta: delta,
            },
          ],
        },
      };

      const adjustResponse = await shopifyService.graphqlRequest(
        adjustMutation,
        adjustVariables
      );

      if (
        adjustResponse.data?.inventoryAdjustQuantities?.userErrors?.length > 0
      ) {
        return {
          success: false,
          error:
            adjustResponse.data.inventoryAdjustQuantities.userErrors[0].message,
        };
      }

      return {
        success: true,
        adjustment:
          adjustResponse.data.inventoryAdjustQuantities
            .inventoryAdjustmentGroup,
        message: `Inventory adjusted by ${delta} units`,
      };
    } catch (error) {
      console.error("Adjust inventory quantity error:", error);
      return {
        success: false,
        error: "Failed to adjust inventory",
      };
    }
  }

  // Get inventory summary for dashboard
  async getInventorySummary(locationId = null) {
    try {
      const lowStockResult = await this.checkLowStockItems(locationId);

      return {
        success: true,
        summary: {
          lowStockItems: lowStockResult.success ? lowStockResult.totalItems : 0,
          criticalItems: lowStockResult.success
            ? lowStockResult.criticalItems
            : 0,
          threshold: this.defaultSettings.lowStockThreshold,
          lastChecked: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error("Get inventory summary error:", error);
      return {
        success: false,
        error: "Failed to get inventory summary",
      };
    }
  }
}

module.exports = new InventoryManagementService();
