import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getAPIClient } from "../../services/api/dukalink-client";
import { ShopifyStore } from "../../types/shopify";

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  sessionToken: string | null;
  connectedStore: ShopifyStore | null;
  authUrl: string | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: false,
  error: null,
  sessionToken: null,
  connectedStore: null,
  authUrl: null,
};

// Async thunks
export const getShopifyAuthURL = createAsyncThunk(
  "auth/getShopifyAuthURL",
  async (shopDomain: string, { rejectWithValue }) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getShopifyAuthURL(shopDomain);

      if (!response.success) {
        return rejectWithValue(response.error || "Failed to get auth URL");
      }

      return response.data!.authUrl;
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

export const exchangeAuthCode = createAsyncThunk(
  "auth/exchangeAuthCode",
  async (
    { code, state }: { code: string; state: string },
    { rejectWithValue }
  ) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.exchangeAuthCode(code, state);

      if (!response.success) {
        return rejectWithValue(response.error || "Authentication failed");
      }

      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

export const verifySession = createAsyncThunk(
  "auth/verifySession",
  async (_, { rejectWithValue }) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.verifySession();

      if (!response.success) {
        return rejectWithValue(response.error || "Session verification failed");
      }

      return response.data!.store;
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

export const logout = createAsyncThunk(
  "auth/logout",
  async (_, { rejectWithValue }) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.logout();

      if (!response.success) {
        return rejectWithValue(response.error || "Logout failed");
      }

      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearAuthUrl: (state) => {
      state.authUrl = null;
    },
    setSessionToken: (state, action: PayloadAction<string>) => {
      state.sessionToken = action.payload;
      state.isAuthenticated = true;
    },
    clearSession: (state) => {
      state.isAuthenticated = false;
      state.sessionToken = null;
      state.connectedStore = null;
      state.authUrl = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Get Shopify Auth URL
    builder
      .addCase(getShopifyAuthURL.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getShopifyAuthURL.fulfilled, (state, action) => {
        state.isLoading = false;
        state.authUrl = action.payload;
      })
      .addCase(getShopifyAuthURL.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Exchange Auth Code
    builder
      .addCase(exchangeAuthCode.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(exchangeAuthCode.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.sessionToken = action.payload.sessionToken;
        state.connectedStore = action.payload.store;
        state.authUrl = null;
      })
      .addCase(exchangeAuthCode.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Verify Session
    builder
      .addCase(verifySession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifySession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.connectedStore = action.payload;
      })
      .addCase(verifySession.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.sessionToken = null;
        state.connectedStore = null;
        state.error = action.payload as string;
      });

    // Logout
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.sessionToken = null;
        state.connectedStore = null;
        state.authUrl = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, clearAuthUrl, setSessionToken, clearSession } =
  authSlice.actions;
export default authSlice.reducer;
