/**
 * RBAC Components Export Index
 * 
 * Centralized exports for all Role-Based Access Control components.
 * This provides a clean API for importing RBAC components throughout the app.
 */

// Permission-based components
export {
  PermissionGate,
  CanManageStaff,
  CanViewReports,
  CanManageCustomers,
  CanManageInventory,
  CanManageOrders,
  CanViewAnalytics,
  CanManageSystem,
  CanManageDiscounts,
  withPermission,
  withPermissions,
} from './PermissionGate';

// Role-based components
export {
  RoleGate,
  CashierOnly,
  ManagerOnly,
  SuperAdminOnly,
  ManagerOrAbove,
  SuperAdminOrAbove,
  ManagerOrSuperAdmin,
  AllRoles,
  withRole,
  withRoles,
  withMinRoleLevel,
} from './RoleGate';

// Feature-based components
export {
  FeatureGate,
  DashboardStaffManagement,
  DashboardReports,
  DashboardAnalytics,
  DashboardSystemSettings,
  OrderRefundButton,
  OrderEditButton,
  CustomerCreateButton,
  CustomerEditButton,
  CustomerDeleteButton,
  SalesAgentCreateButton,
  SalesAgentEditButton,
  SalesAgentPerformanceView,
  StaffCreateButton,
  StaffEditButton,
  StaffPermissionsEdit,
  StaffRoleEdit,
  InventoryAdjustButton,
  InventoryBulkUpdate,
  DiscountCreateButton,
  DiscountEditButton,
  SystemBackupButton,
  IntegrationSettings,
  AuditLogsView,
  withFeature,
} from './FeatureGate';

// Conditional rendering components
export {
  ConditionalRender,
  useConditionalAccess,
  withConditionalAccess,
} from './ConditionalRender';

// Re-export RBAC configuration for convenience
export {
  PERMISSIONS,
  ROLES,
  SCREEN_ACCESS,
  UI_FEATURES,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasRole,
  hasAnyRole,
  canAccessScreen,
  canAccessFeature,
  getRolePermissions,
  getRoleLevel,
  hasRoleLevel,
  getPermissionsByCategory,
  getAllRoles,
  getAllPermissions,
  isValidPermission,
  isValidRole,
  type Permission,
  type Role,
  type ScreenAccess,
  type UIFeature,
  type PermissionCategory,
  type UserRole,
} from '../../config/rbac';
