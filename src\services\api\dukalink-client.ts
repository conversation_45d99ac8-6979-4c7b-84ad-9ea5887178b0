import { API_CONFIG } from "@/src/constants/Api";
import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import {
  APIResponse,
  Customer,
  CustomerLoyaltyData,
  LoyaltyDiscountCalculation,
  LoyaltyLeaderboardEntry,
  LoyaltyTransaction,
  Order,
  PaginatedResponse,
  PointsRedemptionRequest,
  PointsRedemptionResult,
  Product,
  ShopifyStore,
} from "../../types/shopify";
import { CrossPlatformStorage } from "../../utils/storage";

export interface DukalinkAPIConfig {
  baseURL: string;
  timeout?: number;
  retryAttempts?: number;
}

export class DukalinkAPIClient {
  private client: AxiosInstance;
  private config: DukalinkAPIConfig;
  private sessionToken: string | null = null;

  constructor(config: DukalinkAPIConfig) {
    this.config = {
      timeout: 30000,
      retryAttempts: 3,
      ...config,
    };

    this.client = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        if (!this.sessionToken) {
          this.sessionToken = await CrossPlatformStorage.getItemAsync(
            "session_token"
          );
        }

        if (this.sessionToken) {
          config.headers.Authorization = `Bearer ${this.sessionToken}`;
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired, clear it
          await this.clearSession();
        }
        return Promise.reject(error);
      }
    );
  }

  private async request<T>(
    endpoint: string,
    options: AxiosRequestConfig = {}
  ): Promise<APIResponse<T>> {
    try {
      const response = await this.client.request<APIResponse<T>>({
        url: endpoint,
        ...options,
      });

      return response.data;
    } catch (error: any) {
      console.error(`API Error [${endpoint}]:`, error);

      return {
        success: false,
        error: error.response?.data?.error || error.message || "Network error",
      };
    }
  }

  // POS Authentication methods
  async posLogin(
    username: string,
    password: string
  ): Promise<APIResponse<{ token: string; user: any }>> {
    return this.request("/pos/login", {
      method: "POST",
      data: { username, password },
    });
  }

  // POS Authentication with Device Info (for terminal assignment)
  async posLoginWithDevice(
    username: string,
    password: string,
    deviceInfo: any
  ): Promise<
    APIResponse<{ token: string; user: any; terminal?: any; location?: any }>
  > {
    return this.request("/pos/login", {
      method: "POST",
      data: { username, password, deviceInfo },
    });
  }

  async posVerify(): Promise<APIResponse<{ user: any }>> {
    return this.request("/pos/verify");
  }

  async posLogout(): Promise<APIResponse<void>> {
    const response = await this.request<void>("/pos/logout", {
      method: "POST",
    });
    await this.clearSession();
    return response;
  }

  async setPOSToken(token: string): Promise<void> {
    this.sessionToken = token;
    await CrossPlatformStorage.setItemAsync("session_token", token);
  }

  // Store API methods (real Shopify data)
  async getStoreInfo(): Promise<APIResponse<{ store: ShopifyStore }>> {
    return this.request("/store/info");
  }

  async getStoreProducts(params?: {
    page?: number;
    limit?: number;
    search?: string;
    cursor?: string;
  }): Promise<APIResponse<{ products: Product[]; pagination: any }>> {
    return this.request("/store/products", {
      params,
    });
  }

  async getStoreCustomers(params?: {
    limit?: number;
    search?: string;
    includeLoyalty?: boolean;
  }): Promise<APIResponse<{ customers: Customer[]; pagination: any }>> {
    return this.request("/store/customers", {
      params,
    });
  }

  // Get customer's active tickets
  async getCustomerTickets(
    customerId: string,
    params?: {
      status?: string;
      limit?: number;
      includeExpired?: boolean;
    }
  ): Promise<APIResponse<{ tickets: any[]; count: number }>> {
    return this.request(`/customers/${customerId}/tickets`, {
      params,
    });
  }

  async createStoreCustomer(customerData: {
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    note?: string;
    tags?: string;
    addresses?: {
      firstName?: string;
      lastName?: string;
      address1: string;
      address2?: string;
      city: string;
      province?: string;
      country?: string;
      zip: string;
      phone?: string;
    }[];
  }): Promise<APIResponse<{ customer: Customer }>> {
    return this.request("/store/customers", {
      method: "POST",
      data: customerData,
    });
  }

  async createStoreOrder(
    orderData: any
  ): Promise<APIResponse<{ order: Order }>> {
    return this.request("/store/orders", {
      method: "POST",
      data: orderData,
    });
  }

  async getStoreOrders(params?: {
    limit?: number;
    status?: string;
  }): Promise<APIResponse<{ orders: Order[] }>> {
    return this.request("/store/orders", {
      params,
    });
  }

  async updateInventory(
    variantId: string,
    quantity: number
  ): Promise<APIResponse<any>> {
    return this.request(`/store/inventory/${variantId}`, {
      method: "PUT",
      data: { quantity },
    });
  }

  // Shopify Authentication methods (for store setup only)
  async getShopifyAuthURL(
    shopDomain: string
  ): Promise<APIResponse<{ authUrl: string }>> {
    return this.request("/auth/shopify/url", {
      method: "POST",
      data: { shopDomain },
    });
  }

  async exchangeAuthCode(
    code: string,
    state: string
  ): Promise<APIResponse<{ sessionToken: string; store: ShopifyStore }>> {
    const response = await this.request<{
      sessionToken: string;
      store: ShopifyStore;
    }>("/auth/shopify/callback", {
      method: "POST",
      data: { code, state },
    });

    if (response.success && response.data) {
      this.sessionToken = response.data.sessionToken;
      await CrossPlatformStorage.setItemAsync(
        "session_token",
        response.data.sessionToken
      );
    }

    return response;
  }

  async verifySession(): Promise<APIResponse<{ store: ShopifyStore }>> {
    return this.request("/auth/verify");
  }

  async logout(): Promise<APIResponse<void>> {
    const response = await this.request<void>("/auth/logout", {
      method: "POST",
    });
    await this.clearSession();
    return response;
  }

  private async clearSession(): Promise<void> {
    this.sessionToken = null;
    await CrossPlatformStorage.deleteItemAsync("session_token");
  }

  // Store management
  async getConnectedStores(): Promise<APIResponse<ShopifyStore[]>> {
    return this.request("/stores");
  }

  async getStore(storeId: string): Promise<APIResponse<ShopifyStore>> {
    return this.request(`/stores/${storeId}`);
  }

  async disconnectStore(storeId: string): Promise<APIResponse<void>> {
    return this.request(`/stores/${storeId}/disconnect`, { method: "POST" });
  }

  // Product management
  async getProducts(
    storeId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      category?: string;
    }
  ): Promise<APIResponse<PaginatedResponse<Product>>> {
    return this.request(`/stores/${storeId}/products`, {
      params,
    });
  }

  async getProduct(
    storeId: string,
    productId: string
  ): Promise<APIResponse<Product>> {
    return this.request(`/stores/${storeId}/products/${productId}`);
  }

  async searchProducts(
    storeId: string,
    query: string,
    limit: number = 20
  ): Promise<APIResponse<Product[]>> {
    return this.request(`/stores/${storeId}/products/search`, {
      params: { q: query, limit },
    });
  }

  async syncProducts(
    storeId: string
  ): Promise<APIResponse<{ syncId: string }>> {
    return this.request(`/stores/${storeId}/products/sync`, { method: "POST" });
  }

  // Customer management
  async getCustomers(
    storeId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
    }
  ): Promise<APIResponse<PaginatedResponse<Customer>>> {
    return this.request(`/stores/${storeId}/customers`, {
      params,
    });
  }

  async getCustomer(
    storeId: string,
    customerId: string
  ): Promise<APIResponse<Customer>> {
    return this.request(`/stores/${storeId}/customers/${customerId}`);
  }

  async createCustomer(
    storeId: string,
    customerData: Partial<Customer>
  ): Promise<APIResponse<Customer>> {
    return this.request(`/stores/${storeId}/customers`, {
      method: "POST",
      data: customerData,
    });
  }

  async updateCustomer(
    storeId: string,
    customerId: string,
    customerData: Partial<Customer>
  ): Promise<APIResponse<Customer>> {
    return this.request(`/stores/${storeId}/customers/${customerId}`, {
      method: "PUT",
      data: customerData,
    });
  }

  async searchCustomers(
    storeId: string,
    query: string,
    limit: number = 20
  ): Promise<APIResponse<Customer[]>> {
    return this.request(`/stores/${storeId}/customers/search`, {
      params: { q: query, limit },
    });
  }

  // Order management
  async getOrders(
    storeId: string,
    params?: {
      page?: number;
      limit?: number;
      status?: string;
      dateFrom?: string;
      dateTo?: string;
    }
  ): Promise<APIResponse<PaginatedResponse<Order>>> {
    return this.request(`/stores/${storeId}/orders`, {
      params,
    });
  }

  async getOrder(
    storeId: string,
    orderId: string
  ): Promise<APIResponse<Order>> {
    return this.request(`/stores/${storeId}/orders/${orderId}`);
  }

  async createOrder(
    storeId: string,
    orderData: Partial<Order>
  ): Promise<APIResponse<Order>> {
    return this.request(`/stores/${storeId}/orders`, {
      method: "POST",
      data: orderData,
    });
  }

  async updateOrder(
    storeId: string,
    orderId: string,
    orderData: Partial<Order>
  ): Promise<APIResponse<Order>> {
    return this.request(`/stores/${storeId}/orders/${orderId}`, {
      method: "PUT",
      data: orderData,
    });
  }

  async cancelOrder(
    storeId: string,
    orderId: string,
    reason?: string
  ): Promise<APIResponse<Order>> {
    return this.request(`/stores/${storeId}/orders/${orderId}/cancel`, {
      method: "POST",
      data: { reason },
    });
  }

  // Legacy inventory management (kept for compatibility)
  async updateStoreInventory(
    storeId: string,
    variantId: string,
    quantity: number
  ): Promise<APIResponse<{ success: boolean }>> {
    return this.request(`/stores/${storeId}/inventory/${variantId}`, {
      method: "PUT",
      data: { quantity },
    });
  }

  async getInventoryLevels(
    storeId: string,
    variantIds: string[]
  ): Promise<APIResponse<{ [variantId: string]: number }>> {
    return this.request(`/stores/${storeId}/inventory/levels`, {
      params: { variantIds: variantIds.join(",") },
    });
  }

  // Sync operations
  async getSyncStatus(storeId: string): Promise<
    APIResponse<{
      isOnline: boolean;
      lastSyncAt?: string;
      pendingOperations: number;
      failedOperations: number;
    }>
  > {
    return this.request(`/stores/${storeId}/sync/status`);
  }

  async triggerSync(
    storeId: string,
    type: "products" | "customers" | "orders" | "all"
  ): Promise<APIResponse<{ syncId: string }>> {
    return this.request(`/stores/${storeId}/sync/${type}`, { method: "POST" });
  }

  // Sales Agent Management
  async getSalesAgents(): Promise<APIResponse<{ salesAgents: any[] }>> {
    return this.request("/sales-agents");
  }

  async getSalesAgentById(
    agentId: string
  ): Promise<APIResponse<{ salesAgent: any }>> {
    return this.request(`/sales-agents/${agentId}`);
  }

  async getSalesAgentCustomers(
    agentId: string
  ): Promise<APIResponse<{ customers: Customer[] }>> {
    return this.request(`/sales-agents/${agentId}/customers`);
  }

  async getSalesAgentPerformance(
    agentId: string
  ): Promise<APIResponse<{ performance: any }>> {
    return this.request(`/sales-agents/${agentId}/performance`);
  }

  async findCustomerSalesAgent(
    customerId: string
  ): Promise<APIResponse<{ salesAgent: any }>> {
    return this.request(`/customers/${customerId}/sales-agent`);
  }

  async createSalesAgent(agentData: {
    name: string;
    email: string;
    phone?: string;
    commissionRate: number;
    territory?: string;
    region?: string;
  }): Promise<APIResponse<{ salesAgent: any }>> {
    return this.request("/sales-agents", {
      method: "POST",
      data: agentData,
    });
  }

  async updateSalesAgent(
    agentId: string,
    agentData: Partial<{
      name: string;
      email: string;
      phone: string;
      commissionRate: number;
      territory: string;
      region: string;
      active: boolean;
    }>
  ): Promise<APIResponse<{ salesAgent: any }>> {
    return this.request(`/sales-agents/${agentId}`, {
      method: "PUT",
      data: agentData,
    });
  }

  // Staff Management
  async getStaff(): Promise<APIResponse<{ staffMembers: any[] }>> {
    return this.request("/staff");
  }

  async getStaffById(
    staffId: string
  ): Promise<APIResponse<{ staffMember: any }>> {
    return this.request(`/staff/${staffId}`);
  }

  async getStaffProfile(): Promise<APIResponse<{ user: any }>> {
    return this.request("/pos/profile");
  }

  async updateStaffCommissionRate(
    staffId: string,
    commissionRate: number
  ): Promise<APIResponse<{ message: string; metafield: any }>> {
    return this.request(`/staff/${staffId}/commission`, {
      method: "PUT",
      data: { commissionRate },
    });
  }

  async getStaffOrders(
    staffId: string,
    params?: {
      limit?: number;
      status?: string;
      dateFrom?: string;
      dateTo?: string;
    }
  ): Promise<
    APIResponse<{ orders: any[]; totalCount: number; staffId: string }>
  > {
    return this.request(`/staff/${staffId}/orders`, {
      params,
    });
  }

  async getStaffPerformance(
    staffId: string,
    params?: {
      dateFrom?: string;
      dateTo?: string;
    }
  ): Promise<
    APIResponse<{ staffMember: any; performance: any; orders: any[] }>
  > {
    return this.request(`/staff/${staffId}/performance`, {
      params,
    });
  }

  async createStaff(staffData: {
    username: string;
    name: string;
    email?: string;
    password: string;
    role: string;
    commissionRate: number;
    permissions: string[];
  }): Promise<APIResponse<{ staffMember: any; message: string }>> {
    return this.request("/staff", {
      method: "POST",
      data: staffData,
    });
  }

  // Commission System Management
  async getCommissionConfiguration(): Promise<
    APIResponse<{ configuration: any }>
  > {
    return this.request("/discounts/configuration");
  }

  async updateCommissionConfiguration(
    configuration: any
  ): Promise<APIResponse<{ configuration: any }>> {
    return this.request("/discounts/configuration", {
      method: "PUT",
      data: configuration,
    });
  }

  // Commission-based Discounts
  async calculateCommissionDiscount(discountData: {
    cartData: any;
    staffId?: string;
    salesAgentId?: string;
    customerId?: string;
  }): Promise<APIResponse<{ discount: any }>> {
    return this.request("/discounts/calculate", {
      method: "POST",
      data: discountData,
    });
  }

  async getDiscountConfiguration(): Promise<
    APIResponse<{ configuration: any }>
  > {
    return this.request("/discounts/configuration");
  }

  async getCustomerLoyaltyTier(
    customerId: string
  ): Promise<APIResponse<{ loyaltyTier: string }>> {
    return this.request(`/customers/${customerId}/loyalty-tier`);
  }

  // Customer Loyalty Management
  async getCustomerLoyaltySummary(
    customerId: string
  ): Promise<APIResponse<{ summary: CustomerLoyaltyData }>> {
    return this.request(`/loyalty/customers/${customerId}/summary`);
  }

  async getCustomerLoyaltyTransactions(
    customerId: string,
    params?: {
      limit?: number;
      offset?: number;
      type?: "earned" | "redeemed" | "expired" | "adjusted";
    }
  ): Promise<
    APIResponse<{ transactions: LoyaltyTransaction[]; count: number }>
  > {
    return this.request(`/loyalty/customers/${customerId}/transactions`, {
      params,
    });
  }

  async calculateLoyaltyDiscounts(
    customerId: string,
    orderTotal: number
  ): Promise<APIResponse<LoyaltyDiscountCalculation>> {
    return this.request(
      `/loyalty/customers/${customerId}/discounts/calculate`,
      {
        method: "POST",
        data: { orderTotal },
      }
    );
  }

  async redeemLoyaltyPoints(
    customerId: string,
    redemptionData: PointsRedemptionRequest
  ): Promise<APIResponse<PointsRedemptionResult>> {
    return this.request(`/loyalty/customers/${customerId}/points/redeem`, {
      method: "POST",
      data: redemptionData,
    });
  }

  async addLoyaltyPoints(
    customerId: string,
    pointsData: {
      orderTotal: number;
      orderId: string;
      salesAgentId?: string;
    }
  ): Promise<
    APIResponse<{
      pointsAdded: number;
      newBalance: number;
      tierChanged: boolean;
      newTier?: string;
      transactionId: string;
    }>
  > {
    return this.request(`/loyalty/customers/${customerId}/points/add`, {
      method: "POST",
      data: pointsData,
    });
  }

  async initializeCustomerLoyalty(
    customerId: string
  ): Promise<APIResponse<{ loyalty: any; message: string }>> {
    return this.request(`/loyalty/customers/${customerId}/initialize`, {
      method: "POST",
    });
  }

  // Loyalty Analytics and Leaderboard
  async getLoyaltyLeaderboard(params?: {
    limit?: number;
    tier?: "bronze" | "silver" | "gold" | "platinum";
    orderBy?: "points" | "purchases";
  }): Promise<APIResponse<LoyaltyLeaderboardEntry[]>> {
    return this.request("/loyalty/leaderboard", {
      params,
    });
  }

  async getLoyaltyAnalytics(params?: {
    dateFrom?: string;
    dateTo?: string;
    tier?: "bronze" | "silver" | "gold" | "platinum";
  }): Promise<
    APIResponse<{
      totalCustomers: number;
      totalPoints: number;
      tierDistribution: Record<string, number>;
      pointsEarned: number;
      pointsRedeemed: number;
      averagePointsPerCustomer: number;
    }>
  > {
    return this.request("/loyalty/analytics", {
      params,
    });
  }

  // Dual Attribution Order Creation
  async createOrderWithDualAttribution(orderData: {
    lineItems: any[];
    customer?: any;
    staffId: string;
    salesAgentId: string;
    email?: string;
    phone?: string;
    note?: string;
    tags?: string;
    billingAddress?: any;
    // Payment information
    paymentMethod?: string;
    paymentTransactionId?: string;
    paymentTimestamp?: string;
    financialStatus?: string;
    // Location information
    locationId?: string;
    locationName?: string;
  }): Promise<APIResponse<{ order: Order }>> {
    return this.request("/orders/with-dual-attribution", {
      method: "POST",
      data: orderData,
    });
  }

  // Inventory Management
  async validateOrderInventory(
    lineItems: any[]
  ): Promise<APIResponse<{ valid: boolean; validationResults: any[] }>> {
    return this.request("/inventory/validate-order", {
      method: "POST",
      data: { lineItems },
    });
  }

  async checkInventoryLevel(
    variantId: string
  ): Promise<APIResponse<{ available: number; tracked: boolean }>> {
    return this.request(`/inventory/check/${variantId}`);
  }

  // Location Management
  async getLocations(): Promise<APIResponse<{ locations: any[] }>> {
    return this.request("/locations");
  }

  async getLocationById(
    locationId: string
  ): Promise<APIResponse<{ location: any }>> {
    return this.request(`/locations/${locationId}`);
  }

  async getLocationInventory(
    locationId: string,
    variantIds: string[]
  ): Promise<APIResponse<{ locationId: string; inventory: any }>> {
    return this.request("/locations/inventory", {
      method: "POST",
      data: { locationId, variantIds },
    });
  }

  async getVariantInventoryAcrossLocations(
    variantId: string
  ): Promise<APIResponse<{ variantId: string; inventoryLevels: any[] }>> {
    return this.request(`/locations/inventory/${variantId}`);
  }

  async testLocationOperations(
    locationId: string,
    variantIds?: string[]
  ): Promise<APIResponse<{ location: any; inventory?: any; message: string }>> {
    return this.request("/locations/test-location-operations", {
      method: "POST",
      data: { locationId, variantIds },
    });
  }

  // User Switching methods (PIN-based multi-user sessions)
  async initializeUserPin(
    staffId: string,
    pin: string,
    confirmPin: string
  ): Promise<APIResponse<{ message: string }>> {
    return this.request("/pos/user-switching/initialize-pin", {
      method: "POST",
      data: { staffId, pin, confirmPin },
    });
  }

  async validateUserPin(
    staffId: string,
    pin: string
  ): Promise<APIResponse<{ valid: boolean; staff: any }>> {
    return this.request("/pos/user-switching/validate-pin", {
      method: "POST",
      data: { staffId, pin },
    });
  }

  async switchUser(
    targetStaffId: string,
    pin: string,
    reason?: string
  ): Promise<
    APIResponse<{ sessionContext: any; switchId: string; currentUser: any }>
  > {
    return this.request("/pos/user-switching/switch-user", {
      method: "POST",
      data: { targetStaffId, pin, reason: reason || "manual_switch" },
    });
  }

  async switchBack(
    switchId: string
  ): Promise<APIResponse<{ sessionContext: any; currentUser: any }>> {
    return this.request("/pos/user-switching/switch-back", {
      method: "POST",
      data: { switchId },
    });
  }

  async getSessionContext(): Promise<
    APIResponse<{
      sessionContext: any;
      hasActiveSwitch: boolean;
      canSwitchBack: boolean;
    }>
  > {
    return this.request("/pos/user-switching/session-context");
  }

  async getAvailableStaff(): Promise<
    APIResponse<{
      staff: any[];
      availableCount: number;
      totalCount: number;
    }>
  > {
    return this.request("/pos/user-switching/available-staff");
  }

  async getUserSwitchHistory(
    limit?: number
  ): Promise<APIResponse<{ history: any[]; count: number }>> {
    return this.request("/pos/user-switching/switch-history", {
      params: { limit: limit || 50 },
    });
  }

  async getSessionStats(): Promise<APIResponse<any>> {
    return this.request("/pos/user-switching/session-stats");
  }

  async getAuditTrail(params?: {
    staffId?: string;
    eventTypes?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<APIResponse<{ auditLogs: any[]; stats: any; criteria: any }>> {
    return this.request("/pos/user-switching/audit-trail", {
      params,
    });
  }

  // Fulfillment Management API methods
  async createFulfillment(fulfillmentData: any): Promise<APIResponse<any>> {
    return this.request("/fulfillment/fulfillments", {
      method: "POST",
      data: { fulfillmentData },
    });
  }

  async getFulfillment(fulfillmentId: string): Promise<APIResponse<any>> {
    return this.request(`/fulfillment/fulfillments/${fulfillmentId}`);
  }

  async getFulfillmentsByOrder(orderId: string): Promise<APIResponse<any>> {
    return this.request(`/fulfillment/orders/${orderId}/fulfillments`);
  }

  async updateDeliveryDetails(
    fulfillmentId: string,
    updateData: any
  ): Promise<APIResponse<any>> {
    return this.request(`/fulfillment/fulfillments/${fulfillmentId}/delivery`, {
      method: "PUT",
      data: { updateData },
    });
  }

  async updateFulfillmentStatus(
    fulfillmentId: string,
    status: string
  ): Promise<APIResponse<any>> {
    return this.request(`/fulfillment/fulfillments/${fulfillmentId}/status`, {
      method: "PUT",
      data: { status },
    });
  }

  async calculateShippingFee(shippingData: any): Promise<APIResponse<any>> {
    return this.request("/fulfillment/shipping/calculate", {
      method: "POST",
      data: { shippingData },
    });
  }

  async getShippingRates(): Promise<APIResponse<any>> {
    return this.request("/fulfillment/shipping/rates");
  }

  async upsertShippingRate(rateData: any): Promise<APIResponse<any>> {
    return this.request("/fulfillment/shipping/rates", {
      method: "POST",
      data: { rateData },
    });
  }

  async updateShippingRate(
    rateId: string,
    rateData: any
  ): Promise<APIResponse<any>> {
    return this.request(`/fulfillment/shipping/rates/${rateId}`, {
      method: "PUT",
      data: { rateData },
    });
  }

  async getFulfillmentStats(): Promise<APIResponse<any>> {
    return this.request("/fulfillment/fulfillments/stats");
  }

  // Discount Management API methods
  async getDiscountRules(): Promise<APIResponse<any>> {
    return this.request("/discounts/rules");
  }

  async createDiscountRule(ruleData: any): Promise<APIResponse<any>> {
    return this.request("/discounts/rules", {
      method: "POST",
      data: ruleData,
    });
  }

  async updateDiscountRule(
    ruleId: string,
    updateData: any
  ): Promise<APIResponse<any>> {
    return this.request(`/discounts/rules/${ruleId}`, {
      method: "PUT",
      data: updateData,
    });
  }

  async deleteDiscountRule(ruleId: string): Promise<APIResponse<any>> {
    return this.request(`/discounts/rules/${ruleId}`, {
      method: "DELETE",
    });
  }

  async getDiscountPermissions(): Promise<APIResponse<any>> {
    return this.request("/discounts/permissions");
  }

  async updateDiscountPermissions(
    permissionsData: any
  ): Promise<APIResponse<any>> {
    return this.request("/discounts/permissions", {
      method: "PUT",
      data: permissionsData,
    });
  }

  async getDiscountAnalytics(params?: any): Promise<APIResponse<any>> {
    return this.request("/discounts/analytics", {
      params,
    });
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.sessionToken;
  }

  async refreshToken(): Promise<boolean> {
    try {
      const response = await this.request("/auth/refresh", { method: "POST" });
      if (response.success && response.data) {
        this.sessionToken = (response.data as any).sessionToken;
        if (this.sessionToken) {
          await CrossPlatformStorage.setItemAsync(
            "session_token",
            this.sessionToken
          );
        }
        return true;
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
    }
    return false;
  }
}

// Singleton instance
let apiClient: DukalinkAPIClient | null = null;

export const createAPIClient = (
  config: DukalinkAPIConfig
): DukalinkAPIClient => {
  if (!apiClient) {
    apiClient = new DukalinkAPIClient(config);
  }
  return apiClient;
};

export const getAPIClient = (): DukalinkAPIClient => {
  if (!apiClient) {
    // Use centralized API configuration
    apiClient = new DukalinkAPIClient({
      baseURL: API_CONFIG.baseURL,
      timeout: API_CONFIG.timeout,
      retryAttempts: API_CONFIG.retryAttempts,
    });
  }
  return apiClient;
};
