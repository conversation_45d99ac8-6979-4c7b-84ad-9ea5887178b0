/**
 * Shopify Metafields Service
 * Manages loyalty points, customer tiers, and discount tracking metafields
 * Follows patterns from existing shopify-service.js
 */

const axios = require("axios");

class ShopifyMetafieldsService {
  constructor() {
    this.shopifyService = require("./shopify-service");

    // Metafield definitions for loyalty system
    this.metafieldDefinitions = {
      customer: {
        loyaltyPoints: {
          namespace: "dukalink_loyalty",
          key: "points_balance",
          name: "Loyalty Points Balance",
          description: "Customer's current loyalty points balance",
          type: "number_integer",
          validations: [{ name: "min", value: "0" }],
        },
        loyaltyTier: {
          namespace: "dukalink_loyalty",
          key: "tier_level",
          name: "Loyalty Tier",
          description:
            "Customer's loyalty tier (bronze, silver, gold, platinum)",
          type: "single_line_text_field",
          validations: [
            {
              name: "choices",
              value: ["bronze", "silver", "gold", "platinum"],
            },
          ],
        },
        totalPurchases: {
          namespace: "dukalink_loyalty",
          key: "total_purchases",
          name: "Total Purchase Amount",
          description: "Customer's total purchase amount for tier calculation",
          type: "money",
        },
        totalOrders: {
          namespace: "dukalink_loyalty",
          key: "total_orders",
          name: "Total Orders Count",
          description: "Customer's total number of orders for tier calculation",
          type: "number_integer",
          validations: [{ name: "min", value: "0" }],
        },
        lastPurchase: {
          namespace: "dukalink_loyalty",
          key: "last_purchase_date",
          name: "Last Purchase Date",
          description: "Date of customer's last purchase",
          type: "date_time",
        },
        tierUpdated: {
          namespace: "dukalink_loyalty",
          key: "tier_updated_date",
          name: "Tier Updated Date",
          description: "Date when customer's tier was last updated",
          type: "date_time",
        },
      },
      order: {
        loyaltyPointsEarned: {
          namespace: "dukalink_loyalty",
          key: "points_earned",
          name: "Loyalty Points Earned",
          description: "Points earned from this order",
          type: "number_integer",
          validations: [{ name: "min", value: "0" }],
        },
        loyaltyPointsRedeemed: {
          namespace: "dukalink_loyalty",
          key: "points_redeemed",
          name: "Loyalty Points Redeemed",
          description: "Points redeemed for discount on this order",
          type: "number_integer",
          validations: [{ name: "min", value: "0" }],
        },
        loyaltyDiscount: {
          namespace: "dukalink_loyalty",
          key: "loyalty_discount_amount",
          name: "Loyalty Discount Amount",
          description: "Discount amount applied from loyalty benefits",
          type: "money",
        },
        staffDiscount: {
          namespace: "dukalink_discounts",
          key: "staff_discount_amount",
          name: "Staff Discount Amount",
          description: "Discount amount applied by staff",
          type: "money",
        },
        discountRuleId: {
          namespace: "dukalink_discounts",
          key: "discount_rule_id",
          name: "Applied Discount Rule ID",
          description: "ID of the discount rule applied to this order",
          type: "single_line_text_field",
        },
        posStaffId: {
          namespace: "dukalink_pos",
          key: "staff_id",
          name: "POS Staff ID",
          description: "ID of the staff member who processed this order",
          type: "single_line_text_field",
        },
        salesAgentId: {
          namespace: "dukalink_pos",
          key: "sales_agent_id",
          name: "Sales Agent ID",
          description: "ID of the sales agent associated with this order",
          type: "single_line_text_field",
        },
      },
    };
  }

  // Get base URL and headers from shopify service
  getBaseURL() {
    return this.shopifyService.baseURL;
  }

  getHeaders() {
    return this.shopifyService.getHeaders();
  }

  // Make GraphQL request using shopify service method
  async graphqlRequest(query, variables = {}) {
    return await this.shopifyService.graphqlRequest(query, variables);
  }

  // Create metafield definition
  async createMetafieldDefinition(ownerType, definition) {
    try {
      const mutation = `
        mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
          metafieldDefinitionCreate(definition: $definition) {
            createdDefinition {
              id
              name
              namespace
              key
              description
              type {
                name
              }
              ownerType
              validations {
                name
                value
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        definition: {
          name: definition.name,
          namespace: definition.namespace,
          key: definition.key,
          description: definition.description,
          type: definition.type,
          ownerType: ownerType.toUpperCase(),
          validations: definition.validations || [],
        },
      };

      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.metafieldDefinitionCreate?.userErrors?.length > 0) {
        const errors = response.data.metafieldDefinitionCreate.userErrors;
        return {
          success: false,
          error: errors.map((e) => e.message).join(", "),
        };
      }

      return {
        success: true,
        definition: response.data?.metafieldDefinitionCreate?.createdDefinition,
      };
    } catch (error) {
      console.error("Create metafield definition error:", error);
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get existing metafield definitions
  async getMetafieldDefinitions(ownerType, namespace = null) {
    try {
      const query = `
        query getMetafieldDefinitions($ownerType: MetafieldOwnerType!, $namespace: String) {
          metafieldDefinitions(first: 50, ownerType: $ownerType, namespace: $namespace) {
            edges {
              node {
                id
                name
                namespace
                key
                description
                type {
                  name
                }
                ownerType
                validations {
                  name
                  value
                }
              }
            }
          }
        }
      `;

      const variables = {
        ownerType: ownerType.toUpperCase(),
      };

      if (namespace) {
        variables.namespace = namespace;
      }

      const response = await this.graphqlRequest(query, variables);

      if (response.data?.metafieldDefinitions) {
        const definitions = response.data.metafieldDefinitions.edges.map(
          (edge) => edge.node
        );
        return {
          success: true,
          definitions: definitions,
        };
      }

      return {
        success: true,
        definitions: [],
      };
    } catch (error) {
      console.error("Get metafield definitions error:", error);
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Setup all loyalty metafield definitions
  async setupLoyaltyMetafields() {
    try {
      const results = {
        customer: [],
        order: [],
        errors: [],
      };

      console.log("🏗️ Setting up loyalty metafield definitions...");

      // Setup customer metafields
      for (const [key, definition] of Object.entries(
        this.metafieldDefinitions.customer
      )) {
        console.log(
          `Creating customer metafield: ${definition.namespace}.${definition.key}`
        );
        const result = await this.createMetafieldDefinition(
          "customer",
          definition
        );

        if (result.success) {
          results.customer.push({
            key: key,
            definition: result.definition,
          });
          console.log(`✅ Created: ${definition.name}`);
        } else {
          // Check if it already exists
          if (
            result.error.includes("already exists") ||
            result.error.includes("taken")
          ) {
            console.log(`⚠️ Already exists: ${definition.name}`);
            results.customer.push({
              key: key,
              definition: definition,
              existed: true,
            });
          } else {
            console.error(
              `❌ Failed to create ${definition.name}: ${result.error}`
            );
            results.errors.push({
              type: "customer",
              key: key,
              error: result.error,
            });
          }
        }
      }

      // Setup order metafields
      for (const [key, definition] of Object.entries(
        this.metafieldDefinitions.order
      )) {
        console.log(
          `Creating order metafield: ${definition.namespace}.${definition.key}`
        );
        const result = await this.createMetafieldDefinition(
          "order",
          definition
        );

        if (result.success) {
          results.order.push({
            key: key,
            definition: result.definition,
          });
          console.log(`✅ Created: ${definition.name}`);
        } else {
          // Check if it already exists
          if (
            result.error.includes("already exists") ||
            result.error.includes("taken")
          ) {
            console.log(`⚠️ Already exists: ${definition.name}`);
            results.order.push({
              key: key,
              definition: definition,
              existed: true,
            });
          } else {
            console.error(
              `❌ Failed to create ${definition.name}: ${result.error}`
            );
            results.errors.push({
              type: "order",
              key: key,
              error: result.error,
            });
          }
        }
      }

      console.log(
        `✅ Metafield setup complete. Customer: ${results.customer.length}, Order: ${results.order.length}, Errors: ${results.errors.length}`
      );

      return {
        success: results.errors.length === 0,
        results: results,
        message: `Setup complete. Created ${results.customer.length} customer and ${results.order.length} order metafield definitions.`,
      };
    } catch (error) {
      console.error("Setup loyalty metafields error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Set customer loyalty metafields
  async setCustomerLoyaltyData(customerId, loyaltyData) {
    try {
      const metafields = [];

      // Prepare metafields array
      if (loyaltyData.points !== undefined) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "points_balance",
          value: loyaltyData.points.toString(),
          type: "number_integer",
        });
      }

      if (loyaltyData.tier) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "tier_level",
          value: loyaltyData.tier,
          type: "single_line_text_field",
        });
      }

      if (loyaltyData.totalPurchases !== undefined) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "total_purchases",
          value: loyaltyData.totalPurchases.toString(),
          type: "money",
        });
      }

      if (loyaltyData.totalOrders !== undefined) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "total_orders",
          value: loyaltyData.totalOrders.toString(),
          type: "number_integer",
        });
      }

      if (loyaltyData.lastPurchase) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "last_purchase_date",
          value: loyaltyData.lastPurchase,
          type: "date_time",
        });
      }

      if (loyaltyData.tierUpdated) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "tier_updated_date",
          value: loyaltyData.tierUpdated,
          type: "date_time",
        });
      }

      // Use GraphQL mutation to set metafields
      const mutation = `
        mutation customerUpdate($input: CustomerInput!) {
          customerUpdate(input: $input) {
            customer {
              id
              metafields(first: 20, namespace: "dukalink_loyalty") {
                edges {
                  node {
                    id
                    namespace
                    key
                    value
                    type
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: customerId,
          metafields: metafields,
        },
      };

      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.customerUpdate?.userErrors?.length > 0) {
        const errors = response.data.customerUpdate.userErrors;
        return {
          success: false,
          error: errors.map((e) => e.message).join(", "),
        };
      }

      return {
        success: true,
        customer: response.data?.customerUpdate?.customer,
        metafieldsUpdated: metafields.length,
      };
    } catch (error) {
      console.error("Set customer loyalty data error:", error);
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get customer loyalty metafields
  async getCustomerLoyaltyData(customerId) {
    try {
      const query = `
        query getCustomerLoyalty($id: ID!) {
          customer(id: $id) {
            id
            email
            firstName
            lastName
            metafields(first: 20, namespace: "dukalink_loyalty") {
              edges {
                node {
                  id
                  namespace
                  key
                  value
                  type
                }
              }
            }
            tags
          }
        }
      `;

      const variables = { id: customerId };
      const response = await this.graphqlRequest(query, variables);

      if (response.data?.customer) {
        const customer = response.data.customer;
        const metafields = customer.metafields.edges.map((edge) => edge.node);

        // Parse metafields into structured data
        const loyaltyData = {
          customerId: customer.id,
          email: customer.email,
          firstName: customer.firstName,
          lastName: customer.lastName,
          tags: customer.tags,
          points: 0,
          tier: "bronze",
          totalPurchases: 0,
          totalOrders: 0,
          lastPurchase: null,
          tierUpdated: null,
        };

        metafields.forEach((field) => {
          switch (field.key) {
            case "points_balance":
              loyaltyData.points = parseInt(field.value) || 0;
              break;
            case "tier_level":
              loyaltyData.tier = field.value || "bronze";
              break;
            case "total_purchases":
              loyaltyData.totalPurchases = parseFloat(field.value) || 0;
              break;
            case "total_orders":
              loyaltyData.totalOrders = parseInt(field.value) || 0;
              break;
            case "last_purchase_date":
              loyaltyData.lastPurchase = field.value;
              break;
            case "tier_updated_date":
              loyaltyData.tierUpdated = field.value;
              break;
          }
        });

        return {
          success: true,
          loyaltyData: loyaltyData,
        };
      }

      return {
        success: false,
        error: "Customer not found",
      };
    } catch (error) {
      console.error("Get customer loyalty data error:", error);
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Set order loyalty and discount metafields
  async setOrderMetafields(orderId, orderData) {
    try {
      const metafields = [];

      // Prepare order metafields
      if (orderData.pointsEarned !== undefined) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "points_earned",
          value: orderData.pointsEarned.toString(),
          type: "number_integer",
        });
      }

      if (orderData.pointsRedeemed !== undefined) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "points_redeemed",
          value: orderData.pointsRedeemed.toString(),
          type: "number_integer",
        });
      }

      if (orderData.loyaltyDiscount !== undefined) {
        metafields.push({
          namespace: "dukalink_loyalty",
          key: "loyalty_discount_amount",
          value: orderData.loyaltyDiscount.toString(),
          type: "money",
        });
      }

      if (orderData.staffDiscount !== undefined) {
        metafields.push({
          namespace: "dukalink_discounts",
          key: "staff_discount_amount",
          value: orderData.staffDiscount.toString(),
          type: "money",
        });
      }

      if (orderData.discountRuleId) {
        metafields.push({
          namespace: "dukalink_discounts",
          key: "discount_rule_id",
          value: orderData.discountRuleId,
          type: "single_line_text_field",
        });
      }

      if (orderData.staffId) {
        metafields.push({
          namespace: "dukalink_pos",
          key: "staff_id",
          value: orderData.staffId,
          type: "single_line_text_field",
        });
      }

      if (orderData.salesAgentId) {
        metafields.push({
          namespace: "dukalink_pos",
          key: "sales_agent_id",
          value: orderData.salesAgentId,
          type: "single_line_text_field",
        });
      }

      // Use GraphQL mutation to set order metafields
      const mutation = `
        mutation orderUpdate($input: OrderInput!) {
          orderUpdate(input: $input) {
            order {
              id
              metafields(first: 20) {
                edges {
                  node {
                    id
                    namespace
                    key
                    value
                    type
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: orderId,
          metafields: metafields,
        },
      };

      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.orderUpdate?.userErrors?.length > 0) {
        const errors = response.data.orderUpdate.userErrors;
        return {
          success: false,
          error: errors.map((e) => e.message).join(", "),
        };
      }

      return {
        success: true,
        order: response.data?.orderUpdate?.order,
        metafieldsUpdated: metafields.length,
      };
    } catch (error) {
      console.error("Set order metafields error:", error);
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Verify metafield definitions exist
  async verifyMetafieldSetup() {
    try {
      const customerDefs = await this.getMetafieldDefinitions(
        "customer",
        "dukalink_loyalty"
      );
      const orderDefs = await this.getMetafieldDefinitions(
        "order",
        "dukalink_loyalty"
      );
      const discountDefs = await this.getMetafieldDefinitions(
        "order",
        "dukalink_discounts"
      );
      const posDefs = await this.getMetafieldDefinitions(
        "order",
        "dukalink_pos"
      );

      const expectedCustomerFields = Object.keys(
        this.metafieldDefinitions.customer
      );
      const expectedOrderFields = Object.keys(this.metafieldDefinitions.order);

      const existingCustomerFields = customerDefs.success
        ? customerDefs.definitions.map((def) => def.key)
        : [];
      const existingOrderFields = [
        ...(orderDefs.success
          ? orderDefs.definitions.map((def) => def.key)
          : []),
        ...(discountDefs.success
          ? discountDefs.definitions.map((def) => def.key)
          : []),
        ...(posDefs.success ? posDefs.definitions.map((def) => def.key) : []),
      ];

      const missingCustomerFields = expectedCustomerFields.filter(
        (field) =>
          !existingCustomerFields.includes(
            this.metafieldDefinitions.customer[field].key
          )
      );
      const missingOrderFields = expectedOrderFields.filter(
        (field) =>
          !existingOrderFields.includes(
            this.metafieldDefinitions.order[field].key
          )
      );

      return {
        success: true,
        verification: {
          customerFields: {
            expected: expectedCustomerFields.length,
            existing: existingCustomerFields.length,
            missing: missingCustomerFields,
          },
          orderFields: {
            expected: expectedOrderFields.length,
            existing: existingOrderFields.length,
            missing: missingOrderFields,
          },
          isComplete:
            missingCustomerFields.length === 0 &&
            missingOrderFields.length === 0,
        },
      };
    } catch (error) {
      console.error("Verify metafield setup error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = new ShopifyMetafieldsService();
