/**
 * Final Database Fix for User Switching and Ticketing
 */

require("dotenv").config();
const mysql = require("mysql2/promise");

async function finalDatabaseFix() {
  let connection;
  
  try {
    console.log("🔗 Connecting to MySQL database...");
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "dukalink",
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || "dukalink_shopify_pos",
      charset: "utf8mb4",
    });

    console.log("✅ Connected to MySQL database");

    // Check current pos_staff structure
    console.log("🔍 Checking current pos_staff structure...");
    const [staffCols] = await connection.execute("DESCRIBE pos_staff");
    const staffColumnNames = staffCols.map(col => col.Field);
    console.log("Current pos_staff columns:", staffColumnNames.join(", "));

    // Fix PIN column size first
    console.log("🔧 Fixing PIN column size...");
    try {
      await connection.execute("ALTER TABLE pos_staff MODIFY COLUMN pin TEXT NULL");
      console.log("✅ PIN column size fixed");
    } catch (error) {
      console.log("⚠️ PIN column already correct size or error:", error.message);
    }

    // Add missing columns to pos_staff table
    console.log("🔧 Adding missing columns to pos_staff table...");
    
    const requiredColumns = [
      { name: "pin_set_at", definition: "DATETIME NULL" },
      { name: "pin_set_by", definition: "VARCHAR(255) NULL" },
      { name: "last_pin_used", definition: "DATETIME NULL" }
    ];

    for (const col of requiredColumns) {
      if (!staffColumnNames.includes(col.name)) {
        try {
          await connection.execute(`ALTER TABLE pos_staff ADD COLUMN ${col.name} ${col.definition}`);
          console.log(`   ✅ Added: ${col.name}`);
        } catch (error) {
          console.error(`   ❌ Failed to add ${col.name}:`, error.message);
        }
      } else {
        console.log(`   ⚠️ Column already exists: ${col.name}`);
      }
    }

    // Create pos_security_events table
    console.log("🔧 Creating pos_security_events table...");
    try {
      await connection.execute(`DROP TABLE IF EXISTS pos_security_events`);
      await connection.execute(`
        CREATE TABLE pos_security_events (
          id VARCHAR(255) PRIMARY KEY,
          staff_id VARCHAR(255),
          event_type VARCHAR(100) NOT NULL,
          event_data JSON,
          ip_address VARCHAR(45),
          user_agent TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          
          INDEX idx_security_events_staff (staff_id),
          INDEX idx_security_events_type (event_type),
          INDEX idx_security_events_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      console.log("✅ pos_security_events table created");
    } catch (error) {
      console.error("❌ Failed to create pos_security_events table:", error.message);
    }

    // Verify the changes
    console.log("\n🔍 Verifying database structure...");
    
    // Check pos_staff columns again
    const [newStaffCols] = await connection.execute("DESCRIBE pos_staff");
    const newStaffColumnNames = newStaffCols.map(col => col.Field);
    console.log("Updated pos_staff columns:", newStaffColumnNames.join(", "));
    
    // Check if pos_security_events exists
    const [tables] = await connection.execute("SHOW TABLES LIKE 'pos_security_events'");
    console.log(`pos_security_events table exists: ${tables.length > 0}`);

    // Check if all required columns exist
    const missingColumns = requiredColumns.filter(col => !newStaffColumnNames.includes(col.name));
    if (missingColumns.length === 0) {
      console.log("✅ All required columns are present");
    } else {
      console.log("❌ Missing columns:", missingColumns.map(c => c.name).join(", "));
    }

    console.log("\n✅ Final database fix completed successfully!");

  } catch (error) {
    console.error("❌ Final database fix failed:", error);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

finalDatabaseFix();
