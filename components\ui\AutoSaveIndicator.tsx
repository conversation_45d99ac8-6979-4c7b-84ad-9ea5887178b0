/**
 * Auto-Save Indicator Component
 * 
 * Displays real-time auto-save status with visual feedback.
 * Shows save progress, offline queue status, and error states.
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/src/contexts/ThemeContext';
import { AutoSaveStats } from '@/src/services/AutoSaveService';

interface AutoSaveIndicatorProps {
  isAutoSaving: boolean;
  autoSaveEnabled: boolean;
  stats: AutoSaveStats;
  isOnline: boolean;
  error: string | null;
  onToggleAutoSave: () => void;
  onForceAutoSave: () => void;
  onClearError: () => void;
  compact?: boolean;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  isAutoSaving,
  autoSaveEnabled,
  stats,
  isOnline,
  error,
  onToggleAutoSave,
  onForceAutoSave,
  onClearError,
  compact = false,
}) => {
  const { theme } = useTheme();
  const [showDetails, setShowDetails] = useState(false);
  const [pulseAnim] = useState(new Animated.Value(1));

  // Pulse animation for active saving
  React.useEffect(() => {
    if (isAutoSaving) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 0.7,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    }
  }, [isAutoSaving, pulseAnim]);

  const getStatusIcon = () => {
    if (error) return 'alert-circle';
    if (isAutoSaving) return 'sync';
    if (!isOnline) return 'cloud-offline';
    if (!autoSaveEnabled) return 'pause-circle';
    if (stats.offlineQueueSize > 0) return 'cloud-upload';
    return 'checkmark-circle';
  };

  const getStatusColor = () => {
    if (error) return theme.colors.error;
    if (isAutoSaving) return theme.colors.primary;
    if (!isOnline) return theme.colors.warning;
    if (!autoSaveEnabled) return theme.colors.textSecondary;
    if (stats.offlineQueueSize > 0) return theme.colors.warning;
    return theme.colors.success;
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isAutoSaving) return 'Saving...';
    if (!isOnline) return 'Offline';
    if (!autoSaveEnabled) return 'Paused';
    if (stats.offlineQueueSize > 0) return `Queue: ${stats.offlineQueueSize}`;
    if (stats.lastSaveTime) {
      const timeSince = Date.now() - stats.lastSaveTime.getTime();
      const minutes = Math.floor(timeSince / (1000 * 60));
      if (minutes < 1) return 'Just saved';
      if (minutes < 60) return `${minutes}m ago`;
      return 'Saved';
    }
    return 'Ready';
  };

  const formatLastSaveTime = () => {
    if (!stats.lastSaveTime) return 'Never';
    return stats.lastSaveTime.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getSuccessRate = () => {
    if (stats.totalSaves === 0) return 100;
    return Math.round((stats.successfulSaves / stats.totalSaves) * 100);
  };

  if (compact) {
    return (
      <TouchableOpacity 
        style={[styles.compactContainer, { backgroundColor: theme.colors.surface }]}
        onPress={() => setShowDetails(true)}
      >
        <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
          <Ionicons 
            name={getStatusIcon()} 
            size={16} 
            color={getStatusColor()} 
          />
        </Animated.View>
        {stats.offlineQueueSize > 0 && (
          <View style={[styles.badge, { backgroundColor: theme.colors.warning }]}>
            <Text style={[styles.badgeText, { color: theme.colors.onWarning }]}>
              {stats.offlineQueueSize}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <>
      <TouchableOpacity 
        style={[styles.container, { backgroundColor: theme.colors.surface }]}
        onPress={() => setShowDetails(true)}
      >
        <View style={styles.statusRow}>
          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <Ionicons 
              name={getStatusIcon()} 
              size={20} 
              color={getStatusColor()} 
            />
          </Animated.View>
          <Text style={[styles.statusText, { color: theme.colors.text }]}>
            {getStatusText()}
          </Text>
          {stats.offlineQueueSize > 0 && (
            <View style={[styles.badge, { backgroundColor: theme.colors.warning }]}>
              <Text style={[styles.badgeText, { color: theme.colors.onWarning }]}>
                {stats.offlineQueueSize}
              </Text>
            </View>
          )}
        </View>
        
        {error && (
          <TouchableOpacity onPress={onClearError} style={styles.errorRow}>
            <Text style={[styles.errorText, { color: theme.colors.error }]} numberOfLines={1}>
              {error}
            </Text>
            <Ionicons name="close" size={16} color={theme.colors.error} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>

      {/* Details Modal */}
      <Modal
        visible={showDetails}
        animationType="fade"
        transparent
        onRequestClose={() => setShowDetails(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowDetails(false)}
        >
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
                Auto-Save Status
              </Text>
              <TouchableOpacity onPress={() => setShowDetails(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Status
                </Text>
                <View style={styles.statValue}>
                  <Ionicons 
                    name={getStatusIcon()} 
                    size={16} 
                    color={getStatusColor()} 
                  />
                  <Text style={[styles.statText, { color: theme.colors.text }]}>
                    {getStatusText()}
                  </Text>
                </View>
              </View>

              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Last Save
                </Text>
                <Text style={[styles.statText, { color: theme.colors.text }]}>
                  {formatLastSaveTime()}
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Total Saves
                </Text>
                <Text style={[styles.statText, { color: theme.colors.text }]}>
                  {stats.totalSaves}
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Success Rate
                </Text>
                <Text style={[styles.statText, { color: theme.colors.text }]}>
                  {getSuccessRate()}%
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Offline Queue
                </Text>
                <Text style={[styles.statText, { color: theme.colors.text }]}>
                  {stats.offlineQueueSize} items
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Avg Save Time
                </Text>
                <Text style={[styles.statText, { color: theme.colors.text }]}>
                  {Math.round(stats.averageSaveTime)}ms
                </Text>
              </View>
            </View>

            {error && (
              <View style={[styles.errorSection, { backgroundColor: theme.colors.errorSurface }]}>
                <Ionicons name="alert-circle" size={20} color={theme.colors.error} />
                <Text style={[styles.errorDetailText, { color: theme.colors.error }]}>
                  {error}
                </Text>
              </View>
            )}

            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={[
                  styles.actionButton, 
                  { backgroundColor: autoSaveEnabled ? theme.colors.warning : theme.colors.success }
                ]}
                onPress={onToggleAutoSave}
              >
                <Ionicons 
                  name={autoSaveEnabled ? "pause" : "play"} 
                  size={16} 
                  color={theme.colors.onPrimary} 
                />
                <Text style={[styles.actionButtonText, { color: theme.colors.onPrimary }]}>
                  {autoSaveEnabled ? 'Pause' : 'Resume'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                onPress={onForceAutoSave}
                disabled={isAutoSaving}
              >
                <Ionicons 
                  name="save" 
                  size={16} 
                  color={theme.colors.onPrimary} 
                />
                <Text style={[styles.actionButtonText, { color: theme.colors.onPrimary }]}>
                  Save Now
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    position: 'relative',
  },
  container: {
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  badge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  errorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 0, 0, 0.1)',
  },
  errorText: {
    fontSize: 12,
    flex: 1,
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 12,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  statsGrid: {
    gap: 16,
    marginBottom: 20,
  },
  statItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
  },
  statValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorDetailText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default AutoSaveIndicator;
