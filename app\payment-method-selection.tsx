import { useThemeColor } from "@/hooks/useThemeColor";
import { PaymentService } from "@/src/services/payment-service";
import { PaymentMethod } from "@/src/types/payment";
import { Ionicons } from "@expo/vector-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const PaymentMethodSelectionScreen: React.FC = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(
    null
  );

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const infoColor = useThemeColor({}, "info");
  const errorColor = useThemeColor({}, "error");

  // Get cart total from params
  const cartTotal = parseFloat(params.total as string) || 0;
  const customerName = (params.customerName as string) || "Walk-in Customer";

  const availableMethods = PaymentService.getAvailablePaymentMethods();

  const handleMethodSelect = (method: PaymentMethod) => {
    if (!method.enabled && method.placeholder) {
      Alert.alert(
        "Coming Soon",
        `${method.name} integration is coming soon! For now, please use cash payment.`,
        [{ text: "OK" }]
      );
      return;
    }

    setSelectedMethod(method);

    // Navigate to payment processing screen
    router.push({
      pathname: "/payment-processing",
      params: {
        methodId: method.id,
        total: cartTotal.toString(),
        customerName,
      },
    });
  };

  const renderPaymentMethod = (method: PaymentMethod) => (
    <TouchableOpacity
      key={method.id}
      style={[
        styles.methodCard,
        !method.enabled && method.placeholder && styles.methodCardDisabled,
        selectedMethod?.id === method.id && styles.methodCardSelected,
      ]}
      onPress={() => handleMethodSelect(method)}
      disabled={!method.enabled && method.placeholder}
    >
      <View style={styles.methodHeader}>
        <View style={styles.methodIcon}>
          <Text style={styles.methodIconText}>{method.icon}</Text>
        </View>
        <View style={styles.methodInfo}>
          <View style={styles.methodTitleRow}>
            <Text
              style={[
                styles.methodName,
                !method.enabled &&
                  method.placeholder &&
                  styles.methodNameDisabled,
              ]}
            >
              {method.name}
            </Text>
            {method.comingSoon && (
              <View style={styles.comingSoonBadge}>
                <Text style={styles.comingSoonText}>Coming Soon</Text>
              </View>
            )}
          </View>
          <Text
            style={[
              styles.methodDescription,
              !method.enabled &&
                method.placeholder &&
                styles.methodDescriptionDisabled,
            ]}
          >
            {method.description}
          </Text>
        </View>
        <View style={styles.methodAction}>
          {method.enabled ? (
            <Ionicons name="chevron-forward" size={24} color="#2ecc71" />
          ) : (
            <Ionicons name="lock-closed" size={24} color="#bdc3c7" />
          )}
        </View>
      </View>

      {method.enabled && (
        <View style={styles.methodFeatures}>
          <View style={styles.feature}>
            <Ionicons name="flash" size={16} color="#2ecc71" />
            <Text style={styles.featureText}>Instant Processing</Text>
          </View>
          {method.type === "cash" && (
            <View style={styles.feature}>
              <Ionicons name="shield-checkmark" size={16} color="#2ecc71" />
              <Text style={styles.featureText}>No Transaction Fees</Text>
            </View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary,
    primaryColor,
    infoColor,
    errorColor,
    useThemeColor({}, "success")
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={textColor} />
        </TouchableOpacity>
        <Text style={styles.title}>Payment Method</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.orderSummary}>
        <Text style={styles.summaryTitle}>Order Summary</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Customer:</Text>
          <Text style={styles.summaryValue}>{customerName}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Amount:</Text>
          <Text style={styles.summaryTotal}>
            {PaymentService.formatCurrency(cartTotal)}
          </Text>
        </View>
      </View>

      <ScrollView
        style={styles.methodsList}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.sectionTitle}>Select Payment Method</Text>

        {availableMethods.map(renderPaymentMethod)}

        <View style={styles.infoCard}>
          <Ionicons name="information-circle" size={20} color="#3498db" />
          <Text style={styles.infoText}>
            ABSA Till and Credit payments are now available! Card payments will
            be available soon.
          </Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => router.back()}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string,
  primaryColor: string,
  infoColor: string,
  errorColor: string,
  successColor: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: 16,
      backgroundColor: surfaceColor,
      borderBottomWidth: 1,
      borderBottomColor: textSecondary + "40",
    },
    backButton: {
      padding: 8,
    },
    title: {
      fontSize: 18,
      fontWeight: "bold",
      color: textColor,
    },
    placeholder: {
      width: 40,
    },
    orderSummary: {
      backgroundColor: surfaceColor,
      padding: 16,
      marginHorizontal: 16,
      marginVertical: 12,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: textSecondary + "40",
    },
    summaryTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: textColor,
      marginBottom: 12,
    },
    summaryRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 8,
    },
    summaryLabel: {
      fontSize: 16,
      color: textSecondary,
    },
    summaryValue: {
      fontSize: 16,
      color: textColor,
      fontWeight: "500",
    },
    summaryTotal: {
      fontSize: 18,
      color: successColor,
      fontWeight: "700",
    },
    methodsList: {
      flex: 1,
      paddingHorizontal: 16,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: textColor,
      marginBottom: 16,
    },
    methodCard: {
      backgroundColor: surfaceColor,
      borderRadius: 12,
      padding: 14,
      marginBottom: 10,
      borderWidth: 1,
      borderColor: textSecondary + "40",
    },
    methodCardDisabled: {
      backgroundColor: backgroundColor,
      opacity: 0.7,
    },
    methodCardSelected: {
      borderColor: primaryColor,
      backgroundColor: primaryColor + "20",
    },
    methodHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    methodIcon: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: primaryColor + "20",
      alignItems: "center",
      justifyContent: "center",
      marginRight: 12,
    },
    methodIconText: {
      fontSize: 20,
    },
    methodInfo: {
      flex: 1,
    },
    methodTitleRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    methodName: {
      fontSize: 16,
      fontWeight: "600",
      color: textColor,
    },
    methodNameDisabled: {
      color: textSecondary,
    },
    comingSoonBadge: {
      backgroundColor: infoColor,
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 10,
      marginLeft: 8,
    },
    comingSoonText: {
      color: "#fff",
      fontSize: 9,
      fontWeight: "600",
      textTransform: "uppercase",
    },
    methodDescription: {
      fontSize: 14,
      color: textSecondary,
    },
    methodDescriptionDisabled: {
      color: textSecondary,
    },
    methodAction: {
      marginLeft: 12,
    },
    methodFeatures: {
      flexDirection: "row",
      marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: "#e1e8ed",
    },
    feature: {
      flexDirection: "row",
      alignItems: "center",
      marginRight: 16,
    },
    featureText: {
      fontSize: 11,
      color: successColor,
      marginLeft: 4,
      fontWeight: "500",
    },
    infoCard: {
      flexDirection: "row",
      backgroundColor: infoColor + "20",
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: infoColor,
      marginTop: 16,
      marginBottom: 20,
    },
    infoText: {
      flex: 1,
      fontSize: 14,
      color: infoColor,
      marginLeft: 8,
      lineHeight: 20,
    },
    footer: {
      padding: 16,
      backgroundColor: surfaceColor,
      borderTopWidth: 1,
      borderTopColor: textSecondary + "40",
    },
    cancelButton: {
      backgroundColor: errorColor,
      borderRadius: 8,
      padding: 16,
      alignItems: "center",
    },
    cancelButtonText: {
      color: "#fff",
      fontSize: 16,
      fontWeight: "600",
    },
  });

export default PaymentMethodSelectionScreen;
