// Test POS authentication
const axios = require("axios");

async function testPOSAuth() {
  console.log("🧪 Testing POS Authentication...\n");

  const baseURL = "http://192.168.1.8:3020/api";

  try {
    // Test POS login with cashier credentials
    console.log("1️⃣ Testing POS login (cashier)...");
    const loginResponse = await axios.post(`${baseURL}/pos/login`, {
      username: "cashier1",
      password: "password123",
    });

    if (loginResponse.data.success) {
      console.log("✅ Cashier login successful!");
      console.log(`   User: ${loginResponse.data.data.user.name}`);
      console.log(`   Role: ${loginResponse.data.data.user.role}`);
      console.log(`   Store: ${loginResponse.data.data.user.storeId}`);
      console.log(
        `   Permissions: ${loginResponse.data.data.user.permissions.join(", ")}`
      );

      const token = loginResponse.data.data.token;

      // Test token verification
      console.log("\n2️⃣ Testing token verification...");
      const verifyResponse = await axios.get(`${baseURL}/pos/verify`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (verifyResponse.data.success) {
        console.log("✅ Token verification successful!");
        console.log(`   Verified user: ${verifyResponse.data.data.user.name}`);
      } else {
        console.log("❌ Token verification failed");
      }
    } else {
      console.log("❌ Login failed:", loginResponse.data.error);
    }

    // Test manager login
    console.log("\n3️⃣ Testing POS login (manager)...");
    const managerResponse = await axios.post(`${baseURL}/pos/login`, {
      username: "manager1",
      password: "manager123",
    });

    if (managerResponse.data.success) {
      console.log("✅ Manager login successful!");
      console.log(`   User: ${managerResponse.data.data.user.name}`);
      console.log(`   Role: ${managerResponse.data.data.user.role}`);
      console.log(
        `   Permissions: ${managerResponse.data.data.user.permissions.join(
          ", "
        )}`
      );
    } else {
      console.log("❌ Manager login failed:", managerResponse.data.error);
    }

    // Test invalid credentials
    console.log("\n4️⃣ Testing invalid credentials...");
    try {
      await axios.post(`${baseURL}/pos/login`, {
        username: "invalid",
        password: "wrong",
      });
      console.log("❌ Should have failed with invalid credentials");
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log("✅ Invalid credentials correctly rejected");
      } else {
        console.log("❌ Unexpected error:", error.message);
      }
    }
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("Response:", error.response.data);
    }
  }

  console.log("\n🎯 POS Authentication Summary:");
  console.log("   - Cashier can login with: cashier1 / password123");
  console.log("   - Manager can login with: manager1 / manager123");
  console.log("   - Tokens are properly validated");
  console.log("   - Invalid credentials are rejected");
  console.log("\n✅ POS Authentication is ready for mobile app!");
}

testPOSAuth();
