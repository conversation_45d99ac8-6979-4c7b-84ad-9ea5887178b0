/**
 * Payment Processing API Routes
 *
 * Provides RESTful API endpoints for payment processing:
 * - Payment transaction initiation
 * - Payment method processing
 * - Split payment management
 * - Transaction status tracking
 * - Payment completion and validation
 */

const express = require("express");
const router = express.Router();
const PaymentTransactionService = require("../services/payment-transaction-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const { v4: uuidv4 } = require("uuid");

const paymentService = new PaymentTransactionService();

/**
 * POST /api/payments/initiate
 * Initiate a new payment transaction
 */
router.post("/initiate", authenticateToken, async (req, res) => {
  try {
    const {
      orderId,
      shopifyOrderId,
      customerId,
      terminalId,
      locationId,
      totalAmount,
      currency,
      paymentMethods,
      notes,
      metadata,
    } = req.body;

    // Validate required fields
    if (!totalAmount || totalAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: "Valid total amount is required",
      });
    }

    const transactionData = {
      orderId,
      shopifyOrderId,
      staffId: req.user.id,
      customerId,
      terminalId,
      locationId,
      totalAmount: parseFloat(totalAmount),
      currency: currency || "KES",
      paymentMethods: paymentMethods || [],
      notes,
      metadata: {
        ...metadata,
        initiatedBy: req.user.name,
        userAgent: req.get("User-Agent"),
        ipAddress: req.ip,
      },
    };

    const result = await paymentService.initiateTransaction(transactionData);

    if (result.success) {
      res.json({
        success: true,
        data: {
          transactionId: result.transactionId,
          status: result.status,
          totalAmount: result.totalAmount,
          isSplitPayment: result.isSplitPayment,
          remainingAmount: result.remainingAmount,
          paymentMethods: result.paymentMethods,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Payment initiation error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to initiate payment transaction",
    });
  }
});

/**
 * POST /api/payments/:transactionId/methods
 * Add a payment method to an existing transaction
 */
router.post("/:transactionId/methods", authenticateToken, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const {
      methodType,
      methodName,
      amount,
      amountTendered,
      referenceCode,
      transactionCode,
      metadata,
    } = req.body;

    // Validate required fields
    if (!methodType || !amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: "Method type and valid amount are required",
      });
    }

    const methodData = {
      methodType,
      methodName,
      amount: parseFloat(amount),
      amountTendered: amountTendered ? parseFloat(amountTendered) : null,
      referenceCode,
      transactionCode,
      metadata: {
        ...metadata,
        addedBy: req.user.name,
        userAgent: req.get("User-Agent"),
        ipAddress: req.ip,
      },
    };

    const result = await paymentService.addPaymentMethod(
      transactionId,
      methodData,
      req.user.id
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          methodId: result.methodId,
          status: result.status,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Add payment method error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to add payment method",
    });
  }
});

/**
 * POST /api/payments/methods/:methodId/process
 * Process a specific payment method
 */
router.post(
  "/methods/:methodId/process",
  authenticateToken,
  async (req, res) => {
    try {
      const { methodId } = req.params;
      const processingData = {
        ...req.body,
        processedBy: req.user.name,
        userAgent: req.get("User-Agent"),
        ipAddress: req.ip,
      };

      const result = await paymentService.processPaymentMethod(
        methodId,
        req.user.id,
        processingData
      );

      if (result.success) {
        // Log payment processing asynchronously after successful transaction
        if (result.transactionId) {
          paymentService.logPaymentAction(
            result.transactionId,
            methodId,
            req.user.id,
            "method_processed",
            result.oldStatus,
            result.status,
            result.amount,
            {
              ...processingData,
              processedBy: req.user.name,
              userAgent: req.get("User-Agent"),
              ipAddress: req.ip,
            }
          );
        }

        res.json({
          success: true,
          data: {
            status: result.status,
            metadata: result.metadata,
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Process payment method error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to process payment method",
      });
    }
  }
);

/**
 * GET /api/payments/:transactionId
 * Get transaction details with payment methods
 */
router.get("/:transactionId", authenticateToken, async (req, res) => {
  try {
    const { transactionId } = req.params;

    const result = await paymentService.getTransactionDetails(transactionId);

    if (result.success) {
      res.json({
        success: true,
        data: {
          transaction: result.transaction,
          paymentMethods: result.paymentMethods,
          creditPayments: result.creditPayments,
        },
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get transaction details error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get transaction details",
    });
  }
});

/**
 * GET /api/payments/:transactionId/status
 * Get transaction status and summary
 */
router.get("/:transactionId/status", authenticateToken, async (req, res) => {
  try {
    const { transactionId } = req.params;

    const result = await paymentService.getTransactionDetails(transactionId);

    if (result.success) {
      const transaction = result.transaction;
      const paymentMethods = result.paymentMethods;

      // Calculate summary
      const completedMethods = paymentMethods.filter(
        (m) => m.status === "completed"
      );
      const failedMethods = paymentMethods.filter((m) => m.status === "failed");
      const pendingMethods = paymentMethods.filter(
        (m) => m.status === "pending"
      );
      const processingMethods = paymentMethods.filter(
        (m) => m.status === "processing"
      );

      const completedAmount = completedMethods.reduce(
        (sum, m) => sum + parseFloat(m.amount),
        0
      );
      const remainingAmount = Math.max(
        0,
        parseFloat(transaction.total_amount) - completedAmount
      );

      res.json({
        success: true,
        data: {
          transactionId: transaction.id,
          status: transaction.payment_status,
          totalAmount: parseFloat(transaction.total_amount),
          completedAmount,
          remainingAmount,
          isSplitPayment: transaction.is_split_payment,
          summary: {
            totalMethods: paymentMethods.length,
            completed: completedMethods.length,
            failed: failedMethods.length,
            pending: pendingMethods.length,
            processing: processingMethods.length,
          },
          paymentMethods: paymentMethods.map((method) => ({
            id: method.id,
            type: method.method_type,
            name: method.method_name,
            amount: parseFloat(method.amount),
            status: method.status,
            processedAt: method.processed_at,
            errorMessage: method.error_message,
          })),
        },
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get transaction status error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get transaction status",
    });
  }
});

/**
 * POST /api/payments/:transactionId/cancel
 * Cancel a payment transaction
 */
router.post("/:transactionId/cancel", authenticateToken, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { reason } = req.body;

    const result = await paymentService.cancelTransaction(
      transactionId,
      req.user.id,
      reason
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          status: result.status,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Cancel transaction error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to cancel transaction",
    });
  }
});

/**
 * POST /api/payments/:transactionId/validate-split
 * Validate split payment amounts before processing
 */
router.post(
  "/:transactionId/validate-split",
  authenticateToken,
  async (req, res) => {
    try {
      const { transactionId } = req.params;
      const { paymentMethods } = req.body;

      // Get transaction details to get total amount
      const transactionResult = await paymentService.getTransactionDetails(
        transactionId
      );
      if (!transactionResult.success) {
        return res.status(404).json({
          success: false,
          error: "Transaction not found",
        });
      }

      const totalAmount = parseFloat(
        transactionResult.transaction.total_amount
      );
      const result = await paymentService.validateSplitPayment(
        totalAmount,
        paymentMethods
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            isValid: result.isValid,
            totalAmount: result.totalAmount,
            methodCount: result.methodCount,
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Validate split payment error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to validate split payment",
      });
    }
  }
);

/**
 * GET /api/payments/:transactionId/balance
 * Get remaining balance for split payment
 */
router.get("/:transactionId/balance", authenticateToken, async (req, res) => {
  try {
    const { transactionId } = req.params;

    const result = await paymentService.calculateRemainingBalance(
      transactionId
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          totalAmount: result.totalAmount,
          completedAmount: result.completedAmount,
          pendingAmount: result.pendingAmount,
          remainingAmount: result.remainingAmount,
          isFullyPaid: result.isFullyPaid,
          isPartiallyPaid: result.isPartiallyPaid,
        },
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get payment balance error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get payment balance",
    });
  }
});

/**
 * GET /api/payments/:transactionId/split-summary
 * Get comprehensive split payment summary
 */
router.get(
  "/:transactionId/split-summary",
  authenticateToken,
  async (req, res) => {
    try {
      const { transactionId } = req.params;

      const result = await paymentService.getSplitPaymentSummary(transactionId);

      if (result.success) {
        res.json({
          success: true,
          data: result,
        });
      } else {
        res.status(404).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Get split payment summary error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get split payment summary",
      });
    }
  }
);

/**
 * POST /api/payments/:transactionId/complete-with-shopify
 * Complete transaction and create Shopify order with payment details
 */
router.post(
  "/:transactionId/complete-with-shopify",
  authenticateToken,
  async (req, res) => {
    try {
      const { transactionId } = req.params;
      const { orderData } = req.body;

      if (!orderData) {
        return res.status(400).json({
          success: false,
          error: "Order data is required",
        });
      }

      const result = await paymentService.completeTransactionWithShopify(
        transactionId,
        orderData
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            shopifyOrderId: result.shopifyOrderId,
            orderNumber: result.orderNumber,
            paymentsCreated: result.paymentsCreated,
            financialStatus: result.financialStatus,
            paymentDetails: result.paymentDetails,
            loyaltyResult: result.loyaltyResult, // Include loyalty result in API response
            message: result.message,
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Complete transaction with Shopify error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to complete transaction with Shopify",
      });
    }
  }
);

/**
 * GET /api/payments/summary
 * Get payment transaction summary for reporting
 */
router.get("/summary", authenticateToken, async (req, res) => {
  try {
    const { staffId, dateFrom, dateTo, status } = req.query;

    const filters = {};
    if (staffId) filters.staffId = staffId;
    if (dateFrom) filters.dateFrom = dateFrom;
    if (dateTo) filters.dateTo = dateTo;
    if (status) filters.status = status;

    const result = await paymentService.getTransactionSummary(filters);

    if (result.success) {
      res.json({
        success: true,
        data: {
          summary: result.summary,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get payment summary error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get payment summary",
    });
  }
});

module.exports = router;
