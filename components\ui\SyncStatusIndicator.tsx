/**
 * Sync Status Indicator
 * 
 * Shows real-time synchronization status with visual indicators,
 * conflict notifications, and manual sync controls.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { IconSymbol } from './IconSymbol';
import { ModernButton } from './ModernButton';
import { useTheme } from '@/src/contexts/ThemeContext';
import { 
  ticketSyncService,
  SyncResult,
  SyncConflict,
} from '@/src/services/TicketSyncService';
import { ConflictResolutionModal } from './ConflictResolutionModal';

interface SyncStatusIndicatorProps {
  position?: 'top' | 'bottom' | 'floating';
  showDetails?: boolean;
  autoSync?: boolean;
  onSyncComplete?: (result: SyncResult) => void;
}

export const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  position = 'top',
  showDetails = false,
  autoSync = true,
  onSyncComplete,
}) => {
  const theme = useTheme();
  const [syncStatus, setSyncStatus] = useState(ticketSyncService.getSyncStatus());
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [conflicts, setConflicts] = useState<SyncConflict[]>([]);
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [isManualSync, setIsManualSync] = useState(false);

  const styles = createStyles(theme, position);

  // Update sync status periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setSyncStatus(ticketSyncService.getSyncStatus());
      setConflicts(ticketSyncService.getPendingConflicts());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Auto-sync setup
  useEffect(() => {
    if (autoSync) {
      ticketSyncService.startAutoSync();
    } else {
      ticketSyncService.stopAutoSync();
    }

    return () => {
      if (autoSync) {
        ticketSyncService.stopAutoSync();
      }
    };
  }, [autoSync]);

  const handleManualSync = async () => {
    setIsManualSync(true);
    try {
      const result = await ticketSyncService.performSync(true);
      setLastSyncResult(result);
      
      if (result.conflicts.length > 0) {
        setConflicts(result.conflicts);
        setShowConflictModal(true);
      }
      
      onSyncComplete?.(result);
      
      if (result.success) {
        Alert.alert(
          'Sync Complete',
          `Successfully synced ${result.syncedTickets} ticket${result.syncedTickets === 1 ? '' : 's'}.`
        );
      } else {
        Alert.alert('Sync Failed', result.errors.join('\n'));
      }
    } catch (error) {
      Alert.alert('Sync Error', 'An unexpected error occurred during sync.');
    } finally {
      setIsManualSync(false);
    }
  };

  const handleResolveConflict = async (
    conflictId: string,
    resolution: 'local' | 'server' | 'merge'
  ): Promise<boolean> => {
    return await ticketSyncService.resolveConflict(conflictId, resolution);
  };

  const getSyncStatusIcon = () => {
    if (syncStatus.syncInProgress || isManualSync) {
      return 'arrow.triangle.2.circlepath';
    }
    if (!syncStatus.isOnline) {
      return 'wifi.slash';
    }
    if (conflicts.length > 0) {
      return 'exclamationmark.triangle.fill';
    }
    if (lastSyncResult?.success === false) {
      return 'xmark.circle.fill';
    }
    return 'checkmark.circle.fill';
  };

  const getSyncStatusColor = () => {
    if (syncStatus.syncInProgress || isManualSync) {
      return theme.colors.primary;
    }
    if (!syncStatus.isOnline) {
      return theme.colors.textSecondary;
    }
    if (conflicts.length > 0) {
      return theme.colors.warning;
    }
    if (lastSyncResult?.success === false) {
      return theme.colors.error;
    }
    return theme.colors.success;
  };

  const getSyncStatusText = () => {
    if (syncStatus.syncInProgress || isManualSync) {
      return 'Syncing...';
    }
    if (!syncStatus.isOnline) {
      return 'Offline';
    }
    if (conflicts.length > 0) {
      return `${conflicts.length} conflict${conflicts.length === 1 ? '' : 's'}`;
    }
    if (lastSyncResult?.success === false) {
      return 'Sync failed';
    }
    if (syncStatus.lastSyncAttempt) {
      const timeSince = Date.now() - syncStatus.lastSyncAttempt.getTime();
      const minutes = Math.floor(timeSince / 60000);
      if (minutes < 1) {
        return 'Just synced';
      } else if (minutes < 60) {
        return `${minutes}m ago`;
      } else {
        const hours = Math.floor(minutes / 60);
        return `${hours}h ago`;
      }
    }
    return 'Ready to sync';
  };

  const renderCompactIndicator = () => (
    <TouchableOpacity
      style={[
        styles.compactContainer,
        { backgroundColor: getSyncStatusColor() + '20' },
      ]}
      onPress={showDetails ? undefined : handleManualSync}
      disabled={syncStatus.syncInProgress || isManualSync}
    >
      <View style={styles.compactContent}>
        {(syncStatus.syncInProgress || isManualSync) ? (
          <ActivityIndicator size="small" color={getSyncStatusColor()} />
        ) : (
          <IconSymbol 
            name={getSyncStatusIcon()} 
            size={16} 
            color={getSyncStatusColor()} 
          />
        )}
        
        <Text style={[styles.compactText, { color: getSyncStatusColor() }]}>
          {getSyncStatusText()}
        </Text>

        {conflicts.length > 0 && (
          <View style={[styles.conflictBadge, { backgroundColor: theme.colors.warning }]}>
            <Text style={[styles.conflictBadgeText, { color: theme.colors.background }]}>
              {conflicts.length}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderDetailedIndicator = () => (
    <View style={[styles.detailedContainer, { backgroundColor: theme.colors.backgroundSecondary }]}>
      {/* Header */}
      <View style={styles.detailedHeader}>
        <View style={styles.headerLeft}>
          {(syncStatus.syncInProgress || isManualSync) ? (
            <ActivityIndicator size="small" color={getSyncStatusColor()} />
          ) : (
            <IconSymbol 
              name={getSyncStatusIcon()} 
              size={20} 
              color={getSyncStatusColor()} 
            />
          )}
          <Text style={[styles.detailedTitle, { color: theme.colors.text }]}>
            Sync Status
          </Text>
        </View>
        
        <Text style={[styles.statusText, { color: getSyncStatusColor() }]}>
          {getSyncStatusText()}
        </Text>
      </View>

      {/* Status Details */}
      <View style={styles.statusDetails}>
        <View style={styles.statusRow}>
          <Text style={[styles.statusLabel, { color: theme.colors.textSecondary }]}>
            Connection:
          </Text>
          <Text style={[styles.statusValue, { color: theme.colors.text }]}>
            {syncStatus.isOnline ? 'Online' : 'Offline'}
          </Text>
        </View>
        
        <View style={styles.statusRow}>
          <Text style={[styles.statusLabel, { color: theme.colors.textSecondary }]}>
            Auto-sync:
          </Text>
          <Text style={[styles.statusValue, { color: theme.colors.text }]}>
            {syncStatus.autoSyncEnabled ? 'Enabled' : 'Disabled'}
          </Text>
        </View>
        
        {syncStatus.lastSyncAttempt && (
          <View style={styles.statusRow}>
            <Text style={[styles.statusLabel, { color: theme.colors.textSecondary }]}>
              Last sync:
            </Text>
            <Text style={[styles.statusValue, { color: theme.colors.text }]}>
              {syncStatus.lastSyncAttempt.toLocaleTimeString()}
            </Text>
          </View>
        )}
      </View>

      {/* Conflicts Alert */}
      {conflicts.length > 0 && (
        <TouchableOpacity
          style={[styles.conflictsAlert, { backgroundColor: theme.colors.warning + '20' }]}
          onPress={() => setShowConflictModal(true)}
        >
          <IconSymbol name="exclamationmark.triangle" size={16} color={theme.colors.warning} />
          <Text style={[styles.conflictsText, { color: theme.colors.warning }]}>
            {conflicts.length} conflict{conflicts.length === 1 ? '' : 's'} need resolution
          </Text>
          <IconSymbol name="chevron.right" size={16} color={theme.colors.warning} />
        </TouchableOpacity>
      )}

      {/* Actions */}
      <View style={styles.actions}>
        <ModernButton
          title="Sync Now"
          onPress={handleManualSync}
          loading={syncStatus.syncInProgress || isManualSync}
          disabled={syncStatus.syncInProgress || isManualSync || !syncStatus.isOnline}
          style={styles.syncButton}
        />
      </View>
    </View>
  );

  return (
    <>
      {showDetails ? renderDetailedIndicator() : renderCompactIndicator()}
      
      <ConflictResolutionModal
        visible={showConflictModal}
        conflicts={conflicts}
        onResolve={handleResolveConflict}
        onClose={() => setShowConflictModal(false)}
      />
    </>
  );
};

const createStyles = (theme: any, position: string) => {
  const baseStyles = StyleSheet.create({
    compactContainer: {
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
      marginHorizontal: theme.spacing.md,
    },
    compactContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.xs,
    },
    compactText: {
      fontSize: 12,
      fontWeight: '500',
    },
    conflictBadge: {
      borderRadius: 8,
      paddingHorizontal: 6,
      paddingVertical: 2,
      marginLeft: theme.spacing.xs,
    },
    conflictBadgeText: {
      fontSize: 10,
      fontWeight: '700',
    },
    detailedContainer: {
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      margin: theme.spacing.md,
    },
    detailedHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.md,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    detailedTitle: {
      fontSize: 16,
      fontWeight: '600',
    },
    statusText: {
      fontSize: 14,
      fontWeight: '500',
    },
    statusDetails: {
      gap: theme.spacing.xs,
      marginBottom: theme.spacing.md,
    },
    statusRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    statusLabel: {
      fontSize: 14,
    },
    statusValue: {
      fontSize: 14,
      fontWeight: '500',
    },
    conflictsAlert: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
    },
    conflictsText: {
      flex: 1,
      fontSize: 14,
      fontWeight: '500',
    },
    actions: {
      flexDirection: 'row',
    },
    syncButton: {
      flex: 1,
    },
  });

  // Position-specific styles
  if (position === 'floating') {
    return {
      ...baseStyles,
      compactContainer: {
        ...baseStyles.compactContainer,
        position: 'absolute',
        top: 50,
        right: theme.spacing.md,
        zIndex: 1000,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
      },
    };
  }

  return baseStyles;
};

export default SyncStatusIndicator;
