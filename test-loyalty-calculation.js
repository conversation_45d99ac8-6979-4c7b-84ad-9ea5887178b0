/**
 * Test script to verify loyalty points calculation
 * Run with: node test-loyalty-calculation.js
 */

// Mock the loyalty service configuration
const tierConfig = {
  bronze: {
    minPurchases: 0,
    minOrders: 0,
    multiplier: 1.0,
    pointsPerKsh: 0.01, // 1 point per 100 KSh
  },
  silver: {
    minPurchases: 500,
    minOrders: 5,
    multiplier: 1.2,
    pointsPerKsh: 0.01, // 1 point per 100 KSh
  },
  gold: {
    minPurchases: 2000,
    minOrders: 15,
    multiplier: 1.5,
    pointsPerKsh: 0.01, // 1 point per 100 KSh
  },
  platinum: {
    minPurchases: 5000,
    minOrders: 30,
    multiplier: 2.0,
    pointsPerKsh: 0.01, // 1 point per 100 KSh
  },
};

// Test function to calculate points
function calculateLoyaltyPoints(orderTotal, tier = 'bronze') {
  const config = tierConfig[tier];
  return Math.floor(orderTotal * config.pointsPerKsh);
}

// Test cases
const testCases = [
  { orderTotal: 100, tier: 'bronze', expected: 1 },
  { orderTotal: 200, tier: 'bronze', expected: 2 },
  { orderTotal: 150, tier: 'bronze', expected: 1 }, // Should round down
  { orderTotal: 1000, tier: 'bronze', expected: 10 },
  { orderTotal: 1000, tier: 'silver', expected: 10 },
  { orderTotal: 1000, tier: 'gold', expected: 10 },
  { orderTotal: 1000, tier: 'platinum', expected: 10 },
  { orderTotal: 50, tier: 'bronze', expected: 0 }, // Less than 100 KSh
  { orderTotal: 99, tier: 'bronze', expected: 0 }, // Just under 100 KSh
  { orderTotal: 250, tier: 'silver', expected: 2 }, // 250 * 0.01 = 2
];

console.log('🧪 Testing Loyalty Points Calculation');
console.log('=====================================');

let allTestsPassed = true;

testCases.forEach((testCase, index) => {
  const { orderTotal, tier, expected } = testCase;
  const actual = calculateLoyaltyPoints(orderTotal, tier);
  const passed = actual === expected;
  
  if (!passed) {
    allTestsPassed = false;
  }
  
  console.log(`Test ${index + 1}: ${passed ? '✅' : '❌'}`);
  console.log(`  Order Total: KSh ${orderTotal}`);
  console.log(`  Tier: ${tier}`);
  console.log(`  Expected: ${expected} points`);
  console.log(`  Actual: ${actual} points`);
  console.log('');
});

console.log('=====================================');
console.log(`Overall Result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

// Frontend calculation test (should match backend)
console.log('\n🖥️  Frontend Calculation Test');
console.log('==============================');

function frontendCalculation(orderTotal) {
  return Math.floor(orderTotal / 100);
}

const frontendTests = [
  { orderTotal: 100, expected: 1 },
  { orderTotal: 200, expected: 2 },
  { orderTotal: 150, expected: 1 },
  { orderTotal: 1000, expected: 10 },
  { orderTotal: 50, expected: 0 },
  { orderTotal: 99, expected: 0 },
];

let frontendTestsPassed = true;

frontendTests.forEach((testCase, index) => {
  const { orderTotal, expected } = testCase;
  const actual = frontendCalculation(orderTotal);
  const backendActual = calculateLoyaltyPoints(orderTotal, 'bronze');
  const passed = actual === expected && actual === backendActual;
  
  if (!passed) {
    frontendTestsPassed = false;
  }
  
  console.log(`Frontend Test ${index + 1}: ${passed ? '✅' : '❌'}`);
  console.log(`  Order Total: KSh ${orderTotal}`);
  console.log(`  Expected: ${expected} points`);
  console.log(`  Frontend: ${actual} points`);
  console.log(`  Backend: ${backendActual} points`);
  console.log(`  Match: ${actual === backendActual ? '✅' : '❌'}`);
  console.log('');
});

console.log('==============================');
console.log(`Frontend Result: ${frontendTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

if (allTestsPassed && frontendTestsPassed) {
  console.log('\n🎉 All loyalty points calculations are correct!');
  console.log('✅ Backend and frontend calculations match');
  console.log('✅ 1 point per 100 KSh rule is properly implemented');
} else {
  console.log('\n❌ Some tests failed. Please check the implementation.');
  process.exit(1);
}
