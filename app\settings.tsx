import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useScreenNavigation } from "@/src/hooks/useScreenNavigation";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import EnhancedThermalPrintService from "../src/services/EnhancedThermalPrintService";
import ThermalPrintService from "../src/services/ThermalPrintService";

export default function SettingsScreen() {
  const router = useRouter();
  const { canManageStaff, canManageSystem } = useRBAC();
  const [thermalPrinterStatus, setThermalPrinterStatus] = useState<{
    isConnected: boolean;
    printerType: string | null;
    connectedDevice: any;
  }>({
    isConnected: false,
    printerType: null,
    connectedDevice: null,
  });
  const [offlineReceiptsCount, setOfflineReceiptsCount] = useState(0);

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");

  // Use enhanced navigation hook with focus handling
  useScreenNavigation({
    title: "Settings",
    forceTitle: true,
    onFocus: () => {
      loadThermalPrinterStatus();
      loadOfflineReceiptsCount();
    },
  });

  const loadThermalPrinterStatus = () => {
    const status = EnhancedThermalPrintService.getThermalPrinterStatus();
    setThermalPrinterStatus(status);
  };

  const loadOfflineReceiptsCount = async () => {
    try {
      const offlineReceipts =
        await EnhancedThermalPrintService.getOfflineReceipts();
      setOfflineReceiptsCount(offlineReceipts.length);
    } catch (error) {
      console.error("Error loading offline receipts count:", error);
    }
  };

  const handleThermalPrinterSetup = () => {
    router.push("/thermal-printer-setup");
  };

  const handleTestThermalPrint = async () => {
    try {
      const result = await EnhancedThermalPrintService.testThermalPrint();
      if (result.success) {
        Alert.alert(
          "Test Print Successful",
          "Test receipt printed successfully!"
        );
      } else {
        Alert.alert(
          "Test Print Failed",
          result.error || "Failed to print test receipt."
        );
      }
    } catch (error) {
      Alert.alert("Test Print Error", "Failed to print test receipt.");
    }
  };

  const handlePrintOfflineReceipts = async () => {
    if (offlineReceiptsCount === 0) {
      Alert.alert(
        "No Offline Receipts",
        "There are no offline receipts to print."
      );
      return;
    }

    Alert.alert(
      "Print Offline Receipts",
      `Print ${offlineReceiptsCount} offline receipt(s)?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Print All",
          onPress: async () => {
            try {
              const result =
                await EnhancedThermalPrintService.printOfflineReceipts();
              Alert.alert(
                "Offline Receipts Printed",
                `Successfully printed: ${result.success}\nFailed: ${result.failed}`
              );
              loadOfflineReceiptsCount(); // Refresh count
            } catch (error) {
              Alert.alert("Print Error", "Failed to print offline receipts.");
            }
          },
        },
      ]
    );
  };

  const handleDisconnectPrinter = async () => {
    Alert.alert(
      "Disconnect Printer",
      "Are you sure you want to disconnect the thermal printer?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Disconnect",
          style: "destructive",
          onPress: async () => {
            try {
              await ThermalPrintService.disconnect();
              loadThermalPrinterStatus(); // Refresh status
              Alert.alert(
                "Disconnected",
                "Thermal printer disconnected successfully."
              );
            } catch (error) {
              Alert.alert(
                "Disconnect Error",
                "Failed to disconnect thermal printer."
              );
            }
          },
        },
      ]
    );
  };

  const renderManagementSection = () => {
    if (!canManageStaff && !canManageSystem) {
      return null;
    }

    return (
      <View style={styles.section}>
        {canManageStaff && (
          <>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => router.push("/staff-list")}
            >
              <View style={styles.settingItemLeft}>
                <Ionicons name="people" size={24} color="#007AFF" />
                <View style={styles.settingItemText}>
                  <Text style={styles.settingItemTitle}>Staff Management</Text>
                  <Text style={styles.settingItemSubtitle}>
                    Manage staff members and permissions
                  </Text>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#ccc" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => router.push("/sales-agent-list")}
            >
              <View style={styles.settingItemLeft}>
                <Ionicons name="person-add" size={24} color="#4CAF50" />
                <View style={styles.settingItemText}>
                  <Text style={styles.settingItemTitle}>
                    Sales Agent Management
                  </Text>
                  <Text style={styles.settingItemSubtitle}>
                    Manage sales agents and territories
                  </Text>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#ccc" />
            </TouchableOpacity>
          </>
        )}

        {canManageSystem && (
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() =>
              Alert.alert(
                "Coming Soon",
                "System settings feature is being developed."
              )
            }
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="settings" size={24} color="#9C27B0" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>System Settings</Text>
                <Text style={styles.settingItemSubtitle}>
                  Configure system-wide settings
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderThermalPrinterSection = () => (
    <View style={styles.section}>
      <TouchableOpacity
        style={styles.settingItem}
        onPress={handleThermalPrinterSetup}
      >
        <View style={styles.settingItemLeft}>
          <Ionicons name="print" size={24} color="#007AFF" />
          <View style={styles.settingItemText}>
            <Text style={styles.settingItemTitle}>Printer Setup</Text>
            <Text style={styles.settingItemSubtitle}>
              {thermalPrinterStatus.isConnected
                ? `Connected: ${
                    thermalPrinterStatus.connectedDevice?.name || "Unknown"
                  }`
                : "No printer connected"}
            </Text>
          </View>
        </View>
        <Ionicons name="chevron-forward" size={20} color="#ccc" />
      </TouchableOpacity>

      {thermalPrinterStatus.isConnected && (
        <>
          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleTestThermalPrint}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="document-text" size={24} color="#4CAF50" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Test Print</Text>
                <Text style={styles.settingItemSubtitle}>
                  Print a test receipt
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleDisconnectPrinter}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="unlink" size={24} color="#F44336" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Disconnect Printer</Text>
                <Text style={styles.settingItemSubtitle}>
                  Disconnect current printer
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push("/thermal-printer-test")}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="flask" size={24} color="#9C27B0" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Run Tests</Text>
                <Text style={styles.settingItemSubtitle}>
                  Test thermal printing functionality
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>
        </>
      )}

      {offlineReceiptsCount > 0 && (
        <TouchableOpacity
          style={styles.settingItem}
          onPress={handlePrintOfflineReceipts}
        >
          <View style={styles.settingItemLeft}>
            <Ionicons name="cloud-upload" size={24} color="#FF9500" />
            <View style={styles.settingItemText}>
              <Text style={styles.settingItemTitle}>Offline Receipts</Text>
              <Text style={styles.settingItemSubtitle}>
                {offlineReceiptsCount} receipt(s) pending print
              </Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#ccc" />
        </TouchableOpacity>
      )}
    </View>
  );

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary
  );

  return (
    <ScreenWrapper>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderManagementSection()}
        {renderThermalPrinterSection()}
      </ScrollView>
    </ScreenWrapper>
  );
}

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    content: {
      flex: 1,
    },
    section: {
      backgroundColor: surfaceColor,
      marginHorizontal: 16,
      marginTop: 16,
      borderRadius: 12,
      paddingVertical: 8,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    settingItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: "#f0f0f0",
    },
    settingItemLeft: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    settingItemText: {
      marginLeft: 12,
      flex: 1,
    },
    settingItemTitle: {
      fontSize: 16,
      fontWeight: "500",
      color: textColor,
    },
    settingItemSubtitle: {
      fontSize: 14,
      color: textSecondary,
      marginTop: 2,
    },
  });
