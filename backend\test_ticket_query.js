/**
 * Test Ticket Query Fix
 */

const axios = require("axios");

async function testTicketQuery() {
  try {
    console.log("🔐 Logging in...");
    const loginResponse = await axios.post("http://localhost:3020/api/pos/login", {
      username: "admin1",
      password: "admin123"
    });

    if (!loginResponse.data.success) {
      throw new Error("Login failed");
    }

    const token = loginResponse.data.data.token;
    console.log("✅ Login successful");

    console.log("🎫 Testing get tickets endpoint...");
    const ticketsResponse = await axios.get("http://localhost:3020/api/tickets", {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (ticketsResponse.data.success) {
      console.log(`✅ Get tickets successful - found ${ticketsResponse.data.data.tickets.length} tickets`);
    } else {
      console.log("❌ Get tickets failed:", ticketsResponse.data.error);
    }

  } catch (error) {
    console.error("❌ Test failed:", error.response?.data?.error || error.message);
  }
}

testTicketQuery();
