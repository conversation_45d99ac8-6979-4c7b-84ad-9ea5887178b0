/**
 * Fulfillment API Service
 *
 * Frontend service for interacting with the fulfillment management API.
 * Provides methods for managing deliveries, shipping fees, and fulfillment status.
 * Designed to work with TanStack React Query for optimal data management.
 */

import { getAPIClient } from "./api/dukalink-client";

export interface FulfillmentData {
  orderId: string;
  shopifyOrderId?: string;
  deliveryAddress?: {
    street?: string;
    city?: string;
    postalCode?: string;
    country?: string;
  };
  deliveryContactName?: string;
  deliveryContactPhone?: string;
  deliveryInstructions?: string;
  deliveryMethod?: string;
  estimatedDeliveryDate?: string;
  shippingFee?: number;
  shippingFeeCurrency?: string;
}

export interface FulfillmentResponse {
  id: string;
  orderId: string;
  shopifyOrderId?: string;
  staffId: string;
  deliveryAddress?: any;
  deliveryContactName?: string;
  deliveryContactPhone?: string;
  deliveryInstructions?: string;
  deliveryMethod: string;
  estimatedDeliveryDate?: string;
  shippingFee: number;
  shippingFeeCurrency: string;
  fulfillmentStatus: string;
  shopifyFulfillmentId?: string;
  trackingNumber?: string;
  trackingUrl?: string;
  carrier?: string;
  lastUpdatedBy?: string;
  createdAt: string;
  updatedAt: string;
  staffName?: string;
  staffRole?: string;
}

export interface ShippingCalculationData {
  deliveryMethod: string;
  distanceKm?: number;
  weightKg?: number;
}

export interface ShippingCalculationResponse {
  shippingFee: number;
  deliveryMethod: string;
  currency: string;
  breakdown: {
    baseFee: number;
    distanceFee: number;
    weightFee: number;
  };
}

export interface ShippingRate {
  id: string;
  deliveryMethod: string;
  baseFee: number;
  perKmFee?: number;
  perKgFee?: number;
  minimumFee?: number;
  maximumFee?: number;
  description?: string;
  isActive: boolean;
  createdByStaffId?: string;
  updatedByStaffId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateDeliveryDetailsData {
  deliveryAddress?: {
    street?: string;
    city?: string;
    postalCode?: string;
    country?: string;
  };
  deliveryContactName?: string;
  deliveryContactPhone?: string;
  deliveryInstructions?: string;
  deliveryMethod?: string;
  estimatedDeliveryDate?: string;
  shippingFee?: number;
}

class FulfillmentApiService {
  private apiClient = getAPIClient();

  /**
   * Create a new fulfillment record
   */
  async createFulfillment(
    fulfillmentData: FulfillmentData
  ): Promise<FulfillmentResponse> {
    const response = await this.apiClient.createFulfillment(fulfillmentData);

    if (response.success && response.data) {
      return response.data as FulfillmentResponse;
    }
    throw new Error(response.error || "Failed to create fulfillment");
  }

  /**
   * Get fulfillment by ID
   */
  async getFulfillmentById(
    fulfillmentId: string
  ): Promise<FulfillmentResponse> {
    const response = await this.apiClient.getFulfillment(fulfillmentId);

    if (response.success && response.data) {
      return response.data as FulfillmentResponse;
    }
    throw new Error(response.error || "Failed to get fulfillment");
  }

  /**
   * Get all fulfillments for an order
   */
  async getFulfillmentsByOrderId(
    orderId: string
  ): Promise<FulfillmentResponse[]> {
    const response = await this.apiClient.getFulfillmentsByOrder(orderId);

    if (response.success && response.data) {
      return response.data as FulfillmentResponse[];
    }
    throw new Error(response.error || "Failed to get fulfillments for order");
  }

  /**
   * Update delivery details for a fulfillment
   */
  async updateDeliveryDetails(
    fulfillmentId: string,
    updateData: UpdateDeliveryDetailsData
  ): Promise<void> {
    const response = await this.apiClient.updateDeliveryDetails(
      fulfillmentId,
      updateData
    );

    if (!response.success) {
      throw new Error(response.error || "Failed to update delivery details");
    }
  }

  /**
   * Update fulfillment status
   */
  async updateFulfillmentStatus(
    fulfillmentId: string,
    status: string
  ): Promise<void> {
    const response = await this.apiClient.updateFulfillmentStatus(
      fulfillmentId,
      status
    );

    if (!response.success) {
      throw new Error(response.error || "Failed to update fulfillment status");
    }
  }

  /**
   * Calculate shipping fee
   */
  async calculateShippingFee(
    shippingData: ShippingCalculationData
  ): Promise<ShippingCalculationResponse> {
    const response = await this.apiClient.calculateShippingFee(shippingData);

    if (response.success && response.data) {
      return response.data as ShippingCalculationResponse;
    }
    throw new Error(response.error || "Failed to calculate shipping fee");
  }

  /**
   * Get all shipping rates
   */
  async getShippingRates(): Promise<ShippingRate[]> {
    const response = await this.apiClient.getShippingRates();

    if (response.success && response.data) {
      return response.data as ShippingRate[];
    }
    throw new Error(response.error || "Failed to get shipping rates");
  }

  /**
   * Create or update shipping rate (manager+ only)
   */
  async upsertShippingRate(
    rateData: Partial<ShippingRate>
  ): Promise<{ rateId: string }> {
    const response = await this.apiClient.upsertShippingRate(rateData);

    if (response.success && response.data) {
      return response.data as { rateId: string };
    }
    throw new Error(response.error || "Failed to save shipping rate");
  }

  /**
   * Update shipping rate (manager+ only)
   */
  async updateShippingRate(
    rateId: string,
    rateData: Partial<ShippingRate>
  ): Promise<void> {
    const response = await this.apiClient.updateShippingRate(rateId, rateData);

    if (!response.success) {
      throw new Error(response.error || "Failed to update shipping rate");
    }
  }

  /**
   * Get fulfillment statistics (manager+ only)
   */
  async getFulfillmentStats(): Promise<any> {
    const response = await this.apiClient.getFulfillmentStats();

    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.error || "Failed to get fulfillment statistics");
  }
}

export const fulfillmentApiService = new FulfillmentApiService();
