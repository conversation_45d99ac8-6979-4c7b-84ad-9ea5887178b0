/**
 * Enhanced Payment Collection API Routes
 * Handles credit sales, mixed-method payments, and payment collection
 */

const express = require("express");
const router = express.Router();
const { authenticateToken } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");
const EnhancedPaymentCollectionService = require("../services/enhanced-payment-collection-service");

const paymentCollectionService = new EnhancedPaymentCollectionService();

/**
 * Create mixed-method sale (cash + credit combination)
 * POST /api/payments/mixed-method-sale
 */
router.post("/mixed-method-sale", authenticateToken, async (req, res) => {
  try {
    const { orderData, paymentMethods, customerInfo } = req.body;

    // Validate required fields
    if (!orderData || !paymentMethods || !Array.isArray(paymentMethods)) {
      return ResponseFormatter.validationError(res, {
        orderData: !orderData ? "Order data is required" : null,
        paymentMethods: !paymentMethods ? "Payment methods are required" : null,
      });
    }

    // Validate customer info for credit sales
    const hasCreditPayment = paymentMethods.some(m => m.method_type === "credit");
    if (hasCreditPayment && !customerInfo) {
      return ResponseFormatter.validationError(res, {
        customerInfo: "Customer information is required for credit sales",
      });
    }

    const result = await paymentCollectionService.createMixedMethodSale(
      orderData,
      paymentMethods,
      customerInfo
    );

    if (result.success) {
      return ResponseFormatter.success(res, result, "Mixed-method sale created successfully");
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Error creating mixed-method sale:", error);
    return ResponseFormatter.error(res, "Failed to create mixed-method sale", 500);
  }
});

/**
 * Collect payment on existing order
 * POST /api/payments/collect-payment
 */
router.post("/collect-payment", authenticateToken, async (req, res) => {
  try {
    const { shopifyOrderId, amount, paymentMethod, customerInfo } = req.body;

    // Validate required fields
    if (!shopifyOrderId || !amount || !paymentMethod) {
      return ResponseFormatter.validationError(res, {
        shopifyOrderId: !shopifyOrderId ? "Shopify order ID is required" : null,
        amount: !amount ? "Payment amount is required" : null,
        paymentMethod: !paymentMethod ? "Payment method is required" : null,
      });
    }

    // Validate amount is positive
    if (parseFloat(amount) <= 0) {
      return ResponseFormatter.validationError(res, {
        amount: "Payment amount must be greater than zero",
      });
    }

    const result = await paymentCollectionService.collectPayment(
      shopifyOrderId,
      parseFloat(amount),
      paymentMethod,
      customerInfo
    );

    if (result.success) {
      return ResponseFormatter.success(res, result, "Payment collected successfully");
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Error collecting payment:", error);
    return ResponseFormatter.error(res, "Failed to collect payment", 500);
  }
});

/**
 * Get payment history and receipts for an order
 * GET /api/payments/history/:shopifyOrderId
 */
router.get("/history/:shopifyOrderId", authenticateToken, async (req, res) => {
  try {
    const { shopifyOrderId } = req.params;

    if (!shopifyOrderId) {
      return ResponseFormatter.validationError(res, {
        shopifyOrderId: "Shopify order ID is required",
      });
    }

    const result = await paymentCollectionService.getPaymentHistory(shopifyOrderId);

    if (result.success) {
      return ResponseFormatter.success(res, result, "Payment history retrieved successfully");
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Error getting payment history:", error);
    return ResponseFormatter.error(res, "Failed to get payment history", 500);
  }
});

/**
 * Get current order status and payment details from Shopify
 * GET /api/payments/order-status/:shopifyOrderId
 */
router.get("/order-status/:shopifyOrderId", authenticateToken, async (req, res) => {
  try {
    const { shopifyOrderId } = req.params;

    if (!shopifyOrderId) {
      return ResponseFormatter.validationError(res, {
        shopifyOrderId: "Shopify order ID is required",
      });
    }

    const result = await paymentCollectionService.getOrderPaymentStatus(shopifyOrderId);

    if (result.success) {
      return ResponseFormatter.success(res, result.data, "Order status retrieved successfully");
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Error getting order status:", error);
    return ResponseFormatter.error(res, "Failed to get order status", 500);
  }
});

/**
 * Create pure credit sale (reservation)
 * POST /api/payments/credit-sale
 */
router.post("/credit-sale", authenticateToken, async (req, res) => {
  try {
    const { orderData, creditAmount, customerInfo } = req.body;

    // Validate required fields
    if (!orderData || !creditAmount || !customerInfo) {
      return ResponseFormatter.validationError(res, {
        orderData: !orderData ? "Order data is required" : null,
        creditAmount: !creditAmount ? "Credit amount is required" : null,
        customerInfo: !customerInfo ? "Customer information is required" : null,
      });
    }

    // Validate credit amount
    if (parseFloat(creditAmount) <= 0) {
      return ResponseFormatter.validationError(res, {
        creditAmount: "Credit amount must be greater than zero",
      });
    }

    const shopifyPaymentService = paymentCollectionService.shopifyPaymentService;
    const result = await shopifyPaymentService.createCreditSaleOrder(
      orderData,
      parseFloat(creditAmount),
      customerInfo
    );

    if (result.success) {
      return ResponseFormatter.success(res, result, "Credit sale created successfully");
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Error creating credit sale:", error);
    return ResponseFormatter.error(res, "Failed to create credit sale", 500);
  }
});

/**
 * Validate payment amount against order balance
 * POST /api/payments/validate-amount
 */
router.post("/validate-amount", authenticateToken, async (req, res) => {
  try {
    const { shopifyOrderId, paymentAmount } = req.body;

    if (!shopifyOrderId || !paymentAmount) {
      return ResponseFormatter.validationError(res, {
        shopifyOrderId: !shopifyOrderId ? "Shopify order ID is required" : null,
        paymentAmount: !paymentAmount ? "Payment amount is required" : null,
      });
    }

    // Get payment history to calculate current balance
    const historyResult = await paymentCollectionService.getPaymentHistory(shopifyOrderId);
    
    if (!historyResult.success) {
      return ResponseFormatter.error(res, historyResult.error, 400);
    }

    const receipts = historyResult.receipts || [];
    const orderTotal = receipts[0]?.orderTotal || 0;
    const currentPaid = receipts.reduce((sum, receipt) => {
      return sum + (receipt.amountPaid || 0);
    }, 0);
    const remainingBalance = orderTotal - currentPaid;

    // Validate payment amount
    try {
      paymentCollectionService.validatePaymentAmount(
        orderTotal,
        currentPaid,
        parseFloat(paymentAmount)
      );

      return ResponseFormatter.success(res, {
        valid: true,
        orderTotal,
        currentPaid,
        remainingBalance,
        proposedPayment: parseFloat(paymentAmount),
        newBalance: remainingBalance - parseFloat(paymentAmount),
      }, "Payment amount is valid");
    } catch (validationError) {
      return ResponseFormatter.error(res, validationError.message, 400);
    }
  } catch (error) {
    console.error("Error validating payment amount:", error);
    return ResponseFormatter.error(res, "Failed to validate payment amount", 500);
  }
});

/**
 * Get order financial status and payment summary
 * GET /api/payments/status/:shopifyOrderId
 */
router.get("/status/:shopifyOrderId", authenticateToken, async (req, res) => {
  try {
    const { shopifyOrderId } = req.params;

    if (!shopifyOrderId) {
      return ResponseFormatter.validationError(res, {
        shopifyOrderId: "Shopify order ID is required",
      });
    }

    const historyResult = await paymentCollectionService.getPaymentHistory(shopifyOrderId);
    
    if (!historyResult.success) {
      return ResponseFormatter.error(res, historyResult.error, 400);
    }

    const receipts = historyResult.receipts || [];
    const splitMethods = historyResult.splitMethods;
    
    const orderTotal = receipts[0]?.orderTotal || 0;
    const totalPaid = receipts.reduce((sum, receipt) => {
      return sum + (receipt.amountPaid || 0);
    }, 0);
    const remainingBalance = orderTotal - totalPaid;

    const status = {
      shopifyOrderId,
      orderTotal,
      totalPaid,
      remainingBalance,
      isFullyPaid: remainingBalance === 0,
      paymentCount: receipts.length,
      lastPayment: receipts[receipts.length - 1] || null,
      splitMethods,
      receipts,
    };

    return ResponseFormatter.success(res, status, "Order status retrieved successfully");
  } catch (error) {
    console.error("Error getting order status:", error);
    return ResponseFormatter.error(res, "Failed to get order status", 500);
  }
});

module.exports = router;
