/**
 * Loyalty Synchronization Management API Routes
 * Handles manual sync operations and monitoring of loyalty data synchronization
 */

const express = require("express");
const router = express.Router();
const loyaltySyncService = require("../services/loyalty-sync-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");

// Get sync queue status
router.get("/queue/status", authenticateToken, async (req, res) => {
  try {
    const status = loyaltySyncService.getSyncQueueStatus();
    
    return ResponseFormatter.success(
      res,
      status,
      "Sync queue status retrieved successfully"
    );
  } catch (error) {
    console.error("Get sync queue status error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get sync queue status"
    );
  }
});

// Queue customer for sync
router.post("/queue/customer/:customerId", authenticateToken, async (req, res) => {
  try {
    const { customerId } = req.params;
    const { priority = 'normal' } = req.body;

    // Validate priority
    if (!['normal', 'high'].includes(priority)) {
      return ResponseFormatter.validationError(res, {
        priority: "Priority must be 'normal' or 'high'"
      });
    }

    const result = await loyaltySyncService.queueCustomerSync(customerId, priority);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          customerId: customerId,
          priority: priority,
          queueStatus: loyaltySyncService.getSyncQueueStatus()
        },
        `Customer ${customerId} queued for sync with ${priority} priority`
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Queue customer sync error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to queue customer for sync"
    );
  }
});

// Manually sync specific customer
router.post("/customer/:customerId", authenticateToken, async (req, res) => {
  try {
    const { customerId } = req.params;

    const result = await loyaltySyncService.syncCustomerLoyalty(customerId);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          customerId: customerId,
          metafieldsUpdated: result.metafieldsUpdated,
          loyaltyData: result.loyaltyData
        },
        `Customer ${customerId} loyalty data synced successfully`
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Manual customer sync error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to sync customer loyalty data"
    );
  }
});

// Bulk sync all customers (requires admin permission)
router.post("/bulk/all", authenticateToken, requirePermission("manage_loyalty_sync"), async (req, res) => {
  try {
    const result = await loyaltySyncService.bulkSyncAllCustomers();

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          customerCount: result.customerCount,
          queueStatus: loyaltySyncService.getSyncQueueStatus()
        },
        result.message
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Bulk sync all customers error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to start bulk sync"
    );
  }
});

// Bulk sync specific customers
router.post("/bulk/customers", authenticateToken, requirePermission("manage_loyalty_sync"), async (req, res) => {
  try {
    const { customerIds, priority = 'normal' } = req.body;

    if (!Array.isArray(customerIds) || customerIds.length === 0) {
      return ResponseFormatter.validationError(res, {
        customerIds: "Must provide an array of customer IDs"
      });
    }

    if (customerIds.length > 100) {
      return ResponseFormatter.validationError(res, {
        customerIds: "Maximum 100 customers can be synced at once"
      });
    }

    // Queue all customers for sync
    const results = {
      queued: [],
      failed: [],
      total: customerIds.length
    };

    for (const customerId of customerIds) {
      try {
        const result = await loyaltySyncService.queueCustomerSync(customerId, priority);
        if (result.success) {
          results.queued.push(customerId);
        } else {
          results.failed.push({ customerId, error: result.error });
        }
      } catch (error) {
        results.failed.push({ customerId, error: error.message });
      }
    }

    return ResponseFormatter.success(
      res,
      {
        results: results,
        queueStatus: loyaltySyncService.getSyncQueueStatus()
      },
      `Queued ${results.queued.length} customers for sync, ${results.failed.length} failed`
    );
  } catch (error) {
    console.error("Bulk sync customers error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to queue customers for bulk sync"
    );
  }
});

// Clear sync queue (requires admin permission)
router.delete("/queue/clear", authenticateToken, requirePermission("manage_loyalty_sync"), async (req, res) => {
  try {
    const result = loyaltySyncService.clearSyncQueue();

    return ResponseFormatter.success(
      res,
      {
        clearedCount: result.clearedCount,
        queueStatus: loyaltySyncService.getSyncQueueStatus()
      },
      `Cleared ${result.clearedCount} items from sync queue`
    );
  } catch (error) {
    console.error("Clear sync queue error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to clear sync queue"
    );
  }
});

// Force process sync queue
router.post("/queue/process", authenticateToken, requirePermission("manage_loyalty_sync"), async (req, res) => {
  try {
    const statusBefore = loyaltySyncService.getSyncQueueStatus();
    
    // Trigger immediate processing
    loyaltySyncService.processSyncQueue();

    return ResponseFormatter.success(
      res,
      {
        statusBefore: statusBefore,
        message: "Sync queue processing triggered"
      },
      "Sync queue processing started"
    );
  } catch (error) {
    console.error("Force process sync queue error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to trigger sync queue processing"
    );
  }
});

// Get sync statistics
router.get("/statistics", authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // Build date filter
    let dateFilter = '';
    const params = [];
    
    if (startDate) {
      dateFilter += ' AND lt.created_at >= ?';
      params.push(startDate);
    }
    
    if (endDate) {
      dateFilter += ' AND lt.created_at <= ?';
      params.push(endDate);
    }

    // Get sync statistics from loyalty transactions
    const [syncStats] = await loyaltySyncService.loyaltyService.pool.execute(
      `SELECT 
        COUNT(*) as total_transactions,
        COUNT(DISTINCT shopify_customer_id) as unique_customers,
        SUM(CASE WHEN transaction_type = 'earned' THEN points_amount ELSE 0 END) as total_points_earned,
        SUM(CASE WHEN transaction_type = 'redeemed' THEN ABS(points_amount) ELSE 0 END) as total_points_redeemed,
        AVG(points_amount) as avg_points_per_transaction
       FROM loyalty_transactions lt
       WHERE 1=1 ${dateFilter}`,
      params
    );

    // Get tier distribution
    const [tierStats] = await loyaltySyncService.loyaltyService.pool.execute(
      `SELECT 
        loyalty_tier,
        COUNT(*) as customer_count,
        AVG(loyalty_points) as avg_points,
        SUM(total_purchases) as total_purchases
       FROM customer_loyalty
       GROUP BY loyalty_tier`
    );

    const statistics = {
      syncQueue: loyaltySyncService.getSyncQueueStatus(),
      transactions: syncStats[0],
      tierDistribution: tierStats,
      dateRange: {
        startDate: startDate || null,
        endDate: endDate || null
      }
    };

    return ResponseFormatter.success(
      res,
      statistics,
      "Sync statistics retrieved successfully"
    );
  } catch (error) {
    console.error("Get sync statistics error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get sync statistics"
    );
  }
});

// Test sync with sample data
router.post("/test", authenticateToken, requirePermission("manage_loyalty_sync"), async (req, res) => {
  try {
    const { customerId } = req.body;

    if (!customerId) {
      return ResponseFormatter.validationError(res, {
        customerId: "Customer ID is required for testing"
      });
    }

    // Test sync process
    const result = await loyaltySyncService.syncCustomerLoyalty(customerId);

    return ResponseFormatter.success(
      res,
      {
        testResult: result,
        customerId: customerId,
        timestamp: new Date().toISOString()
      },
      result.success ? "Test sync completed successfully" : "Test sync failed"
    );
  } catch (error) {
    console.error("Test sync error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to run test sync"
    );
  }
});

// Health check for loyalty sync service
router.get("/health", async (req, res) => {
  try {
    const queueStatus = loyaltySyncService.getSyncQueueStatus();
    
    return ResponseFormatter.success(
      res,
      {
        status: "healthy",
        service: "loyalty-sync",
        timestamp: new Date().toISOString(),
        queueStatus: queueStatus,
        isProcessing: queueStatus.isProcessing
      },
      "Loyalty sync service health check completed"
    );
  } catch (error) {
    console.error("Loyalty sync health check error:", error);
    return ResponseFormatter.error(
      res,
      "Loyalty sync service health check failed",
      503
    );
  }
});

module.exports = router;
