import React, { ReactNode } from "react";
import { ThemeProvider } from "@/src/contexts/ThemeContext";
import { QueryClientProvider } from "@/src/providers/QueryClientProvider";
import { useUserSync } from "@/src/hooks/useUserSync";

interface AppProvidersProps {
  children: ReactNode;
}

// Internal component that uses the sync hook
const UserSyncProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  useUserSync(); // Sync user from AuthContext to Redux
  return <>{children}</>;
};

export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <QueryClientProvider>
      <ThemeProvider>
        <UserSyncProvider>{children}</UserSyncProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};
