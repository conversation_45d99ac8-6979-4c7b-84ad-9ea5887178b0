/**
 * Enhanced Discount Service - MySQL Implementation
 * Extends commission-discount functionality with advanced rules, staff permissions, and loyalty integration
 * Maintains 100% API compatibility with existing commission-discount-service.js
 */

require("dotenv").config();
const { databaseManager } = require("../config/database-manager");
const { v4: uuidv4 } = require("uuid");
const loyaltyService = require("./loyalty-service");

class EnhancedDiscountService {
  constructor() {
    if (EnhancedDiscountService.instance) {
      return EnhancedDiscountService.instance;
    }

    // Use centralized database manager
    this.databaseManager = databaseManager;

    console.log(
      "✅ Enhanced Discount Service initialized with centralized database manager"
    );

    EnhancedDiscountService.instance = this;
  }

  static getInstance() {
    if (!EnhancedDiscountService.instance) {
      EnhancedDiscountService.instance = new EnhancedDiscountService();
    }
    return EnhancedDiscountService.instance;
  }

  /**
   * Execute query using centralized database manager
   */
  async executeQuery(query, params = []) {
    return await this.databaseManager.executeQuery(query, params);
  }

  // Get all applicable discounts for a cart
  async getApplicableDiscounts(
    cartData,
    staffId,
    customerId = null,
    salesAgentId = null
  ) {
    try {
      const subtotal = parseFloat(cartData.subtotal || 0);
      const applicableDiscounts = [];

      // Get commission-based discounts (existing functionality)
      const commissionService = require("./commission-discount-service");
      const commissionResult =
        await commissionService.calculateCommissionDiscount(
          cartData,
          staffId,
          salesAgentId,
          customerId
        );

      if (
        commissionResult.success &&
        commissionResult.discount.totalDiscount > 0
      ) {
        applicableDiscounts.push({
          id: "commission-discount",
          type: "commission",
          name: "Commission Discount",
          description: "Staff and agent commission-based discount",
          discountType: "percentage",
          amount: commissionResult.discount.totalDiscount,
          percentage: commissionResult.discount.discountPercentage,
          priority: 1,
          canCombine: true,
          source: "commission",
          breakdown: commissionResult.discount.breakdown,
        });
      }

      // Get loyalty-based discounts
      if (customerId) {
        const loyaltyResult = await loyaltyService.calculateLoyaltyDiscount(
          customerId,
          subtotal
        );
        if (loyaltyResult.success) {
          const { tier, points } = loyaltyResult.discounts;

          // Add tier discount if applicable
          if (tier.amount > 0) {
            applicableDiscounts.push({
              id: "loyalty-tier-discount",
              type: "loyalty_tier",
              name: `${
                tier.tierName.charAt(0).toUpperCase() + tier.tierName.slice(1)
              } Member Discount`,
              description: `${tier.percentage}% discount for ${tier.tierName} tier members`,
              discountType: "percentage",
              amount: tier.amount,
              percentage: tier.percentage,
              priority: 2,
              canCombine: true,
              source: "loyalty",
              tierName: tier.tierName,
            });
          }

          // Add points redemption option if customer has points
          if (
            points.availablePoints >=
            loyaltyService.redemptionConfig.minRedemption
          ) {
            applicableDiscounts.push({
              id: "loyalty-points-redemption",
              type: "loyalty_points",
              name: "Loyalty Points Redemption",
              description: `Redeem up to ${points.maxRedeemablePoints} points for KSh ${points.maxDiscount} discount`,
              discountType: "fixed_amount",
              amount: points.maxDiscount,
              maxPoints: points.maxRedeemablePoints,
              availablePoints: points.availablePoints,
              exchangeRate: points.exchangeRate,
              priority: 3,
              canCombine: true,
              source: "loyalty",
            });
          }
        }
      }

      // Get custom discount rules
      const customDiscounts = await this.getCustomDiscountRules(
        staffId,
        customerId,
        subtotal
      );
      if (customDiscounts.success) {
        applicableDiscounts.push(...customDiscounts.discounts);
      }

      return {
        success: true,
        discounts: applicableDiscounts,
        totalPossibleSavings: applicableDiscounts.reduce(
          (sum, discount) => sum + discount.amount,
          0
        ),
      };
    } catch (error) {
      console.error("Get applicable discounts error:", error);
      return {
        success: false,
        error: "Failed to get applicable discounts",
      };
    }
  }

  // Get custom discount rules that staff can apply
  async getCustomDiscountRules(staffId, customerId = null, orderTotal = 0) {
    try {
      // Get customer loyalty info for eligibility check
      let customerTier = null;
      if (customerId) {
        const loyaltyResult = await loyaltyService.getCustomerLoyalty(
          customerId
        );
        if (loyaltyResult.success) {
          customerTier = loyaltyResult.loyalty.loyalty_tier;
        }
      }

      // Query discount rules with staff permissions
      const [rows] = await this.databaseManager.executeQuery(
        `SELECT dr.*, sdp.permission_type, sdp.max_discount_percentage, sdp.max_discount_amount
         FROM discount_rules dr
         INNER JOIN staff_discount_permissions sdp ON dr.id = sdp.discount_rule_id
         WHERE dr.is_active = TRUE 
           AND sdp.staff_id = ? 
           AND sdp.permission_type IN ('apply', 'modify')
           AND dr.min_purchase_amount <= ?
           AND (dr.valid_from IS NULL OR dr.valid_from <= NOW())
           AND (dr.valid_to IS NULL OR dr.valid_to >= NOW())
           AND (dr.customer_eligibility = 'all' 
                OR (dr.customer_eligibility = 'loyalty_tier' AND dr.loyalty_tier_required = ?))
         ORDER BY dr.discount_value DESC`,
        [staffId, orderTotal, customerTier]
      );

      const discounts = rows.map((row) => {
        let discountAmount = 0;

        if (row.discount_type === "percentage") {
          discountAmount = (orderTotal * row.discount_value) / 100;
          // Apply staff-specific limits
          if (
            row.max_discount_percentage &&
            row.discount_value > row.max_discount_percentage
          ) {
            discountAmount = (orderTotal * row.max_discount_percentage) / 100;
          }
        } else if (row.discount_type === "fixed_amount") {
          discountAmount = row.discount_value;
        }

        // Apply maximum discount amount limit
        if (
          row.max_discount_amount &&
          discountAmount > row.max_discount_amount
        ) {
          discountAmount = row.max_discount_amount;
        }

        return {
          id: row.id,
          type: "custom_rule",
          name: row.name,
          description: row.description,
          discountType: row.discount_type,
          amount: parseFloat(discountAmount.toFixed(2)),
          percentage:
            row.discount_type === "percentage"
              ? row.discount_value
              : parseFloat(((discountAmount / orderTotal) * 100).toFixed(2)),
          priority: 4,
          canCombine: false, // Custom rules typically don't combine
          source: "custom_rule",
          staffPermission: row.permission_type,
          eligibility: row.customer_eligibility,
          requiredTier: row.loyalty_tier_required,
          maxUses: row.max_uses_per_customer,
          currentUses: row.current_uses,
        };
      });

      return {
        success: true,
        discounts: discounts.filter((d) => d.amount > 0),
      };
    } catch (error) {
      console.error("Get custom discount rules error:", error);
      return {
        success: false,
        error: "Failed to get custom discount rules",
      };
    }
  }

  // Apply discount to cart
  async applyDiscount(
    discountData,
    cartData,
    staffId,
    customerId = null,
    salesAgentId = null
  ) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          const { discountId, discountType, amount, pointsToRedeem } =
            discountData;
          const subtotal = parseFloat(cartData.subtotal || 0);
          let finalDiscount = 0;
          let discountDetails = {};

          switch (discountType) {
            case "commission":
              // Use existing commission discount calculation
              const commissionService = require("./commission-discount-service");
              const commissionResult =
                await commissionService.calculateCommissionDiscount(
                  cartData,
                  staffId,
                  salesAgentId,
                  customerId
                );
              if (commissionResult.success) {
                finalDiscount = commissionResult.discount.totalDiscount;
                discountDetails = commissionResult.discount;
              }
              break;

            case "loyalty_tier":
              // Apply tier-based discount
              if (customerId) {
                const loyaltyResult =
                  await loyaltyService.calculateLoyaltyDiscount(
                    customerId,
                    subtotal
                  );
                if (loyaltyResult.success) {
                  finalDiscount = loyaltyResult.discounts.tier.amount;
                  discountDetails = loyaltyResult.discounts.tier;
                }
              }
              break;

            case "loyalty_points":
              // Redeem loyalty points
              if (customerId && pointsToRedeem) {
                const redemptionResult = await loyaltyService.redeemPoints(
                  customerId,
                  pointsToRedeem,
                  cartData.orderId,
                  cartData.ticketId,
                  staffId
                );
                if (redemptionResult.success) {
                  finalDiscount = redemptionResult.discountAmount;
                  discountDetails = redemptionResult;
                }
              }
              break;

            case "custom_rule":
              // Apply custom discount rule
              const ruleResult = await this.applyCustomRule(
                discountId,
                cartData,
                staffId,
                customerId
              );
              if (ruleResult.success) {
                finalDiscount = ruleResult.discountAmount;
                discountDetails = ruleResult;
              }
              break;

            default:
              throw new Error(`Unknown discount type: ${discountType}`);
          }

          // Log discount usage
          if (finalDiscount > 0) {
            await this.logDiscountUsage({
              discountId: discountId,
              discountType: discountType,
              customerId: customerId,
              orderId: cartData.orderId,
              ticketId: cartData.ticketId,
              discountAmount: finalDiscount,
              originalAmount: subtotal,
              staffId: staffId,
              salesAgentId: salesAgentId,
            });
          }

          return {
            success: true,
            discount: {
              type: discountType,
              amount: finalDiscount,
              percentage: parseFloat(
                ((finalDiscount / subtotal) * 100).toFixed(2)
              ),
              originalAmount: subtotal,
              finalAmount: subtotal - finalDiscount,
              details: discountDetails,
            },
            message: `Discount of KSh ${finalDiscount.toFixed(
              2
            )} applied successfully`,
          };
        }
      );
    } catch (error) {
      console.error("Apply discount error:", error);
      return {
        success: false,
        error: "Failed to apply discount",
      };
    }
  }

  // Apply custom discount rule
  async applyCustomRule(ruleId, cartData, staffId, customerId) {
    try {
      // Get rule details and verify staff permission
      const [ruleRows] = await this.databaseManager.executeQuery(
        `SELECT dr.*, sdp.permission_type, sdp.max_discount_percentage, sdp.max_discount_amount
         FROM discount_rules dr
         INNER JOIN staff_discount_permissions sdp ON dr.id = sdp.discount_rule_id
         WHERE dr.id = ? AND sdp.staff_id = ? AND dr.is_active = TRUE`,
        [ruleId, staffId]
      );

      if (ruleRows.length === 0) {
        return {
          success: false,
          error: "Discount rule not found or staff not authorized",
        };
      }

      const rule = ruleRows[0];
      const subtotal = parseFloat(cartData.subtotal || 0);

      // Check usage limits
      if (rule.max_uses_total && rule.current_uses >= rule.max_uses_total) {
        return {
          success: false,
          error: "Discount rule usage limit exceeded",
        };
      }

      // Check customer-specific usage limits
      if (customerId && rule.max_uses_per_customer) {
        const [usageRows] = await this.databaseManager.executeQuery(
          `SELECT COUNT(*) as usage_count FROM discount_usage_log
           WHERE discount_rule_id = ? AND shopify_customer_id = ?`,
          [ruleId, customerId]
        );

        if (usageRows[0].usage_count >= rule.max_uses_per_customer) {
          return {
            success: false,
            error: "Customer usage limit exceeded for this discount",
          };
        }
      }

      // Calculate discount amount
      let discountAmount = 0;
      if (rule.discount_type === "percentage") {
        discountAmount = (subtotal * rule.discount_value) / 100;
        // Apply staff limits
        if (
          rule.max_discount_percentage &&
          rule.discount_value > rule.max_discount_percentage
        ) {
          discountAmount = (subtotal * rule.max_discount_percentage) / 100;
        }
      } else if (rule.discount_type === "fixed_amount") {
        discountAmount = rule.discount_value;
      }

      // Apply maximum amount limit
      if (
        rule.max_discount_amount &&
        discountAmount > rule.max_discount_amount
      ) {
        discountAmount = rule.max_discount_amount;
      }

      // Update rule usage count
      await this.databaseManager.executeQuery(
        `UPDATE discount_rules SET current_uses = current_uses + 1 WHERE id = ?`,
        [ruleId]
      );

      return {
        success: true,
        discountAmount: parseFloat(discountAmount.toFixed(2)),
        ruleName: rule.name,
        ruleType: rule.discount_type,
        ruleValue: rule.discount_value,
      };
    } catch (error) {
      console.error("Apply custom rule error:", error);
      return {
        success: false,
        error: "Failed to apply custom discount rule",
      };
    }
  }

  // Log discount usage for analytics
  async logDiscountUsage(usageData) {
    try {
      const logId = uuidv4();
      await this.databaseManager.executeQuery(
        `INSERT INTO discount_usage_log (
          id, discount_rule_id, shopify_customer_id, order_id, ticket_id,
          discount_amount, original_amount, staff_id, sales_agent_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          logId,
          usageData.discountId === "commission-discount"
            ? null
            : usageData.discountId,
          usageData.customerId,
          usageData.orderId,
          usageData.ticketId,
          usageData.discountAmount,
          usageData.originalAmount,
          usageData.staffId,
          usageData.salesAgentId,
        ]
      );

      return { success: true, logId: logId };
    } catch (error) {
      console.error("Log discount usage error:", error);
      return { success: false, error: "Failed to log discount usage" };
    }
  }

  // Create new discount rule
  async createDiscountRule(ruleData, createdBy) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          const ruleId = uuidv4();
          await connection.execute(
            `INSERT INTO discount_rules (
          id, shopify_store_id, name, description, discount_type, discount_value,
          min_purchase_amount, max_discount_amount, applicable_products, excluded_products,
          customer_eligibility, loyalty_tier_required, max_uses_per_customer, max_uses_total,
          valid_from, valid_to, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              ruleId,
              ruleData.shopifyStoreId || "default-store",
              ruleData.name,
              ruleData.description,
              ruleData.discountType,
              ruleData.discountValue,
              ruleData.minPurchaseAmount || 0,
              ruleData.maxDiscountAmount,
              ruleData.applicableProducts
                ? JSON.stringify(ruleData.applicableProducts)
                : null,
              ruleData.excludedProducts
                ? JSON.stringify(ruleData.excludedProducts)
                : null,
              ruleData.customerEligibility || "all",
              ruleData.loyaltyTierRequired,
              ruleData.maxUsesPerCustomer,
              ruleData.maxUsesTotal,
              ruleData.validFrom,
              ruleData.validTo,
              createdBy,
            ]
          );

          return {
            success: true,
            ruleId: ruleId,
            message: "Discount rule created successfully",
          };
        }
      );
    } catch (error) {
      console.error("Create discount rule error:", error);
      return {
        success: false,
        error: "Failed to create discount rule",
      };
    }
  }

  // Grant staff permission for discount rule
  async grantStaffPermission(
    staffId,
    ruleId,
    permissionType,
    grantedBy,
    limits = {}
  ) {
    try {
      await this.databaseManager.executeQuery(
        `INSERT INTO staff_discount_permissions (
          staff_id, discount_rule_id, permission_type, max_discount_percentage,
          max_discount_amount, granted_by
        ) VALUES (?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          permission_type = VALUES(permission_type),
          max_discount_percentage = VALUES(max_discount_percentage),
          max_discount_amount = VALUES(max_discount_amount),
          granted_by = VALUES(granted_by)`,
        [
          staffId,
          ruleId,
          permissionType,
          limits.maxDiscountPercentage,
          limits.maxDiscountAmount,
          grantedBy,
        ]
      );

      return {
        success: true,
        message: "Staff permission granted successfully",
      };
    } catch (error) {
      console.error("Grant staff permission error:", error);
      return {
        success: false,
        error: "Failed to grant staff permission",
      };
    }
  }

  // Get discount analytics
  async getDiscountAnalytics(dateRange = {}, staffId = null) {
    try {
      const { startDate, endDate } = dateRange;
      let dateFilter = "";
      let params = [];

      if (startDate && endDate) {
        dateFilter = "WHERE dul.applied_at BETWEEN ? AND ?";
        params = [startDate, endDate];
      }

      if (staffId) {
        dateFilter += dateFilter
          ? " AND dul.staff_id = ?"
          : "WHERE dul.staff_id = ?";
        params.push(staffId);
      }

      // Get usage statistics
      const [usageStats] = await this.databaseManager.executeQuery(
        `SELECT
          COUNT(*) as total_discounts,
          SUM(dul.discount_amount) as total_savings,
          AVG(dul.discount_amount) as average_discount,
          SUM(dul.original_amount) as total_order_value,
          AVG((dul.discount_amount / dul.original_amount) * 100) as average_discount_percentage
         FROM discount_usage_log dul ${dateFilter}`,
        params
      );

      // Get top staff by discount usage
      const [staffStats] = await this.databaseManager.executeQuery(
        `SELECT ps.name, ps.id, COUNT(*) as discount_count, SUM(dul.discount_amount) as total_discounts
         FROM discount_usage_log dul
         INNER JOIN pos_staff ps ON dul.staff_id = ps.id
         ${dateFilter}
         GROUP BY ps.id, ps.name
         ORDER BY total_discounts DESC
         LIMIT 10`,
        params
      );

      // Get discount rule performance
      const [ruleStats] = await this.databaseManager.executeQuery(
        `SELECT dr.name, dr.id, COUNT(*) as usage_count, SUM(dul.discount_amount) as total_amount
         FROM discount_usage_log dul
         INNER JOIN discount_rules dr ON dul.discount_rule_id = dr.id
         ${dateFilter}
         GROUP BY dr.id, dr.name
         ORDER BY usage_count DESC
         LIMIT 10`,
        params
      );

      return {
        success: true,
        analytics: {
          summary: {
            totalDiscounts: usageStats[0].total_discounts || 0,
            totalSavings: parseFloat(
              (usageStats[0].total_savings || 0).toFixed(2)
            ),
            averageDiscount: parseFloat(
              (usageStats[0].average_discount || 0).toFixed(2)
            ),
            totalOrderValue: parseFloat(
              (usageStats[0].total_order_value || 0).toFixed(2)
            ),
            averageDiscountPercentage: parseFloat(
              (usageStats[0].average_discount_percentage || 0).toFixed(2)
            ),
          },
          topStaff: staffStats.map((row) => ({
            staffId: row.id,
            staffName: row.name,
            discountCount: row.discount_count,
            totalDiscounts: parseFloat(row.total_discounts.toFixed(2)),
          })),
          topRules: ruleStats.map((row) => ({
            ruleId: row.id,
            ruleName: row.name,
            usageCount: row.usage_count,
            totalAmount: parseFloat(row.total_amount.toFixed(2)),
          })),
          dateRange: dateRange,
        },
      };
    } catch (error) {
      console.error("Get discount analytics error:", error);
      return {
        success: false,
        error: "Failed to get discount analytics",
      };
    }
  }

  // Compatibility methods for existing commission-discount-service API
  async getDiscountConfiguration() {
    const commissionService = require("./commission-discount-service");
    return await commissionService.getDiscountConfiguration();
  }

  async updateDiscountConfiguration(newConfig) {
    const commissionService = require("./commission-discount-service");
    return await commissionService.updateDiscountConfiguration(newConfig);
  }

  async calculateCommissionDiscount(
    cartData,
    staffId,
    salesAgentId,
    customerId = null
  ) {
    const commissionService = require("./commission-discount-service");
    return await commissionService.calculateCommissionDiscount(
      cartData,
      staffId,
      salesAgentId,
      customerId
    );
  }

  async createCommissionDiscount(discountData, staffId, salesAgentId) {
    const commissionService = require("./commission-discount-service");
    return await commissionService.createCommissionDiscount(
      discountData,
      staffId,
      salesAgentId
    );
  }

  async getCustomerLoyaltyTier(customerId) {
    const loyaltyResult = await loyaltyService.getCustomerLoyalty(customerId);
    if (loyaltyResult.success) {
      return loyaltyResult.loyalty.loyalty_tier;
    }
    return "bronze";
  }
}

module.exports = new EnhancedDiscountService();
