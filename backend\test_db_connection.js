/**
 * Test Database Connection and Check Users
 */

require("dotenv").config();
const mysql = require("mysql2/promise");

async function testDatabase() {
  let connection;
  
  try {
    console.log("🔗 Connecting to MySQL database...");
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "dukalink",
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || "dukalink_shopify_pos",
      charset: "utf8mb4",
    });

    console.log("✅ Connected to MySQL database");

    // Check if pos_staff table exists and has data
    console.log("\n📋 Checking pos_staff table...");
    const [staffRows] = await connection.execute("SELECT id, username, name, role, is_active FROM pos_staff LIMIT 10");
    
    if (staffRows.length === 0) {
      console.log("⚠️ No staff members found in database");
      console.log("🔧 Creating default admin user...");
      
      const bcrypt = require("bcrypt");
      const { v4: uuidv4 } = require("uuid");
      
      const adminId = uuidv4();
      const hashedPassword = await bcrypt.hash("admin123", 10);
      
      await connection.execute(
        `INSERT INTO pos_staff (id, username, password_hash, name, email, role, store_id, commission_rate, is_active)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [adminId, "admin1", hashedPassword, "Admin User", "<EMAIL>", "super_admin", "default-store", 0.00, 1]
      );
      
      // Add permissions for admin
      const permissions = ["manage_staff", "manage_inventory", "process_orders", "view_reports", "manage_discounts"];
      for (const permission of permissions) {
        await connection.execute(
          "INSERT INTO staff_permissions (staff_id, permission) VALUES (?, ?)",
          [adminId, permission]
        );
      }
      
      console.log("✅ Created admin user: admin1 / admin123");
    } else {
      console.log(`✅ Found ${staffRows.length} staff members:`);
      staffRows.forEach(staff => {
        console.log(`   - ${staff.username} (${staff.name}) - ${staff.role} - Active: ${staff.is_active}`);
      });
    }

    // Check staff_permissions table
    console.log("\n🔐 Checking staff_permissions table...");
    const [permRows] = await connection.execute("SELECT staff_id, permission FROM staff_permissions LIMIT 10");
    console.log(`✅ Found ${permRows.length} permission entries`);

    // Check pos_sessions table
    console.log("\n🔑 Checking pos_sessions table...");
    const [sessionRows] = await connection.execute("SELECT COUNT(*) as count FROM pos_sessions");
    console.log(`✅ Found ${sessionRows[0].count} sessions`);

    // Check ticketing tables
    console.log("\n🎫 Checking ticketing tables...");
    const [ticketRows] = await connection.execute("SELECT COUNT(*) as count FROM pos_tickets");
    console.log(`✅ Found ${ticketRows[0].count} tickets`);

    const [userSwitchRows] = await connection.execute("SELECT COUNT(*) as count FROM pos_user_switches");
    console.log(`✅ Found ${userSwitchRows[0].count} user switches`);

    console.log("\n✅ Database connection test completed successfully!");

  } catch (error) {
    console.error("❌ Database test failed:", error);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

testDatabase();
