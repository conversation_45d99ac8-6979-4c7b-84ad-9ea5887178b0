/**
 * Loyalty Service Layer
 *
 * Provides business logic and data management for the loyalty system.
 * Follows the existing service-to-route pattern and integrates with Redux store.
 * Designed for easy migration to TanStack React Query in the future.
 */

import { getAPIClient } from "./api/dukalink-client";
import {
  CustomerLoyaltyData,
  LoyaltyTransaction,
  LoyaltyDiscountCalculation,
  PointsRedemptionRequest,
  PointsRedemptionResult,
  LoyaltyLeaderboardEntry,
  APIResponse,
} from "../types/shopify";

export interface LoyaltyAnalytics {
  totalCustomers: number;
  totalPoints: number;
  tierDistribution: Record<string, number>;
  pointsEarned: number;
  pointsRedeemed: number;
  averagePointsPerCustomer: number;
}

export interface LoyaltyServiceError {
  code: string;
  message: string;
  details?: any;
}

class LoyaltyService {
  private apiClient = getAPIClient();

  // Extract numeric ID from Shopify GID
  private extractCustomerId(customerId: string): string {
    // Handle Shopify GID format: gid://shopify/Customer/7988938735753
    if (customerId.startsWith("gid://shopify/Customer/")) {
      return customerId.split("/").pop() || customerId;
    }
    return customerId;
  }

  // Customer Loyalty Operations
  async getCustomerLoyaltySummary(
    customerId: string
  ): Promise<CustomerLoyaltyData | null> {
    try {
      // Extract numeric ID from Shopify GID
      const numericCustomerId = this.extractCustomerId(customerId);
      const response = await this.apiClient.getCustomerLoyaltySummary(
        numericCustomerId
      );

      if (response.success && response.data) {
        return response.data.summary;
      }

      console.warn(
        `Failed to fetch loyalty summary for customer ${customerId}:`,
        response.error
      );
      return null;
    } catch (error) {
      console.error("Error fetching customer loyalty summary:", error);
      return null;
    }
  }

  async getCustomerLoyaltyTransactions(
    customerId: string,
    options?: {
      limit?: number;
      offset?: number;
      type?: "earned" | "redeemed" | "expired" | "adjusted";
    }
  ): Promise<{ transactions: LoyaltyTransaction[]; count: number } | null> {
    try {
      // Extract numeric ID from Shopify GID
      const numericCustomerId = this.extractCustomerId(customerId);
      const response = await this.apiClient.getCustomerLoyaltyTransactions(
        numericCustomerId,
        options
      );

      if (response.success && response.data) {
        return response.data;
      }

      console.warn(
        `Failed to fetch loyalty transactions for customer ${customerId}:`,
        response.error
      );
      return null;
    } catch (error) {
      console.error("Error fetching customer loyalty transactions:", error);
      return null;
    }
  }

  async calculateLoyaltyDiscounts(
    customerId: string,
    orderTotal: number
  ): Promise<LoyaltyDiscountCalculation | null> {
    try {
      // Extract numeric ID from Shopify GID
      const numericCustomerId = this.extractCustomerId(customerId);
      const response = await this.apiClient.calculateLoyaltyDiscounts(
        numericCustomerId,
        orderTotal
      );

      if (response.success && response.data) {
        return response.data;
      }

      console.warn(
        `Failed to calculate loyalty discounts for customer ${customerId}:`,
        response.error
      );
      return null;
    } catch (error) {
      console.error("Error calculating loyalty discounts:", error);
      return null;
    }
  }

  async redeemLoyaltyPoints(
    customerId: string,
    redemptionData: PointsRedemptionRequest
  ): Promise<PointsRedemptionResult | null> {
    try {
      // Extract numeric ID from Shopify GID
      const numericCustomerId = this.extractCustomerId(customerId);
      const response = await this.apiClient.redeemLoyaltyPoints(
        numericCustomerId,
        redemptionData
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error(response.error || "Failed to redeem loyalty points");
    } catch (error) {
      console.error("Error redeeming loyalty points:", error);
      throw error;
    }
  }

  async addLoyaltyPoints(
    customerId: string,
    pointsData: {
      orderTotal: number;
      orderId: string;
      salesAgentId?: string;
    }
  ): Promise<{
    pointsAdded: number;
    newBalance: number;
    tierChanged: boolean;
    newTier?: string;
    transactionId: string;
  } | null> {
    try {
      // Extract numeric ID from Shopify GID
      const numericCustomerId = this.extractCustomerId(customerId);
      const response = await this.apiClient.addLoyaltyPoints(
        numericCustomerId,
        pointsData
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error(response.error || "Failed to add loyalty points");
    } catch (error) {
      console.error("Error adding loyalty points:", error);
      throw error;
    }
  }

  async initializeCustomerLoyalty(
    customerId: string
  ): Promise<{ loyalty: any; message: string } | null> {
    try {
      // Extract numeric ID from Shopify GID
      const numericCustomerId = this.extractCustomerId(customerId);
      const response = await this.apiClient.initializeCustomerLoyalty(
        numericCustomerId
      );

      if (response.success && response.data) {
        return response.data;
      }

      console.warn(
        `Failed to initialize loyalty for customer ${customerId}:`,
        response.error
      );
      return null;
    } catch (error) {
      console.error("Error initializing customer loyalty:", error);
      return null;
    }
  }

  // Analytics and Leaderboard
  async getLoyaltyLeaderboard(options?: {
    limit?: number;
    tier?: "bronze" | "silver" | "gold" | "platinum";
    orderBy?: "points" | "purchases";
  }): Promise<LoyaltyLeaderboardEntry[]> {
    try {
      const response = await this.apiClient.getLoyaltyLeaderboard(options);

      if (response.success && response.data) {
        return response.data;
      }

      console.warn("Failed to fetch loyalty leaderboard:", response.error);
      return [];
    } catch (error) {
      console.error("Error fetching loyalty leaderboard:", error);
      return [];
    }
  }

  async getLoyaltyAnalytics(options?: {
    dateFrom?: string;
    dateTo?: string;
    tier?: "bronze" | "silver" | "gold" | "platinum";
  }): Promise<LoyaltyAnalytics | null> {
    try {
      const response = await this.apiClient.getLoyaltyAnalytics(options);

      if (response.success && response.data) {
        return response.data;
      }

      console.warn("Failed to fetch loyalty analytics:", response.error);
      return null;
    } catch (error) {
      console.error("Error fetching loyalty analytics:", error);
      return null;
    }
  }

  // Utility Methods
  formatLoyaltyTier(tier: string): string {
    return tier.charAt(0).toUpperCase() + tier.slice(1);
  }

  calculatePointsValue(points: number, exchangeRate: number = 100): number {
    return points / exchangeRate;
  }

  // Safe formatting for loyalty points to prevent NaN display
  formatLoyaltyPoints(points: number | null | undefined): string {
    const safePoints = points || 0;
    if (typeof safePoints !== "number" || isNaN(safePoints)) {
      return "0";
    }
    return safePoints.toLocaleString();
  }

  // Safe formatting for currency values
  formatLoyaltyValue(value: number | null | undefined): string {
    const safeValue = value || 0;
    if (typeof safeValue !== "number" || isNaN(safeValue)) {
      return "KSh 0.00";
    }
    return `KSh ${safeValue.toLocaleString("en-KE", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  }

  getTierColor(tier: string): string {
    const tierColors = {
      bronze: "#CD7F32",
      silver: "#C0C0C0",
      gold: "#FFD700",
      platinum: "#E5E4E2",
    };
    return tierColors[tier as keyof typeof tierColors] || tierColors.bronze;
  }

  getTierIcon(tier: string): string {
    const tierIcons = {
      bronze: "medal",
      silver: "medal-outline",
      gold: "trophy",
      platinum: "diamond",
    };
    return tierIcons[tier as keyof typeof tierIcons] || tierIcons.bronze;
  }

  // Cache invalidation helpers (for future React Query integration)
  invalidateCustomerLoyalty(customerId: string): void {
    // Placeholder for cache invalidation
    console.log(`Invalidating loyalty cache for customer: ${customerId}`);
  }

  invalidateLoyaltyLeaderboard(): void {
    // Placeholder for cache invalidation
    console.log("Invalidating loyalty leaderboard cache");
  }

  invalidateLoyaltyAnalytics(): void {
    // Placeholder for cache invalidation
    console.log("Invalidating loyalty analytics cache");
  }
}

// Export singleton instance
export const loyaltyService = new LoyaltyService();
export default loyaltyService;
