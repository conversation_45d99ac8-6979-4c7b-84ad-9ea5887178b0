# Thermal Printing System - Dependencies and Setup Guide

## Prerequisites

### Development Environment
- **React Native**: 0.70+ (tested with 0.76.1)
- **Node.js**: 18+ 
- **Android SDK**: API Level 24+ (Android 7.0+)
- **iOS**: 12.0+ (if supporting iOS)

### Hardware Requirements
- **Bluetooth Thermal Printers**: ESC/POS compatible
- **USB Thermal Printers**: Android devices with USB Host support
- **Network Thermal Printers**: WiFi/Ethernet enabled printers

## Core Dependencies

### 1. Primary Printing Libraries

```bash
# Install thermal printing libraries
npm install @tumihub/react-native-thermal-receipt-printer@^1.0.4
npm install react-native-bluetooth-escpos-printer@^0.0.5
```

**Library Details:**
- `@tumihub/react-native-thermal-receipt-printer`: USB and Network printer support
- `react-native-bluetooth-escpos-printer`: Bluetooth ESC/POS printer support

### 2. Supporting Dependencies

```bash
# Storage and state management
npm install @react-native-async-storage/async-storage@^2.1.2

# QR Code and image handling
npm install react-native-qrcode-svg@^6.3.15
npm install react-native-view-shot@^4.0.3
npm install react-native-svg@^15.11.2

# File system and sharing
npm install react-native-fs@^2.20.0
npm install react-native-share@^12.0.9

# PDF generation (optional)
npm install react-native-html-to-pdf@^0.12.0

# Base64 encoding
npm install react-native-base64@^0.2.1
```

### 3. UI Dependencies (if using React Native Paper)

```bash
npm install react-native-paper@^5.13.1
npm install react-native-vector-icons@^10.2.0
npm install react-native-safe-area-context@^5.3.0
```

## Platform-Specific Setup

### Android Configuration

#### 1. Permissions (`android/app/src/main/AndroidManifest.xml`)

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Bluetooth Permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    
    <!-- Android 12+ Bluetooth Permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    
    <!-- Location Permissions (required for Bluetooth scanning) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- USB Printer Support -->
    <uses-feature android:name="android.hardware.usb.host" />
    
    <!-- Network Printer Support -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    
    <!-- Storage Permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="29" />
    
    <!-- Android 13+ Media Permissions -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
</manifest>
```

#### 2. Gradle Configuration (`android/app/build.gradle`)

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 34
        // ... other config
    }
}

dependencies {
    // Ensure these are included for proper Bluetooth support
    implementation 'androidx.core:core:1.8.0'
    implementation 'androidx.appcompat:appcompat:1.5.0'
}
```

#### 3. ProGuard Rules (`android/app/proguard-rules.pro`)

```proguard
# Keep thermal printer classes
-keep class cn.jystudio.bluetooth.** { *; }
-keep class com.tumihub.** { *; }

# Keep React Native classes
-keep class com.facebook.react.** { *; }
```

### iOS Configuration (if supporting iOS)

#### 1. Info.plist Permissions

```xml
<key>NSBluetoothAlwaysUsageDescription</key>
<string>This app needs Bluetooth access to connect to thermal printers</string>
<key>NSBluetoothPeripheralUsageDescription</key>
<string>This app needs Bluetooth access to connect to thermal printers</string>
```

#### 2. Podfile Configuration

```ruby
platform :ios, '12.0'

target 'YourApp' do
  # ... other pods
  
  # Required for thermal printing
  pod 'RNFS', :path => '../node_modules/react-native-fs'
  pod 'react-native-share', :path => '../node_modules/react-native-share'
end
```

## Library Patches

### Required Patches

The project includes custom patches for compatibility:

#### 1. Bluetooth ESC/POS Printer Patch

```bash
# Apply patch after npm install
npx patch-package react-native-bluetooth-escpos-printer
```

**Patch file**: `patches/react-native-bluetooth-escpos-printer+0.0.5.patch`

#### 2. Thermal Receipt Printer Patch

```bash
# Apply patch after npm install
npx patch-package @tumihub/react-native-thermal-receipt-printer
```

**Patch file**: `patches/@tumihub+react-native-thermal-receipt-printer+1.0.4.patch`

### Auto-apply Patches

Add to `package.json`:

```json
{
  "scripts": {
    "postinstall": "patch-package"
  },
  "devDependencies": {
    "patch-package": "^8.0.0"
  }
}
```

## Metro Configuration

### metro.config.js

```javascript
const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  resolver: {
    assetExts: [...defaultConfig.resolver.assetExts, 'svg'],
  },
  transformer: {
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
};

module.exports = mergeConfig(defaultConfig, config);
```

## Build Configuration

### Android Build Setup

#### 1. Enable Multidex (if needed)

```gradle
// android/app/build.gradle
android {
    defaultConfig {
        multiDexEnabled true
    }
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'
}
```

#### 2. Increase Heap Size

```gradle
// android/gradle.properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
```

### Build Scripts

Add to `package.json`:

```json
{
  "scripts": {
    "android:clean": "cd android && ./gradlew clean",
    "android:build": "cd android && ./gradlew assembleRelease",
    "ios:clean": "cd ios && xcodebuild clean",
    "reset": "npx react-native start --reset-cache"
  }
}
```

## Environment Variables

### API Configuration

Create `.env` file:

```env
# Printer Configuration
DEFAULT_PRINTER_TYPE=ble
PRINTER_TIMEOUT=30000
MAX_RETRY_ATTEMPTS=3

# Debug Settings
ENABLE_PRINTER_LOGS=true
ENABLE_OFFLINE_STORAGE=true
```

## Supported Printer Models

### Bluetooth Printers
- **Xprinter**: XP-58, XP-80, XP-365B
- **Epson**: TM-T20, TM-T82, TM-T88
- **Star Micronics**: TSP100, TSP650
- **Bixolon**: SPP-R200, SPP-R300
- **Generic ESC/POS**: Most 58mm and 80mm thermal printers

### USB Printers
- **Android Only**: Requires USB Host support
- **ESC/POS Compatible**: Most thermal receipt printers

### Network Printers
- **WiFi/Ethernet**: IP-based thermal printers
- **Port 9100**: Standard thermal printer port

## Installation Verification

### Quick Setup Test

```bash
# 1. Install dependencies
npm install

# 2. Apply patches
npx patch-package

# 3. Clean and rebuild
npm run android:clean
npm run android

# 4. Test printer functionality
# Use the TestReceiptScreen in the app
```

### Common Issues and Solutions

#### 1. Bluetooth Permission Issues
```bash
# Check Android version compatibility
adb shell getprop ro.build.version.sdk
```

#### 2. USB Printer Not Detected
```bash
# Check USB host support
adb shell pm list features | grep usb
```

#### 3. Network Printer Connection
```bash
# Test network connectivity
ping [printer_ip]
telnet [printer_ip] 9100
```

## Development Tools

### Debug Configuration

```javascript
// Enable debug logging
import { LogBox } from 'react-native';

LogBox.ignoreLogs([
  'new NativeEventEmitter',
  'Bluetooth',
]);

// Enable printer debug mode
const PRINTER_DEBUG = __DEV__;
```

### Testing Tools

```bash
# Install development dependencies
npm install --save-dev @types/react-native
npm install --save-dev jest
npm install --save-dev react-test-renderer
```

This setup guide provides all necessary dependencies and configurations for implementing the thermal printing system in a new React Native project.
