import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
// import "expo-dev-client";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { LogBox } from "react-native";
import "react-native-reanimated";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { Provider } from "react-redux";

import { AppProviders } from "@/src/components/providers/AppProviders";
import { SessionProvider } from "@/src/contexts/AuthContext";
import { CheckoutFlowProvider } from "@/src/contexts/CheckoutFlowContext";
import { CustomerProvider } from "@/src/contexts/CustomerContext";
import { LocationProvider } from "@/src/contexts/LocationContext";
import { NavigationProvider } from "@/src/contexts/NavigationContext";
import { SalesAgentProvider } from "@/src/contexts/SalesAgentContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { UserPreferencesProvider } from "@/src/contexts/UserPreferencesContext";
import { UserSwitchingProvider } from "@/src/contexts/UserSwitchingContext";
import { store } from "@/src/store";
import { initializeStore } from "@/src/store/initialization";
import {
  NotoSerifJP_400Regular,
  NotoSerifJP_600SemiBold,
  NotoSerifJP_700Bold,
} from "@expo-google-fonts/noto-serif-jp";
import {
  Poppins_400Regular,
  Poppins_500Medium,
  Poppins_600SemiBold,
  Poppins_700Bold,
} from "@expo-google-fonts/poppins";

// Suppress thermal printing library warnings globally
LogBox.ignoreLogs([
  "new NativeEventEmitter()", // Ignore NativeEventEmitter warnings from thermal printing libraries
  "EventEmitter.removeListener", // Ignore removeListener warnings
  "Require cycle:", // Ignore require cycle warnings during development
]);

// Inner component that uses the custom theme
function ThemedRootLayout() {
  const { isDark } = useTheme();

  return (
    <NavigationProvider>
      <ThemeProvider value={isDark ? DarkTheme : DefaultTheme}>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="pos-login" options={{ headerShown: false }} />
          <Stack.Screen name="auth" options={{ headerShown: false }} />
          <Stack.Screen name="customer-list" options={{ headerShown: false }} />
          <Stack.Screen
            name="customer-details"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="location-selection"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="payment-method-selection"
            options={{ headerShown: false }}
          />
          <Stack.Screen name="checkout" options={{ headerShown: false }} />
          <Stack.Screen
            name="payment-processing"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ticket-management"
            options={{ headerShown: false }}
          />
          <Stack.Screen name="settings" options={{ headerShown: false }} />
          <Stack.Screen name="order-receipt" options={{ headerShown: false }} />
          <Stack.Screen
            name="thermal-printer-setup"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="thermal-printer-test"
            options={{ headerShown: false }}
          />
          <Stack.Screen name="staff-list" options={{ headerShown: false }} />
          <Stack.Screen name="staff-details" options={{ headerShown: false }} />
          <Stack.Screen name="staff-create" options={{ headerShown: false }} />
          <Stack.Screen
            name="sales-agent-list"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="sales-agent-details"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="sales-agent-create"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="fulfillment-management"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="fulfillment-details"
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="discount-management"
            options={{ headerShown: false }}
          />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style={isDark ? "light" : "dark"} />
      </ThemeProvider>
    </NavigationProvider>
  );
}

// Wrapper component that provides theme context
function ThemedRootLayoutWrapper() {
  return (
    <AppProviders>
      <ThemedRootLayout />
    </AppProviders>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    Poppins_400Regular,
    Poppins_500Medium,
    Poppins_600SemiBold,
    Poppins_700Bold,
    NotoSerifJP_400Regular,
    NotoSerifJP_600SemiBold,
    NotoSerifJP_700Bold,
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  // Initialize store with ticket mode enabled
  initializeStore();

  return (
    <SafeAreaProvider>
      <Provider store={store}>
        <UserPreferencesProvider>
          <SessionProvider>
            <UserSwitchingProvider>
              <CustomerProvider>
                <SalesAgentProvider>
                  <LocationProvider>
                    <CheckoutFlowProvider>
                      <ThemedRootLayoutWrapper />
                    </CheckoutFlowProvider>
                  </LocationProvider>
                </SalesAgentProvider>
              </CustomerProvider>
            </UserSwitchingProvider>
          </SessionProvider>
        </UserPreferencesProvider>
      </Provider>
    </SafeAreaProvider>
  );
}
