import { GridCard } from "@/components/ui/GridCard";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { PinVerificationModal } from "@/components/ui/PinVerificationModal";
import { Colors } from "@/constants/Colors";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";

import { useSession } from "@/src/contexts/AuthContext";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { usePinVerification } from "@/src/hooks/usePinVerification";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useAppSelector } from "@/src/store";
import { useRouter } from "expo-router";
import React, { useEffect } from "react";
import { ScrollView, StyleSheet, Text, View, Platform } from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import {
  ResponsiveHeading2,
  ResponsiveCaption,
} from "@/src/components/typography/ResponsiveText";
import { ResponsiveGrid } from "@/src/components/layout/ResponsiveContainer";

export default function DashboardScreen() {
  const router = useRouter();
  const { isPosAuthenticated, user } = useSession();
  const { setCurrentTitle } = useNavigation();
  const cartItems = useAppSelector((state) => state.cart.items);
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const responsiveLayout = useResponsiveLayout();
  const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
  const gridColumns = responsiveLayout?.gridColumns || 1;

  // RBAC permissions
  const { canManageStaff, canManageSystem, hasPermission } = useRBAC();

  // PIN verification hook
  const {
    isVisible: showPinVerification,
    title: pinTitle,
    description: pinDescription,
    executePendingAction,
    closePinVerification,
    navigateWithPinVerification,
  } = usePinVerification();

  // Set page title
  useEffect(() => {
    setCurrentTitle("Dashboard");
  }, [setCurrentTitle]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isPosAuthenticated) {
      router.replace("/pos-login");
    }
  }, [isPosAuthenticated, router]);

  if (!isPosAuthenticated) {
    return null; // Show nothing while redirecting
  }

  const cartItemCount = cartItems.reduce(
    (sum: any, item: { quantity: any }) => sum + item.quantity,
    0
  );

  // Base dashboard items (always visible) - with PIN verification
  const baseDashboardItems = [
    {
      title: "Products",
      subtitle: "Browse & Add to Cart",
      icon: (
        <IconSymbol name="bag.fill" size={24} color={Colors.light.primary} />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/(tabs)/products"),
          "Products"
        ),
    },
    {
      title: "Cart",
      subtitle: "Review & Checkout",
      icon: (
        <IconSymbol name="cart.fill" size={24} color={Colors.light.primary} />
      ),
      onPress: () =>
        navigateWithPinVerification(() => router.push("/(tabs)/cart"), "Cart"),
      badge: cartItemCount > 0 ? cartItemCount : undefined,
    },
    {
      title: "Tickets",
      subtitle: "Manage Multiple Orders",
      icon: (
        <IconSymbol name="ticket.fill" size={24} color={Colors.light.primary} />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/ticket-management"),
          "Tickets"
        ),
    },
    {
      title: "Orders",
      subtitle: "View Order History",
      icon: (
        <IconSymbol name="list.bullet" size={24} color={Colors.light.primary} />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/(tabs)/orders"),
          "Orders"
        ),
    },
    {
      title: "Customers",
      subtitle: "Manage Customers",
      icon: (
        <IconSymbol
          name="person.2.fill"
          size={24}
          color={Colors.light.primary}
        />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/customer-list"),
          "Customers"
        ),
    },
    {
      title: "Sales Agents",
      subtitle: "Commission Tracking",
      icon: (
        <IconSymbol
          name="person.badge.plus"
          size={24}
          color={Colors.light.primary}
        />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/sales-agent-list"),
          "Sales Agents"
        ),
    },
  ];

  // RBAC-based management items
  const managementItems = [];

  if (canManageStaff) {
    managementItems.push({
      title: "Staff Management",
      subtitle: "Manage Staff & Permissions",
      icon: (
        <IconSymbol
          name="person.3.fill"
          size={24}
          color={Colors.light.primary}
        />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/staff-list"),
          "Staff Management"
        ),
    });

    managementItems.push({
      title: "Sales Agent Management",
      subtitle: "Manage Sales Agents",
      icon: (
        <IconSymbol
          name="person.badge.plus.fill"
          size={24}
          color={Colors.light.primary}
        />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/sales-agent-list"),
          "Sales Agent Management"
        ),
    });
  }

  // Add loyalty management for users with loyalty permissions
  if (hasPermission("view_loyalty") || hasPermission("manage_loyalty")) {
    managementItems.push({
      title: "Loyalty Management",
      subtitle: "Customer Loyalty & Rewards",
      icon: (
        <IconSymbol name="star.fill" size={24} color={Colors.light.primary} />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/loyalty-management"),
          "Loyalty Management"
        ),
    });
  }

  // Add discount management for users with discount permissions
  if (hasPermission("apply_discounts") || hasPermission("manage_discounts")) {
    managementItems.push({
      title: "Discount Management",
      subtitle: "Discount Rules & Analytics",
      icon: (
        <IconSymbol name="tag.fill" size={24} color={Colors.light.primary} />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/discount-management"),
          "Discount Management"
        ),
    });
  }

  // Add fulfillment management for users with fulfillment permissions
  if (hasPermission("view_fulfillments")) {
    managementItems.push({
      title: "Fulfillment Management",
      subtitle: "Orders & Delivery Tracking",
      icon: (
        <IconSymbol
          name="shippingbox.fill"
          size={24}
          color={Colors.light.primary}
        />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/fulfillment-management"),
          "Fulfillment Management"
        ),
    });
  }

  // Add shipping rates management for managers
  if (hasPermission("manage_shipping_rates")) {
    managementItems.push({
      title: "Shipping Management",
      subtitle: "Delivery Rates & Methods",
      icon: (
        <IconSymbol
          name="truck.box.fill"
          size={24}
          color={Colors.light.primary}
        />
      ),
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/fulfillment-management?tab=shipping"),
          "Shipping Management"
        ),
    });
  }

  if (canManageSystem) {
    managementItems.push({
      title: "System Settings",
      subtitle: "System Configuration",
      icon: <IconSymbol name="gear" size={24} color={Colors.light.primary} />,
      onPress: () =>
        navigateWithPinVerification(
          () => router.push("/settings"),
          "System Settings"
        ),
    });
  }

  // Combine all dashboard items
  const dashboardItems = [...baseDashboardItems, ...managementItems];

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <View
        style={[
          styles.header,
          {
            paddingHorizontal: Spacing.lg * spacingMultiplier,
            paddingVertical: Spacing.lg * spacingMultiplier,
          },
        ]}
      >
        <View>
          <ResponsiveHeading2 color="primary">
            Welcome back, {user?.name || "User"}
          </ResponsiveHeading2>
          <ResponsiveCaption>Dukalink POS Dashboard</ResponsiveCaption>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <ResponsiveGrid
          spacing={Spacing.sm * spacingMultiplier}
          minItemWidth={180}
        >
          {dashboardItems.map((item, index) => (
            <GridCard
              key={index}
              title={item.title}
              subtitle={item.subtitle}
              icon={item.icon}
              onPress={item.onPress}
              badge={item.badge}
            />
          ))}
        </ResponsiveGrid>
      </ScrollView>

      {/* Enhanced PIN Verification Modal with User Switching */}
      <PinVerificationModal
        visible={showPinVerification}
        onClose={closePinVerification}
        onSuccess={() => {
          // Execute the pending action from the hook
          // since the actual verification was already done in the modal
          executePendingAction();
        }}
        title={pinTitle}
        description={pinDescription}
        allowUserSwitching={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // Add layout stability constraints
    minHeight: 0,
    maxHeight: "100%",
    overflow: "hidden",
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  content: {
    flex: 1,
    // Prevent content expansion
    minHeight: 0,
    maxHeight: "100%",
  },
  scrollContent: {
    padding: Spacing.md,
  },
});
