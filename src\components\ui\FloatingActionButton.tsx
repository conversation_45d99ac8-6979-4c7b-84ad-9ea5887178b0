/**
 * Reusable Floating Action Button (FAB) Component
 * 
 * Follows the existing design pattern from customer selection screen
 * with proper theming and accessibility support
 */

import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TouchableOpacityProps,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTheme } from '@/src/contexts/ThemeContext';

interface FloatingActionButtonProps extends TouchableOpacityProps {
  /** Icon name to display in the FAB */
  iconName?: string;
  /** Icon size (default: 24) */
  iconSize?: number;
  /** Icon color (default: white) */
  iconColor?: string;
  /** FAB background color (default: primary theme color) */
  backgroundColor?: string;
  /** Custom style for the FAB */
  style?: ViewStyle;
  /** Position from bottom (default: theme.spacing.xl) */
  bottom?: number;
  /** Position from right (default: theme.spacing.xl) */
  right?: number;
  /** FAB size (default: 56) */
  size?: number;
  /** Whether to show shadow (default: true) */
  showShadow?: boolean;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  iconName = 'plus',
  iconSize = 24,
  iconColor = 'white',
  backgroundColor,
  style,
  bottom,
  right,
  size = 56,
  showShadow = true,
  ...touchableProps
}) => {
  const theme = useTheme();
  const primaryColor = useThemeColor({}, 'primary');
  
  const fabBackgroundColor = backgroundColor || primaryColor;
  const fabBottom = bottom ?? theme.spacing.xl;
  const fabRight = right ?? theme.spacing.xl;
  const borderRadius = size / 2;

  const fabStyle: ViewStyle = {
    position: 'absolute',
    bottom: fabBottom,
    right: fabRight,
    width: size,
    height: size,
    borderRadius,
    backgroundColor: fabBackgroundColor,
    alignItems: 'center',
    justifyContent: 'center',
    ...(showShadow && {
      elevation: 8,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
    }),
  };

  return (
    <TouchableOpacity
      style={[fabStyle, style]}
      activeOpacity={0.8}
      accessibilityRole="button"
      accessibilityLabel="Add new item"
      {...touchableProps}
    >
      <IconSymbol 
        name={iconName as any} 
        size={iconSize} 
        color={iconColor} 
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
