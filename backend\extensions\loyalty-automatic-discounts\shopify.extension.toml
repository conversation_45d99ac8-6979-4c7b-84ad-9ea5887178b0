api_version = "2025-04"

[[extensions]]
name = "Loyalty Automatic Discounts"
handle = "loyalty-automatic-discounts"
type = "function"
description = "Automatic loyalty-based discounts for tier benefits and special promotions"

  [[extensions.targeting]]
  target = "purchase.cart-lines.discounts.generate.run"
  input_query = "src/run.graphql"
  export = "run"

  [extensions.build]
  command = "cargo build --target=wasm32-wasip1 --release"
  path = "target/wasm32-wasip1/release/loyalty-automatic-discounts.wasm"
  watch = [ "src/**/*.rs" ]

  [extensions.ui.paths]
  create = "/"
  details = "/"
