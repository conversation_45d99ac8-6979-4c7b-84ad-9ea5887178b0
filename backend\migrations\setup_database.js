/**
 * DUKALINK POS DATABASE SETUP
 *
 * This is the ONLY migration script you need to run.
 * It will:
 * 1. Create all required tables
 * 2. Seed all users with proper passwords
 * 3. Seed all sales agents
 * 4. Set up all permissions
 *
 * Usage: npm run setup
 */

require("dotenv").config();
const mysql = require("mysql2/promise");
const bcrypt = require("bcrypt");

class DatabaseSetup {
  constructor() {
    this.connection = null;
  }

  async connect() {
    try {
      console.log("🔌 Connecting to MySQL...");
      this.connection = await mysql.createConnection({
        host: process.env.DB_HOST || "localhost",
        user: process.env.DB_USER || "dukalink",
        password: process.env.DB_PASSWORD || "dukalink_secure_password_2024",
        database: process.env.DB_NAME || "dukalink_shopify_pos",
        multipleStatements: true,
      });
      console.log("✅ Connected to MySQL database");
      return true;
    } catch (error) {
      console.error("❌ Failed to connect to MySQL:", error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log("🔌 Disconnected from MySQL");
    }
  }

  async dropAllTables() {
    console.log("🗑️ Dropping existing tables...");

    // Disable foreign key checks temporarily
    await this.connection.execute("SET FOREIGN_KEY_CHECKS = 0");

    const dropTables = [
      "DROP TABLE IF EXISTS fulfillment_audit_log",
      "DROP TABLE IF EXISTS shipping_fees",
      "DROP TABLE IF EXISTS fulfillments",
      "DROP TABLE IF EXISTS shipping_rates",
      "DROP TABLE IF EXISTS loyalty_redemptions",
      "DROP TABLE IF EXISTS loyalty_transactions",
      "DROP TABLE IF EXISTS staff_discount_permissions",
      "DROP TABLE IF EXISTS discount_usage_log",
      "DROP TABLE IF EXISTS discount_rules",
      "DROP TABLE IF EXISTS customer_loyalty",
      "DROP TABLE IF EXISTS ticket_audit_log",
      "DROP TABLE IF EXISTS pos_security_events",
      "DROP TABLE IF EXISTS pos_user_switches",
      "DROP TABLE IF EXISTS ticket_discounts",
      "DROP TABLE IF EXISTS ticket_items",
      "DROP TABLE IF EXISTS pos_tickets",
      "DROP TABLE IF EXISTS payment_audit_log",
      "DROP TABLE IF EXISTS credit_payments",
      "DROP TABLE IF EXISTS payment_methods_used",
      "DROP TABLE IF EXISTS payment_transactions",
      "DROP TABLE IF EXISTS staff_permissions",
      "DROP TABLE IF EXISTS pos_sessions",
      "DROP TABLE IF EXISTS pos_staff",
      "DROP TABLE IF EXISTS agent_customers",
      "DROP TABLE IF EXISTS agent_performance_history",
      "DROP TABLE IF EXISTS sales_agents",
      "DROP TABLE IF EXISTS pos_terminals",
    ];

    for (const dropSql of dropTables) {
      try {
        await this.connection.execute(dropSql);
      } catch (error) {
        console.log(`⚠️ ${dropSql} - ${error.message}`);
      }
    }

    // Re-enable foreign key checks
    await this.connection.execute("SET FOREIGN_KEY_CHECKS = 1");

    console.log("✅ All tables dropped");
  }

  async createSalesAgentTables() {
    console.log("🏗️ Creating sales agent tables...");

    // Create sales_agents table
    await this.connection.execute(`
      CREATE TABLE sales_agents (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(50),
        commission_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
        active BOOLEAN DEFAULT TRUE,
        territory VARCHAR(255),
        region VARCHAR(255),
        join_date DATE,
        total_sales DECIMAL(12,2) DEFAULT 0.00,
        total_commission DECIMAL(12,2) DEFAULT 0.00,
        customer_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_sales_agents_email (email),
        INDEX idx_sales_agents_active (active),
        INDEX idx_sales_agents_territory (territory),
        INDEX idx_sales_agents_region (region),
        INDEX idx_sales_agents_commission (commission_rate)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create agent_customers table
    await this.connection.execute(`
      CREATE TABLE agent_customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        agent_id VARCHAR(50) NOT NULL,
        customer_id VARCHAR(255) NOT NULL,
        linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        linked_by VARCHAR(50),
        
        FOREIGN KEY (agent_id) REFERENCES sales_agents(id) ON DELETE CASCADE,
        UNIQUE KEY unique_agent_customer (agent_id, customer_id),
        
        INDEX idx_agent_customers_agent (agent_id),
        INDEX idx_agent_customers_customer (customer_id),
        INDEX idx_agent_customers_linked (linked_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create agent_performance_history table
    await this.connection.execute(`
      CREATE TABLE agent_performance_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        agent_id VARCHAR(50) NOT NULL,
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        sales_count INT DEFAULT 0,
        sales_amount DECIMAL(12,2) DEFAULT 0.00,
        commission_earned DECIMAL(12,2) DEFAULT 0.00,
        customers_acquired INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (agent_id) REFERENCES sales_agents(id) ON DELETE CASCADE,
        INDEX idx_performance_agent (agent_id),
        INDEX idx_performance_period (period_start, period_end)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ Sales agent tables created");
  }

  async createStaffTables() {
    console.log("🏗️ Creating staff tables...");

    // Create pos_staff table
    await this.connection.execute(`
      CREATE TABLE pos_staff (
        id VARCHAR(255) PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE,
        role ENUM('cashier', 'manager', 'company_admin', 'super_admin') NOT NULL DEFAULT 'cashier',
        store_id VARCHAR(255) DEFAULT 'default-store',
        commission_rate DECIMAL(5,2) DEFAULT 2.50,
        is_active TINYINT(1) DEFAULT 1,
        shopify_staff_id VARCHAR(255),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        failed_login_attempts INT DEFAULT 0,
        locked_until DATETIME NULL,
        last_login DATETIME NULL,
        pin VARCHAR(255) NULL,
        pin_set_at DATETIME NULL,
        pin_set_by VARCHAR(255) NULL,
        pin_attempts INT DEFAULT 0,
        pin_locked_until DATETIME NULL,
        last_pin_used DATETIME NULL,

        INDEX idx_pos_staff_username (username),
        INDEX idx_pos_staff_role (role),
        INDEX idx_pos_staff_store (store_id),
        INDEX idx_pos_staff_active (is_active),
        INDEX idx_pos_staff_last_login (last_login),
        INDEX idx_pos_staff_failed_attempts (failed_login_attempts),
        INDEX idx_pos_staff_locked_until (locked_until)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create staff_permissions table
    await this.connection.execute(`
      CREATE TABLE staff_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        staff_id VARCHAR(255) NOT NULL,
        permission VARCHAR(100) NOT NULL,
        granted_by VARCHAR(255),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,
        FOREIGN KEY (granted_by) REFERENCES pos_staff(id) ON DELETE SET NULL,
        UNIQUE KEY unique_staff_permission (staff_id, permission),
        
        INDEX idx_staff_permissions_staff (staff_id),
        INDEX idx_staff_permissions_permission (permission)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create pos_sessions table
    await this.connection.execute(`
      CREATE TABLE pos_sessions (
        id VARCHAR(255) PRIMARY KEY,
        staff_id VARCHAR(255) NOT NULL,
        token_hash VARCHAR(255) NOT NULL,
        terminal_id VARCHAR(255),
        location_id VARCHAR(255),
        ip_address VARCHAR(45),
        user_agent TEXT,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,
        
        INDEX idx_pos_sessions_staff (staff_id),
        INDEX idx_pos_sessions_token (token_hash),
        INDEX idx_pos_sessions_expires (expires_at),
        INDEX idx_pos_sessions_terminal (terminal_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Add foreign key constraint for pin_set_by (self-referencing)
    await this.connection.execute(`
      ALTER TABLE pos_staff
      ADD CONSTRAINT fk_pos_staff_pin_set_by
      FOREIGN KEY (pin_set_by) REFERENCES pos_staff(id) ON DELETE SET NULL
    `);

    console.log("✅ Staff tables created");
  }

  async createTicketTables() {
    console.log("🏗️ Creating ticket management tables...");

    // Create pos_tickets table
    await this.connection.execute(`
      CREATE TABLE pos_tickets (
        id VARCHAR(255) PRIMARY KEY,
        staff_id VARCHAR(255) NOT NULL,
        terminal_id VARCHAR(255),
        location_id VARCHAR(255),
        name VARCHAR(255) DEFAULT 'New Ticket',
        customer_id VARCHAR(255),
        sales_agent_id VARCHAR(255),
        subtotal DECIMAL(10,2) DEFAULT 0.00,
        tax DECIMAL(10,2) DEFAULT 0.00,
        total DECIMAL(10,2) DEFAULT 0.00,
        discount_total DECIMAL(10,2) DEFAULT 0.00,
        note TEXT,
        status ENUM('active', 'paused', 'completed', 'cancelled', 'expired', 'archived') DEFAULT 'active',
        expires_at DATETIME NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        completed_at DATETIME NULL,
        last_auto_save DATETIME NULL,

        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,
        FOREIGN KEY (sales_agent_id) REFERENCES sales_agents(id) ON DELETE SET NULL,

        INDEX idx_tickets_staff (staff_id),
        INDEX idx_tickets_terminal (terminal_id),
        INDEX idx_tickets_location (location_id),
        INDEX idx_tickets_status (status),
        INDEX idx_tickets_created (created_at),
        INDEX idx_tickets_expires (expires_at),
        INDEX idx_tickets_customer (customer_id),
        INDEX idx_tickets_agent (sales_agent_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create ticket_items table
    await this.connection.execute(`
      CREATE TABLE ticket_items (
        id VARCHAR(255) PRIMARY KEY,
        ticket_id VARCHAR(255) NOT NULL,
        variant_id VARCHAR(255) NOT NULL,
        product_id VARCHAR(255) NOT NULL,
        title VARCHAR(255) NOT NULL,
        variant_title VARCHAR(255),
        sku VARCHAR(255),
        price DECIMAL(10,2) NOT NULL,
        quantity INT NOT NULL DEFAULT 1,
        line_total DECIMAL(10,2) NOT NULL,
        discount_amount DECIMAL(10,2) DEFAULT 0.00,
        discount_type ENUM('percentage', 'fixed_amount') NULL,
        notes TEXT,
        inventory_quantity INT DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (ticket_id) REFERENCES pos_tickets(id) ON DELETE CASCADE,

        INDEX idx_ticket_items_ticket (ticket_id),
        INDEX idx_ticket_items_variant (variant_id),
        INDEX idx_ticket_items_product (product_id),
        INDEX idx_ticket_items_sku (sku)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create ticket_discounts table
    await this.connection.execute(`
      CREATE TABLE ticket_discounts (
        id VARCHAR(255) PRIMARY KEY,
        ticket_id VARCHAR(255) NOT NULL,
        code VARCHAR(255),
        title VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        type ENUM('percentage', 'fixed_amount') NOT NULL,
        description TEXT,
        applied_by VARCHAR(255),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (ticket_id) REFERENCES pos_tickets(id) ON DELETE CASCADE,
        FOREIGN KEY (applied_by) REFERENCES pos_staff(id) ON DELETE SET NULL,

        INDEX idx_ticket_discounts_ticket (ticket_id),
        INDEX idx_ticket_discounts_code (code),
        INDEX idx_ticket_discounts_type (type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ Ticket management tables created");
  }

  async createPaymentSystemTables() {
    console.log("🏗️ Creating payment system tables...");

    // Create payment_transactions table
    await this.connection.execute(`
      CREATE TABLE payment_transactions (
        id VARCHAR(255) PRIMARY KEY,
        order_id VARCHAR(255),
        shopify_order_id VARCHAR(255),
        staff_id VARCHAR(255) NOT NULL,
        customer_id VARCHAR(255),
        terminal_id VARCHAR(255),
        location_id VARCHAR(255),
        total_amount DECIMAL(12,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'KES',
        payment_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
        is_split_payment BOOLEAN DEFAULT FALSE,
        payment_count INT DEFAULT 1,
        completed_payments INT DEFAULT 0,
        remaining_amount DECIMAL(12,2),
        notes TEXT,
        metadata JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        completed_at DATETIME NULL,

        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE RESTRICT,

        INDEX idx_payment_transactions_order (order_id),
        INDEX idx_payment_transactions_shopify (shopify_order_id),
        INDEX idx_payment_transactions_staff (staff_id),
        INDEX idx_payment_transactions_status (payment_status),
        INDEX idx_payment_transactions_created (created_at),
        INDEX idx_payment_transactions_split (is_split_payment)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create payment_methods_used table
    await this.connection.execute(`
      CREATE TABLE payment_methods_used (
        id VARCHAR(255) PRIMARY KEY,
        transaction_id VARCHAR(255) NOT NULL,
        method_type ENUM('cash', 'mpesa', 'absa_till', 'card', 'credit') NOT NULL,
        method_name VARCHAR(100) NOT NULL,
        amount DECIMAL(12,2) NOT NULL,
        amount_tendered DECIMAL(12,2),
        change_amount DECIMAL(12,2) DEFAULT 0.00,
        reference_code VARCHAR(255),
        transaction_code VARCHAR(255),
        status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
        error_message TEXT,
        processed_at DATETIME,
        metadata JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (transaction_id) REFERENCES payment_transactions(id) ON DELETE CASCADE,

        INDEX idx_payment_methods_transaction (transaction_id),
        INDEX idx_payment_methods_type (method_type),
        INDEX idx_payment_methods_status (status),
        INDEX idx_payment_methods_reference (reference_code),
        INDEX idx_payment_methods_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create credit_payments table
    await this.connection.execute(`
      CREATE TABLE credit_payments (
        id VARCHAR(255) PRIMARY KEY,
        payment_method_id VARCHAR(255) NOT NULL,
        customer_id VARCHAR(255),
        customer_name VARCHAR(255) NOT NULL,
        customer_phone VARCHAR(20),
        credit_limit DECIMAL(12,2) DEFAULT 0.00,
        outstanding_balance DECIMAL(12,2) DEFAULT 0.00,
        due_date DATE,
        payment_terms VARCHAR(100),
        notes TEXT,
        status ENUM('active', 'paid', 'overdue', 'cancelled') DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        paid_at DATETIME NULL,

        FOREIGN KEY (payment_method_id) REFERENCES payment_methods_used(id) ON DELETE CASCADE,

        INDEX idx_credit_payments_customer (customer_id),
        INDEX idx_credit_payments_phone (customer_phone),
        INDEX idx_credit_payments_status (status),
        INDEX idx_credit_payments_due_date (due_date),
        INDEX idx_credit_payments_outstanding (outstanding_balance)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create payment_audit_log table
    await this.connection.execute(`
      CREATE TABLE payment_audit_log (
        id VARCHAR(255) PRIMARY KEY,
        transaction_id VARCHAR(255),
        payment_method_id VARCHAR(255),
        staff_id VARCHAR(255) NOT NULL,
        action ENUM('initiated', 'method_added', 'method_processed', 'method_failed', 'completed', 'cancelled', 'refunded') NOT NULL,
        old_status VARCHAR(50),
        new_status VARCHAR(50),
        amount DECIMAL(12,2),
        details JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (transaction_id) REFERENCES payment_transactions(id) ON DELETE CASCADE,
        FOREIGN KEY (payment_method_id) REFERENCES payment_methods_used(id) ON DELETE CASCADE,
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE RESTRICT,

        INDEX idx_payment_audit_transaction (transaction_id),
        INDEX idx_payment_audit_staff (staff_id),
        INDEX idx_payment_audit_action (action),
        INDEX idx_payment_audit_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ Payment system tables created");
  }

  async createUserSwitchingTables() {
    console.log("🏗️ Creating user switching and audit tables...");

    // Create pos_user_switches table
    await this.connection.execute(`
      CREATE TABLE pos_user_switches (
        id VARCHAR(255) PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL,
        from_staff_id VARCHAR(255),
        to_staff_id VARCHAR(255) NOT NULL,
        terminal_id VARCHAR(255),
        location_id VARCHAR(255),
        switch_reason VARCHAR(255),
        switched_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        switched_back_at DATETIME NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,

        FOREIGN KEY (session_id) REFERENCES pos_sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (from_staff_id) REFERENCES pos_staff(id) ON DELETE SET NULL,
        FOREIGN KEY (to_staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,

        INDEX idx_switches_session (session_id),
        INDEX idx_switches_from_staff (from_staff_id),
        INDEX idx_switches_to_staff (to_staff_id),
        INDEX idx_switches_terminal (terminal_id),
        INDEX idx_switches_switched_at (switched_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create pos_security_events table for audit trail
    await this.connection.execute(`
      CREATE TABLE pos_security_events (
        id VARCHAR(255) PRIMARY KEY,
        staff_id VARCHAR(255),
        event_type VARCHAR(100) NOT NULL,
        event_data JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE SET NULL,

        INDEX idx_security_events_staff (staff_id),
        INDEX idx_security_events_type (event_type),
        INDEX idx_security_events_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create ticket_audit_log table for tracking ticket modifications
    await this.connection.execute(`
      CREATE TABLE ticket_audit_log (
        id VARCHAR(255) PRIMARY KEY,
        ticket_id VARCHAR(255) NOT NULL,
        staff_id VARCHAR(255) NOT NULL,
        action ENUM('created', 'updated', 'item_added', 'item_removed', 'item_updated', 'discount_applied', 'discount_removed', 'completed', 'cancelled', 'expired', 'archived', 'auto_saved') NOT NULL,
        details JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (ticket_id) REFERENCES pos_tickets(id) ON DELETE CASCADE,
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,

        INDEX idx_audit_ticket (ticket_id),
        INDEX idx_audit_staff (staff_id),
        INDEX idx_audit_action (action),
        INDEX idx_audit_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ User switching and audit tables created");
  }

  async createLoyaltyTables() {
    console.log("🏗️ Creating customer loyalty tables...");

    // Create customer_loyalty table
    await this.connection.execute(`
      CREATE TABLE customer_loyalty (
        id VARCHAR(255) PRIMARY KEY,
        shopify_customer_id VARCHAR(255) NOT NULL,
        shopify_store_id VARCHAR(255) NOT NULL DEFAULT 'default-store',
        total_purchases DECIMAL(12,2) DEFAULT 0.00,
        total_orders INT DEFAULT 0,
        loyalty_points INT DEFAULT 0,
        loyalty_tier ENUM('bronze', 'silver', 'gold', 'platinum') DEFAULT 'bronze',
        tier_updated_at DATETIME NULL,
        last_purchase_at DATETIME NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        UNIQUE KEY unique_customer_store (shopify_customer_id, shopify_store_id),
        INDEX idx_loyalty_customer (shopify_customer_id),
        INDEX idx_loyalty_store (shopify_store_id),
        INDEX idx_loyalty_tier (loyalty_tier),
        INDEX idx_loyalty_points (loyalty_points),
        INDEX idx_loyalty_purchases (total_purchases),
        INDEX idx_loyalty_updated (updated_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create loyalty_transactions table
    await this.connection.execute(`
      CREATE TABLE loyalty_transactions (
        id VARCHAR(255) PRIMARY KEY,
        customer_loyalty_id VARCHAR(255) NOT NULL,
        shopify_customer_id VARCHAR(255) NOT NULL,
        transaction_type ENUM('earned', 'redeemed', 'expired', 'adjusted', 'bonus') NOT NULL,
        points_amount INT NOT NULL,
        order_id VARCHAR(255) NULL,
        order_total DECIMAL(10,2) NULL,
        description TEXT,
        staff_id VARCHAR(255) NULL,
        sales_agent_id VARCHAR(255) NULL,
        expires_at DATETIME NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (customer_loyalty_id) REFERENCES customer_loyalty(id) ON DELETE CASCADE,
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE SET NULL,
        FOREIGN KEY (sales_agent_id) REFERENCES sales_agents(id) ON DELETE SET NULL,

        INDEX idx_loyalty_trans_customer (customer_loyalty_id),
        INDEX idx_loyalty_trans_shopify_customer (shopify_customer_id),
        INDEX idx_loyalty_trans_type (transaction_type),
        INDEX idx_loyalty_trans_order (order_id),
        INDEX idx_loyalty_trans_staff (staff_id),
        INDEX idx_loyalty_trans_agent (sales_agent_id),
        INDEX idx_loyalty_trans_created (created_at),
        INDEX idx_loyalty_trans_expires (expires_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create loyalty_redemptions table
    await this.connection.execute(`
      CREATE TABLE loyalty_redemptions (
        id VARCHAR(255) PRIMARY KEY,
        customer_loyalty_id VARCHAR(255) NOT NULL,
        shopify_customer_id VARCHAR(255) NOT NULL,
        points_redeemed INT NOT NULL,
        discount_amount DECIMAL(10,2) NOT NULL,
        order_id VARCHAR(255) NULL,
        ticket_id VARCHAR(255) NULL,
        redemption_type ENUM('discount', 'cash_equivalent', 'product') DEFAULT 'discount',
        status ENUM('pending', 'applied', 'cancelled', 'expired') DEFAULT 'pending',
        staff_id VARCHAR(255) NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        applied_at DATETIME NULL,

        FOREIGN KEY (customer_loyalty_id) REFERENCES customer_loyalty(id) ON DELETE CASCADE,
        FOREIGN KEY (ticket_id) REFERENCES pos_tickets(id) ON DELETE SET NULL,
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE SET NULL,

        INDEX idx_loyalty_redemptions_customer (customer_loyalty_id),
        INDEX idx_loyalty_redemptions_shopify_customer (shopify_customer_id),
        INDEX idx_loyalty_redemptions_order (order_id),
        INDEX idx_loyalty_redemptions_ticket (ticket_id),
        INDEX idx_loyalty_redemptions_status (status),
        INDEX idx_loyalty_redemptions_staff (staff_id),
        INDEX idx_loyalty_redemptions_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ Customer loyalty tables created");
  }

  async createDiscountTables() {
    console.log("🏗️ Creating advanced discount tables...");

    // Create discount_rules table
    await this.connection.execute(`
      CREATE TABLE discount_rules (
        id VARCHAR(255) PRIMARY KEY,
        shopify_store_id VARCHAR(255) NOT NULL DEFAULT 'default-store',
        name VARCHAR(255) NOT NULL,
        description TEXT,
        discount_type ENUM('percentage', 'fixed_amount', 'bogo', 'loyalty_points') NOT NULL,
        discount_value DECIMAL(10,2) NOT NULL,
        min_purchase_amount DECIMAL(10,2) DEFAULT 0.00,
        max_discount_amount DECIMAL(10,2) NULL,
        applicable_products JSON NULL,
        excluded_products JSON NULL,
        customer_eligibility ENUM('all', 'loyalty_tier', 'specific_customers') DEFAULT 'all',
        loyalty_tier_required ENUM('bronze', 'silver', 'gold', 'platinum') NULL,
        max_uses_per_customer INT NULL,
        max_uses_total INT NULL,
        current_uses INT DEFAULT 0,
        valid_from DATETIME NOT NULL,
        valid_to DATETIME NULL,
        is_active BOOLEAN DEFAULT true,
        created_by VARCHAR(255) NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (created_by) REFERENCES pos_staff(id) ON DELETE SET NULL,

        INDEX idx_discount_rules_store (shopify_store_id),
        INDEX idx_discount_rules_type (discount_type),
        INDEX idx_discount_rules_active (is_active),
        INDEX idx_discount_rules_valid_from (valid_from),
        INDEX idx_discount_rules_valid_to (valid_to),
        INDEX idx_discount_rules_loyalty_tier (loyalty_tier_required),
        INDEX idx_discount_rules_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create staff_discount_permissions table
    await this.connection.execute(`
      CREATE TABLE staff_discount_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        staff_id VARCHAR(255) NOT NULL,
        discount_rule_id VARCHAR(255) NOT NULL,
        permission_type ENUM('apply', 'modify', 'view') NOT NULL,
        max_discount_percentage DECIMAL(5,2) NULL,
        max_discount_amount DECIMAL(10,2) NULL,
        granted_by VARCHAR(255) NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,
        FOREIGN KEY (discount_rule_id) REFERENCES discount_rules(id) ON DELETE CASCADE,
        FOREIGN KEY (granted_by) REFERENCES pos_staff(id) ON DELETE SET NULL,

        UNIQUE KEY unique_staff_discount_permission (staff_id, discount_rule_id, permission_type),
        INDEX idx_staff_discount_perms_staff (staff_id),
        INDEX idx_staff_discount_perms_rule (discount_rule_id),
        INDEX idx_staff_discount_perms_type (permission_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create discount_usage_log table
    await this.connection.execute(`
      CREATE TABLE discount_usage_log (
        id VARCHAR(255) PRIMARY KEY,
        discount_rule_id VARCHAR(255) NOT NULL,
        shopify_customer_id VARCHAR(255) NULL,
        order_id VARCHAR(255) NULL,
        ticket_id VARCHAR(255) NULL,
        discount_amount DECIMAL(10,2) NOT NULL,
        original_amount DECIMAL(10,2) NOT NULL,
        staff_id VARCHAR(255) NOT NULL,
        sales_agent_id VARCHAR(255) NULL,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (discount_rule_id) REFERENCES discount_rules(id) ON DELETE CASCADE,
        FOREIGN KEY (ticket_id) REFERENCES pos_tickets(id) ON DELETE SET NULL,
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,
        FOREIGN KEY (sales_agent_id) REFERENCES sales_agents(id) ON DELETE SET NULL,

        INDEX idx_discount_usage_rule (discount_rule_id),
        INDEX idx_discount_usage_customer (shopify_customer_id),
        INDEX idx_discount_usage_order (order_id),
        INDEX idx_discount_usage_ticket (ticket_id),
        INDEX idx_discount_usage_staff (staff_id),
        INDEX idx_discount_usage_agent (sales_agent_id),
        INDEX idx_discount_usage_applied (applied_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ Advanced discount tables created");
  }

  async seedStaff() {
    console.log("🌱 Seeding staff users...");

    try {
      await this.connection.beginTransaction();

      // Hash passwords
      const passwordHash = await bcrypt.hash("password123", 10);
      const managerPasswordHash = await bcrypt.hash("manager123", 10);
      const companyAdminPasswordHash = await bcrypt.hash("company123", 10);
      const adminPasswordHash = await bcrypt.hash("admin123", 10);

      // Insert staff members
      const staffData = [
        {
          id: "pos-001",
          username: "cashier1",
          password_hash: passwordHash,
          name: "John Cashier",
          email: "<EMAIL>",
          role: "cashier",
          commission_rate: 3.0,
          permissions: [
            "read_products",
            "create_orders",
            "read_customers",
            "process_payments",
          ],
        },
        {
          id: "pos-002",
          username: "manager1",
          password_hash: managerPasswordHash,
          name: "Jane Manager",
          email: "<EMAIL>",
          role: "manager",
          commission_rate: 5.0,
          permissions: [
            "read_products",
            "create_orders",
            "read_customers",
            "process_payments",
            "manage_inventory",
            "view_reports",
            "view_staff",
            "manage_staff",
            "view_staff_performance",
            "manage_discounts",
            "view_analytics",
          ],
        },
        {
          id: "pos-003",
          username: "companyadmin1",
          password_hash: companyAdminPasswordHash,
          name: "Company Admin",
          email: "<EMAIL>",
          role: "company_admin",
          commission_rate: 0.0,
          permissions: [
            // All manager permissions
            "read_products",
            "create_orders",
            "read_customers",
            "process_payments",
            "manage_inventory",
            "view_reports",
            "view_staff",
            "manage_staff",
            "view_staff_performance",
            "manage_discounts",
            "view_analytics",
            // Elevated company admin permissions
            "manage_locations",
            "manage_sales_agents",
            "manage_system_settings",
            "view_advanced_reports",
            "manage_terminals",
          ],
        },
        {
          id: "pos-004",
          username: "admin1",
          password_hash: adminPasswordHash,
          name: "Admin User",
          email: "<EMAIL>",
          role: "super_admin",
          commission_rate: 0.0,
          permissions: [
            "read_products",
            "create_orders",
            "read_customers",
            "process_payments",
            "manage_inventory",
            "view_reports",
            "view_staff",
            "manage_staff",
            "view_staff_performance",
            "manage_discounts",
            "view_analytics",
            "manage_system",
            "manage_roles",
            "manage_permissions",
            "view_audit_logs",
            "manage_integrations",
          ],
        },
      ];

      for (const staff of staffData) {
        // Insert staff member
        await this.connection.execute(
          `
          INSERT INTO pos_staff (
            id, username, password_hash, name, email, role,
            store_id, commission_rate, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
          [
            staff.id,
            staff.username,
            staff.password_hash,
            staff.name,
            staff.email,
            staff.role,
            "default-store",
            staff.commission_rate,
            true,
          ]
        );

        // Insert permissions
        for (const permission of staff.permissions) {
          await this.connection.execute(
            `
            INSERT INTO staff_permissions (staff_id, permission) VALUES (?, ?)
          `,
            [staff.id, permission]
          );
        }
      }

      await this.connection.commit();
      console.log("✅ Staff users seeded successfully");
      console.log("   • cashier1 (password123) - Role: cashier");
      console.log("   • manager1 (manager123) - Role: manager");
      console.log("   • companyadmin1 (company123) - Role: company_admin");
      console.log("   • admin1 (admin123) - Role: super_admin");
    } catch (error) {
      await this.connection.rollback();
      console.error("❌ Error seeding staff:", error);
      throw error;
    }
  }

  async seedPins() {
    console.log("🔐 Seeding staff PINs...");

    try {
      const bcrypt = require("bcrypt");

      // Default PINs (will be hashed)
      const defaultPins = {
        "pos-001": "1234", // cashier1
        "pos-002": "5678", // manager1
        "pos-003": "7777", // companyadmin1
        "pos-004": "9999", // admin1
      };

      for (const [staffId, pin] of Object.entries(defaultPins)) {
        // Hash the PIN
        const hashedPin = await bcrypt.hash(pin, 10);

        await this.connection.execute(
          `
          UPDATE pos_staff
          SET pin = ?, pin_set_at = NOW(), pin_set_by = 'pos-004'
          WHERE id = ?
        `,
          [hashedPin, staffId]
        );
      }

      console.log("✅ Staff PINs seeded successfully");
      console.log("   • cashier1 PIN: 1234");
      console.log("   • manager1 PIN: 5678");
      console.log("   • companyadmin1 PIN: 7777");
      console.log("   • admin1 PIN: 9999");
    } catch (error) {
      console.error("❌ Error seeding PINs:", error);
      throw error;
    }
  }

  async seedEnhancedPermissions() {
    console.log("🔐 Adding enhanced permissions for advanced features...");

    const enhancedPermissions = [
      "manage_tickets",
      "view_all_tickets",
      "switch_users",
      "view_audit_logs",
      "manage_cart_discounts",
      "override_inventory_limits",
      "access_dashboard",
      "manage_customers",
      "manage_discounts",
      "manage_inventory",
      "manage_locations",
      "manage_sales_agents",
      "manage_staff",
      "manage_terminals",
      "process_orders",
      "view_reports",
      // Fulfillment Management Permissions
      "view_fulfillments",
      "manage_fulfillments",
      "create_fulfillments",
      "update_fulfillment_status",
      "manage_shipping_rates",
      "view_fulfillment_reports",
      "calculate_shipping_fees",
    ];

    // Enhanced role permissions mapping
    const rolePermissions = {
      cashier: [
        "access_dashboard",
        "manage_customers",
        "process_orders",
        "view_fulfillments",
        "create_fulfillments",
      ],
      manager: [
        "access_dashboard",
        "manage_customers",
        "manage_discounts",
        "manage_inventory",
        "manage_staff",
        "process_orders",
        "view_reports",
        "manage_tickets",
        "view_all_tickets",
        "manage_cart_discounts",
        // Fulfillment permissions for managers
        "view_fulfillments",
        "manage_fulfillments",
        "create_fulfillments",
        "update_fulfillment_status",
        "manage_shipping_rates",
        "view_fulfillment_reports",
        "calculate_shipping_fees",
      ],
      company_admin: [
        "access_dashboard",
        "manage_customers",
        "manage_discounts",
        "manage_inventory",
        "manage_locations",
        "manage_sales_agents",
        "manage_staff",
        "manage_terminals",
        "process_orders",
        "view_reports",
        "manage_tickets",
        "view_all_tickets",
        "switch_users",
        "manage_cart_discounts",
        "override_inventory_limits",
        // Fulfillment permissions for company admins
        "view_fulfillments",
        "manage_fulfillments",
        "create_fulfillments",
        "update_fulfillment_status",
        "manage_shipping_rates",
        "view_fulfillment_reports",
        "calculate_shipping_fees",
      ],
      super_admin: enhancedPermissions, // All permissions
    };

    for (const [role, permissions] of Object.entries(rolePermissions)) {
      // Get staff members with this role
      const [staffMembers] = await this.connection.execute(
        `SELECT id FROM pos_staff WHERE role = ?`,
        [role]
      );

      for (const staff of staffMembers) {
        for (const permission of permissions) {
          try {
            await this.connection.execute(
              `INSERT IGNORE INTO staff_permissions (staff_id, permission) VALUES (?, ?)`,
              [staff.id, permission]
            );
          } catch (error) {
            // Ignore duplicate key errors
            if (!error.message.includes("Duplicate entry")) {
              throw error;
            }
          }
        }
      }
    }

    console.log("✅ Enhanced permissions seeded successfully");
  }

  async seedSalesAgents() {
    console.log("🌱 Seeding sales agents...");

    const salesAgents = [
      {
        id: "agent-001",
        name: "Alice Johnson",
        email: "<EMAIL>",
        phone: "+254700111111",
        commission_rate: 8.0,
        active: true,
        territory: "Nairobi Central",
        region: "Nairobi",
        join_date: "2024-01-15",
      },
      {
        id: "agent-002",
        name: "Bob Kimani",
        email: "<EMAIL>",
        phone: "+254700222222",
        commission_rate: 7.5,
        active: true,
        territory: "Westlands",
        region: "Nairobi",
        join_date: "2024-02-01",
      },
      {
        id: "agent-003",
        name: "Carol Wanjiku",
        email: "<EMAIL>",
        phone: "+254700333333",
        commission_rate: 9.0,
        active: true,
        territory: "Karen",
        region: "Nairobi",
        join_date: "2023-12-01",
      },
      {
        id: "agent-004",
        name: "David Mwangi",
        email: "<EMAIL>",
        phone: "+254700444444",
        commission_rate: 6.5,
        active: false,
        territory: "Mombasa",
        region: "Coast",
        join_date: "2024-01-01",
      },
    ];

    for (const agent of salesAgents) {
      await this.connection.execute(
        `
        INSERT INTO sales_agents (
          id, name, email, phone, commission_rate, active, territory, region,
          join_date, total_sales, total_commission, customer_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          agent.id,
          agent.name,
          agent.email,
          agent.phone,
          agent.commission_rate,
          agent.active,
          agent.territory,
          agent.region,
          agent.join_date,
          0.0,
          0.0,
          0,
        ]
      );
    }

    // Insert sample customer relationship
    await this.connection.execute(`
      INSERT INTO agent_customers (agent_id, customer_id, linked_at)
      VALUES ('agent-001', '8095960301705', '2024-01-15 00:00:00')
    `);

    console.log("✅ Sales agents seeded successfully");
    console.log("   • 4 sales agents created");
    console.log("   • 1 customer relationship linked");
  }

  async seedLoyaltyData() {
    console.log("🌱 Seeding loyalty system data...");

    // Create sample customer loyalty records
    const loyaltyRecords = [
      {
        id: "loyalty-001",
        shopify_customer_id: "8095960301705",
        total_purchases: 2500.0,
        total_orders: 15,
        loyalty_points: 250,
        loyalty_tier: "gold",
        tier_updated_at: "2024-01-15 10:00:00",
        last_purchase_at: "2024-01-20 14:30:00",
      },
      {
        id: "loyalty-002",
        shopify_customer_id: "8095960301706",
        total_purchases: 850.0,
        total_orders: 8,
        loyalty_points: 85,
        loyalty_tier: "silver",
        tier_updated_at: "2024-01-10 09:00:00",
        last_purchase_at: "2024-01-18 16:45:00",
      },
      {
        id: "loyalty-003",
        shopify_customer_id: "8095960301707",
        total_purchases: 350.0,
        total_orders: 3,
        loyalty_points: 35,
        loyalty_tier: "bronze",
        tier_updated_at: "2024-01-05 11:00:00",
        last_purchase_at: "2024-01-15 12:20:00",
      },
    ];

    for (const record of loyaltyRecords) {
      await this.connection.execute(
        `
        INSERT INTO customer_loyalty (
          id, shopify_customer_id, shopify_store_id, total_purchases, total_orders,
          loyalty_points, loyalty_tier, tier_updated_at, last_purchase_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          record.id,
          record.shopify_customer_id,
          "default-store",
          record.total_purchases,
          record.total_orders,
          record.loyalty_points,
          record.loyalty_tier,
          record.tier_updated_at,
          record.last_purchase_at,
        ]
      );
    }

    // Create sample loyalty transactions
    const transactions = [
      {
        id: "trans-001",
        customer_loyalty_id: "loyalty-001",
        shopify_customer_id: "8095960301705",
        transaction_type: "earned",
        points_amount: 50,
        order_id: "order-001",
        order_total: 500.0,
        description: "Points earned from purchase",
        staff_id: "pos-001",
      },
      {
        id: "trans-002",
        customer_loyalty_id: "loyalty-001",
        shopify_customer_id: "8095960301705",
        transaction_type: "redeemed",
        points_amount: -25,
        order_id: "order-002",
        order_total: 250.0,
        description: "Points redeemed for discount",
        staff_id: "pos-002",
      },
    ];

    for (const trans of transactions) {
      await this.connection.execute(
        `
        INSERT INTO loyalty_transactions (
          id, customer_loyalty_id, shopify_customer_id, transaction_type, points_amount,
          order_id, order_total, description, staff_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          trans.id,
          trans.customer_loyalty_id,
          trans.shopify_customer_id,
          trans.transaction_type,
          trans.points_amount,
          trans.order_id,
          trans.order_total,
          trans.description,
          trans.staff_id,
        ]
      );
    }

    console.log("✅ Loyalty system data seeded successfully");
    console.log("   • 3 customer loyalty records created");
    console.log("   • 2 loyalty transactions created");
  }

  async seedDiscountRules() {
    console.log("🌱 Seeding discount rules...");

    const discountRules = [
      {
        id: "discount-001",
        name: "Gold Member Discount",
        description: "10% discount for gold tier loyalty members",
        discount_type: "percentage",
        discount_value: 10.0,
        min_purchase_amount: 100.0,
        customer_eligibility: "loyalty_tier",
        loyalty_tier_required: "gold",
        max_uses_per_customer: null,
        valid_from: "2024-01-01 00:00:00",
        valid_to: "2024-12-31 23:59:59",
        created_by: "pos-002",
      },
      {
        id: "discount-002",
        name: "Staff Discount",
        description: "5% staff discount for all purchases",
        discount_type: "percentage",
        discount_value: 5.0,
        min_purchase_amount: 0.0,
        customer_eligibility: "all",
        max_uses_per_customer: null,
        valid_from: "2024-01-01 00:00:00",
        valid_to: null,
        created_by: "pos-003",
      },
      {
        id: "discount-003",
        name: "Loyalty Points Redemption",
        description: "Redeem loyalty points for discounts",
        discount_type: "loyalty_points",
        discount_value: 1.0,
        min_purchase_amount: 50.0,
        customer_eligibility: "all",
        valid_from: "2024-01-01 00:00:00",
        valid_to: null,
        created_by: "pos-002",
      },
    ];

    for (const rule of discountRules) {
      await this.connection.execute(
        `
        INSERT INTO discount_rules (
          id, shopify_store_id, name, description, discount_type, discount_value,
          min_purchase_amount, customer_eligibility, loyalty_tier_required,
          max_uses_per_customer, valid_from, valid_to, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          rule.id,
          "default-store",
          rule.name,
          rule.description,
          rule.discount_type,
          rule.discount_value,
          rule.min_purchase_amount,
          rule.customer_eligibility,
          rule.loyalty_tier_required || null,
          rule.max_uses_per_customer || null,
          rule.valid_from,
          rule.valid_to || null,
          rule.created_by,
        ]
      );
    }

    // Create staff discount permissions
    const permissions = [
      {
        staff_id: "pos-002",
        discount_rule_id: "discount-001",
        permission_type: "apply",
        granted_by: "pos-003",
      },
      {
        staff_id: "pos-002",
        discount_rule_id: "discount-002",
        permission_type: "apply",
        granted_by: "pos-003",
      },
      {
        staff_id: "pos-001",
        discount_rule_id: "discount-002",
        permission_type: "apply",
        granted_by: "pos-002",
      },
      {
        staff_id: "pos-003",
        discount_rule_id: "discount-001",
        permission_type: "modify",
        granted_by: "pos-004",
      },
      {
        staff_id: "pos-003",
        discount_rule_id: "discount-002",
        permission_type: "modify",
        granted_by: "pos-004",
      },
    ];

    for (const perm of permissions) {
      await this.connection.execute(
        `
        INSERT INTO staff_discount_permissions (
          staff_id, discount_rule_id, permission_type, granted_by
        ) VALUES (?, ?, ?, ?)
      `,
        [
          perm.staff_id,
          perm.discount_rule_id,
          perm.permission_type,
          perm.granted_by,
        ]
      );
    }

    console.log("✅ Discount rules seeded successfully");
    console.log("   • 3 discount rules created");
    console.log("   • 5 staff discount permissions created");
  }

  async createFulfillmentTables() {
    console.log("🏗️ Creating fulfillment management tables...");

    // Create shipping_rates table
    await this.connection.execute(`
      CREATE TABLE shipping_rates (
        id VARCHAR(255) PRIMARY KEY,
        delivery_method VARCHAR(100) NOT NULL,
        base_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        per_km_fee DECIMAL(10,2) NULL,
        per_kg_fee DECIMAL(10,2) NULL,
        minimum_fee DECIMAL(10,2) NULL,
        maximum_fee DECIMAL(10,2) NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_by_staff_id VARCHAR(255),
        updated_by_staff_id VARCHAR(255),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (created_by_staff_id) REFERENCES pos_staff(id) ON DELETE SET NULL,
        FOREIGN KEY (updated_by_staff_id) REFERENCES pos_staff(id) ON DELETE SET NULL,

        UNIQUE KEY unique_delivery_method (delivery_method),
        INDEX idx_shipping_rates_method (delivery_method),
        INDEX idx_shipping_rates_active (is_active),
        INDEX idx_shipping_rates_created_by (created_by_staff_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create fulfillments table
    await this.connection.execute(`
      CREATE TABLE fulfillments (
        id VARCHAR(255) PRIMARY KEY,
        order_id VARCHAR(255),
        shopify_order_id VARCHAR(255),
        staff_id VARCHAR(255) NOT NULL,
        delivery_address JSON,
        delivery_contact_name VARCHAR(255),
        delivery_contact_phone VARCHAR(50),
        delivery_instructions TEXT,
        delivery_method VARCHAR(100) DEFAULT 'standard',
        estimated_delivery_date DATE,
        shipping_fee DECIMAL(10,2) DEFAULT 0.00,
        shipping_fee_currency VARCHAR(3) DEFAULT 'KES',
        fulfillment_status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'failed') DEFAULT 'pending',
        shopify_fulfillment_id VARCHAR(255),
        tracking_number VARCHAR(255),
        tracking_url TEXT,
        carrier VARCHAR(100),
        last_updated_by VARCHAR(255),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE RESTRICT,
        FOREIGN KEY (last_updated_by) REFERENCES pos_staff(id) ON DELETE SET NULL,

        INDEX idx_fulfillments_order (order_id),
        INDEX idx_fulfillments_shopify_order (shopify_order_id),
        INDEX idx_fulfillments_staff (staff_id),
        INDEX idx_fulfillments_status (fulfillment_status),
        INDEX idx_fulfillments_method (delivery_method),
        INDEX idx_fulfillments_created (created_at),
        INDEX idx_fulfillments_tracking (tracking_number)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create shipping_fees table
    await this.connection.execute(`
      CREATE TABLE shipping_fees (
        id VARCHAR(255) PRIMARY KEY,
        fulfillment_id VARCHAR(255) NOT NULL,
        order_id VARCHAR(255),
        fee_amount DECIMAL(10,2) NOT NULL,
        fee_type VARCHAR(100) NOT NULL,
        currency VARCHAR(3) DEFAULT 'KES',
        applied_by_staff_id VARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (fulfillment_id) REFERENCES fulfillments(id) ON DELETE CASCADE,
        FOREIGN KEY (applied_by_staff_id) REFERENCES pos_staff(id) ON DELETE RESTRICT,

        INDEX idx_shipping_fees_fulfillment (fulfillment_id),
        INDEX idx_shipping_fees_order (order_id),
        INDEX idx_shipping_fees_type (fee_type),
        INDEX idx_shipping_fees_staff (applied_by_staff_id),
        INDEX idx_shipping_fees_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Create fulfillment_audit_log table
    await this.connection.execute(`
      CREATE TABLE fulfillment_audit_log (
        id VARCHAR(255) PRIMARY KEY,
        fulfillment_id VARCHAR(255) NOT NULL,
        staff_id VARCHAR(255) NOT NULL,
        action_type VARCHAR(100) NOT NULL,
        changes_made JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (fulfillment_id) REFERENCES fulfillments(id) ON DELETE CASCADE,
        FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE RESTRICT,

        INDEX idx_fulfillment_audit_fulfillment (fulfillment_id),
        INDEX idx_fulfillment_audit_staff (staff_id),
        INDEX idx_fulfillment_audit_action (action_type),
        INDEX idx_fulfillment_audit_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ Fulfillment management tables created");
  }

  async seedShippingRates() {
    console.log("🌱 Seeding default shipping rates...");

    const defaultRates = [
      {
        id: "rate-standard",
        delivery_method: "standard",
        base_fee: 200.0,
        description: "Standard delivery within Nairobi (3-5 business days)",
      },
      {
        id: "rate-express",
        delivery_method: "express",
        base_fee: 500.0,
        per_km_fee: 50.0,
        minimum_fee: 500.0,
        maximum_fee: 2000.0,
        description: "Express delivery within Nairobi (same day)",
      },
      {
        id: "rate-local",
        delivery_method: "local_pickup",
        base_fee: 0.0,
        description: "Customer pickup at store location",
      },
      {
        id: "rate-upcountry",
        delivery_method: "upcountry",
        base_fee: 800.0,
        per_km_fee: 10.0,
        per_kg_fee: 100.0,
        minimum_fee: 800.0,
        maximum_fee: 5000.0,
        description: "Delivery outside Nairobi (5-7 business days)",
      },
    ];

    for (const rate of defaultRates) {
      await this.connection.execute(
        `INSERT INTO shipping_rates (
          id, delivery_method, base_fee, per_km_fee, per_kg_fee,
          minimum_fee, maximum_fee, description, is_active,
          created_by_staff_id, updated_by_staff_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          rate.id,
          rate.delivery_method,
          rate.base_fee,
          rate.per_km_fee || null,
          rate.per_kg_fee || null,
          rate.minimum_fee || null,
          rate.maximum_fee || null,
          rate.description,
          true,
          "pos-004",
          "pos-004",
        ]
      );
    }

    console.log("✅ Default shipping rates seeded");
    console.log(
      "   • 4 shipping rates created (standard, express, local_pickup, upcountry)"
    );
  }

  async run() {
    try {
      console.log("🚀 Starting Dukalink POS Database Setup...\n");

      const connected = await this.connect();
      if (!connected) {
        throw new Error("Failed to connect to database");
      }

      await this.dropAllTables();
      await this.createSalesAgentTables();
      await this.createStaffTables();
      await this.createTicketTables();
      await this.createPaymentSystemTables();
      await this.createUserSwitchingTables();
      await this.createLoyaltyTables();
      await this.createDiscountTables();
      await this.createFulfillmentTables();
      await this.seedSalesAgents();
      await this.seedStaff();
      await this.seedPins();
      await this.seedEnhancedPermissions();
      await this.seedLoyaltyData();
      await this.seedDiscountRules();
      await this.seedShippingRates();

      // Verify setup
      console.log("\n🔍 Verifying setup...");
      const [tables] = await this.connection.execute("SHOW TABLES");
      console.log(
        "📋 Created tables:",
        tables.map((t) => Object.values(t)[0])
      );

      const [staffCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM pos_staff"
      );
      const [agentCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM sales_agents"
      );
      const [permissionCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM staff_permissions"
      );
      const [ticketCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM pos_tickets"
      );
      const [loyaltyCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM customer_loyalty"
      );
      const [discountRulesCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM discount_rules"
      );
      const [loyaltyTransCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM loyalty_transactions"
      );
      const [paymentTransCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM payment_transactions"
      );
      const [shippingRatesCount] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM shipping_rates"
      );

      console.log(`👥 Staff members: ${staffCount[0].count}`);
      console.log(`🤝 Sales agents: ${agentCount[0].count}`);
      console.log(`🔐 Permissions: ${permissionCount[0].count}`);
      console.log(`🎫 Tickets: ${ticketCount[0].count}`);
      console.log(`🏆 Loyalty customers: ${loyaltyCount[0].count}`);
      console.log(`💰 Discount rules: ${discountRulesCount[0].count}`);
      console.log(`📊 Loyalty transactions: ${loyaltyTransCount[0].count}`);
      console.log(`💳 Payment transactions: ${paymentTransCount[0].count}`);
      console.log(`🚚 Shipping rates: ${shippingRatesCount[0].count}`);

      console.log("\n🎉 Database setup completed successfully!");
      console.log("\n📝 Default Login Credentials:");
      console.log("   • admin1 / admin123 (super_admin) - PIN: 9999");
      console.log(
        "   • companyadmin1 / company123 (company_admin) - PIN: 7777"
      );
      console.log("   • manager1 / manager123 (manager) - PIN: 5678");
      console.log("   • cashier1 / password123 (cashier) - PIN: 1234");
      console.log("\n🚀 New Features Available:");
      console.log("   • Multi-session cart management (tickets)");
      console.log("   • Persistent cart storage & resume");
      console.log("   • Enhanced order editing with discounts");
      console.log("   • PIN-based user switching");
      console.log("   • Company admin role with elevated permissions");
      console.log("   • Comprehensive audit trail");
      console.log(
        "   • Multi-payment method support (Cash, M-Pesa, ABSA Till, Card, Credit)"
      );
      console.log("   • Split payment functionality");
      console.log("   • Credit payment tracking with customer profiles");
      console.log("   • Customer loyalty points system");
      console.log(
        "   • Tier-based loyalty benefits (bronze/silver/gold/platinum)"
      );
      console.log("   • Advanced discount rules with staff permissions");
      console.log("   • Loyalty points redemption");
      console.log("   • Commission-based discount integration");
      console.log("   • Comprehensive fulfillment management system");
      console.log(
        "   • Shipping fee calculation with multiple delivery methods"
      );
      console.log("   • Staff attribution for fulfillment operations");
      console.log("   • Delivery tracking and status management");
    } catch (error) {
      console.error("\n❌ Database setup failed:", error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// Run if called directly
if (require.main === module) {
  const setup = new DatabaseSetup();
  setup
    .run()
    .then(() => {
      console.log("\n✅ Setup process completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Setup process failed:", error);
      process.exit(1);
    });
}

module.exports = { DatabaseSetup };
