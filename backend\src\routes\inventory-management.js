const express = require('express');
const router = express.Router();
const inventoryService = require('../services/inventory-management-service');
const { authenticateToken, requirePermission } = require('../middleware/auth');

// Get inventory levels for specific variants
router.post('/inventory/levels', authenticateToken, async (req, res) => {
  try {
    const { variantIds, locationId } = req.body;
    
    if (!variantIds || !Array.isArray(variantIds) || variantIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Variant IDs array is required'
      });
    }
    
    const result = await inventoryService.getInventoryLevels(variantIds, locationId);
    
    if (result.success) {
      res.json({
        success: true,
        data: {
          inventory: result.inventory,
          message: `Inventory levels retrieved for ${variantIds.length} variants`
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get inventory levels error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get inventory levels'
    });
  }
});

// Validate inventory for order (pre-checkout validation)
router.post('/inventory/validate-order', authenticateToken, async (req, res) => {
  try {
    const { lineItems, locationId } = req.body;
    
    if (!lineItems || !Array.isArray(lineItems) || lineItems.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Line items array is required'
      });
    }
    
    // Validate line items format
    for (const item of lineItems) {
      if (!item.variantId || !item.quantity) {
        return res.status(400).json({
          success: false,
          error: 'Each line item must have variantId and quantity'
        });
      }
    }
    
    const result = await inventoryService.validateOrderInventory(lineItems, locationId);
    
    if (result.success) {
      res.json({
        success: true,
        data: {
          valid: result.valid,
          validationResults: result.validationResults,
          insufficientStock: result.insufficientStock,
          message: result.message
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Validate order inventory error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate inventory'
    });
  }
});

// Get low stock items
router.get('/inventory/low-stock', 
  authenticateToken, 
  requirePermission('manage_inventory'), 
  async (req, res) => {
    try {
      const { locationId, threshold } = req.query;
      
      const result = await inventoryService.checkLowStockItems(
        locationId, 
        threshold ? parseInt(threshold) : null
      );
      
      if (result.success) {
        res.json({
          success: true,
          data: {
            lowStockItems: result.lowStockItems,
            totalItems: result.totalItems,
            criticalItems: result.criticalItems,
            threshold: result.threshold,
            message: `Found ${result.totalItems} low stock items (${result.criticalItems} critical)`
          }
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error
        });
      }
    } catch (error) {
      console.error('Get low stock items error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get low stock items'
      });
    }
  }
);

// Adjust inventory quantity (manual adjustment)
router.post('/inventory/adjust', 
  authenticateToken, 
  requirePermission('manage_inventory'), 
  async (req, res) => {
    try {
      const { variantId, locationId, delta, reason } = req.body;
      
      if (!variantId || !locationId || delta === undefined) {
        return res.status(400).json({
          success: false,
          error: 'Variant ID, location ID, and delta are required'
        });
      }
      
      if (typeof delta !== 'number') {
        return res.status(400).json({
          success: false,
          error: 'Delta must be a number'
        });
      }
      
      const result = await inventoryService.adjustInventoryQuantity(
        variantId, 
        locationId, 
        delta, 
        reason || 'manual_adjustment'
      );
      
      if (result.success) {
        res.json({
          success: true,
          data: {
            adjustment: result.adjustment,
            message: result.message
          }
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error
        });
      }
    } catch (error) {
      console.error('Adjust inventory error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to adjust inventory'
      });
    }
  }
);

// Get inventory summary for dashboard
router.get('/inventory/summary', 
  authenticateToken, 
  requirePermission('view_reports'), 
  async (req, res) => {
    try {
      const { locationId } = req.query;
      
      const result = await inventoryService.getInventorySummary(locationId);
      
      if (result.success) {
        res.json({
          success: true,
          data: {
            summary: result.summary,
            message: 'Inventory summary retrieved successfully'
          }
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error
        });
      }
    } catch (error) {
      console.error('Get inventory summary error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get inventory summary'
      });
    }
  }
);

// Quick inventory check for a single variant (for real-time UI updates)
router.get('/inventory/check/:variantId', authenticateToken, async (req, res) => {
  try {
    const { variantId } = req.params;
    const { locationId } = req.query;
    
    const result = await inventoryService.getInventoryLevels([variantId], locationId);
    
    if (result.success) {
      const variantInventory = result.inventory[variantId];
      
      if (!variantInventory) {
        return res.status(404).json({
          success: false,
          error: 'Variant not found'
        });
      }
      
      const level = variantInventory.levels[0] || { available: 0, tracked: false };
      
      res.json({
        success: true,
        data: {
          variantId: variantId,
          available: level.available,
          tracked: variantInventory.tracked,
          locationId: level.locationId,
          locationName: level.locationName,
          inStock: !variantInventory.tracked || level.available > 0
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Quick inventory check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check inventory'
    });
  }
});

// Test inventory validation with sample data
router.post('/inventory/test-validation', authenticateToken, async (req, res) => {
  try {
    // Sample line items for testing
    const sampleLineItems = [
      {
        variantId: '44095923863689',
        quantity: 1,
        title: 'Test Product'
      }
    ];
    
    const { lineItems, locationId } = req.body;
    const testItems = lineItems || sampleLineItems;
    
    const result = await inventoryService.validateOrderInventory(testItems, locationId);
    
    res.json({
      success: true,
      data: {
        testItems: testItems,
        validation: result,
        message: 'Inventory validation test completed'
      }
    });
  } catch (error) {
    console.error('Test inventory validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test inventory validation'
    });
  }
});

module.exports = router;
