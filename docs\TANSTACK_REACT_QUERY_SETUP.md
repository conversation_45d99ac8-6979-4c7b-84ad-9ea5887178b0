# TanStack React Query Setup Guide

This document provides a comprehensive guide for using TanStack React Query in the Dukalink POS application.

## Overview

TanStack React Query has been properly configured with:
- ✅ QueryClient with React Native optimizations
- ✅ Network state awareness
- ✅ Proper error handling and retry logic
- ✅ Development tools (React Query Devtools)
- ✅ Centralized query key management
- ✅ Custom hooks for all major API operations
- ✅ Cache invalidation strategies

## Architecture

```
src/
├── providers/
│   └── QueryClientProvider.tsx     # Main QueryClient setup
├── lib/
│   └── queryKeys.ts               # Centralized query key factory
├── hooks/
│   └── queries/
│       ├── index.ts               # Export all query hooks
│       ├── useProducts.ts         # Product-related queries
│       ├── useCustomers.ts        # Customer-related queries
│       └── useLoyalty.ts          # Loyalty system queries
└── services/
    └── loyalty-order-completion.ts # Updated with new cache invalidation
```

## Configuration

### QueryClient Configuration

The QueryClient is configured with React Native optimizations:

```typescript
// Default options for all queries
queries: {
  staleTime: 1000 * 60 * 5,        // 5 minutes
  gcTime: 1000 * 60 * 30,          // 30 minutes
  retry: 2,                        // Retry failed requests 2 times
  refetchOnWindowFocus: true,      // Refetch on app focus
  refetchOnReconnect: true,        // Refetch when back online
  networkMode: 'online',           // Only run when online
}

// Default options for all mutations
mutations: {
  retry: 1,                        // Retry mutations once
  networkMode: 'online',           // Only run when online
}
```

### Network State Integration

Automatically detects network state changes using `@react-native-community/netinfo`:

```typescript
onlineManager.setEventListener((setOnline) => {
  return NetInfo.addEventListener((state) => {
    setOnline(!!state.isConnected);
  });
});
```

## Usage Examples

### 1. Basic Product Fetching

```typescript
import { useProducts } from '@/src/hooks/queries';

function ProductList() {
  const { data, isLoading, error, refetch } = useProducts({
    limit: 20,
    search: 'shirt'
  });

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <FlatList
      data={data?.products}
      renderItem={({ item }) => <ProductCard product={item} />}
      onRefresh={refetch}
      refreshing={isLoading}
    />
  );
}
```

### 2. Infinite Scrolling Products

```typescript
import { useInfiniteProducts } from '@/src/hooks/queries';

function InfiniteProductList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteProducts({ limit: 20 });

  const products = data?.pages.flatMap(page => page.products) ?? [];

  return (
    <FlatList
      data={products}
      renderItem={({ item }) => <ProductCard product={item} />}
      onEndReached={() => hasNextPage && fetchNextPage()}
      ListFooterComponent={
        isFetchingNextPage ? <LoadingSpinner /> : null
      }
    />
  );
}
```

### 3. Customer Management with Mutations

```typescript
import { useCustomers, useCreateCustomer, useUpdateCustomer } from '@/src/hooks/queries';

function CustomerManagement() {
  const { data: customers, isLoading } = useCustomers();
  const createCustomer = useCreateCustomer();
  const updateCustomer = useUpdateCustomer();

  const handleCreateCustomer = async (customerData) => {
    try {
      await createCustomer.mutateAsync(customerData);
      // Cache is automatically updated
      Alert.alert('Success', 'Customer created successfully');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  const handleUpdateCustomer = async (customerId, updates) => {
    try {
      await updateCustomer.mutateAsync({ id: customerId, ...updates });
      // Cache is automatically updated
      Alert.alert('Success', 'Customer updated successfully');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  return (
    <View>
      <FlatList
        data={customers?.customers}
        renderItem={({ item }) => (
          <CustomerCard 
            customer={item}
            onUpdate={(updates) => handleUpdateCustomer(item.id, updates)}
          />
        )}
      />
      <CreateCustomerButton onPress={handleCreateCustomer} />
    </View>
  );
}
```

### 4. Loyalty System Integration

```typescript
import { useCustomerLoyalty, useRedeemLoyaltyPoints } from '@/src/hooks/queries';

function LoyaltySection({ customerId }) {
  const { data: loyalty, isLoading } = useCustomerLoyalty(customerId);
  const redeemPoints = useRedeemLoyaltyPoints();

  const handleRedeemPoints = async (points) => {
    try {
      await redeemPoints.mutateAsync({
        customerId,
        redemptionData: { points, type: 'discount' }
      });
      // Loyalty cache is automatically invalidated
      Alert.alert('Success', 'Points redeemed successfully');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  if (isLoading) return <LoadingSpinner />;

  return (
    <View>
      <Text>Available Points: {loyalty?.availablePoints}</Text>
      <Text>Tier: {loyalty?.currentTier}</Text>
      <Button
        title="Redeem 100 Points"
        onPress={() => handleRedeemPoints(100)}
        disabled={redeemPoints.isPending}
      />
    </View>
  );
}
```

### 5. Prefetching for Better UX

```typescript
import { usePrefetchProducts, usePrefetchCustomers } from '@/src/hooks/queries';

function Dashboard() {
  const { prefetchProducts } = usePrefetchProducts();
  const { prefetchCustomers } = usePrefetchCustomers();

  useEffect(() => {
    // Prefetch data that users are likely to need
    prefetchProducts({ limit: 20 });
    prefetchCustomers({ limit: 50 });
  }, []);

  return <DashboardContent />;
}
```

## Query Key Management

All query keys are centrally managed in `src/lib/queryKeys.ts`:

```typescript
import { queryKeys } from '@/src/lib/queryKeys';

// Examples of query keys
queryKeys.products.all                    // ['products']
queryKeys.products.list({ search: 'shirt' }) // ['products', 'list', { search: 'shirt' }]
queryKeys.customers.detail('123')         // ['customers', 'detail', '123']
queryKeys.loyalty.customer('456')         // ['loyalty', 'customer', '456']
```

## Cache Invalidation

### Automatic Invalidation

Most mutations automatically invalidate related caches:

```typescript
// When a customer is updated, these caches are invalidated:
// - Customer details
// - Customer lists
// - Customer loyalty data
// - Customer order history
```

### Manual Invalidation

For custom invalidation scenarios:

```typescript
import { useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/src/lib/queryKeys';

function CustomComponent() {
  const queryClient = useQueryClient();

  const invalidateCustomerData = (customerId) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.customers.detail(customerId)
    });
  };

  return <YourComponent />;
}
```

## Development Tools

React Query Devtools are automatically included in development builds:

- Access via the floating button in the bottom-right corner
- View all queries, their status, and cached data
- Manually trigger refetches and invalidations
- Monitor network requests and cache updates

## Migration from Redux

### Before (Redux)
```typescript
const dispatch = useAppDispatch();
const { products, isLoading } = useAppSelector(state => state.products);

useEffect(() => {
  dispatch(fetchProducts());
}, []);
```

### After (React Query)
```typescript
const { data: products, isLoading } = useProducts();
// No useEffect needed - automatic fetching and caching
```

## Best Practices

1. **Use the provided hooks** instead of calling API directly
2. **Leverage prefetching** for better user experience
3. **Use optimistic updates** for immediate UI feedback
4. **Handle loading and error states** consistently
5. **Use query keys consistently** via the centralized factory
6. **Invalidate caches appropriately** after mutations

## Performance Benefits

- ✅ Automatic background refetching
- ✅ Intelligent caching with stale-while-revalidate
- ✅ Request deduplication
- ✅ Optimistic updates
- ✅ Offline support with network state awareness
- ✅ Memory management with garbage collection
- ✅ Reduced bundle size (compared to Redux + middleware)

## Next Steps

1. Gradually migrate existing Redux async thunks to React Query hooks
2. Implement optimistic updates for better UX
3. Add more specific query hooks as needed
4. Consider implementing infinite queries for large datasets
5. Add error boundaries for better error handling
