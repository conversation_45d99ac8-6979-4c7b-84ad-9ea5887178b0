import { useThemeColor } from "@/hooks/useThemeColor";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import ThermalPrintService, {
  ThermalPrinterDevice,
} from "../../services/ThermalPrintService";
import { BluetoothPermissionHelper } from "../../utils/BluetoothPermissionHelper";
import BluetoothTroubleshootingGuide from "./BluetoothTroubleshootingGuide";

export default function ThermalPrinterSetup() {
  const [printerType, setPrinterType] = useState<"ble" | "usb" | "net">("ble");
  const [devices, setDevices] = useState<ThermalPrinterDevice[]>([]);
  const [scanning, setScanning] = useState(false);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [connectedDevice, setConnectedDevice] =
    useState<ThermalPrinterDevice | null>(null);
  const [bluetoothEnabled, setBluetoothEnabled] = useState(false);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);
  const [showAllDevices, setShowAllDevices] = useState(false);

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");

  useEffect(() => {
    initializePrinterService();
    checkBluetoothStatus();
  }, []);

  useEffect(() => {
    // Update printer type when changed
    ThermalPrintService.setPrinterType(printerType);
  }, [printerType]);

  const initializePrinterService = async () => {
    try {
      await ThermalPrintService.init();
      const connected = ThermalPrintService.getConnectedDevice();
      if (connected) {
        setConnectedDevice(connected);
      }
    } catch (error) {
      console.error("Failed to initialize thermal printer service:", error);
    }
  };

  const checkBluetoothStatus = async () => {
    try {
      const isEnabled = await BluetoothPermissionHelper.isBluetoothEnabled();
      setBluetoothEnabled(isEnabled);
    } catch (error) {
      console.error("Failed to check Bluetooth status:", error);
    }
  };

  const handleScanDevices = async () => {
    if (printerType === "ble" && !bluetoothEnabled) {
      const enabled =
        await BluetoothPermissionHelper.showBluetoothEnableDialog();
      if (!enabled) {
        Alert.alert(
          "Bluetooth Required",
          "Please enable Bluetooth to scan for printers."
        );
        return;
      }
      setBluetoothEnabled(true);
    }

    // Check permissions for Bluetooth
    if (printerType === "ble") {
      const permissionResult =
        await BluetoothPermissionHelper.ensureBluetoothReady();
      if (!permissionResult.granted) {
        Alert.alert(
          "Permissions Required",
          permissionResult.error ||
            "Bluetooth permissions are required to scan for printers."
        );
        return;
      }
    }

    setScanning(true);
    setDevices([]); // Clear previous results

    try {
      let foundDevices;
      if (showAllDevices) {
        // Get all devices without filtering
        foundDevices = await ThermalPrintService.scanAllDevices();
      } else {
        // Get only printer devices (filtered)
        foundDevices = await ThermalPrintService.scanDevices();
      }

      setDevices(foundDevices);

      if (foundDevices.length === 0) {
        let message = "No devices found.";
        if (printerType === "ble") {
          if (showAllDevices) {
            message =
              "No Bluetooth devices found. Make sure Bluetooth is enabled and devices are discoverable.";
          } else {
            message =
              "No printer devices found. Try enabling 'Show All Devices' or pair your thermal printer in Android Bluetooth settings first.";
          }
        }
        Alert.alert("No Devices Found", message);
      }
    } catch (error) {
      console.error("Scan devices error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      Alert.alert(
        "Scan Error",
        `Failed to scan for devices: ${errorMessage}\n\nTip: For Bluetooth printers, make sure the printer is paired in Android Bluetooth settings first.`
      );
    } finally {
      setScanning(false);
    }
  };

  const handleConnectDevice = async (device: ThermalPrinterDevice) => {
    setConnecting(device.address);
    try {
      const success = await ThermalPrintService.connectToPrinter(
        device.address,
        device.port
      );
      if (success) {
        setConnectedDevice(device);
        Alert.alert("Success", `Connected to ${device.name}`);
      } else {
        Alert.alert("Connection Failed", `Failed to connect to ${device.name}`);
      }
    } catch (error) {
      console.error("Connect device error:", error);
      Alert.alert(
        "Connection Error",
        "Failed to connect to printer. Please try again."
      );
    } finally {
      setConnecting(null);
    }
  };

  const handleTestPrint = async () => {
    if (!connectedDevice) {
      Alert.alert("No Printer", "Please connect to a printer first.");
      return;
    }

    try {
      const result = await ThermalPrintService.printTestReceipt();
      if (result.success) {
        Alert.alert(
          "Test Print Successful",
          "Test receipt printed successfully!"
        );
      } else {
        Alert.alert(
          "Test Print Failed",
          result.error || "Failed to print test receipt."
        );
      }
    } catch (error) {
      console.error("Test print error:", error);
      Alert.alert("Test Print Error", "Failed to print test receipt.");
    }
  };

  const handleDisconnect = async () => {
    try {
      await ThermalPrintService.disconnect();
      setConnectedDevice(null);
      Alert.alert("Disconnected", "Printer disconnected successfully.");
    } catch (error) {
      console.error("Disconnect error:", error);
      Alert.alert("Disconnect Error", "Failed to disconnect printer.");
    }
  };

  const renderPrinterTypeSelector = () => (
    <View style={styles.cardContainer}>
      <Text style={styles.sectionTitle}>Printer Type</Text>
      <View style={styles.printerTypeContainer}>
        {[
          { key: "ble", label: "Bluetooth", icon: "bluetooth" },
          { key: "usb", label: "USB", icon: "hardware-chip" },
          { key: "net", label: "Network", icon: "wifi" },
        ].map((type) => (
          <TouchableOpacity
            key={type.key}
            style={[
              styles.printerTypeButton,
              printerType === type.key && styles.printerTypeButtonActive,
            ]}
            onPress={() => setPrinterType(type.key as "ble" | "usb" | "net")}
          >
            <Ionicons
              name={type.icon as any}
              size={24}
              color={printerType === type.key ? "#fff" : primaryColor}
            />
            <Text
              style={[
                styles.printerTypeText,
                printerType === type.key && styles.printerTypeTextActive,
              ]}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderBluetoothStatus = () => {
    if (printerType !== "ble") return null;

    return (
      <View style={styles.cardContainer}>
        <View style={styles.bluetoothStatus}>
          <Text style={styles.sectionTitle}>Bluetooth Status</Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusText}>
              {bluetoothEnabled ? "Enabled" : "Disabled"}
            </Text>
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: bluetoothEnabled ? "#4CAF50" : "#F44336" },
              ]}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderConnectedDevice = () => {
    if (!connectedDevice) return null;

    return (
      <View style={styles.cardContainer}>
        <Text style={styles.sectionTitle}>Connected Printer</Text>
        <View style={styles.connectedDevice}>
          <View style={styles.deviceInfo}>
            <Text style={styles.deviceName}>{connectedDevice.name}</Text>
            <Text style={styles.deviceAddress}>{connectedDevice.address}</Text>
          </View>
          <View style={styles.deviceActions}>
            <TouchableOpacity
              style={styles.testButton}
              onPress={handleTestPrint}
            >
              <Text style={styles.testButtonText}>Test Print</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.disconnectButton}
              onPress={handleDisconnect}
            >
              <Text style={styles.disconnectButtonText}>Disconnect</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary,
    primaryColor
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderPrinterTypeSelector()}
        {renderBluetoothStatus()}
        {renderConnectedDevice()}

        <View style={styles.cardContainer}>
          <View style={styles.scanHeader}>
            <Text style={styles.sectionTitle}>Available Devices</Text>
            <View style={styles.scanActions}>
              {printerType === "ble" && (
                <TouchableOpacity
                  style={styles.helpButton}
                  onPress={() => setShowTroubleshooting(true)}
                >
                  <Ionicons name="help-circle" size={20} color={primaryColor} />
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={[
                  styles.scanButton,
                  scanning && styles.scanButtonDisabled,
                ]}
                onPress={handleScanDevices}
                disabled={scanning}
              >
                {scanning ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Ionicons name="refresh" size={20} color="#fff" />
                )}
                <Text style={styles.scanButtonText}>
                  {scanning ? "Scanning..." : "Scan"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {printerType === "ble" && (
            <View style={styles.filterSection}>
              <View style={styles.filterRow}>
                <Text style={styles.filterLabel}>Show All Devices</Text>
                <Switch
                  value={showAllDevices}
                  onValueChange={setShowAllDevices}
                  trackColor={{
                    false: textSecondary,
                    true: primaryColor + "80",
                  }}
                  thumbColor={showAllDevices ? primaryColor : surfaceColor}
                />
              </View>
              <Text style={styles.filterDescription}>
                {showAllDevices
                  ? "Showing all Bluetooth devices (including audio devices, phones, etc.)"
                  : "Showing only devices that appear to be printers"}
              </Text>
            </View>
          )}

          {devices.length === 0 ? (
            <Text style={styles.emptyText}>
              {scanning
                ? "Scanning for devices..."
                : "No devices found. Tap scan to search."}
            </Text>
          ) : (
            devices.map((device) => (
              <TouchableOpacity
                key={device.address}
                style={styles.deviceItem}
                onPress={() => handleConnectDevice(device)}
                disabled={connecting === device.address}
              >
                <View style={styles.deviceInfo}>
                  <Text style={styles.deviceName}>{device.name}</Text>
                  <Text style={styles.deviceAddress}>{device.address}</Text>
                </View>
                {connecting === device.address ? (
                  <ActivityIndicator size="small" color={primaryColor} />
                ) : (
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={textSecondary}
                  />
                )}
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>

      {/* Troubleshooting Modal */}
      {showTroubleshooting && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <BluetoothTroubleshootingGuide
              onClose={() => setShowTroubleshooting(false)}
            />
          </View>
        </View>
      )}
    </View>
  );
}

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string,
  primaryColor: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    scrollContainer: {
      flex: 1,
    },
    section: {
      backgroundColor: surfaceColor,
      marginTop: 16,
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    cardContainer: {
      backgroundColor: surfaceColor,
      marginHorizontal: 16,
      marginTop: 16,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: textColor,
      marginBottom: 12,
    },
    printerTypeContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
    },
    printerTypeButton: {
      alignItems: "center",
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: primaryColor,
      minWidth: 80,
    },
    printerTypeButtonActive: {
      backgroundColor: primaryColor,
    },
    printerTypeText: {
      marginTop: 4,
      fontSize: 12,
      color: primaryColor,
    },
    printerTypeTextActive: {
      color: "#fff",
    },
    bluetoothStatus: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    statusRow: {
      flexDirection: "row",
      alignItems: "center",
    },
    statusText: {
      fontSize: 14,
      color: textSecondary,
      marginRight: 8,
    },
    statusIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    connectedDevice: {
      padding: 12,
      backgroundColor: primaryColor + "20",
      borderRadius: 8,
      borderWidth: 1,
      borderColor: primaryColor,
    },
    deviceInfo: {
      marginBottom: 8,
    },
    deviceName: {
      fontSize: 16,
      fontWeight: "600",
      color: textColor,
    },
    deviceAddress: {
      fontSize: 12,
      color: textSecondary,
      marginTop: 2,
    },
    deviceActions: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    testButton: {
      backgroundColor: primaryColor,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 6,
      flex: 1,
      marginRight: 8,
    },
    testButtonText: {
      color: "#fff",
      textAlign: "center",
      fontWeight: "600",
    },
    disconnectButton: {
      backgroundColor: "#F44336",
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 6,
      flex: 1,
      marginLeft: 8,
    },
    disconnectButtonText: {
      color: "#fff",
      textAlign: "center",
      fontWeight: "600",
    },
    scanHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 12,
    },
    scanActions: {
      flexDirection: "row",
      alignItems: "center",
      gap: 8,
    },
    helpButton: {
      padding: 8,
      borderRadius: 6,
      backgroundColor: primaryColor + "20",
      borderWidth: 1,
      borderColor: primaryColor,
    },
    scanButton: {
      backgroundColor: primaryColor,
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 6,
    },
    scanButtonDisabled: {
      backgroundColor: textSecondary,
    },
    scanButtonText: {
      color: "#fff",
      marginLeft: 4,
      fontWeight: "600",
    },

    deviceItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      backgroundColor: backgroundColor,
      marginBottom: 8,
      borderRadius: 8,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    emptyText: {
      textAlign: "center",
      color: textSecondary,
      fontStyle: "italic",
      padding: 20,
    },
    modalOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContainer: {
      width: "95%",
      height: "90%",
      backgroundColor: "#fff",
      borderRadius: 12,
      overflow: "hidden",
    },
    filterSection: {
      backgroundColor: backgroundColor,
      padding: 12,
      marginTop: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: textSecondary + "30",
    },
    filterRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 8,
    },
    filterLabel: {
      fontSize: 14,
      fontWeight: "600",
      color: textColor,
    },
    filterDescription: {
      fontSize: 12,
      color: textSecondary,
      lineHeight: 16,
    },
  });
