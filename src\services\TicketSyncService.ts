/**
 * Enhanced Ticket Synchronization Service
 *
 * Provides sophisticated real-time synchronization between frontend and backend
 * with advanced conflict resolution, intelligent sync strategies, and robust
 * offline/online state management.
 */

import { store } from "@/src/store";
import { clearError, setError, Ticket } from "@/src/store/slices/ticketSlice";
import { saveTicket } from "@/src/store/thunks/ticketThunks";

export interface SyncConflict {
  ticketId: string;
  localTicket: Ticket;
  serverTicket: Ticket;
  conflictType: "timestamp" | "content" | "status";
  resolution?: "local" | "server" | "merge";
}

export interface SyncResult {
  success: boolean;
  syncedTickets: number;
  conflicts: SyncConflict[];
  detailedConflicts: DetailedConflict[];
  errors: string[];
  strategy: "full" | "incremental" | "conflict-only" | "emergency";
  performance: {
    duration: number;
    networkLatency: number;
    conflictResolutionTime: number;
  };
}

export interface SyncStrategy {
  name: string;
  priority: number;
  conditions: (context: SyncContext) => boolean;
  execute: (context: SyncContext) => Promise<SyncResult>;
}

export interface SyncContext {
  isOnline: boolean;
  lastSyncTime: Date | null;
  dirtyTicketCount: number;
  networkQuality: "poor" | "fair" | "good" | "excellent";
  userActivity: "active" | "idle" | "background";
  conflictHistory: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fixableIssues: string[];
}

class TicketSyncService {
  private syncInterval: NodeJS.Timeout | null = null;
  private isOnline: boolean = true;
  private lastSyncAttempt: Date | null = null;
  private syncInProgress: boolean = false;
  private conflictQueue: SyncConflict[] = [];
  private detailedConflictQueue: DetailedConflict[] = [];

  // Enhanced state tracking
  private networkQuality: SyncContext["networkQuality"] = "good";
  private userActivity: SyncContext["userActivity"] = "active";
  private syncStrategies: SyncStrategy[] = [];
  private performanceMetrics: Array<{
    timestamp: Date;
    duration: number;
    strategy: string;
    success: boolean;
  }> = [];

  // Configuration
  private readonly SYNC_INTERVAL_MS = 30000; // 30 seconds
  private readonly RETRY_DELAY_MS = 5000; // 5 seconds
  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly CONFLICT_RESOLUTION_TIMEOUT = 10000; // 10 seconds

  constructor() {
    this.setupNetworkListener();
    this.initializeSyncStrategies();
    this.startAutoSync();
    this.setupNetworkQualityMonitoring();
    this.setupUserActivityTracking();
  }

  /**
   * Start automatic synchronization
   */
  startAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.performSync();
      }
    }, this.SYNC_INTERVAL_MS);
  }

  /**
   * Stop automatic synchronization
   */
  stopAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Perform manual synchronization
   */
  async performSync(force: boolean = false): Promise<SyncResult> {
    if (this.syncInProgress && !force) {
      return {
        success: false,
        syncedTickets: 0,
        conflicts: [],
        detailedConflicts: [],
        errors: ["Sync already in progress"],
        strategy: "conflict-only",
        performance: {
          duration: 0,
          networkLatency: 0,
          conflictResolutionTime: 0,
        },
      };
    }

    const startTime = Date.now();
    this.syncInProgress = true;
    this.lastSyncAttempt = new Date();

    try {
      store.dispatch(clearError());

      // Build sync context
      const context = await this.buildSyncContext();

      // Select optimal sync strategy
      const strategy = this.selectSyncStrategy(context);

      // Execute selected strategy
      const result = await strategy.execute(context);

      // Record performance metrics
      this.recordPerformanceMetrics(
        strategy.name,
        Date.now() - startTime,
        result.success
      );

      return result;
    } catch (error) {
      console.error("Sync failed:", error);

      const duration = Date.now() - startTime;
      this.recordPerformanceMetrics("failed", duration, false);

      const errorMessage =
        error instanceof Error ? error.message : "Sync failed";
      store.dispatch(setError(errorMessage));

      return {
        success: false,
        syncedTickets: 0,
        conflicts: [],
        detailedConflicts: [],
        errors: [errorMessage],
        strategy: "emergency",
        performance: { duration, networkLatency: 0, conflictResolutionTime: 0 },
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Validate ticket data consistency
   */
  validateTicket(ticket: Ticket): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const fixableIssues: string[] = [];

    // Basic validation
    if (!ticket.id || ticket.id.trim() === "") {
      errors.push("Ticket ID is required");
    }

    if (!ticket.name || ticket.name.trim() === "") {
      fixableIssues.push("Ticket name is empty");
    }

    if (!ticket.staffId || ticket.staffId.trim() === "") {
      errors.push("Staff ID is required");
    }

    // Items validation
    if (ticket.items.length === 0) {
      warnings.push("Ticket has no items");
    }

    ticket.items.forEach((item, index) => {
      if (!item.variantId) {
        errors.push(`Item ${index + 1}: Variant ID is required`);
      }

      if (item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
      }

      if (item.quantity > item.inventoryQuantity) {
        errors.push(
          `Item ${index + 1}: Insufficient inventory (${item.quantity} > ${
            item.inventoryQuantity
          })`
        );
      }

      if (item.inventoryQuantity <= 5) {
        warnings.push(
          `Item ${index + 1}: Low stock (${item.inventoryQuantity} remaining)`
        );
      }
    });

    // Financial validation
    const calculatedSubtotal = ticket.items.reduce(
      (sum, item) => sum + parseFloat(item.price) * item.quantity,
      0
    );

    if (Math.abs(calculatedSubtotal - ticket.subtotal) > 0.01) {
      fixableIssues.push("Subtotal calculation mismatch");
    }

    if (ticket.total < 0) {
      errors.push("Total cannot be negative");
    }

    // Status validation
    const validStatuses = ["active", "paused", "completed", "cancelled"];
    if (!validStatuses.includes(ticket.status)) {
      errors.push(`Invalid status: ${ticket.status}`);
    }

    // Timestamp validation
    if (new Date(ticket.createdAt) > new Date(ticket.updatedAt)) {
      errors.push("Created date cannot be after updated date");
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      fixableIssues,
    };
  }

  /**
   * Fix common ticket issues automatically
   */
  async fixTicketIssues(ticket: Ticket): Promise<Ticket> {
    const fixedTicket = { ...ticket };

    // Fix empty name
    if (!fixedTicket.name || fixedTicket.name.trim() === "") {
      fixedTicket.name = `Ticket ${new Date().toLocaleTimeString()}`;
    }

    // Recalculate totals
    const subtotal = fixedTicket.items.reduce(
      (sum, item) => sum + parseFloat(item.price) * item.quantity,
      0
    );

    fixedTicket.subtotal = Math.round(subtotal * 100) / 100;
    fixedTicket.total = Math.round((subtotal + fixedTicket.tax) * 100) / 100;

    // Update timestamp
    fixedTicket.updatedAt = new Date().toISOString();
    fixedTicket.isDirty = true;

    return fixedTicket;
  }

  /**
   * Get pending conflicts for manual resolution
   */
  getPendingConflicts(): SyncConflict[] {
    return [...this.conflictQueue];
  }

  /**
   * Resolve conflict manually
   */
  async resolveConflict(
    conflictId: string,
    resolution: "local" | "server" | "merge"
  ): Promise<boolean> {
    const conflictIndex = this.conflictQueue.findIndex(
      (c) => c.ticketId === conflictId
    );

    if (conflictIndex === -1) {
      return false;
    }

    const conflict = this.conflictQueue[conflictIndex];

    try {
      let resolvedTicket: Ticket;

      switch (resolution) {
        case "local":
          resolvedTicket = conflict.localTicket;
          break;
        case "server":
          resolvedTicket = conflict.serverTicket;
          break;
        case "merge":
          resolvedTicket = this.mergeTickets(
            conflict.localTicket,
            conflict.serverTicket
          );
          break;
        default:
          return false;
      }

      // Save resolved ticket
      await store.dispatch(saveTicket({ ticket: resolvedTicket })).unwrap();

      // Remove from conflict queue
      this.conflictQueue.splice(conflictIndex, 1);

      return true;
    } catch (error) {
      console.error("Failed to resolve conflict:", error);
      return false;
    }
  }

  /**
   * Get enhanced sync status
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      lastSyncAttempt: this.lastSyncAttempt,
      pendingConflicts: this.conflictQueue.length,
      detailedConflicts: this.detailedConflictQueue.length,
      autoSyncEnabled: this.syncInterval !== null,
      networkQuality: this.networkQuality,
      userActivity: this.userActivity,
      performanceStats: this.getPerformanceStats(),
    };
  }

  /**
   * Get detailed conflicts for advanced resolution
   */
  getDetailedConflicts(): DetailedConflict[] {
    return [...this.detailedConflictQueue];
  }

  /**
   * Resolve detailed conflict using advanced resolver
   */
  async resolveDetailedConflict(
    conflictId: string,
    userChoices?: Record<string, "local" | "server" | "merge">
  ): Promise<boolean> {
    const conflictIndex = this.detailedConflictQueue.findIndex(
      (c) => c.ticketId === conflictId
    );

    if (conflictIndex === -1) {
      return false;
    }

    const conflict = this.detailedConflictQueue[conflictIndex];

    try {
      // Apply user choices if provided
      if (userChoices) {
        for (const [field, choice] of Object.entries(userChoices)) {
          await advancedConflictResolver.learnFromUserChoice(field, choice);
        }
      }

      const result = await advancedConflictResolver.resolveConflict(conflict);

      if (result.success && result.resolvedTicket) {
        // Save resolved ticket
        await store
          .dispatch(saveTicket({ ticket: result.resolvedTicket }))
          .unwrap();

        // Remove from queue
        this.detailedConflictQueue.splice(conflictIndex, 1);

        return true;
      }

      return false;
    } catch (error) {
      console.error("Failed to resolve detailed conflict:", error);
      return false;
    }
  }

  // Private methods

  private setupNetworkListener(): void {
    // Listen for online/offline events
    if (typeof window !== "undefined") {
      window.addEventListener("online", () => {
        this.isOnline = true;
        this.performSync();
      });

      window.addEventListener("offline", () => {
        this.isOnline = false;
      });

      this.isOnline = navigator.onLine;
    }
  }

  private detectConflicts(serverTickets: Ticket[]): SyncConflict[] {
    const state = store.getState();
    const localTickets = state.tickets.tickets;
    const conflicts: SyncConflict[] = [];

    serverTickets.forEach((serverTicket) => {
      const localTicket = localTickets.find((t) => t.id === serverTicket.id);

      if (localTicket) {
        // Check for timestamp conflicts
        if (localTicket.updatedAt !== serverTicket.updatedAt) {
          conflicts.push({
            ticketId: serverTicket.id,
            localTicket,
            serverTicket,
            conflictType: "timestamp",
          });
        }

        // Check for status conflicts
        if (localTicket.status !== serverTicket.status) {
          conflicts.push({
            ticketId: serverTicket.id,
            localTicket,
            serverTicket,
            conflictType: "status",
          });
        }

        // Check for content conflicts
        if (
          JSON.stringify(localTicket.items) !==
          JSON.stringify(serverTicket.items)
        ) {
          conflicts.push({
            ticketId: serverTicket.id,
            localTicket,
            serverTicket,
            conflictType: "content",
          });
        }
      }
    });

    return conflicts;
  }

  private async autoResolveConflicts(
    conflicts: SyncConflict[]
  ): Promise<SyncConflict[]> {
    const resolvedConflicts: SyncConflict[] = [];

    for (const conflict of conflicts) {
      // Auto-resolve simple timestamp conflicts (prefer newer)
      if (conflict.conflictType === "timestamp") {
        const localTime = new Date(conflict.localTicket.updatedAt);
        const serverTime = new Date(conflict.serverTicket.updatedAt);

        conflict.resolution = localTime > serverTime ? "local" : "server";

        try {
          const resolvedTicket =
            conflict.resolution === "local"
              ? conflict.localTicket
              : conflict.serverTicket;

          await store.dispatch(saveTicket({ ticket: resolvedTicket })).unwrap();
        } catch (error) {
          // If auto-resolution fails, queue for manual resolution
          delete conflict.resolution;
        }
      }

      resolvedConflicts.push(conflict);
    }

    return resolvedConflicts;
  }

  private mergeTickets(localTicket: Ticket, serverTicket: Ticket): Ticket {
    // Smart merge strategy: prefer local changes for items, server for metadata
    return {
      ...serverTicket,
      items: localTicket.items, // Prefer local cart changes
      note: localTicket.note || serverTicket.note, // Prefer non-empty notes
      updatedAt: new Date().toISOString(),
      isDirty: true,
    };
  }

  // Enhanced private methods for advanced sync

  private initializeSyncStrategies(): void {
    this.syncStrategies = [
      {
        name: "emergency",
        priority: 1,
        conditions: (context) =>
          !context.isOnline || context.networkQuality === "poor",
        execute: this.executeEmergencySync.bind(this),
      },
      {
        name: "incremental",
        priority: 2,
        conditions: (context) =>
          context.dirtyTicketCount <= 3 &&
          context.networkQuality === "excellent",
        execute: this.executeIncrementalSync.bind(this),
      },
      {
        name: "conflict-only",
        priority: 3,
        conditions: (context) =>
          context.conflictHistory > 0 && context.userActivity === "active",
        execute: this.executeConflictOnlySync.bind(this),
      },
      {
        name: "full",
        priority: 4,
        conditions: () => true, // Default fallback
        execute: this.executeFullSync.bind(this),
      },
    ];
  }

  private async buildSyncContext(): Promise<SyncContext> {
    const state = store.getState();
    const dirtyTickets = state.tickets.tickets.filter((t) => t.isDirty);

    return {
      isOnline: this.isOnline,
      lastSyncTime: this.lastSyncAttempt,
      dirtyTicketCount: dirtyTickets.length,
      networkQuality: this.networkQuality,
      userActivity: this.userActivity,
      conflictHistory:
        this.conflictQueue.length + this.detailedConflictQueue.length,
    };
  }

  private selectSyncStrategy(context: SyncContext): SyncStrategy {
    // Sort by priority and find first matching strategy
    const sortedStrategies = [...this.syncStrategies].sort(
      (a, b) => a.priority - b.priority
    );

    for (const strategy of sortedStrategies) {
      if (strategy.conditions(context)) {
        return strategy;
      }
    }

    // Fallback to full sync
    return this.syncStrategies.find((s) => s.name === "full")!;
  }

  private async executeEmergencySync(
    context: SyncContext
  ): Promise<SyncResult> {
    const startTime = Date.now();

    try {
      // Only save critical dirty tickets locally
      const state = store.getState();
      const criticalTickets = state.tickets.tickets.filter(
        (t) => t.isDirty && (t.items.length > 0 || t.total > 0)
      );

      return {
        success: true,
        syncedTickets: criticalTickets.length,
        conflicts: [],
        detailedConflicts: [],
        errors: [],
        strategy: "emergency",
        performance: {
          duration: Date.now() - startTime,
          networkLatency: 0,
          conflictResolutionTime: 0,
        },
      };
    } catch (error) {
      return this.createErrorResult("emergency", startTime, error);
    }
  }

  private async executeIncrementalSync(
    context: SyncContext
  ): Promise<SyncResult> {
    const startTime = Date.now();

    try {
      // Only sync dirty tickets
      const autoSaveResult = await store
        .dispatch(autoSaveDirtyTickets())
        .unwrap();

      return {
        success: true,
        syncedTickets: autoSaveResult.length,
        conflicts: [],
        detailedConflicts: [],
        errors: [],
        strategy: "incremental",
        performance: {
          duration: Date.now() - startTime,
          networkLatency: this.estimateNetworkLatency(),
          conflictResolutionTime: 0,
        },
      };
    } catch (error) {
      return this.createErrorResult("incremental", startTime, error);
    }
  }

  private async executeConflictOnlySync(
    context: SyncContext
  ): Promise<SyncResult> {
    const startTime = Date.now();
    const conflictStartTime = Date.now();

    try {
      // Focus on resolving existing conflicts
      const resolvedCount = await this.processExistingConflicts();

      return {
        success: true,
        syncedTickets: resolvedCount,
        conflicts: [],
        detailedConflicts: [],
        errors: [],
        strategy: "conflict-only",
        performance: {
          duration: Date.now() - startTime,
          networkLatency: this.estimateNetworkLatency(),
          conflictResolutionTime: Date.now() - conflictStartTime,
        },
      };
    } catch (error) {
      return this.createErrorResult("conflict-only", startTime, error);
    }
  }

  private async executeFullSync(context: SyncContext): Promise<SyncResult> {
    const startTime = Date.now();
    const conflictStartTime = Date.now();

    try {
      // Step 1: Auto-save dirty tickets
      const autoSaveResult = await store
        .dispatch(autoSaveDirtyTickets())
        .unwrap();

      // Step 2: Load latest tickets from server
      const serverTickets = await store.dispatch(loadTickets()).unwrap();

      // Step 3: Detect and resolve conflicts with advanced resolver
      const conflicts = this.detectConflicts(serverTickets);
      const detailedConflicts: DetailedConflict[] = [];

      for (const conflict of conflicts) {
        const detailedConflict =
          await advancedConflictResolver.analyzeConflicts(
            conflict.localTicket,
            conflict.serverTicket
          );

        const resolution = await advancedConflictResolver.resolveConflict(
          detailedConflict
        );

        if (resolution.success && resolution.resolvedTicket) {
          await store
            .dispatch(saveTicket({ ticket: resolution.resolvedTicket }))
            .unwrap();
        } else {
          detailedConflicts.push(detailedConflict);
        }
      }

      // Queue unresolved conflicts
      this.detailedConflictQueue.push(...detailedConflicts);

      return {
        success: true,
        syncedTickets: autoSaveResult.length + serverTickets.length,
        conflicts,
        detailedConflicts,
        errors: [],
        strategy: "full",
        performance: {
          duration: Date.now() - startTime,
          networkLatency: this.estimateNetworkLatency(),
          conflictResolutionTime: Date.now() - conflictStartTime,
        },
      };
    } catch (error) {
      return this.createErrorResult("full", startTime, error);
    }
  }

  private async processExistingConflicts(): Promise<number> {
    let resolvedCount = 0;

    // Process detailed conflicts
    for (let i = this.detailedConflictQueue.length - 1; i >= 0; i--) {
      const conflict = this.detailedConflictQueue[i];
      const result = await advancedConflictResolver.resolveConflict(conflict);

      if (result.success && result.resolvedTicket) {
        await store
          .dispatch(saveTicket({ ticket: result.resolvedTicket }))
          .unwrap();
        this.detailedConflictQueue.splice(i, 1);
        resolvedCount++;
      }
    }

    return resolvedCount;
  }

  private createErrorResult(
    strategy: string,
    startTime: number,
    error: any
  ): SyncResult {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return {
      success: false,
      syncedTickets: 0,
      conflicts: [],
      detailedConflicts: [],
      errors: [errorMessage],
      strategy: strategy as any,
      performance: {
        duration: Date.now() - startTime,
        networkLatency: 0,
        conflictResolutionTime: 0,
      },
    };
  }

  private recordPerformanceMetrics(
    strategy: string,
    duration: number,
    success: boolean
  ): void {
    this.performanceMetrics.push({
      timestamp: new Date(),
      duration,
      strategy,
      success,
    });

    // Keep only recent metrics (last 100)
    if (this.performanceMetrics.length > 100) {
      this.performanceMetrics = this.performanceMetrics.slice(-50);
    }
  }

  private getPerformanceStats() {
    if (this.performanceMetrics.length === 0) {
      return {
        averageDuration: 0,
        successRate: 0,
        totalSyncs: 0,
        fastestSync: 0,
        slowestSync: 0,
      };
    }

    const durations = this.performanceMetrics.map((m) => m.duration);
    const successCount = this.performanceMetrics.filter(
      (m) => m.success
    ).length;

    return {
      averageDuration:
        durations.reduce((sum, d) => sum + d, 0) / durations.length,
      successRate: successCount / this.performanceMetrics.length,
      totalSyncs: this.performanceMetrics.length,
      fastestSync: Math.min(...durations),
      slowestSync: Math.max(...durations),
    };
  }

  private estimateNetworkLatency(): number {
    // Simple network latency estimation
    // In a real implementation, this would ping the server
    switch (this.networkQuality) {
      case "excellent":
        return 50;
      case "good":
        return 150;
      case "fair":
        return 300;
      case "poor":
        return 1000;
      default:
        return 200;
    }
  }

  private setupNetworkQualityMonitoring(): void {
    // Monitor network quality using NetInfo in React Native
    // This would be implemented with actual network monitoring
    // For now, we'll simulate it
    setInterval(() => {
      this.updateNetworkQuality();
    }, 10000); // Check every 10 seconds
  }

  private updateNetworkQuality(): void {
    // Simulate network quality detection
    // In a real implementation, this would measure actual network performance
    const qualities: SyncContext["networkQuality"][] = [
      "poor",
      "fair",
      "good",
      "excellent",
    ];
    const randomIndex = Math.floor(Math.random() * qualities.length);
    this.networkQuality = qualities[randomIndex];
  }

  private setupUserActivityTracking(): void {
    if (typeof window !== "undefined") {
      // Track user activity for better sync timing
      let activityTimer: NodeJS.Timeout;

      const resetActivityTimer = () => {
        this.userActivity = "active";
        clearTimeout(activityTimer);

        activityTimer = setTimeout(() => {
          this.userActivity = "idle";
        }, 60000); // 1 minute of inactivity
      };

      // Listen for user interactions
      ["mousedown", "mousemove", "keypress", "scroll", "touchstart"].forEach(
        (event) => {
          window.addEventListener(event, resetActivityTimer, true);
        }
      );

      // Listen for app state changes
      document.addEventListener("visibilitychange", () => {
        this.userActivity = document.hidden ? "background" : "active";
      });
    }
  }
}

// Export singleton instance
export const ticketSyncService = new TicketSyncService();
export default TicketSyncService;
