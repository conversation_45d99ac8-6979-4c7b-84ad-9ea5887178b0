/**
 * Comprehensive API Route Testing
 * Tests all major endpoints with MySQL backend
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";
let authToken = null;

class APITester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: [],
    };
  }

  async test(name, testFunction) {
    try {
      console.log(`🧪 Testing: ${name}`);
      await testFunction();
      console.log(`✅ PASSED: ${name}`);
      this.results.passed++;
      this.results.tests.push({ name, status: "PASSED" });
    } catch (error) {
      console.log(`❌ FAILED: ${name}`);
      console.log(`   Error: ${error.message}`);
      this.results.failed++;
      this.results.tests.push({ name, status: "FAILED", error: error.message });
    }
  }

  async login() {
    const response = await axios.post(`${BASE_URL}/api/pos/login`, {
      username: "manager1",
      password: "manager123",
    });

    if (!response.data.success) {
      throw new Error("Login failed");
    }

    authToken = response.data.data.token;
    return authToken;
  }

  getAuthHeaders() {
    return { Authorization: `Bearer ${authToken}` };
  }

  async runAllTests() {
    console.log("🚀 Starting Comprehensive API Route Testing...\n");

    // Authentication Tests
    await this.test("Staff Login - Manager", async () => {
      await this.login();
      if (!authToken) throw new Error("No token received");
    });

    await this.test("Token Verification", async () => {
      const response = await axios.get(`${BASE_URL}/api/pos/verify`, {
        headers: this.getAuthHeaders(),
      });
      if (!response.data.success) throw new Error("Token verification failed");
    });

    // Sales Agent Tests
    await this.test("Get All Sales Agents", async () => {
      const response = await axios.get(`${BASE_URL}/api/sales-agents`, {
        headers: this.getAuthHeaders(),
      });
      if (!response.data.success) throw new Error("Failed to get sales agents");
      if (!Array.isArray(response.data.data.salesAgents))
        throw new Error("Invalid response format");
    });

    await this.test("Get Sales Agent by ID", async () => {
      const response = await axios.get(
        `${BASE_URL}/api/sales-agents/agent-001`,
        {
          headers: this.getAuthHeaders(),
        }
      );
      if (!response.data.success) throw new Error("Failed to get sales agent");
      if (!response.data.data.salesAgent)
        throw new Error("No sales agent data");
    });

    await this.test("Create New Sales Agent", async () => {
      const response = await axios.post(
        `${BASE_URL}/api/sales-agents`,
        {
          name: "Test Agent MySQL",
          email: "<EMAIL>",
          phone: "+254700888888",
          commissionRate: 7.0,
          territory: "Test Territory MySQL",
          region: "Test Region MySQL",
        },
        {
          headers: {
            ...this.getAuthHeaders(),
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.data.success)
        throw new Error("Failed to create sales agent");
    });

    // Staff Management Tests
    await this.test("Get All Staff", async () => {
      const response = await axios.get(`${BASE_URL}/api/staff`, {
        headers: this.getAuthHeaders(),
      });
      if (!response.data.success) throw new Error("Failed to get staff");
      if (!Array.isArray(response.data.data.staff))
        throw new Error("Invalid response format");
    });

    // Commission System Tests
    await this.test("Get Commission Configuration", async () => {
      const response = await axios.get(
        `${BASE_URL}/api/discounts/configuration`,
        {
          headers: this.getAuthHeaders(),
        }
      );
      if (!response.data.success)
        throw new Error("Failed to get commission configuration");
      if (!response.data.data.configuration)
        throw new Error("No configuration data");
    });

    await this.test("Calculate Commission Discount", async () => {
      const response = await axios.post(
        `${BASE_URL}/api/discounts/calculate`,
        {
          cartData: { subtotal: "150.00" },
          staffId: "pos-001",
          salesAgentId: "agent-001",
        },
        {
          headers: {
            ...this.getAuthHeaders(),
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.data.success)
        throw new Error("Failed to calculate discount");
      if (!response.data.data.discount) throw new Error("No discount data");
    });

    await this.test("Test Commission Calculation", async () => {
      const response = await axios.post(
        `${BASE_URL}/api/discounts/test`,
        {
          cartData: { subtotal: "200.00" },
          staffId: "pos-002",
          salesAgentId: "agent-002",
        },
        {
          headers: {
            ...this.getAuthHeaders(),
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.data.success)
        throw new Error("Failed to test commission calculation");
    });

    // Customer Loyalty Tests
    await this.test("Get Customer Loyalty Tier", async () => {
      const response = await axios.get(
        `${BASE_URL}/api/customers/8095960301705/loyalty-tier`,
        {
          headers: this.getAuthHeaders(),
        }
      );
      if (!response.data.success)
        throw new Error("Failed to get customer loyalty tier");
    });

    // Store API Tests
    await this.test("Get Store Products", async () => {
      const response = await axios.get(
        `${BASE_URL}/api/store/products?limit=5`,
        {
          headers: this.getAuthHeaders(),
        }
      );
      if (!response.data.success)
        throw new Error("Failed to get store products");
    });

    await this.test("Get Store Customers", async () => {
      const response = await axios.get(
        `${BASE_URL}/api/store/customers?limit=5`,
        {
          headers: this.getAuthHeaders(),
        }
      );
      if (!response.data.success)
        throw new Error("Failed to get store customers");
    });

    // Health Check
    await this.test("Health Check", async () => {
      const response = await axios.get(`${BASE_URL}/health`);
      if (response.data.status !== "OK") throw new Error("Health check failed");
      if (!response.data.commission)
        throw new Error("Commission system not active");
    });

    // Print Results
    console.log("\n📊 COMPREHENSIVE TEST RESULTS:");
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(
      `📈 Success Rate: ${(
        (this.results.passed / (this.results.passed + this.results.failed)) *
        100
      ).toFixed(1)}%`
    );

    if (this.results.failed === 0) {
      console.log(
        "\n🎉 All comprehensive tests passed! System is fully operational with MySQL backend."
      );
    } else {
      console.log("\n⚠️  Some tests failed. Review the errors above.");
      console.log("\nFailed tests:");
      this.results.tests
        .filter((t) => t.status === "FAILED")
        .forEach((test) => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
    }

    return this.results;
  }
}

// Run comprehensive tests
const tester = new APITester();
tester.runAllTests().catch(console.error);
