/**
 * Fix Missing Database Columns and Tables for User Switching
 */

require("dotenv").config();
const mysql = require("mysql2/promise");

async function fixDatabase() {
  let connection;
  
  try {
    console.log("🔗 Connecting to MySQL database...");
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "dukalink",
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || "dukalink_shopify_pos",
      charset: "utf8mb4",
    });

    console.log("✅ Connected to MySQL database");

    // Add missing columns to pos_staff table
    console.log("🔧 Adding missing columns to pos_staff table...");
    
    const staffColumns = [
      "ADD COLUMN pin VARCHAR(255) NULL",
      "ADD COLUMN pin_set_at DATETIME NULL",
      "ADD COLUMN pin_set_by VARCHAR(255) NULL",
      "ADD COLUMN pin_attempts INT DEFAULT 0",
      "ADD COLUMN pin_locked_until DATETIME NULL",
      "ADD COLUMN last_pin_used DATETIME NULL"
    ];

    for (const column of staffColumns) {
      try {
        await connection.execute(`ALTER TABLE pos_staff ${column}`);
        console.log(`   ✅ Added: ${column}`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`   ⚠️ Column already exists: ${column}`);
        } else {
          console.error(`   ❌ Failed to add: ${column}`, error.message);
        }
      }
    }

    // Create pos_security_events table
    console.log("🔧 Creating pos_security_events table...");
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS pos_security_events (
          id VARCHAR(255) PRIMARY KEY,
          staff_id VARCHAR(255),
          event_type VARCHAR(100) NOT NULL,
          event_data JSON,
          ip_address VARCHAR(45),
          user_agent TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (staff_id) REFERENCES pos_staff(id) ON DELETE SET NULL,
          INDEX idx_security_events_staff (staff_id),
          INDEX idx_security_events_type (event_type),
          INDEX idx_security_events_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      console.log("✅ pos_security_events table created");
    } catch (error) {
      console.error("❌ Failed to create pos_security_events table:", error.message);
    }

    // Verify the changes
    console.log("\n🔍 Verifying database structure...");
    
    // Check pos_staff columns
    const [staffCols] = await connection.execute("DESCRIBE pos_staff");
    const staffColumnNames = staffCols.map(col => col.Field);
    console.log("pos_staff columns:", staffColumnNames.join(", "));
    
    // Check if pos_security_events exists
    const [tables] = await connection.execute("SHOW TABLES LIKE 'pos_security_events'");
    console.log(`pos_security_events table exists: ${tables.length > 0}`);

    console.log("\n✅ Database fix completed successfully!");

  } catch (error) {
    console.error("❌ Database fix failed:", error);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

fixDatabase();
