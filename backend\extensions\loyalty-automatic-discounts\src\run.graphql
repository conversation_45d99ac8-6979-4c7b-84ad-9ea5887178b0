query Input {
  cart {
    lines {
      id
      quantity
      cost {
        amountPerQuantity {
          amount
        }
        subtotalAmount {
          amount
        }
      }
      merchandise {
        __typename
        ... on ProductVariant {
          id
          product {
            id
            title
            tags
            productType
            vendor
            metafield(namespace: "dukalink_loyalty", key: "tier_discount_eligible") {
              value
            }
            metafield(namespace: "dukalink_loyalty", key: "bonus_points_multiplier") {
              value
            }
          }
        }
      }
    }
    cost {
      subtotalAmount {
        amount
      }
      totalAmount {
        amount
      }
    }
    buyerIdentity {
      customer {
        id
        tags
        loyaltyTier: metafield(namespace: "dukalink_loyalty", key: "tier_level") {
          value
        }
        loyaltyPoints: metafield(namespace: "dukalink_loyalty", key: "points_balance") {
          value
        }
        totalPurchases: metafield(namespace: "dukalink_loyalty", key: "total_purchases") {
          value
        }
        totalOrders: metafield(namespace: "dukalink_loyalty", key: "total_orders") {
          value
        }
        lastPurchase: metafield(namespace: "dukalink_loyalty", key: "last_purchase_date") {
          value
        }
        memberSince: metafield(namespace: "dukalink_loyalty", key: "tier_updated_date") {
          value
        }
      }
    }
    attribute(key: "loyalty_promotion_code") {
      value
    }
    attribute(key: "birthday_month") {
      value
    }
  }
  discountNode {
    metafield(namespace: "$app:loyalty-discounts", key: "function-configuration") {
      jsonValue
    }
  }
}
