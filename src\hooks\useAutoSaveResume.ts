/**
 * Auto-Save and Resume Hook
 * 
 * Provides comprehensive auto-save and resume functionality for tickets.
 * Handles app lifecycle events, network status changes, and user interactions
 * to ensure data persistence and seamless user experience.
 */

import { useEffect, useCallback, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { useAppDispatch, useAppSelector } from '@/src/store';
import { selectAllTickets, selectActiveTicketId } from '@/src/store/slices/ticketSlice';
import { autoSaveService, AutoSaveStats } from '@/src/services/AutoSaveService';
import { resumeService, ResumeResult } from '@/src/services/ResumeService';
import { useSession } from '@/src/contexts/AuthContext';

export interface AutoSaveResumeState {
  // Auto-save state
  isAutoSaving: boolean;
  autoSaveEnabled: boolean;
  autoSaveStats: AutoSaveStats;
  lastAutoSave: Date | null;
  
  // Resume state
  isResuming: boolean;
  resumeAvailable: boolean;
  lastResumeResult: ResumeResult | null;
  
  // Network and app state
  isOnline: boolean;
  appState: AppStateStatus;
  
  // Error state
  error: string | null;
}

export interface AutoSaveResumeActions {
  // Auto-save actions
  enableAutoSave: () => void;
  disableAutoSave: () => void;
  forceAutoSave: () => Promise<void>;
  clearAutoSaveQueue: () => void;
  
  // Resume actions
  triggerResume: () => Promise<ResumeResult>;
  clearResumeData: () => Promise<void>;
  
  // Manual controls
  saveCurrentSession: () => Promise<void>;
  refreshStats: () => void;
  clearError: () => void;
}

export const useAutoSaveResume = (): [AutoSaveResumeState, AutoSaveResumeActions] => {
  const dispatch = useAppDispatch();
  const { user } = useSession();
  
  // Redux state
  const tickets = useAppSelector(selectAllTickets);
  const activeTicketId = useAppSelector(selectActiveTicketId);
  
  // Local state
  const [state, setState] = useState<AutoSaveResumeState>({
    isAutoSaving: false,
    autoSaveEnabled: true,
    autoSaveStats: autoSaveService.getStats(),
    lastAutoSave: null,
    isResuming: false,
    resumeAvailable: false,
    lastResumeResult: null,
    isOnline: true,
    appState: AppState.currentState,
    error: null,
  });

  // Initialize services on mount
  useEffect(() => {
    let mounted = true;

    const initializeServices = async () => {
      try {
        // Initialize resume service and attempt recovery
        setState(prev => ({ ...prev, isResuming: true }));
        
        const resumeResult = await resumeService.initialize();
        
        if (mounted) {
          setState(prev => ({
            ...prev,
            isResuming: false,
            lastResumeResult: resumeResult,
            resumeAvailable: resumeResult.requiresUserAction,
          }));
        }

        // Check if resume is available
        const isAvailable = await resumeService.isResumeAvailable();
        if (mounted) {
          setState(prev => ({ ...prev, resumeAvailable: isAvailable }));
        }

      } catch (error) {
        if (mounted) {
          setState(prev => ({
            ...prev,
            isResuming: false,
            error: error instanceof Error ? error.message : 'Initialization failed',
          }));
        }
      }
    };

    if (user) {
      initializeServices();
    }

    return () => {
      mounted = false;
    };
  }, [user]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      setState(prev => ({ ...prev, appState: nextAppState }));

      if (nextAppState === 'background' || nextAppState === 'inactive') {
        // App going to background, save session
        resumeService.saveSession();
        autoSaveService.saveAllDirtyTickets();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, []);

  // Handle network status changes
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setState(prev => ({ ...prev, isOnline: state.isConnected ?? false }));
    });

    return unsubscribe;
  }, []);

  // Update auto-save stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const stats = autoSaveService.getStats();
      setState(prev => ({ 
        ...prev, 
        autoSaveStats: stats,
        lastAutoSave: stats.lastSaveTime,
      }));
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  // Save session when tickets or active ticket changes
  useEffect(() => {
    if (user && tickets.length > 0) {
      resumeService.updateSession();
    }
  }, [user, tickets, activeTicketId]);

  // Actions
  const enableAutoSave = useCallback(() => {
    setState(prev => ({ ...prev, autoSaveEnabled: true }));
    // Auto-save service is always enabled, this just updates UI state
  }, []);

  const disableAutoSave = useCallback(() => {
    setState(prev => ({ ...prev, autoSaveEnabled: false }));
    autoSaveService.clear();
  }, []);

  const forceAutoSave = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isAutoSaving: true, error: null }));
      
      await autoSaveService.saveAllDirtyTickets();
      
      setState(prev => ({ 
        ...prev, 
        isAutoSaving: false,
        autoSaveStats: autoSaveService.getStats(),
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isAutoSaving: false,
        error: error instanceof Error ? error.message : 'Auto-save failed',
      }));
    }
  }, []);

  const clearAutoSaveQueue = useCallback(() => {
    autoSaveService.clear();
    setState(prev => ({ 
      ...prev, 
      autoSaveStats: autoSaveService.getStats(),
    }));
  }, []);

  const triggerResume = useCallback(async (): Promise<ResumeResult> => {
    try {
      setState(prev => ({ ...prev, isResuming: true, error: null }));
      
      const result = await resumeService.triggerManualRecovery();
      
      setState(prev => ({
        ...prev,
        isResuming: false,
        lastResumeResult: result,
        resumeAvailable: result.requiresUserAction,
      }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Resume failed';
      setState(prev => ({
        ...prev,
        isResuming: false,
        error: errorMessage,
      }));

      return {
        success: false,
        recoveredTickets: [],
        restoredActiveTicket: null,
        message: errorMessage,
        requiresUserAction: false,
      };
    }
  }, []);

  const clearResumeData = useCallback(async () => {
    try {
      await resumeService.clearSession();
      setState(prev => ({
        ...prev,
        resumeAvailable: false,
        lastResumeResult: null,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to clear resume data',
      }));
    }
  }, []);

  const saveCurrentSession = useCallback(async () => {
    try {
      await resumeService.saveSession();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to save session',
      }));
    }
  }, []);

  const refreshStats = useCallback(() => {
    const stats = autoSaveService.getStats();
    setState(prev => ({ 
      ...prev, 
      autoSaveStats: stats,
      lastAutoSave: stats.lastSaveTime,
    }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return [
    state,
    {
      enableAutoSave,
      disableAutoSave,
      forceAutoSave,
      clearAutoSaveQueue,
      triggerResume,
      clearResumeData,
      saveCurrentSession,
      refreshStats,
      clearError,
    },
  ];
};

export default useAutoSaveResume;
