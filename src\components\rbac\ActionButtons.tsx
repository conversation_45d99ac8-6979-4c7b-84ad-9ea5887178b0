/**
 * Permission-Based Action Button Components
 * 
 * Provides reusable action buttons that automatically check permissions
 * before rendering edit/delete/view actions for list items
 */

import React from 'react';
import {
  TouchableOpacity,
  View,
  StyleSheet,
  ViewStyle,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRBAC } from '@/src/hooks/useRBAC';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Permission } from '@/src/config/rbac';

interface ActionButtonProps {
  onPress: () => void;
  iconName: string;
  iconColor?: string;
  backgroundColor?: string;
  size?: number;
  disabled?: boolean;
  style?: ViewStyle;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  onPress,
  iconName,
  iconColor = '#666',
  backgroundColor = 'transparent',
  size = 32,
  disabled = false,
  style,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.actionButton,
        {
          backgroundColor,
          width: size,
          height: size,
          borderRadius: size / 2,
        },
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <Ionicons 
        name={iconName as any} 
        size={size * 0.6} 
        color={disabled ? '#ccc' : iconColor} 
      />
    </TouchableOpacity>
  );
};

interface PermissionActionButtonProps extends ActionButtonProps {
  requiredPermission: Permission;
}

const PermissionActionButton: React.FC<PermissionActionButtonProps> = ({
  requiredPermission,
  ...props
}) => {
  const { hasPermission } = useRBAC();

  if (!hasPermission(requiredPermission)) {
    return null;
  }

  return <ActionButton {...props} />;
};

// Action Button Container for list items
interface ActionButtonsContainerProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const ActionButtonsContainer: React.FC<ActionButtonsContainerProps> = ({
  children,
  style,
}) => {
  return (
    <View style={[styles.actionButtonsContainer, style]}>
      {children}
    </View>
  );
};

// Specific Action Button Components
interface EditButtonProps {
  onPress: () => void;
  requiredPermission: Permission;
  disabled?: boolean;
}

export const EditButton: React.FC<EditButtonProps> = ({
  onPress,
  requiredPermission,
  disabled = false,
}) => {
  const primaryColor = useThemeColor({}, 'primary');
  
  return (
    <PermissionActionButton
      requiredPermission={requiredPermission}
      onPress={onPress}
      iconName="pencil"
      iconColor={primaryColor}
      disabled={disabled}
    />
  );
};

interface DeleteButtonProps {
  onPress: () => void;
  requiredPermission: Permission;
  itemName: string;
  disabled?: boolean;
}

export const DeleteButton: React.FC<DeleteButtonProps> = ({
  onPress,
  requiredPermission,
  itemName,
  disabled = false,
}) => {
  const handleDelete = () => {
    Alert.alert(
      'Confirm Delete',
      `Are you sure you want to delete ${itemName}? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress,
        },
      ]
    );
  };

  return (
    <PermissionActionButton
      requiredPermission={requiredPermission}
      onPress={handleDelete}
      iconName="trash"
      iconColor="#FF3B30"
      disabled={disabled}
    />
  );
};

interface ViewDetailsButtonProps {
  onPress: () => void;
  disabled?: boolean;
}

export const ViewDetailsButton: React.FC<ViewDetailsButtonProps> = ({
  onPress,
  disabled = false,
}) => {
  const textSecondary = useThemeColor({}, 'textSecondary');
  
  return (
    <ActionButton
      onPress={onPress}
      iconName="chevron-forward"
      iconColor={textSecondary}
      disabled={disabled}
    />
  );
};

// Convenience components for specific entities
export const StaffActionButtons: React.FC<{
  onEdit: () => void;
  onDelete: () => void;
  onViewDetails: () => void;
  staffName: string;
}> = ({ onEdit, onDelete, onViewDetails, staffName }) => (
  <ActionButtonsContainer>
    <EditButton
      onPress={onEdit}
      requiredPermission="manage_staff"
    />
    <DeleteButton
      onPress={onDelete}
      requiredPermission="manage_staff"
      itemName={staffName}
    />
    <ViewDetailsButton onPress={onViewDetails} />
  </ActionButtonsContainer>
);

export const SalesAgentActionButtons: React.FC<{
  onEdit: () => void;
  onDelete: () => void;
  onViewDetails: () => void;
  agentName: string;
}> = ({ onEdit, onDelete, onViewDetails, agentName }) => (
  <ActionButtonsContainer>
    <EditButton
      onPress={onEdit}
      requiredPermission="manage_staff"
    />
    <DeleteButton
      onPress={onDelete}
      requiredPermission="manage_staff"
      itemName={agentName}
    />
    <ViewDetailsButton onPress={onViewDetails} />
  </ActionButtonsContainer>
);

export const CustomerActionButtons: React.FC<{
  onEdit: () => void;
  onDelete: () => void;
  onViewDetails: () => void;
  customerName: string;
}> = ({ onEdit, onDelete, onViewDetails, customerName }) => (
  <ActionButtonsContainer>
    <EditButton
      onPress={onEdit}
      requiredPermission="manage_customers"
    />
    <DeleteButton
      onPress={onDelete}
      requiredPermission="manage_customers"
      itemName={customerName}
    />
    <ViewDetailsButton onPress={onViewDetails} />
  </ActionButtonsContainer>
);

const styles = StyleSheet.create({
  actionButton: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  disabled: {
    opacity: 0.5,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 8,
  },
});
