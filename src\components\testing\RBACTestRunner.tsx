/**
 * RBAC Test Runner Component
 * 
 * A development component for testing role-based access control
 * across all loyalty and discount management features.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '@/src/contexts/ThemeContext';
import { createStyleUtils } from '@/src/utils/themeUtils';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernButton } from '@/components/ui/ModernButton';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useRBAC } from '@/src/hooks/useRBAC';
import { UserRole } from '@/src/types/auth';
import {
  runComprehensiveRBACTests,
  generateRBACTestReport,
  RBACTestSuite,
  RBACTestResult,
} from '@/src/utils/rbacTestUtils';

interface RBACTestRunnerProps {
  onClose?: () => void;
}

export const RBACTestRunner: React.FC<RBACTestRunnerProps> = ({ onClose }) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const { hasPermission, userRole } = useRBAC();

  const [testSuites, setTestSuites] = useState<RBACTestSuite[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole | 'all'>('all');

  // Mock permission checker for different roles
  const hasPermissionForRole = (role: UserRole, permission: string): boolean => {
    // Define role-based permissions based on RBAC configuration
    const rolePermissions: Record<UserRole, string[]> = {
      staff: [
        'view_loyalty',
        'apply_discounts',
        'view_customers',
        'create_orders',
      ],
      manager: [
        'view_loyalty',
        'manage_loyalty',
        'apply_discounts',
        'manage_discounts',
        'view_analytics',
        'view_customers',
        'manage_customers',
        'create_orders',
        'view_orders',
      ],
      company_admin: [
        'view_loyalty',
        'manage_loyalty',
        'apply_discounts',
        'manage_discounts',
        'view_analytics',
        'view_customers',
        'manage_customers',
        'create_orders',
        'view_orders',
        'manage_staff',
      ],
      super_admin: [
        'view_loyalty',
        'manage_loyalty',
        'apply_discounts',
        'manage_discounts',
        'view_analytics',
        'view_customers',
        'manage_customers',
        'create_orders',
        'view_orders',
        'manage_staff',
        'manage_system',
      ],
    };

    return rolePermissions[role]?.includes(permission) || false;
  };

  const runTests = async () => {
    setIsRunning(true);
    try {
      const suites = runComprehensiveRBACTests(hasPermissionForRole);
      setTestSuites(suites);
    } catch (error) {
      Alert.alert('Error', 'Failed to run RBAC tests');
      console.error('RBAC test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const exportReport = () => {
    const report = generateRBACTestReport(testSuites);
    console.log('RBAC Test Report:', report);
    Alert.alert(
      'Report Generated',
      'RBAC test report has been logged to console. Check the developer console for details.'
    );
  };

  const getStatusColor = (passed: boolean) => {
    return passed ? theme.colors.success : theme.colors.error;
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? 'checkmark.circle.fill' : 'xmark.circle.fill';
  };

  const renderTestResult = (result: RBACTestResult, index: number) => (
    <View key={index} style={[styles.testResult, { borderLeftColor: getStatusColor(result.passed) }]}>
      <View style={styles.testHeader}>
        <IconSymbol
          name={getStatusIcon(result.passed)}
          size={16}
          color={getStatusColor(result.passed)}
        />
        <Text style={[styles.testDescription, { color: theme.colors.text }]}>
          {result.testCase.description}
        </Text>
      </View>
      
      <View style={styles.testDetails}>
        <Text style={[styles.testDetail, { color: theme.colors.textSecondary }]}>
          Expected: {result.testCase.expectedAccess ? 'Allow' : 'Deny'}
        </Text>
        <Text style={[styles.testDetail, { color: theme.colors.textSecondary }]}>
          Actual: {result.actualAccess ? 'Allow' : 'Deny'}
        </Text>
        {result.error && (
          <Text style={[styles.testError, { color: theme.colors.error }]}>
            Error: {result.error}
          </Text>
        )}
      </View>
    </View>
  );

  const renderTestSuite = (suite: RBACTestSuite, index: number) => {
    if (selectedRole !== 'all' && !suite.suiteName.toLowerCase().includes(selectedRole)) {
      return null;
    }

    return (
      <ModernCard key={index} style={styles.suiteCard}>
        <View style={styles.suiteHeader}>
          <Text style={[styles.suiteName, { color: theme.colors.text }]}>
            {suite.suiteName}
          </Text>
          <View style={styles.suiteStats}>
            <Text style={[styles.statText, { color: theme.colors.success }]}>
              ✓ {suite.passed}
            </Text>
            <Text style={[styles.statText, { color: theme.colors.error }]}>
              ✗ {suite.failed}
            </Text>
          </View>
        </View>

        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                backgroundColor: theme.colors.success,
                width: `${(suite.passed / suite.total) * 100}%`,
              },
            ]}
          />
        </View>

        <View style={styles.testResults}>
          {suite.results.map(renderTestResult)}
        </View>
      </ModernCard>
    );
  };

  const roles: Array<{ key: UserRole | 'all'; label: string }> = [
    { key: 'all', label: 'All Roles' },
    { key: 'staff', label: 'Staff' },
    { key: 'manager', label: 'Manager' },
    { key: 'company_admin', label: 'Company Admin' },
    { key: 'super_admin', label: 'Super Admin' },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          RBAC Test Runner
        </Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        )}
      </View>

      {/* Current User Info */}
      <ModernCard style={styles.userInfo}>
        <Text style={[styles.userInfoText, { color: theme.colors.text }]}>
          Current User Role: <Text style={{ color: theme.colors.primary }}>{userRole}</Text>
        </Text>
      </ModernCard>

      {/* Controls */}
      <View style={styles.controls}>
        <View style={styles.roleSelector}>
          <Text style={[styles.roleSelectorLabel, { color: theme.colors.text }]}>
            Filter by Role:
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {roles.map((role) => (
              <TouchableOpacity
                key={role.key}
                style={[
                  styles.roleButton,
                  {
                    backgroundColor: selectedRole === role.key 
                      ? theme.colors.primary 
                      : theme.colors.surface,
                  },
                ]}
                onPress={() => setSelectedRole(role.key)}
              >
                <Text
                  style={[
                    styles.roleButtonText,
                    {
                      color: selectedRole === role.key 
                        ? theme.colors.background 
                        : theme.colors.text,
                    },
                  ]}
                >
                  {role.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.actionButtons}>
          <ModernButton
            title={isRunning ? 'Running Tests...' : 'Run Tests'}
            onPress={runTests}
            loading={isRunning}
            disabled={isRunning}
            style={styles.actionButton}
          />
          
          {testSuites.length > 0 && (
            <ModernButton
              title="Export Report"
              onPress={exportReport}
              variant="secondary"
              style={styles.actionButton}
            />
          )}
        </View>
      </View>

      {/* Test Results */}
      <ScrollView style={styles.results} showsVerticalScrollIndicator={false}>
        {testSuites.map(renderTestSuite)}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  userInfo: {
    margin: 16,
    padding: 12,
  },
  userInfoText: {
    fontSize: 14,
    textAlign: 'center',
  },
  controls: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  roleSelector: {
    marginBottom: 16,
  },
  roleSelectorLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  roleButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  roleButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  results: {
    flex: 1,
    paddingHorizontal: 16,
  },
  suiteCard: {
    marginBottom: 16,
    padding: 16,
  },
  suiteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  suiteName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  suiteStats: {
    flexDirection: 'row',
    gap: 12,
  },
  statText: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 2,
    marginBottom: 16,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  testResults: {
    gap: 8,
  },
  testResult: {
    borderLeftWidth: 3,
    paddingLeft: 12,
    paddingVertical: 8,
  },
  testHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  testDescription: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  testDetails: {
    paddingLeft: 24,
  },
  testDetail: {
    fontSize: 12,
    marginBottom: 2,
  },
  testError: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});
