import React from 'react';
import { useSession } from '../../contexts/AuthContext';
import { hasPermission, hasAllPermissions, hasAnyPermission } from '../../config/rbac';

interface PermissionGateProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * PermissionGate Component
 * 
 * Conditionally renders children based on user permissions.
 * Integrates with AuthContext and RBAC configuration.
 * 
 * @param children - Content to render if user has required permissions
 * @param permission - Single permission to check
 * @param permissions - Array of permissions to check
 * @param requireAll - If true, user must have ALL permissions. If false, user needs ANY permission
 * @param fallback - Content to render if user doesn't have permissions
 * @param showFallback - Whether to show fallback content or nothing
 */
export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permission,
  permissions,
  requireAll = false,
  fallback = null,
  showFallback = false,
}) => {
  const { user, isPosAuthenticated } = useSession();

  // If not authenticated, don't show anything
  if (!isPosAuthenticated || !user) {
    return showFallback ? <>{fallback}</> : null;
  }

  const userPermissions = user.permissions || [];
  let hasAccess = false;

  if (permission) {
    // Single permission check
    hasAccess = hasPermission(userPermissions, permission);
  } else if (permissions && permissions.length > 0) {
    // Multiple permissions check
    if (requireAll) {
      hasAccess = hasAllPermissions(userPermissions, permissions);
    } else {
      hasAccess = hasAnyPermission(userPermissions, permissions);
    }
  } else {
    // No permissions specified, allow access
    hasAccess = true;
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

// Convenience components for common permission checks
export const CanManageStaff: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="manage_staff" fallback={fallback}>
    {children}
  </PermissionGate>
);

export const CanViewReports: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="view_reports" fallback={fallback}>
    {children}
  </PermissionGate>
);

export const CanManageCustomers: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="manage_customers" fallback={fallback}>
    {children}
  </PermissionGate>
);

export const CanManageInventory: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="manage_inventory" fallback={fallback}>
    {children}
  </PermissionGate>
);

export const CanManageOrders: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="manage_orders" fallback={fallback}>
    {children}
  </PermissionGate>
);

export const CanViewAnalytics: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="view_analytics" fallback={fallback}>
    {children}
  </PermissionGate>
);

export const CanManageSystem: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="manage_system" fallback={fallback}>
    {children}
  </PermissionGate>
);

export const CanManageDiscounts: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <PermissionGate permission="manage_discounts" fallback={fallback}>
    {children}
  </PermissionGate>
);

// Higher-order component for permission-based rendering
export const withPermission = <P extends object>(
  Component: React.ComponentType<P>,
  permission: string,
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <PermissionGate permission={permission} fallback={fallback}>
      <Component {...props} />
    </PermissionGate>
  );
};

// Higher-order component for multiple permissions
export const withPermissions = <P extends object>(
  Component: React.ComponentType<P>,
  permissions: string[],
  requireAll: boolean = false,
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <PermissionGate permissions={permissions} requireAll={requireAll} fallback={fallback}>
      <Component {...props} />
    </PermissionGate>
  );
};
