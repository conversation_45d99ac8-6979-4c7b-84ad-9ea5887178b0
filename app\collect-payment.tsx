import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { PermissionGate } from "@/src/components/rbac";
import { Colors } from "@/constants/Colors";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { UnifiedReceiptManager } from "@/src/services/UnifiedReceiptManager";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useScreenNavigation } from "@/src/hooks/useScreenNavigation";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { router, useLocalSearchParams } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const CollectPaymentScreen: React.FC = () => {
  const { isPosAuthenticated: isAuthenticated } = useSession();
  const { orderData } = useLocalSearchParams();

  const [order, setOrder] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentHistory, setPaymentHistory] = useState<any>(null);
  const [shopifyPaymentData, setShopifyPaymentData] = useState<any>(null);

  // Payment form state
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [paymentNotes, setPaymentNotes] = useState("");

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
    orderNumber?: string;
    paymentAmount?: number;
    paymentMethod?: string;
    receiptData?: any;
  }>({ title: "", message: "" });

  // Use enhanced navigation hook
  useScreenNavigation({
    title: "Collect Payment",
    forceTitle: true,
  });

  // RBAC permissions
  const { hasPermission } = useRBAC();

  // Theme system
  const theme = useTheme();
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const surfaceColor = useThemeColor({}, "surface");
  const successColor = useThemeColor({}, "success");
  const warningColor = useThemeColor({}, "warning");
  const errorColor = useThemeColor({}, "error");

  // Create styles using theme
  const styles = createStyles(theme);

  // Payment methods
  const paymentMethods = [
    { id: "cash", name: "Cash", icon: "banknote.fill" },
    { id: "mpesa", name: "M-Pesa", icon: "phone.fill" },
    { id: "card", name: "Card", icon: "creditcard.fill" },
    { id: "absa_till", name: "ABSA Till", icon: "building.columns.fill" },
  ];

  // Fetch payment history and Shopify data
  const fetchPaymentData = async (orderId: string) => {
    try {
      setIsLoading(true);
      const apiClient = getAPIClient();

      // Fetch payment history
      const historyResponse = await apiClient.getPaymentHistory(orderId);
      if (historyResponse.success) {
        console.log("📜 Payment history:", historyResponse.data);
        setPaymentHistory(historyResponse.data);
      }

      // Fetch current Shopify order status
      const statusResponse = await apiClient.getOrderPaymentStatus(orderId);
      if (statusResponse.success && statusResponse.data) {
        console.log("🏪 Shopify payment data:", statusResponse.data);
        console.log("🔍 Detailed payment amounts:", {
          totalAmount: statusResponse.data.totalAmount,
          paidAmount: statusResponse.data.paidAmount,
          outstandingAmount: statusResponse.data.outstandingAmount,
          financialStatus: statusResponse.data.financialStatus
        });
        setShopifyPaymentData(statusResponse.data);
      } else {
        console.error("❌ Failed to fetch Shopify payment data:", statusResponse);
      }

    } catch (error) {
      console.error("Failed to fetch payment data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Parse order data from params
  useEffect(() => {
    if (orderData) {
      try {
        const parsedOrder = JSON.parse(decodeURIComponent(orderData as string));
        console.log("📦 Received order data:", parsedOrder);
        setOrder(parsedOrder);

        // Calculate remaining balance
        const totalPrice = parseFloat(parsedOrder.totalPrice || parsedOrder.total_price || "0") || 0;
        const paidAmount = parseFloat(parsedOrder.paidAmount || "0") || 0;
        const remainingBalance = Math.max(0, totalPrice - paidAmount);

        console.log("💰 Frontend calculated amounts:", { totalPrice, paidAmount, remainingBalance });
        console.log("🔍 Frontend order financial status:", parsedOrder.financialStatus || parsedOrder.financial_status);

        // Set default payment amount to remaining balance
        setPaymentAmount((remainingBalance || 0).toFixed(2));

        // Fetch additional payment data
        const orderId = parsedOrder.id || parsedOrder.order_id;
        if (orderId) {
          fetchPaymentData(orderId);
        }
      } catch (error) {
        console.error("Failed to parse order data:", error);
        setModalData({
          title: "Error",
          message: "Invalid order data",
        });
        setShowErrorModal(true);
      }
    }
  }, [orderData]);

  // Calculate payment details
  const getPaymentDetails = () => {
    if (!order) return { totalPrice: 0, paidAmount: 0, remainingBalance: 0, paymentCount: 0, financialStatus: "pending", canCapture: false };

    // Use Shopify data if available for most accurate information
    if (shopifyPaymentData) {
      const totalPrice = shopifyPaymentData.totalAmount;
      const paidAmount = shopifyPaymentData.paidAmount; // Now correctly using totalReceivedSet from Shopify

      // For authorized orders, calculate remaining balance as totalAmount - paidAmount
      // For other orders, use outstandingAmount if available, otherwise calculate
      const remainingBalance = shopifyPaymentData.financialStatus === 'AUTHORIZED' && shopifyPaymentData.outstandingAmount === 0
        ? totalPrice - paidAmount
        : shopifyPaymentData.outstandingAmount;

      const shopifyDetails = {
        totalPrice,
        paidAmount,
        remainingBalance,
        paymentCount: shopifyPaymentData.receipts?.length || 0,
        financialStatus: shopifyPaymentData.financialStatus,
        canCapture: shopifyPaymentData.canCapture,
      };

      console.log("🧮 Frontend calculated amounts from Shopify data:", shopifyDetails);
      console.log("🏪 Using Shopify payment details (corrected field interpretation):", shopifyDetails);
      console.log("💡 paidAmount now correctly reflects totalReceivedSet (actual captured amount)");
      console.log("🔧 remainingBalance calculation:", {
        financialStatus: shopifyPaymentData.financialStatus,
        outstandingAmount: shopifyPaymentData.outstandingAmount,
        calculatedBalance: totalPrice - paidAmount,
        usedBalance: remainingBalance
      });

      // Determine expected payment strategy for user info
      const expectedStrategy = shopifyPaymentData.financialStatus === 'AUTHORIZED' && shopifyPaymentData.outstandingAmount === 0
        ? 'capture' : 'markAsPaid';
      console.log(`💡 Expected payment strategy: ${expectedStrategy}`);
      return shopifyDetails;
    }

    // Fallback to local order data
    const totalPrice = parseFloat(order.totalPrice || order.total_price || "0");
    let paidAmount = parseFloat(order.paidAmount || "0");

    // If we have payment history, calculate from receipts for more accuracy
    if (paymentHistory?.receipts?.length > 0) {
      paidAmount = paymentHistory.receipts.reduce((total: number, receipt: any) => {
        return total + parseFloat(receipt.amountPaid || 0);
      }, 0);
    }

    const remainingBalance = totalPrice - paidAmount;
    const paymentCount = paymentHistory?.receipts?.length || 0;

    return { totalPrice, paidAmount, remainingBalance, paymentCount, financialStatus: order.financialStatus || "pending", canCapture: false };
  };

  // Handle receipt printing
  const handlePrintReceipt = async () => {
    if (!modalData.receiptData) {
      setModalData({
        title: "Error",
        message: "No receipt data available for printing.",
      });
      setShowErrorModal(true);
      return;
    }

    try {
      console.log("🖨️ Starting receipt printing for payment collection...");

      // Create payment data for receipt generation
      const paymentDataForManualReceipt = {
        paymentMethods: [{
          method_name: modalData.receiptData.paymentCollection.paymentMethod,
          method_type: modalData.receiptData.paymentCollection.paymentMethod.toLowerCase(),
          amount: modalData.receiptData.paymentCollection.currentPayment,
          date: new Date().toLocaleDateString(),
          time: new Date().toLocaleTimeString(),
          transactionCode: `PAY-${modalData.receiptData.orderNumber}-${Date.now()}`
        }],
        completedAmount: modalData.receiptData.paymentCollection.totalPaid,
        transaction: {
          completed_amount: modalData.receiptData.paymentCollection.totalPaid
        }
      };

      // Use UnifiedReceiptManager for consistent receipt generation
      const unifiedResult = await UnifiedReceiptManager.generateReceipt(
        modalData.receiptData,
        {
          format: "thermal",
          autoPrint: true,
          printerType: "thermal",
        },
        paymentDataForManualReceipt
      );

      if (unifiedResult.success && unifiedResult.printed) {
        setModalData({
          ...modalData,
          message: `${modalData.message}\n\nReceipt has been printed successfully!`,
        });
      } else {
        // Fallback to PDF save
        const pdfResult = await UnifiedReceiptManager.generateReceipt(
          orderDataForReceipt,
          {
            format: "pdf",
            autoPrint: false,
            printerType: "web",
          }
        );

        if (pdfResult.success) {
          setModalData({
            ...modalData,
            message: `${modalData.message}\n\nReceipt has been saved as PDF!`,
          });
        } else {
          throw new Error(pdfResult.error || "Failed to generate receipt");
        }
      }
    } catch (error) {
      console.error("Receipt printing error:", error);
      setModalData({
        title: "Print Error",
        message: `Failed to print receipt: ${error.message}\n\nPlease check your printer connection and try again.`,
      });
      setShowErrorModal(true);
    }
  };

  // Handle collect payment
  const handleCollectPayment = async () => {
    if (!order || !hasPermission("collect_payments")) {
      setModalData({
        title: "Permission Denied",
        message: "You don't have permission to collect payments",
      });
      setShowErrorModal(true);
      return;
    }

    const amount = parseFloat(paymentAmount);
    const { remainingBalance } = getPaymentDetails();

    if (isNaN(amount) || amount <= 0) {
      setModalData({
        title: "Invalid Amount",
        message: "Please enter a valid payment amount",
      });
      setShowErrorModal(true);
      return;
    }

    if (amount > remainingBalance) {
      setModalData({
        title: "Amount Too High",
        message: `Payment amount cannot exceed remaining balance of KSh ${(remainingBalance || 0).toFixed(2)}`,
      });
      setShowErrorModal(true);
      return;
    }

    setIsProcessing(true);
    try {
      const apiClient = getAPIClient();
      const orderId = order.id || order.order_id;

      const paymentData = {
        orderId,
        amount,
        paymentMethod,
        notes: paymentNotes,
      };

      const response = await apiClient.collectPayment(paymentData);

      if (response.success) {
        // Store receipt data for printing - compatible with StandardizedReceiptService
        const receiptData = {
          // Basic order info
          orderNumber: order.name,
          name: order.name,
          id: order.id,
          createdAt: new Date().toISOString(),
          totalPrice: order.totalPrice,

          // Customer info
          customer: order.customer,
          lineItems: order.lineItems || [],

          // Payment collection specific data
          paymentCollection: {
            type: 'payment_collection',
            currentPayment: amount,
            totalPaid: response.data?.newPaidTotal || amount,
            remainingBalance: response.data?.remainingBalance || 0,
            paymentMethod: paymentMethod,
            processedBy: 'Staff',
            processedAt: new Date().toISOString()
          }
        };

        // Create payment data structure for StandardizedReceiptService
        const paymentDataForReceipt = {
          paymentMethods: [{
            method_name: paymentMethod,
            method_type: paymentMethod.toLowerCase(),
            amount: amount,
            date: new Date().toLocaleDateString(),
            time: new Date().toLocaleTimeString(),
            transactionCode: `PAY-${order.name}-${Date.now()}`
          }],
          completedAmount: response.data?.newPaidTotal || amount,
          transaction: {
            completed_amount: response.data?.newPaidTotal || amount
          }
        };

        setModalData({
          title: "Payment Collected",
          message: `Successfully collected KSh ${amount.toFixed(2)} payment`,
          orderNumber: order.name,
          paymentAmount: amount,
          paymentMethod: paymentMethod,
          receiptData: receiptData
        });
        setShowSuccessModal(true);

        // Auto-print receipt (similar to checkout.tsx)
        try {
          console.log("🖨️ Auto-printing receipt for payment collection...");

          const autoReceiptResult = await UnifiedReceiptManager.generateReceipt(
            receiptData,
            {
              format: "thermal",
              autoPrint: true,
              printerType: "thermal",
            },
            paymentDataForReceipt
          );

          if (autoReceiptResult.success && autoReceiptResult.printed) {
            console.log("✅ Receipt auto-printed successfully");
            setModalData(prev => ({
              ...prev,
              message: `${prev.message}\n\nReceipt has been printed automatically!`,
            }));
          } else {
            console.log("⚠️ Auto-print failed, user can manually print");
          }
        } catch (error) {
          console.error("Auto-print error:", error);
          // Don't show error for auto-print failure, user can manually print
        }

        // Refresh order data
        await fetchPaymentData(order.id.replace('gid://shopify/Order/', ''));
      } else {
        setModalData({
          title: "Payment Failed",
          message: response.error || "Failed to collect payment",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Failed to collect payment:", error);
      setModalData({
        title: "Payment Failed",
        message: "Failed to collect payment",
      });
      setShowErrorModal(true);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    router.back();
  };

  if (!isAuthenticated) {
    return null;
  }

  if (!order) {
    return (
      <View style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            Loading order...
          </Text>
        </View>
      </View>
    );
  }

  const orderNumber = order.orderNumber || order.number || order.name;
  // const financialStatus = order.financialStatus || order.financial_status;
  const { totalPrice, paidAmount, remainingBalance, paymentCount, financialStatus, canCapture } = getPaymentDetails();

  // Check if order is already fully paid (only for truly paid orders, not authorized)
  const isFullyPaid = remainingBalance <= 0 && financialStatus === 'PAID';

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: surfaceColor }]}>
        <View style={styles.headerContent}>
          <View>
            <Text style={[styles.headerTitle, { color: textColor }]}>
              Collect Payment
            </Text>
            <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
              Order #{orderNumber} • Status: {financialStatus?.toUpperCase()}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.cancelButton, { borderColor: errorColor }]}
            onPress={handleCancel}
          >
            <Text style={[styles.cancelButtonText, { color: errorColor }]}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Payment Summary */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Summary
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: textSecondary }]}>
              Order Total:
            </Text>
            <Text style={[styles.summaryValue, { color: textColor }]}>
              KSh {(totalPrice || 0).toFixed(2)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: textSecondary }]}>
              Amount Paid:
            </Text>
            <Text style={[styles.summaryValue, { color: successColor }]}>
              KSh {(paidAmount || 0).toFixed(2)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: textSecondary }]}>
              Status:
            </Text>
            <Text style={[styles.summaryValue, { color: textColor }]}>
              {financialStatus === 'authorized' ? 'PENDING PAYMENT' : financialStatus?.toUpperCase()}
              {canCapture && ' (Can Capture)'}
            </Text>
          </View>

          {paymentCount > 0 && (
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: textSecondary }]}>
                Previous Payments:
              </Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>
                {paymentCount} payment{paymentCount !== 1 ? 's' : ''}
              </Text>
            </View>
          )}

          <View style={[styles.summaryRow, styles.summaryRowHighlight]}>
            <Text style={[styles.summaryLabel, { color: textColor, fontWeight: "600" }]}>
              Remaining Balance:
            </Text>
            <Text style={[styles.summaryValue, { color: warningColor, fontWeight: "600" }]}>
              KSh {(remainingBalance || 0).toFixed(2)}
            </Text>
          </View>

          {/* Payment Strategy Info */}
          {shopifyPaymentData && !isFullyPaid && (
            <View style={[styles.summaryRow, { backgroundColor: "#E3F2FD", padding: 8, borderRadius: 6, marginTop: 8 }]}>
              <Text style={[styles.summaryLabel, { color: "#1976D2", fontSize: 12 }]}>
                Payment Method:
              </Text>
              <Text style={[styles.summaryValue, { color: "#1976D2", fontSize: 12 }]}>
                {shopifyPaymentData.financialStatus === 'AUTHORIZED' && shopifyPaymentData.outstandingAmount === 0
                  ? '🔐 Capture (First payment on authorized order)'
                  : '💰 Mark as Paid (Subsequent payment)'}
              </Text>
            </View>
          )}
        </ModernCard>

        {/* Fully Paid Warning */}
        {isFullyPaid && (
          <ModernCard style={{ backgroundColor: "#FFF3CD", borderColor: "#FFC107", borderWidth: 1 }}>
            <View style={styles.warningContainer}>
              <IconSymbol name="exclamationmark.triangle.fill" size={24} color="#FFC107" />
              <View style={styles.warningTextContainer}>
                <Text style={[styles.warningTitle, { color: "#856404" }]}>Order Already Paid</Text>
                <Text style={[styles.warningMessage, { color: "#856404" }]}>
                  This order has been fully paid. No additional payment can be collected.
                </Text>
              </View>
            </View>
          </ModernCard>
        )}

        {/* Payment Method Selection */}
        {!isFullyPaid && (
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Method
          </Text>
          
          <View style={styles.paymentMethodGrid}>
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentMethodButton,
                  {
                    backgroundColor: paymentMethod === method.id ? primaryColor : surfaceColor,
                    borderColor: paymentMethod === method.id ? primaryColor : Colors.light.border,
                  },
                ]}
                onPress={() => setPaymentMethod(method.id)}
              >
                <IconSymbol
                  name={method.icon as any}
                  size={24}
                  color={paymentMethod === method.id ? "white" : textColor}
                />
                <Text
                  style={[
                    styles.paymentMethodText,
                    {
                      color: paymentMethod === method.id ? "white" : textColor,
                    },
                  ]}
                >
                  {method.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ModernCard>
        )}

        {/* Payment Amount */}
        {!isFullyPaid && (
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Amount
          </Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Amount to Collect (KSh)
            </Text>
            <TextInput
              style={[styles.amountInput, { color: textColor, borderColor: Colors.light.border }]}
              value={paymentAmount}
              onChangeText={setPaymentAmount}
              placeholder="0.00"
              placeholderTextColor={textSecondary}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.quickAmountButtons}>
            <TouchableOpacity
              style={[styles.quickAmountButton, { backgroundColor: primaryColor }]}
              onPress={() => setPaymentAmount((remainingBalance || 0).toFixed(2))}
            >
              <Text style={styles.quickAmountText}>Full Balance</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickAmountButton, { backgroundColor: Colors.light.success }]}
              onPress={() => setPaymentAmount(((remainingBalance || 0) / 2).toFixed(2))}
            >
              <Text style={styles.quickAmountText}>Half</Text>
            </TouchableOpacity>
          </View>
        </ModernCard>
        )}

        {/* Payment Notes */}
        {!isFullyPaid && (
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Notes (Optional)
          </Text>
          <TextInput
            style={[styles.textInput, { color: textColor, borderColor: Colors.light.border }]}
            value={paymentNotes}
            onChangeText={setPaymentNotes}
            placeholder="Add notes about this payment..."
            placeholderTextColor={textSecondary}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </ModernCard>
        )}

        {/* Collect Payment Button */}
        {!isFullyPaid && (
        <TouchableOpacity
          style={[styles.collectButton, { backgroundColor: successColor }]}
          onPress={handleCollectPayment}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <IconSymbol name="creditcard.fill" size={20} color="white" />
              <Text style={styles.collectButtonText}>
                Collect KSh {parseFloat(paymentAmount || "0").toFixed(2)}
              </Text>
            </>
          )}
        </TouchableOpacity>
        )}
      </ScrollView>

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowErrorModal(false)}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => {
          setShowSuccessModal(false);
          // Navigate back after modal closes
          setTimeout(() => {
            router.back();
          }, 500);
        }}
        actions={[
          {
            title: "Print Receipt",
            onPress: handlePrintReceipt,
            variant: "primary",
            icon: "printer.fill",
            dismissModal: false, // Keep modal open while printing
          },
          {
            title: "Done",
            onPress: () => {
              setShowSuccessModal(false);
              setTimeout(() => {
                router.back();
              }, 500);
            },
            variant: "outline",
            icon: "checkmark.circle.fill",
          },
        ]}
      />
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: Colors.light.border,
    },
    headerContent: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerTitle: {
      ...theme.typography.h3,
      marginBottom: 4,
    },
    headerSubtitle: {
      ...theme.typography.caption,
    },
    cancelButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderWidth: 1,
      borderRadius: 6,
    },
    cancelButtonText: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      ...theme.typography.h4,
      marginBottom: theme.spacing.md,
    },
    summaryRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.sm,
    },
    summaryRowHighlight: {
      borderTopWidth: 1,
      borderTopColor: Colors.light.border,
      marginTop: theme.spacing.sm,
      paddingTop: theme.spacing.md,
    },
    summaryLabel: {
      ...theme.typography.bodyMedium,
    },
    summaryValue: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    paymentMethodGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: theme.spacing.sm,
    },
    paymentMethodButton: {
      flex: 1,
      minWidth: "45%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.md,
      borderWidth: 1,
      borderRadius: 8,
      gap: theme.spacing.sm,
    },
    paymentMethodText: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    inputGroup: {
      marginBottom: theme.spacing.md,
    },
    inputLabel: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.sm,
      fontWeight: "600",
    },
    amountInput: {
      borderWidth: 1,
      borderRadius: 6,
      padding: theme.spacing.md,
      ...theme.typography.h3,
      textAlign: "center",
      minHeight: 60,
    },
    quickAmountButtons: {
      flexDirection: "row",
      gap: theme.spacing.sm,
      marginTop: theme.spacing.sm,
    },
    quickAmountButton: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      borderRadius: 6,
      alignItems: "center",
    },
    quickAmountText: {
      ...theme.typography.bodyMedium,
      color: "white",
      fontWeight: "600",
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 6,
      padding: theme.spacing.md,
      ...theme.typography.body,
      minHeight: 80,
    },
    collectButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.lg,
      borderRadius: 8,
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.xl,
    },
    collectButtonText: {
      ...theme.typography.h4,
      color: "white",
      fontWeight: "600",
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    loadingText: {
      ...theme.typography.body,
      marginTop: theme.spacing.md,
    },
    warningContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.md,
    },
    warningTextContainer: {
      flex: 1,
    },
    warningTitle: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      marginBottom: theme.spacing.xs,
    },
    warningMessage: {
      ...theme.typography.bodySmall,
    },
  });

export default CollectPaymentScreen;
