import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { PermissionGate } from "@/src/components/rbac";
import { Colors } from "@/constants/Colors";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useScreenNavigation } from "@/src/hooks/useScreenNavigation";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { router, useLocalSearchParams } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const CollectPaymentScreen: React.FC = () => {
  const { isPosAuthenticated: isAuthenticated } = useSession();
  const { orderData } = useLocalSearchParams();

  const [order, setOrder] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Payment form state
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [paymentNotes, setPaymentNotes] = useState("");

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
  }>({ title: "", message: "" });

  // Use enhanced navigation hook
  useScreenNavigation({
    title: "Collect Payment",
    forceTitle: true,
  });

  // RBAC permissions
  const { hasPermission } = useRBAC();

  // Theme system
  const theme = useTheme();
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const surfaceColor = useThemeColor({}, "surface");
  const successColor = useThemeColor({}, "success");
  const warningColor = useThemeColor({}, "warning");
  const errorColor = useThemeColor({}, "error");

  // Create styles using theme
  const styles = createStyles(theme);

  // Payment methods
  const paymentMethods = [
    { id: "cash", name: "Cash", icon: "banknote.fill" },
    { id: "mpesa", name: "M-Pesa", icon: "phone.fill" },
    { id: "card", name: "Card", icon: "creditcard.fill" },
    { id: "absa_till", name: "ABSA Till", icon: "building.columns.fill" },
  ];

  // Parse order data from params
  useEffect(() => {
    if (orderData) {
      try {
        const parsedOrder = JSON.parse(decodeURIComponent(orderData as string));
        setOrder(parsedOrder);
        
        // Calculate remaining balance
        const totalPrice = parseFloat(parsedOrder.totalPrice || parsedOrder.total_price || "0");
        const paidAmount = parseFloat(parsedOrder.paidAmount || "0");
        const remainingBalance = totalPrice - paidAmount;
        
        // Set default payment amount to remaining balance
        setPaymentAmount(remainingBalance.toFixed(2));
      } catch (error) {
        console.error("Failed to parse order data:", error);
        setModalData({
          title: "Error",
          message: "Invalid order data",
        });
        setShowErrorModal(true);
      }
    }
  }, [orderData]);

  // Calculate payment details
  const getPaymentDetails = () => {
    if (!order) return { totalPrice: 0, paidAmount: 0, remainingBalance: 0 };
    
    const totalPrice = parseFloat(order.totalPrice || order.total_price || "0");
    const paidAmount = parseFloat(order.paidAmount || "0");
    const remainingBalance = totalPrice - paidAmount;
    
    return { totalPrice, paidAmount, remainingBalance };
  };

  // Handle collect payment
  const handleCollectPayment = async () => {
    if (!order || !hasPermission("collect_payments")) {
      setModalData({
        title: "Permission Denied",
        message: "You don't have permission to collect payments",
      });
      setShowErrorModal(true);
      return;
    }

    const amount = parseFloat(paymentAmount);
    const { remainingBalance } = getPaymentDetails();

    if (isNaN(amount) || amount <= 0) {
      setModalData({
        title: "Invalid Amount",
        message: "Please enter a valid payment amount",
      });
      setShowErrorModal(true);
      return;
    }

    if (amount > remainingBalance) {
      setModalData({
        title: "Amount Too High",
        message: `Payment amount cannot exceed remaining balance of KSh ${remainingBalance.toFixed(2)}`,
      });
      setShowErrorModal(true);
      return;
    }

    setIsProcessing(true);
    try {
      const apiClient = getAPIClient();
      const orderId = order.id || order.order_id;

      const paymentData = {
        orderId,
        amount,
        paymentMethod,
        notes: paymentNotes,
      };

      const response = await apiClient.collectPayment(paymentData);

      if (response.success) {
        setModalData({
          title: "Payment Collected",
          message: `Successfully collected KSh ${amount.toFixed(2)} payment`,
        });
        setShowSuccessModal(true);
        
        // Navigate back after a delay
        setTimeout(() => {
          router.back();
        }, 2000);
      } else {
        setModalData({
          title: "Payment Failed",
          message: response.error || "Failed to collect payment",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Failed to collect payment:", error);
      setModalData({
        title: "Payment Failed",
        message: "Failed to collect payment",
      });
      setShowErrorModal(true);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    router.back();
  };

  if (!isAuthenticated) {
    return null;
  }

  if (!order) {
    return (
      <View style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            Loading order...
          </Text>
        </View>
      </View>
    );
  }

  const orderNumber = order.orderNumber || order.number || order.name;
  const financialStatus = order.financialStatus || order.financial_status;
  const { totalPrice, paidAmount, remainingBalance } = getPaymentDetails();

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: surfaceColor }]}>
        <View style={styles.headerContent}>
          <View>
            <Text style={[styles.headerTitle, { color: textColor }]}>
              Collect Payment
            </Text>
            <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
              Order #{orderNumber} • Status: {financialStatus?.toUpperCase()}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.cancelButton, { borderColor: errorColor }]}
            onPress={handleCancel}
          >
            <Text style={[styles.cancelButtonText, { color: errorColor }]}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Payment Summary */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Summary
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: textSecondary }]}>
              Order Total:
            </Text>
            <Text style={[styles.summaryValue, { color: textColor }]}>
              KSh {totalPrice.toFixed(2)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: textSecondary }]}>
              Amount Paid:
            </Text>
            <Text style={[styles.summaryValue, { color: successColor }]}>
              KSh {paidAmount.toFixed(2)}
            </Text>
          </View>

          <View style={[styles.summaryRow, styles.summaryRowHighlight]}>
            <Text style={[styles.summaryLabel, { color: textColor, fontWeight: "600" }]}>
              Remaining Balance:
            </Text>
            <Text style={[styles.summaryValue, { color: warningColor, fontWeight: "600" }]}>
              KSh {remainingBalance.toFixed(2)}
            </Text>
          </View>
        </ModernCard>

        {/* Payment Method Selection */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Method
          </Text>
          
          <View style={styles.paymentMethodGrid}>
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentMethodButton,
                  {
                    backgroundColor: paymentMethod === method.id ? primaryColor : surfaceColor,
                    borderColor: paymentMethod === method.id ? primaryColor : Colors.light.border,
                  },
                ]}
                onPress={() => setPaymentMethod(method.id)}
              >
                <IconSymbol
                  name={method.icon as any}
                  size={24}
                  color={paymentMethod === method.id ? "white" : textColor}
                />
                <Text
                  style={[
                    styles.paymentMethodText,
                    {
                      color: paymentMethod === method.id ? "white" : textColor,
                    },
                  ]}
                >
                  {method.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ModernCard>

        {/* Payment Amount */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Amount
          </Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Amount to Collect (KSh)
            </Text>
            <TextInput
              style={[styles.amountInput, { color: textColor, borderColor: Colors.light.border }]}
              value={paymentAmount}
              onChangeText={setPaymentAmount}
              placeholder="0.00"
              placeholderTextColor={textSecondary}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.quickAmountButtons}>
            <TouchableOpacity
              style={[styles.quickAmountButton, { backgroundColor: primaryColor }]}
              onPress={() => setPaymentAmount(remainingBalance.toFixed(2))}
            >
              <Text style={styles.quickAmountText}>Full Balance</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.quickAmountButton, { backgroundColor: Colors.light.success }]}
              onPress={() => setPaymentAmount((remainingBalance / 2).toFixed(2))}
            >
              <Text style={styles.quickAmountText}>Half</Text>
            </TouchableOpacity>
          </View>
        </ModernCard>

        {/* Payment Notes */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Notes (Optional)
          </Text>
          <TextInput
            style={[styles.textInput, { color: textColor, borderColor: Colors.light.border }]}
            value={paymentNotes}
            onChangeText={setPaymentNotes}
            placeholder="Add notes about this payment..."
            placeholderTextColor={textSecondary}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </ModernCard>

        {/* Collect Payment Button */}
        <TouchableOpacity
          style={[styles.collectButton, { backgroundColor: successColor }]}
          onPress={handleCollectPayment}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <IconSymbol name="creditcard.fill" size={20} color="white" />
              <Text style={styles.collectButtonText}>
                Collect KSh {parseFloat(paymentAmount || "0").toFixed(2)}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowErrorModal(false)}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowSuccessModal(false)}
      />
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: Colors.light.border,
    },
    headerContent: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerTitle: {
      ...theme.typography.h3,
      marginBottom: 4,
    },
    headerSubtitle: {
      ...theme.typography.caption,
    },
    cancelButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderWidth: 1,
      borderRadius: 6,
    },
    cancelButtonText: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      ...theme.typography.h4,
      marginBottom: theme.spacing.md,
    },
    summaryRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.sm,
    },
    summaryRowHighlight: {
      borderTopWidth: 1,
      borderTopColor: Colors.light.border,
      marginTop: theme.spacing.sm,
      paddingTop: theme.spacing.md,
    },
    summaryLabel: {
      ...theme.typography.bodyMedium,
    },
    summaryValue: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    paymentMethodGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: theme.spacing.sm,
    },
    paymentMethodButton: {
      flex: 1,
      minWidth: "45%",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.md,
      borderWidth: 1,
      borderRadius: 8,
      gap: theme.spacing.sm,
    },
    paymentMethodText: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    inputGroup: {
      marginBottom: theme.spacing.md,
    },
    inputLabel: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.sm,
      fontWeight: "600",
    },
    amountInput: {
      borderWidth: 1,
      borderRadius: 6,
      padding: theme.spacing.md,
      ...theme.typography.h3,
      textAlign: "center",
      minHeight: 60,
    },
    quickAmountButtons: {
      flexDirection: "row",
      gap: theme.spacing.sm,
      marginTop: theme.spacing.sm,
    },
    quickAmountButton: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      borderRadius: 6,
      alignItems: "center",
    },
    quickAmountText: {
      ...theme.typography.bodyMedium,
      color: "white",
      fontWeight: "600",
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 6,
      padding: theme.spacing.md,
      ...theme.typography.body,
      minHeight: 80,
    },
    collectButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.lg,
      borderRadius: 8,
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.xl,
    },
    collectButtonText: {
      ...theme.typography.h4,
      color: "white",
      fontWeight: "600",
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    loadingText: {
      ...theme.typography.body,
      marginTop: theme.spacing.md,
    },
  });

export default CollectPaymentScreen;
