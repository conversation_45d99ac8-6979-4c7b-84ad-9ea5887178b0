/**
 * Ticket Validation Indicator
 * 
 * Real-time validation display for tickets with inventory checks,
 * error highlighting, and automatic adjustment suggestions.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { IconSymbol } from './IconSymbol';
import { ModernButton } from './ModernButton';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Ticket } from '@/src/store/slices/ticketSlice';
import { 
  ticketInventoryValidator,
  InventoryValidationResult,
  InventoryError,
  InventoryWarning,
} from '@/src/services/TicketInventoryValidator';

interface TicketValidationIndicatorProps {
  ticket: Ticket;
  onAdjustmentApplied?: (adjustedItems: any[]) => void;
  showDetails?: boolean;
  autoValidate?: boolean;
  validationInterval?: number; // in milliseconds
}

export const TicketValidationIndicator: React.FC<TicketValidationIndicatorProps> = ({
  ticket,
  onAdjustmentApplied,
  showDetails = true,
  autoValidate = true,
  validationInterval = 30000, // 30 seconds
}) => {
  const theme = useTheme();
  const [validation, setValidation] = useState<InventoryValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidated, setLastValidated] = useState<Date | null>(null);
  const [showDetailedView, setShowDetailedView] = useState(false);

  const styles = createStyles(theme);

  // Auto-validation effect
  useEffect(() => {
    if (autoValidate) {
      validateTicket();
      
      const interval = setInterval(() => {
        validateTicket();
      }, validationInterval);

      return () => clearInterval(interval);
    }
  }, [ticket.id, ticket.items, autoValidate, validationInterval]);

  const validateTicket = async () => {
    if (isValidating) return;

    setIsValidating(true);
    try {
      const result = await ticketInventoryValidator.validateTicketInventory(ticket);
      setValidation(result);
      setLastValidated(new Date());
    } catch (error) {
      console.error('Validation failed:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const handleApplyAdjustments = async () => {
    if (!validation || !onAdjustmentApplied) return;

    const autoAdjustments = validation.suggestions.filter(s => s.autoApplicable);
    
    if (autoAdjustments.length === 0) {
      Alert.alert('No Adjustments', 'No automatic adjustments are available for this ticket.');
      return;
    }

    Alert.alert(
      'Apply Adjustments',
      `Apply ${autoAdjustments.length} automatic adjustment${autoAdjustments.length === 1 ? '' : 's'}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Apply',
          onPress: async () => {
            try {
              const adjustedItems = await ticketInventoryValidator.applyAutoAdjustments(
                ticket,
                autoAdjustments
              );
              onAdjustmentApplied(adjustedItems);
              
              // Re-validate after adjustments
              setTimeout(() => validateTicket(), 500);
            } catch (error) {
              Alert.alert('Error', 'Failed to apply adjustments. Please try again.');
            }
          },
        },
      ]
    );
  };

  const getValidationStatus = () => {
    if (!validation) return 'unknown';
    if (validation.isValid) return 'valid';
    if (validation.errors.length > 0) return 'error';
    if (validation.warnings.length > 0) return 'warning';
    return 'valid';
  };

  const getStatusColor = () => {
    const status = getValidationStatus();
    switch (status) {
      case 'valid': return theme.colors.success;
      case 'warning': return theme.colors.warning;
      case 'error': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getStatusIcon = () => {
    const status = getValidationStatus();
    switch (status) {
      case 'valid': return 'checkmark.circle.fill';
      case 'warning': return 'exclamationmark.triangle.fill';
      case 'error': return 'xmark.circle.fill';
      default: return 'questionmark.circle';
    }
  };

  const renderErrorItem = (error: InventoryError, index: number) => (
    <View key={index} style={[styles.issueItem, styles.errorItem]}>
      <IconSymbol name="xmark.circle" size={16} color={theme.colors.error} />
      <View style={styles.issueContent}>
        <Text style={[styles.issueTitle, { color: theme.colors.error }]}>
          {error.title}
        </Text>
        <Text style={[styles.issueMessage, { color: theme.colors.textSecondary }]}>
          {error.message}
        </Text>
      </View>
    </View>
  );

  const renderWarningItem = (warning: InventoryWarning, index: number) => (
    <View key={index} style={[styles.issueItem, styles.warningItem]}>
      <IconSymbol 
        name="exclamationmark.triangle" 
        size={16} 
        color={theme.colors.warning} 
      />
      <View style={styles.issueContent}>
        <Text style={[styles.issueTitle, { color: theme.colors.warning }]}>
          {warning.title}
        </Text>
        <Text style={[styles.issueMessage, { color: theme.colors.textSecondary }]}>
          {warning.message}
        </Text>
      </View>
    </View>
  );

  const renderCompactView = () => (
    <TouchableOpacity
      style={[
        styles.compactContainer,
        { backgroundColor: getStatusColor() + '20', borderColor: getStatusColor() },
      ]}
      onPress={() => setShowDetailedView(true)}
    >
      <View style={styles.compactContent}>
        <IconSymbol name={getStatusIcon()} size={20} color={getStatusColor()} />
        
        <View style={styles.compactText}>
          <Text style={[styles.compactTitle, { color: theme.colors.text }]}>
            {isValidating ? 'Validating...' : getValidationStatusText()}
          </Text>
          
          {validation && validation.totalIssues > 0 && (
            <Text style={[styles.compactSubtitle, { color: theme.colors.textSecondary }]}>
              {validation.errors.length} error{validation.errors.length === 1 ? '' : 's'}, {' '}
              {validation.warnings.length} warning{validation.warnings.length === 1 ? '' : 's'}
            </Text>
          )}
        </View>

        {isValidating && (
          <ActivityIndicator size="small" color={getStatusColor()} />
        )}
      </View>
    </TouchableOpacity>
  );

  const renderDetailedView = () => (
    <View style={[styles.detailedContainer, { backgroundColor: theme.colors.backgroundSecondary }]}>
      {/* Header */}
      <View style={styles.detailedHeader}>
        <View style={styles.headerLeft}>
          <IconSymbol name={getStatusIcon()} size={24} color={getStatusColor()} />
          <Text style={[styles.detailedTitle, { color: theme.colors.text }]}>
            Ticket Validation
          </Text>
        </View>
        
        <TouchableOpacity
          onPress={() => setShowDetailedView(false)}
          style={styles.collapseButton}
        >
          <IconSymbol name="chevron.up" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      {/* Status Summary */}
      <View style={styles.statusSummary}>
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getValidationStatusText()}
        </Text>
        
        {lastValidated && (
          <Text style={[styles.lastValidatedText, { color: theme.colors.textSecondary }]}>
            Last checked: {lastValidated.toLocaleTimeString()}
          </Text>
        )}
      </View>

      {/* Issues List */}
      {validation && validation.totalIssues > 0 && (
        <View style={styles.issuesList}>
          {validation.errors.map(renderErrorItem)}
          {validation.warnings.map(renderWarningItem)}
        </View>
      )}

      {/* Actions */}
      <View style={styles.actions}>
        <ModernButton
          title="Refresh"
          onPress={validateTicket}
          loading={isValidating}
          disabled={isValidating}
          variant="outline"
          style={styles.actionButton}
        />
        
        {validation && validation.suggestions.some(s => s.autoApplicable) && (
          <ModernButton
            title="Auto-Fix"
            onPress={handleApplyAdjustments}
            disabled={isValidating}
            style={styles.actionButton}
          />
        )}
      </View>
    </View>
  );

  const getValidationStatusText = () => {
    if (!validation) return 'Not validated';
    if (validation.isValid) return 'All items valid';
    if (validation.errors.length > 0) return 'Issues found';
    if (validation.warnings.length > 0) return 'Warnings detected';
    return 'Validation complete';
  };

  if (!showDetails) {
    return renderCompactView();
  }

  return showDetailedView ? renderDetailedView() : renderCompactView();
};

const createStyles = (theme: any) => {
  return StyleSheet.create({
    compactContainer: {
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      padding: theme.spacing.md,
      marginVertical: theme.spacing.xs,
    },
    compactContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    compactText: {
      flex: 1,
    },
    compactTitle: {
      fontSize: 14,
      fontWeight: '600',
    },
    compactSubtitle: {
      fontSize: 12,
      marginTop: 2,
    },
    detailedContainer: {
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      marginVertical: theme.spacing.sm,
    },
    detailedHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.md,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    detailedTitle: {
      fontSize: 16,
      fontWeight: '600',
    },
    collapseButton: {
      padding: theme.spacing.xs,
    },
    statusSummary: {
      marginBottom: theme.spacing.md,
    },
    statusText: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    lastValidatedText: {
      fontSize: 12,
    },
    issuesList: {
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.lg,
    },
    issueItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: theme.spacing.sm,
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.sm,
    },
    errorItem: {
      backgroundColor: theme.colors.error + '10',
    },
    warningItem: {
      backgroundColor: theme.colors.warning + '10',
    },
    issueContent: {
      flex: 1,
    },
    issueTitle: {
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 2,
    },
    issueMessage: {
      fontSize: 12,
      lineHeight: 16,
    },
    actions: {
      flexDirection: 'row',
      gap: theme.spacing.sm,
    },
    actionButton: {
      flex: 1,
    },
  });
};

export default TicketValidationIndicator;
