import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { PermissionGate } from "@/src/components/rbac";
import { Colors } from "@/constants/Colors";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useScreenNavigation } from "@/src/hooks/useScreenNavigation";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { router, useLocalSearchParams } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const EditOrderScreen: React.FC = () => {
  const { isPosAuthenticated: isAuthenticated } = useSession();
  const { orderData } = useLocalSearchParams();

  const [order, setOrder] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Form state
  const [editedOrder, setEditedOrder] = useState<any>({});

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
  }>({ title: "", message: "" });

  // Use enhanced navigation hook
  useScreenNavigation({
    title: "Edit Order",
    forceTitle: true,
  });

  // RBAC permissions
  const { hasPermission } = useRBAC();

  // Theme system
  const theme = useTheme();
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const surfaceColor = useThemeColor({}, "surface");
  const successColor = useThemeColor({}, "success");
  const warningColor = useThemeColor({}, "warning");
  const errorColor = useThemeColor({}, "error");

  // Create styles using theme
  const styles = createStyles(theme);

  // Parse order data from params
  useEffect(() => {
    if (orderData) {
      try {
        const parsedOrder = JSON.parse(decodeURIComponent(orderData as string));
        setOrder(parsedOrder);
        setEditedOrder({
          notes: parsedOrder.note || "",
          customerEmail: parsedOrder.customer?.email || "",
          customerPhone: parsedOrder.customer?.phone || "",
          shippingAddress: parsedOrder.shippingAddress || {},
        });
      } catch (error) {
        console.error("Failed to parse order data:", error);
        setModalData({
          title: "Error",
          message: "Invalid order data",
        });
        setShowErrorModal(true);
      }
    }
  }, [orderData]);

  // Handle save order changes
  const handleSaveChanges = async () => {
    if (!order || !hasPermission("edit_orders")) {
      setModalData({
        title: "Permission Denied",
        message: "You don't have permission to edit orders",
      });
      setShowErrorModal(true);
      return;
    }

    setIsSaving(true);
    try {
      const apiClient = getAPIClient();
      const orderId = order.id || order.order_id;

      const updateData = {
        orderId,
        notes: editedOrder.notes,
        customerEmail: editedOrder.customerEmail,
        customerPhone: editedOrder.customerPhone,
        shippingAddress: editedOrder.shippingAddress,
      };

      const response = await apiClient.updateOrder(updateData);

      if (response.success) {
        setModalData({
          title: "Success",
          message: "Order updated successfully",
        });
        setShowSuccessModal(true);
        
        // Navigate back after a delay
        setTimeout(() => {
          router.back();
        }, 1500);
      } else {
        setModalData({
          title: "Error",
          message: response.error || "Failed to update order",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Failed to update order:", error);
      setModalData({
        title: "Error",
        message: "Failed to update order",
      });
      setShowErrorModal(true);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    Alert.alert(
      "Discard Changes",
      "Are you sure you want to discard your changes?",
      [
        { text: "Keep Editing", style: "cancel" },
        { text: "Discard", style: "destructive", onPress: () => router.back() },
      ]
    );
  };

  if (!isAuthenticated) {
    return null;
  }

  if (!order) {
    return (
      <View style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            Loading order...
          </Text>
        </View>
      </View>
    );
  }

  const orderNumber = order.orderNumber || order.number || order.name;
  const totalPrice = order.totalPrice || order.total_price;
  const financialStatus = order.financialStatus || order.financial_status;

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: surfaceColor }]}>
        <View style={styles.headerContent}>
          <View>
            <Text style={[styles.headerTitle, { color: textColor }]}>
              Edit Order #{orderNumber}
            </Text>
            <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
              Total: KSh {parseFloat(totalPrice || "0").toFixed(2)} • Status: {financialStatus?.toUpperCase()}
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: errorColor }]}
              onPress={handleCancel}
            >
              <Text style={[styles.cancelButtonText, { color: errorColor }]}>
                Cancel
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: successColor }]}
              onPress={handleSaveChanges}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <>
                  <IconSymbol name="checkmark.circle.fill" size={16} color="white" />
                  <Text style={styles.saveButtonText}>Save</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Order Notes */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Order Notes
          </Text>
          <TextInput
            style={[styles.textInput, { color: textColor, borderColor: Colors.light.border }]}
            value={editedOrder.notes}
            onChangeText={(text) => setEditedOrder(prev => ({ ...prev, notes: text }))}
            placeholder="Add notes about this order..."
            placeholderTextColor={textSecondary}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </ModernCard>

        {/* Customer Information */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Customer Information
          </Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Email Address
            </Text>
            <TextInput
              style={[styles.textInput, { color: textColor, borderColor: Colors.light.border }]}
              value={editedOrder.customerEmail}
              onChangeText={(text) => setEditedOrder(prev => ({ ...prev, customerEmail: text }))}
              placeholder="<EMAIL>"
              placeholderTextColor={textSecondary}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Phone Number
            </Text>
            <TextInput
              style={[styles.textInput, { color: textColor, borderColor: Colors.light.border }]}
              value={editedOrder.customerPhone}
              onChangeText={(text) => setEditedOrder(prev => ({ ...prev, customerPhone: text }))}
              placeholder="+254 700 000 000"
              placeholderTextColor={textSecondary}
              keyboardType="phone-pad"
            />
          </View>
        </ModernCard>

        {/* Order Items - Read Only */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Order Items (Read Only)
          </Text>
          <Text style={[styles.sectionNote, { color: textSecondary }]}>
            To modify items, use the POS system to create a new order or process a refund.
          </Text>
          
          {(order.lineItems || order.line_items || []).map((item: any, index: number) => (
            <View key={item.id || index} style={styles.orderItem}>
              <View style={styles.itemInfo}>
                <Text style={[styles.itemTitle, { color: textColor }]}>
                  {item.title}
                </Text>
                {item.variantTitle && item.variantTitle !== "Default Title" && (
                  <Text style={[styles.itemVariant, { color: textSecondary }]}>
                    {item.variantTitle}
                  </Text>
                )}
              </View>
              <Text style={[styles.itemPrice, { color: textColor }]}>
                {item.quantity} × KSh {parseFloat(item.price).toFixed(2)}
              </Text>
            </View>
          ))}
        </ModernCard>
      </ScrollView>

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowErrorModal(false)}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowSuccessModal(false)}
      />
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: Colors.light.border,
    },
    headerContent: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    headerTitle: {
      ...theme.typography.h3,
      marginBottom: 4,
    },
    headerSubtitle: {
      ...theme.typography.caption,
    },
    headerActions: {
      flexDirection: "row",
      gap: theme.spacing.sm,
    },
    cancelButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderWidth: 1,
      borderRadius: 6,
    },
    cancelButtonText: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    saveButton: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: 6,
      gap: 4,
    },
    saveButtonText: {
      ...theme.typography.bodyMedium,
      color: "white",
      fontWeight: "600",
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      ...theme.typography.h4,
      marginBottom: theme.spacing.md,
    },
    sectionNote: {
      ...theme.typography.caption,
      marginBottom: theme.spacing.md,
      fontStyle: "italic",
    },
    inputGroup: {
      marginBottom: theme.spacing.md,
    },
    inputLabel: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.sm,
      fontWeight: "600",
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 6,
      padding: theme.spacing.md,
      ...theme.typography.body,
      minHeight: 44,
    },
    orderItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: Colors.light.border,
    },
    itemInfo: {
      flex: 1,
    },
    itemTitle: {
      ...theme.typography.bodyMedium,
    },
    itemVariant: {
      ...theme.typography.caption,
    },
    itemPrice: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    loadingText: {
      ...theme.typography.body,
      marginTop: theme.spacing.md,
    },
  });

export default EditOrderScreen;
