// Test script for split payment functionality
console.log("🧪 Testing Split Payment System...\n");

// Mock payment methods (matching the actual implementation)
const PAYMENT_METHODS = [
  {
    id: "cash",
    name: "Cash",
    type: "cash",
    enabled: true,
    placeholder: false,
    icon: "💵",
    description: "Cash payment - immediate processing",
    requiresAmount: true,
  },
  {
    id: "credit",
    name: "Credit",
    type: "credit",
    enabled: true,
    placeholder: false,
    icon: "💳",
    description: "Credit payment - immediate processing",
    requiresAmount: true,
  },
  {
    id: "absa_till",
    name: "ABSA Till",
    type: "absa_till",
    enabled: true,
    placeholder: false,
    comingSoon: false,
    icon: "📱",
    description: "Mobile money payment via ABSA Till Number",
    requiresAmount: true,
    requiresReference: true,
  },
];

// Test 1: Split Payment Validation
console.log("1. Testing Split Payment Validation:");

function validateSplitPayment(totalAmount, payments) {
  if (!payments || payments.length === 0) {
    return { valid: false, error: "At least one payment method is required" };
  }

  if (payments.length > 5) {
    return { valid: false, error: "Maximum 5 payment methods allowed" };
  }

  // Calculate total payment amount
  const totalPaymentAmount = payments.reduce((sum, payment) => {
    return sum + (payment.amount || 0);
  }, 0);

  // Check if total payments match the required amount
  if (Math.abs(totalPaymentAmount - totalAmount) > 0.01) {
    return {
      valid: false,
      error: `Payment total (${totalPaymentAmount}) does not match order total (${totalAmount})`,
      totalPaymentAmount,
    };
  }

  return { valid: true, totalPaymentAmount };
}

// Test scenarios
const testScenarios = [
  {
    name: "Valid 2-method split",
    totalAmount: 1000,
    payments: [
      { paymentMethod: PAYMENT_METHODS[0], amount: 600, amountTendered: 600 },
      { paymentMethod: PAYMENT_METHODS[1], amount: 400, amountTendered: 400 },
    ],
  },
  {
    name: "Valid 3-method split",
    totalAmount: 6000,
    payments: [
      { paymentMethod: PAYMENT_METHODS[0], amount: 2000, amountTendered: 2000 },
      { paymentMethod: PAYMENT_METHODS[1], amount: 1500, amountTendered: 1500 },
      { 
        paymentMethod: PAYMENT_METHODS[2], 
        amount: 2500, 
        additionalData: { transactionCode: "ABC123" }
      },
    ],
  },
  {
    name: "Invalid - amounts don't match total",
    totalAmount: 1000,
    payments: [
      { paymentMethod: PAYMENT_METHODS[0], amount: 600, amountTendered: 600 },
      { paymentMethod: PAYMENT_METHODS[1], amount: 300, amountTendered: 300 },
    ],
  },
  {
    name: "Invalid - no payments",
    totalAmount: 1000,
    payments: [],
  },
];

testScenarios.forEach((scenario, index) => {
  const result = validateSplitPayment(scenario.totalAmount, scenario.payments);
  console.log(`  Scenario ${index + 1} (${scenario.name}):`, 
    result.valid ? "✅ VALID" : `❌ INVALID - ${result.error}`);
});

// Test 2: Split Payment Processing Simulation
console.log("\n2. Testing Split Payment Processing:");

function simulatePaymentProcessing(payment) {
  const transactionId = `${payment.paymentMethod.type.toUpperCase()}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  
  // Simulate different payment method processing
  switch (payment.paymentMethod.type) {
    case "cash":
    case "credit":
      const change = (payment.amountTendered || payment.amount) - payment.amount;
      return {
        success: true,
        transactionId,
        method: payment.paymentMethod,
        amount: payment.amount,
        currency: "KES",
        timestamp: new Date().toISOString(),
        change: change > 0 ? change : 0,
      };
    
    case "absa_till":
      return {
        success: true,
        transactionId,
        method: payment.paymentMethod,
        amount: payment.amount,
        currency: "KES",
        timestamp: new Date().toISOString(),
        reference: `REF-${Date.now()}`,
      };
    
    default:
      return {
        success: false,
        transactionId: "",
        method: payment.paymentMethod,
        amount: payment.amount,
        currency: "KES",
        timestamp: new Date().toISOString(),
        error: "Unsupported payment method",
      };
  }
}

function processSplitPayment(totalAmount, payments) {
  const validation = validateSplitPayment(totalAmount, payments);
  if (!validation.valid) {
    return {
      success: false,
      error: validation.error,
      payments: [],
      totalChange: 0,
    };
  }

  const paymentResults = [];
  let totalChange = 0;
  let allSuccessful = true;

  for (const payment of payments) {
    const result = simulatePaymentProcessing(payment);
    paymentResults.push(result);
    
    if (result.success && result.change) {
      totalChange += result.change;
    } else if (!result.success) {
      allSuccessful = false;
      break;
    }
  }

  return {
    success: allSuccessful,
    payments: paymentResults,
    totalAmount,
    totalChange,
    combinedTransactionId: `SPLIT-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
    timestamp: new Date().toISOString(),
  };
}

// Test the successful 3-method split
const testSplit = {
  totalAmount: 6000,
  payments: [
    { paymentMethod: PAYMENT_METHODS[0], amount: 2000, amountTendered: 2500 }, // Cash with change
    { paymentMethod: PAYMENT_METHODS[1], amount: 1500, amountTendered: 1500 }, // Credit exact
    { 
      paymentMethod: PAYMENT_METHODS[2], 
      amount: 2500, 
      additionalData: { transactionCode: "ABC123XYZ" }
    }, // ABSA Till
  ],
};

const splitResult = processSplitPayment(testSplit.totalAmount, testSplit.payments);

console.log("✅ Split Payment Result:");
console.log(`  Success: ${splitResult.success}`);
console.log(`  Total Amount: KSh ${splitResult.totalAmount?.toLocaleString()}`);
console.log(`  Total Change: KSh ${splitResult.totalChange?.toLocaleString()}`);
console.log(`  Combined Transaction ID: ${splitResult.combinedTransactionId}`);
console.log(`  Individual Payments:`);

splitResult.payments?.forEach((payment, index) => {
  console.log(`    ${index + 1}. ${payment.method.name}: KSh ${payment.amount.toLocaleString()}${payment.change ? ` (Change: KSh ${payment.change.toLocaleString()})` : ""}`);
  console.log(`       Transaction ID: ${payment.transactionId}`);
});

console.log("\n🎉 Split Payment Tests Completed!");
console.log("\n📋 Summary:");
console.log("- Split payment validation working correctly");
console.log("- Multiple payment method processing functional");
console.log("- Change calculation across methods working");
console.log("- Transaction ID generation for each payment");
console.log("- Combined transaction ID for the entire split");
console.log("- Ready for UI integration");
