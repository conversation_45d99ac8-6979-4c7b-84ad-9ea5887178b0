import { Platform } from "react-native";
import { ReceiptData } from "../components/receipt/ReceiptGenerator";
import {
  StandardizedReceiptService,
  StandardizedReceiptData,
} from "./StandardizedReceiptService";

export interface WebPrintResult {
  success: boolean;
  error?: string;
  method?: "browser" | "thermal" | "pdf" | "fallback";
}

export interface WebPrinterConfig {
  type: "thermal" | "standard" | "network";
  endpoint?: string; // For network/API printing
  width?: number; // Receipt width in characters (default: 32)
  encoding?: string; // Character encoding (default: 'utf-8')
}

/**
 * Web-compatible printing service for receipt printing
 * SIMPLIFIED: Always uses browser print dialog - other methods disabled
 * Handles thermal and standard formatting styles for web browsers
 */
export class WebPrintService {
  private static config: WebPrinterConfig = {
    type: "standard", // Formatting style only - always uses browser print dialog
    width: 32, // Character width for formatting
    encoding: "utf-8", // Text encoding
  };

  /**
   * Create properly aligned two-column line for web printing
   * @param leftText - Text for left column
   * @param rightText - Text for right column
   * @param totalWidth - Total character width (default: 32 for 58mm thermal)
   * @returns Properly aligned string
   */
  private static createAlignedLine(
    leftText: string,
    rightText: string,
    totalWidth: number = 32
  ): string {
    // Ensure we don't exceed the total width
    const maxLeftWidth = totalWidth - rightText.length - 2; // Reserve 2 chars minimum spacing
    const truncatedLeft =
      leftText.length > maxLeftWidth
        ? leftText.substring(0, maxLeftWidth - 1) + "…"
        : leftText;

    // Calculate spacing needed
    const spacing = Math.max(
      1,
      totalWidth - truncatedLeft.length - rightText.length
    );

    return `${truncatedLeft}${" ".repeat(spacing)}${rightText}`;
  }

  /**
   * Initialize web print service with configuration
   */
  static async init(config?: Partial<WebPrinterConfig>): Promise<boolean> {
    try {
      if (Platform.OS !== "web") {
        return false; // Only for web platform
      }

      this.config = { ...this.config, ...config };

      // Check if browser supports printing
      if (typeof window !== "undefined" && typeof window.print === "function") {
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error initializing web print service:", error);
      return false;
    }
  }

  /**
   * Check if web printing is available
   */
  static isAvailable(): boolean {
    return (
      Platform.OS === "web" && typeof window !== "undefined" && !!window.print
    );
  }

  /**
   * Print receipt using web browser dialog (WEB DEFAULT - no other methods)
   */
  static async printReceipt(receiptData: ReceiptData): Promise<WebPrintResult> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: "Web printing not available",
      };
    }

    try {
      console.log(
        "🖨️ WebPrintService: Using browser print dialog (WEB DEFAULT - no thermal connections)"
      );

      // ALWAYS use browser print dialog - no thermal printer connections on web
      return await this.printStandardReceipt(receiptData);
    } catch (error) {
      console.error("Web print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Print failed",
      };
    }
  }

  /**
   * Print standardized receipt using the Universal Template system
   */
  static async printStandardizedReceipt(
    receiptData: StandardizedReceiptData
  ): Promise<WebPrintResult> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: "Web printing not available",
      };
    }

    try {
      console.log(
        "🖨️ WebPrintService: Printing with Universal Template system"
      );

      // Generate HTML using the Universal Template via StandardizedReceiptService
      const html = StandardizedReceiptService.generateHTMLReceipt(receiptData);

      console.log("✅ Universal Template HTML generated for web printing");

      // Print the Universal Template HTML
      return await this.printHTML(html, "browser");
    } catch (error) {
      console.error("❌ Universal Template web print error:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Universal Template print failed",
      };
    }
  }

  /**
   * Print via thermal printer API endpoint - DISABLED (browser print only)
   */
  private static async printViaThermalAPI(
    receiptData: ReceiptData
  ): Promise<WebPrintResult> {
    try {
      if (!this.config.endpoint) {
        throw new Error("No thermal printer endpoint configured");
      }

      const escPosCommands = this.generateESCPOSCommands(receiptData);

      const response = await fetch(this.config.endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          commands: escPosCommands,
          printerType: "thermal",
        }),
      });

      if (!response.ok) {
        throw new Error(`Thermal print API error: ${response.statusText}`);
      }

      return {
        success: true,
        method: "thermal",
      };
    } catch (error) {
      console.error("Thermal API print error:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Thermal API print failed",
      };
    }
  }

  /**
   * Print thermal-style receipt using browser print with Universal Template fallback
   */
  private static async printThermalStyleReceipt(
    receiptData: ReceiptData
  ): Promise<WebPrintResult> {
    try {
      console.log(
        "🔄 Attempting thermal-style print with Universal Template fallback..."
      );

      // Try to convert legacy ReceiptData to StandardizedReceiptData and use Universal Template
      try {
        const standardizedData = await this.convertLegacyToStandardized(
          receiptData
        );
        const universalHTML =
          StandardizedReceiptService.generateHTMLReceipt(standardizedData);
        console.log("✅ Using Universal Template for thermal-style print");
        return await this.printHTML(universalHTML, "thermal");
      } catch (universalError) {
        console.warn(
          "⚠️ Universal Template failed, falling back to legacy thermal HTML:",
          universalError
        );
        const thermalHTML = this.generateThermalHTML(receiptData);
        return await this.printHTML(thermalHTML, "thermal");
      }
    } catch (error) {
      console.error("❌ Thermal style print error:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Thermal style print failed",
      };
    }
  }

  /**
   * Print standard receipt using browser print with Universal Template fallback
   */
  private static async printStandardReceipt(
    receiptData: ReceiptData
  ): Promise<WebPrintResult> {
    try {
      console.log(
        "🔄 Attempting standard print with Universal Template fallback..."
      );

      // Try to convert legacy ReceiptData to StandardizedReceiptData and use Universal Template
      try {
        const standardizedData = await this.convertLegacyToStandardized(
          receiptData
        );
        const universalHTML =
          StandardizedReceiptService.generateHTMLReceipt(standardizedData);
        console.log("✅ Using Universal Template for standard print");
        return await this.printHTML(universalHTML, "browser");
      } catch (universalError) {
        console.warn(
          "⚠️ Universal Template failed, falling back to legacy standard HTML:",
          universalError
        );
        const standardHTML = this.generateStandardHTML(receiptData);
        return await this.printHTML(standardHTML, "browser");
      }
    } catch (error) {
      console.error("❌ Standard print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Standard print failed",
      };
    }
  }

  /**
   * Extract shipping fee from receipt data
   */
  private static extractShippingFeeFromReceiptData(
    receiptData: ReceiptData
  ): number {
    // Check if order data has shipping information
    const orderData = (receiptData as any).orderData || receiptData;

    // Priority 1: Check shipping_lines array (Shopify format)
    if (orderData.shipping_lines && Array.isArray(orderData.shipping_lines)) {
      const totalShipping = orderData.shipping_lines.reduce(
        (total: number, line: any) => {
          return total + parseFloat(line.price || "0");
        },
        0
      );
      if (totalShipping > 0) {
        console.log(
          "✅ WebPrintService: Found shipping fee in shipping_lines:",
          totalShipping
        );
        return totalShipping;
      }
    }

    // Priority 2: Check shippingData (from checkout process)
    if (
      orderData.shippingData?.shippingFee &&
      orderData.shippingData.shippingFee > 0
    ) {
      console.log(
        "✅ WebPrintService: Found shipping fee in shippingData:",
        orderData.shippingData.shippingFee
      );
      return orderData.shippingData.shippingFee;
    }

    // Priority 3: Check line items for shipping items
    const lineItems = orderData.lineItems || orderData.line_items || [];
    const shippingLineItem = lineItems.find(
      (item: any) =>
        item.title?.toLowerCase().includes("shipping") ||
        item.title?.toLowerCase().includes("delivery")
    );
    if (shippingLineItem) {
      const shippingFee = parseFloat(shippingLineItem.price || "0");
      console.log(
        "✅ WebPrintService: Found shipping fee in line items:",
        shippingFee
      );
      return shippingFee;
    }

    console.log("ℹ️ WebPrintService: No shipping fee found in receipt data");
    return 0;
  }

  /**
   * Convert legacy ReceiptData to StandardizedReceiptData format
   */
  private static async convertLegacyToStandardized(
    receiptData: ReceiptData
  ): Promise<StandardizedReceiptData> {
    console.log("🔄 Converting legacy receipt data to standardized format...");

    // Convert legacy format to standardized format
    const standardizedData: StandardizedReceiptData = {
      receiptNumber: receiptData.orderNumber,
      date: new Date(receiptData.orderDate).toLocaleDateString("en-GB"),
      time: new Date(receiptData.orderDate).toLocaleTimeString("en-GB", {
        hour12: false,
      }),

      store: {
        name: receiptData.store.name,
        address: receiptData.store.address || "Store Address",
        mobile: "+254 111 443 993",
        // mobile: receiptData.store.phone ||"+254 111 443 993",
        email: "<EMAIL>",
        website: "www.treasuredscents.co.ke",
      },

      staff: {
        name: receiptData.staff.name,
        role: receiptData.staff.role,
      },

      customer: {
        name: receiptData.customer.name,
        mobile: receiptData.customer.phone,
        email: receiptData.customer.email,
      },

      items: receiptData.items.map((item, index) => ({
        number: index + 1,
        name: item.title,
        quantity: item.quantity,
        unitPrice: parseFloat(item.price),
        totalPrice: item.quantity * parseFloat(item.price),
        sku: item.sku,
        variant: item.variantTitle,
      })),

      totals: {
        itemCount: receiptData.items.length,
        subtotal: receiptData.subtotal,
        shippingCharges: this.extractShippingFeeFromReceiptData(receiptData), // ✅ FIXED: Extract shipping fee
        tax: receiptData.tax,
        grandTotal: receiptData.total,
        grandTotalInWords: "", // Could be generated if needed
      },

      payment: {
        isSplitPayment: receiptData.paymentBreakdown?.isSplitPayment || false,
        methods: receiptData.paymentBreakdown?.paymentMethods?.map(
          (method: any) => ({
            method: method.name || method.type,
            amount: method.amount,
            transactionCode: method.metadata?.transactionCode,
            phoneNumber: method.metadata?.phoneNumber,
          })
        ) || [
          {
            method: receiptData.paymentMethod,
            amount: receiptData.total,
          },
        ],
        totalPaid: receiptData.total,
      },

      salesAgent: receiptData.salesAgent
        ? {
            name: receiptData.salesAgent.name,
            id: receiptData.salesAgent.id || "unknown",
          }
        : undefined,

      // CRITICAL FIX: Include loyalty data from backend
      loyalty: receiptData.loyaltyPoints
        ? {
            totalPoints: receiptData.loyaltyPoints.balance,
            tier: "bronze", // Default tier, could be enhanced
            pointsEarned: receiptData.loyaltyPoints.earned || 0,
            previousPoints:
              receiptData.loyaltyPoints.balance -
              (receiptData.loyaltyPoints.earned || 0), // Calculate previous points
          }
        : undefined,
    };

    console.log("✅ Legacy receipt data converted to standardized format");
    console.log("🔍 Loyalty data conversion:", {
      hasLoyaltyPoints: !!receiptData.loyaltyPoints,
      loyaltyBalance: receiptData.loyaltyPoints?.balance,
      convertedLoyalty: standardizedData.loyalty,
    });
    return standardizedData;
  }

  /**
   * Print HTML content using browser print dialog (improved to avoid popup blocking)
   */
  private static async printHTML(
    html: string,
    method: "thermal" | "browser"
  ): Promise<WebPrintResult> {
    return new Promise((resolve) => {
      try {
        // Create a hidden iframe for printing
        const printFrame = document.createElement("iframe");
        printFrame.style.position = "absolute";
        printFrame.style.top = "-9999px";
        printFrame.style.left = "-9999px";
        printFrame.style.width = "0px";
        printFrame.style.height = "0px";
        printFrame.style.border = "none";

        document.body.appendChild(printFrame);

        const printDocument =
          printFrame.contentDocument || printFrame.contentWindow?.document;
        if (!printDocument) {
          throw new Error("Could not access print document");
        }

        printDocument.open();
        printDocument.write(html);
        printDocument.close();

        // Wait for content to load then print
        printFrame.onload = () => {
          try {
            // Use a small delay to ensure content is fully loaded
            setTimeout(() => {
              try {
                printFrame.contentWindow?.focus();
                printFrame.contentWindow?.print();

                // Clean up after print dialog
                setTimeout(() => {
                  if (document.body.contains(printFrame)) {
                    document.body.removeChild(printFrame);
                  }
                }, 1000);

                resolve({
                  success: true,
                  method: method,
                });
              } catch (printError) {
                if (document.body.contains(printFrame)) {
                  document.body.removeChild(printFrame);
                }
                resolve({
                  success: false,
                  error:
                    printError instanceof Error
                      ? printError.message
                      : "Print dialog failed",
                });
              }
            }, 100); // Small delay to ensure iframe is ready
          } catch (error) {
            if (document.body.contains(printFrame)) {
              document.body.removeChild(printFrame);
            }
            resolve({
              success: false,
              error:
                error instanceof Error ? error.message : "Print setup failed",
            });
          }
        };

        // Fallback timeout
        setTimeout(() => {
          if (document.body.contains(printFrame)) {
            document.body.removeChild(printFrame);
            resolve({
              success: false,
              error: "Print timeout",
            });
          }
        }, 5000);
      } catch (error) {
        resolve({
          success: false,
          error: error instanceof Error ? error.message : "Print setup failed",
        });
      }
    });
  }

  /**
   * Generate thermal printer HTML (80mm width)
   */
  private static generateThermalHTML(receiptData: ReceiptData): string {
    console.log(
      "WebPrintService: Generating thermal HTML with Universal Template styling"
    );

    // Try to use Universal Template first
    try {
      const { UniversalReceiptStyler } = require("./UniversalReceiptStyler");
      const universalCSS = UniversalReceiptStyler.generateCSS({
        width: "thermal-80mm",
        deviceType: "web",
      });

      const {
        store,
        orderNumber,
        orderDate,
        items,
        subtotal,
        tax,
        total,
        customer,
        salesAgent,
      } = receiptData;

      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Receipt ${orderNumber} - ${Date.now()}</title>
    <style>
        ${universalCSS}
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Store Header -->
        <div class="store-header">
            <div class="store-name">${store?.name || "TREASURED SCENTS"}</div>
            ${
              store?.address
                ? `<div class="store-address">${store.address}</div>`
                : ""
            }
            ${
              store?.phone
                ? `<div class="store-contact">Mobile: ${store.phone}</div>`
                : ""
            }
            <div class="store-contact">Email: <EMAIL></div>
            <div class="store-contact">Website: www.treasuredscents.co.ke</div>
        </div>

        <div class="separator"></div>

        <div class="receipt-title">SALES RECEIPT</div>

        <!-- Receipt Details -->
        <table class="receipt-table">
            <tr>
                <td class="label-col">Receipt No:</td>
                <td class="value-col">${orderNumber || "N/A"}</td>
            </tr>
            <tr>
                <td class="label-col">Date:</td>
                <td class="value-col">${new Date(
                  orderDate || new Date()
                ).toLocaleDateString()} ${new Date(
        orderDate || new Date()
      ).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}</td>
            </tr>
            <tr>
                <td class="label-col">Served by:</td>
                <td class="value-col">${
                  receiptData.staff?.name || "POS Staff"
                }</td>
            </tr>
            ${
              customer?.name
                ? `
            <tr>
                <td class="label-col">Customer:</td>
                <td class="value-col">${customer.name}</td>
            </tr>`
                : ""
            }
            ${
              customer?.phone
                ? `
            <tr>
                <td class="label-col">Mobile:</td>
                <td class="value-col">${customer.phone}</td>
            </tr>`
                : ""
            }
            ${
              salesAgent?.name
                ? `
            <tr>
                <td class="label-col">Sales Agent:</td>
                <td class="value-col">${salesAgent.name}</td>
            </tr>`
                : ""
            }
        </table>

        <div class="separator"></div>

        <!-- Items -->
        ${(items || [])
          .map(
            (item) => `
        <div class="item">
            <div class="item-name">${item.title || "Item"}</div>
            ${
              item.variantTitle
                ? `<div class="item-variant">Variant: ${item.variantTitle}</div>`
                : ""
            }
            ${item.sku ? `<div class="item-sku">SKU: ${item.sku}</div>` : ""}
            <table class="two-column">
                <tr>
                    <td class="left-col">${
                      item.quantity || 1
                    } x KSh ${parseFloat(
              (item.price || "0").toString()
            ).toFixed(2)}</td>
                    <td class="right-col">KSh ${(
                      parseFloat((item.price || "0").toString()) *
                      (item.quantity || 1)
                    ).toFixed(2)}</td>
                </tr>
            </table>
        </div>
        `
          )
          .join("")}

        <div class="separator"></div>

        <!-- Total -->
        <table class="two-column total-section">
            <tr>
                <td class="left-col bold">TOTAL</td>
                <td class="right-col bold">KSh ${(total || 0).toFixed(2)}</td>
            </tr>
        </table>

        <!-- Payment -->
        <table class="receipt-table">
            <tr>
                <td class="label-col">Payment:</td>
                <td class="value-col">${
                  receiptData.paymentMethod || "Cash"
                }</td>
            </tr>
            ${
              receiptData.paymentDetails?.transactionId
                ? `
            <tr>
                <td class="label-col">Transaction:</td>
                <td class="value-col">${receiptData.paymentDetails.transactionId}</td>
            </tr>`
                : ""
            }
        </table>

        <div class="separator"></div>

        <!-- Loyalty Section -->
        ${
          receiptData.loyaltyPoints
            ? `
        <div class="loyalty-section">
            <div class="loyalty-title">🌟 LOYALTY REWARDS 🌟</div>
            <table class="receipt-table">
                <tr>
                    <td class="label-col">Total TS Points:</td>
                    <td class="value-col">${
                      receiptData.loyaltyPoints.balance
                    }</td>
                </tr>
                <tr>
                    <td class="label-col">Member ID:</td>
                    <td class="value-col">TS${
                      receiptData.customer?.phone?.slice(-8) || "GUEST"
                    }</td>
                </tr>
            </table>
        </div>
        `
            : ""
        }

        <!-- Footer -->
        <div class="footer">
            <div>You're treasured! Thank you</div>
            <div>Powered by Dukalink POS</div>
        </div>

        <div class="paper-feed"></div>
    </div>
</body>
</html>`;
    } catch (error) {
      console.error(
        "Failed to use Universal Template, falling back to legacy CSS:",
        error
      );
      // Fallback to original implementation
      const {
        store,
        orderNumber,
        orderDate,
        items,
        subtotal,
        tax,
        total,
        customer,
        salesAgent,
      } = receiptData;

      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Receipt ${orderNumber} - ${Date.now()}</title>
    <style>
        /* Thermal Printer Optimized CSS for Browser Print Dialog - Updated v2.0 with proper alignment */
        @page {
            size: 80mm auto;
            margin: 0mm;
            padding: 0mm;
        }

        * {
            box-sizing: border-box;
        }

        html {
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Courier New', 'Lucida Console', monospace !important;
            font-size: 14px !important;
            font-weight: bold !important;
            line-height: 1.2 !important;
            margin: 0 !important;
            padding: 3mm 2mm !important;
            width: 80mm !important;
            max-width: 80mm !important;
            color: #000 !important;
            background: #fff !important;
            text-align: center !important;
            overflow: hidden;
        }
        .center {
            text-align: center !important;
            width: 100% !important;
            display: block !important;
        }

        .bold {
            font-weight: bold !important;
        }

        .line {
            border-top: 1px dashed #000 !important;
            margin: 3mm 0 !important;
            width: 100% !important;
            height: 0 !important;
        }

        .item {
            margin: 1mm 0 !important;
            font-size: 11px !important;
            width: 100% !important;
        }

        .item-line {
            display: flex !important;
            justify-content: space-between !important;
            width: 100% !important;
            font-size: 11px !important;
            font-family: 'Courier New', monospace !important;
            margin-top: 2px !important;
        }

        .item-name {
            text-align: left !important;
            font-weight: bold !important;
            margin-bottom: 2px !important;
        }

        .item-details {
            text-align: left !important;
            font-size: 10px !important;
            margin-left: 4px !important;
            margin-bottom: 1px !important;
        }

        .store-info {
            font-size: 12px !important;
            line-height: 1.3 !important;
            margin-bottom: 3mm !important;
            text-align: center !important;
        }

        .receipt-header {
            font-size: 13px !important;
            font-weight: bold !important;
            margin: 3mm 0 !important;
            text-align: center !important;
        }

        .receipt-details {
            font-size: 11px !important;
            margin: 2mm 0 !important;
            text-align: left !important;
        }

        .total-section {
            margin-top: 3mm !important;
            font-weight: bold !important;
            font-size: 12px !important;
            border-top: 2px solid #000 !important;
            padding-top: 2mm !important;
        }

        .total-line {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            font-weight: bold !important;
            font-size: 14px !important;
            margin: 2mm 0 !important;
        }

        .payment-section {
            margin-top: 3mm !important;
            text-align: left !important;
        }

        .footer-section {
            margin-top: 5mm !important;
            font-size: 11px !important;
            text-align: center !important;
        }

        .loyalty-section {
            margin: 3mm 0 !important;
            padding: 2mm !important;
            border: 1px dashed #007bff !important;
            text-align: center !important;
            background-color: #f8f9fa !important;
        }

        .loyalty-title {
            font-weight: bold !important;
            font-size: 12px !important;
            color: #007bff !important;
            margin-bottom: 1mm !important;
        }

        .loyalty-details {
            font-size: 10px !important;
            line-height: 1.3 !important;
        }

        .paper-feed {
            height: 20mm !important;
            width: 100% !important;
            page-break-after: always !important;
        }

        .no-print { display: none; }
        /* Print-specific optimizations */
        @media print {
            html, body {
                margin: 0 !important;
                padding: 0 !important;
                width: 80mm !important;
                max-width: 80mm !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                padding: 3mm 2mm !important;
                text-align: center !important;
            }

            .center {
                text-align: center !important;
                margin: 0 auto !important;
            }

            .receipt-details {
                text-align: left !important;
            }

            .line {
                border-color: #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .total-section {
                border-color: #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .no-print { display: none !important; }

            .paper-feed {
                height: 20mm !important;
                page-break-after: always !important;
            }

            /* Force thermal printer layout */
            @page {
                size: 80mm auto !important;
                margin: 0mm !important;
            }
        }

        /* Thermal printer specific adjustments */
        @media print and (max-width: 80mm) {
            body {
                font-size: 11px !important;
                padding: 2mm 1mm !important;
            }
        }
    </style>
</head>
<body>
    <div class="store-info">
        <div class="center bold">${store?.name || "TREASURED SCENTS"}</div>
        ${store?.address ? `<div class="center">${store.address}</div>` : ""}
        ${
          store?.phone ? `<div class="center">Mobile: ${store.phone}</div>` : ""
        }
        <div class="center">Email: <EMAIL></div>
        <div class="center">Website: www.treasuredscents.co.ke</div>
    </div>

    <div class="line"></div>

    <div class="receipt-header center">SALES RECEIPT</div>

    <table style="width: 100%; border-collapse: collapse; margin-bottom: 8px; text-align: left;">
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Receipt No:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${
              orderNumber || "N/A"
            }</td>
        </tr>
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Date:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${new Date(
              orderDate || new Date()
            ).toLocaleDateString()} ${new Date(
        orderDate || new Date()
      ).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}</td>
        </tr>
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Served by:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${
              receiptData.staff?.name || "POS Staff"
            }</td>
        </tr>
        ${
          customer?.name
            ? `
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Customer:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${customer.name}</td>
        </tr>`
            : ""
        }
        ${
          customer?.phone
            ? `
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Mobile:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${customer.phone}</td>
        </tr>`
            : ""
        }
        ${
          salesAgent?.name
            ? `
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Sales Agent:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${salesAgent.name}</td>
        </tr>`
            : ""
        }
    </table>
    
    <div class="line"></div>
    
    ${(items || [])
      .map(
        (item) => `
    <div class="item">
        <div class="item-name">${item.title || "Item"}</div>
        ${
          item.variantTitle
            ? `<div class="item-details">Variant: ${item.variantTitle}</div>`
            : ""
        }
        ${item.sku ? `<div class="item-details">SKU: ${item.sku}</div>` : ""}
        <div class="item-line">
            <span>${item.quantity || 1} x KSh ${parseFloat(
          (item.price || "0").toString()
        ).toFixed(2)}</span>
            <span>KSh ${(
              parseFloat((item.price || "0").toString()) * (item.quantity || 1)
            ).toFixed(2)}</span>
        </div>
    </div>
    `
      )
      .join("")}
    
    <div class="line"></div>

    <div class="total-section">
        <div class="total-line">
            <span>TOTAL</span>
            <span>KSh ${(total || 0).toFixed(2)}</span>
        </div>
    </div>

    <table style="width: 100%; border-collapse: collapse; margin-bottom: 8px; text-align: left;">
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Payment:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${
              receiptData.paymentMethod || "Cash"
            }</td>
        </tr>
        ${
          receiptData.paymentDetails?.transactionId
            ? `
        <tr>
            <td style="text-align: left; width: 65%; padding-right: 8px;">Transaction:</td>
            <td style="text-align: right; width: 35%; font-weight: bold;">${receiptData.paymentDetails.transactionId}</td>
        </tr>`
            : ""
        }
    </table>

    <div class="line"></div>

    ${
      receiptData.loyaltyPoints
        ? `
    <div class="loyalty-section">
        <div class="loyalty-title">🌟 LOYALTY REWARDS 🌟</div>
        <div class="loyalty-details">
            <div>Total TS Points: <strong>${
              receiptData.loyaltyPoints.balance
            }</strong></div>
            <div>Member ID: TS${
              receiptData.customer?.phone?.slice(-8) || "GUEST"
            }</div>
        </div>
    </div>
    `
        : ""
    }

    <div class="footer-section">
        <div>You're treasured! Thank you</div>
        <div>Powered by Dukalink POS</div>
    </div>

    <div class="paper-feed"></div>
</body>
</html>`;
    }
  }

  /**
   * Generate standard printer HTML
   */
  private static generateStandardHTML(receiptData: ReceiptData): string {
    // Similar to thermal but with standard page formatting
    // Implementation would be similar but with different CSS for A4/Letter size
    return this.generateThermalHTML(receiptData)
      .replace("size: 80mm auto;", "size: A4; margin: 20mm;")
      .replace("width: 76mm;", "width: auto; max-width: 400px;")
      .replace("max-width: 76mm;", "max-width: 400px;");
  }

  /**
   * Generate ESC/POS commands for thermal printer API
   */
  private static generateESCPOSCommands(receiptData: ReceiptData): string[] {
    const commands: string[] = [];

    // Initialize printer
    commands.push("\x1B\x40"); // ESC @

    // Center align
    commands.push("\x1B\x61\x01"); // ESC a 1

    // Store name (bold)
    commands.push("\x1B\x45\x01"); // ESC E 1 (bold on)
    commands.push(`${receiptData.store?.name || "Store Name"}\n`);
    commands.push("\x1B\x45\x00"); // ESC E 0 (bold off)

    // Store details
    if (receiptData.store?.address) {
      commands.push(`${receiptData.store.address}\n`);
    }
    if (receiptData.store?.phone) {
      // commands.push(`Mobile: ${receiptData.store.phone}\n`);
      commands.push(`Mobile: +254 111 443 993\n`);
    }
    commands.push(`Email: <EMAIL>\n`);
    commands.push(`Website: www.treasuredscents.co.ke\n`);

    // Separator
    commands.push("------------------------\n");

    // Receipt details
    commands.push("\x1B\x45\x01"); // Bold on
    commands.push("SALES RECEIPT\n");
    commands.push("\x1B\x45\x00"); // Bold off

    commands.push(`Receipt No: ${receiptData.orderNumber}\n`);
    commands.push(
      `Date: ${new Date(receiptData.orderDate).toLocaleDateString()} ${new Date(
        receiptData.orderDate
      ).toLocaleTimeString()}\n`
    );
    commands.push(`Served by: ${receiptData.staff?.name || "POS Staff"}\n`);

    // Customer and sales agent
    if (receiptData.customer?.name) {
      commands.push(`Customer: ${receiptData.customer.name}\n`);
    }
    if (receiptData.customer?.phone) {
      commands.push(`Mobile: ${receiptData.customer.phone}\n`);
    }
    if (receiptData.salesAgent?.name) {
      commands.push(`Sales Agent: ${receiptData.salesAgent.name}\n`);
    }

    commands.push("------------------------\n");

    // Left align for items
    commands.push("\x1B\x61\x00"); // ESC a 0

    // Items
    (receiptData.items || []).forEach((item) => {
      commands.push(`${item.title || "Item"}\n`);
      if (item.variantTitle) {
        commands.push(`  Variant: ${item.variantTitle}\n`);
      }
      if (item.sku) {
        commands.push(`  SKU: ${item.sku}\n`);
      }

      // Create aligned price line
      const qtyPrice = `${item.quantity || 1} x KSh ${parseFloat(
        (item.price || "0").toString()
      ).toFixed(2)}`;
      const total = `KSh ${(
        parseFloat((item.price || "0").toString()) * (item.quantity || 1)
      ).toFixed(2)}`;

      // Use the new alignment function for better formatting
      const alignedLine = this.createAlignedLine(qtyPrice, total, 32);
      commands.push(alignedLine + "\n");
    });

    commands.push("------------------------\n");

    // Subtotal
    const subtotalLine = this.createAlignedLine(
      "Subtotal:",
      `KSh ${(receiptData.total || 0).toFixed(2)}`,
      32
    );
    commands.push(subtotalLine + "\n");

    // Delivery fee (default standard delivery)
    const shippingFee = 200.0; // Default standard delivery fee
    const shippingLine = this.createAlignedLine(
      "Delivery Fee:",
      `KSh ${shippingFee.toFixed(2)}`,
      32
    );
    commands.push(shippingLine + "\n");

    commands.push("------------------------\n");

    // Total (aligned)
    commands.push("\x1B\x45\x01"); // Bold on
    const totalLabel = "TOTAL:";
    const totalAmount = `KSh ${((receiptData.total || 0) + shippingFee).toFixed(
      2
    )}`;

    // Use the new alignment function for better total formatting
    const totalLine = this.createAlignedLine(totalLabel, totalAmount, 32);
    commands.push(totalLine + "\n");
    commands.push("\x1B\x45\x00"); // Bold off

    // Loyalty points section (if available)
    if (receiptData.loyaltyPoints) {
      commands.push("\n");
      commands.push("\x1B\x61\x01"); // Center align
      commands.push("*** LOYALTY REWARDS ***\n");
      commands.push(`Total TS Points: ${receiptData.loyaltyPoints.balance}\n`);
      commands.push(
        `Member ID: TS${receiptData.customer?.phone?.slice(-8) || "GUEST"}\n`
      );
    }

    // Center align for footer
    commands.push("\x1B\x61\x01"); // ESC a 1
    commands.push("You're treasured! Thank you\n");
    commands.push("Powered by Dukalink POS\n");

    // Feed paper
    commands.push("\n\n\n");

    return commands;
  }

  /**
   * Configure printer settings
   */
  static configure(config: Partial<WebPrinterConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  static getConfig(): WebPrinterConfig {
    return { ...this.config };
  }
}
