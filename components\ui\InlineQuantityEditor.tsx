/**
 * InlineQuantityEditor Component
 * 
 * Provides inline quantity editing with increment/decrement buttons,
 * direct input editing, and real-time inventory validation.
 */

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ViewStyle,
} from 'react-native';
import { IconSymbol } from './IconSymbol';
import { useTheme } from '@/src/contexts/ThemeContext';
import { createStyleUtils } from '@/src/utils/styleUtils';

interface InlineQuantityEditorProps {
  value: number;
  max: number;
  min?: number;
  onQuantityChange: (quantity: number) => void;
  disabled?: boolean;
  style?: ViewStyle;
  showStock?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export function InlineQuantityEditor({
  value,
  max,
  min = 1,
  onQuantityChange,
  disabled = false,
  style,
  showStock = true,
  size = 'medium',
}: InlineQuantityEditorProps) {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(value.toString());
  const inputRef = useRef<TextInput>(null);

  const styles = createStyles(theme, size);

  const canDecrement = value > min && !disabled;
  const canIncrement = value < max && !disabled;
  const isLowStock = max <= 5 && max > 0;
  const isOutOfStock = max <= 0;

  const handleDecrement = () => {
    if (canDecrement) {
      const newValue = Math.max(min, value - 1);
      onQuantityChange(newValue);
    }
  };

  const handleIncrement = () => {
    if (canIncrement) {
      const newValue = Math.min(max, value + 1);
      onQuantityChange(newValue);
    }
  };

  const handleInputFocus = () => {
    setIsEditing(true);
    setInputValue(value.toString());
  };

  const handleInputBlur = () => {
    setIsEditing(false);
    handleInputSubmit();
  };

  const handleInputSubmit = () => {
    const numValue = parseInt(inputValue, 10);
    
    if (isNaN(numValue) || numValue < min) {
      setInputValue(value.toString());
      Alert.alert('Invalid Quantity', `Minimum quantity is ${min}`);
      return;
    }

    if (numValue > max) {
      setInputValue(value.toString());
      Alert.alert(
        'Insufficient Stock',
        `Only ${max} units available in stock.`
      );
      return;
    }

    onQuantityChange(numValue);
    setInputValue(numValue.toString());
  };

  const handleInputChange = (text: string) => {
    // Only allow numeric input
    const numericText = text.replace(/[^0-9]/g, '');
    setInputValue(numericText);
  };

  const getStockStatusColor = () => {
    if (isOutOfStock) return theme.colors.error;
    if (isLowStock) return theme.colors.warning;
    return theme.colors.success;
  };

  const getStockStatusText = () => {
    if (isOutOfStock) return 'Out of Stock';
    if (isLowStock) return `${max} left`;
    return `${max} in stock`;
  };

  return (
    <View style={[styles.container, style]}>
      {/* Quantity Controls */}
      <View style={styles.quantityContainer}>
        {/* Decrement Button */}
        <TouchableOpacity
          style={[
            styles.quantityButton,
            styles.decrementButton,
            {
              borderColor: theme.colors.border,
              backgroundColor: canDecrement 
                ? theme.colors.background 
                : theme.colors.backgroundSecondary,
              opacity: canDecrement ? 1 : 0.5,
            },
          ]}
          onPress={handleDecrement}
          disabled={!canDecrement}
        >
          <IconSymbol
            name="minus"
            size={size === 'small' ? 12 : size === 'large' ? 20 : 16}
            color={canDecrement ? theme.colors.text : theme.colors.textSecondary}
          />
        </TouchableOpacity>

        {/* Quantity Input */}
        <View style={styles.inputContainer}>
          <TextInput
            ref={inputRef}
            style={[
              styles.quantityInput,
              {
                color: theme.colors.text,
                borderColor: isEditing ? theme.colors.primary : theme.colors.border,
                backgroundColor: theme.colors.background,
              },
            ]}
            value={isEditing ? inputValue : value.toString()}
            onChangeText={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onSubmitEditing={handleInputSubmit}
            keyboardType="numeric"
            selectTextOnFocus
            maxLength={3}
            editable={!disabled}
          />
        </View>

        {/* Increment Button */}
        <TouchableOpacity
          style={[
            styles.quantityButton,
            styles.incrementButton,
            {
              borderColor: theme.colors.border,
              backgroundColor: canIncrement 
                ? theme.colors.background 
                : theme.colors.backgroundSecondary,
              opacity: canIncrement ? 1 : 0.5,
            },
          ]}
          onPress={handleIncrement}
          disabled={!canIncrement}
        >
          <IconSymbol
            name="plus"
            size={size === 'small' ? 12 : size === 'large' ? 20 : 16}
            color={canIncrement ? theme.colors.text : theme.colors.textSecondary}
          />
        </TouchableOpacity>
      </View>

      {/* Stock Status */}
      {showStock && (
        <Text
          style={[
            styles.stockStatus,
            {
              color: getStockStatusColor(),
              fontSize: size === 'small' ? 10 : size === 'large' ? 14 : 12,
            },
          ]}
        >
          {getStockStatusText()}
        </Text>
      )}
    </View>
  );
}

const createStyles = (theme: any, size: 'small' | 'medium' | 'large') => {
  const buttonSize = size === 'small' ? 28 : size === 'large' ? 40 : 32;
  const inputWidth = size === 'small' ? 40 : size === 'large' ? 60 : 50;
  const fontSize = size === 'small' ? 12 : size === 'large' ? 16 : 14;

  return StyleSheet.create({
    container: {
      alignItems: 'center',
    },
    quantityContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: theme.borderRadius.medium,
      backgroundColor: theme.colors.backgroundSecondary,
      padding: 2,
    },
    quantityButton: {
      width: buttonSize,
      height: buttonSize,
      borderRadius: theme.borderRadius.small,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
    },
    decrementButton: {
      marginRight: 2,
    },
    incrementButton: {
      marginLeft: 2,
    },
    inputContainer: {
      marginHorizontal: 2,
    },
    quantityInput: {
      width: inputWidth,
      height: buttonSize,
      textAlign: 'center',
      fontSize: fontSize,
      fontWeight: '600',
      borderWidth: 1,
      borderRadius: theme.borderRadius.small,
      paddingHorizontal: 4,
    },
    stockStatus: {
      marginTop: 4,
      fontWeight: '500',
      textAlign: 'center',
    },
  });
};
