import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getAPIClient } from "../../services/api/dukalink-client";
import {
  Customer,
  CustomerLoyaltyData,
  LoyaltyTransaction,
} from "../../types/shopify";
import { loyaltyService } from "../../services/loyalty-service";

interface CustomerState {
  customers: Customer[];
  selectedCustomer: Customer | null;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  // Loyalty-related state
  loyaltyData: Record<string, CustomerLoyaltyData>;
  loyaltyTransactions: Record<string, LoyaltyTransaction[]>;
  loyaltyLoading: Record<string, boolean>;
  loyaltyErrors: Record<string, string | null>;
}

const initialState: CustomerState = {
  customers: [],
  selectedCustomer: null,
  isLoading: false,
  error: null,
  searchQuery: "",
  // Loyalty-related initial state
  loyaltyData: {},
  loyaltyTransactions: {},
  loyaltyLoading: {},
  loyaltyErrors: {},
};

// Async thunks
export const fetchCustomers = createAsyncThunk(
  "customers/fetchCustomers",
  async (
    {
      limit = 50,
      search = "",
      includeLoyalty = false,
    }: {
      limit?: number;
      search?: string;
      includeLoyalty?: boolean;
    } = {},
    { rejectWithValue }
  ) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomers({
        limit,
        search,
        includeLoyalty,
      });

      if (!response.success) {
        return rejectWithValue(response.error || "Failed to fetch customers");
      }

      return {
        customers: response.data?.customers || [],
        pagination: response.data?.pagination ||
          response.meta?.pagination || {
            page: 1,
            limit,
            hasNext: false,
            hasPrev: false,
            total: 0,
          },
        isSearch: !!search && search.trim().length > 0,
      };
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

export const createCustomer = createAsyncThunk(
  "customers/createCustomer",
  async (
    customerData: {
      firstName: string;
      lastName: string;
      email?: string;
      phone?: string;
      note?: string;
      tags?: string;
      addresses?: any[];
    },
    { rejectWithValue }
  ) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.createStoreCustomer(customerData);

      if (!response.success) {
        return rejectWithValue(response.error || "Failed to create customer");
      }

      return response.data?.customer;
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

// Loyalty-related async thunks
export const fetchCustomerLoyalty = createAsyncThunk(
  "customers/fetchCustomerLoyalty",
  async (customerId: string, { rejectWithValue }) => {
    try {
      const loyaltyData = await loyaltyService.getCustomerLoyaltySummary(
        customerId
      );

      if (!loyaltyData) {
        return rejectWithValue("Failed to fetch customer loyalty data");
      }

      return { customerId, loyaltyData };
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

export const fetchCustomerLoyaltyTransactions = createAsyncThunk(
  "customers/fetchCustomerLoyaltyTransactions",
  async (
    {
      customerId,
      options,
    }: {
      customerId: string;
      options?: {
        limit?: number;
        offset?: number;
        type?: "earned" | "redeemed" | "expired" | "adjusted";
      };
    },
    { rejectWithValue }
  ) => {
    try {
      const result = await loyaltyService.getCustomerLoyaltyTransactions(
        customerId,
        options
      );

      if (!result) {
        return rejectWithValue("Failed to fetch customer loyalty transactions");
      }

      return {
        customerId,
        transactions: result.transactions,
        count: result.count,
      };
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

export const refreshCustomerLoyalty = createAsyncThunk(
  "customers/refreshCustomerLoyalty",
  async (customerId: string, { dispatch }) => {
    // Refresh both loyalty data and transactions
    await Promise.all([
      dispatch(fetchCustomerLoyalty(customerId)),
      dispatch(fetchCustomerLoyaltyTransactions({ customerId })),
    ]);

    return customerId;
  }
);

const customerSlice = createSlice({
  name: "customers",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedCustomer: (state, action: PayloadAction<Customer | null>) => {
      state.selectedCustomer = action.payload;
    },
    clearSelectedCustomer: (state) => {
      state.selectedCustomer = null;
    },
    clearCustomers: (state) => {
      state.customers = [];
    },
    // Loyalty-related reducers
    clearLoyaltyError: (state, action: PayloadAction<string>) => {
      state.loyaltyErrors[action.payload] = null;
    },
    clearCustomerLoyalty: (state, action: PayloadAction<string>) => {
      const customerId = action.payload;
      delete state.loyaltyData[customerId];
      delete state.loyaltyTransactions[customerId];
      delete state.loyaltyLoading[customerId];
      delete state.loyaltyErrors[customerId];
    },
    updateCustomerLoyaltyPoints: (
      state,
      action: PayloadAction<{
        customerId: string;
        newPoints: number;
        newTier?: string;
      }>
    ) => {
      const { customerId, newPoints, newTier } = action.payload;
      if (state.loyaltyData[customerId]) {
        state.loyaltyData[customerId].loyaltyPoints = newPoints;
        if (newTier) {
          state.loyaltyData[customerId].tier = newTier as any;
        }
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch Customers
    builder
      .addCase(fetchCustomers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCustomers.fulfilled, (state, action) => {
        state.isLoading = false;
        const response = action.payload;
        state.customers = response.customers || [];
      })
      .addCase(fetchCustomers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create Customer
    builder
      .addCase(createCustomer.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createCustomer.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          // Add displayName property to the new customer
          const customerWithDisplayName = {
            ...action.payload,
            displayName:
              `${action.payload.firstName || ""} ${
                action.payload.lastName || ""
              }`.trim() || "Unknown Customer",
          };
          state.customers.unshift(customerWithDisplayName);
          state.selectedCustomer = customerWithDisplayName;
        }
      })
      .addCase(createCustomer.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Customer Loyalty
    builder
      .addCase(fetchCustomerLoyalty.pending, (state, action) => {
        const customerId = action.meta.arg;
        state.loyaltyLoading[customerId] = true;
        state.loyaltyErrors[customerId] = null;
      })
      .addCase(fetchCustomerLoyalty.fulfilled, (state, action) => {
        const { customerId, loyaltyData } = action.payload;
        state.loyaltyLoading[customerId] = false;
        state.loyaltyData[customerId] = loyaltyData;
      })
      .addCase(fetchCustomerLoyalty.rejected, (state, action) => {
        const customerId = action.meta.arg;
        state.loyaltyLoading[customerId] = false;
        state.loyaltyErrors[customerId] = action.payload as string;
      });

    // Fetch Customer Loyalty Transactions
    builder
      .addCase(fetchCustomerLoyaltyTransactions.pending, (state, action) => {
        const customerId = action.meta.arg.customerId;
        state.loyaltyLoading[customerId] = true;
        state.loyaltyErrors[customerId] = null;
      })
      .addCase(fetchCustomerLoyaltyTransactions.fulfilled, (state, action) => {
        const { customerId, transactions } = action.payload;
        state.loyaltyLoading[customerId] = false;
        state.loyaltyTransactions[customerId] = transactions;
      })
      .addCase(fetchCustomerLoyaltyTransactions.rejected, (state, action) => {
        const customerId = action.meta.arg.customerId;
        state.loyaltyLoading[customerId] = false;
        state.loyaltyErrors[customerId] = action.payload as string;
      });

    // Refresh Customer Loyalty
    builder
      .addCase(refreshCustomerLoyalty.pending, (state, action) => {
        const customerId = action.meta.arg;
        state.loyaltyLoading[customerId] = true;
        state.loyaltyErrors[customerId] = null;
      })
      .addCase(refreshCustomerLoyalty.fulfilled, (state, action) => {
        const customerId = action.payload;
        state.loyaltyLoading[customerId] = false;
      })
      .addCase(refreshCustomerLoyalty.rejected, (state, action) => {
        const customerId = action.meta.arg;
        state.loyaltyLoading[customerId] = false;
        state.loyaltyErrors[customerId] = "Failed to refresh loyalty data";
      });
  },
});

export const {
  clearError,
  setSearchQuery,
  setSelectedCustomer,
  clearSelectedCustomer,
  clearCustomers,
  clearLoyaltyError,
  clearCustomerLoyalty,
  updateCustomerLoyaltyPoints,
} = customerSlice.actions;

// Selectors
export const selectCustomers = (state: { customers: CustomerState }) =>
  state.customers.customers;
export const selectSelectedCustomer = (state: { customers: CustomerState }) =>
  state.customers.selectedCustomer;
export const selectCustomersLoading = (state: { customers: CustomerState }) =>
  state.customers.isLoading;
export const selectCustomersError = (state: { customers: CustomerState }) =>
  state.customers.error;
export const selectSearchQuery = (state: { customers: CustomerState }) =>
  state.customers.searchQuery;

// Loyalty selectors
export const selectCustomerLoyalty =
  (customerId: string) => (state: { customers: CustomerState }) =>
    state.customers.loyaltyData[customerId] || null;

export const selectCustomerLoyaltyTransactions =
  (customerId: string) => (state: { customers: CustomerState }) =>
    state.customers.loyaltyTransactions[customerId] || [];

export const selectCustomerLoyaltyLoading =
  (customerId: string) => (state: { customers: CustomerState }) =>
    state.customers.loyaltyLoading[customerId] || false;

export const selectCustomerLoyaltyError =
  (customerId: string) => (state: { customers: CustomerState }) =>
    state.customers.loyaltyErrors[customerId] || null;

export const selectAllLoyaltyData = (state: { customers: CustomerState }) =>
  state.customers.loyaltyData;

export default customerSlice.reducer;
