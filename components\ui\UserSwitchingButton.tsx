/**
 * User Switching Button Component
 *
 * Simple button component for triggering user switching modal.
 * Can be used in sidebar, header, or other locations.
 */

import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useUserSwitching } from "@/src/contexts/UserSwitchingContext";
import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import { Alert, StyleSheet, Text, TouchableOpacity } from "react-native";
import { UserSwitchingModal } from "./UserSwitchingModal";

interface UserSwitchingButtonProps {
  variant?: "primary" | "secondary" | "ghost";
  size?: "small" | "medium" | "large";
  showIcon?: boolean;
  showText?: boolean;
  style?: any;
  onPress?: () => void;
}

export const UserSwitchingButton: React.FC<UserSwitchingButtonProps> = ({
  variant = "secondary",
  size = "medium",
  showIcon = true,
  showText = true,
  style,
  onPress,
}) => {
  const theme = useTheme();
  const { hasPermission, user } = useSession();
  const { sessionContext } = useUserSwitching();
  const [showModal, setShowModal] = useState(false);

  // Check if user can switch users
  const canSwitchUsers =
    hasPermission("switch_users") ||
    user?.role === "super_admin" ||
    user?.role === "manager";

  const handlePress = () => {
    if (onPress) {
      onPress();
      return;
    }

    if (!canSwitchUsers) {
      Alert.alert(
        "Permission Denied",
        "You do not have permission to switch users.",
        [{ text: "OK" }]
      );
      return;
    }

    setShowModal(true);
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button];

    // Size styles
    switch (size) {
      case "small":
        baseStyle.push(styles.buttonSmall);
        break;
      case "large":
        baseStyle.push(styles.buttonLarge);
        break;
      default:
        baseStyle.push(styles.buttonMedium);
    }

    // Variant styles
    switch (variant) {
      case "primary":
        baseStyle.push({
          backgroundColor: theme.colors.primary,
        });
        break;
      case "secondary":
        baseStyle.push({
          backgroundColor: theme.colors.surface,
          borderWidth: 1,
          borderColor: theme.colors.border,
        });
        break;
      case "ghost":
        baseStyle.push({
          backgroundColor: "transparent",
        });
        break;
    }

    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle = [styles.text];

    // Size styles
    switch (size) {
      case "small":
        baseStyle.push(styles.textSmall);
        break;
      case "large":
        baseStyle.push(styles.textLarge);
        break;
      default:
        baseStyle.push(styles.textMedium);
    }

    // Variant styles
    switch (variant) {
      case "primary":
        baseStyle.push({ color: theme.colors.primaryForeground });
        break;
      case "secondary":
        baseStyle.push({ color: theme.colors.text });
        break;
      case "ghost":
        baseStyle.push({ color: theme.colors.text });
        break;
    }

    return baseStyle;
  };

  const getIconSize = () => {
    switch (size) {
      case "small":
        return 16;
      case "large":
        return 24;
      default:
        return 20;
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case "primary":
        return theme.colors.primaryForeground;
      case "secondary":
        return theme.colors.text;
      case "ghost":
        return theme.colors.text;
      default:
        return theme.colors.text;
    }
  };

  const hasActiveSwitch = sessionContext?.hasActiveSwitch || false;
  const buttonText = hasActiveSwitch ? "Switch Back" : "Switch User";
  const iconName = hasActiveSwitch ? "arrow-back" : "people";

  if (!canSwitchUsers) {
    return null; // Don't render if user doesn't have permission
  }

  return (
    <>
      <TouchableOpacity
        style={[...getButtonStyle(), style]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        {showIcon && (
          <Ionicons
            name={iconName}
            size={getIconSize()}
            color={getIconColor()}
            style={showText ? styles.iconWithText : undefined}
          />
        )}
        {showText && <Text style={getTextStyle()}>{buttonText}</Text>}
      </TouchableOpacity>

      <UserSwitchingModal
        visible={showModal}
        onClose={() => setShowModal(false)}
        onSwitchComplete={(success) => {
          if (success) {
            setShowModal(false);
          }
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  buttonSmall: {
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 6,
  },
  buttonMedium: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  buttonLarge: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 10,
  },
  text: {
    fontWeight: "600",
  },
  textSmall: {
    fontSize: 12,
  },
  textMedium: {
    fontSize: 14,
  },
  textLarge: {
    fontSize: 16,
  },
  iconWithText: {
    marginRight: 6,
  },
});

export default UserSwitchingButton;
