/**
 * Offline State Management Service
 * 
 * Handles robust offline/online state management with intelligent
 * data queuing, conflict prevention, and seamless synchronization
 * when connectivity is restored.
 */

import { store } from '@/src/store';
import { ticketSyncService } from './TicketSyncService';
import { autoSaveService } from './AutoSaveService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

export interface OfflineOperation {
  id: string;
  type: 'create' | 'update' | 'delete' | 'sync';
  entityType: 'ticket' | 'item' | 'customer' | 'order';
  entityId: string;
  data: any;
  timestamp: Date;
  retryCount: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  dependencies?: string[]; // IDs of operations this depends on
}

export interface OfflineState {
  isOnline: boolean;
  networkQuality: 'poor' | 'fair' | 'good' | 'excellent';
  lastOnlineTime: Date | null;
  queuedOperations: OfflineOperation[];
  failedOperations: OfflineOperation[];
  syncInProgress: boolean;
  estimatedSyncTime: number; // in seconds
}

export interface NetworkMetrics {
  latency: number;
  bandwidth: number;
  stability: number; // 0-1, how stable the connection is
  lastMeasured: Date;
}

class OfflineStateManager {
  private state: OfflineState;
  private networkMetrics: NetworkMetrics;
  private syncQueue: OfflineOperation[] = [];
  private operationHistory: Array<{ operation: OfflineOperation; success: boolean; timestamp: Date }> = [];
  private listeners: Array<(state: OfflineState) => void> = [];
  
  // Configuration
  private readonly MAX_QUEUE_SIZE = 1000;
  private readonly MAX_RETRY_ATTEMPTS = 5;
  private readonly SYNC_BATCH_SIZE = 10;
  private readonly STORAGE_KEY = 'dukalink_offline_queue';

  constructor() {
    this.state = {
      isOnline: true,
      networkQuality: 'good',
      lastOnlineTime: new Date(),
      queuedOperations: [],
      failedOperations: [],
      syncInProgress: false,
      estimatedSyncTime: 0,
    };

    this.networkMetrics = {
      latency: 100,
      bandwidth: 1000, // kbps
      stability: 1.0,
      lastMeasured: new Date(),
    };

    this.initialize();
  }

  /**
   * Initialize offline state manager
   */
  private async initialize(): Promise<void> {
    try {
      // Load persisted queue
      await this.loadPersistedQueue();
      
      // Setup network monitoring
      this.setupNetworkMonitoring();
      
      // Setup periodic sync
      this.setupPeriodicSync();
      
      // Setup network quality monitoring
      this.setupNetworkQualityMonitoring();

    } catch (error) {
      console.error('Failed to initialize offline state manager:', error);
    }
  }

  /**
   * Add operation to offline queue
   */
  async queueOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): Promise<string> {
    const fullOperation: OfflineOperation = {
      ...operation,
      id: this.generateOperationId(),
      timestamp: new Date(),
      retryCount: 0,
    };

    // Add to queue
    this.syncQueue.push(fullOperation);
    this.state.queuedOperations = [...this.syncQueue];

    // Persist queue
    await this.persistQueue();

    // Estimate sync time
    this.updateSyncTimeEstimate();

    // Notify listeners
    this.notifyStateChange();

    // Try immediate sync if online
    if (this.state.isOnline && !this.state.syncInProgress) {
      this.processSyncQueue();
    }

    return fullOperation.id;
  }

  /**
   * Process sync queue when online
   */
  private async processSyncQueue(): Promise<void> {
    if (this.state.syncInProgress || !this.state.isOnline || this.syncQueue.length === 0) {
      return;
    }

    this.state.syncInProgress = true;
    this.notifyStateChange();

    try {
      // Sort operations by priority and dependencies
      const sortedOperations = this.sortOperationsByPriority();
      
      // Process in batches
      const batches = this.createBatches(sortedOperations, this.SYNC_BATCH_SIZE);
      
      for (const batch of batches) {
        await this.processBatch(batch);
        
        // Check if still online
        if (!this.state.isOnline) {
          break;
        }
      }

      // Clean up completed operations
      this.cleanupCompletedOperations();
      
    } catch (error) {
      console.error('Sync queue processing failed:', error);
    } finally {
      this.state.syncInProgress = false;
      this.updateSyncTimeEstimate();
      this.notifyStateChange();
    }
  }

  /**
   * Process a batch of operations
   */
  private async processBatch(operations: OfflineOperation[]): Promise<void> {
    const promises = operations.map(operation => this.processOperation(operation));
    await Promise.allSettled(promises);
  }

  /**
   * Process individual operation
   */
  private async processOperation(operation: OfflineOperation): Promise<void> {
    try {
      let success = false;

      switch (operation.type) {
        case 'create':
          success = await this.processCreateOperation(operation);
          break;
        case 'update':
          success = await this.processUpdateOperation(operation);
          break;
        case 'delete':
          success = await this.processDeleteOperation(operation);
          break;
        case 'sync':
          success = await this.processSyncOperation(operation);
          break;
      }

      if (success) {
        // Remove from queue
        this.removeOperationFromQueue(operation.id);
        
        // Record success
        this.recordOperationResult(operation, true);
      } else {
        // Increment retry count
        operation.retryCount++;
        
        if (operation.retryCount >= this.MAX_RETRY_ATTEMPTS) {
          // Move to failed operations
          this.moveToFailedOperations(operation);
        }
        
        // Record failure
        this.recordOperationResult(operation, false);
      }

    } catch (error) {
      console.error(`Operation ${operation.id} failed:`, error);
      operation.retryCount++;
      
      if (operation.retryCount >= this.MAX_RETRY_ATTEMPTS) {
        this.moveToFailedOperations(operation);
      }
      
      this.recordOperationResult(operation, false);
    }
  }

  /**
   * Process create operation
   */
  private async processCreateOperation(operation: OfflineOperation): Promise<boolean> {
    try {
      switch (operation.entityType) {
        case 'ticket':
          // Create ticket via API
          const result = await store.dispatch(require('@/src/store/thunks/ticketThunks').saveTicket({
            ticket: operation.data
          })).unwrap();
          return !!result;
        
        default:
          console.warn(`Unsupported create operation for entity type: ${operation.entityType}`);
          return false;
      }
    } catch (error) {
      console.error('Create operation failed:', error);
      return false;
    }
  }

  /**
   * Process update operation
   */
  private async processUpdateOperation(operation: OfflineOperation): Promise<boolean> {
    try {
      switch (operation.entityType) {
        case 'ticket':
          // Update ticket via auto-save
          autoSaveService.scheduleAutoSave(operation.entityId, true);
          return true;
        
        default:
          console.warn(`Unsupported update operation for entity type: ${operation.entityType}`);
          return false;
      }
    } catch (error) {
      console.error('Update operation failed:', error);
      return false;
    }
  }

  /**
   * Process delete operation
   */
  private async processDeleteOperation(operation: OfflineOperation): Promise<boolean> {
    try {
      switch (operation.entityType) {
        case 'ticket':
          // Delete ticket via API
          const result = await store.dispatch(require('@/src/store/thunks/ticketThunks').deleteTicketFromBackend(
            operation.entityId
          )).unwrap();
          return !!result;
        
        default:
          console.warn(`Unsupported delete operation for entity type: ${operation.entityType}`);
          return false;
      }
    } catch (error) {
      console.error('Delete operation failed:', error);
      return false;
    }
  }

  /**
   * Process sync operation
   */
  private async processSyncOperation(operation: OfflineOperation): Promise<boolean> {
    try {
      const result = await ticketSyncService.performSync(true);
      return result.success;
    } catch (error) {
      console.error('Sync operation failed:', error);
      return false;
    }
  }

  /**
   * Setup network monitoring
   */
  private setupNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      const wasOnline = this.state.isOnline;
      const isOnline = state.isConnected ?? false;
      
      this.state.isOnline = isOnline;
      
      if (isOnline) {
        this.state.lastOnlineTime = new Date();
        
        // If we just came back online, process queue
        if (!wasOnline) {
          console.log('Network restored, processing offline queue');
          this.processSyncQueue();
        }
      }
      
      this.notifyStateChange();
    });
  }

  /**
   * Setup periodic sync
   */
  private setupPeriodicSync(): void {
    setInterval(() => {
      if (this.state.isOnline && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Setup network quality monitoring
   */
  private setupNetworkQualityMonitoring(): void {
    setInterval(async () => {
      if (this.state.isOnline) {
        await this.measureNetworkQuality();
      }
    }, 60000); // Every minute
  }

  /**
   * Measure network quality
   */
  private async measureNetworkQuality(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Simple ping to measure latency
      // In a real implementation, this would ping your server
      const response = await fetch('https://httpbin.org/get', {
        method: 'GET',
        cache: 'no-cache',
      });
      
      const latency = Date.now() - startTime;
      
      this.networkMetrics = {
        latency,
        bandwidth: this.estimateBandwidth(latency),
        stability: this.calculateStability(),
        lastMeasured: new Date(),
      };
      
      this.state.networkQuality = this.calculateNetworkQuality();
      this.notifyStateChange();
      
    } catch (error) {
      console.error('Network quality measurement failed:', error);
      this.state.networkQuality = 'poor';
    }
  }

  /**
   * Estimate bandwidth based on latency
   */
  private estimateBandwidth(latency: number): number {
    // Simple estimation - in reality you'd measure actual transfer speeds
    if (latency < 100) return 10000; // 10 Mbps
    if (latency < 300) return 5000;  // 5 Mbps
    if (latency < 500) return 1000;  // 1 Mbps
    return 500; // 500 kbps
  }

  /**
   * Calculate connection stability
   */
  private calculateStability(): number {
    // Based on recent operation success rate
    const recentOperations = this.operationHistory.slice(-20);
    if (recentOperations.length === 0) return 1.0;
    
    const successCount = recentOperations.filter(op => op.success).length;
    return successCount / recentOperations.length;
  }

  /**
   * Calculate overall network quality
   */
  private calculateNetworkQuality(): OfflineState['networkQuality'] {
    const { latency, stability } = this.networkMetrics;
    
    if (latency < 100 && stability > 0.9) return 'excellent';
    if (latency < 300 && stability > 0.8) return 'good';
    if (latency < 500 && stability > 0.6) return 'fair';
    return 'poor';
  }

  /**
   * Sort operations by priority and dependencies
   */
  private sortOperationsByPriority(): OfflineOperation[] {
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    
    return [...this.syncQueue].sort((a, b) => {
      // First by priority
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // Then by timestamp (older first)
      return a.timestamp.getTime() - b.timestamp.getTime();
    });
  }

  /**
   * Create batches from operations
   */
  private createBatches(operations: OfflineOperation[], batchSize: number): OfflineOperation[][] {
    const batches: OfflineOperation[][] = [];
    
    for (let i = 0; i < operations.length; i += batchSize) {
      batches.push(operations.slice(i, i + batchSize));
    }
    
    return batches;
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Remove operation from queue
   */
  private removeOperationFromQueue(operationId: string): void {
    this.syncQueue = this.syncQueue.filter(op => op.id !== operationId);
    this.state.queuedOperations = [...this.syncQueue];
  }

  /**
   * Move operation to failed operations
   */
  private moveToFailedOperations(operation: OfflineOperation): void {
    this.removeOperationFromQueue(operation.id);
    this.state.failedOperations.push(operation);
  }

  /**
   * Record operation result for analytics
   */
  private recordOperationResult(operation: OfflineOperation, success: boolean): void {
    this.operationHistory.push({
      operation,
      success,
      timestamp: new Date(),
    });
    
    // Keep only recent history
    if (this.operationHistory.length > 100) {
      this.operationHistory = this.operationHistory.slice(-50);
    }
  }

  /**
   * Clean up completed operations
   */
  private cleanupCompletedOperations(): void {
    // Remove old failed operations
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    this.state.failedOperations = this.state.failedOperations.filter(
      op => op.timestamp > cutoffTime
    );
  }

  /**
   * Update sync time estimate
   */
  private updateSyncTimeEstimate(): void {
    const operationCount = this.syncQueue.length;
    const avgLatency = this.networkMetrics.latency / 1000; // Convert to seconds
    const batchCount = Math.ceil(operationCount / this.SYNC_BATCH_SIZE);
    
    this.state.estimatedSyncTime = batchCount * avgLatency * 2; // Conservative estimate
  }

  /**
   * Persist queue to storage
   */
  private async persistQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('Failed to persist offline queue:', error);
    }
  }

  /**
   * Load persisted queue from storage
   */
  private async loadPersistedQueue(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.syncQueue = JSON.parse(stored);
        this.state.queuedOperations = [...this.syncQueue];
      }
    } catch (error) {
      console.error('Failed to load persisted queue:', error);
    }
  }

  /**
   * Add state change listener
   */
  addStateListener(listener: (state: OfflineState) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of state change
   */
  private notifyStateChange(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('State listener error:', error);
      }
    });
  }

  /**
   * Get current offline state
   */
  getState(): OfflineState {
    return { ...this.state };
  }

  /**
   * Get network metrics
   */
  getNetworkMetrics(): NetworkMetrics {
    return { ...this.networkMetrics };
  }

  /**
   * Retry failed operations
   */
  async retryFailedOperations(): Promise<void> {
    const failedOps = [...this.state.failedOperations];
    this.state.failedOperations = [];
    
    for (const op of failedOps) {
      op.retryCount = 0; // Reset retry count
      await this.queueOperation(op);
    }
  }

  /**
   * Clear all queued operations
   */
  async clearQueue(): Promise<void> {
    this.syncQueue = [];
    this.state.queuedOperations = [];
    this.state.failedOperations = [];
    
    await AsyncStorage.removeItem(this.STORAGE_KEY);
    this.notifyStateChange();
  }

  /**
   * Get operation statistics
   */
  getOperationStats(): {
    totalOperations: number;
    successRate: number;
    averageLatency: number;
    queueSize: number;
    failedCount: number;
  } {
    const total = this.operationHistory.length;
    const successful = this.operationHistory.filter(op => op.success).length;
    
    return {
      totalOperations: total,
      successRate: total > 0 ? successful / total : 0,
      averageLatency: this.networkMetrics.latency,
      queueSize: this.syncQueue.length,
      failedCount: this.state.failedOperations.length,
    };
  }
}

// Export singleton instance
export const offlineStateManager = new OfflineStateManager();
export default OfflineStateManager;
