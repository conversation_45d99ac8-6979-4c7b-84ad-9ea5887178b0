/**
 * Responsive Text Component for Dukalink POS
 * Automatically scales typography based on screen size
 */

import React from "react";
import { Text, TextProps, TextStyle } from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import { getResponsiveTypography } from "@/src/utils/responsiveUtils";

interface ResponsiveTextProps extends TextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'bodyLarge' | 'bodySmall' | 'caption' | 'label' | 'button';
  color?: 'primary' | 'secondary' | 'muted' | 'accent' | 'success' | 'warning' | 'error';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  align?: 'left' | 'center' | 'right';
}

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  variant = 'body',
  color = 'primary',
  weight,
  align,
  style,
  ...props
}) => {
  const theme = useTheme();
  const { screenSize } = useResponsiveLayout();

  // Get responsive typography
  const responsiveTypography = getResponsiveTypography(theme, screenSize, variant);

  // Get color style
  const getColorStyle = (): TextStyle => {
    switch (color) {
      case 'primary':
        return { color: theme.colors.text };
      case 'secondary':
        return { color: theme.colors.textSecondary };
      case 'muted':
        return { color: theme.colors.textMuted };
      case 'accent':
        return { color: theme.colors.primary };
      case 'success':
        return { color: theme.colors.success };
      case 'warning':
        return { color: theme.colors.warning };
      case 'error':
        return { color: theme.colors.error };
      default:
        return { color: theme.colors.text };
    }
  };

  // Get weight style
  const getWeightStyle = (): TextStyle => {
    if (!weight) return {};
    
    switch (weight) {
      case 'normal':
        return { fontWeight: '400' };
      case 'medium':
        return { fontWeight: '500' };
      case 'semibold':
        return { fontWeight: '600' };
      case 'bold':
        return { fontWeight: '700' };
      default:
        return {};
    }
  };

  // Get alignment style
  const getAlignStyle = (): TextStyle => {
    if (!align) return {};
    return { textAlign: align };
  };

  return (
    <Text
      style={[
        responsiveTypography,
        getColorStyle(),
        getWeightStyle(),
        getAlignStyle(),
        style,
      ]}
      {...props}
    >
      {children}
    </Text>
  );
};

// Convenience components for common text variants
export const ResponsiveHeading1: React.FC<Omit<ResponsiveTextProps, 'variant'>> = (props) => (
  <ResponsiveText variant="h1" weight="bold" {...props} />
);

export const ResponsiveHeading2: React.FC<Omit<ResponsiveTextProps, 'variant'>> = (props) => (
  <ResponsiveText variant="h2" weight="semibold" {...props} />
);

export const ResponsiveHeading3: React.FC<Omit<ResponsiveTextProps, 'variant'>> = (props) => (
  <ResponsiveText variant="h3" weight="semibold" {...props} />
);

export const ResponsiveBody: React.FC<Omit<ResponsiveTextProps, 'variant'>> = (props) => (
  <ResponsiveText variant="body" {...props} />
);

export const ResponsiveCaption: React.FC<Omit<ResponsiveTextProps, 'variant'>> = (props) => (
  <ResponsiveText variant="caption" color="secondary" {...props} />
);

export const ResponsiveLabel: React.FC<Omit<ResponsiveTextProps, 'variant'>> = (props) => (
  <ResponsiveText variant="label" weight="medium" {...props} />
);
