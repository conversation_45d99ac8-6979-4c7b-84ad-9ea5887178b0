import { Tabs, useRouter } from "expo-router";
import React from "react";
import { Platform } from "react-native";

import { GlobalLayout } from "@/components/layout/GlobalLayout";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { isDesktop } = useResponsiveLayout();

  // Remove old auth check - POS login handles this now
  // const { isAuthenticated } = useAppSelector((state) => state.auth);

  // useEffect(() => {
  //   if (!isAuthenticated) {
  //     router.replace("/auth");
  //   }
  // }, [isAuthenticated, router]);

  // if (!isAuthenticated) {
  //   return null; // Show nothing while redirecting
  // }

  return (
    <GlobalLayout>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: Colors[colorScheme ?? "light"].tint,
          headerShown: false,

          tabBarStyle: { display: "none" }, // Hide the tab bar completely
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: "Dashboard",
          }}
        />
        <Tabs.Screen
          name="products"
          options={{
            title: "Products",
          }}
        />
        <Tabs.Screen
          name="cart"
          options={{
            title: "Cart",
          }}
        />
        <Tabs.Screen
          name="orders"
          options={{
            title: "Orders",
          }}
        />
      </Tabs>
    </GlobalLayout>
  );
}
