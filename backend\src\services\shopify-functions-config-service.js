/**
 * Shopify Functions Configuration Service
 * Manages configuration for Shopify Functions including commission discounts and loyalty features
 */

const shopifyService = require("./shopify-service");

class ShopifyFunctionsConfigService {
  constructor() {
    this.defaultCommissionConfig = {
      staff_discount_rate: 2.0,
      agent_discount_rate: 3.0,
      loyalty_multiplier: 1.1,
      min_order_amount: 50.0,
      max_discount_percentage: 15.0,
      commission_threshold: 1.0,
      // Enhanced loyalty configuration
      loyalty_enabled: true,
      tier_discounts: {
        bronze: 2.0,
        silver: 5.0,
        gold: 8.0,
        platinum: 12.0
      },
      points_redemption_enabled: true,
      points_to_currency_rate: 0.01, // 100 points = 1 KSh
      min_points_redemption: 100,
      max_points_per_order: 5000,
      loyalty_points_earning_rate: 1.0 // 1 point per KSh spent
    };

    this.defaultLoyaltyConfig = {
      enabled: true,
      tier_benefits: {
        bronze: {
          automatic_discount: 0.0,
          free_shipping_threshold: 1000.0,
          bonus_points_multiplier: 1.0,
          early_access_enabled: false
        },
        silver: {
          automatic_discount: 2.0,
          free_shipping_threshold: 750.0,
          bonus_points_multiplier: 1.2,
          early_access_enabled: false
        },
        gold: {
          automatic_discount: 5.0,
          free_shipping_threshold: 500.0,
          bonus_points_multiplier: 1.5,
          early_access_enabled: true
        },
        platinum: {
          automatic_discount: 8.0,
          free_shipping_threshold: 250.0,
          bonus_points_multiplier: 2.0,
          early_access_enabled: true
        }
      },
      special_promotions: {
        double_points_days: ["friday", "saturday"],
        seasonal_bonus: 5.0,
        bulk_purchase_threshold: 2000.0,
        bulk_purchase_discount: 10.0,
        category_specific_discounts: [
          {
            product_type: "Electronics",
            discount_percentage: 3.0,
            min_tier: "silver"
          },
          {
            product_type: "Fashion",
            discount_percentage: 5.0,
            min_tier: "gold"
          }
        ]
      },
      birthday_discount: 10.0,
      anniversary_discount: 15.0,
      min_order_amount: 100.0,
      max_discount_per_order: 500.0,
      stackable_with_other_discounts: true
    };
  }

  // Get commission function configuration
  async getCommissionFunctionConfig() {
    try {
      // Try to get existing configuration from Shopify
      const result = await shopifyService.makeRequest(
        "GET",
        "/metafields.json?namespace=$app:commission-discounts&key=function-configuration"
      );

      if (result.success && result.data.metafields && result.data.metafields.length > 0) {
        const configValue = result.data.metafields[0].value;
        return {
          success: true,
          configuration: typeof configValue === 'string' ? JSON.parse(configValue) : configValue
        };
      }

      // Return default configuration if not found
      return {
        success: true,
        configuration: this.defaultCommissionConfig
      };
    } catch (error) {
      console.error("Get commission function config error:", error);
      return {
        success: false,
        error: "Failed to get commission function configuration"
      };
    }
  }

  // Update commission function configuration
  async updateCommissionFunctionConfig(newConfig) {
    try {
      // Validate configuration
      const validatedConfig = this.validateCommissionConfig(newConfig);
      if (!validatedConfig.isValid) {
        return {
          success: false,
          error: validatedConfig.errors.join(", ")
        };
      }

      // Update or create metafield
      const metafieldData = {
        metafield: {
          namespace: "$app:commission-discounts",
          key: "function-configuration",
          value: JSON.stringify(newConfig),
          type: "json"
        }
      };

      const result = await shopifyService.makeRequest("POST", "/metafields.json", metafieldData);

      if (result.success) {
        return {
          success: true,
          configuration: newConfig,
          message: "Commission function configuration updated successfully"
        };
      } else {
        return {
          success: false,
          error: result.error || "Failed to update commission function configuration"
        };
      }
    } catch (error) {
      console.error("Update commission function config error:", error);
      return {
        success: false,
        error: "Failed to update commission function configuration"
      };
    }
  }

  // Get loyalty function configuration
  async getLoyaltyFunctionConfig() {
    try {
      const result = await shopifyService.makeRequest(
        "GET",
        "/metafields.json?namespace=$app:loyalty-discounts&key=function-configuration"
      );

      if (result.success && result.data.metafields && result.data.metafields.length > 0) {
        const configValue = result.data.metafields[0].value;
        return {
          success: true,
          configuration: typeof configValue === 'string' ? JSON.parse(configValue) : configValue
        };
      }

      return {
        success: true,
        configuration: this.defaultLoyaltyConfig
      };
    } catch (error) {
      console.error("Get loyalty function config error:", error);
      return {
        success: false,
        error: "Failed to get loyalty function configuration"
      };
    }
  }

  // Update loyalty function configuration
  async updateLoyaltyFunctionConfig(newConfig) {
    try {
      const validatedConfig = this.validateLoyaltyConfig(newConfig);
      if (!validatedConfig.isValid) {
        return {
          success: false,
          error: validatedConfig.errors.join(", ")
        };
      }

      const metafieldData = {
        metafield: {
          namespace: "$app:loyalty-discounts",
          key: "function-configuration",
          value: JSON.stringify(newConfig),
          type: "json"
        }
      };

      const result = await shopifyService.makeRequest("POST", "/metafields.json", metafieldData);

      if (result.success) {
        return {
          success: true,
          configuration: newConfig,
          message: "Loyalty function configuration updated successfully"
        };
      } else {
        return {
          success: false,
          error: result.error || "Failed to update loyalty function configuration"
        };
      }
    } catch (error) {
      console.error("Update loyalty function config error:", error);
      return {
        success: false,
        error: "Failed to update loyalty function configuration"
      };
    }
  }

  // Validate commission configuration
  validateCommissionConfig(config) {
    const errors = [];

    if (typeof config.staff_discount_rate !== 'number' || config.staff_discount_rate < 0) {
      errors.push("Staff discount rate must be a non-negative number");
    }

    if (typeof config.agent_discount_rate !== 'number' || config.agent_discount_rate < 0) {
      errors.push("Agent discount rate must be a non-negative number");
    }

    if (typeof config.max_discount_percentage !== 'number' || config.max_discount_percentage <= 0 || config.max_discount_percentage > 100) {
      errors.push("Max discount percentage must be between 0 and 100");
    }

    if (typeof config.min_order_amount !== 'number' || config.min_order_amount < 0) {
      errors.push("Minimum order amount must be a non-negative number");
    }

    if (config.loyalty_enabled && config.tier_discounts) {
      const tiers = ['bronze', 'silver', 'gold', 'platinum'];
      for (const tier of tiers) {
        if (typeof config.tier_discounts[tier] !== 'number' || config.tier_discounts[tier] < 0) {
          errors.push(`${tier} tier discount must be a non-negative number`);
        }
      }
    }

    if (config.points_redemption_enabled) {
      if (typeof config.points_to_currency_rate !== 'number' || config.points_to_currency_rate <= 0) {
        errors.push("Points to currency rate must be a positive number");
      }

      if (typeof config.min_points_redemption !== 'number' || config.min_points_redemption <= 0) {
        errors.push("Minimum points redemption must be a positive number");
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  // Validate loyalty configuration
  validateLoyaltyConfig(config) {
    const errors = [];

    if (typeof config.enabled !== 'boolean') {
      errors.push("Enabled must be a boolean value");
    }

    if (typeof config.min_order_amount !== 'number' || config.min_order_amount < 0) {
      errors.push("Minimum order amount must be a non-negative number");
    }

    if (typeof config.max_discount_per_order !== 'number' || config.max_discount_per_order < 0) {
      errors.push("Maximum discount per order must be a non-negative number");
    }

    if (config.tier_benefits) {
      const tiers = ['bronze', 'silver', 'gold', 'platinum'];
      for (const tier of tiers) {
        const benefit = config.tier_benefits[tier];
        if (benefit) {
          if (typeof benefit.automatic_discount !== 'number' || benefit.automatic_discount < 0) {
            errors.push(`${tier} automatic discount must be a non-negative number`);
          }
          if (typeof benefit.free_shipping_threshold !== 'number' || benefit.free_shipping_threshold < 0) {
            errors.push(`${tier} free shipping threshold must be a non-negative number`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  // Get all function configurations
  async getAllConfigurations() {
    try {
      const [commissionResult, loyaltyResult] = await Promise.all([
        this.getCommissionFunctionConfig(),
        this.getLoyaltyFunctionConfig()
      ]);

      return {
        success: true,
        configurations: {
          commission: commissionResult.success ? commissionResult.configuration : this.defaultCommissionConfig,
          loyalty: loyaltyResult.success ? loyaltyResult.configuration : this.defaultLoyaltyConfig
        }
      };
    } catch (error) {
      console.error("Get all configurations error:", error);
      return {
        success: false,
        error: "Failed to get function configurations"
      };
    }
  }
}

module.exports = new ShopifyFunctionsConfigService();
