/**
 * Tier Progress Indicator Component
 * Shows customer's current tier and progress towards next tier
 */

import React from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { useTheme } from '@/src/contexts/ThemeContext';
import { createStyleUtils } from '@/src/utils/themeUtils';
import { ThemedView, ThemedText } from '@/src/components/themed/ThemedComponents';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { formatCurrency } from '@/src/utils/currencyUtils';

interface TierProgressIndicatorProps {
  currentTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  currentSpending: number;
  nextTierThreshold?: number;
  compact?: boolean;
  showLabels?: boolean;
  animated?: boolean;
}

const TIER_CONFIG = {
  bronze: {
    color: '#CD7F32',
    icon: 'star.fill',
    label: 'Bronze',
    threshold: 0,
  },
  silver: {
    color: '#C0C0C0',
    icon: 'star.fill',
    label: 'Silver',
    threshold: 5000,
  },
  gold: {
    color: '#FFD700',
    icon: 'crown.fill',
    label: 'Gold',
    threshold: 15000,
  },
  platinum: {
    color: '#E5E4E2',
    icon: 'crown.fill',
    label: 'Platinum',
    threshold: 50000,
  },
};

const TIER_ORDER = ['bronze', 'silver', 'gold', 'platinum'] as const;

export const TierProgressIndicator: React.FC<TierProgressIndicatorProps> = ({
  currentTier,
  currentSpending,
  nextTierThreshold,
  compact = false,
  showLabels = true,
  animated = true,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const currentTierIndex = TIER_ORDER.indexOf(currentTier);
  const nextTier = TIER_ORDER[currentTierIndex + 1];
  const isMaxTier = currentTierIndex === TIER_ORDER.length - 1;

  // Calculate progress percentage
  const currentTierThreshold = TIER_CONFIG[currentTier].threshold;
  const nextThreshold = nextTierThreshold || (nextTier ? TIER_CONFIG[nextTier].threshold : currentTierThreshold);
  const progressRange = nextThreshold - currentTierThreshold;
  const currentProgress = currentSpending - currentTierThreshold;
  const progressPercentage = isMaxTier ? 100 : Math.min((currentProgress / progressRange) * 100, 100);

  const remainingAmount = isMaxTier ? 0 : Math.max(nextThreshold - currentSpending, 0);

  const renderTierIcon = (tier: keyof typeof TIER_CONFIG, isActive: boolean, isPassed: boolean) => {
    const config = TIER_CONFIG[tier];
    const iconColor = isPassed || isActive ? config.color : theme.colors.textSecondary;
    const backgroundColor = isPassed || isActive ? `${config.color}20` : theme.colors.surface;

    return (
      <View
        key={tier}
        style={[
          styles.tierIconContainer,
          compact ? styles.tierIconCompact : styles.tierIconFull,
          { backgroundColor },
        ]}
      >
        <IconSymbol
          name={config.icon}
          size={compact ? 16 : 20}
          color={iconColor}
        />
        {showLabels && !compact && (
          <ThemedText
            variant="caption"
            style={[
              styles.tierLabel,
              { color: isPassed || isActive ? config.color : theme.colors.textSecondary },
            ]}
          >
            {config.label}
          </ThemedText>
        )}
      </View>
    );
  };

  const renderProgressBar = () => {
    if (isMaxTier) {
      return (
        <View style={styles.maxTierContainer}>
          <View style={[styles.progressTrack, { backgroundColor: TIER_CONFIG[currentTier].color }]} />
          <ThemedText variant="small" color="secondary" style={styles.maxTierText}>
            Maximum tier achieved!
          </ThemedText>
        </View>
      );
    }

    return (
      <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: theme.colors.border }]}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                backgroundColor: TIER_CONFIG[currentTier].color,
                width: `${progressPercentage}%`,
              },
            ]}
          />
        </View>
        {!compact && (
          <View style={styles.progressLabels}>
            <ThemedText variant="caption" color="secondary">
              {formatCurrency(currentSpending)}
            </ThemedText>
            <ThemedText variant="caption" color="secondary">
              {formatCurrency(nextThreshold)}
            </ThemedText>
          </View>
        )}
      </View>
    );
  };

  const renderCompactView = () => (
    <ThemedView variant="card" style={[styles.compactContainer, utils.p('sm')]}>
      <View style={styles.compactHeader}>
        {renderTierIcon(currentTier, true, true)}
        <View style={styles.compactInfo}>
          <ThemedText variant="small" style={{ color: TIER_CONFIG[currentTier].color }}>
            {TIER_CONFIG[currentTier].label} Member
          </ThemedText>
          {!isMaxTier && (
            <ThemedText variant="caption" color="secondary">
              {formatCurrency(remainingAmount)} to {TIER_CONFIG[nextTier!].label}
            </ThemedText>
          )}
        </View>
        <ThemedText variant="small" color="secondary">
          {progressPercentage.toFixed(0)}%
        </ThemedText>
      </View>
      {renderProgressBar()}
    </ThemedView>
  );

  const renderFullView = () => (
    <ThemedView variant="card" style={[styles.fullContainer, utils.p('lg')]}>
      {/* Current Tier Header */}
      <View style={styles.currentTierHeader}>
        <View style={styles.currentTierInfo}>
          <View
            style={[
              styles.currentTierIcon,
              { backgroundColor: `${TIER_CONFIG[currentTier].color}20` },
            ]}
          >
            <IconSymbol
              name={TIER_CONFIG[currentTier].icon}
              size={24}
              color={TIER_CONFIG[currentTier].color}
            />
          </View>
          <View>
            <ThemedText variant="h3" style={{ color: TIER_CONFIG[currentTier].color }}>
              {TIER_CONFIG[currentTier].label} Member
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              Current Tier
            </ThemedText>
          </View>
        </View>
        
        <View style={styles.spendingInfo}>
          <ThemedText variant="h3" color="primary">
            {formatCurrency(currentSpending)}
          </ThemedText>
          <ThemedText variant="caption" color="secondary">
            Total Spent
          </ThemedText>
        </View>
      </View>

      {/* Tier Timeline */}
      <View style={styles.tierTimeline}>
        {TIER_ORDER.map((tier, index) => {
          const isActive = tier === currentTier;
          const isPassed = index < currentTierIndex;
          const isNext = index === currentTierIndex + 1;

          return (
            <View key={tier} style={styles.timelineItem}>
              {renderTierIcon(tier, isActive, isPassed)}
              {index < TIER_ORDER.length - 1 && (
                <View
                  style={[
                    styles.timelineConnector,
                    {
                      backgroundColor: isPassed
                        ? TIER_CONFIG[currentTier].color
                        : theme.colors.border,
                    },
                  ]}
                />
              )}
            </View>
          );
        })}
      </View>

      {/* Progress Section */}
      <View style={styles.progressSection}>
        {!isMaxTier ? (
          <>
            <View style={styles.progressHeader}>
              <ThemedText variant="body" color="secondary">
                Progress to {TIER_CONFIG[nextTier!].label}
              </ThemedText>
              <ThemedText variant="body" color="primary">
                {progressPercentage.toFixed(0)}%
              </ThemedText>
            </View>
            {renderProgressBar()}
            <View style={styles.remainingInfo}>
              <ThemedText variant="small" color="secondary">
                Spend {formatCurrency(remainingAmount)} more to reach {TIER_CONFIG[nextTier!].label} tier
              </ThemedText>
            </View>
          </>
        ) : (
          <View style={styles.maxTierSection}>
            {renderProgressBar()}
            <ThemedText variant="body" color="secondary" style={styles.maxTierDescription}>
              You've reached the highest tier! Enjoy all premium benefits.
            </ThemedText>
          </View>
        )}
      </View>
    </ThemedView>
  );

  return compact ? renderCompactView() : renderFullView();
};

const styles = StyleSheet.create({
  compactContainer: {
    marginVertical: 4,
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  compactInfo: {
    flex: 1,
    marginLeft: 8,
  },
  fullContainer: {
    marginVertical: 8,
  },
  currentTierHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  currentTierInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentTierIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  spendingInfo: {
    alignItems: 'flex-end',
  },
  tierTimeline: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  tierIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  tierIconFull: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  tierIconCompact: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  tierLabel: {
    marginTop: 4,
    fontSize: 10,
    fontWeight: '600',
  },
  timelineConnector: {
    flex: 1,
    height: 2,
    marginHorizontal: 8,
  },
  progressSection: {
    marginTop: 8,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressTrack: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  remainingInfo: {
    alignItems: 'center',
    marginTop: 8,
  },
  maxTierContainer: {
    marginBottom: 8,
  },
  maxTierSection: {
    alignItems: 'center',
  },
  maxTierText: {
    textAlign: 'center',
    marginTop: 8,
  },
  maxTierDescription: {
    textAlign: 'center',
    marginTop: 12,
  },
});
