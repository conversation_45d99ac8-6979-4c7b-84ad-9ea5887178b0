import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CartItem } from '@/src/types/shopify';
import useInventoryManagement from '@/src/hooks/useInventoryManagement';

interface CartInventoryValidatorProps {
  cartItems: CartItem[];
  onAdjustCart?: (adjustedItems: CartItem[]) => void;
  showWarnings?: boolean;
}

const CartInventoryValidator: React.FC<CartInventoryValidatorProps> = ({
  cartItems,
  onAdjustCart,
  showWarnings = true,
}) => {
  const {
    validateCartInventory,
    hasOutOfStockItems,
    getLowStockItems,
    adjustCartForInventory,
  } = useInventoryManagement();

  const inventoryValidation = validateCartInventory(cartItems);
  const lowStockItems = getLowStockItems(cartItems);
  const hasOutOfStock = hasOutOfStockItems(cartItems);

  const handleAutoAdjust = () => {
    const adjustment = adjustCartForInventory(cartItems);
    if (adjustment.adjustmentsMade && onAdjustCart) {
      onAdjustCart(adjustment.adjustedItems);
    }
  };

  if (!showWarnings || (inventoryValidation.isValid && lowStockItems.length === 0)) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Out of Stock Errors */}
      {!inventoryValidation.isValid && (
        <View style={styles.errorCard}>
          <View style={styles.errorHeader}>
            <Ionicons name="warning" size={20} color="#e74c3c" />
            <Text style={styles.errorTitle}>Insufficient Inventory</Text>
          </View>
          
          {inventoryValidation.errors.map((error, index) => (
            <Text key={index} style={styles.errorText}>
              • {error}
            </Text>
          ))}

          {onAdjustCart && (
            <TouchableOpacity style={styles.adjustButton} onPress={handleAutoAdjust}>
              <Ionicons name="refresh" size={16} color="#fff" />
              <Text style={styles.adjustButtonText}>Auto-Adjust Cart</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Low Stock Warnings */}
      {inventoryValidation.isValid && lowStockItems.length > 0 && (
        <View style={styles.warningCard}>
          <View style={styles.warningHeader}>
            <Ionicons name="alert-circle" size={20} color="#f39c12" />
            <Text style={styles.warningTitle}>Low Stock Items</Text>
          </View>
          
          {lowStockItems.map((item, index) => (
            <Text key={index} style={styles.warningText}>
              • {item.title}: Only {item.inventoryQuantity} left in stock
            </Text>
          ))}
        </View>
      )}

      {/* Out of Stock Items (when cart has mixed valid/invalid items) */}
      {hasOutOfStock && inventoryValidation.outOfStockItems.length > 0 && (
        <View style={styles.outOfStockCard}>
          <View style={styles.outOfStockHeader}>
            <Ionicons name="close-circle" size={20} color="#e74c3c" />
            <Text style={styles.outOfStockTitle}>Out of Stock</Text>
          </View>
          
          {inventoryValidation.outOfStockItems.map((item, index) => (
            <View key={index} style={styles.outOfStockItem}>
              <Text style={styles.outOfStockText}>
                {item.title}
              </Text>
              <Text style={styles.outOfStockDetails}>
                Requested: {item.quantity} | Available: {item.inventoryQuantity}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  errorCard: {
    backgroundColor: '#fdf2f2',
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  errorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#e74c3c',
    marginLeft: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#c0392b',
    marginBottom: 4,
    lineHeight: 20,
  },
  adjustButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e74c3c',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  adjustButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  warningCard: {
    backgroundColor: '#fef9e7',
    borderLeftWidth: 4,
    borderLeftColor: '#f39c12',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  warningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#f39c12',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: '#d68910',
    marginBottom: 4,
    lineHeight: 20,
  },
  outOfStockCard: {
    backgroundColor: '#fdf2f2',
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  outOfStockHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  outOfStockTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#e74c3c',
    marginLeft: 8,
  },
  outOfStockItem: {
    marginBottom: 8,
  },
  outOfStockText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#c0392b',
    marginBottom: 2,
  },
  outOfStockDetails: {
    fontSize: 12,
    color: '#e74c3c',
    fontStyle: 'italic',
  },
});

export default CartInventoryValidator;
