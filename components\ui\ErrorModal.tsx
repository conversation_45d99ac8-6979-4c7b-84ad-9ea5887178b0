import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import React from "react";
import { Modal, StyleSheet, Text, View } from "react-native";

export interface ErrorAction {
  title: string;
  onPress: () => void;
  variant?: "primary" | "outline" | "ghost";
  icon?: string;
}

interface ErrorModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  message: string;
  actions?: ErrorAction[];
  showCloseButton?: boolean;
  showRetryButton?: boolean;
  onRetry?: () => void;
}

export function ErrorModal({
  visible,
  onClose,
  title,
  message,
  actions = [],
  showCloseButton = true,
  showRetryButton = false,
  onRetry,
}: ErrorModalProps) {
  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const borderColor = useThemeColor({}, "border");
  const errorColor = "#EF4444"; // Red color for errors

  const handleActionPress = (action: ErrorAction) => {
    action.onPress();
    onClose();
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
    onClose();
  };

  // Default actions if none provided
  const defaultActions: ErrorAction[] = [];

  if (showRetryButton && onRetry) {
    defaultActions.push({
      title: "Retry",
      onPress: handleRetry,
      variant: "primary",
      icon: "arrow.clockwise",
    });
  }

  defaultActions.push({
    title: "OK",
    onPress: onClose,
    variant: "outline",
  });

  const finalActions = actions.length > 0 ? actions : defaultActions;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}} // Disable Android back button
    >
      <View style={styles.overlay}>
        <View style={[styles.modal, { backgroundColor, borderColor }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <View
                style={[
                  styles.errorIcon,
                  { backgroundColor: errorColor + "20" },
                ]}
              >
                <IconSymbol
                  name="exclamationmark.triangle.fill"
                  size={32}
                  color={errorColor}
                />
              </View>
              <Text style={[styles.title, { color: textColor }]}>{title}</Text>
            </View>
            {/* Close button removed - users must use action buttons */}
          </View>

          {/* Message */}
          <View style={styles.messageSection}>
            <Text style={[styles.message, { color: textSecondary }]}>
              {message}
            </Text>
          </View>

          {/* Actions */}
          <View style={styles.actionsSection}>
            {finalActions.map((action, index) => (
              <ModernButton
                key={index}
                title={action.title}
                onPress={() => handleActionPress(action)}
                variant={action.variant || "outline"}
                icon={
                  action.icon ? (
                    <IconSymbol name={action.icon} size={16} color="white" />
                  ) : undefined
                }
                style={[
                  styles.actionButton,
                  finalActions.length === 2 && styles.halfWidthButton,
                ]}
              />
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modal: {
    width: "90%",
    maxWidth: 400,
    borderRadius: 16,
    borderWidth: 1,
    padding: Spacing.lg,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: Spacing.lg,
  },
  headerContent: {
    flex: 1,
    alignItems: "center",
  },
  errorIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Spacing.md,
  },
  title: {
    ...Typography.h3,
    fontWeight: "600",
    textAlign: "center",
  },
  messageSection: {
    marginBottom: Spacing.lg,
  },
  message: {
    ...Typography.body,
    textAlign: "center",
    lineHeight: 22,
  },
  actionsSection: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.md,
  },
  actionButton: {
    flex: 1,
    minWidth: "100%",
  },
  halfWidthButton: {
    minWidth: "45%",
    flex: 0,
  },
});
