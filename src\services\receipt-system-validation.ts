/**
 * Receipt System Validation Script
 * 
 * This script validates that the unified receipt system is properly integrated
 * across all components and that legacy systems are no longer in use.
 */

import { UnifiedReceiptManager } from './UnifiedReceiptManager';
import { StandardizedReceiptService } from './StandardizedReceiptService';

export class ReceiptSystemValidator {
  
  /**
   * Validate that UnifiedReceiptManager is available and functional
   */
  static async validateUnifiedSystem(): Promise<{
    success: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if UnifiedReceiptManager is available
      if (!UnifiedReceiptManager) {
        errors.push('UnifiedReceiptManager is not available');
        return { success: false, errors, warnings };
      }

      // Check if StandardizedReceiptService is available
      if (!StandardizedReceiptService) {
        errors.push('StandardizedReceiptService is not available');
      }

      // Validate UnifiedReceiptManager has required methods
      const requiredMethods = ['generateReceipt'];
      for (const method of requiredMethods) {
        if (typeof UnifiedReceiptManager[method] !== 'function') {
          errors.push(`UnifiedReceiptManager.${method} is not a function`);
        }
      }

      // Validate StandardizedReceiptService has required methods
      const requiredServiceMethods = ['generateStandardizedReceipt'];
      for (const method of requiredServiceMethods) {
        if (typeof StandardizedReceiptService[method] !== 'function') {
          errors.push(`StandardizedReceiptService.${method} is not a function`);
        }
      }

      console.log('✅ Receipt system validation completed');
      console.log(`Errors: ${errors.length}, Warnings: ${warnings.length}`);

      return {
        success: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      errors.push(`Validation error: ${error.message}`);
      return { success: false, errors, warnings };
    }
  }

  /**
   * Test receipt generation with sample data
   */
  static async testReceiptGeneration(): Promise<{
    success: boolean;
    results: any[];
    errors: string[];
  }> {
    const results: any[] = [];
    const errors: string[] = [];

    try {
      // Sample order data for testing
      const sampleOrder = {
        id: 'test-order-123',
        orderNumber: 'TEST-001',
        totalPrice: '150.00',
        createdAt: new Date().toISOString(),
        customer: {
          id: 'test-customer-1',
          firstName: 'Test',
          lastName: 'Customer',
          email: '<EMAIL>',
          phone: '+254700000000',
          loyaltyData: {
            loyaltyPoints: 100,
            tier: 'bronze'
          }
        },
        lineItems: [
          {
            id: 'item-1',
            title: 'Test Product',
            quantity: 2,
            price: '50.00',
            sku: 'TEST-SKU-001',
            discount: {
              type: 'percentage',
              value: 10,
              amount: '10.00'
            }
          }
        ],
        shippingData: {
          includeShipping: true,
          shippingFee: 20.00,
          deliveryMethod: 'Standard Delivery'
        },
        paymentMethod: 'M-Pesa',
        staff: {
          id: 'staff-1',
          name: 'Test Staff'
        },
        salesAgent: {
          id: 'agent-1',
          name: 'Test Agent'
        }
      };

      // Test thermal receipt generation
      console.log('🧪 Testing thermal receipt generation...');
      const thermalResult = await UnifiedReceiptManager.generateReceipt(sampleOrder, {
        format: 'thermal',
        autoPrint: false, // Don't actually print during testing
        printerType: 'thermal'
      });

      results.push({
        test: 'thermal_receipt_generation',
        success: thermalResult.success,
        data: thermalResult
      });

      if (!thermalResult.success) {
        errors.push(`Thermal receipt generation failed: ${thermalResult.error}`);
      }

      // Test web receipt generation
      console.log('🧪 Testing web receipt generation...');
      const webResult = await UnifiedReceiptManager.generateReceipt(sampleOrder, {
        format: 'web',
        autoPrint: false, // Don't actually print during testing
        printerType: 'web'
      });

      results.push({
        test: 'web_receipt_generation',
        success: webResult.success,
        data: webResult
      });

      if (!webResult.success) {
        errors.push(`Web receipt generation failed: ${webResult.error}`);
      }

      // Test standardized receipt data generation
      console.log('🧪 Testing standardized receipt data generation...');
      const standardizedData = await StandardizedReceiptService.generateStandardizedReceipt(
        sampleOrder
      );

      results.push({
        test: 'standardized_data_generation',
        success: !!standardizedData,
        data: standardizedData
      });

      // Validate that critical data is present in standardized receipt
      if (standardizedData) {
        const criticalFields = [
          'header.storeName',
          'header.storePhone',
          'customer.name',
          'items',
          'totals.subtotal',
          'totals.grandTotal'
        ];

        for (const field of criticalFields) {
          const fieldValue = this.getNestedValue(standardizedData, field);
          if (!fieldValue) {
            errors.push(`Missing critical field in standardized receipt: ${field}`);
          }
        }

        // Check for discount data
        if (standardizedData.items && standardizedData.items.length > 0) {
          const hasDiscountData = standardizedData.items.some(item => item.discount);
          if (!hasDiscountData) {
            errors.push('No discount data found in standardized receipt items');
          }
        }

        // Check for loyalty data
        if (!standardizedData.loyalty || !standardizedData.loyalty.membershipId) {
          errors.push('Missing loyalty data in standardized receipt');
        }

        // Check for shipping data
        if (!standardizedData.fulfillment || !standardizedData.fulfillment.shippingFee) {
          errors.push('Missing shipping data in standardized receipt');
        }
      }

      console.log('✅ Receipt generation testing completed');
      console.log(`Tests run: ${results.length}, Errors: ${errors.length}`);

      return {
        success: errors.length === 0,
        results,
        errors
      };

    } catch (error) {
      errors.push(`Testing error: ${error.message}`);
      return { success: false, results, errors };
    }
  }

  /**
   * Helper method to get nested object values
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Run complete validation suite
   */
  static async runCompleteValidation(): Promise<{
    systemValidation: any;
    generationTesting: any;
    overallSuccess: boolean;
  }> {
    console.log('🚀 Starting complete receipt system validation...');

    const systemValidation = await this.validateUnifiedSystem();
    const generationTesting = await this.testReceiptGeneration();

    const overallSuccess = systemValidation.success && generationTesting.success;

    console.log('📊 Validation Summary:');
    console.log(`System Validation: ${systemValidation.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Generation Testing: ${generationTesting.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Overall Result: ${overallSuccess ? '✅ PASS' : '❌ FAIL'}`);

    if (!overallSuccess) {
      console.log('❌ Validation Errors:');
      [...systemValidation.errors, ...generationTesting.errors].forEach(error => {
        console.log(`  - ${error}`);
      });
    }

    return {
      systemValidation,
      generationTesting,
      overallSuccess
    };
  }
}

// Export validation function for easy use
export const validateReceiptSystem = () => ReceiptSystemValidator.runCompleteValidation();
