import { BorderRadius, Spacing, Typography } from "@/constants/Design";
import React from "react";
import { StyleSheet } from "react-native";
import { ModernCard } from "./ModernCard";

interface GridCardProps {
  title: string;
  subtitle?: string;
  icon: React.ReactNode;
  onPress: () => void;
  badge?: string | number;
  disabled?: boolean;
}

export function GridCard({
  title,
  subtitle,
  icon,
  onPress,
  badge,
  disabled = false,
}: GridCardProps) {
  // Import themed components inline to avoid auto-formatting issues
  const {
    ThemedView,
    ThemedText,
    ThemedBadge,
  } = require("@/src/components/themed/ThemedComponents");
  const {
    FadeInView,
  } = require("@/src/components/animated/AnimatedComponents");

  return (
    <FadeInView>
      <ModernCard
        onPress={onPress}
        style={styles.card}
        variant="elevated"
        disabled={disabled}
      >
        <ThemedView style={styles.content}>
          <ThemedView style={styles.header}>
            <ThemedView style={styles.iconContainer}>{icon}</ThemedView>
            {badge && (
              <ThemedBadge
                text={badge.toString()}
                variant="primary"
                size="small"
              />
            )}
          </ThemedView>

          <ThemedView style={styles.textContainer}>
            <ThemedText
              variant="bodyMedium"
              style={styles.title}
              numberOfLines={2}
            >
              {title}
            </ThemedText>
            {subtitle && (
              <ThemedText
                variant="caption"
                color="secondary"
                style={styles.subtitle}
                numberOfLines={1}
              >
                {subtitle}
              </ThemedText>
            )}
          </ThemedView>
        </ThemedView>
      </ModernCard>
    </FadeInView>
  );
}

const styles = StyleSheet.create({
  card: {
    flex: 1,
    minHeight: 140,
    margin: Spacing.sm,
  },
  content: {
    flex: 1,
    justifyContent: "space-between",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: Spacing.md,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.sm,
    backgroundColor: "rgba(212, 175, 55, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  badge: {
    borderRadius: 12,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: "center",
  },
  badgeText: {
    ...Typography.small,
    color: "#1A1A1A",
    fontWeight: "600",
  },
  textContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  title: {
    ...Typography.bodyMedium,
    marginBottom: 4,
  },
  subtitle: {
    ...Typography.caption,
  },
});
