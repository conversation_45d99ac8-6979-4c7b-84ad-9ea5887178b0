const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// In-memory storage for demo (use database in production)
let accessTokens = new Map();
let sessions = new Map();

// Shopify OAuth configuration
const SHOPIFY_CONFIG = {
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecret: process.env.SHOPIFY_API_SECRET,
  scopes: 'read_products,write_orders,read_customers,write_customers,read_inventory,write_inventory',
  redirectUri: `${process.env.SHOPIFY_APP_URL}/api/shopify/callback`,
};

// Generate OAuth URL for store setup
router.post('/auth-url', async (req, res) => {
  try {
    const { shopDomain } = req.body;
    
    if (!shopDomain) {
      return res.status(400).json({
        success: false,
        error: 'Shop domain is required'
      });
    }

    // Generate state for security
    const state = crypto.randomBytes(32).toString('hex');
    const timestamp = Date.now();
    
    // Store session
    sessions.set(state, { 
      shop: shopDomain, 
      timestamp,
      expires: timestamp + (10 * 60 * 1000) // 10 minutes
    });

    // Build OAuth URL
    const authUrl = `https://${shopDomain}/admin/oauth/authorize?` + 
      `client_id=${SHOPIFY_CONFIG.apiKey}&` +
      `scope=${SHOPIFY_CONFIG.scopes}&` +
      `redirect_uri=${encodeURIComponent(SHOPIFY_CONFIG.redirectUri)}&` +
      `state=${state}`;

    res.json({
      success: true,
      data: {
        authUrl,
        state
      }
    });

  } catch (error) {
    console.error('OAuth URL generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate OAuth URL'
    });
  }
});

// Handle OAuth callback
router.get('/callback', async (req, res) => {
  try {
    const { code, state, shop } = req.query;

    if (!code || !state) {
      return res.status(400).send('Missing authorization code or state');
    }

    // Verify state
    const session = sessions.get(state);
    if (!session || session.expires < Date.now()) {
      return res.status(400).send('Invalid or expired state');
    }

    // Exchange code for access token
    const tokenResponse = await axios.post(`https://${shop}/admin/oauth/access_token`, {
      client_id: SHOPIFY_CONFIG.apiKey,
      client_secret: SHOPIFY_CONFIG.apiSecret,
      code: code
    });

    const { access_token, scope } = tokenResponse.data;

    // Store access token
    accessTokens.set(shop, {
      token: access_token,
      scope: scope,
      shop: shop,
      createdAt: new Date().toISOString()
    });

    // Update .env file with access token (for demo purposes)
    updateEnvFile('SHOPIFY_ACCESS_TOKEN', access_token);

    // Clean up session
    sessions.delete(state);

    console.log(`✅ OAuth completed for ${shop}`);
    console.log(`✅ Access token stored: ${access_token.substring(0, 10)}...`);

    // Redirect to success page or close window
    res.send(`
      <html>
        <head><title>Authorization Complete</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1 style="color: #2ecc71;">✅ Store Connected Successfully!</h1>
          <p>Your Shopify store has been connected to Dukalink POS.</p>
          <p><strong>Store:</strong> ${shop}</p>
          <p><strong>Scopes:</strong> ${scope}</p>
          <p>You can now close this window and use the POS system.</p>
          <script>
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
      </html>
    `);

  } catch (error) {
    console.error('OAuth callback error:', error);
    res.status(500).send(`
      <html>
        <head><title>Authorization Failed</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1 style="color: #e74c3c;">❌ Authorization Failed</h1>
          <p>There was an error connecting your store.</p>
          <p>Error: ${error.message}</p>
          <p>Please try again.</p>
        </body>
      </html>
    `);
  }
});

// Get current connection status
router.get('/status', (req, res) => {
  const shopDomain = process.env.SHOPIFY_SHOP_DOMAIN;
  const hasToken = accessTokens.has(shopDomain) || !!process.env.SHOPIFY_ACCESS_TOKEN;
  
  if (hasToken) {
    const tokenInfo = accessTokens.get(shopDomain);
    res.json({
      success: true,
      data: {
        connected: true,
        shop: shopDomain,
        connectedAt: tokenInfo?.createdAt || 'Unknown',
        scopes: tokenInfo?.scope || 'Unknown'
      }
    });
  } else {
    res.json({
      success: true,
      data: {
        connected: false,
        shop: shopDomain
      }
    });
  }
});

// Test Shopify connection
router.get('/test', async (req, res) => {
  try {
    const shopDomain = process.env.SHOPIFY_SHOP_DOMAIN;
    const tokenInfo = accessTokens.get(shopDomain);
    const accessToken = tokenInfo?.token || process.env.SHOPIFY_ACCESS_TOKEN;

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'No access token available. Please complete OAuth first.'
      });
    }

    // Test API call
    const response = await axios.get(`https://${shopDomain}/admin/api/2023-10/shop.json`, {
      headers: {
        'X-Shopify-Access-Token': accessToken
      }
    });

    res.json({
      success: true,
      data: {
        shop: response.data.shop,
        message: 'Shopify connection successful!'
      }
    });

  } catch (error) {
    console.error('Shopify test error:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: error.response?.data?.errors || error.message
    });
  }
});

// Helper function to update .env file
function updateEnvFile(key, value) {
  try {
    const envPath = path.join(__dirname, '../../.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, `${key}=${value}`);
    } else {
      envContent += `\n${key}=${value}`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log(`✅ Updated ${key} in .env file`);
  } catch (error) {
    console.error('Error updating .env file:', error);
  }
}

module.exports = router;
