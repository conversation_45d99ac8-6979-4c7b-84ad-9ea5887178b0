#!/usr/bin/env node

/**
 * Sales Agent Auto-Selection Bug Fix Test
 *
 * This script verifies that the sales agent auto-selection bug has been fixed
 * and that no sales agent is automatically selected on app startup.
 */

const fs = require("fs");
const path = require("path");

console.log("🔍 Testing Sales Agent Auto-Selection Fix...\n");

const tests = [
  {
    name: "SalesAgentContext Auto-Loading Disabled",
    checks: [
      {
        type: "file_content",
        path: "src/contexts/SalesAgentContext.tsx",
        not_contains: [
          "useEffect(() => {\n    loadSelectedAgent();\n  }, []);",
          "loadSelectedAgent();",
        ],
        contains: [
          "// Clear any previously selected agent on app start to prevent auto-selection",
          "// Users should explicitly select sales agents for each transaction",
        ],
        description: "Auto-loading of stored agent should be disabled",
      },
    ],
  },
  {
    name: "SalesAgentContext Auto-Clearing Enabled",
    checks: [
      {
        type: "file_content",
        path: "src/contexts/SalesAgentContext.tsx",
        contains: [
          "Clear any previously selected agent on app start to prevent auto-selection",
          "clearStoredAgent",
          "setSelectedAgentState(null)",
          "AsyncStorage.removeItem(STORAGE_KEY)",
        ],
        description:
          "Auto-clearing of stored agent should be enabled on startup",
      },
    ],
  },
  {
    name: "Checkout Screen Cleanup Preserved",
    checks: [
      {
        type: "file_content",
        path: "app/checkout.tsx",
        contains: ["clearSelectedAgent", "clearSelectedCustomer"],
        description:
          "Checkout should still clear agents after order completion",
      },
    ],
  },
  {
    name: "SalesAgentContext Storage Key Defined",
    checks: [
      {
        type: "file_content",
        path: "src/contexts/SalesAgentContext.tsx",
        contains: ["STORAGE_KEY", "@dukalink_selected_sales_agent"],
        description: "Storage key should be properly defined",
      },
    ],
  },
  {
    name: "SalesAgentContext State Management",
    checks: [
      {
        type: "file_content",
        path: "src/contexts/SalesAgentContext.tsx",
        contains: [
          "const [selectedAgent, setSelectedAgentState] = useState<SalesAgent | null>(",
          "null",
          "setSelectedAgentState",
        ],
        description: "State should be properly initialized as null",
      },
    ],
  },
  {
    name: "SalesAgentContext Clear Function",
    checks: [
      {
        type: "file_content",
        path: "src/contexts/SalesAgentContext.tsx",
        contains: [
          "const clearSelectedAgent",
          "setSelectedAgentState(null)",
          "await AsyncStorage.removeItem(STORAGE_KEY)",
        ],
        description: "Clear function should properly reset state and storage",
      },
    ],
  },
  {
    name: "SalesAgentContext Set Function",
    checks: [
      {
        type: "file_content",
        path: "src/contexts/SalesAgentContext.tsx",
        contains: [
          "const setSelectedAgent",
          "setSelectedAgentState(agent)",
          "await AsyncStorage.setItem(STORAGE_KEY",
        ],
        description: "Set function should properly update state and storage",
      },
    ],
  },
  {
    name: "No Auto-Selection in Other Components",
    checks: [
      {
        type: "file_content",
        path: "app/checkout.tsx",
        not_contains: [
          "setSelectedAgent(salesAgents[0])",
          "setSelectedAgent(agents[0])",
          "auto-select",
          "default agent",
        ],
        description: "Checkout should not auto-select first agent from list",
      },
    ],
  },
];

let passedTests = 0;
let totalTests = 0;
let issues = [];

function runTest(test) {
  console.log(`📋 Testing: ${test.name}`);

  test.checks.forEach((check) => {
    totalTests++;

    try {
      switch (check.type) {
        case "file_content":
          const filePath = path.join(process.cwd(), check.path);
          if (!fs.existsSync(filePath)) {
            console.log(`   ❌ ${check.description} - File not found`);
            issues.push(`File not found: ${check.path}`);
            break;
          }

          const content = fs.readFileSync(filePath, "utf8");
          let contentPassed = true;

          // Check for required content
          if (check.contains) {
            check.contains.forEach((item) => {
              if (!content.includes(item)) {
                console.log(`   ❌ ${check.description} - Missing: ${item}`);
                issues.push(`Missing in ${check.path}: ${item}`);
                contentPassed = false;
              }
            });
          }

          // Check for content that should not exist
          if (check.not_contains) {
            check.not_contains.forEach((item) => {
              if (content.includes(item)) {
                console.log(
                  `   ❌ ${check.description} - Should not contain: ${item}`
                );
                issues.push(`Should not contain in ${check.path}: ${item}`);
                contentPassed = false;
              }
            });
          }

          if (contentPassed) {
            console.log(`   ✅ ${check.description}`);
            passedTests++;
          }
          break;

        default:
          console.log(`   ❌ Unknown test type: ${check.type}`);
          issues.push(`Unknown test type: ${check.type}`);
      }
    } catch (error) {
      console.log(`   ❌ ${check.description} - Error: ${error.message}`);
      issues.push(`Error in ${check.path}: ${error.message}`);
    }
  });

  console.log("");
}

// Run all tests
tests.forEach(runTest);

// Summary
console.log("📊 Sales Agent Auto-Selection Test Summary:");
console.log(`   Passed: ${passedTests}/${totalTests}`);
console.log(
  `   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`
);

if (passedTests === totalTests) {
  console.log("\n🎉 All sales agent auto-selection tests passed!");
  console.log("\n✅ **Sales Agent Auto-Selection Bug FIXED:**");
  console.log("   • Auto-loading from storage disabled on app startup");
  console.log("   • Auto-clearing implemented to remove stored selections");
  console.log("   • No default agent selection behavior");
  console.log("   • Explicit user selection required for each transaction");

  console.log("\n🔄 **Behavior Changes Verified:**");
  console.log("   • App startup: Clears any stored agent selection");
  console.log("   • User experience: No agent pre-selected");
  console.log("   • Transaction flow: User must explicitly choose agent");
  console.log("   • Order completion: Agent selection properly cleared");

  console.log("\n🚀 **Sales Agent Auto-Selection Issue RESOLVED:**");
  console.log("   • No more confusing auto-selected agents");
  console.log("   • Clean slate for each transaction");
  console.log("   • Proper user control over agent selection");
  console.log("   • Maintained all existing functionality");
} else {
  console.log(
    "\n⚠️  Some sales agent auto-selection tests failed. Issues found:"
  );
  issues.forEach((issue) => {
    console.log(`   • ${issue}`);
  });

  console.log("\n🔧 **Next Steps:**");
  console.log("   1. Review and fix the failing tests");
  console.log("   2. Ensure auto-loading is properly disabled");
  console.log("   3. Verify auto-clearing is implemented correctly");
  console.log("   4. Re-run tests after fixes");
}

console.log("\n📚 **Sales Agent Fix Summary:**");
console.log("   🗑️  Removed: Auto-loading of stored agent on startup");
console.log("   ➕ Added: Auto-clearing of stored agent on startup");
console.log("   ✅ Preserved: Order completion cleanup functionality");
console.log("   🎯 Result: No auto-selection, explicit user choice required");
