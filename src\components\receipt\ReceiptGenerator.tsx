import * as Print from "expo-print";
import * as Sharing from "expo-sharing";
import { Alert } from "react-native";
import EnhancedThermalPrintService from "../../services/EnhancedThermalPrintService";
import ShopService from "../../services/shop-service";
import BluetoothPermissionHelper from "../../utils/BluetoothPermissionHelper";

export interface OrderData {
  id: string;
  orderNumber?: string;
  number?: string;
  name?: string;
  totalPrice?: string;
  total_price?: string;
  createdAt?: string;
  created_at?: string;
  financialStatus?: string;
  financial_status?: string;
  fulfillmentStatus?: string;
  fulfillment_status?: string;
  salespersonName?: string;
  salespersonId?: string;
  paymentMethod?: string;
  paymentTransactionId?: string;
  lineItems?: any[];
  line_items?: any[];
  customer?: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
  };
  // ✅ CRITICAL FIX: Add shipping fields for fee extraction
  shipping_lines?: Array<{
    title: string;
    price: string;
    code?: string;
  }>;
  shippingData?: {
    includeShipping?: boolean;
    shippingFee?: number;
    deliveryMethod?: string;
  };
}

export interface PaymentMethodDetail {
  id: string;
  type: string;
  name: string;
  amount: number;
  status: string;
  processedAt?: string;
  metadata?: {
    transactionCode?: string;
    referenceCode?: string;
    mpesaReceiptNumber?: string;
    absaReceiptNumber?: string;
    tillNumber?: string;
    cardType?: string;
    lastFourDigits?: string;
    authorizationCode?: string;
    amountTendered?: number;
    change?: number;
    dueDate?: string;
    paymentTerms?: string;
    creditLimit?: number;
    outstandingBalance?: number;
    phoneNumber?: string;
    customerName?: string;
    [key: string]: any;
  };
}

export interface PaymentBreakdown {
  isSplitPayment: boolean;
  totalAmount: number;
  paymentMethods: PaymentMethodDetail[];
  transactionId: string;
  paymentStatus: string;
  completedAmount?: number;
  pendingAmount?: number;
  remainingAmount?: number;
}

export interface ReceiptData {
  orderNumber: string;
  orderDate: string;
  customer: {
    name: string;
    email?: string;
    phone?: string;
  };
  staff: {
    name: string;
    role: string;
  };
  salesAgent?: {
    name: string;
    id?: string;
  };
  items: {
    id: string;
    title: string;
    quantity: number;
    price: string;
    variantTitle?: string;
    discount?: {
      type: "percentage" | "fixed_amount";
      amount: number;
    };
  }[];
  subtotal: number;
  tax: number;
  total: number;
  paymentMethod: string;
  paymentDetails?: {
    transactionId?: string;
  };
  // Enhanced payment breakdown
  paymentBreakdown?: PaymentBreakdown;
  loyaltyPoints?: {
    earned: number;
    balance: number;
  };
  store: {
    name: string;
    address?: string;
    phone?: string;
  };
  // ✅ CRITICAL FIX: Include original order data for shipping fee extraction
  orderData?: OrderData;
}

// Loyalty completion data interface for receipt generation
export interface LoyaltyCompletionData {
  pointsEarned: number;
  newBalance: number;
  membershipId: string;
  tierStatus?: {
    current: string;
    changed: boolean;
    previous?: string;
  };
}

export class ReceiptGenerator {
  // Convert order data to receipt format
  static async generateReceiptData(
    order: OrderData,
    forceRefreshShopInfo: boolean = false,
    loyaltyCompletionData?: LoyaltyCompletionData
  ): Promise<ReceiptData> {
    // Receipt generation from order data

    const lineItems = order.lineItems || order.line_items || [];
    const orderNumber =
      order.orderNumber || order.number || order.name || "N/A";
    const totalPrice = parseFloat(order.totalPrice || order.total_price || "0");
    const createdAt =
      order.createdAt || order.created_at || new Date().toISOString();

    // Get shop information from Shopify
    const shopInfo = await ShopService.getShopInfo(forceRefreshShopInfo);
    const formattedAddress = await ShopService.getFormattedStoreAddress(
      forceRefreshShopInfo
    );

    // Fetch enhanced payment details if transaction ID is available
    let paymentBreakdown: PaymentBreakdown | undefined;
    const transactionId = order.paymentTransactionId;

    if (transactionId) {
      try {
        paymentBreakdown = await this.fetchPaymentBreakdown(transactionId);
      } catch (error) {
        console.warn("Failed to fetch payment breakdown:", error);
        // Continue without payment breakdown if fetch fails
      }
    }

    // Process loyalty points data - use real data if available, fallback to calculated estimate
    let loyaltyPoints:
      | { earned: number; balance: number; membershipId: string }
      | undefined;

    if (loyaltyCompletionData) {
      // Use real loyalty completion data from order processing
      loyaltyPoints = {
        earned: loyaltyCompletionData.pointsEarned,
        balance: loyaltyCompletionData.newBalance,
        membershipId:
          loyaltyCompletionData.membershipId ||
          `TS${(order.customer as any)?.id?.toString().slice(-8) || "GUEST"}`,
      };
      console.log(
        "✅ Using real loyalty completion data for receipt:",
        loyaltyPoints
      );
      console.log(
        "🔍 Loyalty completion data structure:",
        JSON.stringify(loyaltyCompletionData, null, 2)
      );
    } else if (order.customer) {
      // Enhanced loyalty data extraction with fallback to note field
      const { getEffectiveLoyaltyPoints } = require("@/src/utils/loyaltyUtils");
      const pointsEarned = Math.floor(totalPrice / 100); // Estimated points for this transaction
      const effectiveBalance = getEffectiveLoyaltyPoints(order.customer);

      loyaltyPoints = {
        earned: pointsEarned,
        balance: effectiveBalance, // Use effective balance (includes fallback to note field)
        membershipId: `TS${
          (order.customer as any)?.id?.toString().slice(-8) || "GUEST"
        }`,
      };

      console.log("✅ Enhanced loyalty data extraction for receipt:", {
        customerId: (order.customer as any)?.id,
        customerName: (order.customer as any)?.displayName,
        loyaltyDataPoints:
          (order.customer as any)?.loyaltyData?.loyaltyPoints || 0,
        noteField: (order.customer as any)?.note,
        effectiveBalance: effectiveBalance,
        tier: (order.customer as any)?.loyaltyData?.tier || "bronze",
        estimatedEarned: pointsEarned,
        finalLoyaltyPoints: loyaltyPoints,
      });
    }
    // If no customer, loyaltyPoints remains undefined and won't be shown on receipt

    // Handle different customer field formats from Shopify API
    const getCustomerName = () => {
      if (!order.customer) return "Walk-in Customer";

      // Handle different field name formats
      const firstName =
        order.customer.firstName || (order.customer as any).first_name || "";
      const lastName =
        order.customer.lastName || (order.customer as any).last_name || "";
      const displayName = (order.customer as any).displayName;

      // Use displayName if available, otherwise construct from first/last name
      if (displayName && displayName.trim()) {
        return displayName.trim();
      }

      const fullName = `${firstName} ${lastName}`.trim();
      return fullName || order.customer.email || "Walk-in Customer";
    };

    const receiptData: ReceiptData = {
      orderNumber,
      orderDate: createdAt,
      customer: {
        name: getCustomerName(),
        email: order.customer?.email,
        phone: order.customer?.phone,
      },
      staff: {
        name: order.salespersonName || (order as any).staffName || "POS Staff",
        role: "Staff",
      },
      salesAgent:
        order.salespersonId || (order as any).salesAgentId
          ? {
              name:
                order.salespersonName ||
                (order as any).salesAgentName ||
                "Sales Agent",
              id: order.salespersonId || (order as any).salesAgentId,
            }
          : undefined,
      items: lineItems.map((item: any) => ({
        id: item.id || "",
        title: item.title || item.name || "Unknown Item",
        quantity: item.quantity || 1,
        price: item.price || item.variant?.price || "0.00",
        variantTitle:
          item.variantTitle !== "Default Title" &&
          item.variantTitle !== "Default"
            ? item.variantTitle
            : item.variant?.title !== "Default Title" &&
              item.variant?.title !== "Default"
            ? item.variant?.title
            : undefined,
        discount: item.discount || undefined,
      })),
      subtotal: totalPrice, // No tax calculations - show total as subtotal
      tax: 0, // Remove tax calculations
      total: totalPrice,
      paymentMethod: order.paymentMethod || "Cash",
      paymentDetails: {
        transactionId: order.paymentTransactionId || `ORDER-${orderNumber}`,
      },
      // Enhanced payment breakdown
      paymentBreakdown,
      loyaltyPoints,
      store: {
        name: shopInfo.name,
        address: formattedAddress,
        phone: shopInfo.phone,
      },
      // ✅ CRITICAL FIX: Include original order data for shipping fee extraction
      orderData: order,
    };

    // CRITICAL DEBUGGING: Track loyalty data in final ReceiptData
    console.log("🔍 LOYALTY DATA FLOW TRACE - ReceiptGenerator Output:", {
      hasLoyaltyPoints: !!loyaltyPoints,
      loyaltyPointsData: loyaltyPoints,
      loyaltyBalance: loyaltyPoints?.balance,
      loyaltyEarned: loyaltyPoints?.earned,
      loyaltyMembershipId: loyaltyPoints?.membershipId,
      source: loyaltyCompletionData
        ? "BACKEND_COMPLETION_DATA"
        : "FALLBACK_EXTRACTION",
      backendNewBalance: loyaltyCompletionData?.newBalance,
    });

    return receiptData;
  }

  /**
   * Fetch detailed payment breakdown from enhanced payment system
   */
  private static async fetchPaymentBreakdown(
    transactionId: string
  ): Promise<PaymentBreakdown | undefined> {
    try {
      // Import enhanced payment service
      const { enhancedPaymentService } = await import(
        "../../services/enhanced-payment-service"
      );

      // Get transaction status and details
      const statusResponse = await enhancedPaymentService.getTransactionStatus(
        transactionId
      );

      if (!statusResponse.success || !statusResponse.data) {
        console.warn(
          "Failed to fetch transaction status:",
          statusResponse.error
        );
        return undefined;
      }

      const transaction = statusResponse.data.transaction;
      const paymentMethods = statusResponse.data.paymentMethods || [];

      // Check if this is a split payment
      const isSplitPayment =
        transaction.is_split_payment || paymentMethods.length > 1;

      // Map payment methods to detailed format
      const mappedPaymentMethods: PaymentMethodDetail[] = paymentMethods.map(
        (method: any) => ({
          id: method.id,
          type: method.method_type,
          name: method.method_name,
          amount: parseFloat(method.amount),
          status: method.status,
          processedAt: method.processed_at,
          metadata: method.metadata || {},
        })
      );

      console.log("🔍 Transaction data structure in ReceiptGenerator:", {
        keys: Object.keys(transaction),
        hasCompletedAmount: "completed_amount" in transaction,
        hasCompletedAmountCamel: "completedAmount" in transaction,
        completedAmount: transaction.completedAmount,
        completed_amount: transaction.completed_amount,
      });

      return {
        isSplitPayment,
        totalAmount: parseFloat(
          transaction.total_amount || transaction.totalAmount || "0"
        ),
        paymentMethods: mappedPaymentMethods,
        transactionId: transaction.id || transaction.transactionId,
        paymentStatus: transaction.payment_status || transaction.status,
        completedAmount: parseFloat(
          transaction.completedAmount || transaction.completed_amount || "0"
        ),
        pendingAmount: parseFloat(
          transaction.pendingAmount || transaction.pending_amount || "0"
        ),
        remainingAmount: parseFloat(
          transaction.remainingAmount || transaction.remaining_amount || "0"
        ),
      };
    } catch (error) {
      console.error("Error fetching payment breakdown:", error);
      return undefined;
    }
  }

  // Generate HTML receipt for printing with universal template support
  static generateReceiptHTML(
    receiptData: ReceiptData,
    useUniversalTemplate: boolean = true
  ): string {
    // Try universal template first for better device compatibility
    if (useUniversalTemplate) {
      try {
        const {
          UniversalReceiptTemplate,
        } = require("@/src/services/UniversalReceiptTemplate");

        // Convert ReceiptData to UniversalReceiptTemplate format
        // ✅ CRITICAL FIX: Extract shipping fee for proper breakdown
        const shippingFee = this.extractShippingFeeFromReceiptData(receiptData);
        const subtotalAmount = receiptData.total - shippingFee;

        const templateData = {
          storeName: receiptData.store.name,
          storeAddress:
            receiptData.store.address || "Greenhouse Mall, Ngong Road",
          storePhone: "+254 111 443 993" /**receiptData.store.phone || */,
          storeEmail: "<EMAIL>",
          storeLogo: require("@/assets/images/treasured-logo.jpg"), // Include Treasured Scents logo

          receiptNumber: receiptData.orderNumber,
          date: new Date(receiptData.orderDate).toLocaleDateString(),
          time: new Date(receiptData.orderDate).toLocaleTimeString(),
          staffName: receiptData.staff.name,

          customerName: receiptData.customer.name,
          customerPhone: receiptData.customer.phone,

          items: receiptData.items.map((item: any, index: number) => {
            const quantity = item.quantity;
            const unitPrice = parseFloat(item.price);
            const lineTotal = quantity * unitPrice;

            // ✅ CRITICAL FIX: Calculate discount information for template
            let discount = null;
            if (item.discount && item.discount.amount > 0) {
              let discountAmount = 0;
              if (item.discount.type === "percentage") {
                discountAmount = (lineTotal * item.discount.amount) / 100;
              } else {
                discountAmount = Math.min(item.discount.amount, lineTotal);
              }

              discount = {
                type: item.discount.type,
                amount: item.discount.amount,
                discountAmount: discountAmount,
              };
            }

            return {
              number: index + 1,
              name: item.title,
              variant: item.variantTitle,
              quantity: quantity,
              unitPrice: unitPrice,
              totalPrice: discount
                ? lineTotal - discount.discountAmount
                : lineTotal,
              // ✅ CRITICAL FIX: Include discount information
              discount: discount,
            };
          }),

          subtotal: subtotalAmount, // ✅ FIXED: Use calculated subtotal (total - shipping)
          // ✅ CRITICAL FIX: Calculate total discount from all items
          totalDiscount: receiptData.items.reduce(
            (total: number, item: any) => {
              if (item.discount && item.discount.amount > 0) {
                const lineTotal = item.quantity * parseFloat(item.price);
                let discountAmount = 0;
                if (item.discount.type === "percentage") {
                  discountAmount = (lineTotal * item.discount.amount) / 100;
                } else {
                  discountAmount = Math.min(item.discount.amount, lineTotal);
                }
                return total + discountAmount;
              }
              return total;
            },
            0
          ),
          shippingFee: shippingFee > 0 ? shippingFee : undefined, // ✅ FIXED: Include shipping fee
          grandTotal: receiptData.total,

          paymentMethods: receiptData.paymentBreakdown?.paymentMethods?.map(
            (method) => ({
              method: method.name,
              amount: method.amount,
              transactionCode: method.metadata?.transactionCode,
              phoneNumber: method.metadata?.phoneNumber,
            })
          ) || [
            {
              method: receiptData.paymentMethod,
              amount: receiptData.total,
            },
          ],
          totalPaid: receiptData.total,

          loyalty: receiptData.loyaltyPoints
            ? {
                totalPoints: receiptData.loyaltyPoints.balance,
              }
            : undefined,

          salesAgent: receiptData.salesAgent,
        };

        return UniversalReceiptTemplate.generateHTML(templateData, {
          width: "thermal-80mm",
          deviceType: "thermal",
        });
      } catch (error) {
        console.warn(
          "Universal template failed, falling back to legacy HTML:",
          error
        );
      }
    }

    // Legacy HTML generation
    return this.generateLegacyReceiptHTML(receiptData);
  }

  // Legacy HTML receipt generation (fallback)
  private static generateLegacyReceiptHTML(receiptData: ReceiptData): string {
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return (
        date.toLocaleDateString() +
        " " +
        date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    };

    // Use centralized currency formatting
    const { formatCurrency } = require("@/src/utils/currencyUtils");
    const formatReceiptCurrency = (amount: number) =>
      formatCurrency(amount, { showSymbol: true });

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Receipt - ${receiptData.orderNumber}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            max-width: 300px;
            color: #1a1a1a;
        }
        .receipt {
            text-align: center;
        }
        .store-header {
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .store-logo {
            text-align: center;
            margin-bottom: 8px;
        }
        .logo-image {
            max-width: 140px;
            max-height: 140px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 6px;
            min-width: 120px;
            min-height: 80px;
        }
        .store-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
            letter-spacing: 0.5px;
            color: #2563eb;
        }
        .store-details {
            font-size: 10px;
            margin-bottom: 2px;
        }
        .section {
            margin-bottom: 15px;
            text-align: left;
        }
        .section-title {
            font-weight: 600;
            margin-bottom: 5px;
            border-bottom: 1px solid #ccc;
            font-size: 13px;
            color: #374151;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .item {
            margin-bottom: 12px;
            border-bottom: 1px dotted #ccc;
            padding-bottom: 8px;
        }
        .item-name {
            font-weight: bold;
            margin-bottom: 3px;
            text-align: left;
            font-size: 13px;
            color: #1a1a1a;
            line-height: 1.3;
        }
        .item-details {
            font-size: 10px;
            color: #888;
            text-align: left;
            margin-bottom: 2px;
            font-style: italic;
            line-height: 1.2;
        }
        .item-price-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 3px;
            width: 100%;
            border-bottom: 1px dotted #ddd;
            padding-bottom: 2px;
        }
        .item-left {
            text-align: left;
            flex: 1;
            font-size: 11px;
            color: #666;
            padding-right: 15px;
        }
        .item-right {
            text-align: right;
            font-weight: bold;
            min-width: 80px;
            font-size: 12px;
            color: #1a1a1a;
            white-space: nowrap;
        }
        .totals {
            border-top: 2px solid #000;
            padding-top: 10px;
            margin-top: 15px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .final-total {
            font-weight: 700;
            font-size: 16px;
            border-top: 2px solid #000;
            padding-top: 8px;
            margin-top: 8px;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 10px;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .loyalty-section {
            text-align: center;
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px dashed #007bff;
            border-radius: 5px;
        }
        .loyalty-title {
            font-weight: bold;
            font-size: 14px;
            color: #007bff;
            margin-bottom: 5px;
        }
        .loyalty-details {
            font-size: 12px;
            line-height: 1.4;
        }
        @media print {
            body { margin: 0; padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- Store Header -->
        <div class="store-header">
            <div class="store-logo">
                <img src="${require("@/assets/images/treasured-logo.jpg")}" alt="Treasured Scents" class="logo-image" />
            </div>
            <div class="store-name">${receiptData.store.name}</div>
            ${
              receiptData.store.address
                ? `<div class="store-details">${receiptData.store.address}</div>`
                : ""
            }
            ${
              receiptData.store.phone
                ? `<div class="store-details">Mobile: +254 111 443 993</div>`
                : "+254 111 443 993"
            }
            <div class="store-details">Email: <EMAIL></div>
            <div class="store-details">Website: www.treasuredscents.co.ke</div>
        </div>

        <!-- Transaction Details -->
        <div class="section">
            <div class="item-price-row">
                <div class="item-left">Receipt No:</div>
                <div class="item-right">${receiptData.orderNumber}</div>
            </div>
            <div class="item-price-row">
                <div class="item-left">Date:</div>
                <div class="item-right">${formatDate(
                  receiptData.orderDate
                )}</div>
            </div>
            <div class="item-price-row">
                <div class="item-left">Served by:</div>
                <div class="item-right">${receiptData.staff.name}</div>
            </div>
            ${
              receiptData.salesAgent
                ? `
            <div class="item-price-row">
                <div class="item-left">Sales Agent:</div>
                <div class="item-right">${receiptData.salesAgent.name}</div>
            </div>
            `
                : ""
            }
        </div>

        <!-- Customer Information -->
        <div class="section">
            <div class="section-title">Customer</div>
            <div class="item-price-row">
                <div class="item-left">Customer:</div>
                <div class="item-right">${receiptData.customer.name}</div>
            </div>
            ${
              receiptData.customer.phone
                ? `
            <div class="item-price-row">
                <div class="item-left">Mobile:</div>
                <div class="item-right">${receiptData.customer.phone}</div>
            </div>
            `
                : ""
            }
            ${
              receiptData.customer.email
                ? `
            <div class="item-price-row">
                <div class="item-left">Email:</div>
                <div class="item-right">${receiptData.customer.email}</div>
            </div>
            `
                : ""
            }
        </div>

        <!-- Items -->
        <div class="section">
            <div class="section-title">Items</div>
            ${receiptData.items
              .map(
                (item) => `
            <div class="item">
                <div class="item-name">${item.title}</div>
                ${
                  item.variantTitle
                    ? `<div class="item-details">Variant: ${item.variantTitle}</div>`
                    : ""
                }
                <div class="item-price-row">
                    <div class="item-left">${
                      item.quantity
                    } × ${formatReceiptCurrency(parseFloat(item.price))}</div>
                    <div class="item-right">${formatReceiptCurrency(
                      parseFloat(item.price) * item.quantity
                    )}</div>
                </div>
                ${
                  item.discount && item.discount.amount > 0
                    ? `
                <div class="item-price-row" style="color: #e87c83;">
                    <div class="item-left">Discount (${
                      item.discount.type === "percentage"
                        ? `${item.discount.amount}%`
                        : `${formatReceiptCurrency(item.discount.amount)}`
                    }):</div>
                    <div class="item-right">-${formatReceiptCurrency(
                      item.discount.type === "percentage"
                        ? (parseFloat(item.price) *
                            item.quantity *
                            item.discount.amount) /
                            100
                        : Math.min(
                            item.discount.amount,
                            parseFloat(item.price) * item.quantity
                          )
                    )}</div>
                </div>
                `
                    : ""
                }
            </div>
            `
              )
              .join("")}
        </div>

        <!-- Totals -->
        <div class="totals">
            ${this.generateTotalsBreakdownHTML(receiptData)}
        </div>

        <!-- Payment Information -->
        ${this.generatePaymentDetailsHTML(receiptData)}

        <!-- Loyalty Points Section -->
        ${
          receiptData.loyaltyPoints
            ? `
        <div class="section">
            <div class="section-title">🌟 Loyalty Rewards 🌟</div>
            <div class="item-price-row">
                <div class="item-left">Total TS Points:</div>
                <div class="item-right"><strong>${receiptData.loyaltyPoints.balance}</strong></div>
            </div>
        </div>
        `
            : ""
        }

        <!-- Footer -->
        <div class="footer">
            <div>You're treasured! Thank you</div>
            <div>Powered by Dukalink POS</div>
        </div>
    </div>
</body>
</html>
    `.trim();
  }

  /**
   * Generate totals breakdown HTML with shipping fee extraction
   */
  private static generateTotalsBreakdownHTML(receiptData: ReceiptData): string {
    const { formatCurrency } = require("@/src/utils/currencyUtils");
    const formatReceiptCurrency = (amount: number) =>
      formatCurrency(amount, { showSymbol: true });

    // Extract shipping fee from order data
    const shippingFee = this.extractShippingFeeFromReceiptData(receiptData);
    const subtotal = receiptData.total - shippingFee;

    let totalsHTML = "";

    // ✅ CRITICAL FIX: Calculate total discount from all items
    const totalDiscount = receiptData.items.reduce(
      (total: number, item: any) => {
        if (item.discount && item.discount.amount > 0) {
          const lineTotal = item.quantity * parseFloat(item.price);
          let discountAmount = 0;
          if (item.discount.type === "percentage") {
            discountAmount = (lineTotal * item.discount.amount) / 100;
          } else {
            discountAmount = Math.min(item.discount.amount, lineTotal);
          }
          return total + discountAmount;
        }
        return total;
      },
      0
    );

    // Show subtotal if there's a shipping fee
    if (shippingFee > 0) {
      totalsHTML += `
        <div class="total-row">
            <span>Subtotal:</span>
            <span>${formatReceiptCurrency(subtotal)}</span>
        </div>
        <div class="total-row">
            <span>Delivery Fee:</span>
            <span>${formatReceiptCurrency(shippingFee)}</span>
        </div>
      `;
    }

    // ✅ CRITICAL FIX: Show total discount if there are any discounts
    if (totalDiscount > 0) {
      totalsHTML += `
        <div class="total-row" style="color: #e87c83;">
            <span>Total Discount:</span>
            <span>-${formatReceiptCurrency(totalDiscount)}</span>
        </div>
      `;
    }

    // Grand total
    totalsHTML += `
      <div class="total-row final-total">
          <span>GRAND TOTAL:</span>
          <span>${formatReceiptCurrency(receiptData.total)}</span>
      </div>
    `;

    return totalsHTML;
  }

  /**
   * Extract shipping fee from receipt data
   */
  private static extractShippingFeeFromReceiptData(
    receiptData: ReceiptData
  ): number {
    // Check if order data has shipping information
    const orderData = (receiptData.orderData || receiptData) as any;

    // Priority 1: Check shipping_lines array (Shopify format)
    if (orderData.shipping_lines && Array.isArray(orderData.shipping_lines)) {
      const totalShipping = orderData.shipping_lines.reduce(
        (total: number, line: any) => {
          return total + parseFloat(line.price || "0");
        },
        0
      );
      if (totalShipping > 0) {
        console.log("✅ Found shipping fee in shipping_lines:", totalShipping);
        return totalShipping;
      }
    }

    // Priority 2: Check shippingData (from checkout process)
    if (
      orderData.shippingData?.shippingFee &&
      orderData.shippingData.shippingFee > 0
    ) {
      console.log(
        "✅ Found shipping fee in shippingData:",
        orderData.shippingData.shippingFee
      );
      return orderData.shippingData.shippingFee;
    }

    // Priority 3: Check line items for shipping items
    const lineItems = orderData.lineItems || orderData.line_items || [];
    const shippingLineItem = lineItems.find(
      (item: any) =>
        item.title?.toLowerCase().includes("shipping") ||
        item.title?.toLowerCase().includes("delivery")
    );
    if (shippingLineItem) {
      const shippingFee = parseFloat(shippingLineItem.price || "0");
      console.log("✅ Found shipping fee in line items:", shippingFee);
      return shippingFee;
    }

    console.log("ℹ️ No shipping fee found in receipt data");
    return 0;
  }

  /**
   * Generate totals breakdown text with shipping fee extraction
   */
  private static generateTotalsBreakdownText(
    receiptData: ReceiptData,
    formatCurrency: (amount: number) => string
  ): string {
    // Extract shipping fee from order data
    const shippingFee = this.extractShippingFeeFromReceiptData(receiptData);
    const subtotal = receiptData.total - shippingFee;

    let totalsText = "";

    // Show subtotal if there's a shipping fee
    if (shippingFee > 0) {
      totalsText += `Subtotal: ${formatCurrency(subtotal)}\n`;
      totalsText += `Delivery Fee: ${formatCurrency(shippingFee)}\n`;
      totalsText += `\n`;
    }

    // Grand total
    totalsText += `*GRAND TOTAL: ${formatCurrency(receiptData.total)}*\n`;

    return totalsText;
  }

  /**
   * Generate payment details HTML section with enhanced breakdown for split payments
   */
  private static generatePaymentDetailsHTML(receiptData: ReceiptData): string {
    const { formatCurrency } = require("@/src/utils/currencyUtils");
    const formatReceiptCurrency = (amount: number) =>
      formatCurrency(amount, { showSymbol: true });

    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return (
        date.toLocaleDateString() +
        " " +
        date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    };

    // Check if we have enhanced payment breakdown
    if (
      receiptData.paymentBreakdown &&
      receiptData.paymentBreakdown.isSplitPayment
    ) {
      const breakdown = receiptData.paymentBreakdown;

      return `
        <div class="section">
            <div class="section-title">Payment Details</div>
            <div class="item-price-row">
                <div class="item-left">Payment Type:</div>
                <div class="item-right">Split Payment</div>
            </div>
            <div class="item-price-row">
                <div class="item-left">Transaction ID:</div>
                <div class="item-right">${breakdown.transactionId}</div>
            </div>
            <div class="item-price-row">
                <div class="item-left">Status:</div>
                <div class="item-right">${breakdown.paymentStatus.toUpperCase()}</div>
            </div>
        </div>

        <!-- Split Payment Breakdown -->
        <div class="section">
            <div class="section-title">Payment Breakdown</div>
            ${breakdown.paymentMethods
              .map(
                (method, index) => `
            <div style="margin-bottom: 10px; padding: 8px; background-color: #f8f9fa; border-radius: 4px;">
                <div class="item-price-row">
                    <div class="item-left"><strong>${method.name}</strong></div>
                    <div class="item-right"><strong>${formatReceiptCurrency(
                      method.amount
                    )}</strong></div>
                </div>
                <div class="item-price-row">
                    <div class="item-left">Status:</div>
                    <div class="item-right">${method.status.toUpperCase()}</div>
                </div>
                ${
                  method.processedAt
                    ? `
                <div class="item-price-row">
                    <div class="item-left">Processed:</div>
                    <div class="item-right">${formatDate(
                      method.processedAt
                    )}</div>
                </div>
                `
                    : ""
                }
                ${this.generateMethodSpecificDetailsHTML(
                  method,
                  formatReceiptCurrency
                )}
            </div>
            `
              )
              .join("")}

            <!-- Payment Summary -->
            <div style="margin-top: 10px; padding: 8px; background-color: #e9ecef; border-radius: 4px; border: 1px solid #dee2e6;">
                <div class="item-price-row">
                    <div class="item-left"><strong>Total Paid:</strong></div>
                    <div class="item-right"><strong>${formatReceiptCurrency(
                      breakdown.completedAmount || 0
                    )}</strong></div>
                </div>
                ${
                  breakdown.remainingAmount && breakdown.remainingAmount > 0
                    ? `
                <div class="item-price-row">
                    <div class="item-left">Remaining:</div>
                    <div class="item-right">${formatReceiptCurrency(
                      breakdown.remainingAmount
                    )}</div>
                </div>
                `
                    : ""
                }
            </div>
        </div>
      `;
    } else {
      // Single payment method display
      let paymentDetailsHTML = `
        <div class="section">
            <div class="section-title">Payment Details</div>
            <div class="item-price-row">
                <div class="item-left">Method:</div>
                <div class="item-right">${receiptData.paymentMethod}</div>
            </div>
            ${
              receiptData.paymentDetails?.transactionId
                ? `
            <div class="item-price-row">
                <div class="item-left">Transaction ID:</div>
                <div class="item-right">${receiptData.paymentDetails.transactionId}</div>
            </div>
            `
                : ""
            }
      `;

      // Add single payment method details if available
      if (
        receiptData.paymentBreakdown &&
        receiptData.paymentBreakdown.paymentMethods.length === 1
      ) {
        const method = receiptData.paymentBreakdown.paymentMethods[0];
        paymentDetailsHTML += this.generateMethodSpecificDetailsHTML(
          method,
          formatReceiptCurrency
        );
      }

      paymentDetailsHTML += `</div>`;
      return paymentDetailsHTML;
    }
  }

  /**
   * Generate method-specific payment details HTML
   */
  private static generateMethodSpecificDetailsHTML(
    method: PaymentMethodDetail,
    formatCurrency: (amount: number) => string
  ): string {
    let detailsHTML = "";

    switch (method.type) {
      case "cash":
        if (method.metadata?.amountTendered) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Amount Tendered:</div>
                <div class="item-right">${formatCurrency(
                  method.metadata.amountTendered
                )}</div>
            </div>
          `;
          if (method.metadata.change && method.metadata.change > 0) {
            detailsHTML += `
              <div class="item-price-row">
                  <div class="item-left">Change:</div>
                  <div class="item-right">${formatCurrency(
                    method.metadata.change
                  )}</div>
              </div>
            `;
          }
        }
        break;

      case "mpesa":
        if (method.metadata?.mpesaReceiptNumber) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">M-Pesa Receipt:</div>
                <div class="item-right">${method.metadata.mpesaReceiptNumber}</div>
            </div>
          `;
        }
        if (method.metadata?.transactionCode) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Transaction Code:</div>
                <div class="item-right">${method.metadata.transactionCode}</div>
            </div>
          `;
        }
        if (method.metadata?.phoneNumber) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Phone Number:</div>
                <div class="item-right">${method.metadata.phoneNumber}</div>
            </div>
          `;
        }
        break;

      case "absa_till":
        if (method.metadata?.transactionCode) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Transaction Code:</div>
                <div class="item-right">${method.metadata.transactionCode}</div>
            </div>
          `;
        }
        if (method.metadata?.tillNumber) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Till Number:</div>
                <div class="item-right">${method.metadata.tillNumber}</div>
            </div>
          `;
        }
        if (method.metadata?.absaReceiptNumber) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">ABSA Receipt:</div>
                <div class="item-right">${method.metadata.absaReceiptNumber}</div>
            </div>
          `;
        }
        break;

      case "card":
        if (method.metadata?.cardType) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Card Type:</div>
                <div class="item-right">${method.metadata.cardType}</div>
            </div>
          `;
        }
        if (method.metadata?.lastFourDigits) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Card Number:</div>
                <div class="item-right">****${method.metadata.lastFourDigits}</div>
            </div>
          `;
        }
        if (method.metadata?.authorizationCode) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Auth Code:</div>
                <div class="item-right">${method.metadata.authorizationCode}</div>
            </div>
          `;
        }
        break;

      case "credit":
        if (method.metadata?.dueDate) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Due Date:</div>
                <div class="item-right">${method.metadata.dueDate}</div>
            </div>
          `;
        }
        if (method.metadata?.paymentTerms) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Payment Terms:</div>
                <div class="item-right">${method.metadata.paymentTerms}</div>
            </div>
          `;
        }
        if (method.metadata?.creditLimit) {
          detailsHTML += `
            <div class="item-price-row">
                <div class="item-left">Credit Limit:</div>
                <div class="item-right">${formatCurrency(
                  method.metadata.creditLimit
                )}</div>
            </div>
          `;
        }
        break;
    }

    return detailsHTML;
  }

  /**
   * Generate payment details text section for WhatsApp/text receipts
   */
  private static generatePaymentDetailsText(
    receiptData: ReceiptData,
    formatCurrency: (amount: number) => string
  ): string {
    let text = "";

    // Check if we have enhanced payment breakdown
    if (
      receiptData.paymentBreakdown &&
      receiptData.paymentBreakdown.isSplitPayment
    ) {
      const breakdown = receiptData.paymentBreakdown;

      text += `💳 *Payment Details (Split Payment)*\n`;
      text += `🔗 Transaction: ${breakdown.transactionId}\n`;
      text += `📊 Status: ${breakdown.paymentStatus.toUpperCase()}\n\n`;

      text += `*Payment Breakdown:*\n`;
      breakdown.paymentMethods.forEach((method, index) => {
        text += `${index + 1}. ${method.name}: ${formatCurrency(
          method.amount
        )}\n`;
        text += `   Status: ${method.status.toUpperCase()}\n`;

        // Add method-specific details
        const methodDetails = this.generateMethodSpecificDetailsText(
          method,
          formatCurrency
        );
        if (methodDetails) {
          text += methodDetails;
        }
        text += `\n`;
      });

      text += `💰 *Payment Summary:*\n`;
      text += `Total Paid: ${formatCurrency(breakdown.completedAmount || 0)}\n`;
      if (breakdown.remainingAmount && breakdown.remainingAmount > 0) {
        text += `Remaining: ${formatCurrency(breakdown.remainingAmount)}\n`;
      }
    } else {
      // Single payment method
      text += `💳 Payment: ${receiptData.paymentMethod}\n`;
      if (receiptData.paymentDetails?.transactionId) {
        text += `🔗 Transaction: ${receiptData.paymentDetails.transactionId}\n`;
      }

      // Add single payment method details if available
      if (
        receiptData.paymentBreakdown &&
        receiptData.paymentBreakdown.paymentMethods.length === 1
      ) {
        const method = receiptData.paymentBreakdown.paymentMethods[0];
        const methodDetails = this.generateMethodSpecificDetailsText(
          method,
          formatCurrency
        );
        if (methodDetails) {
          text += methodDetails;
        }
      }
    }

    return text;
  }

  /**
   * Generate method-specific payment details for text receipts
   */
  private static generateMethodSpecificDetailsText(
    method: PaymentMethodDetail,
    formatCurrency: (amount: number) => string
  ): string {
    let details = "";

    switch (method.type) {
      case "cash":
        if (method.metadata?.amountTendered) {
          details += `   Amount Tendered: ${formatCurrency(
            method.metadata.amountTendered
          )}\n`;
          if (method.metadata.change && method.metadata.change > 0) {
            details += `   Change: ${formatCurrency(method.metadata.change)}\n`;
          }
        }
        break;

      case "mpesa":
        if (method.metadata?.mpesaReceiptNumber) {
          details += `   M-Pesa Receipt: ${method.metadata.mpesaReceiptNumber}\n`;
        }
        if (method.metadata?.transactionCode) {
          details += `   Transaction Code: ${method.metadata.transactionCode}\n`;
        }
        if (method.metadata?.phoneNumber) {
          details += `   Phone: ${method.metadata.phoneNumber}\n`;
        }
        break;

      case "absa_till":
        if (method.metadata?.transactionCode) {
          details += `   Transaction Code: ${method.metadata.transactionCode}\n`;
        }
        if (method.metadata?.tillNumber) {
          details += `   Till Number: ${method.metadata.tillNumber}\n`;
        }
        if (method.metadata?.absaReceiptNumber) {
          details += `   ABSA Receipt: ${method.metadata.absaReceiptNumber}\n`;
        }
        break;

      case "card":
        if (method.metadata?.cardType) {
          details += `   Card Type: ${method.metadata.cardType}\n`;
        }
        if (method.metadata?.lastFourDigits) {
          details += `   Card: ****${method.metadata.lastFourDigits}\n`;
        }
        if (method.metadata?.authorizationCode) {
          details += `   Auth Code: ${method.metadata.authorizationCode}\n`;
        }
        break;

      case "credit":
        if (method.metadata?.dueDate) {
          details += `   Due Date: ${method.metadata.dueDate}\n`;
        }
        if (method.metadata?.paymentTerms) {
          details += `   Terms: ${method.metadata.paymentTerms}\n`;
        }
        if (method.metadata?.creditLimit) {
          details += `   Credit Limit: ${formatCurrency(
            method.metadata.creditLimit
          )}\n`;
        }
        break;
    }

    return details;
  }

  /**
   * Create properly aligned two-column line for text receipts
   * @param leftText - Text for left column
   * @param rightText - Text for right column
   * @param totalWidth - Total character width (default: 35 for WhatsApp)
   * @returns Properly aligned string
   */
  private static createTextAlignedLine(
    leftText: string,
    rightText: string,
    totalWidth: number = 35
  ): string {
    // Ensure we don't exceed the total width
    const maxLeftWidth = totalWidth - rightText.length - 2; // Reserve 2 chars minimum spacing
    const truncatedLeft =
      leftText.length > maxLeftWidth
        ? leftText.substring(0, maxLeftWidth - 1) + "…"
        : leftText;

    // Calculate spacing needed
    const spacing = Math.max(
      1,
      totalWidth - truncatedLeft.length - rightText.length
    );

    return `${truncatedLeft}${" ".repeat(spacing)}${rightText}`;
  }

  // Generate text-based receipt for WhatsApp sharing
  static generateReceiptText(receiptData: ReceiptData): string {
    const { formatCurrency } = require("@/src/utils/currencyUtils");
    const formatReceiptCurrency = (amount: number) =>
      formatCurrency(amount, { showSymbol: true });

    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return (
        date.toLocaleDateString() +
        " " +
        date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    };

    let text = `🧾 *${receiptData.store.name}*\n`;
    if (receiptData.store.address) {
      text += `📍 ${receiptData.store.address}\n`;
    }
    if (receiptData.store.phone) {
      text += `📞 Mobile: ${receiptData.store.phone}\n`;
    }
    text += `📧 Email: <EMAIL>\n`;
    text += `🌐 Website: www.treasuredscents.co.ke\n`;
    text += `\n`;

    text += `*Receipt No: ${receiptData.orderNumber}*\n`;
    text += `📅 Date: ${formatDate(receiptData.orderDate)}\n`;
    text += `👤 Served by: ${receiptData.staff.name}\n`;
    if (receiptData.salesAgent) {
      text += `🤝 Sales Agent: ${receiptData.salesAgent.name}\n`;
    }
    text += `\n`;

    text += `*Customer:* ${receiptData.customer.name}\n`;
    if (receiptData.customer.phone) {
      text += `📱 Mobile: ${receiptData.customer.phone}\n`;
    }
    text += `\n`;

    text += `*Items:*\n`;
    receiptData.items.forEach((item) => {
      text += `• ${item.title}\n`;
      if (item.variantTitle) {
        text += `  Variant: ${item.variantTitle}\n`;
      }
      // Create aligned layout for quantity and price using improved alignment
      const qtyPrice = `${item.quantity} × ${formatReceiptCurrency(
        parseFloat(item.price)
      )}`;
      const total = formatReceiptCurrency(
        parseFloat(item.price) * item.quantity
      );
      const alignedLine = this.createTextAlignedLine(qtyPrice, total, 35);
      text += `  ${alignedLine}\n`;
    });
    text += `\n`;

    // Generate totals breakdown with shipping fee
    text += this.generateTotalsBreakdownText(
      receiptData,
      formatReceiptCurrency
    );

    // Enhanced payment details
    text += this.generatePaymentDetailsText(receiptData, formatReceiptCurrency);
    text += `\n`;

    if (receiptData.loyaltyPoints) {
      text += `🌟 *Loyalty Rewards*\n`;
      text += `Total TS Points: ${receiptData.loyaltyPoints.balance}\n`;
      text += `\n`;
    }

    text += `💎 You're treasured! Thank you\n`;
    text += `⚡ Powered by Dukalink POS`;

    return text;
  }

  // Generate email-friendly receipt body
  static generateEmailReceiptBody(receiptData: ReceiptData): string {
    const { formatCurrency } = require("@/src/utils/currencyUtils");
    const formatReceiptCurrency = (amount: number) =>
      formatCurrency(amount, { showSymbol: true });

    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return (
        date.toLocaleDateString() +
        " " +
        date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    };

    let body = `Dear Customer,\n\n`;
    body += `You're treasured! Thank you for choosing ${receiptData.store.name}!\n\n`;
    body += `RECEIPT DETAILS\n`;
    body += `===============\n\n`;

    body += `Store: ${receiptData.store.name}\n`;
    if (receiptData.store.address) {
      body += `Address: ${receiptData.store.address}\n`;
    }
    if (receiptData.store.phone || !receiptData.store.phone) {
      body += `Mobile: +254 111 443 993\n`;
    }
    body += `Email: <EMAIL>\n`;
    body += `Website: www.treasuredscents.co.ke\n`;
    body += `\n`;

    body += `Receipt No: ${receiptData.orderNumber}\n`;
    body += `Date: ${formatDate(receiptData.orderDate)}\n`;
    body += `Served by: ${receiptData.staff.name}\n`;
    if (receiptData.salesAgent) {
      body += `Sales Agent: ${receiptData.salesAgent.name}\n`;
    }
    body += `\n`;

    body += `Customer: ${receiptData.customer.name}\n`;
    if (receiptData.customer.phone) {
      body += `Mobile: ${receiptData.customer.phone}\n`;
    }
    if (receiptData.customer.email) {
      body += `Email: ${receiptData.customer.email}\n`;
    }
    body += `\n`;

    body += `ITEMS PURCHASED\n`;
    body += `===============\n`;
    receiptData.items.forEach((item, index) => {
      body += `${index + 1}. ${item.title}\n`;
      if (item.variantTitle) {
        body += `   Variant: ${item.variantTitle}\n`;
      }
      // Create aligned layout for email using improved alignment
      const qtyPrice = `   ${item.quantity} × ${formatReceiptCurrency(
        parseFloat(item.price)
      )}`;
      const total = formatReceiptCurrency(
        parseFloat(item.price) * item.quantity
      );
      const alignedLine = this.createTextAlignedLine(qtyPrice, total, 50);
      body += `${alignedLine}\n\n`;
    });

    body += `PAYMENT SUMMARY\n`;
    body += `===============\n`;

    // Generate totals breakdown with shipping fee
    const shippingFee = this.extractShippingFeeFromReceiptData(receiptData);
    const subtotal = receiptData.total - shippingFee;

    // Show subtotal if there's a shipping fee
    if (shippingFee > 0) {
      body += `Subtotal: ${formatReceiptCurrency(subtotal)}\n`;
      body += `Delivery Fee: ${formatReceiptCurrency(shippingFee)}\n`;
    }

    body += `Total Amount: ${formatReceiptCurrency(receiptData.total)}\n`;
    body += `Payment Method: ${receiptData.paymentMethod}\n`;
    if (receiptData.paymentDetails?.transactionId) {
      body += `Transaction ID: ${receiptData.paymentDetails.transactionId}\n`;
    }
    body += `\n`;

    if (receiptData.loyaltyPoints) {
      body += `LOYALTY REWARDS\n`;
      body += `===============\n`;
      body += `Total TS Points: ${receiptData.loyaltyPoints.balance}\n`;
      body += `\n`;
    }

    body += `You're treasured! Thank you for choosing ${receiptData.store.name}.\n\n`;
    body += `This receipt was generated by Dukalink POS.\n`;
    body += `For any inquiries, please contact us at ${"+254 111 443 993"}.`;
    // body += `For any inquiries, please contact us at ${
    //   receiptData.store.phone || "our store"
    // }.`;

    return body;
  }

  // Print receipt directly from order
  /**
   * @deprecated Use UnifiedReceiptManager.generateReceipt() instead
   * @see UnifiedReceiptManager for the modern, standardized receipt system
   *
   * Migration example:
   * ```typescript
   * // Old way:
   * const result = await ReceiptGenerator.printReceipt(order);
   *
   * // New way:
   * const result = await UnifiedReceiptManager.generateReceipt(order, {
   *   format: "thermal",
   *   autoPrint: true,
   *   printerType: "thermal"
   * });
   * ```
   */
  static async printReceipt(
    order: OrderData,
    loyaltyCompletionData?: LoyaltyCompletionData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const receiptData = await this.generateReceiptData(
        order,
        true,
        loyaltyCompletionData
      ); // Force refresh for print
      const html = this.generateReceiptHTML(receiptData);

      await Print.printAsync({
        html,
        printerUrl: undefined, // Use default printer
      });

      return { success: true };
    } catch (error) {
      console.error("Print receipt error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Print failed",
      };
    }
  }

  // Share receipt directly from order
  static async shareReceipt(
    order: OrderData,
    loyaltyCompletionData?: LoyaltyCompletionData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const receiptData = await this.generateReceiptData(
        order,
        true,
        loyaltyCompletionData
      ); // Force refresh for share
      const html = this.generateReceiptHTML(receiptData);

      const { uri } = await Print.printToFileAsync({ html });

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: "application/pdf",
          dialogTitle: `Receipt - ${receiptData.orderNumber}`,
        });
        return { success: true };
      } else {
        return {
          success: false,
          error: "Sharing is not available on this device",
        };
      }
    } catch (error) {
      console.error("Share receipt error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Share failed",
      };
    }
  }

  // Share receipt via WhatsApp
  static async shareReceiptWhatsApp(
    order: OrderData,
    loyaltyCompletionData?: LoyaltyCompletionData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const receiptData = await this.generateReceiptData(
        order,
        true,
        loyaltyCompletionData
      );

      // Create a text-based receipt for WhatsApp
      const receiptText = this.generateReceiptText(receiptData);

      // Create WhatsApp URL with the receipt text
      const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(
        receiptText
      )}`;

      // Check if we're on web or mobile
      const { Linking } = require("react-native");

      const canOpen = await Linking.canOpenURL(whatsappUrl);
      if (canOpen) {
        await Linking.openURL(whatsappUrl);
        return { success: true };
      } else {
        // Fallback to web WhatsApp
        const webWhatsappUrl = `https://web.whatsapp.com/send?text=${encodeURIComponent(
          receiptText
        )}`;
        await Linking.openURL(webWhatsappUrl);
        return { success: true };
      }
    } catch (error) {
      console.error("WhatsApp share error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "WhatsApp share failed",
      };
    }
  }

  // Share receipt via Email
  static async shareReceiptEmail(
    order: OrderData,
    loyaltyCompletionData?: LoyaltyCompletionData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const receiptData = await this.generateReceiptData(
        order,
        true,
        loyaltyCompletionData
      );

      // Create email subject and body
      const subject = `Receipt - Order #${receiptData.orderNumber} from ${receiptData.store.name}`;
      const body = this.generateEmailReceiptBody(receiptData);

      // Create mailto URL
      const mailtoUrl = `mailto:?subject=${encodeURIComponent(
        subject
      )}&body=${encodeURIComponent(body)}`;

      // Check if we're on web or mobile
      const { Linking } = require("react-native");

      const canOpen = await Linking.canOpenURL(mailtoUrl);
      if (canOpen) {
        await Linking.openURL(mailtoUrl);
        return { success: true };
      } else {
        return {
          success: false,
          error: "Email client is not available on this device",
        };
      }
    } catch (error) {
      console.error("Email share error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Email share failed",
      };
    }
  }

  // Print receipt using thermal printer
  /**
   * @deprecated Use UnifiedReceiptManager.generateReceipt() instead
   * @see UnifiedReceiptManager for the modern, standardized thermal printing
   *
   * Migration example:
   * ```typescript
   * // Old way:
   * const result = await ReceiptGenerator.printThermalReceipt(order);
   *
   * // New way:
   * const result = await UnifiedReceiptManager.generateReceipt(order, {
   *   format: "thermal",
   *   autoPrint: true,
   *   printerType: "thermal"
   * });
   * ```
   */
  static async printThermalReceipt(
    order: OrderData
  ): Promise<{ success: boolean; error?: string; redirected?: boolean }> {
    try {
      // Check Bluetooth permissions first
      const bluetoothReady =
        await BluetoothPermissionHelper.ensureBluetoothReady();
      if (!bluetoothReady.granted) {
        return {
          success: false,
          error: bluetoothReady.error || "Bluetooth permissions not granted",
        };
      }

      // Convert order data to compatible format for enhanced thermal print service
      const compatibleOrderData = {
        id: order.id,
        orderNumber: order.orderNumber || order.number || order.name,
        totalPrice: order.totalPrice || order.total_price || "0",
        createdAt:
          order.createdAt || order.created_at || new Date().toISOString(),
        customer: order.customer,
        salespersonName: order.salespersonName,
        salespersonId: order.salespersonId,
        paymentMethod: order.paymentMethod,
        lineItems: (order.lineItems || order.line_items || []).map(
          (item: any) => ({
            id: item.id,
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            variantTitle: item.variantTitle,
            discount: item.discount,
          })
        ),
      };

      // Use enhanced thermal print service
      const result = await EnhancedThermalPrintService.printReceipt(
        compatibleOrderData
      );

      if (result.redirected) {
        // User chose to use standard printing or setup printer
        return { success: false, redirected: true };
      }

      return {
        success: result.success,
        error: result.error,
      };
    } catch (error) {
      console.error("Thermal print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Thermal print failed",
      };
    }
  }

  // Print receipt using cross-platform service (automatically chooses best method)
  static async printReceiptCrossPlatform(
    order: OrderData,
    loyaltyCompletionData?: LoyaltyCompletionData
  ): Promise<{
    success: boolean;
    error?: string;
    platform?: string;
    method?: string;
  }> {
    try {
      const receiptData = await this.generateReceiptData(
        order,
        true,
        loyaltyCompletionData
      );

      // Import here to avoid circular dependencies
      const {
        CrossPlatformPrintService,
      } = require("../../services/CrossPlatformPrintService");

      const result = await CrossPlatformPrintService.printReceipt(receiptData);

      return {
        success: result.success,
        error: result.error,
        platform: result.platform,
        method: result.method,
      };
    } catch (error) {
      console.error("Cross-platform print error:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Cross-platform print failed",
      };
    }
  }

  // Check if thermal printing is available
  static async isThermalPrintingAvailable(): Promise<boolean> {
    try {
      return await EnhancedThermalPrintService.isThermalPrinterAvailable();
    } catch (error) {
      console.error("Error checking thermal printer availability:", error);
      return false;
    }
  }

  // Check if cross-platform printing is available
  static async isCrossPlatformPrintingAvailable(): Promise<boolean> {
    try {
      // Import here to avoid circular dependencies
      const {
        CrossPlatformPrintService,
      } = require("../../services/CrossPlatformPrintService");
      return await CrossPlatformPrintService.isPrintingAvailable();
    } catch (error) {
      console.error(
        "Error checking cross-platform printing availability:",
        error
      );
      return false;
    }
  }

  // Show enhanced receipt actions alert with cross-platform printing option
  /**
   * @deprecated Use UnifiedReceiptManager.generateReceipt() instead
   * @see UnifiedReceiptManager for the modern, standardized receipt system
   *
   * Migration example:
   * ```typescript
   * // Old way:
   * ReceiptGenerator.showEnhancedReceiptActions(order);
   *
   * // New way:
   * const result = await UnifiedReceiptManager.generateReceipt(order, {
   *   format: "thermal",
   *   autoPrint: true,
   *   printerType: "thermal"
   * });
   * if (!result.success) {
   *   // Handle error with simple error modal instead of complex action sheet
   * }
   * ```
   */
  static async showEnhancedReceiptActions(order: OrderData) {
    const orderNumber =
      order.orderNumber || order.number || order.name || "N/A";

    // Check if cross-platform printing is available
    const crossPlatformAvailable =
      await this.isCrossPlatformPrintingAvailable();
    const thermalAvailable = await this.isThermalPrintingAvailable();

    const actions: any[] = [];

    // Add cross-platform print option (recommended)
    if (crossPlatformAvailable) {
      actions.push({
        text: "🖨️ Smart Print (Recommended)",
        onPress: () => this.printReceiptCrossPlatform(order),
      });
    }

    // Add platform-specific options
    actions.push({
      text: "📄 Standard Print",
      onPress: () => this.printReceipt(order),
    });

    // Add thermal print option if available (mobile only)
    if (thermalAvailable) {
      actions.push({
        text: "🧾 Thermal Print",
        onPress: () => this.printThermalReceipt(order),
      });
    }

    actions.push({
      text: "📤 Share PDF",
      onPress: () => this.shareReceipt(order),
    });

    actions.push({
      text: "💬 Share via WhatsApp",
      onPress: () => this.shareReceiptWhatsApp(order),
    });

    actions.push({
      text: "📧 Share via Email",
      onPress: () => this.shareReceiptEmail(order),
    });

    actions.push({
      text: "Cancel",
      style: "cancel",
    });

    Alert.alert("Receipt Options", `Order #${orderNumber}`, actions);
  }

  // Show receipt actions alert (legacy method - now enhanced)
  static showReceiptActions(order: OrderData) {
    // Use enhanced version by default
    this.showEnhancedReceiptActions(order);
  }
}

export default ReceiptGenerator;
