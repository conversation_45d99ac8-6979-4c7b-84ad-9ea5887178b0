/**
 * Example usage of the redesigned inline PaymentFlowManager component
 * This shows how to integrate it into a checkout page
 */

import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import PaymentFlowManager, { PaymentFlowResult } from "./PaymentFlowManager";
import { formatCurrency } from "@/src/utils/currencyUtils";

interface CheckoutExampleProps {
  cartTotal: number;
  customerId?: string;
  customerName?: string;
  staffId?: string;
  terminalId?: string;
  locationId?: string;
}

const CheckoutExample: React.FC<CheckoutExampleProps> = ({
  cartTotal,
  customerId,
  customerName,
  staffId,
  terminalId,
  locationId,
}) => {
  const theme = useTheme();
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [paymentResult, setPaymentResult] = useState<PaymentFlowResult | null>(null);

  const handlePaymentComplete = (result: PaymentFlowResult) => {
    console.log("Payment completed:", result);
    setPaymentResult(result);
    setPaymentComplete(true);
    
    // Here you would typically:
    // 1. Create the order in your system
    // 2. Update inventory
    // 3. Print receipt
    // 4. Clear cart
    // 5. Navigate to success page
  };

  if (paymentComplete && paymentResult) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.successCard, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.successTitle, { color: theme.colors.success }]}>
            Payment Successful! 🎉
          </Text>
          <Text style={[styles.successAmount, { color: theme.colors.primary }]}>
            {formatCurrency(paymentResult.totalAmount)}
          </Text>
          <Text style={[styles.successTransaction, { color: theme.colors.textSecondary }]}>
            Transaction ID: {paymentResult.transactionId}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Other checkout components would go here */}
      <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Customer Information
        </Text>
        <Text style={[styles.customerName, { color: theme.colors.textSecondary }]}>
          {customerName || "Walk-in Customer"}
        </Text>
      </View>

      <View style={[styles.section, { backgroundColor: theme.colors.card }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Order Summary
        </Text>
        <View style={styles.totalRow}>
          <Text style={[styles.totalLabel, { color: theme.colors.text }]}>
            Total Amount:
          </Text>
          <Text style={[styles.totalAmount, { color: theme.colors.primary }]}>
            {formatCurrency(cartTotal)}
          </Text>
        </View>
      </View>

      {/* Inline PaymentFlowManager - No modal wrapper needed */}
      <PaymentFlowManager
        totalAmount={cartTotal}
        currency="KES"
        onPaymentComplete={handlePaymentComplete}
        customerId={customerId}
        customerName={customerName}
        staffId={staffId}
        terminalId={terminalId}
        locationId={locationId}
        allowSplitPayment={true}
      />

      {/* Other checkout components would continue here */}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
  },
  customerName: {
    fontSize: 16,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  totalAmount: {
    fontSize: 20,
    fontWeight: "bold",
  },
  successCard: {
    padding: 32,
    borderRadius: 16,
    alignItems: "center",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  successAmount: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 8,
  },
  successTransaction: {
    fontSize: 14,
    textAlign: "center",
  },
});

export default CheckoutExample;
