import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { StaffCreateButton } from "@/src/components/rbac";
import { StaffActionButtons } from "@/src/components/rbac/ActionButtons";
import { StaffManagementFAB } from "@/src/components/rbac/PermissionAwareFAB";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  Alert,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface StaffMember {
  id: string;
  username: string;
  name: string;
  email?: string;
  role: string;
  commissionRate: number;
  isActive: boolean;
  lastLogin?: string;
  permissions: string[];
}

export default function StaffListScreen() {
  const router = useRouter();
  const { setCurrentTitle } = useNavigation();
  const { canManageStaff, canViewStaff } = useRBAC();

  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = "#4CAF50";
  const warningColor = "#FF9500";
  const errorColor = "#FF3B30";

  useEffect(() => {
    setCurrentTitle("Staff Management");
    loadStaffMembers();
  }, [setCurrentTitle]);

  // Refresh data when screen comes into focus (after creating/editing staff)
  useFocusEffect(
    useCallback(() => {
      loadStaffMembers();
    }, [])
  );

  const loadStaffMembers = async () => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getStaff();

      if (response.success && response.data) {
        setStaffMembers(response.data.staffMembers || []);
      } else {
        Alert.alert("Error", response.error || "Failed to load staff members");
      }
    } catch (error) {
      console.error("Load staff members error:", error);
      Alert.alert("Error", "Failed to load staff members");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadStaffMembers();
  };

  const handleStaffPress = (staffMember: StaffMember) => {
    router.push({
      pathname: "/staff-details",
      params: { staffId: staffMember.id },
    });
  };

  const handleCreateStaff = () => {
    router.push("/staff-create");
  };

  const handleEditStaff = (staffMember: StaffMember) => {
    // TODO: Navigate to staff edit screen
    Alert.alert(
      "Edit Staff",
      `Edit functionality for ${staffMember.name} will be implemented soon.`
    );
  };

  const handleDeleteStaff = async (staffMember: StaffMember) => {
    try {
      // TODO: Implement delete API call
      Alert.alert(
        "Delete Staff",
        `Delete functionality for ${staffMember.name} will be implemented soon.`
      );
    } catch (error) {
      Alert.alert("Error", "Failed to delete staff member");
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "super_admin":
        return errorColor;
      case "manager":
        return warningColor;
      case "cashier":
        return successColor;
      default:
        return textSecondary;
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role.toLowerCase()) {
      case "super_admin":
        return "Super Admin";
      case "manager":
        return "Manager";
      case "cashier":
        return "Cashier";
      default:
        return role;
    }
  };

  const formatLastLogin = (lastLogin?: string) => {
    if (!lastLogin) return "Never";

    const date = new Date(lastLogin);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 24 * 7) {
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const renderStaffCard = (staffMember: StaffMember) => (
    <TouchableOpacity
      key={staffMember.id}
      onPress={() => handleStaffPress(staffMember)}
      style={styles.cardTouchable}
    >
      <ModernCard style={[styles.staffCard, { backgroundColor: surfaceColor }]}>
        <View style={styles.staffHeader}>
          <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
            <Text style={styles.avatarText}>
              {staffMember.name.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.staffInfo}>
            <Text style={[styles.staffName, { color: textColor }]}>
              {staffMember.name}
            </Text>
            <Text style={[styles.staffUsername, { color: textSecondary }]}>
              @{staffMember.username}
            </Text>
            {staffMember.email && (
              <Text style={[styles.staffEmail, { color: textSecondary }]}>
                {staffMember.email}
              </Text>
            )}
          </View>
          <View style={styles.staffStatus}>
            <View
              style={[
                styles.roleTag,
                { backgroundColor: getRoleColor(staffMember.role) + "20" },
              ]}
            >
              <Text
                style={[
                  styles.roleText,
                  { color: getRoleColor(staffMember.role) },
                ]}
              >
                {getRoleDisplayName(staffMember.role)}
              </Text>
            </View>
            <View
              style={[
                styles.statusIndicator,
                {
                  backgroundColor: staffMember.isActive
                    ? successColor
                    : errorColor,
                },
              ]}
            />
          </View>
        </View>

        <View style={styles.staffDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="card" size={16} color={textSecondary} />
            <Text style={[styles.detailText, { color: textSecondary }]}>
              Commission: {staffMember.commissionRate}%
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="time" size={16} color={textSecondary} />
            <Text style={[styles.detailText, { color: textSecondary }]}>
              Last login: {formatLastLogin(staffMember.lastLogin)}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="shield-checkmark" size={16} color={textSecondary} />
            <Text style={[styles.detailText, { color: textSecondary }]}>
              {staffMember.permissions.length} permissions
            </Text>
          </View>
        </View>

        <View style={styles.cardFooter}>
          <StaffActionButtons
            onEdit={() => handleEditStaff(staffMember)}
            onDelete={() => handleDeleteStaff(staffMember)}
            onViewDetails={() => handleStaffPress(staffMember)}
            staffName={staffMember.name}
          />
        </View>
      </ModernCard>
    </TouchableOpacity>
  );

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary,
    primaryColor
  );

  // Check permissions
  if (!canViewStaff) {
    return (
      <ScreenWrapper title="Staff Management" showBackButton>
        <View style={styles.noPermissionContainer}>
          <Ionicons name="lock-closed" size={64} color={textSecondary} />
          <Text style={[styles.noPermissionText, { color: textSecondary }]}>
            You don't have permission to view staff members
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper title="Staff Management" showBackButton>
      <View style={styles.container}>
        {/* Header with Create Button */}
        <View style={styles.header}>
          <View style={styles.headerInfo}>
            <Text style={[styles.headerTitle, { color: textColor }]}>
              Staff Members
            </Text>
            <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
              {staffMembers.length} total members
            </Text>
          </View>
          <StaffCreateButton>
            <TouchableOpacity
              style={[styles.createButton, { backgroundColor: primaryColor }]}
              onPress={handleCreateStaff}
            >
              <Ionicons name="add" size={24} color="white" />
            </TouchableOpacity>
          </StaffCreateButton>
        </View>

        {/* Staff List */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={primaryColor}
            />
          }
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={[styles.loadingText, { color: textSecondary }]}>
                Loading staff members...
              </Text>
            </View>
          ) : staffMembers.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="people" size={64} color={textSecondary} />
              <Text style={[styles.emptyText, { color: textSecondary }]}>
                No staff members found
              </Text>
              <StaffCreateButton>
                <TouchableOpacity
                  style={[styles.emptyButton, { borderColor: primaryColor }]}
                  onPress={handleCreateStaff}
                >
                  <Text
                    style={[styles.emptyButtonText, { color: primaryColor }]}
                  >
                    Create First Staff Member
                  </Text>
                </TouchableOpacity>
              </StaffCreateButton>
            </View>
          ) : (
            <View style={styles.staffList}>
              {staffMembers.map(renderStaffCard)}
            </View>
          )}
        </ScrollView>

        {/* Floating Action Button */}
        <StaffManagementFAB onPress={handleCreateStaff} />
      </View>
    </ScreenWrapper>
  );
}

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string,
  primaryColor: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: textSecondary + "20",
    },
    headerInfo: {
      flex: 1,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: "bold",
      marginBottom: 4,
    },
    headerSubtitle: {
      fontSize: 14,
    },
    createButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: "center",
      alignItems: "center",
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 100, // Space for FAB + extra padding
    },
    staffList: {
      padding: 20,
    },
    cardTouchable: {
      marginBottom: 16,
    },
    staffCard: {
      padding: 16,
    },
    staffHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
    },
    avatarText: {
      color: "white",
      fontSize: 18,
      fontWeight: "bold",
    },
    staffInfo: {
      flex: 1,
    },
    staffName: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 2,
    },
    staffUsername: {
      fontSize: 14,
      marginBottom: 2,
    },
    staffEmail: {
      fontSize: 12,
    },
    staffStatus: {
      alignItems: "flex-end",
    },
    roleTag: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginBottom: 4,
    },
    roleText: {
      fontSize: 12,
      fontWeight: "600",
    },
    statusIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    staffDetails: {
      marginBottom: 12,
    },
    detailRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    detailText: {
      fontSize: 14,
      marginLeft: 8,
    },
    cardFooter: {
      alignItems: "flex-end",
      justifyContent: "flex-end",
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: textSecondary + "10",
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: 40,
    },
    loadingText: {
      fontSize: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: 40,
    },
    emptyText: {
      fontSize: 16,
      marginTop: 16,
      marginBottom: 24,
    },
    emptyButton: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderWidth: 2,
      borderRadius: 8,
    },
    emptyButtonText: {
      fontSize: 16,
      fontWeight: "600",
    },
    noPermissionContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 40,
    },
    noPermissionText: {
      fontSize: 16,
      textAlign: "center",
      marginTop: 16,
    },
  });
