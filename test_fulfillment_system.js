/**
 * Comprehensive Fulfillment System Test Script
 *
 * Tests all core fulfillment functionality including:
 * - Authentication
 * - Shipping rates management
 * - Shipping fee calculation
 * - Fulfillment creation and management
 * - Error handling
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";
let authToken = "";

// Test configuration
const testConfig = {
  credentials: {
    username: "admin1",
    password: "admin123",
  },
  testOrder: {
    orderId: "test-order-001",
    shopifyOrderId: "shopify-order-123",
  },
  testDelivery: {
    address: {
      street: "123 Test Street",
      city: "Nairobi",
      country: "Kenya",
    },
    contactName: "John Doe",
    contactPhone: "+254700123456",
    instructions: "Call before delivery",
  },
};

// Helper function for API calls
async function apiCall(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

// Test functions
async function testAuthentication() {
  console.log("\n🔐 Testing Authentication...");

  const result = await apiCall(
    "POST",
    "/api/pos/login",
    testConfig.credentials
  );

  if (result.success && result.data.success) {
    authToken = result.data.data.token;
    console.log("✅ Authentication successful");
    console.log(`   Token: ${authToken.substring(0, 50)}...`);
    return true;
  } else {
    console.log("❌ Authentication failed:", result.error);
    return false;
  }
}

async function testGetShippingRates() {
  console.log("\n📦 Testing Get Shipping Rates...");

  const result = await apiCall("GET", "/api/fulfillment/shipping/rates", null, {
    Authorization: `Bearer ${authToken}`,
  });

  if (result.success) {
    const shippingRates = result.data.data || result.data;
    console.log("✅ Shipping rates retrieved successfully");
    console.log(`   Found ${shippingRates.length} shipping rates:`);
    shippingRates.forEach((rate) => {
      console.log(
        `   • ${rate.delivery_method}: KES ${rate.base_fee} (${rate.description})`
      );
    });
    return shippingRates;
  } else {
    console.log("❌ Failed to get shipping rates:", result.error);
    return null;
  }
}

async function testCalculateShippingFee() {
  console.log("\n💰 Testing Shipping Fee Calculation...");

  const testCases = [
    { deliveryMethod: "standard", distanceKm: 5, weightKg: 2 },
    { deliveryMethod: "express", distanceKm: 10, weightKg: 1 },
    { deliveryMethod: "local_pickup" },
    { deliveryMethod: "upcountry", distanceKm: 50, weightKg: 5 },
  ];

  for (const testCase of testCases) {
    const result = await apiCall(
      "POST",
      "/api/fulfillment/shipping/calculate",
      {
        shippingData: testCase,
      },
      {
        Authorization: `Bearer ${authToken}`,
      }
    );

    if (result.success) {
      const calculation = result.data.data || result.data;
      console.log(
        `✅ ${testCase.deliveryMethod}: KES ${calculation.shippingFee}`
      );
      if (calculation.breakdown) {
        console.log(
          `   Breakdown: Base: ${calculation.breakdown.baseFee}, Distance: ${calculation.breakdown.distanceFee}, Weight: ${calculation.breakdown.weightFee}`
        );
      }
    } else {
      console.log(
        `❌ Failed to calculate ${testCase.deliveryMethod}:`,
        result.error
      );
    }
  }
}

async function testCreateFulfillment() {
  console.log("\n📋 Testing Create Fulfillment...");

  const fulfillmentData = {
    orderId: testConfig.testOrder.orderId,
    shopifyOrderId: testConfig.testOrder.shopifyOrderId,
    deliveryAddress: testConfig.testDelivery.address,
    deliveryContactName: testConfig.testDelivery.contactName,
    deliveryContactPhone: testConfig.testDelivery.contactPhone,
    deliveryInstructions: testConfig.testDelivery.instructions,
    deliveryMethod: "standard",
    shippingFee: 200.0,
    shippingFeeCurrency: "KES",
  };

  const result = await apiCall(
    "POST",
    "/api/fulfillment/fulfillments",
    {
      fulfillmentData,
    },
    {
      Authorization: `Bearer ${authToken}`,
    }
  );

  if (result.success) {
    const fulfillment = result.data.data || result.data;
    console.log("✅ Fulfillment created successfully");
    console.log(`   Fulfillment ID: ${fulfillment.id}`);
    console.log(`   Status: ${fulfillment.fulfillmentStatus}`);
    console.log(`   Shipping Fee: KES ${fulfillment.shippingFee}`);
    return fulfillment;
  } else {
    console.log("❌ Failed to create fulfillment:", result.error);
    return null;
  }
}

async function testGetFulfillmentsByOrder(orderId) {
  console.log("\n📋 Testing Get Fulfillments by Order...");

  const result = await apiCall(
    "GET",
    `/api/fulfillment/orders/${orderId}/fulfillments`,
    null,
    {
      Authorization: `Bearer ${authToken}`,
    }
  );

  if (result.success) {
    const fulfillments = result.data.data || result.data;
    console.log("✅ Order fulfillments retrieved successfully");
    console.log(
      `   Found ${fulfillments.length} fulfillments for order ${orderId}`
    );
    fulfillments.forEach((fulfillment) => {
      console.log(
        `   • ${fulfillment.id}: ${fulfillment.fulfillmentStatus} (${fulfillment.deliveryMethod})`
      );
    });
    return fulfillments;
  } else {
    console.log("❌ Failed to get order fulfillments:", result.error);
    return null;
  }
}

async function testUpdateDeliveryDetails(fulfillmentId) {
  console.log("\n✏️ Testing Update Delivery Details...");

  const updateData = {
    deliveryAddress: {
      street: "456 Updated Street",
      city: "Nairobi",
      country: "Kenya",
    },
    deliveryContactPhone: "+254700654321",
    deliveryInstructions: "Updated: Ring doorbell twice",
  };

  const result = await apiCall(
    "PUT",
    `/api/fulfillment/fulfillments/${fulfillmentId}/delivery`,
    {
      updateData,
    },
    {
      Authorization: `Bearer ${authToken}`,
    }
  );

  if (result.success) {
    console.log("✅ Delivery details updated successfully");
    return true;
  } else {
    console.log("❌ Failed to update delivery details:", result.error);
    return false;
  }
}

async function testUpdateFulfillmentStatus(fulfillmentId) {
  console.log("\n🔄 Testing Update Fulfillment Status...");

  const statuses = ["processing", "shipped"];

  for (const status of statuses) {
    const result = await apiCall(
      "PUT",
      `/api/fulfillment/fulfillments/${fulfillmentId}/status`,
      {
        status,
      },
      {
        Authorization: `Bearer ${authToken}`,
      }
    );

    if (result.success) {
      console.log(`✅ Status updated to: ${status}`);
    } else {
      console.log(`❌ Failed to update status to ${status}:`, result.error);
    }
  }
}

async function testErrorHandling() {
  console.log("\n⚠️ Testing Error Handling...");

  // Test invalid fulfillment ID
  const result1 = await apiCall(
    "GET",
    "/api/fulfillment/fulfillments/invalid-id",
    null,
    {
      Authorization: `Bearer ${authToken}`,
    }
  );

  if (!result1.success) {
    console.log("✅ Invalid fulfillment ID properly rejected");
  } else {
    console.log("❌ Invalid fulfillment ID should have been rejected");
  }

  // Test unauthorized access
  const result2 = await apiCall("GET", "/api/fulfillment/shipping/rates");

  if (!result2.success && result2.status === 401) {
    console.log("✅ Unauthorized access properly rejected");
  } else {
    console.log("❌ Unauthorized access should have been rejected");
  }
}

// Main test runner
async function runAllTests() {
  console.log("🚀 Starting Comprehensive Fulfillment System Tests\n");
  console.log("=".repeat(60));

  try {
    // Step 1: Authentication
    const authSuccess = await testAuthentication();
    if (!authSuccess) {
      console.log("\n❌ Cannot proceed without authentication");
      return;
    }

    // Step 2: Get shipping rates
    const shippingRates = await testGetShippingRates();

    // Step 3: Calculate shipping fees
    await testCalculateShippingFee();

    // Step 4: Create fulfillment
    const fulfillment = await testCreateFulfillment();

    if (fulfillment) {
      // Step 5: Get fulfillments by order
      await testGetFulfillmentsByOrder(testConfig.testOrder.orderId);

      // Step 6: Update delivery details
      await testUpdateDeliveryDetails(fulfillment.id);

      // Step 7: Update fulfillment status
      await testUpdateFulfillmentStatus(fulfillment.id);
    }

    // Step 8: Error handling
    await testErrorHandling();

    console.log("\n" + "=".repeat(60));
    console.log("🎉 All fulfillment system tests completed!");
    console.log("\n✅ Core Features Verified:");
    console.log("   • Authentication and authorization");
    console.log("   • Shipping rates management");
    console.log("   • Shipping fee calculation");
    console.log("   • Fulfillment creation and management");
    console.log("   • Delivery details updates");
    console.log("   • Status tracking");
    console.log("   • Error handling and validation");
  } catch (error) {
    console.log("\n❌ Test suite failed:", error.message);
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error("Test suite error:", error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
