/**
 * Universal Receipt Template
 *
 * Generates device-agnostic HTML receipts using only basic HTML and CSS
 * that works consistently across all printing devices and platforms
 */

import {
  UniversalReceiptStyler,
  UniversalReceiptOptions,
} from "./UniversalReceiptStyler";
import { TREASURED_LOGO_BASE64 } from "../constants/logoConstants";

export interface ReceiptTemplateData {
  // Store information
  storeName: string;
  storeAddress: string;
  storePhone: string;
  storeEmail: string;
  storeWebsite?: string;
  storeLogo?: string; // Path to store logo image

  // Receipt details
  receiptNumber: string;
  date: string;
  time: string;
  staffName: string;

  // Customer information
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;

  // Items
  items: Array<{
    number: number;
    name: string;
    variant?: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    // ✅ CRITICAL FIX: Add discount support to template
    discount?: {
      type: "percentage" | "fixed_amount";
      amount: number;
      discountAmount: number;
    };
  }>;

  // Totals
  subtotal: number;
  totalDiscount?: number; // ✅ CRITICAL FIX: Add total discount display
  shippingFee?: number;
  tax?: number;
  grandTotal: number;
  grandTotalInWords?: string;

  // Payment
  paymentMethods: Array<{
    method: string;
    amount: number;
    date?: string;
    transactionCode?: string;
    phoneNumber?: string;
  }>;
  totalPaid: number;
  change?: number;

  // Loyalty (simplified)
  loyalty?: {
    totalPoints: number;
  };

  // Sales agent
  salesAgent?: {
    name: string;
    id: string;
  };

  // Delivery information
  delivery?: {
    method: string;
    trackingNumber?: string;
    shippingFee?: number;
  };
}

export class UniversalReceiptTemplate {
  /**
   * Generate universal HTML receipt
   */
  static generateHTML(
    data: ReceiptTemplateData,
    options: UniversalReceiptOptions = {}
  ): string {
    const css = UniversalReceiptStyler.generateUniversalCSS(options);

    return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - ${data.receiptNumber}</title>
    <style>${css}</style>
</head>
<body>
    ${this.generateReceiptContent(data)}
</body>
</html>`;
  }

  /**
   * Generate receipt content HTML
   */
  private static generateReceiptContent(data: ReceiptTemplateData): string {
    return `
    <!-- Store Header -->
    <div class="receipt-header">
        ${
          data.storeLogo
            ? `<div class="store-logo">
                <img src="${data.storeLogo}" alt="${data.storeName}" class="logo-image" />
               </div>`
            : ""
        }
        <div class="store-name">${data.storeName}</div>
        <div class="store-info">${data.storeAddress}</div>
        <div class="store-info">Mobile: ${data.storePhone}</div>
        <div class="store-info">Email: ${data.storeEmail}</div>
        ${
          data.storeWebsite
            ? `<div class="store-info">${data.storeWebsite}</div>`
            : ""
        }
    </div>

    <!-- Receipt Details -->
    <div class="receipt-body">
        <div class="receipt-details center">
            <div class="bold large mb-2">SALES RECEIPT</div>
        </div>
        
        <table class="two-column">
            <tr>
                <td class="left-col">Receipt No:</td>
                <td class="right-col bold">${data.receiptNumber}</td>
            </tr>
            <tr>
                <td class="left-col">Date:</td>
                <td class="right-col">${data.date} ${data.time}</td>
            </tr>
            <tr>
                <td class="left-col">Served by:</td>
                <td class="right-col">${data.staffName}</td>
            </tr>
            ${
              data.customerName
                ? `
            <tr>
                <td class="left-col">Customer:</td>
                <td class="right-col">${data.customerName}</td>
            </tr>
            `
                : ""
            }
            ${
              data.customerPhone
                ? `
            <tr>
                <td class="left-col">Mobile:</td>
                <td class="right-col">${data.customerPhone}</td>
            </tr>
            `
                : ""
            }
        </table>
    </div>

    <!-- Items Section -->
    <div class="items-section">
        <div class="separator"></div>
        ${data.items
          .map(
            (item) => `
        <div class="item">
            <div class="item-name">${item.number}. ${item.name}</div>
            ${
              item.variant
                ? `<div class="item-details">Variant: ${item.variant}</div>`
                : ""
            }
            <table class="two-column">
                <tr>
                    <td class="left-col small">${item.quantity.toFixed(
                      2
                    )} x KSh ${item.unitPrice.toFixed(2)}</td>
                    <td class="right-col bold">KSh ${item.totalPrice.toFixed(
                      2
                    )}</td>
                </tr>
                ${
                  item.discount && item.discount.discountAmount > 0
                    ? `
                <tr>
                    <td class="left-col small" style="color: #e87c83;">Discount (${
                      item.discount.type === "percentage"
                        ? `${item.discount.amount}%`
                        : `KSh ${item.discount.amount.toFixed(2)}`
                    }):</td>
                    <td class="right-col small" style="color: #e87c83;">-KSh ${item.discount.discountAmount.toFixed(
                      2
                    )}</td>
                </tr>
                `
                    : ""
                }
            </table>
        </div>
        `
          )
          .join("")}
    </div>

    <!-- Totals Section -->
    <div class="totals-section">
        <table class="two-column">
            <tr>
                <td class="left-col">Total Items:</td>
                <td class="right-col">${data.items.length}</td>
            </tr>
            <tr>
                <td class="left-col">Subtotal:</td>
                <td class="right-col">KSh ${data.subtotal.toFixed(2)}</td>
            </tr>

            ${
              data.totalDiscount && data.totalDiscount > 0
                ? `
            <tr>
                <td class="left-col" style="color: #e87c83;">Total Discount:</td>
                <td class="right-col" style="color: #e87c83;">-KSh ${data.totalDiscount.toFixed(
                  2
                )}</td>
            </tr>
            `
                : ""
            }

            ${
              data.shippingFee && data.shippingFee > 0
                ? `
            <tr>
                <td class="left-col">Delivery Fee:</td>
                <td class="right-col">KSh ${data.shippingFee.toFixed(2)}</td>
            </tr>
            `
                : ""
            }

            ${
              data.tax && data.tax > 0
                ? `
            <tr>
                <td class="left-col">Tax:</td>
                <td class="right-col">KSh ${data.tax.toFixed(2)}</td>
            </tr>
            `
                : ""
            }
        </table>
        
        <div class="grand-total">
            <table class="two-column">
                <tr>
                    <td class="left-col bold">GRAND TOTAL:</td>
                    <td class="right-col bold">KSh ${data.grandTotal.toFixed(
                      2
                    )}</td>
                </tr>
            </table>
            ${
              data.grandTotalInWords
                ? `
            <div class="center small mt-2">(${data.grandTotalInWords})</div>
            `
                : ""
            }
        </div>
    </div>

    <!-- Payment Section -->
    <div class="payment-section">
        <div class="center bold mb-2">PAYMENT DETAILS</div>
        ${data.paymentMethods
          .map(
            (method) => `
        <table class="two-column">
            <tr>
                <td class="left-col">${method.method}${
              method.date ? ` (${method.date})` : ""
            }:</td>
                <td class="right-col bold">KSh ${method.amount.toFixed(2)}</td>
            </tr>
            ${
              method.transactionCode
                ? `
            <tr>
                <td class="left-col small">Transaction Code:</td>
                <td class="right-col small">${method.transactionCode}</td>
            </tr>
            `
                : ""
            }
            ${
              method.phoneNumber
                ? `
            <tr>
                <td class="left-col small">Phone:</td>
                <td class="right-col small">${method.phoneNumber}</td>
            </tr>
            `
                : ""
            }
        </table>
        `
          )
          .join("")}
        
        <div class="separator mt-2"></div>
        <table class="two-column">
            <tr>
                <td class="left-col bold">Total Paid:</td>
                <td class="right-col bold">KSh ${data.totalPaid.toFixed(2)}</td>
            </tr>
            ${
              data.change && data.change > 0
                ? `
            <tr>
                <td class="left-col">Change:</td>
                <td class="right-col">KSh ${data.change.toFixed(2)}</td>
            </tr>
            `
                : ""
            }
        </table>
    </div>

    <!-- Sales Agent Section -->
    ${
      data.salesAgent
        ? `
    <div class="mt-3">
        <table class="two-column">
            <tr>
                <td class="left-col">Sales Agent:</td>
                <td class="right-col">${data.salesAgent.name}</td>
            </tr>
        </table>
    </div>
    `
        : ""
    }

    <!-- Delivery Section (tracking only, no delivery method display) -->
    ${
      data.delivery && data.delivery.trackingNumber
        ? `
    <div class="mt-3">
        <table class="two-column">
            <tr>
                <td class="left-col">Tracking:</td>
                <td class="right-col">${data.delivery.trackingNumber}</td>
            </tr>
        </table>
    </div>
    `
        : ""
    }

    <!-- Loyalty Section (Enhanced) -->
    ${
      data.loyalty && data.loyalty.totalPoints > 0
        ? `
    <div class="loyalty-section">
        <div class="loyalty-title bold">🌟 LOYALTY REWARDS 🌟</div>
        <table class="two-column">
            <tr>
                <td class="left-col">Total TS Points:</td>
                <td class="right-col bold">${data.loyalty.totalPoints.toLocaleString()}</td>
            </tr>
        </table>
    </div>
    `
        : ""
    }

    <!-- Footer -->
    <div class="receipt-footer">
        <div class="center bold">You Are Treasured</div>
        <div class="center small mt-2">Thank you for shopping with us!</div>
    </div>
    `;
  }

  /**
   * Generate text receipt
   */
  static generateText(data: ReceiptTemplateData, width: number = 32): string {
    return UniversalReceiptStyler.generateTextReceipt(data, width);
  }

  /**
   * Convert StandardizedReceiptData to ReceiptTemplateData
   */
  static fromStandardizedData(standardizedData: any): ReceiptTemplateData {
    return {
      storeName: standardizedData.store.name,
      storeAddress: standardizedData.store.address,
      storePhone: standardizedData.store.mobile,
      storeEmail: standardizedData.store.email,
      storeWebsite: standardizedData.store.website,
      storeLogo: TREASURED_LOGO_BASE64, // Include Treasured Scents logo from constants

      receiptNumber: standardizedData.receiptNumber,
      date: standardizedData.date,
      time: standardizedData.time,
      staffName: standardizedData.staff.name,

      customerName: standardizedData.customer.name,
      customerPhone: standardizedData.customer.mobile,
      customerEmail: standardizedData.customer.email,

      items: standardizedData.items.map((item: any) => ({
        number: item.number,
        name: item.name,
        variant: item.variant,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        // ✅ CRITICAL FIX: Include discount information in template
        discount: item.discount
          ? {
              type: item.discount.type,
              amount: item.discount.amount,
              discountAmount: item.discount.discountAmount || 0,
            }
          : undefined,
      })),

      subtotal: standardizedData.totals.subtotal,
      // ✅ CRITICAL FIX: Calculate total discount from all items
      totalDiscount: standardizedData.items.reduce(
        (total: number, item: any) =>
          total + (item.discount?.discountAmount || 0),
        0
      ),
      shippingFee:
        standardizedData.totals.shippingCharges > 0
          ? standardizedData.totals.shippingCharges
          : standardizedData.fulfillment?.shippingFee > 0
          ? standardizedData.fulfillment.shippingFee
          : undefined,
      tax: standardizedData.totals.tax,
      grandTotal: standardizedData.totals.grandTotal,
      grandTotalInWords: standardizedData.totals.grandTotalInWords,

      paymentMethods: standardizedData.payment.methods.map((method: any) => ({
        method: method.method,
        amount: method.amount,
        date: method.date,
        transactionCode: method.transactionCode,
        phoneNumber: method.phoneNumber,
      })),
      totalPaid: standardizedData.payment.totalPaid,
      change: standardizedData.payment.change,

      loyalty: standardizedData.loyalty
        ? {
            totalPoints: standardizedData.loyalty.totalPoints,
          }
        : undefined,

      salesAgent: standardizedData.salesAgent,

      delivery:
        standardizedData.fulfillment &&
        standardizedData.fulfillment.deliveryMethod !== "local_pickup"
          ? {
              method: standardizedData.fulfillment.deliveryMethod,
              trackingNumber: standardizedData.fulfillment.trackingNumber,
              shippingFee: standardizedData.fulfillment.shippingFee,
            }
          : undefined,
    };
  }
}
