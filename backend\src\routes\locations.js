const express = require("express");
const router = express.Router();
const { authenticateToken } = require("../middleware/auth");
const shopifyService = require("../services/shopify-service");

// Get all locations
router.get("/", authenticateToken, async (req, res) => {
  try {
    if (!shopifyService.isStoreConnected()) {
      return res.status(400).json({
        success: false,
        error: "Store not connected to Shopify",
      });
    }

    const result = await shopifyService.getLocations();

    if (result.success) {
      res.json({
        success: true,
        data: {
          locations: result.locations,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get locations error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch locations",
    });
  }
});

// Get location-specific inventory for multiple variants
router.post("/inventory", authenticateToken, async (req, res) => {
  try {
    const { locationId, variantIds } = req.body;

    if (!locationId || !variantIds || !Array.isArray(variantIds)) {
      return res.status(400).json({
        success: false,
        error: "Location ID and variant IDs array are required",
      });
    }

    if (!shopifyService.isStoreConnected()) {
      return res.status(400).json({
        success: false,
        error: "Store not connected to Shopify",
      });
    }

    const result = await shopifyService.getLocationInventory(
      locationId,
      variantIds
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          locationId,
          inventory: result.locationInventory,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get location inventory error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch location inventory",
    });
  }
});

// Get inventory for a single variant across all locations
router.get("/inventory/:variantId", authenticateToken, async (req, res) => {
  try {
    const { variantId } = req.params;

    if (!shopifyService.isStoreConnected()) {
      return res.status(400).json({
        success: false,
        error: "Store not connected to Shopify",
      });
    }

    const result = await shopifyService.getVariantInventoryAcrossLocations(
      variantId
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          variantId,
          inventoryLevels: result.inventoryLevels,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get variant inventory across locations error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch variant inventory across locations",
    });
  }
});

// Get location details by ID
router.get("/:locationId", authenticateToken, async (req, res) => {
  try {
    const { locationId } = req.params;

    if (!shopifyService.isStoreConnected()) {
      return res.status(400).json({
        success: false,
        error: "Store not connected to Shopify",
      });
    }

    const result = await shopifyService.getLocations();

    if (result.success) {
      const location = result.locations.find(
        (loc) => loc.id.toString() === locationId
      );

      if (location) {
        res.json({
          success: true,
          data: {
            location,
          },
        });
      } else {
        res.status(404).json({
          success: false,
          error: "Location not found",
        });
      }
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get location details error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch location details",
    });
  }
});

// Test endpoint to validate location-based operations
router.post(
  "/test-location-operations",
  authenticateToken,
  async (req, res) => {
    try {
      const { locationId, variantIds } = req.body;

      if (!shopifyService.isStoreConnected()) {
        return res.status(400).json({
          success: false,
          error: "Store not connected to Shopify",
        });
      }

      // Get all locations
      const locationsResult = await shopifyService.getLocations();
      if (!locationsResult.success) {
        return res.status(400).json({
          success: false,
          error: "Failed to fetch locations",
        });
      }

      // Find the specified location
      const targetLocation = locationsResult.locations.find(
        (loc) => loc.id.toString() === locationId.toString()
      );

      if (!targetLocation) {
        return res.status(404).json({
          success: false,
          error: "Location not found",
        });
      }

      // Get inventory for the location if variant IDs provided
      let inventoryData = null;
      if (variantIds && variantIds.length > 0) {
        const inventoryResult = await shopifyService.getLocationInventory(
          locationId,
          variantIds
        );
        if (inventoryResult.success) {
          inventoryData = inventoryResult.locationInventory;
        }
      }

      res.json({
        success: true,
        data: {
          location: targetLocation,
          inventory: inventoryData,
          message: `Location operations test successful for ${targetLocation.name}`,
        },
      });
    } catch (error) {
      console.error("Test location operations error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to test location operations",
      });
    }
  }
);

module.exports = router;
