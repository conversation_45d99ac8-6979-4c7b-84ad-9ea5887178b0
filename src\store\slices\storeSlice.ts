import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { getAPIClient } from '../../services/api/dukalink-client';
import { ShopifyStore } from '../../types/shopify';

interface StoreState {
  connectedStores: ShopifyStore[];
  currentStore: ShopifyStore | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: StoreState = {
  connectedStores: [],
  currentStore: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const getConnectedStores = createAsyncThunk(
  'store/getConnectedStores',
  async (_, { rejectWithValue }) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getConnectedStores();
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch stores');
      }
      
      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const getStore = createAsyncThunk(
  'store/getStore',
  async (storeId: string, { rejectWithValue }) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getStore(storeId);
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch store');
      }
      
      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

const storeSlice = createSlice({
  name: 'store',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentStore: (state, action: PayloadAction<ShopifyStore>) => {
      state.currentStore = action.payload;
    },
    clearCurrentStore: (state) => {
      state.currentStore = null;
    },
  },
  extraReducers: (builder) => {
    // Get Connected Stores
    builder
      .addCase(getConnectedStores.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getConnectedStores.fulfilled, (state, action) => {
        state.isLoading = false;
        state.connectedStores = action.payload;
      })
      .addCase(getConnectedStores.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Get Store
    builder
      .addCase(getStore.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getStore.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentStore = action.payload;
      })
      .addCase(getStore.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentStore, clearCurrentStore } = storeSlice.actions;
export default storeSlice.reducer;
