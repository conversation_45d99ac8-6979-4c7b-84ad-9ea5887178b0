/**
 * Ticket Management Redux Slice
 *
 * Manages multiple ticket sessions with full CRUD operations,
 * auto-save functionality, and seamless integration with existing cart system.
 */

import { createSlice, PayloadAction, createSelector } from "@reduxjs/toolkit";
import {
  <PERSON><PERSON>,
  CartItem,
  CartItemDiscount,
  Customer,
  OrderDiscount,
  Salesperson,
} from "../../types/shopify";

// Ticket interface extending Cart with additional metadata
export interface Ticket extends Cart {
  id: string;
  name: string;
  staffId: string;
  terminalId?: string;
  locationId?: string;
  status:
    | "active"
    | "paused"
    | "completed"
    | "cancelled"
    | "expired"
    | "archived";
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  isLocal?: boolean; // True for tickets not yet saved to server
  isDirty?: boolean; // True when ticket has unsaved changes
}

interface TicketState {
  tickets: Ticket[];
  activeTicketId: string | null;
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  lastSyncAt?: string;
  autoSaveEnabled: boolean;
  maxTickets: number;
}

const initialState: TicketState = {
  tickets: [],
  activeTicketId: null,
  isLoading: false,
  isSaving: false,
  error: null,
  autoSaveEnabled: true,
  maxTickets: 10, // Maximum number of concurrent tickets
};

// Helper function to create a new ticket
const createNewTicket = (
  name: string = "New Ticket",
  staffId: string,
  terminalId?: string,
  locationId?: string
): Ticket => {
  const now = new Date().toISOString();
  return {
    id: `ticket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name,
    staffId,
    terminalId,
    locationId,
    items: [],
    subtotal: 0,
    tax: 0,
    total: 0,
    discounts: [],
    customer: undefined,
    note: undefined,
    status: "active",
    createdAt: now,
    updatedAt: now,
    isLocal: true,
    isDirty: true, // Mark as dirty so it gets saved immediately
  };
};

// Helper function to calculate ticket totals
const calculateTicketTotals = (ticket: Ticket): Ticket => {
  // Calculate subtotal with item-level discounts
  let subtotalBeforeOrderDiscounts = 0;

  ticket.items.forEach((item) => {
    const lineTotal = parseFloat(item.price) * item.quantity;
    let itemDiscountAmount = 0;

    // Apply item-level discount
    if (item.discount && item.discount.amount > 0) {
      if (item.discount.type === "percentage") {
        itemDiscountAmount = (lineTotal * item.discount.amount) / 100;
      } else {
        itemDiscountAmount = Math.min(item.discount.amount, lineTotal);
      }
    }

    subtotalBeforeOrderDiscounts += Math.max(0, lineTotal - itemDiscountAmount);
  });

  const subtotal = subtotalBeforeOrderDiscounts;

  // Apply order-level discounts
  let orderDiscountAmount = 0;
  ticket.discounts.forEach((discount) => {
    if (discount.type === "percentage") {
      orderDiscountAmount += subtotal * (parseFloat(discount.amount) / 100);
    } else {
      orderDiscountAmount += parseFloat(discount.amount);
    }
  });

  const finalTotal = Math.max(0, subtotal - orderDiscountAmount);

  return {
    ...ticket,
    subtotal: Math.round(subtotal * 100) / 100,
    tax: 0, // Always 0 for this POS system
    total: Math.round(finalTotal * 100) / 100,
    updatedAt: new Date().toISOString(),
    isDirty: true,
  };
};

const ticketSlice = createSlice({
  name: "tickets",
  initialState,
  reducers: {
    // Ticket management actions
    createTicket: (
      state,
      action: PayloadAction<{
        name?: string;
        staffId: string;
        terminalId?: string;
        locationId?: string;
      }>
    ) => {
      const { name, staffId, terminalId, locationId } = action.payload;

      // Check max tickets limit
      if (state.tickets.length >= state.maxTickets) {
        state.error = `Maximum of ${state.maxTickets} tickets allowed`;
        return;
      }

      const newTicket = createNewTicket(name, staffId, terminalId, locationId);
      state.tickets.push(newTicket);
      state.activeTicketId = newTicket.id;
      state.error = null;
    },

    setActiveTicket: (state, action: PayloadAction<string>) => {
      const ticketId = action.payload;
      const ticket = state.tickets.find((t) => t.id === ticketId);

      if (ticket) {
        state.activeTicketId = ticketId;
        state.error = null;
      } else {
        state.error = "Ticket not found";
      }
    },

    updateTicketName: (
      state,
      action: PayloadAction<{ ticketId: string; name: string }>
    ) => {
      const { ticketId, name } = action.payload;
      const ticket = state.tickets.find((t) => t.id === ticketId);

      if (ticket) {
        ticket.name = name.trim() || "Unnamed Ticket";
        ticket.updatedAt = new Date().toISOString();
        ticket.isDirty = true;
      }
    },

    deleteTicket: (state, action: PayloadAction<string>) => {
      const ticketId = action.payload;
      const ticketIndex = state.tickets.findIndex((t) => t.id === ticketId);

      if (ticketIndex !== -1) {
        state.tickets.splice(ticketIndex, 1);

        // If deleted ticket was active, switch to another ticket or clear active
        if (state.activeTicketId === ticketId) {
          const remainingTickets = state.tickets.filter(
            (t) => t.status === "active"
          );
          state.activeTicketId =
            remainingTickets.length > 0 ? remainingTickets[0].id : null;
        }
      }
    },

    updateTicketStatus: (
      state,
      action: PayloadAction<{
        ticketId: string;
        status:
          | "active"
          | "paused"
          | "completed"
          | "cancelled"
          | "expired"
          | "archived";
      }>
    ) => {
      const { ticketId, status } = action.payload;
      const ticket = state.tickets.find((t) => t.id === ticketId);

      if (ticket) {
        ticket.status = status;
        ticket.updatedAt = new Date().toISOString();
        ticket.isDirty = true;

        // If pausing the active ticket, switch to another active ticket
        if (status === "paused" && state.activeTicketId === ticketId) {
          const remainingActiveTickets = state.tickets.filter(
            (t) => t.id !== ticketId && t.status === "active"
          );
          state.activeTicketId =
            remainingActiveTickets.length > 0
              ? remainingActiveTickets[0].id
              : null;
        }
      }
    },

    duplicateTicket: (
      state,
      action: PayloadAction<{ originalTicketId: string; newName?: string }>
    ) => {
      const { originalTicketId, newName } = action.payload;
      const originalTicket = state.tickets.find(
        (t) => t.id === originalTicketId
      );

      if (originalTicket) {
        const newTicketId = `ticket_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;
        const duplicatedTicket: Ticket = {
          ...originalTicket,
          id: newTicketId,
          name: newName || `${originalTicket.name} (Copy)`,
          status: "active",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          isLocal: true,
          isDirty: true,
          // Reset customer and salesperson for new ticket
          customer: undefined,
          // Clear any order-specific data
          note: undefined,
        };

        state.tickets.push(duplicatedTicket);
        // Optionally set as active ticket
        state.activeTicketId = newTicketId;
      }
    },

    // ATOMIC: Add item to ticket with auto-creation if needed
    addItemToTicketWithAutoCreate: (
      state,
      action: PayloadAction<
        CartItem & {
          staffId?: string;
          terminalId?: string;
          locationId?: string;
        }
      >
    ) => {
      let activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );

      // Create ticket if none exists
      if (!activeTicket || !state.activeTicketId) {
        const { staffId, terminalId, locationId, ...item } = action.payload;

        if (staffId) {
          const ticketName = `Order ${new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}`;

          const newTicket = createNewTicket(
            ticketName,
            staffId,
            terminalId,
            locationId
          );
          state.tickets.push(newTicket);
          state.activeTicketId = newTicket.id;
          activeTicket = newTicket;

          console.log(`🎫 Auto-created ticket ${newTicket.id}: ${ticketName}`);
        } else {
          state.error = "Cannot create ticket: No staff ID provided";
          return;
        }
      }

      // Add item to the active ticket
      const existingItem = activeTicket.items.find(
        (item) => item.variantId === action.payload.variantId
      );

      if (existingItem) {
        // Check inventory before adding
        if (
          existingItem.quantity + action.payload.quantity <=
          existingItem.inventoryQuantity
        ) {
          existingItem.quantity += action.payload.quantity;
        } else {
          state.error = "Insufficient inventory";
          return;
        }
      } else {
        // Check inventory for new item
        if (action.payload.quantity <= action.payload.inventoryQuantity) {
          const { staffId, terminalId, locationId, ...item } = action.payload;
          activeTicket.items.push(item);
        } else {
          state.error = "Insufficient inventory";
          return;
        }
      }

      // Recalculate totals and mark as dirty
      const updatedTicket = calculateTicketTotals(activeTicket);
      Object.assign(activeTicket, updatedTicket);
      activeTicket.updatedAt = new Date().toISOString();
      activeTicket.isDirty = true;
      state.error = null;

      console.log(
        `📦 Added item to ticket ${activeTicket.id}: ${action.payload.title}`
      );
    },

    // Cart operations on active ticket
    addItemToActiveTicket: (state, action: PayloadAction<CartItem>) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      const existingItem = activeTicket.items.find(
        (item) => item.variantId === action.payload.variantId
      );

      if (existingItem) {
        // Check inventory before adding
        if (
          existingItem.quantity + action.payload.quantity <=
          existingItem.inventoryQuantity
        ) {
          existingItem.quantity += action.payload.quantity;
        } else {
          state.error = "Insufficient inventory";
          return;
        }
      } else {
        // Check inventory for new item
        if (action.payload.quantity <= action.payload.inventoryQuantity) {
          activeTicket.items.push(action.payload);
        } else {
          state.error = "Insufficient inventory";
          return;
        }
      }

      // Recalculate totals
      const updatedTicket = calculateTicketTotals(activeTicket);
      Object.assign(activeTicket, updatedTicket);
      state.error = null;
    },

    removeItemFromActiveTicket: (state, action: PayloadAction<string>) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      activeTicket.items = activeTicket.items.filter(
        (item) => item.variantId !== action.payload
      );

      // Recalculate totals
      const updatedTicket = calculateTicketTotals(activeTicket);
      Object.assign(activeTicket, updatedTicket);
    },

    updateItemQuantityInActiveTicket: (
      state,
      action: PayloadAction<{ variantId: string; quantity: number }>
    ) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      const { variantId, quantity } = action.payload;
      const item = activeTicket.items.find(
        (item) => item.variantId === variantId
      );

      if (item) {
        if (quantity <= 0) {
          activeTicket.items = activeTicket.items.filter(
            (item) => item.variantId !== variantId
          );
        } else if (quantity <= item.inventoryQuantity) {
          item.quantity = quantity;
        } else {
          state.error = "Insufficient inventory";
          return;
        }

        // Recalculate totals
        const updatedTicket = calculateTicketTotals(activeTicket);
        Object.assign(activeTicket, updatedTicket);
        state.error = null;
      }
    },

    applyItemDiscountInActiveTicket: (
      state,
      action: PayloadAction<{
        variantId: string;
        discount: CartItemDiscount | null;
      }>
    ) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      const { variantId, discount } = action.payload;
      const item = activeTicket.items.find(
        (item) => item.variantId === variantId
      );

      if (item) {
        item.discount = discount || undefined;

        // Recalculate totals
        const updatedTicket = calculateTicketTotals(activeTicket);
        Object.assign(activeTicket, updatedTicket);
      }
    },

    updateItemNotesInActiveTicket: (
      state,
      action: PayloadAction<{ variantId: string; notes: string }>
    ) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      const { variantId, notes } = action.payload;
      const item = activeTicket.items.find(
        (item) => item.variantId === variantId
      );

      if (item) {
        item.notes = notes.trim() || undefined;
        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;
      }
    },

    updateItemPriceInActiveTicket: (
      state,
      action: PayloadAction<{
        variantId: string;
        newPrice: number;
        originalPrice?: number | null;
      }>
    ) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      const { variantId, newPrice, originalPrice } = action.payload;
      const item = activeTicket.items.find(
        (item) => item.variantId === variantId
      );

      if (item) {
        // Store original price if not already stored
        if (originalPrice !== null && !(item as any).originalPrice) {
          (item as any).originalPrice = originalPrice;
        }

        // Update the price
        item.price = newPrice.toString();

        // If resetting to original price, clear the originalPrice field
        if (originalPrice === null) {
          delete (item as any).originalPrice;
        }

        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;

        // Recalculate totals
        const updatedTicket = calculateTicketTotals(activeTicket);
        Object.assign(activeTicket, updatedTicket);
      }
    },

    setActiveTicketCustomer: (
      state,
      action: PayloadAction<Customer | undefined>
    ) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (activeTicket) {
        activeTicket.customer = action.payload;
        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;
      }
    },

    setActiveTicketSalesperson: (
      state,
      action: PayloadAction<Salesperson | null>
    ) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (activeTicket) {
        // Store salesperson in a custom field since Cart interface doesn't have it
        (activeTicket as any).selectedSalesperson = action.payload;
        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;
      }
    },

    // Resume customer ticket (replace current cart)
    resumeCustomerTicket: (state, action: PayloadAction<string>) => {
      const ticketId = action.payload;
      const ticket = state.tickets.find((t) => t.id === ticketId);

      if (ticket) {
        // Set as active ticket
        state.activeTicketId = ticketId;
        ticket.status = "active";
        ticket.updatedAt = new Date().toISOString();
        ticket.isDirty = true;
      }
    },

    // Merge customer ticket with current cart
    mergeCustomerTicket: (state, action: PayloadAction<string>) => {
      const sourceTicketId = action.payload;
      const sourceTicket = state.tickets.find((t) => t.id === sourceTicketId);
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );

      if (sourceTicket && activeTicket && sourceTicket.id !== activeTicket.id) {
        // Merge items from source ticket to active ticket
        sourceTicket.items.forEach((sourceItem) => {
          const existingItemIndex = activeTicket.items.findIndex(
            (item) => item.variantId === sourceItem.variantId
          );

          if (existingItemIndex >= 0) {
            // Item exists, add quantities
            activeTicket.items[existingItemIndex].quantity +=
              sourceItem.quantity;
          } else {
            // New item, add to cart
            activeTicket.items.push({ ...sourceItem });
          }
        });

        // Recalculate totals
        const subtotal = activeTicket.items.reduce(
          (sum, item) => sum + parseFloat(item.price) * item.quantity,
          0
        );
        activeTicket.subtotal = subtotal;
        activeTicket.total = subtotal; // Tax is always 0
        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;

        // Mark source ticket as completed/merged
        sourceTicket.status = "completed";
        sourceTicket.updatedAt = new Date().toISOString();
        sourceTicket.isDirty = true;
      }
    },

    addDiscountToActiveTicket: (
      state,
      action: PayloadAction<OrderDiscount>
    ) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      // Remove existing discount with same code if any
      if (action.payload.code) {
        activeTicket.discounts = activeTicket.discounts.filter(
          (discount) => discount.code !== action.payload.code
        );
      }

      activeTicket.discounts.push(action.payload);

      // Recalculate totals
      const updatedTicket = calculateTicketTotals(activeTicket);
      Object.assign(activeTicket, updatedTicket);
    },

    removeDiscountFromActiveTicket: (state, action: PayloadAction<string>) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (!activeTicket) {
        state.error = "No active ticket";
        return;
      }

      activeTicket.discounts = activeTicket.discounts.filter(
        (discount) => discount.id !== action.payload
      );

      // Recalculate totals
      const updatedTicket = calculateTicketTotals(activeTicket);
      Object.assign(activeTicket, updatedTicket);
    },

    setActiveTicketNote: (state, action: PayloadAction<string>) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (activeTicket) {
        activeTicket.note = action.payload;
        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;
      }
    },

    // Utility actions
    clearActiveTicket: (state) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (activeTicket) {
        activeTicket.items = [];
        activeTicket.subtotal = 0;
        activeTicket.tax = 0;
        activeTicket.total = 0;
        activeTicket.discounts = [];
        activeTicket.customer = undefined;
        activeTicket.note = undefined;
        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;
      }
    },

    markTicketAsSaved: (
      state,
      action: PayloadAction<{ ticketId: string; isLocal?: boolean }>
    ) => {
      const { ticketId, isLocal = false } = action.payload;
      const ticket = state.tickets.find((t) => t.id === ticketId);
      if (ticket) {
        ticket.isLocal = isLocal;
        ticket.isDirty = false;
        ticket.updatedAt = new Date().toISOString();
      }
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setSaving: (state, action: PayloadAction<boolean>) => {
      state.isSaving = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    clearError: (state) => {
      state.error = null;
    },

    setAutoSave: (state, action: PayloadAction<boolean>) => {
      state.autoSaveEnabled = action.payload;
    },

    updateLastSync: (state) => {
      state.lastSyncAt = new Date().toISOString();
    },

    completeActiveTicket: (state) => {
      const activeTicket = state.tickets.find(
        (t) => t.id === state.activeTicketId
      );
      if (activeTicket) {
        activeTicket.status = "completed";
        activeTicket.updatedAt = new Date().toISOString();
        activeTicket.isDirty = true;

        // Clear the active ticket and switch to another active ticket if available
        const remainingActiveTickets = state.tickets.filter(
          (t) => t.id !== state.activeTicketId && t.status === "active"
        );

        if (remainingActiveTickets.length > 0) {
          state.activeTicketId = remainingActiveTickets[0].id;
        } else {
          state.activeTicketId = null;
        }
      }
    },
  },
  extraReducers: (builder) => {
    // Import async thunks dynamically to avoid circular dependencies
    const {
      saveTicket,
      loadTickets,
      deleteTicketFromBackend,
      autoSaveDirtyTickets,
      syncTickets,
      batchSaveTickets,
      loadTicketById,
      updateTicketStatus,
      autoSaveTicket,
      batchAutoSaveTickets,
      getRecoveryTickets,
      getAutoSaveStats,
    } = require("../thunks/ticketThunks");

    // Save ticket
    builder
      .addCase(saveTicket.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(saveTicket.fulfilled, (state, action) => {
        state.isSaving = false;
        const savedTicket = action.payload;

        // For local tickets, we need to find by the original local ID
        // since the backend might assign a new ID
        const originalTicket = action.meta.arg.ticket;
        const existingIndex = state.tickets.findIndex(
          (t) => t.id === originalTicket.id
        );

        if (existingIndex !== -1) {
          // ATOMIC UPDATE: Update both ticket data and active ticket ID in one operation
          const wasActive = state.activeTicketId === originalTicket.id;
          const idChanged = savedTicket.id !== originalTicket.id;

          // Update existing ticket with the saved data
          state.tickets[existingIndex] = {
            ...savedTicket,
            isLocal: false,
            isDirty: false,
          };

          // Update active ticket ID atomically if this was the active ticket and ID changed
          if (wasActive && idChanged) {
            state.activeTicketId = savedTicket.id;
            console.log(
              `🔄 Ticket ID updated atomically: ${originalTicket.id} → ${savedTicket.id}`
            );
          }
        } else {
          // Add new ticket (shouldn't happen in normal flow)
          state.tickets.push({
            ...savedTicket,
            isLocal: false,
            isDirty: false,
          });
        }

        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(saveTicket.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload || "Failed to save ticket";
      });

    // Load tickets
    builder
      .addCase(loadTickets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadTickets.fulfilled, (state, action) => {
        state.isLoading = false;
        const serverTickets = action.payload;

        // Merge server tickets with local tickets
        const mergedTickets = [...state.tickets];

        serverTickets.forEach((serverTicket) => {
          const existingIndex = mergedTickets.findIndex(
            (t) => t.id === serverTicket.id
          );
          if (existingIndex !== -1) {
            // Update existing ticket if server version is newer
            const localTicket = mergedTickets[existingIndex];
            if (
              !localTicket.isDirty ||
              serverTicket.updatedAt > localTicket.updatedAt
            ) {
              mergedTickets[existingIndex] = {
                ...serverTicket,
                isLocal: false,
                isDirty: false,
              };
            }
          } else {
            // Add new ticket from server
            mergedTickets.push({
              ...serverTicket,
              isLocal: false,
              isDirty: false,
            });
          }
        });

        state.tickets = mergedTickets;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(loadTickets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || "Failed to load tickets";
      });

    // Delete ticket
    builder
      .addCase(deleteTicketFromBackend.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteTicketFromBackend.fulfilled, (state, action) => {
        state.isLoading = false;
        const deletedTicketId = action.payload;
        state.tickets = state.tickets.filter((t) => t.id !== deletedTicketId);

        // If deleted ticket was active, clear active ticket
        if (state.activeTicketId === deletedTicketId) {
          state.activeTicketId = null;
        }
      })
      .addCase(deleteTicketFromBackend.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || "Failed to delete ticket";
      });

    // Auto-save dirty tickets
    builder
      .addCase(autoSaveDirtyTickets.pending, (state) => {
        state.isSaving = true;
      })
      .addCase(autoSaveDirtyTickets.fulfilled, (state, action) => {
        state.isSaving = false;
        const savedTickets = action.payload;

        savedTickets.forEach((savedTicket) => {
          const existingIndex = state.tickets.findIndex(
            (t) => t.id === savedTicket.id
          );
          if (existingIndex !== -1) {
            state.tickets[existingIndex] = {
              ...savedTicket,
              isLocal: false,
              isDirty: false,
            };
          }
        });

        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(autoSaveDirtyTickets.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload || "Auto-save failed";
      });

    // Sync tickets
    builder
      .addCase(syncTickets.pending, (state) => {
        state.isLoading = true;
        state.isSaving = true;
        state.error = null;
      })
      .addCase(syncTickets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSaving = false;
        const { serverTickets } = action.payload;

        // Replace all tickets with server tickets (conflicts should be resolved separately)
        state.tickets = serverTickets.map((ticket) => ({
          ...ticket,
          isLocal: false,
          isDirty: false,
        }));

        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(syncTickets.rejected, (state, action) => {
        state.isLoading = false;
        state.isSaving = false;
        state.error = action.payload || "Sync failed";
      });

    // Auto-save ticket
    builder
      .addCase(autoSaveTicket.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(autoSaveTicket.fulfilled, (state, action) => {
        state.isSaving = false;
        const { ticketId, timestamp } = action.payload;

        const ticket = state.tickets.find((t) => t.id === ticketId);
        if (ticket) {
          ticket.isDirty = false;
          ticket.updatedAt = timestamp;
        }
      })
      .addCase(autoSaveTicket.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload || "Auto-save failed";
      });

    // Batch auto-save tickets
    builder
      .addCase(batchAutoSaveTickets.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(batchAutoSaveTickets.fulfilled, (state, action) => {
        state.isSaving = false;
        const { results } = action.payload;

        results.forEach((result: any) => {
          if (result.success) {
            const ticket = state.tickets.find((t) => t.id === result.ticketId);
            if (ticket) {
              ticket.isDirty = false;
              ticket.updatedAt = result.timestamp;
            }
          }
        });
      })
      .addCase(batchAutoSaveTickets.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload || "Batch auto-save failed";
      });

    // Get recovery tickets
    builder
      .addCase(getRecoveryTickets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getRecoveryTickets.fulfilled, (state, action) => {
        state.isLoading = false;
        // Recovery tickets are handled separately, don't merge with current tickets
      })
      .addCase(getRecoveryTickets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || "Failed to get recovery tickets";
      });

    // Get auto-save stats
    builder
      .addCase(getAutoSaveStats.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAutoSaveStats.fulfilled, (state) => {
        state.isLoading = false;
        // Stats are handled separately, don't affect ticket state
      })
      .addCase(getAutoSaveStats.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || "Failed to get auto-save stats";
      });
  },
});

export const {
  createTicket,
  setActiveTicket,
  updateTicketName,
  updateTicketStatus,
  duplicateTicket,
  deleteTicket,
  addItemToTicketWithAutoCreate,
  addItemToActiveTicket,
  removeItemFromActiveTicket,
  updateItemQuantityInActiveTicket,
  applyItemDiscountInActiveTicket,
  updateItemNotesInActiveTicket,
  updateItemPriceInActiveTicket,
  setActiveTicketCustomer,
  setActiveTicketSalesperson,
  resumeCustomerTicket,
  mergeCustomerTicket,
  addDiscountToActiveTicket,
  removeDiscountFromActiveTicket,
  setActiveTicketNote,
  clearActiveTicket,
  markTicketAsSaved,
  setLoading,
  setSaving,
  setError,
  clearError,
  setAutoSave,
  updateLastSync,
  completeActiveTicket,
} = ticketSlice.actions;

export default ticketSlice.reducer;

// Selectors
export const selectAllTickets = (state: { tickets: TicketState }) =>
  state.tickets.tickets;
export const selectActiveTicketId = (state: { tickets: TicketState }) =>
  state.tickets.activeTicketId;
// OPTIMIZED: Memoized selector with stable object references during ID transitions
export const selectActiveTicket = createSelector(
  [
    (state: { tickets: TicketState }) => state.tickets.activeTicketId,
    (state: { tickets: TicketState }) => state.tickets.tickets,
  ],
  (activeId, tickets) => {
    if (!activeId) return null;

    // First try to find by exact ID match
    let ticket = tickets.find((t) => t.id === activeId);

    // Enhanced fallback logic for ID transitions
    if (!ticket) {
      // If activeId looks like a local ID, find the corresponding backend ticket
      if (activeId.startsWith("ticket_")) {
        // Find the most recently updated active ticket as fallback
        const activeTickets = tickets.filter(
          (t) => t.status === "active" && !t.isLocal
        );

        if (activeTickets.length === 1) {
          ticket = activeTickets[0];
          console.log(
            `🔄 Fallback: Found single active ticket ${ticket.id} for local ID ${activeId}`
          );
        } else if (activeTickets.length > 1) {
          // Get the most recently updated one
          ticket = activeTickets.reduce((latest, current) =>
            new Date(current.updatedAt) > new Date(latest.updatedAt)
              ? current
              : latest
          );
          console.log(
            `🔄 Fallback: Found most recent active ticket ${ticket.id} for local ID ${activeId}`
          );
        }
      } else {
        // For backend IDs, try to find any active ticket if exact match fails
        const activeTickets = tickets.filter((t) => t.status === "active");
        if (activeTickets.length === 1) {
          ticket = activeTickets[0];
          console.log(
            `🔄 Fallback: Found single active ticket ${ticket.id} for backend ID ${activeId}`
          );
        }
      }
    }

    return ticket || null;
  }
);

// Memoized selector for active ticket items to prevent unnecessary re-renders
export const selectActiveTicketItems = createSelector(
  [selectActiveTicket],
  (activeTicket) => activeTicket?.items || []
);

// FIXED: Memoized selectors to prevent unnecessary re-renders during ticket ID transitions
export const selectActiveTicketTotal = createSelector(
  [selectActiveTicket],
  (activeTicket) => activeTicket?.total || 0
);

export const selectActiveTicketSubtotal = createSelector(
  [selectActiveTicket],
  (activeTicket) => activeTicket?.subtotal || 0
);

export const selectActiveTicketItemCount = createSelector(
  [selectActiveTicket],
  (activeTicket) =>
    activeTicket?.items.reduce((total, item) => total + item.quantity, 0) || 0
);

export const selectActiveTicketCustomer = createSelector(
  [selectActiveTicket],
  (activeTicket) => activeTicket?.customer
);

export const selectActiveTicketSalesperson = createSelector(
  [selectActiveTicket],
  (activeTicket) => (activeTicket as any)?.selectedSalesperson
);

// Memoized selector for active ticket discounts to prevent unnecessary re-renders
export const selectActiveTicketDiscounts = createSelector(
  [selectActiveTicket],
  (activeTicket) => activeTicket?.discounts || []
);

export const selectActiveTicketNote = createSelector(
  [selectActiveTicket],
  (activeTicket) => activeTicket?.note
);

export const selectTicketsLoading = (state: { tickets: TicketState }) =>
  state.tickets.isLoading;
export const selectTicketsSaving = (state: { tickets: TicketState }) =>
  state.tickets.isSaving;
export const selectTicketsError = (state: { tickets: TicketState }) =>
  state.tickets.error;

export const selectTicketById =
  (ticketId: string) => (state: { tickets: TicketState }) => {
    return state.tickets.tickets.find((t) => t.id === ticketId);
  };

// Memoized selectors for filtered ticket arrays to prevent unnecessary re-renders
export const selectActiveTickets = createSelector(
  [(state: { tickets: TicketState }) => state.tickets.tickets],
  (tickets) => tickets.filter((t) => t.status === "active")
);

export const selectDirtyTickets = createSelector(
  [(state: { tickets: TicketState }) => state.tickets.tickets],
  (tickets) => tickets.filter((t) => t.isDirty)
);

export const selectLocalTickets = createSelector(
  [(state: { tickets: TicketState }) => state.tickets.tickets],
  (tickets) => tickets.filter((t) => t.isLocal)
);

// Memoized selector for ticket stats to prevent unnecessary re-renders
export const selectTicketStats = createSelector(
  [(state: { tickets: TicketState }) => state.tickets.tickets],
  (tickets) => ({
    total: tickets.length,
    active: tickets.filter((t) => t.status === "active").length,
    paused: tickets.filter((t) => t.status === "paused").length,
    dirty: tickets.filter((t) => t.isDirty).length,
    local: tickets.filter((t) => t.isLocal).length,
  })
);
