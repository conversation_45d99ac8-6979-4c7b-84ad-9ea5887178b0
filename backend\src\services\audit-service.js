/**
 * Comprehensive Audit Service
 *
 * Handles security event logging, user activity tracking,
 * and audit trail management for compliance and security monitoring.
 */

const crypto = require("crypto");
const mysql = require("mysql2/promise");

class AuditService {
  constructor(pool) {
    this.pool = pool;
    this.eventQueue = [];
    this.batchSize = 50;
    this.flushInterval = 5000; // 5 seconds
    this.isProcessing = false;

    // Start batch processing
    this.startBatchProcessor();
  }

  /**
   * Log security event
   */
  async logSecurityEvent(
    staffId,
    eventType,
    eventData = {},
    ipAddress = null,
    userAgent = null
  ) {
    const event = {
      id: crypto.randomUUID(),
      staffId,
      eventType,
      eventData: JSON.stringify(eventData),
      ipAddress,
      userAgent,
      timestamp: new Date(),
    };

    // Add to queue for batch processing
    this.eventQueue.push(event);

    // If queue is full, process immediately
    if (this.eventQueue.length >= this.batchSize) {
      setImmediate(() => this.processBatch());
    }

    return event.id;
  }

  /**
   * Log user action for audit trail
   */
  async logUserAction(
    staffId,
    action,
    resourceType,
    resourceId,
    details = {},
    sessionId = null,
    ipAddress = null
  ) {
    const actionData = {
      action,
      resourceType,
      resourceId,
      details,
      sessionId,
      timestamp: new Date().toISOString(),
    };

    return this.logSecurityEvent(staffId, "USER_ACTION", actionData, ipAddress);
  }

  /**
   * Log ticket modification
   */
  async logTicketAction(
    ticketId,
    staffId,
    action,
    details = {},
    ipAddress = null,
    userAgent = null
  ) {
    try {
      const auditId = crypto.randomUUID();

      await this.pool.execute(
        `INSERT INTO ticket_audit_log 
         (id, ticket_id, staff_id, action, details, ip_address, user_agent)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          auditId,
          ticketId,
          staffId,
          action,
          JSON.stringify(details),
          ipAddress,
          userAgent,
        ]
      );

      return auditId;
    } catch (error) {
      console.error("Ticket audit logging error:", error);
      // Don't throw - logging failures shouldn't break functionality
      return null;
    }
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(
    staffId,
    eventType,
    success,
    details = {},
    ipAddress = null,
    userAgent = null
  ) {
    const authData = {
      success,
      details,
      timestamp: new Date().toISOString(),
    };

    return this.logSecurityEvent(
      staffId,
      `AUTH_${eventType}`,
      authData,
      ipAddress,
      userAgent
    );
  }

  /**
   * Log permission changes
   */
  async logPermissionChange(
    targetStaffId,
    adminStaffId,
    action,
    permission,
    ipAddress = null
  ) {
    const permissionData = {
      targetStaffId,
      adminStaffId,
      action, // 'granted', 'revoked'
      permission,
      timestamp: new Date().toISOString(),
    };

    return this.logSecurityEvent(
      adminStaffId,
      "PERMISSION_CHANGE",
      permissionData,
      ipAddress
    );
  }

  /**
   * Log data access events
   */
  async logDataAccess(
    staffId,
    resourceType,
    resourceId,
    accessType,
    success = true,
    ipAddress = null
  ) {
    const accessData = {
      resourceType,
      resourceId,
      accessType, // 'read', 'write', 'delete'
      success,
      timestamp: new Date().toISOString(),
    };

    return this.logSecurityEvent(staffId, "DATA_ACCESS", accessData, ipAddress);
  }

  /**
   * Start batch processor for efficient logging
   */
  startBatchProcessor() {
    setInterval(() => {
      if (this.eventQueue.length > 0 && !this.isProcessing) {
        this.processBatch();
      }
    }, this.flushInterval);
  }

  /**
   * Process queued events in batch
   */
  async processBatch() {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const batch = this.eventQueue.splice(0, this.batchSize);

    try {
      // Prepare batch insert
      const values = batch.map((event) => [
        event.id,
        event.staffId,
        event.eventType,
        event.eventData,
        event.ipAddress,
        event.userAgent,
        event.timestamp,
      ]);

      // Batch insert
      await this.pool.query(
        `INSERT INTO pos_security_events 
         (id, staff_id, event_type, event_data, ip_address, user_agent, created_at)
         VALUES ?`,
        [values]
      );

      console.log(`Audit: Processed ${batch.length} security events`);
    } catch (error) {
      console.error("Batch audit processing error:", error);

      // Re-queue failed events (with limit to prevent infinite loops)
      if (batch.length < this.batchSize) {
        this.eventQueue.unshift(...batch);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get security events for a staff member
   */
  async getStaffSecurityEvents(staffId, limit = 100, eventTypes = null) {
    try {
      let query = `
        SELECT id, event_type, event_data, ip_address, created_at
        FROM pos_security_events 
        WHERE staff_id = ?
      `;

      const params = [staffId];

      if (eventTypes && eventTypes.length > 0) {
        query += ` AND event_type IN (${eventTypes.map(() => "?").join(",")})`;
        params.push(...eventTypes);
      }

      query += ` ORDER BY created_at DESC LIMIT ?`;
      params.push(limit);

      const [rows] = await this.pool.execute(query, params);

      return rows.map((row) => ({
        ...row,
        event_data: JSON.parse(row.event_data || "{}"),
      }));
    } catch (error) {
      console.error("Get staff security events error:", error);
      return [];
    }
  }

  /**
   * Get ticket audit trail
   */
  async getTicketAuditTrail(ticketId, limit = 50) {
    try {
      const [rows] = await this.pool.execute(
        `SELECT tal.*, ps.name as staff_name
         FROM ticket_audit_log tal
         JOIN pos_staff ps ON tal.staff_id = ps.id
         WHERE tal.ticket_id = ?
         ORDER BY tal.created_at DESC
         LIMIT ?`,
        [ticketId, limit]
      );

      return rows.map((row) => ({
        ...row,
        details: JSON.parse(row.details || "{}"),
      }));
    } catch (error) {
      console.error("Get ticket audit trail error:", error);
      return [];
    }
  }

  /**
   * Get user switch audit trail
   */
  async getUserSwitchAuditTrail(sessionId = null, staffId = null, limit = 50) {
    try {
      let query = `
        SELECT us.*, 
               from_staff.name as from_staff_name,
               to_staff.name as to_staff_name
        FROM pos_user_switches us
        LEFT JOIN pos_staff from_staff ON us.from_staff_id = from_staff.id
        JOIN pos_staff to_staff ON us.to_staff_id = to_staff.id
        WHERE 1=1
      `;

      const params = [];

      if (sessionId) {
        query += ` AND us.session_id = ?`;
        params.push(sessionId);
      }

      if (staffId) {
        query += ` AND (us.from_staff_id = ? OR us.to_staff_id = ?)`;
        params.push(staffId, staffId);
      }

      query += ` ORDER BY us.switched_at DESC LIMIT ?`;
      params.push(limit);

      const [rows] = await this.pool.execute(query, params);
      return rows;
    } catch (error) {
      console.error("Get user switch audit trail error:", error);
      return [];
    }
  }

  /**
   * Get security statistics
   */
  async getSecurityStats(timeframe = "24h") {
    try {
      let timeCondition;
      switch (timeframe) {
        case "1h":
          timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
          break;
        case "24h":
          timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
          break;
        case "7d":
          timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)";
          break;
        case "30d":
          timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)";
          break;
        default:
          timeCondition = "created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
      }

      const [stats] = await this.pool.execute(`
        SELECT 
          COUNT(*) as total_events,
          COUNT(CASE WHEN event_type LIKE 'AUTH_%' THEN 1 END) as auth_events,
          COUNT(CASE WHEN event_type = 'USER_SWITCHED' THEN 1 END) as user_switches,
          COUNT(CASE WHEN event_type = 'PIN_FAILED' THEN 1 END) as failed_pins,
          COUNT(CASE WHEN event_type = 'DATA_ACCESS' THEN 1 END) as data_access_events,
          COUNT(DISTINCT staff_id) as active_users,
          COUNT(DISTINCT ip_address) as unique_ips
        FROM pos_security_events 
        WHERE ${timeCondition}
      `);

      const [ticketStats] = await this.pool.execute(`
        SELECT 
          COUNT(*) as total_ticket_actions,
          COUNT(CASE WHEN action = 'created' THEN 1 END) as tickets_created,
          COUNT(CASE WHEN action = 'completed' THEN 1 END) as tickets_completed,
          COUNT(DISTINCT staff_id) as active_staff
        FROM ticket_audit_log 
        WHERE ${timeCondition}
      `);

      return {
        timeframe,
        security: stats[0],
        tickets: ticketStats[0],
        queuedEvents: this.eventQueue.length,
      };
    } catch (error) {
      console.error("Get security stats error:", error);
      return {};
    }
  }

  /**
   * Search audit logs
   */
  async searchAuditLogs(criteria = {}) {
    try {
      const {
        staffId,
        eventTypes,
        startDate,
        endDate,
        ipAddress,
        limit = 100,
        offset = 0,
      } = criteria;

      let query = `
        SELECT se.*, ps.name as staff_name
        FROM pos_security_events se
        LEFT JOIN pos_staff ps ON se.staff_id = ps.id
        WHERE 1=1
      `;

      const params = [];

      if (staffId) {
        query += ` AND se.staff_id = ?`;
        params.push(staffId);
      }

      if (eventTypes && eventTypes.length > 0) {
        query += ` AND se.event_type IN (${eventTypes
          .map(() => "?")
          .join(",")})`;
        params.push(...eventTypes);
      }

      if (startDate) {
        query += ` AND se.created_at >= ?`;
        params.push(startDate);
      }

      if (endDate) {
        query += ` AND se.created_at <= ?`;
        params.push(endDate);
      }

      if (ipAddress) {
        query += ` AND se.ip_address = ?`;
        params.push(ipAddress);
      }

      // Use string interpolation for LIMIT and OFFSET to avoid MySQL parameter type issues
      // This is safe because limit and offset are validated integers
      query += ` ORDER BY se.created_at DESC LIMIT ${limit} OFFSET ${offset}`;

      const [rows] = await this.pool.execute(query, params);

      return rows.map((row) => ({
        ...row,
        event_data: JSON.parse(row.event_data || "{}"),
      }));
    } catch (error) {
      console.error("Search audit logs error:", error);
      return [];
    }
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(retentionDays = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // Clean up security events
      const [securityResult] = await this.pool.execute(
        `DELETE FROM pos_security_events WHERE created_at < ?`,
        [cutoffDate]
      );

      // Clean up ticket audit logs
      const [ticketResult] = await this.pool.execute(
        `DELETE FROM ticket_audit_log WHERE created_at < ?`,
        [cutoffDate]
      );

      console.log(
        `Audit cleanup: Removed ${securityResult.affectedRows} security events and ${ticketResult.affectedRows} ticket audit logs older than ${retentionDays} days`
      );

      return {
        securityEventsRemoved: securityResult.affectedRows,
        ticketAuditLogsRemoved: ticketResult.affectedRows,
      };
    } catch (error) {
      console.error("Audit cleanup error:", error);
      return { error: error.message };
    }
  }

  /**
   * Flush remaining events (for graceful shutdown)
   */
  async flush() {
    if (this.eventQueue.length > 0) {
      await this.processBatch();
    }
  }
}

module.exports = AuditService;
