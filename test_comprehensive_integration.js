/**
 * Comprehensive React Native Integration Test
 * Tests all user roles, screens, and workflows
 */

const axios = require("axios");
const BASE_URL = "http://localhost:3020/api";

async function testComprehensiveIntegration() {
  console.log("🔍 COMPREHENSIVE REACT NATIVE INTEGRATION VERIFICATION\n");

  const results = {
    authentication: { passed: 0, total: 0 },
    permissions: { passed: 0, total: 0 },
    salesAgents: { passed: 0, total: 0 },
    commission: { passed: 0, total: 0 },
    dataIntegration: { passed: 0, total: 0 },
  };

  try {
    // ===== AUTHENTICATION & USER ROLE VERIFICATION =====
    console.log("🔐 AUTHENTICATION & USER ROLE VERIFICATION\n");

    // Test 1: Super Admin Login
    console.log("📋 Test 1: Super Admin Login");
    results.authentication.total++;
    const adminResponse = await axios.post(`${BASE_URL}/pos/login`, {
      username: "admin1",
      password: "admin123",
    });

    if (adminResponse.data.success) {
      console.log("✅ PASSED: Super Admin Login");
      console.log(`   User: ${adminResponse.data.data.user.name}`);
      console.log(`   Role: ${adminResponse.data.data.user.role}`);
      console.log(
        `   Permissions: ${adminResponse.data.data.user.permissions.length} permissions`
      );
      results.authentication.passed++;
    } else {
      console.log("❌ FAILED: Super Admin Login");
    }

    // Test 2: Manager Login
    console.log("\n📋 Test 2: Manager Login");
    results.authentication.total++;
    const managerResponse = await axios.post(`${BASE_URL}/pos/login`, {
      username: "manager1",
      password: "manager123",
    });

    if (managerResponse.data.success) {
      console.log("✅ PASSED: Manager Login");
      console.log(`   User: ${managerResponse.data.data.user.name}`);
      console.log(`   Role: ${managerResponse.data.data.user.role}`);
      console.log(
        `   Permissions: ${managerResponse.data.data.user.permissions.length} permissions`
      );
      results.authentication.passed++;
    } else {
      console.log("❌ FAILED: Manager Login");
    }

    // Test 3: Cashier Login
    console.log("\n📋 Test 3: Cashier Login");
    results.authentication.total++;
    const cashierResponse = await axios.post(`${BASE_URL}/pos/login`, {
      username: "cashier1",
      password: "password123",
    });

    if (cashierResponse.data.success) {
      console.log("✅ PASSED: Cashier Login");
      console.log(`   User: ${cashierResponse.data.data.user.name}`);
      console.log(`   Role: ${cashierResponse.data.data.user.role}`);
      console.log(
        `   Permissions: ${cashierResponse.data.data.user.permissions.length} permissions`
      );
      results.authentication.passed++;
    } else {
      console.log("❌ FAILED: Cashier Login");
    }

    // Use manager token for remaining tests
    const token = managerResponse.data.data.token;
    const authHeaders = { Authorization: `Bearer ${token}` };

    // ===== PERMISSION-BASED ACCESS VERIFICATION =====
    console.log("\n\n🔒 PERMISSION-BASED ACCESS VERIFICATION\n");

    // Test 4: Manager Can Access Sales Agents
    console.log("📋 Test 4: Manager Can Access Sales Agents");
    results.permissions.total++;
    const agentsResponse = await axios.get(`${BASE_URL}/sales-agents`, {
      headers: authHeaders,
    });

    if (agentsResponse.data.success) {
      console.log("✅ PASSED: Manager Can Access Sales Agents");
      console.log(
        `   Found: ${agentsResponse.data.data.salesAgents.length} agents`
      );
      results.permissions.passed++;
    } else {
      console.log("❌ FAILED: Manager Can Access Sales Agents");
    }

    // Test 5: Manager Can Access Commission Configuration
    console.log("\n📋 Test 5: Manager Can Access Commission Configuration");
    results.permissions.total++;
    const configResponse = await axios.get(
      `${BASE_URL}/discounts/configuration`,
      {
        headers: authHeaders,
      }
    );

    if (configResponse.data.success) {
      console.log("✅ PASSED: Manager Can Access Commission Configuration");
      console.log(
        `   System Enabled: ${configResponse.data.data.configuration.enabled}`
      );
      results.permissions.passed++;
    } else {
      console.log("❌ FAILED: Manager Can Access Commission Configuration");
    }

    // ===== SALES AGENT MANAGEMENT VERIFICATION =====
    console.log("\n\n👥 SALES AGENT MANAGEMENT VERIFICATION\n");

    // Test 6: Sales Agent Data Structure
    console.log("📋 Test 6: Sales Agent Data Structure");
    results.salesAgents.total++;
    if (agentsResponse.data.success) {
      const agents = agentsResponse.data.data.salesAgents;
      const firstAgent = agents[0];

      const requiredFields = [
        "id",
        "name",
        "email",
        "commissionRate",
        "territory",
        "active",
      ];
      const hasAllFields = requiredFields.every((field) =>
        firstAgent.hasOwnProperty(field)
      );

      if (hasAllFields) {
        console.log("✅ PASSED: Sales Agent Data Structure");
        console.log(`   Sample Agent: ${firstAgent.name}`);
        console.log(`   Commission Rate: ${firstAgent.commissionRate}%`);
        console.log(`   Territory: ${firstAgent.territory}`);
        console.log(`   Customer Count: ${firstAgent.customerCount}`);
        results.salesAgents.passed++;
      } else {
        console.log("❌ FAILED: Sales Agent Data Structure - Missing fields");
      }
    }

    // ===== COMMISSION SYSTEM VERIFICATION =====
    console.log("\n\n💰 COMMISSION SYSTEM VERIFICATION\n");

    // Test 7: Commission Calculation
    console.log("📋 Test 7: Commission Calculation");
    results.commission.total++;
    const calcResponse = await axios.post(
      `${BASE_URL}/discounts/calculate`,
      {
        cartData: { subtotal: "100.00" },
        staffId: "pos-002",
        salesAgentId: "agent-001",
      },
      {
        headers: { ...authHeaders, "Content-Type": "application/json" },
      }
    );

    if (calcResponse.data.success) {
      console.log("✅ PASSED: Commission Calculation");
      const discount = calcResponse.data.data.discount;
      console.log(`   Subtotal: KSh ${discount.subtotal}`);
      console.log(`   Total Discount: KSh ${discount.totalDiscount}`);
      console.log(`   Staff Discount: KSh ${discount.staffDiscount}`);
      console.log(`   Agent Discount: KSh ${discount.agentDiscount}`);
      console.log(`   Final Amount: KSh ${discount.finalAmount}`);
      results.commission.passed++;
    } else {
      console.log("❌ FAILED: Commission Calculation");
    }

    // ===== DATA INTEGRATION VERIFICATION =====
    console.log("\n\n📊 DATA INTEGRATION VERIFICATION\n");

    // Test 8: Store Products Integration
    console.log("📋 Test 8: Store Products Integration");
    results.dataIntegration.total++;
    const productsResponse = await axios.get(
      `${BASE_URL}/store/products?limit=3`,
      {
        headers: authHeaders,
      }
    );

    if (productsResponse.data.success) {
      console.log("✅ PASSED: Store Products Integration");
      console.log(
        `   Found: ${productsResponse.data.data.products.length} products`
      );
      results.dataIntegration.passed++;
    } else {
      console.log("❌ FAILED: Store Products Integration");
    }

    // Test 9: Store Customers Integration
    console.log("\n📋 Test 9: Store Customers Integration");
    results.dataIntegration.total++;
    const customersResponse = await axios.get(
      `${BASE_URL}/store/customers?limit=3`,
      {
        headers: authHeaders,
      }
    );

    if (customersResponse.data.success) {
      console.log("✅ PASSED: Store Customers Integration");
      console.log(
        `   Found: ${customersResponse.data.data.customers.length} customers`
      );
      results.dataIntegration.passed++;
    } else {
      console.log("❌ FAILED: Store Customers Integration");
    }

    // ===== FINAL RESULTS =====
    console.log("\n\n📊 COMPREHENSIVE INTEGRATION TEST RESULTS\n");

    const categories = [
      { name: "Authentication", results: results.authentication },
      { name: "Permissions", results: results.permissions },
      { name: "Sales Agents", results: results.salesAgents },
      { name: "Commission System", results: results.commission },
      { name: "Data Integration", results: results.dataIntegration },
    ];

    let totalPassed = 0;
    let totalTests = 0;

    categories.forEach((category) => {
      const percentage =
        category.results.total > 0
          ? ((category.results.passed / category.results.total) * 100).toFixed(
              1
            )
          : "0.0";

      console.log(
        `${category.name}: ${category.results.passed}/${category.results.total} (${percentage}%)`
      );
      totalPassed += category.results.passed;
      totalTests += category.results.total;
    });

    const overallPercentage =
      totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : "0.0";

    console.log(
      `\n🎯 OVERALL INTEGRATION STATUS: ${totalPassed}/${totalTests} (${overallPercentage}%)`
    );

    if (overallPercentage >= 80) {
      console.log("🎉 INTEGRATION STATUS: READY FOR PRODUCTION");
    } else if (overallPercentage >= 60) {
      console.log("⚠️  INTEGRATION STATUS: NEEDS MINOR FIXES");
    } else {
      console.log("❌ INTEGRATION STATUS: NEEDS MAJOR WORK");
    }
  } catch (error) {
    console.error("❌ Comprehensive integration test failed:", error.message);
  }
}

testComprehensiveIntegration();
