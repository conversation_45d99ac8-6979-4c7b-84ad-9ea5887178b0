/**
 * Auto-Save Service
 *
 * Handles automatic saving of ticket state with debouncing, offline support,
 * and intelligent save strategies to minimize server load while ensuring
 * data persistence.
 */

import { store } from "@/src/store";
import {
  autoSaveTicket,
  batchAutoSaveTickets,
  saveTicket,
} from "@/src/store/thunks/ticketThunks";
import {
  selectAllTickets,
  selectActiveTicketId,
  markTicketAsSaved,
} from "@/src/store/slices/ticketSlice";

export interface AutoSaveConfig {
  debounceMs: number;
  batchSize: number;
  maxRetries: number;
  retryDelayMs: number;
  offlineQueueSize: number;
  enableBatching: boolean;
  exponentialBackoffMultiplier: number;
  maxRetryDelayMs: number;
}

export interface AutoSaveStats {
  totalSaves: number;
  successfulSaves: number;
  failedSaves: number;
  lastSaveTime: Date | null;
  averageSaveTime: number;
  offlineQueueSize: number;
  errorsByType: Record<string, number>;
  lastError?: {
    ticketId: string;
    error: string;
    timestamp: Date;
    retryCount: number;
  };
}

export enum AutoSaveErrorType {
  TICKET_NOT_FOUND = "TICKET_NOT_FOUND",
  NETWORK_ERROR = "NETWORK_ERROR",
  AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  PERMISSION_ERROR = "PERMISSION_ERROR",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

class AutoSaveService {
  private config: AutoSaveConfig;
  private saveTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private pendingSaves: Set<string> = new Set();
  private offlineQueue: Array<{
    ticketId: string;
    ticketState: any;
    timestamp: Date;
    retryCount: number;
    lastRetryAt?: Date;
  }> = [];
  private retryCounters: Map<string, number> = new Map();
  private isOnline: boolean = true;
  private stats: AutoSaveStats;
  private lastTicketStates: Map<string, string> = new Map();

  constructor(config: Partial<AutoSaveConfig> = {}) {
    this.config = {
      debounceMs: 2000, // 2 seconds
      batchSize: 5,
      maxRetries: 3,
      retryDelayMs: 1000,
      offlineQueueSize: 50,
      enableBatching: true,
      exponentialBackoffMultiplier: 2,
      maxRetryDelayMs: 30000, // 30 seconds max delay
      ...config,
    };

    this.stats = {
      totalSaves: 0,
      successfulSaves: 0,
      failedSaves: 0,
      lastSaveTime: null,
      averageSaveTime: 0,
      offlineQueueSize: 0,
      errorsByType: {},
    };

    this.setupNetworkListener();
    this.startPeriodicBatchSave();
  }

  /**
   * Schedule auto-save for a ticket with debouncing
   */
  scheduleAutoSave(ticketId: string, force: boolean = false): void {
    // Clear existing timeout for this ticket
    const existingTimeout = this.saveTimeouts.get(ticketId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Get current ticket state
    const state = store.getState();
    const ticket = state.tickets.tickets.find((t) => t.id === ticketId);

    if (!ticket) {
      console.warn(`Ticket ${ticketId} not found for auto-save`);
      return;
    }

    // Skip if ticket is already saved and not dirty (unless forced)
    if (!force && !ticket.isDirty && !ticket.isLocal) {
      console.log(
        `✅ Ticket ${ticketId} is already saved and not dirty, skipping auto-save`
      );
      return;
    }

    // Check if ticket state has actually changed
    const currentStateHash = this.hashTicketState(ticket);
    const lastStateHash = this.lastTicketStates.get(ticketId);

    if (!force && currentStateHash === lastStateHash) {
      // No changes detected, skip save
      console.log(
        `🔄 No changes detected for ticket ${ticketId}, skipping auto-save`
      );
      return;
    }

    // Update last known state
    this.lastTicketStates.set(ticketId, currentStateHash);

    if (force || !this.config.enableBatching) {
      // Immediate save
      this.performSave(ticketId);
    } else {
      // Debounced save
      const timeout = setTimeout(() => {
        this.performSave(ticketId);
        this.saveTimeouts.delete(ticketId);
      }, this.config.debounceMs);

      this.saveTimeouts.set(ticketId, timeout);
    }
  }

  /**
   * Force immediate save of all dirty tickets
   */
  async saveAllDirtyTickets(): Promise<void> {
    const state = store.getState();
    const dirtyTickets = state.tickets.tickets.filter((t) => t.isDirty);

    if (dirtyTickets.length === 0) {
      return;
    }

    if (this.config.enableBatching && dirtyTickets.length > 1) {
      await this.performBatchSave(dirtyTickets.map((t) => t.id));
    } else {
      // Save individually
      const savePromises = dirtyTickets.map((ticket) =>
        this.performSave(ticket.id, false)
      );
      await Promise.allSettled(savePromises);
    }
  }

  /**
   * Perform individual ticket save with intelligent retry logic
   */
  private async performSave(
    ticketId: string,
    retry: boolean = true,
    retryCount: number = 0
  ): Promise<void> {
    if (this.pendingSaves.has(ticketId)) {
      return; // Already saving this ticket
    }

    this.pendingSaves.add(ticketId);
    const startTime = Date.now();

    try {
      // Re-fetch ticket state to ensure we have the latest data
      const state = store.getState();
      const ticket = state.tickets.tickets.find((t) => t.id === ticketId);

      if (!ticket) {
        console.warn(`❌ Ticket ${ticketId} not found during save`);
        return;
      }

      // Check if ticket is already saved and not dirty
      if (!ticket.isDirty && !ticket.isLocal) {
        console.log(
          `✅ Ticket ${ticketId} is already saved and not dirty, skipping`
        );
        return;
      }

      // Validate ticket state before attempting save
      const validationResult = this.validateTicketState(ticket);
      if (!validationResult.isValid) {
        console.error(
          `❌ Ticket ${ticketId} validation failed:`,
          validationResult.errors
        );
        console.error(`❌ Ticket data:`, JSON.stringify(ticket, null, 2));
        this.updateSaveStats(
          false,
          Date.now() - startTime,
          1,
          AutoSaveErrorType.VALIDATION_ERROR,
          ticketId,
          retryCount
        );
        return;
      }

      if (!this.isOnline) {
        console.log(`📴 Offline - adding ticket ${ticketId} to queue`);
        this.addToOfflineQueue(ticketId, ticket);
        return;
      }

      // Check if ticket is local (not yet persisted to backend)
      if (ticket.isLocal) {
        console.log(
          `Saving local ticket ${ticketId} to backend for first time (isDirty: ${ticket.isDirty})`
        );

        // Use saveTicket for local tickets (creates new ticket in backend)
        const result = await store
          .dispatch(
            saveTicket({
              ticket,
            })
          )
          .unwrap();

        console.log(`Successfully saved local ticket ${ticketId} to backend`);

        // Update the last known state hash to prevent duplicate saves
        const updatedState = store.getState();
        // Look for the ticket by original ID first, then by the result ID if it changed
        let updatedTicket = updatedState.tickets.tickets.find(
          (t) => t.id === ticketId
        );
        if (!updatedTicket && result && result.id !== ticketId) {
          // Ticket ID might have changed after backend save
          updatedTicket = updatedState.tickets.tickets.find(
            (t) => t.id === result.id
          );
          console.log(
            `Ticket ID changed from ${ticketId} to ${result.id} after save`
          );
        }

        if (updatedTicket) {
          console.log(
            `Updated ticket ${updatedTicket.id} state after save: isLocal=${updatedTicket.isLocal}, isDirty=${updatedTicket.isDirty}`
          );
          this.lastTicketStates.set(
            updatedTicket.id,
            this.hashTicketState(updatedTicket)
          );
          // Also remove the old ticket ID from tracking if it changed
          if (updatedTicket.id !== ticketId) {
            this.lastTicketStates.delete(ticketId);
          }
        }
      } else {
        // Use autoSaveTicket for existing tickets (updates existing ticket)
        const ticketState = this.serializeTicketState(ticket);

        const result = await store
          .dispatch(
            autoSaveTicket({
              ticketId,
              ticketState,
            })
          )
          .unwrap();

        console.log(`Auto-saved existing ticket ${ticketId} successfully`);

        // Update the last known state hash to prevent duplicate saves
        const updatedState = store.getState();
        const updatedTicket = updatedState.tickets.tickets.find(
          (t) => t.id === ticketId
        );
        if (updatedTicket) {
          this.lastTicketStates.set(
            ticketId,
            this.hashTicketState(updatedTicket)
          );
        }
      }

      // Update stats
      this.updateSaveStats(true, Date.now() - startTime);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorType = this.categorizeError(errorMessage);

      console.error(`🚨 Auto-save failed for ticket ${ticketId}:`, {
        error: errorMessage,
        type: errorType,
        retryCount,
        isLocal: ticket?.isLocal,
        timestamp: new Date().toISOString(),
      });

      this.updateSaveStats(
        false,
        Date.now() - startTime,
        1,
        errorType,
        ticketId,
        retryCount
      );

      const isTicketNotFoundError =
        errorType === AutoSaveErrorType.TICKET_NOT_FOUND;

      if (isTicketNotFoundError) {
        console.warn(
          `Ticket ${ticketId} not found in backend - will retry as local ticket`
        );
        // Mark ticket as local and retry
        const state = store.getState();
        const ticket = state.tickets.tickets.find((t) => t.id === ticketId);
        if (ticket && !ticket.isLocal) {
          // Update ticket to be local and retry
          store.dispatch(markTicketAsSaved({ ticketId, isLocal: true }));
          if (retry && retryCount < this.config.maxRetries) {
            const delay = this.calculateRetryDelay(retryCount);
            setTimeout(() => {
              this.performSave(ticketId, true, retryCount + 1);
            }, delay);
          }
          return;
        }
      }

      if (retry && this.isOnline && retryCount < this.config.maxRetries) {
        // Retry with exponential backoff for other errors
        const delay = this.calculateRetryDelay(retryCount);
        console.log(
          `Retrying save for ticket ${ticketId} (attempt ${retryCount + 1}/${
            this.config.maxRetries
          }) in ${delay}ms`
        );
        setTimeout(() => {
          this.performSave(ticketId, true, retryCount + 1);
        }, delay);
      } else {
        // Max retries reached or not retrying - add to offline queue
        console.warn(
          `Max retries reached for ticket ${ticketId} or not retrying - adding to offline queue`
        );
        const state = store.getState();
        const ticket = state.tickets.tickets.find((t) => t.id === ticketId);
        if (ticket) {
          this.addToOfflineQueue(ticketId, ticket, retryCount);
        }
      }
    } finally {
      this.pendingSaves.delete(ticketId);
    }
  }

  /**
   * Perform batch save of multiple tickets
   */
  private async performBatchSave(ticketIds: string[]): Promise<void> {
    if (ticketIds.length === 0) return;

    const startTime = Date.now();

    try {
      const state = store.getState();
      const tickets = ticketIds
        .map((id) => state.tickets.tickets.find((t) => t.id === id))
        .filter(Boolean);

      if (tickets.length === 0) return;

      if (!this.isOnline) {
        tickets.forEach((ticket) => {
          this.addToOfflineQueue(ticket!.id, ticket!);
        });
        return;
      }

      const ticketsData = tickets.map((ticket) => ({
        ticketId: ticket!.id,
        ticketState: this.serializeTicketState(ticket!),
      }));

      const result = await store
        .dispatch(
          batchAutoSaveTickets({
            tickets: ticketsData,
          })
        )
        .unwrap();

      // Update stats
      this.updateSaveStats(true, Date.now() - startTime, tickets.length);

      console.log(`Batch auto-saved ${tickets.length} tickets successfully`);
    } catch (error) {
      console.error(`Batch auto-save failed:`, error);

      this.updateSaveStats(false, Date.now() - startTime, ticketIds.length);

      // Add failed tickets to offline queue
      const state = store.getState();
      ticketIds.forEach((ticketId) => {
        const ticket = state.tickets.tickets.find((t) => t.id === ticketId);
        if (ticket) {
          this.addToOfflineQueue(ticketId, ticket);
        }
      });
    }
  }

  /**
   * Add ticket to offline queue with retry tracking
   */
  private addToOfflineQueue(
    ticketId: string,
    ticket: any,
    retryCount: number = 0
  ): void {
    // Remove existing entry for this ticket
    this.offlineQueue = this.offlineQueue.filter(
      (item) => item.ticketId !== ticketId
    );

    // Add new entry with retry tracking
    this.offlineQueue.push({
      ticketId,
      ticketState: this.serializeTicketState(ticket),
      timestamp: new Date(),
      retryCount,
      lastRetryAt: retryCount > 0 ? new Date() : undefined,
    });

    // Limit queue size
    if (this.offlineQueue.length > this.config.offlineQueueSize) {
      this.offlineQueue.shift(); // Remove oldest
    }

    this.stats.offlineQueueSize = this.offlineQueue.length;
    console.log(
      `📥 Added ticket ${ticketId} to offline queue (${this.offlineQueue.length} items, retry count: ${retryCount})`
    );
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    const baseDelay = this.config.retryDelayMs;
    const exponentialDelay =
      baseDelay *
      Math.pow(this.config.exponentialBackoffMultiplier, retryCount);
    return Math.min(exponentialDelay, this.config.maxRetryDelayMs);
  }

  /**
   * Process offline queue when coming back online
   */
  private async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return;

    console.log(
      `Processing offline queue with ${this.offlineQueue.length} items`
    );

    const queueCopy = [...this.offlineQueue];
    this.offlineQueue = [];
    this.stats.offlineQueueSize = 0;

    // Process tickets individually to handle local vs existing tickets properly
    const failedItems: typeof queueCopy = [];

    for (const item of queueCopy) {
      try {
        // Get current ticket state to check if it's local
        const state = store.getState();
        const currentTicket = state.tickets.tickets.find(
          (t) => t.id === item.ticketId
        );

        if (!currentTicket) {
          console.warn(
            `Ticket ${item.ticketId} not found in current state, skipping offline queue item`
          );
          continue;
        }

        if (currentTicket.isLocal) {
          // Use saveTicket for local tickets
          console.log(
            `Processing offline local ticket ${item.ticketId} with saveTicket`
          );
          await store
            .dispatch(
              saveTicket({
                ticket: currentTicket,
              })
            )
            .unwrap();
        } else {
          // Use autoSaveTicket for existing tickets
          console.log(
            `Processing offline existing ticket ${item.ticketId} with autoSaveTicket`
          );
          await store
            .dispatch(
              autoSaveTicket({
                ticketId: item.ticketId,
                ticketState: item.ticketState,
              })
            )
            .unwrap();
        }

        console.log(`Successfully processed offline ticket ${item.ticketId}`);
      } catch (error) {
        console.error(
          `Failed to process offline ticket ${item.ticketId}:`,
          error
        );

        // Check if this is a "Ticket not found" error
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        const isTicketNotFoundError = errorMessage.includes("Ticket not found");

        if (isTicketNotFoundError) {
          console.warn(
            `Ticket ${item.ticketId} not found in backend, will mark as local and retry`
          );
          // Mark ticket as local in state and add back to failed items for retry
          const state = store.getState();
          const ticket = state.tickets.tickets.find(
            (t) => t.id === item.ticketId
          );
          if (ticket && !ticket.isLocal) {
            store.dispatch(
              markTicketAsSaved({ ticketId: item.ticketId, isLocal: true })
            );
          }
        }

        // Add to failed items for potential retry
        failedItems.push(item);
      }
    }

    // Re-add failed items to queue (with limit to prevent infinite growth)
    if (failedItems.length > 0) {
      console.log(
        `Re-adding ${failedItems.length} failed items to offline queue`
      );
      failedItems.forEach((item) => {
        if (this.offlineQueue.length < this.config.offlineQueueSize) {
          this.offlineQueue.push(item);
        }
      });
    }

    this.stats.offlineQueueSize = this.offlineQueue.length;

    if (this.offlineQueue.length === 0) {
      console.log("✅ Offline queue processed successfully - all items saved");
    } else {
      console.log(
        `⚠️ Offline queue processing completed with ${this.offlineQueue.length} remaining items`
      );
    }
  }

  /**
   * Serialize ticket state for saving
   */
  private serializeTicketState(ticket: any): any {
    return {
      name: ticket.name,
      items: ticket.items,
      customer: ticket.customer,
      salesperson: ticket.salesperson,
      note: ticket.note,
      discounts: ticket.discounts,
      subtotal: ticket.subtotal,
      tax: ticket.tax,
      total: ticket.total,
      status: ticket.status,
    };
  }

  /**
   * Create hash of ticket state for change detection
   */
  private hashTicketState(ticket: any): string {
    const stateString = JSON.stringify(this.serializeTicketState(ticket));
    return btoa(stateString).slice(0, 16); // Simple hash
  }

  /**
   * Update save statistics with error tracking
   */
  private updateSaveStats(
    success: boolean,
    duration: number,
    count: number = 1,
    errorType?: AutoSaveErrorType,
    ticketId?: string,
    retryCount?: number
  ): void {
    this.stats.totalSaves += count;
    this.stats.lastSaveTime = new Date();

    if (success) {
      this.stats.successfulSaves += count;
    } else {
      this.stats.failedSaves += count;

      // Track error by type
      if (errorType) {
        this.stats.errorsByType[errorType] =
          (this.stats.errorsByType[errorType] || 0) + 1;

        // Update last error info
        if (ticketId) {
          this.stats.lastError = {
            ticketId,
            error: errorType,
            timestamp: new Date(),
            retryCount: retryCount || 0,
          };
        }
      }
    }

    // Update average save time
    const totalDuration =
      this.stats.averageSaveTime * (this.stats.totalSaves - count) + duration;
    this.stats.averageSaveTime = totalDuration / this.stats.totalSaves;
  }

  /**
   * Categorize error for better tracking and handling
   */
  private categorizeError(errorMessage: string): AutoSaveErrorType {
    const message = errorMessage.toLowerCase();

    if (message.includes("ticket not found") || message.includes("not found")) {
      return AutoSaveErrorType.TICKET_NOT_FOUND;
    }

    if (
      message.includes("authentication") ||
      message.includes("unauthorized") ||
      message.includes("token")
    ) {
      return AutoSaveErrorType.AUTHENTICATION_ERROR;
    }

    if (
      message.includes("permission") ||
      message.includes("forbidden") ||
      message.includes("access denied")
    ) {
      return AutoSaveErrorType.PERMISSION_ERROR;
    }

    if (
      message.includes("validation") ||
      message.includes("invalid") ||
      message.includes("required")
    ) {
      return AutoSaveErrorType.VALIDATION_ERROR;
    }

    if (
      message.includes("network") ||
      message.includes("connection") ||
      message.includes("timeout") ||
      message.includes("fetch")
    ) {
      return AutoSaveErrorType.NETWORK_ERROR;
    }

    return AutoSaveErrorType.UNKNOWN_ERROR;
  }

  /**
   * Validate ticket state before save operations
   */
  private validateTicketState(ticket: any): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check required fields
    if (!ticket.id) {
      errors.push("Ticket ID is required");
    }

    // Only validate staffId for local tickets that haven't been saved yet
    // Backend-saved tickets might have different field structures
    if (ticket.isLocal && !ticket.staffId) {
      errors.push("Staff ID is required");
    }

    if (!ticket.name || ticket.name.trim() === "") {
      errors.push("Ticket name is required");
    }

    // Validate ticket status
    const validStatuses = [
      "active",
      "paused",
      "completed",
      "cancelled",
      "expired",
      "archived",
    ];
    if (!ticket.status || !validStatuses.includes(ticket.status)) {
      errors.push(
        `Invalid ticket status: ${
          ticket.status
        }. Must be one of: ${validStatuses.join(", ")}`
      );
    }

    // Validate numeric fields
    if (
      ticket.subtotal !== undefined &&
      (isNaN(ticket.subtotal) || ticket.subtotal < 0)
    ) {
      errors.push("Subtotal must be a non-negative number");
    }

    if (ticket.tax !== undefined && (isNaN(ticket.tax) || ticket.tax < 0)) {
      errors.push("Tax must be a non-negative number");
    }

    if (
      ticket.total !== undefined &&
      (isNaN(ticket.total) || ticket.total < 0)
    ) {
      errors.push("Total must be a non-negative number");
    }

    // Validate items array
    if (ticket.items && !Array.isArray(ticket.items)) {
      errors.push("Items must be an array");
    } else if (ticket.items) {
      ticket.items.forEach((item: any, index: number) => {
        // For cart items, variantId serves as the primary identifier
        // Backend items will have both id and variantId
        if (!item.id && !item.variantId) {
          errors.push(`Item ${index + 1}: ID or Variant ID is required`);
        }
        if (!item.variantId) {
          errors.push(`Item ${index + 1}: Variant ID is required`);
        }
        if (!item.title) {
          errors.push(`Item ${index + 1}: Title is required`);
        }
        if (isNaN(item.quantity) || item.quantity <= 0) {
          errors.push(`Item ${index + 1}: Quantity must be a positive number`);
        }
        if (isNaN(item.price) || item.price < 0) {
          errors.push(`Item ${index + 1}: Price must be a non-negative number`);
        }
      });
    }

    // Validate discounts array
    if (ticket.discounts && !Array.isArray(ticket.discounts)) {
      errors.push("Discounts must be an array");
    } else if (ticket.discounts) {
      ticket.discounts.forEach((discount: any, index: number) => {
        if (!discount.id) {
          errors.push(`Discount ${index + 1}: ID is required`);
        }
        if (
          !discount.type ||
          !["percentage", "fixed_amount"].includes(discount.type)
        ) {
          errors.push(
            `Discount ${index + 1}: Type must be 'percentage' or 'fixed_amount'`
          );
        }
        if (isNaN(discount.value) || discount.value < 0) {
          errors.push(
            `Discount ${index + 1}: Value must be a non-negative number`
          );
        }
      });
    }

    // Validate timestamps
    if (ticket.createdAt && isNaN(Date.parse(ticket.createdAt))) {
      errors.push("Invalid createdAt timestamp");
    }

    if (ticket.updatedAt && isNaN(Date.parse(ticket.updatedAt))) {
      errors.push("Invalid updatedAt timestamp");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Setup network status listener for React Native
   */
  private setupNetworkListener(): void {
    // React Native doesn't have window.addEventListener
    // Network monitoring is handled by NetInfo in React Native
    // This will be set up in the hook that uses this service
    this.isOnline = true; // Default to online
  }

  /**
   * Start periodic batch save for efficiency
   */
  private startPeriodicBatchSave(): void {
    setInterval(() => {
      if (this.config.enableBatching) {
        this.saveAllDirtyTickets();
      }
    }, this.config.debounceMs * 3); // Every 6 seconds by default
  }

  /**
   * Utility function to chunk array
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Get current auto-save statistics
   */
  getStats(): AutoSaveStats {
    return { ...this.stats };
  }

  /**
   * Get current configuration
   */
  getConfig(): AutoSaveConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AutoSaveConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Clear all pending saves and offline queue
   */
  clear(): void {
    this.saveTimeouts.forEach((timeout) => clearTimeout(timeout));
    this.saveTimeouts.clear();
    this.pendingSaves.clear();
    this.offlineQueue = [];
    this.lastTicketStates.clear();
    this.stats.offlineQueueSize = 0;
  }
}

// Export singleton instance
export const autoSaveService = new AutoSaveService();
export default AutoSaveService;
