/**
 * Loyalty System Diagnostic Service
 * 
 * Comprehensive diagnostic tool to identify and fix loyalty system issues
 */

import { getAPIClient } from './api/dukalink-client';
import { loyaltyService } from './loyalty-service';

export interface DiagnosticResult {
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

export interface LoyaltySystemDiagnostic {
  authentication: DiagnosticResult;
  apiEndpoints: DiagnosticResult;
  customerLoyalty: DiagnosticResult;
  discountCalculation: DiagnosticResult;
  pointsAddition: DiagnosticResult;
  overallHealth: DiagnosticResult;
}

class LoyaltyDiagnosticService {
  private apiClient = getAPIClient();

  /**
   * Run comprehensive loyalty system diagnostics
   */
  async runDiagnostics(testCustomerId: string = '8095960301705'): Promise<LoyaltySystemDiagnostic> {
    console.log('🔍 Starting Loyalty System Diagnostics...');

    const results: LoyaltySystemDiagnostic = {
      authentication: await this.testAuthentication(),
      apiEndpoints: await this.testAPIEndpoints(),
      customerLoyalty: await this.testCustomerLoyalty(testCustomerId),
      discountCalculation: await this.testDiscountCalculation(testCustomerId),
      pointsAddition: await this.testPointsAddition(testCustomerId),
      overallHealth: { success: false, message: 'Not tested' }
    };

    // Calculate overall health
    const allTests = [
      results.authentication,
      results.apiEndpoints,
      results.customerLoyalty,
      results.discountCalculation,
      results.pointsAddition
    ];

    const successfulTests = allTests.filter(test => test.success).length;
    const totalTests = allTests.length;

    results.overallHealth = {
      success: successfulTests === totalTests,
      message: `${successfulTests}/${totalTests} tests passed`,
      details: {
        successfulTests,
        totalTests,
        healthPercentage: Math.round((successfulTests / totalTests) * 100)
      }
    };

    console.log('✅ Loyalty System Diagnostics Complete!');
    return results;
  }

  /**
   * Test POS authentication
   */
  private async testAuthentication(): Promise<DiagnosticResult> {
    try {
      console.log('🔐 Testing POS Authentication...');
      
      const response = await this.apiClient.posVerify();
      
      if (response.success && response.data) {
        return {
          success: true,
          message: 'POS authentication is working',
          details: {
            user: response.data.user,
            authenticated: true
          }
        };
      } else {
        return {
          success: false,
          message: 'POS authentication failed',
          error: response.error || 'Unknown authentication error'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Authentication test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test API endpoints availability
   */
  private async testAPIEndpoints(): Promise<DiagnosticResult> {
    try {
      console.log('🌐 Testing API Endpoints...');
      
      // Test loyalty config endpoint (should be accessible)
      const configResponse = await this.apiClient.request('/loyalty/config');
      
      if (configResponse.success) {
        return {
          success: true,
          message: 'Loyalty API endpoints are accessible',
          details: {
            configEndpoint: 'working',
            baseURL: this.apiClient.config?.baseURL
          }
        };
      } else {
        return {
          success: false,
          message: 'Loyalty API endpoints are not accessible',
          error: configResponse.error
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'API endpoints test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test customer loyalty data retrieval
   */
  private async testCustomerLoyalty(customerId: string): Promise<DiagnosticResult> {
    try {
      console.log('👤 Testing Customer Loyalty Data...');
      
      const loyaltyData = await loyaltyService.getCustomerLoyaltySummary(customerId);
      
      if (loyaltyData) {
        return {
          success: true,
          message: 'Customer loyalty data retrieved successfully',
          details: {
            customerId,
            loyaltyPoints: loyaltyData.loyaltyPoints,
            tier: loyaltyData.tier,
            totalPurchases: loyaltyData.totalPurchases
          }
        };
      } else {
        return {
          success: false,
          message: 'Failed to retrieve customer loyalty data',
          error: 'No loyalty data returned'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Customer loyalty test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test discount calculation
   */
  private async testDiscountCalculation(customerId: string): Promise<DiagnosticResult> {
    try {
      console.log('💰 Testing Discount Calculation...');
      
      const discountData = await loyaltyService.calculateLoyaltyDiscounts(customerId, 1000);
      
      if (discountData) {
        return {
          success: true,
          message: 'Discount calculation working',
          details: {
            customerId,
            orderTotal: 1000,
            tierDiscount: discountData.tier,
            pointsDiscount: discountData.points,
            totalDiscount: discountData.tier.amount + discountData.points.maxDiscount
          }
        };
      } else {
        return {
          success: false,
          message: 'Failed to calculate discounts',
          error: 'No discount data returned'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Discount calculation test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test points addition
   */
  private async testPointsAddition(customerId: string): Promise<DiagnosticResult> {
    try {
      console.log('⭐ Testing Points Addition...');
      
      const pointsResult = await loyaltyService.addLoyaltyPoints(customerId, {
        orderTotal: 100,
        orderId: `TEST_ORDER_${Date.now()}`,
        salesAgentId: 'test-agent'
      });
      
      if (pointsResult) {
        return {
          success: true,
          message: 'Points addition working',
          details: {
            customerId,
            pointsAdded: pointsResult.pointsAdded,
            newBalance: pointsResult.newBalance,
            tierChanged: pointsResult.tierChanged,
            transactionId: pointsResult.transactionId
          }
        };
      } else {
        return {
          success: false,
          message: 'Failed to add points',
          error: 'No points result returned'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Points addition test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate diagnostic report
   */
  generateReport(results: LoyaltySystemDiagnostic): string {
    const lines: string[] = [];
    
    lines.push('🔍 LOYALTY SYSTEM DIAGNOSTIC REPORT');
    lines.push('═══════════════════════════════════');
    lines.push('');
    
    // Overall Health
    lines.push(`🏥 Overall Health: ${results.overallHealth.success ? '✅ HEALTHY' : '❌ ISSUES DETECTED'}`);
    lines.push(`📊 Success Rate: ${results.overallHealth.details?.healthPercentage}%`);
    lines.push('');
    
    // Individual Tests
    const tests = [
      { name: 'Authentication', result: results.authentication },
      { name: 'API Endpoints', result: results.apiEndpoints },
      { name: 'Customer Loyalty', result: results.customerLoyalty },
      { name: 'Discount Calculation', result: results.discountCalculation },
      { name: 'Points Addition', result: results.pointsAddition }
    ];
    
    tests.forEach(test => {
      const status = test.result.success ? '✅' : '❌';
      lines.push(`${status} ${test.name}: ${test.result.message}`);
      
      if (test.result.error) {
        lines.push(`   Error: ${test.result.error}`);
      }
      
      if (test.result.details) {
        lines.push(`   Details: ${JSON.stringify(test.result.details, null, 2)}`);
      }
      
      lines.push('');
    });
    
    // Recommendations
    lines.push('💡 RECOMMENDATIONS');
    lines.push('─────────────────');
    
    if (!results.authentication.success) {
      lines.push('• Fix POS authentication - ensure user is logged in');
    }
    
    if (!results.apiEndpoints.success) {
      lines.push('• Check backend server is running on correct port');
      lines.push('• Verify loyalty routes are properly registered');
    }
    
    if (!results.customerLoyalty.success) {
      lines.push('• Check customer loyalty data initialization');
      lines.push('• Verify database connectivity');
    }
    
    if (!results.discountCalculation.success) {
      lines.push('• Check loyalty service discount calculation logic');
    }
    
    if (!results.pointsAddition.success) {
      lines.push('• Check loyalty points addition service');
      lines.push('• Verify database write permissions');
    }
    
    return lines.join('\n');
  }
}

// Export singleton instance
export const loyaltyDiagnosticService = new LoyaltyDiagnosticService();
export default loyaltyDiagnosticService;
