/**
 * Fulfillment Dashboard Card Component
 *
 * Dashboard widget for fulfillment management overview.
 * Shows key fulfillment metrics and quick actions with proper RBAC.
 */

import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { PermissionGate } from "@/src/components/rbac";
import { ThemedText } from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  useShippingRates,
  useFulfillmentStats,
} from "@/src/hooks/useFulfillment";
import { useRBAC } from "@/src/hooks/useRBAC";
import { createStyleUtils } from "@/src/utils/themeUtils";
import { useRouter } from "expo-router";
import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

interface FulfillmentCardProps {
  style?: any;
}

export const FulfillmentCard: React.FC<FulfillmentCardProps> = ({ style }) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const router = useRouter();
  const { hasPermission } = useRBAC();

  // React Query hooks
  const { data: shippingRates, isLoading: ratesLoading } = useShippingRates();
  const { data: fulfillmentStatsData, isLoading: statsLoading } =
    useFulfillmentStats();

  // Check permissions
  const canViewFulfillments = hasPermission("view_fulfillments");
  const canManageFulfillments = hasPermission("manage_fulfillments");
  const canManageShippingRates = hasPermission("manage_shipping_rates");

  // Use real data from API with fallback
  const fulfillmentStats = fulfillmentStatsData || {
    pendingCount: 0,
    shippedToday: 0,
    totalRevenue: 0,
    averageDeliveryTime: "0 days",
  };

  if (!canViewFulfillments) {
    return null; // Don't show card if user doesn't have permission
  }

  // Create theme-aware styles
  const styles = createStyles(theme);

  const handleNavigateToFulfillment = () => {
    router.push("/fulfillment-management");
  };

  const handleCreateFulfillment = () => {
    router.push("/fulfillment-details");
  };

  const handleManageRates = () => {
    router.push("/fulfillment-management?tab=shipping");
  };

  return (
    <ModernCard style={[styles.container, style]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <IconSymbol
            name="shippingbox.fill"
            size={24}
            color={theme.colors.primary}
          />
          <ThemedText variant="h3" style={utils.ml("sm")}>
            Fulfillment
          </ThemedText>
        </View>
        <TouchableOpacity
          style={styles.viewAllButton}
          onPress={handleNavigateToFulfillment}
        >
          <ThemedText variant="small" color="primary">
            View All
          </ThemedText>
          <IconSymbol
            name="chevron.right"
            size={16}
            color={theme.colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Stats Grid */}
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <IconSymbol
              name="clock.fill"
              size={20}
              color={theme.colors.warning}
            />
          </View>
          <View style={styles.statContent}>
            <ThemedText variant="h2" style={styles.statValue}>
              {statsLoading ? "..." : fulfillmentStats.pendingCount || 0}
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              Pending
            </ThemedText>
          </View>
        </View>

        <View style={styles.statItem}>
          <View style={styles.statIcon}>
            <IconSymbol
              name="truck.box.fill"
              size={20}
              color={theme.colors.info}
            />
          </View>
          <View style={styles.statContent}>
            <ThemedText variant="h2" style={styles.statValue}>
              {statsLoading ? "..." : fulfillmentStats.shippedToday || 0}
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              Shipped Today
            </ThemedText>
          </View>
        </View>
      </View>

      {/* Performance Metrics */}
      <View style={styles.metricsContainer}>
        <View style={styles.metricRow}>
          <ThemedText variant="small" color="secondary">
            Avg. Delivery Time:
          </ThemedText>
          <ThemedText
            variant="small"
            color="primary"
            style={styles.metricValue}
          >
            {statsLoading
              ? "..."
              : fulfillmentStats.averageDeliveryTime || "0 days"}
          </ThemedText>
        </View>
        <View style={styles.metricRow}>
          <ThemedText variant="small" color="secondary">
            Shipping Revenue:
          </ThemedText>
          <ThemedText
            variant="small"
            color="success"
            style={styles.metricValue}
          >
            KES{" "}
            {statsLoading
              ? "..."
              : (fulfillmentStats.totalRevenue || 0).toLocaleString()}
          </ThemedText>
        </View>
      </View>

      {/* Shipping Rates Summary */}
      <View style={styles.ratesContainer}>
        <ThemedText variant="bodyMedium" style={utils.mb("xs")}>
          Active Shipping Rates
        </ThemedText>
        {ratesLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
            <ThemedText
              variant="small"
              color="secondary"
              style={utils.ml("xs")}
            >
              Loading rates...
            </ThemedText>
          </View>
        ) : (
          <View style={styles.ratesList}>
            {shippingRates?.slice(0, 3).map((rate) => (
              <View key={rate.id} style={styles.rateItem}>
                <ThemedText variant="small">
                  {rate.deliveryMethod.charAt(0).toUpperCase() +
                    rate.deliveryMethod.slice(1)}
                </ThemedText>
                <ThemedText variant="small" color="primary">
                  KES {rate.baseFee}
                </ThemedText>
              </View>
            ))}
            {shippingRates && shippingRates.length > 3 && (
              <ThemedText
                variant="small"
                color="secondary"
                style={styles.moreRates}
              >
                +{shippingRates.length - 3} more rates
              </ThemedText>
            )}
          </View>
        )}
      </View>

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <PermissionGate requiredPermissions={["create_fulfillments"]}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleCreateFulfillment}
          >
            <IconSymbol
              name="plus.circle.fill"
              size={16}
              color={theme.colors.primary}
            />
            <ThemedText variant="small" color="primary" style={utils.ml("xs")}>
              Create
            </ThemedText>
          </TouchableOpacity>
        </PermissionGate>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleNavigateToFulfillment}
        >
          <IconSymbol
            name="list.bullet"
            size={16}
            color={theme.colors.primary}
          />
          <ThemedText variant="small" color="primary" style={utils.ml("xs")}>
            Manage
          </ThemedText>
        </TouchableOpacity>

        <PermissionGate requiredPermissions={["manage_shipping_rates"]}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleManageRates}
          >
            <IconSymbol name="gear" size={16} color={theme.colors.primary} />
            <ThemedText variant="small" color="primary" style={utils.ml("xs")}>
              Rates
            </ThemedText>
          </TouchableOpacity>
        </PermissionGate>
      </View>
    </ModernCard>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      padding: 16,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 16,
    },
    titleContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    viewAllButton: {
      flexDirection: "row",
      alignItems: "center",
      gap: 4,
    },
    statsGrid: {
      flexDirection: "row",
      gap: 16,
      marginBottom: 16,
    },
    statItem: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      gap: 12,
    },
    statIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.surfaceSecondary,
      justifyContent: "center",
      alignItems: "center",
    },
    statContent: {
      flex: 1,
    },
    statValue: {
      fontSize: 20,
      fontWeight: "bold",
      lineHeight: 24,
    },
    metricsContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 12,
      marginBottom: 16,
    },
    metricRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 4,
    },
    metricValue: {
      fontWeight: "600",
    },
    ratesContainer: {
      marginBottom: 16,
    },
    loadingContainer: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 8,
    },
    ratesList: {
      gap: 6,
    },
    rateItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 4,
      paddingHorizontal: 8,
      backgroundColor: theme.colors.surface,
      borderRadius: 6,
    },
    moreRates: {
      textAlign: "center",
      fontStyle: "italic",
      paddingVertical: 4,
    },
    actionsContainer: {
      flexDirection: "row",
      gap: 8,
    },
    actionButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: 10,
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
  });
