/**
 * Ticket Indicator Component
 * 
 * Displays active ticket information in the GlobalHeader with real-time updates.
 * Provides quick access to ticket management and switching functionality.
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppSelector } from '@/src/store';
import { selectActiveTicket, selectAllTickets } from '@/src/store/slices/ticketSlice';
import { formatCurrency } from '@/src/utils/currencyUtils';
import { Typography, Spacing } from '@/constants/Design';

interface TicketIndicatorProps {
  onPress?: () => void;
  onSwitchTicket?: () => void;
  compact?: boolean;
}

export function TicketIndicator({ 
  onPress, 
  onSwitchTicket, 
  compact = false 
}: TicketIndicatorProps) {
  const activeTicket = useAppSelector(selectActiveTicket);
  const allTickets = useAppSelector(selectAllTickets);
  
  const backgroundColor = useThemeColor({}, 'surface');
  const textColor = useThemeColor({}, 'text');
  const primaryColor = useThemeColor({}, 'primary');
  const borderColor = useThemeColor({}, 'border');
  const successColor = useThemeColor({}, 'success');

  // Don't render if no active ticket
  if (!activeTicket) {
    return null;
  }

  const itemCount = activeTicket.items?.reduce((total, item) => total + item.quantity, 0) || 0;
  const hasMultipleTickets = allTickets.length > 1;
  const ticketName = activeTicket.name || `Ticket ${activeTicket.id.slice(-4)}`;

  const styles = createStyles({
    backgroundColor,
    textColor,
    primaryColor,
    borderColor,
    successColor,
  });

  if (compact) {
    return (
      <TouchableOpacity
        style={styles.compactContainer}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={styles.compactContent}>
          <View style={styles.ticketDot} />
          <Text style={styles.compactText} numberOfLines={1}>
            {itemCount} items
          </Text>
        </View>
        <Text style={styles.compactTotal}>
          {formatCurrency(activeTicket.total || 0)}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {/* Ticket Info */}
        <View style={styles.ticketInfo}>
          <View style={styles.ticketHeader}>
            <View style={styles.ticketDot} />
            <Text style={styles.ticketName} numberOfLines={1}>
              {ticketName}
            </Text>
            {hasMultipleTickets && (
              <TouchableOpacity
                style={styles.switchButton}
                onPress={onSwitchTicket}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name="arrow.triangle.2.circlepath"
                  size={14}
                  color={primaryColor}
                />
              </TouchableOpacity>
            )}
          </View>
          
          <View style={styles.ticketDetails}>
            <Text style={styles.itemCount}>
              {itemCount} item{itemCount === 1 ? '' : 's'}
            </Text>
            <Text style={styles.total}>
              {formatCurrency(activeTicket.total || 0)}
            </Text>
          </View>
        </View>

        {/* Action Arrow */}
        <View style={styles.actionArrow}>
          <IconSymbol
            name="chevron.right"
            size={16}
            color={primaryColor}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
}

const createStyles = ({
  backgroundColor,
  textColor,
  primaryColor,
  borderColor,
  successColor,
}: {
  backgroundColor: string;
  textColor: string;
  primaryColor: string;
  borderColor: string;
  successColor: string;
}) => StyleSheet.create({
  container: {
    backgroundColor: backgroundColor,
    borderWidth: 1,
    borderColor: borderColor,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  compactContainer: {
    backgroundColor: backgroundColor,
    borderWidth: 1,
    borderColor: borderColor,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginHorizontal: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minWidth: 80,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  ticketInfo: {
    flex: 1,
  },
  ticketHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  ticketDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: successColor,
    marginRight: 6,
  },
  ticketName: {
    ...Typography.caption,
    fontSize: 12,
    fontWeight: '600',
    color: textColor,
    flex: 1,
  },
  switchButton: {
    marginLeft: 4,
    padding: 2,
  },
  ticketDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemCount: {
    ...Typography.caption,
    fontSize: 11,
    color: textColor + '80',
  },
  total: {
    ...Typography.caption,
    fontSize: 12,
    fontWeight: '700',
    color: primaryColor,
  },
  compactText: {
    ...Typography.caption,
    fontSize: 10,
    color: textColor + '80',
    marginLeft: 2,
  },
  compactTotal: {
    ...Typography.caption,
    fontSize: 11,
    fontWeight: '600',
    color: primaryColor,
  },
  actionArrow: {
    marginLeft: 8,
    opacity: 0.6,
  },
});
