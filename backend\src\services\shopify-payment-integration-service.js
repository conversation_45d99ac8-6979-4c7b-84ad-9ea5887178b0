/**
 * Shopify Payment Integration Service
 *
 * Handles integration between payment transactions and Shopify orders
 * Supports split payments, manual payment creation, and payment metadata
 */

const fetch = require("node-fetch");

class ShopifyPaymentIntegrationService {
  constructor() {
    this.shopifyConfig = {
      shopDomain: process.env.SHOPIFY_SHOP_DOMAIN,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || "2024-01",
    };

    this.baseUrl = `https://${this.shopifyConfig.shopDomain}/admin/api/${this.shopifyConfig.apiVersion}`;
    this.graphqlUrl = `https://${this.shopifyConfig.shopDomain}/admin/api/${this.shopifyConfig.apiVersion}/graphql.json`;
  }

  /**
   * Convert GraphQL Global ID to numeric ID
   */
  extractNumericId(globalId) {
    if (!globalId || typeof globalId !== "string") {
      return globalId;
    }

    // Check if it's a GraphQL Global ID (starts with "gid://shopify/")
    if (globalId.startsWith("gid://shopify/")) {
      const parts = globalId.split("/");
      return parts[parts.length - 1]; // Return the last part (numeric ID)
    }

    // If it's already a numeric ID, return as-is
    return globalId;
  }

  /**
   * Convert order data from GraphQL format to REST format
   */
  convertOrderDataToRestFormat(orderData) {
    const convertedData = { ...orderData };

    // Convert line items variant_id from GraphQL Global ID to numeric ID
    if (convertedData.line_items) {
      convertedData.line_items = convertedData.line_items.map((item) => ({
        ...item,
        variant_id: this.extractNumericId(item.variant_id),
      }));
    }

    // Convert customer ID and validate customer data
    if (convertedData.customer) {
      if (convertedData.customer.id) {
        convertedData.customer.id = this.extractNumericId(
          convertedData.customer.id
        );
      }

      // Ensure customer has a valid last name (Shopify requirement)
      if (
        !convertedData.customer.last_name ||
        convertedData.customer.last_name.trim() === ""
      ) {
        // If no last name, try to split first name or use a default
        const firstName = convertedData.customer.first_name || "";
        const nameParts = firstName.trim().split(" ");

        if (nameParts.length > 1) {
          // If first name contains multiple words, use the last word as last name
          convertedData.customer.first_name = nameParts.slice(0, -1).join(" ");
          convertedData.customer.last_name = nameParts[nameParts.length - 1];
        } else {
          // If only one name, use it as first name and set a default last name
          convertedData.customer.last_name = "Customer";
        }
      }
    }

    return convertedData;
  }

  /**
   * Create Shopify order with payment transaction details
   */
  async createOrderWithPaymentDetails(
    orderData,
    paymentTransaction,
    paymentMethods
  ) {
    try {
      // Debug: Check original orderData
      console.log('🔍 Debug original orderData:', {
        total_price: orderData.total_price,
        subtotal_price: orderData.subtotal_price,
        current_total_price: orderData.current_total_price,
        line_items: orderData.line_items?.map(item => ({
          price: item.price,
          quantity: item.quantity,
          total: parseFloat(item.price || 0) * parseInt(item.quantity || 0)
        }))
      });

      // Convert order data from GraphQL format to REST format
      const convertedOrderData = this.convertOrderDataToRestFormat(orderData);

      // Debug: Log payment methods received
      console.log('🔍 Payment methods received by Shopify service:', paymentMethods.map(m => ({
        id: m.id,
        method_type: m.method_type,
        status: m.status,
        amount: m.amount
      })));

      // Prepare transactions for the order
      const transactions = paymentMethods
        .filter((method) => method.status !== "pending")
        .map((method) => {
          let transactionStatus, transactionKind;

          if (method.status === "completed") {
            transactionStatus = "success";
            transactionKind = "sale";
          } else if (method.status === "authorized") {
            transactionStatus = "success";
            transactionKind = "authorization"; // Credit payments use authorization
          } else {
            transactionStatus = "failure";
            transactionKind = "void";
          }

          return {
            kind: transactionKind,
            status: transactionStatus,
            amount: parseFloat(method.amount).toFixed(2),
            currency: "KES",
            gateway: this.getShopifyGatewayName(method.method_type),
            source_name: "pos",
            test: false,
            ...this.getPaymentMethodDetails(method),
          };
        });

      console.log('🔍 Transactions created for Shopify:', transactions.map(t => ({
        kind: t.kind,
        status: t.status,
        amount: t.amount,
        gateway: t.gateway
      })));

      // Determine financial status based on transactions
      const completedTransactions = transactions.filter(
        (t) => t.status === "success" && t.kind === "sale"
      );
      const authorizedTransactions = transactions.filter(
        (t) => t.status === "success" && t.kind === "authorization"
      );

      // Debug: Check what's in convertedOrderData
      console.log('🔍 Debug convertedOrderData:', {
        total_price: convertedOrderData.total_price,
        subtotal_price: convertedOrderData.subtotal_price,
        current_total_price: convertedOrderData.current_total_price,
        line_items: convertedOrderData.line_items?.map(item => ({
          price: item.price,
          quantity: item.quantity,
          total: parseFloat(item.price || 0) * parseInt(item.quantity || 0)
        }))
      });

      // Calculate total amounts for proper financial status
      // If total_price is not set, calculate it from line items
      let totalOrderAmount = parseFloat(convertedOrderData.total_price || 0);
      if (totalOrderAmount === 0 && convertedOrderData.line_items) {
        totalOrderAmount = convertedOrderData.line_items.reduce((sum, item) => {
          return sum + (parseFloat(item.price || 0) * parseInt(item.quantity || 0));
        }, 0);
        console.log('🔍 Calculated totalOrderAmount from line items:', totalOrderAmount);
      }
      const totalPaidAmount = completedTransactions.reduce(
        (sum, t) => sum + parseFloat(t.amount), 0
      );
      const totalAuthorizedAmount = authorizedTransactions.reduce(
        (sum, t) => sum + parseFloat(t.amount), 0
      );

      let financialStatus = "pending";
      if (totalPaidAmount === 0 && totalAuthorizedAmount === 0) {
        financialStatus = "pending";
      } else if (totalPaidAmount >= totalOrderAmount) {
        financialStatus = "paid";
      } else if (totalAuthorizedAmount >= totalOrderAmount) {
        financialStatus = "authorized"; // Credit orders are authorized, not paid
      } else if (totalPaidAmount + totalAuthorizedAmount >= totalOrderAmount) {
        financialStatus = "authorized"; // Mixed payments with full coverage
      } else if (totalPaidAmount > 0 || totalAuthorizedAmount > 0) {
        financialStatus = "partially_paid";
      }

      console.log('🔍 Financial status calculation:', {
        totalOrderAmount,
        totalPaidAmount,
        totalAuthorizedAmount,
        calculatedFinancialStatus: financialStatus,
        completedTransactions: completedTransactions.length,
        authorizedTransactions: authorizedTransactions.length
      });

      // Prepare order data with payment metadata and transactions
      const orderPayload = {
        order: {
          ...convertedOrderData,
          financial_status: financialStatus,
          transactions: transactions, // Include transactions in order creation
          inventory_behaviour: "decrement_obeying_policy", // Automatically reduce inventory
          source_name: "Dukalink POS",
          send_receipt: false,
          send_fulfillment_receipt: false,
          note_attributes: [
            {
              name: "payment_transaction_id",
              value: paymentTransaction.id,
            },
            {
              name: "payment_breakdown",
              value: JSON.stringify(
                paymentMethods.map((method) => ({
                  method: method.method_type,
                  name: method.method_name,
                  amount: parseFloat(method.amount),
                  status: method.status,
                  transactionId: method.id,
                }))
              ),
            },
            {
              name: "split_payment",
              value: paymentMethods.length > 1 ? "true" : "false",
            },
            {
              name: "pos_staff_id",
              value: paymentTransaction.staff_id,
            },
            {
              name: "payment_processing_date",
              value: new Date().toISOString(),
            },
          ],
        },
      };

      // Create order in Shopify
      const response = await fetch(`${this.baseUrl}/orders.json`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": this.shopifyConfig.accessToken,
        },
        body: JSON.stringify(orderPayload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Shopify order creation failed: ${JSON.stringify(errorData)}`
        );
      }

      const orderResult = await response.json();
      const shopifyOrder = orderResult.order;

      console.log('🔍 Shopify order created with financial status:', {
        sentFinancialStatus: financialStatus,
        receivedFinancialStatus: shopifyOrder.financial_status,
        transactionsSent: transactions.length,
        transactionsReceived: shopifyOrder.transactions?.length || 0
      });

      return {
        success: true,
        shopifyOrderId: shopifyOrder.id,
        orderNumber: shopifyOrder.order_number,
        order: shopifyOrder,
      };
    } catch (error) {
      console.error(
        "Error creating Shopify order with payment details:",
        error
      );
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create manual payments in Shopify for each payment method
   */
  async createManualPayments(shopifyOrderId, paymentMethods, currency = "KES") {
    try {
      const createdPayments = [];

      for (const method of paymentMethods) {
        if (method.status === "pending") {
          continue; // Skip pending methods
        }

        // Determine transaction status and kind based on payment method status
        const transactionStatus =
          method.status === "completed" ? "success" : "failure";
        // Use "sale" for successful manual payments (immediate capture), "void" for failed ones
        const transactionKind = method.status === "completed" ? "sale" : "void";

        const paymentData = {
          transaction: {
            kind: transactionKind,
            status: transactionStatus,
            amount: parseFloat(method.amount).toFixed(2),
            currency: currency,
            gateway: this.getShopifyGatewayName(method.method_type),
            source_name: "pos",
            receipt: {
              paid_amount:
                method.status === "completed"
                  ? parseFloat(method.amount).toFixed(2)
                  : "0.00",
            },
            // Add payment method specific details
            ...this.getPaymentMethodDetails(method),
          },
        };

        const response = await fetch(
          `${this.baseUrl}/orders/${shopifyOrderId}/transactions.json`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-Shopify-Access-Token": this.shopifyConfig.accessToken,
            },
            body: JSON.stringify(paymentData),
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          console.error(
            `Failed to create payment for method ${method.method_type}:`,
            errorData
          );
          continue; // Continue with other payments
        }

        const paymentResult = await response.json();
        createdPayments.push({
          methodId: method.id,
          methodType: method.method_type,
          amount: method.amount,
          shopifyTransactionId: paymentResult.transaction.id,
          status: "created",
        });
      }

      return {
        success: true,
        createdPayments,
        totalPayments: createdPayments.length,
      };
    } catch (error) {
      console.error("Error creating manual payments in Shopify:", error);
      return {
        success: false,
        error: error.message,
        createdPayments: [],
      };
    }
  }

  /**
   * Update Shopify order financial status based on payment completion
   */
  async updateOrderFinancialStatus(shopifyOrderId, paymentStatus) {
    try {
      let financialStatus = "pending";

      switch (paymentStatus) {
        case "completed":
          financialStatus = "paid";
          break;
        case "failed":
          financialStatus = "pending";
          break;
        case "cancelled":
          financialStatus = "voided";
          break;
        case "processing":
          financialStatus = "pending";
          break;
        default:
          financialStatus = "pending";
      }

      const updateData = {
        order: {
          id: shopifyOrderId,
          financial_status: financialStatus,
        },
      };

      const response = await fetch(
        `${this.baseUrl}/orders/${shopifyOrderId}.json`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": this.shopifyConfig.accessToken,
          },
          body: JSON.stringify(updateData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Failed to update order financial status: ${JSON.stringify(
            errorData
          )}`
        );
      }

      const result = await response.json();

      return {
        success: true,
        financialStatus: result.order.financial_status,
        orderId: shopifyOrderId,
      };
    } catch (error) {
      console.error("Error updating Shopify order financial status:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get Shopify gateway name for payment method
   */
  getShopifyGatewayName(methodType) {
    const gatewayMap = {
      cash: "cash",
      mpesa: "mpesa",
      absa_till: "absa_till",
      card: "manual",
      credit: "manual",
    };

    return gatewayMap[methodType] || "manual";
  }

  /**
   * Get payment method specific details for Shopify transaction
   */
  getPaymentMethodDetails(method) {
    // Handle metadata that might be a string or already an object
    let metadata;
    if (typeof method.metadata === "string") {
      try {
        metadata = JSON.parse(method.metadata || "{}");
      } catch (error) {
        console.warn("Failed to parse metadata as JSON:", method.metadata);
        metadata = {};
      }
    } else if (
      typeof method.metadata === "object" &&
      method.metadata !== null
    ) {
      metadata = method.metadata;
    } else {
      metadata = {};
    }

    const details = {};

    switch (method.method_type) {
      case "mpesa":
        if (metadata.mpesaReceiptNumber) {
          details.receipt = {
            ...details.receipt,
            mpesa_receipt: metadata.mpesaReceiptNumber,
            phone_number: metadata.phoneNumber,
          };
        }
        if (metadata.transactionCode) {
          details.receipt = {
            ...details.receipt,
            transaction_code: metadata.transactionCode,
          };
        }
        break;

      case "absa_till":
        if (metadata.transactionCode) {
          details.receipt = {
            ...details.receipt,
            absa_transaction_code: metadata.transactionCode,
            till_number: metadata.tillNumber,
          };
        }
        break;

      case "card":
        if (metadata.authorizationCode) {
          details.receipt = {
            ...details.receipt,
            authorization_code: metadata.authorizationCode,
            card_type: metadata.cardType,
            last_four: metadata.lastFourDigits,
          };
        }
        break;

      case "credit":
        if (metadata.creditId) {
          details.receipt = {
            ...details.receipt,
            credit_id: metadata.creditId,
            customer_name: metadata.customerName,
            due_date: metadata.dueDate,
          };
        }
        break;

      case "cash":
        if (metadata.change) {
          details.receipt = {
            ...details.receipt,
            change_given: metadata.change,
          };
        }
        break;
    }

    return details;
  }

  /**
   * Complete order processing with payment integration
   */
  async completeOrderWithPayments(
    orderData,
    paymentTransaction,
    paymentMethods
  ) {
    try {
      // Create order in Shopify with transactions included
      const orderResult = await this.createOrderWithPaymentDetails(
        orderData,
        paymentTransaction,
        paymentMethods
      );

      if (!orderResult.success) {
        throw new Error(`Order creation failed: ${orderResult.error}`);
      }

      // Extract transaction details from the created order
      const shopifyOrder = orderResult.order;
      const createdTransactions = shopifyOrder.transactions || [];

      return {
        success: true,
        shopifyOrderId: orderResult.shopifyOrderId,
        orderNumber: orderResult.orderNumber,
        paymentsCreated: createdTransactions.length,
        financialStatus: shopifyOrder.financial_status,
        paymentDetails: createdTransactions.map((transaction) => ({
          shopifyTransactionId: transaction.id,
          kind: transaction.kind,
          status: transaction.status,
          amount: transaction.amount,
          gateway: transaction.gateway,
        })),
      };
    } catch (error) {
      console.error("Error completing order with payments:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create credit sale order with authorization (reservation) flow
   */
  async createCreditSaleOrder(orderData, creditAmount, customerInfo) {
    try {
      // Create order with pending status and authorization transaction
      const orderPayload = {
        order: {
          ...orderData,
          financial_status: "pending",
          transactions: [
            {
              kind: "authorization", // Reserve the amount
              status: "success",
              amount: creditAmount.toFixed(2),
              currency: "KES",
              gateway: "manual",
              source_name: "pos",
              test: false,
            },
          ],
          metafields: [
            {
              namespace: "dukalink",
              key: "payment_type",
              value: "credit_sale",
              type: "single_line_text_field",
            },
            {
              namespace: "dukalink",
              key: "credit_customer",
              value: JSON.stringify(customerInfo),
              type: "json",
            },
            {
              namespace: "dukalink",
              key: "receipt",
              value: JSON.stringify([
                {
                  type: "reservation",
                  timestamp: new Date().toISOString(),
                  orderTotal: creditAmount,
                  amountPaid: 0,
                  remainingBalance: creditAmount,
                  paymentMethod: "credit",
                  customerInfo,
                },
              ]),
              type: "json",
            },
          ],
        },
      };

      const response = await this.makeShopifyRequest("/orders.json", "POST", orderPayload);

      if (response.order) {
        return {
          success: true,
          shopifyOrderId: response.order.id.toString(),
          orderNumber: response.order.order_number,
          financialStatus: response.order.financial_status,
          authorizationId: response.order.transactions?.[0]?.id,
        };
      } else {
        throw new Error("Failed to create credit sale order");
      }
    } catch (error) {
      console.error("Error creating credit sale order:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Capture partial payment from authorized transaction using GraphQL
   */
  async capturePartialPayment(shopifyOrderId, amount, parentTransactionId) {
    try {
      const mutation = `
        mutation orderCapture($input: OrderCaptureInput!) {
          orderCapture(input: $input) {
            transaction {
              id
              kind
              status
              amountSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: `gid://shopify/Order/${shopifyOrderId}`,
          parentTransactionId: `gid://shopify/OrderTransaction/${parentTransactionId}`,
          amount: amount.toFixed(2),
        },
      };

      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.orderCapture?.userErrors?.length > 0) {
        throw new Error(response.data.orderCapture.userErrors[0].message);
      }

      return {
        success: true,
        transaction: response.data?.orderCapture?.transaction,
      };
    } catch (error) {
      console.error("Error capturing partial payment:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Process manual payment using the appropriate Shopify method based on order status
   * @param {string} shopifyOrderId - The Shopify order ID
   * @param {number} amount - Payment amount
   * @param {string} paymentMethod - Payment method
   * @param {string} reference - Payment reference
   * @param {Object} staffInfo - Staff information
   * @returns {Promise<Object>} Payment result
   */
  async processManualPayment(shopifyOrderId, amount, paymentMethod, reference, staffInfo) {
    try {
      console.log(`🔄 Processing manual payment for order ${shopifyOrderId}:`, {
        amount,
        paymentMethod,
        reference,
        staff: staffInfo?.name
      });

      // Get order details first to determine payment method
      const orderDetailsResponse = await this.getOrderDetails(shopifyOrderId);

      if (!orderDetailsResponse.success) {
        throw new Error(`Failed to get order details: ${orderDetailsResponse.error}`);
      }

      const orderDetails = orderDetailsResponse.order;
      console.log(`📋 Order details for payment processing:`, {
        orderId: shopifyOrderId,
        financialStatus: orderDetails.financialStatus,
        totalAmount: orderDetails.totalAmount,
        paidAmount: orderDetails.receivedAmount,
        outstandingAmount: orderDetails.outstandingAmount
      });

      // Determine the appropriate payment strategy based on order status
      const strategyInput = {
        displayFinancialStatus: orderDetails.financialStatus,
        totalAmount: orderDetails.totalAmount,
        paidAmount: orderDetails.receivedAmount,
        outstandingAmount: orderDetails.outstandingAmount
      };

      console.log(`🔍 Strategy input data:`, strategyInput);

      // WORKAROUND: Enable persistAuthorizedStatus to allow multiple payments on AUTHORIZED orders
      const workaroundOptions = {
        persistAuthorizedStatus: true // This prevents status change to PARTIALLY_PAID
      };

      console.log(`🔧 WORKAROUND ENABLED: persistAuthorizedStatus = true`);
      console.log(`📊 Current order status before payment:`, {
        status: strategyInput.displayFinancialStatus,
        totalAmount: strategyInput.totalAmount,
        paidAmount: strategyInput.paidAmount,
        outstandingAmount: strategyInput.outstandingAmount
      });

      const paymentStrategy = this.determinePaymentStrategy(strategyInput, amount, workaroundOptions);
      console.log(`🎯 Selected payment strategy:`, paymentStrategy);

      let result;
      if (paymentStrategy.method === 'capture') {
        result = await this.processOrderCapture(shopifyOrderId, amount, paymentStrategy.parentTransactionId);
      } else {
        result = await this.processOrderMarkAsPaid(shopifyOrderId);
      }

      return {
        success: true,
        orderId: shopifyOrderId,
        paymentAmount: amount,
        newFinancialStatus: result.displayFinancialStatus,
        totalReceived: result.totalReceived,
        outstanding: result.outstanding,
        paymentMethod,
        reference,
        processedBy: staffInfo?.name,
        processedAt: new Date().toISOString(),
        strategy: paymentStrategy.method
      };

    } catch (error) {
      console.error(`❌ Error processing manual payment for order ${shopifyOrderId}:`, error);
      throw error;
    }
  }

  /**
   * Determine the appropriate payment strategy based on order status
   * @param {Object} orderDetails - Order details from Shopify
   * @param {number} amount - Payment amount to process
   * @returns {Object} Payment strategy
   */
  determinePaymentStrategy(orderDetails, amount, options = {}) {
    const { displayFinancialStatus, totalAmount, paidAmount, outstandingAmount } = orderDetails;
    const { persistAuthorizedStatus = true } = options; // WORKAROUND: Default to true to allow multiple payments

    console.log(`🤔 Determining payment strategy for:`, {
      status: displayFinancialStatus,
      totalAmount,
      paidAmount,
      outstandingAmount,
      requestedAmount: amount,
      persistAuthorizedStatus
    });

    // WORKAROUND: If persistAuthorizedStatus is enabled, always use capture for AUTHORIZED orders
    // This prevents the status from changing to PARTIALLY_PAID, allowing unlimited payments
    if (persistAuthorizedStatus && displayFinancialStatus === 'AUTHORIZED') {
      console.log(`🔐 WORKAROUND: Persisting AUTHORIZED status - using orderCapture for all payments`);
      return {
        method: 'capture',
        reason: 'Persisting AUTHORIZED status to allow multiple payments (WORKAROUND)',
        parentTransactionId: null, // Will be fetched from order transactions
        isWorkaround: true
      };
    }

    // Original logic for when workaround is disabled
    // For AUTHORIZED orders with zero outstanding (authorized but not captured)
    if (displayFinancialStatus === 'AUTHORIZED' && outstandingAmount === 0) {
      console.log(`🔐 AUTHORIZED order detected - using orderCapture for first payment`);
      return {
        method: 'capture',
        reason: 'First payment on authorized order requires capture',
        parentTransactionId: null // Will be fetched from order transactions
      };
    }

    // For PARTIALLY_PAID orders or other statuses
    if (displayFinancialStatus === 'PARTIALLY_PAID' ||
        displayFinancialStatus === 'PENDING' ||
        (displayFinancialStatus === 'AUTHORIZED' && outstandingAmount > 0)) {
      console.log(`💰 ${displayFinancialStatus} order detected - using orderMarkAsPaid for subsequent payment`);
      return {
        method: 'markAsPaid',
        reason: 'Subsequent payment on partially paid order'
      };
    }

    // Default to markAsPaid for other cases
    console.log(`📝 Default strategy - using orderMarkAsPaid`);
    return {
      method: 'markAsPaid',
      reason: 'Default payment method'
    };
  }

  /**
   * Process order capture for authorized orders
   * @param {string} shopifyOrderId - The Shopify order ID
   * @param {number} amount - Amount to capture
   * @param {string} parentTransactionId - Parent transaction ID (optional)
   * @returns {Promise<Object>} Capture result
   */
  async processOrderCapture(shopifyOrderId, amount, parentTransactionId = null) {
    try {
      console.log(`🔐 Processing order capture for ${shopifyOrderId}:`, { amount, parentTransactionId });

      // If no parent transaction ID provided, fetch it from order transactions
      if (!parentTransactionId) {
        parentTransactionId = await this.getAuthorizationTransactionId(shopifyOrderId);
      }

      const mutation = `
        mutation orderCapture($input: OrderCaptureInput!) {
          orderCapture(input: $input) {
            transaction {
              id
              status
              amountSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              order {
                id
                displayFinancialStatus
                totalReceivedSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
                totalOutstandingSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: `gid://shopify/Order/${shopifyOrderId}`,
          parentTransactionId: parentTransactionId,
          amount: amount.toString(),
          currency: 'KES' // TODO: Make this dynamic based on order currency
        }
      };

      console.log(`🚀 Executing orderCapture mutation:`, variables);
      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.orderCapture?.userErrors?.length > 0) {
        const errors = response.data.orderCapture.userErrors;
        console.error(`❌ GraphQL errors in orderCapture:`, errors);
        throw new Error(`Capture failed: ${errors.map(e => e.message).join(', ')}`);
      }

      const transaction = response.data.orderCapture.transaction;
      const order = transaction.order;

      console.log(`✅ Order capture successful:`, {
        transactionId: transaction.id,
        status: transaction.status,
        capturedAmount: transaction.amountSet.shopMoney.amount,
        newFinancialStatus: order.displayFinancialStatus
      });

      // WORKAROUND DEBUGGING: Log status change behavior
      console.log(`🔍 WORKAROUND DEBUG - Order status after capture:`, {
        previousStatus: 'AUTHORIZED (expected)',
        newStatus: order.displayFinancialStatus,
        statusChanged: order.displayFinancialStatus !== 'AUTHORIZED',
        workaroundEffect: order.displayFinancialStatus === 'AUTHORIZED' ? 'SUCCESS - Status preserved' : 'FAILED - Status changed',
        totalReceived: order.totalReceivedSet?.shopMoney?.amount,
        totalOutstanding: order.totalOutstandingSet?.shopMoney?.amount
      });

      return {
        displayFinancialStatus: order.displayFinancialStatus,
        totalReceived: parseFloat(order.totalReceivedSet.shopMoney.amount),
        outstanding: parseFloat(order.totalOutstandingSet.shopMoney.amount),
        transactionId: transaction.id,
        capturedAmount: parseFloat(transaction.amountSet.shopMoney.amount)
      };

    } catch (error) {
      console.error(`❌ Error processing order capture:`, error);
      throw error;
    }
  }

  /**
   * Process order mark as paid for subsequent payments
   * @param {string} shopifyOrderId - The Shopify order ID
   * @returns {Promise<Object>} Mark as paid result
   */
  async processOrderMarkAsPaid(shopifyOrderId) {
    try {
      console.log(`💰 Processing orderMarkAsPaid for ${shopifyOrderId}`);

      const mutation = `
        mutation orderMarkAsPaid($input: OrderMarkAsPaidInput!) {
          orderMarkAsPaid(input: $input) {
            order {
              id
              displayFinancialStatus
              totalReceivedSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              totalOutstandingSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: `gid://shopify/Order/${shopifyOrderId}`
        }
      };

      console.log(`🚀 Executing orderMarkAsPaid mutation for ${shopifyOrderId}`);
      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.orderMarkAsPaid?.userErrors?.length > 0) {
        const errors = response.data.orderMarkAsPaid.userErrors;
        console.error(`❌ GraphQL errors in orderMarkAsPaid:`, errors);
        throw new Error(`Mark as paid failed: ${errors.map(e => e.message).join(', ')}`);
      }

      const order = response.data.orderMarkAsPaid.order;

      console.log(`✅ Order marked as paid successfully:`, {
        orderId: shopifyOrderId,
        newStatus: order.displayFinancialStatus,
        totalReceived: order.totalReceivedSet.shopMoney.amount
      });

      return {
        displayFinancialStatus: order.displayFinancialStatus,
        totalReceived: parseFloat(order.totalReceivedSet.shopMoney.amount),
        outstanding: parseFloat(order.totalOutstandingSet.shopMoney.amount)
      };

    } catch (error) {
      console.error(`❌ Error processing orderMarkAsPaid:`, error);
      throw error;
    }
  }

  /**
   * Get authorization transaction ID from order transactions
   * @param {string} shopifyOrderId - The Shopify order ID
   * @returns {Promise<string>} Authorization transaction ID
   */
  async getAuthorizationTransactionId(shopifyOrderId) {
    try {
      console.log(`🔍 Fetching authorization transaction for order ${shopifyOrderId}`);

      const query = `
        query getOrderTransactions($id: ID!) {
          order(id: $id) {
            transactions(first: 10) {
              id
              kind
              status
              parentTransaction {
                id
              }
            }
          }
        }
      `;

      const variables = { id: `gid://shopify/Order/${shopifyOrderId}` };
      const response = await this.graphqlRequest(query, variables);

      const transactions = response.data?.order?.transactions || [];

      // Find the authorization transaction
      const authTransaction = transactions.find(t =>
        t.kind === 'AUTHORIZATION' && t.status === 'SUCCESS'
      );

      if (!authTransaction) {
        throw new Error('No successful authorization transaction found for this order');
      }

      console.log(`✅ Found authorization transaction:`, authTransaction.id);
      return authTransaction.id;

    } catch (error) {
      console.error(`❌ Error fetching authorization transaction:`, error);
      throw error;
    }
  }

  /**
   * Add manual payment using GraphQL (for additional payments beyond authorization)
   */
  async addManualPayment(shopifyOrderId, amount, paymentMethodName = "manual") {
    try {
      const mutation = `
        mutation orderMarkAsPaid($input: OrderMarkAsPaidInput!) {
          orderMarkAsPaid(input: $input) {
            order {
              id
              displayFinancialStatus
              totalReceivedSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              totalOutstandingSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: `gid://shopify/Order/${shopifyOrderId}`,
        },
      };

      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.orderMarkAsPaid?.userErrors?.length > 0) {
        throw new Error(response.data.orderMarkAsPaid.userErrors[0].message);
      }

      return {
        success: true,
        order: response.data?.orderMarkAsPaid?.order,
      };
    } catch (error) {
      console.error("Error adding manual payment:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update receipt metafield with new payment entry
   */
  async updateReceiptMetafield(shopifyOrderId, receiptEntry) {
    try {
      // First, get current receipts
      const currentReceipts = await this.getOrderMetafield(shopifyOrderId, "dukalink", "receipt");
      let receipts = [];

      if (currentReceipts) {
        try {
          receipts = JSON.parse(currentReceipts);
        } catch (e) {
          console.warn("Failed to parse existing receipts, starting fresh");
        }
      }

      // Add new receipt entry
      receipts.push(receiptEntry);

      // Update metafield
      const mutation = `
        mutation orderUpdate($input: OrderInput!) {
          orderUpdate(input: $input) {
            order {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: `gid://shopify/Order/${shopifyOrderId}`,
          metafields: [
            {
              namespace: "dukalink",
              key: "receipt",
              value: JSON.stringify(receipts),
              type: "json",
            },
          ],
        },
      };

      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.orderUpdate?.userErrors?.length > 0) {
        throw new Error(response.data.orderUpdate.userErrors[0].message);
      }

      return {
        success: true,
        receipts,
      };
    } catch (error) {
      console.error("Error updating receipt metafield:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get order details including transactions and payment status
   */
  async getOrderDetails(shopifyOrderId) {
    try {
      console.log(`🔍 Getting order details for Shopify Order ID: ${shopifyOrderId}`);

      const query = `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            totalOutstandingSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            totalReceivedSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            displayFinancialStatus
            transactions {
              id
              kind
              status
              amount
              gateway
              parentTransaction {
                id
              }
            }
            metafield(namespace: "dukalink", key: "receipt") {
              value
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Order/${shopifyOrderId}`,
      };

      console.log(`📡 Making GraphQL request with variables:`, variables);
      const result = await this.graphqlRequest(query, variables);
      console.log(`✅ GraphQL request successful`);


      if (result.data?.order) {
        const order = result.data.order;

        console.log(`📊 Raw Shopify order data:`, {
          id: order.id,
          name: order.name,
          totalPriceSet: order.totalPriceSet,
          totalOutstandingSet: order.totalOutstandingSet,
          totalReceivedSet: order.totalReceivedSet,
          displayFinancialStatus: order.displayFinancialStatus,
          transactionCount: order.transactions?.length || 0,
          transactions: order.transactions,
          metafield: order.metafield?.value
        });

        const totalAmount = parseFloat(order.totalPriceSet.shopMoney.amount);
        const outstandingAmount = parseFloat(order.totalOutstandingSet.shopMoney.amount);
        const receivedAmount = parseFloat(order.totalReceivedSet.shopMoney.amount);

        console.log(`💰 Parsed amounts - Total: ${totalAmount}, Outstanding: ${outstandingAmount}, Received: ${receivedAmount}`);

        return {
          success: true,
          order: {
            id: order.id,
            name: order.name,
            totalAmount,
            outstandingAmount,
            receivedAmount,
            financialStatus: order.displayFinancialStatus,
            transactions: order.transactions,
            receipts: order.metafield?.value ? JSON.parse(order.metafield.value) : [],
          },
        };
      } else {
        throw new Error("Order not found");
      }
    } catch (error) {
      console.error("Error getting order details:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Helper method to get order metafield value
   */
  async getOrderMetafield(shopifyOrderId, namespace, key) {
    try {
      const query = `
        query getOrder($id: ID!) {
          order(id: $id) {
            metafield(namespace: "${namespace}", key: "${key}") {
              value
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Order/${shopifyOrderId}`,
      };

      const response = await this.graphqlRequest(query, variables);
      return response.data?.order?.metafield?.value || null;
    } catch (error) {
      console.error("Error getting order metafield:", error);
      return null;
    }
  }

  /**
   * Helper method to make GraphQL requests
   */
  async graphqlRequest(query, variables = {}) {
    const shopifyService = require("./shopify-service");
    return await shopifyService.graphqlRequest(query, variables);
  }
}

module.exports = ShopifyPaymentIntegrationService;
