import { createSlice, PayloadAction, createSelector } from "@reduxjs/toolkit";
import {
  <PERSON><PERSON>,
  CartI<PERSON>,
  Customer,
  OrderDiscount,
  Salesperson,
} from "../../types/shopify";

interface CartState extends Cart {
  isLoading: boolean;
  error: string | null;
  selectedSalesperson: Salesperson | null;
}

const initialState: CartState = {
  items: [],
  subtotal: 0,
  tax: 0, // Keep for compatibility but will always be 0
  total: 0,
  discounts: [],
  customer: undefined,
  note: undefined,
  isLoading: false,
  error: null,
  selectedSalesperson: null,
};

const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const existingItem = state.items.find(
        (item) => item.variantId === action.payload.variantId
      );

      if (existingItem) {
        // Check inventory before adding
        if (
          existingItem.quantity + action.payload.quantity <=
          existingItem.inventoryQuantity
        ) {
          existingItem.quantity += action.payload.quantity;
        } else {
          state.error = "Insufficient inventory";
          return;
        }
      } else {
        // Check inventory for new item
        if (action.payload.quantity <= action.payload.inventoryQuantity) {
          state.items.push(action.payload);
        } else {
          state.error = "Insufficient inventory";
          return;
        }
      }

      cartSlice.caseReducers.calculateTotals(state);
    },

    removeFromCart: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(
        (item) => item.variantId !== action.payload
      );
      cartSlice.caseReducers.calculateTotals(state);
    },

    updateQuantity: (
      state,
      action: PayloadAction<{ variantId: string; quantity: number }>
    ) => {
      const { variantId, quantity } = action.payload;
      const item = state.items.find((item) => item.variantId === variantId);

      if (item) {
        if (quantity <= 0) {
          state.items = state.items.filter(
            (item) => item.variantId !== variantId
          );
        } else if (quantity <= item.inventoryQuantity) {
          item.quantity = quantity;
        } else {
          state.error = "Insufficient inventory";
          return;
        }
      }

      cartSlice.caseReducers.calculateTotals(state);
    },

    clearCart: (state) => {
      state.items = [];
      state.subtotal = 0;
      state.tax = 0; // Keep for compatibility but will always be 0
      state.total = 0;
      state.discounts = [];
      state.customer = undefined;
      state.note = undefined;
      state.selectedSalesperson = null;
      state.error = null;
    },

    setCustomer: (state, action: PayloadAction<Customer | undefined>) => {
      state.customer = action.payload;
      cartSlice.caseReducers.calculateTotals(state);
    },

    setSalesperson: (state, action: PayloadAction<Salesperson | null>) => {
      state.selectedSalesperson = action.payload;
    },

    addDiscount: (state, action: PayloadAction<OrderDiscount>) => {
      // Remove existing discount with same code if any
      if (action.payload.code) {
        state.discounts = state.discounts.filter(
          (discount) => discount.code !== action.payload.code
        );
      }

      state.discounts.push(action.payload);
      cartSlice.caseReducers.calculateTotals(state);
    },

    removeDiscount: (state, action: PayloadAction<string>) => {
      state.discounts = state.discounts.filter(
        (discount) => discount.id !== action.payload
      );
      cartSlice.caseReducers.calculateTotals(state);
    },

    setNote: (state, action: PayloadAction<string>) => {
      state.note = action.payload;
    },

    calculateTotals: (state) => {
      // Calculate subtotal with item-level discounts
      let subtotalBeforeOrderDiscounts = 0;

      state.items.forEach((item) => {
        const lineTotal = parseFloat(item.price) * item.quantity;
        let itemDiscountAmount = 0;

        // Apply item-level discount
        if (item.discount && item.discount.amount > 0) {
          if (item.discount.type === "percentage") {
            itemDiscountAmount = (lineTotal * item.discount.amount) / 100;
          } else {
            itemDiscountAmount = Math.min(item.discount.amount, lineTotal);
          }
        }

        subtotalBeforeOrderDiscounts += Math.max(
          0,
          lineTotal - itemDiscountAmount
        );
      });

      state.subtotal = subtotalBeforeOrderDiscounts;

      // Apply order-level discounts
      let orderDiscountAmount = 0;
      state.discounts.forEach((discount) => {
        if (discount.type === "percentage") {
          orderDiscountAmount +=
            state.subtotal * (parseFloat(discount.amount) / 100);
        } else {
          orderDiscountAmount += parseFloat(discount.amount);
        }
      });

      const finalTotal = Math.max(0, state.subtotal - orderDiscountAmount);

      // No tax calculation - POS system no longer calculates taxes
      state.tax = 0;

      // Calculate total (equals discounted subtotal since no tax)
      state.total = finalTotal;

      // Round to 2 decimal places
      state.subtotal = Math.round(state.subtotal * 100) / 100;
      state.tax = 0; // Always 0
      state.total = Math.round(state.total * 100) / 100;
    },

    updateInventoryQuantity: (
      state,
      action: PayloadAction<{ variantId: string; quantity: number }>
    ) => {
      const { variantId, quantity } = action.payload;
      const item = state.items.find((item) => item.variantId === variantId);

      if (item) {
        item.inventoryQuantity = quantity;

        // If cart quantity exceeds available inventory, adjust it
        if (item.quantity > quantity) {
          if (quantity <= 0) {
            state.items = state.items.filter(
              (item) => item.variantId !== variantId
            );
          } else {
            item.quantity = quantity;
          }
          cartSlice.caseReducers.calculateTotals(state);
        }
      }
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    clearError: (state) => {
      state.error = null;
    },

    // Enhanced cart editing actions
    applyItemDiscount: (
      state,
      action: PayloadAction<{
        variantId: string;
        discount: {
          type: "percentage" | "fixed_amount";
          amount: number;
          description?: string;
        } | null;
      }>
    ) => {
      const { variantId, discount } = action.payload;
      const item = state.items.find((item) => item.variantId === variantId);

      if (item) {
        item.discount = discount || undefined;
        cartSlice.caseReducers.calculateTotals(state);
      }
    },

    updateItemNotes: (
      state,
      action: PayloadAction<{ variantId: string; notes: string }>
    ) => {
      const { variantId, notes } = action.payload;
      const item = state.items.find((item) => item.variantId === variantId);

      if (item) {
        item.notes = notes.trim() || undefined;
      }
    },

    updateItemPrice: (
      state,
      action: PayloadAction<{
        variantId: string;
        newPrice: number;
        originalPrice?: number | null;
      }>
    ) => {
      const { variantId, newPrice, originalPrice } = action.payload;
      const item = state.items.find((item) => item.variantId === variantId);

      if (item) {
        // Store original price if not already stored
        if (originalPrice !== null && !(item as any).originalPrice) {
          (item as any).originalPrice = originalPrice;
        }

        // Update the price
        item.price = newPrice.toString();

        // If resetting to original price, clear the originalPrice field
        if (originalPrice === null) {
          delete (item as any).originalPrice;
        }

        // Recalculate totals
        cartSlice.caseReducers.calculateTotals(state);
      }
    },
  },
});

export const {
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  setCustomer,
  setSalesperson,
  addDiscount,
  removeDiscount,
  setNote,
  calculateTotals,
  updateInventoryQuantity,
  setLoading,
  setError,
  clearError,
  applyItemDiscount,
  updateItemNotes,
  updateItemPrice,
} = cartSlice.actions;

export default cartSlice.reducer;

// Selectors
export const selectCartItems = (state: { cart: CartState }) => state.cart.items;
export const selectCartTotal = (state: { cart: CartState }) => state.cart.total;
export const selectCartSubtotal = (state: { cart: CartState }) =>
  state.cart.subtotal;
export const selectCartTax = (state: { cart: CartState }) => state.cart.tax;
// Memoized selector for cart item count to prevent unnecessary re-renders
export const selectCartItemCount = createSelector([selectCartItems], (items) =>
  items.reduce((total, item) => total + item.quantity, 0)
);
export const selectCartCustomer = (state: { cart: CartState }) =>
  state.cart.customer;
export const selectCartSalesperson = (state: { cart: CartState }) =>
  state.cart.selectedSalesperson;
export const selectCartDiscounts = (state: { cart: CartState }) =>
  state.cart.discounts;
export const selectCartNote = (state: { cart: CartState }) => state.cart.note;
export const selectCartError = (state: { cart: CartState }) => state.cart.error;
export const selectCartLoading = (state: { cart: CartState }) =>
  state.cart.isLoading;
