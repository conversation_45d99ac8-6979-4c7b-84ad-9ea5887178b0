import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  <PERSON><PERSON>,
  Linking,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface BluetoothTroubleshootingGuideProps {
  onClose: () => void;
}

export default function BluetoothTroubleshootingGuide({
  onClose,
}: BluetoothTroubleshootingGuideProps) {
  const handleOpenBluetoothSettings = () => {
    Alert.alert(
      "Open Bluetooth Settings",
      "This will open your device Bluetooth settings where you can pair your thermal printer.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Open Settings",
          onPress: () => {
            // On Android, this should open Bluetooth settings
            Linking.sendIntent("android.settings.BLUETOOTH_SETTINGS").catch(
              () => {
                Alert.alert(
                  "Error",
                  "Could not open Bluetooth settings. Please open them manually."
                );
              }
            );
          },
        },
      ]
    );
  };

  const troubleshootingSteps = [
    {
      title: "1. Pair Your Printer First",
      description:
        "For best results, pair your thermal printer in Android Bluetooth settings first. The app will find both paired and discoverable devices.",
      action: "Open Bluetooth Settings",
      onAction: handleOpenBluetoothSettings,
      icon: "bluetooth",
      color: "#007AFF",
    },
    {
      title: "2. Turn On Your Printer",
      description:
        "Make sure your thermal printer is powered on and in pairing/discoverable mode.",
      icon: "power",
      color: "#4CAF50",
    },
    {
      title: "3. Check Printer Distance",
      description:
        "Keep your thermal printer within 10 meters of your device during pairing and connection.",
      icon: "radio",
      color: "#FF9500",
    },
    {
      title: "4. Enable Location Services",
      description:
        "Android 12+ requires location services to be enabled for Bluetooth scanning.",
      icon: "location",
      color: "#9C27B0",
    },
    {
      title: "5. Grant App Permissions",
      description:
        "Make sure the app has Bluetooth Connect and Bluetooth Scan permissions.",
      icon: "shield-checkmark",
      color: "#2196F3",
    },
    {
      title: "6. Restart Bluetooth",
      description:
        "If scanning still fails, try turning Bluetooth off and on again in Android settings.",
      icon: "refresh",
      color: "#FF6B35",
    },
  ];

  const commonPrinterNames = [
    "XPrinter",
    "EPSON TM",
    "Star TSP",
    "Bixolon SRP",
    "Citizen CT",
    "POS Printer",
    "Thermal Printer",
    "Receipt Printer",
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Bluetooth Troubleshooting</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Why No Devices Found?</Text>
          <Text style={styles.sectionDescription}>
            If scanning doesn't find your thermal printer, follow these steps:
          </Text>
        </View>

        {troubleshootingSteps.map((step, index) => (
          <View key={index} style={styles.stepCard}>
            <View style={styles.stepHeader}>
              <Ionicons name={step.icon as any} size={24} color={step.color} />
              <Text style={styles.stepTitle}>{step.title}</Text>
            </View>
            <Text style={styles.stepDescription}>{step.description}</Text>
            {step.action && step.onAction && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: step.color }]}
                onPress={step.onAction}
              >
                <Text style={styles.actionButtonText}>{step.action}</Text>
              </TouchableOpacity>
            )}
          </View>
        ))}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Common Printer Names</Text>
          <Text style={styles.sectionDescription}>
            Look for devices with these names in Bluetooth settings:
          </Text>
          <View style={styles.printerNamesContainer}>
            {commonPrinterNames.map((name, index) => (
              <View key={index} style={styles.printerNameChip}>
                <Text style={styles.printerNameText}>{name}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Still Having Issues?</Text>
          <Text style={styles.sectionDescription}>
            If you're still unable to find your thermal printer:
          </Text>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>
              • Check if your printer supports ESC/POS commands
            </Text>
            <Text style={styles.tipItem}>
              • Try restarting both your device and the printer
            </Text>
            <Text style={styles.tipItem}>
              • Make sure no other device is connected to the printer
            </Text>
            <Text style={styles.tipItem}>
              • Check the printer manual for pairing instructions
            </Text>
            <Text style={styles.tipItem}>
              • Some printers require a specific pairing PIN (often 0000 or
              1234)
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Alternative: Network Printer</Text>
          <Text style={styles.sectionDescription}>
            If Bluetooth continues to have issues, consider using a
            network-enabled thermal printer connected via WiFi.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
  stepCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: "#007AFF",
  },
  stepHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginLeft: 12,
    flex: 1,
  },
  stepDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 12,
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    alignSelf: "flex-start",
  },
  actionButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  printerNamesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
  },
  printerNameChip: {
    backgroundColor: "#f0f8ff",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 4,
    borderWidth: 1,
    borderColor: "#007AFF",
  },
  printerNameText: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "500",
  },
  tipsList: {
    marginTop: 8,
  },
  tipItem: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 4,
  },
});
