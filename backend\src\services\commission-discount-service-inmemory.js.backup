/**
 * Commission-Based Discount Service
 *
 * Manages advanced discount functionality that integrates with:
 * - Sales Agent Attribution System
 * - Staff Commission Tracking
 * - Customer Loyalty Programs
 * - Shopify Functions for real-time discount calculation
 */

const shopifyService = require("./shopify-service");
const staffAttributionService = require("./staff-attribution-service");
const salesAgentService = require("./sales-agent-service");

class CommissionDiscountService {
  constructor() {
    // Default discount configuration
    this.defaultConfig = {
      staff_discount_rate: 2.0, // 2% base discount for staff attribution
      agent_discount_rate: 3.0, // 3% base discount for sales agent attribution
      loyalty_multiplier: 1.1, // 10% bonus for loyalty program members
      min_order_amount: 50.0, // Minimum KSh 50 order for discount eligibility
      max_discount_percentage: 12.0, // Maximum 12% total discount
      commission_threshold: 1.0, // Minimum 1% commission rate to qualify
      enabled: true, // Global enable/disable flag
    };

    // Loyalty tier multipliers
    this.loyaltyTiers = {
      bronze: 1.0, // No additional multiplier
      silver: 1.2, // 20% bonus
      gold: 1.5, // 50% bonus
      platinum: 2.0, // 100% bonus
    };
  }

  // Calculate commission-based discount for a cart
  async calculateCommissionDiscount(
    cartData,
    staffId,
    salesAgentId,
    customerId = null
  ) {
    try {
      // Get configuration
      const config = await this.getDiscountConfiguration();

      if (!config.enabled) {
        return {
          success: true,
          discount: null,
          reason: "Commission discounts are disabled",
        };
      }

      // Validate minimum order amount
      const orderTotal = parseFloat(cartData.subtotal || 0);
      if (orderTotal < config.min_order_amount) {
        return {
          success: true,
          discount: null,
          reason: `Order total $${orderTotal} is below minimum $${config.min_order_amount}`,
        };
      }

      // Get staff information
      let staffInfo = null;
      if (staffId) {
        const staffResult = await staffAttributionService.getStaffById(staffId);
        if (staffResult.success) {
          staffInfo = staffResult.staffMember;
        }
      }

      // Get sales agent information
      let agentInfo = null;
      if (salesAgentId) {
        const agentResult = await salesAgentService.getSalesAgentById(
          salesAgentId
        );
        if (agentResult.success) {
          agentInfo = agentResult.salesAgent;
        }
      }

      // Get customer loyalty tier
      const loyaltyTier = await this.getCustomerLoyaltyTier(customerId);

      // Calculate base discount rate
      let discountRate = 0.0;

      // Add staff-based discount
      if (staffInfo) {
        discountRate += config.staff_discount_rate;
      }

      // Add sales agent-based discount
      if (agentInfo) {
        discountRate += config.agent_discount_rate;
      }

      // Apply loyalty multiplier
      const loyaltyMultiplier = this.loyaltyTiers[loyaltyTier] || 1.0;
      discountRate *= loyaltyMultiplier * config.loyalty_multiplier;

      // Cap the discount rate
      if (discountRate > config.max_discount_percentage) {
        discountRate = config.max_discount_percentage;
      }

      // Check commission threshold
      if (discountRate < config.commission_threshold) {
        return {
          success: true,
          discount: null,
          reason: `Calculated discount ${discountRate}% is below threshold ${config.commission_threshold}%`,
        };
      }

      // Calculate discount amount
      const discountAmount = (orderTotal * discountRate) / 100;

      return {
        success: true,
        discount: {
          rate: discountRate,
          amount: discountAmount,
          orderTotal: orderTotal,
          staffInfo: staffInfo
            ? {
                id: staffInfo.id,
                name: staffInfo.name,
                role: staffInfo.role,
              }
            : null,
          agentInfo: agentInfo
            ? {
                id: agentInfo.id,
                name: agentInfo.name,
                territory: agentInfo.territory,
              }
            : null,
          loyaltyTier: loyaltyTier,
          loyaltyMultiplier: loyaltyMultiplier,
          configuration: config,
        },
      };
    } catch (error) {
      console.error("Calculate commission discount error:", error);
      return {
        success: false,
        error: "Failed to calculate commission discount",
      };
    }
  }

  // Create a Shopify discount using the Admin API
  async createCommissionDiscount(discountData, staffId, salesAgentId) {
    try {
      const calculation = await this.calculateCommissionDiscount(
        discountData.cartData,
        staffId,
        salesAgentId,
        discountData.customerId
      );

      if (!calculation.success || !calculation.discount) {
        return {
          success: false,
          error: calculation.error || calculation.reason,
        };
      }

      const discount = calculation.discount;

      // Create automatic discount using Shopify Admin API
      const mutation = `
        mutation discountAutomaticBasicCreate($automaticBasicDiscount: DiscountAutomaticBasicInput!) {
          discountAutomaticBasicCreate(automaticBasicDiscount: $automaticBasicDiscount) {
            automaticDiscountNode {
              id
              automaticDiscount {
                ... on DiscountAutomaticBasic {
                  title
                  summary
                  status
                  startsAt
                  endsAt
                  customerGets {
                    value {
                      ... on DiscountPercentage {
                        percentage
                      }
                    }
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        automaticBasicDiscount: {
          title: `Commission Discount - ${discount.rate.toFixed(1)}%`,
          summary: `Staff: ${discount.staffInfo?.name || "N/A"}, Agent: ${
            discount.agentInfo?.name || "N/A"
          }, Loyalty: ${discount.loyaltyTier}`,
          startsAt: new Date().toISOString(),
          endsAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
          customerGets: {
            value: {
              percentage: discount.rate / 100,
            },
            items: {
              all: true,
            },
          },
          customerSelection: {
            all: true,
          },
        },
      };

      const response = await shopifyService.graphqlRequest(mutation, variables);

      if (response.data?.discountAutomaticBasicCreate?.userErrors?.length > 0) {
        return {
          success: false,
          error:
            response.data.discountAutomaticBasicCreate.userErrors[0].message,
        };
      }

      return {
        success: true,
        discount:
          response.data?.discountAutomaticBasicCreate?.automaticDiscountNode,
        calculation: calculation.discount,
      };
    } catch (error) {
      console.error("Create commission discount error:", error);
      return {
        success: false,
        error: "Failed to create commission discount",
      };
    }
  }

  // Get discount configuration (from database or default)
  async getDiscountConfiguration() {
    try {
      // In production, this would fetch from database
      // For now, return default configuration
      return this.defaultConfig;
    } catch (error) {
      console.error("Get discount configuration error:", error);
      return this.defaultConfig;
    }
  }

  // Update discount configuration
  async updateDiscountConfiguration(newConfig) {
    try {
      // Validate configuration
      const validatedConfig = {
        staff_discount_rate: Math.max(
          0,
          Math.min(newConfig.staff_discount_rate || 0, 20)
        ),
        agent_discount_rate: Math.max(
          0,
          Math.min(newConfig.agent_discount_rate || 0, 20)
        ),
        loyalty_multiplier: Math.max(
          1,
          Math.min(newConfig.loyalty_multiplier || 1, 3)
        ),
        min_order_amount: Math.max(0, newConfig.min_order_amount || 0),
        max_discount_percentage: Math.max(
          0,
          Math.min(newConfig.max_discount_percentage || 0, 50)
        ),
        commission_threshold: Math.max(0, newConfig.commission_threshold || 0),
        enabled: newConfig.enabled !== undefined ? newConfig.enabled : true,
      };

      // In production, save to database
      Object.assign(this.defaultConfig, validatedConfig);

      return {
        success: true,
        configuration: this.defaultConfig,
      };
    } catch (error) {
      console.error("Update discount configuration error:", error);
      return {
        success: false,
        error: "Failed to update discount configuration",
      };
    }
  }

  // Get customer loyalty tier
  async getCustomerLoyaltyTier(customerId) {
    try {
      if (!customerId) {
        return "bronze"; // Default tier for non-customers
      }

      // In production, this would calculate based on:
      // - Total purchase history
      // - Number of orders
      // - Customer lifetime value
      // - Referral activity

      // For now, return a simple tier based on customer ID
      const customerIdNum = parseInt(customerId.toString().slice(-2));

      if (customerIdNum >= 90) return "platinum";
      if (customerIdNum >= 70) return "gold";
      if (customerIdNum >= 40) return "silver";
      return "bronze";
    } catch (error) {
      console.error("Get customer loyalty tier error:", error);
      return "bronze";
    }
  }

  // Get discount analytics
  async getDiscountAnalytics(dateRange = {}) {
    try {
      // In production, this would query actual discount usage data
      return {
        success: true,
        analytics: {
          totalDiscountsApplied: 0,
          totalDiscountAmount: 0,
          averageDiscountRate: 0,
          discountsByStaff: {},
          discountsByAgent: {},
          discountsByLoyaltyTier: {
            bronze: 0,
            silver: 0,
            gold: 0,
            platinum: 0,
          },
          configuration: this.defaultConfig,
          dateRange: dateRange,
        },
      };
    } catch (error) {
      console.error("Get discount analytics error:", error);
      return {
        success: false,
        error: "Failed to fetch discount analytics",
      };
    }
  }
}

module.exports = new CommissionDiscountService();
