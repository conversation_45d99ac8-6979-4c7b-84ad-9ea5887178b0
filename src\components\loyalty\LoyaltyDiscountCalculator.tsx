/**
 * Loyalty Discount Calculator Component
 *
 * Calculates and displays available loyalty discounts for the current order.
 * Integrates with the payment flow to apply tier-based discounts and points redemption.
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

import {
  useLoyaltyDiscounts,
  usePointsRedemption,
} from "@/src/hooks/useLoyalty";
import { useRBAC } from "@/src/hooks/useRBAC";
import { PointsRedemptionRequest } from "@/src/types/shopify";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { PointsRedemptionModal } from "./PointsRedemptionModal";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";

interface LoyaltyDiscountCalculatorProps {
  customerId: string | null;
  orderTotal: number;
  onDiscountApplied: (
    discountAmount: number,
    discountType: "tier" | "points",
    metadata?: any
  ) => void;
  onDiscountRemoved: () => void;
  disabled?: boolean;
}

export const LoyaltyDiscountCalculator: React.FC<
  LoyaltyDiscountCalculatorProps
> = ({
  customerId,
  orderTotal,
  onDiscountApplied,
  onDiscountRemoved,
  disabled = false,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const { hasPermission } = useRBAC();

  // Check if user can view/manage loyalty
  const canViewLoyalty =
    hasPermission("view_loyalty") || hasPermission("manage_loyalty");

  // Hooks for loyalty operations
  const {
    discountCalculation,
    isCalculating,
    calculateDiscounts,
    clearDiscounts,
  } = useLoyaltyDiscounts();
  const { isRedeeming, redeemPoints } = usePointsRedemption();

  // Local state
  const [appliedDiscount, setAppliedDiscount] = useState<{
    type: "tier" | "points";
    amount: number;
    metadata?: any;
  } | null>(null);
  const [showPointsModal, setShowPointsModal] = useState(false);

  // Calculate discounts when customer or order total changes
  useEffect(() => {
    if (customerId && orderTotal > 0 && canViewLoyalty) {
      calculateDiscounts(customerId, orderTotal);
    } else {
      clearDiscounts();
    }
  }, [customerId, orderTotal, canViewLoyalty]);

  // Clear applied discount when customer changes
  useEffect(() => {
    if (appliedDiscount) {
      handleRemoveDiscount();
    }
  }, [customerId]);

  const handleApplyTierDiscount = useCallback(() => {
    if (!discountCalculation?.tier || appliedDiscount) return;

    const discountAmount = discountCalculation.tier.amount;
    if (discountAmount <= 0) return;

    setAppliedDiscount({
      type: "tier",
      amount: discountAmount,
      metadata: {
        tier: discountCalculation.tier.tierName,
        percentage: discountCalculation.tier.percentage,
      },
    });

    onDiscountApplied(discountAmount, "tier", {
      tier: discountCalculation.tier.tierName,
      percentage: discountCalculation.tier.percentage,
    });
  }, [discountCalculation, appliedDiscount, onDiscountApplied]);

  const handlePointsRedemption = useCallback(
    async (pointsToRedeem: number) => {
      if (!customerId || !discountCalculation?.points || appliedDiscount)
        return;

      try {
        const redemptionData: PointsRedemptionRequest = {
          pointsToRedeem,
        };

        const result = await redeemPoints(customerId, redemptionData);

        if (result) {
          setAppliedDiscount({
            type: "points",
            amount: result.discountAmount,
            metadata: {
              pointsRedeemed: result.pointsRedeemed,
              newBalance: result.newBalance,
              redemptionId: result.redemptionId,
            },
          });

          onDiscountApplied(result.discountAmount, "points", {
            pointsRedeemed: result.pointsRedeemed,
            newBalance: result.newBalance,
            redemptionId: result.redemptionId,
          });
        }
      } catch (error) {
        throw error; // Let the modal handle the error
      }
    },
    [
      customerId,
      discountCalculation,
      appliedDiscount,
      redeemPoints,
      onDiscountApplied,
    ]
  );

  const handleRemoveDiscount = useCallback(() => {
    setAppliedDiscount(null);
    setShowPointsModal(false);
    onDiscountRemoved();
  }, [onDiscountRemoved]);

  const styles = StyleSheet.create({
    container: {
      marginBottom: theme.spacing.md,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    headerIcon: {
      marginRight: theme.spacing.sm,
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.text,
    },
    loadingContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.md,
    },
    loadingText: {
      marginLeft: theme.spacing.sm,
      color: theme.colors.textSecondary,
    },
    discountOption: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    discountInfo: {
      flex: 1,
    },
    discountTitle: {
      fontSize: 14,
      fontWeight: "500",
      color: theme.colors.text,
      marginBottom: 2,
    },
    discountDescription: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    discountAmount: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.success,
    },
    applyButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      marginLeft: theme.spacing.sm,
    },
    applyButtonText: {
      color: theme.colors.surface,
      fontSize: 12,
      fontWeight: "600",
    },
    appliedDiscount: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: theme.spacing.md,
      backgroundColor: theme.colors.successBackground,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.success,
    },
    appliedDiscountInfo: {
      flex: 1,
    },
    appliedDiscountTitle: {
      fontSize: 14,
      fontWeight: "600",
      color: theme.colors.success,
      marginBottom: 2,
    },
    appliedDiscountDescription: {
      fontSize: 12,
      color: theme.colors.success,
    },
    removeButton: {
      padding: theme.spacing.sm,
    },
    noDiscountsText: {
      textAlign: "center",
      color: theme.colors.textSecondary,
      fontSize: 14,
      paddingVertical: theme.spacing.md,
    },
    unavailableText: {
      textAlign: "center",
      color: theme.colors.textSecondary,
      fontSize: 14,
      paddingVertical: theme.spacing.md,
      fontStyle: "italic",
    },
  });

  // Don't render if user can't view loyalty or no customer selected
  if (!canViewLoyalty || !customerId) {
    return null;
  }

  // Show loading state
  if (isCalculating) {
    return (
      <ModernCard style={styles.container} variant="elevated">
        <View style={styles.header}>
          <IconSymbol
            name="star.circle"
            size={20}
            color={theme.colors.primary}
            style={styles.headerIcon}
          />
          <Text style={styles.headerTitle}>Loyalty Discounts</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Calculating discounts...</Text>
        </View>
      </ModernCard>
    );
  }

  // Show applied discount
  if (appliedDiscount) {
    return (
      <ModernCard style={styles.container} variant="elevated">
        <View style={styles.header}>
          <IconSymbol
            name="star.circle.fill"
            size={20}
            color={theme.colors.success}
            style={styles.headerIcon}
          />
          <Text style={styles.headerTitle}>Loyalty Discount Applied</Text>
        </View>
        <View style={styles.appliedDiscount}>
          <View style={styles.appliedDiscountInfo}>
            <Text style={styles.appliedDiscountTitle}>
              {appliedDiscount.type === "tier"
                ? "Tier Discount"
                : "Points Redemption"}
            </Text>
            <Text style={styles.appliedDiscountDescription}>
              {appliedDiscount.type === "tier"
                ? `${appliedDiscount.metadata?.tier} tier - ${appliedDiscount.metadata?.percentage}%`
                : `${appliedDiscount.metadata?.pointsRedeemed} points redeemed`}
            </Text>
          </View>
          <View style={{ alignItems: "flex-end" }}>
            <Text style={styles.discountAmount}>
              -{formatCurrency(appliedDiscount.amount)}
            </Text>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={handleRemoveDiscount}
            >
              <IconSymbol
                name="xmark.circle"
                size={20}
                color={theme.colors.error}
              />
            </TouchableOpacity>
          </View>
        </View>
      </ModernCard>
    );
  }

  // Show available discounts
  if (!discountCalculation) {
    return (
      <ModernCard style={styles.container} variant="elevated">
        <View style={styles.header}>
          <IconSymbol
            name="star.circle"
            size={20}
            color={theme.colors.textSecondary}
            style={styles.headerIcon}
          />
          <Text style={styles.headerTitle}>Loyalty Discounts</Text>
        </View>
        <Text style={styles.unavailableText}>
          Loyalty discounts not available
        </Text>
      </ModernCard>
    );
  }

  const hasTierDiscount = discountCalculation.tier.amount > 0;
  const hasPointsDiscount = discountCalculation.points.availablePoints > 0;

  if (!hasTierDiscount && !hasPointsDiscount) {
    return (
      <ModernCard style={styles.container} variant="elevated">
        <View style={styles.header}>
          <IconSymbol
            name="star.circle"
            size={20}
            color={theme.colors.textSecondary}
            style={styles.headerIcon}
          />
          <Text style={styles.headerTitle}>Loyalty Discounts</Text>
        </View>
        <Text style={styles.noDiscountsText}>
          No loyalty discounts available for this order
        </Text>
      </ModernCard>
    );
  }

  return (
    <ModernCard style={styles.container} variant="elevated">
      <View style={styles.header}>
        <IconSymbol
          name="star.circle"
          size={20}
          color={theme.colors.primary}
          style={styles.headerIcon}
        />
        <Text style={styles.headerTitle}>Available Loyalty Discounts</Text>
      </View>

      {/* Tier-based discount */}
      {hasTierDiscount && (
        <View style={styles.discountOption}>
          <View style={styles.discountInfo}>
            <Text style={styles.discountTitle}>
              {discountCalculation.tier.tierName} Tier Discount
            </Text>
            <Text style={styles.discountDescription}>
              {discountCalculation.tier.percentage}% off your order
            </Text>
          </View>
          <View style={{ alignItems: "flex-end" }}>
            <Text style={styles.discountAmount}>
              -{formatCurrency(discountCalculation.tier.amount)}
            </Text>
            <TouchableOpacity
              style={styles.applyButton}
              onPress={handleApplyTierDiscount}
              disabled={disabled}
            >
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Points redemption */}
      {hasPointsDiscount && (
        <>
          <View style={styles.discountOption}>
            <View style={styles.discountInfo}>
              <Text style={styles.discountTitle}>Redeem Loyalty Points</Text>
              <Text style={styles.discountDescription}>
                {discountCalculation.points.availablePoints} points available
              </Text>
            </View>
            <TouchableOpacity
              style={styles.applyButton}
              onPress={() => setShowPointsModal(true)}
              disabled={disabled}
            >
              <Text style={styles.applyButtonText}>Redeem</Text>
            </TouchableOpacity>
          </View>
        </>
      )}

      {/* Points Redemption Modal */}
      {showPointsModal && discountCalculation && (
        <PointsRedemptionModal
          visible={showPointsModal}
          onClose={() => setShowPointsModal(false)}
          onRedeem={handlePointsRedemption}
          customerLoyalty={{
            loyaltyPoints: discountCalculation.points.availablePoints,
            tier: discountCalculation.tier.tierName,
          }}
          orderTotal={orderTotal}
          pointsToKshRate={1 / discountCalculation.points.exchangeRate}
          minRedemption={100}
          maxRedemption={discountCalculation.points.maxRedeemablePoints}
        />
      )}
    </ModernCard>
  );
};
