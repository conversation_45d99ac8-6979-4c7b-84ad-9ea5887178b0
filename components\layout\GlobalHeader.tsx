import { IconSymbol } from "@/components/ui/IconSymbol";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import { useAppSelector } from "@/src/store";
import {
  selectAllTickets,
  selectTicketStats,
} from "@/src/store/slices/ticketSlice";
import { usePathname, useRouter } from "expo-router";
import React, { useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { ProfileDropdown } from "./ProfileDropdown";

interface GlobalHeaderProps {
  title: string;
  onMenuPress: () => void;
  showBackButton?: boolean;
  onBackPress?: () => void;
  // Ticket-specific props
  showTicketConfig?: boolean;
  onRefreshTickets?: () => void;
  onFilterTickets?: (filter: string) => void;
}

export function GlobalHeader({
  title,
  onMenuPress,
  showBackButton = false,
  onBackPress,
  showTicketConfig = false,
  onRefreshTickets,
  onFilterTickets,
}: GlobalHeaderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useSession();
  const { currentTitle, canGoBack } = useNavigation();
  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");
  const responsiveLayout = useResponsiveLayout();
  const navigationHeight = responsiveLayout?.navigationHeight || 60;
  const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
  const isDesktop = responsiveLayout?.isDesktop || false;

  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [showTicketActions, setShowTicketActions] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState("all");

  // Get ticket data for ticket-specific features
  const tickets = useAppSelector(selectAllTickets);
  const ticketStats = useAppSelector(selectTicketStats);

  // Auto-detect if we're on tickets page
  const isTicketsPage =
    pathname?.includes("ticket-management") || showTicketConfig;

  // Enhanced button type logic using navigation context
  const getButtonType = () => {
    // Explicit override from props takes precedence
    if (showBackButton) return "back";

    // Use navigation context to determine if we can go back
    if (canGoBack) return "back";

    // Default to menu for main screens
    return "menu";
  };

  // Use the title from props if provided, otherwise use navigation context title
  const displayTitle = title || currentTitle;

  const buttonType = getButtonType();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else if (router.canGoBack()) {
      router.back();
    } else {
      // Fallback to dashboard if no back history
      router.replace("/(tabs)");
    }
  };

  const handleProfilePress = () => {
    setShowProfileDropdown(true);
  };

  const handleFilterChange = (filter: string) => {
    setSelectedFilter(filter);
    onFilterTickets?.(filter);
  };

  const activeTickets = tickets.filter((t) => t.status === "active");
  const pausedTickets = tickets.filter((t) => t.status === "paused");
  const completedTickets = tickets.filter((t) => t.status === "completed");

  return (
    <View style={{ backgroundColor }}>
      <View
        style={[
          styles.header,
          {
            backgroundColor,
            borderBottomColor: borderColor,
            height: navigationHeight,
            paddingHorizontal: Spacing.md * spacingMultiplier,
          },
        ]}
      >
        <View style={styles.leftSection}>
          {buttonType === "back" ? (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleBackPress}
              activeOpacity={0.7}
            >
              <IconSymbol name="chevron.left" size={24} color={textColor} />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onMenuPress}
              activeOpacity={0.7}
            >
              <IconSymbol
                name="line.3.horizontal"
                size={24}
                color={textColor}
              />
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.centerSection}>
          <Text style={[styles.title, { color: textColor }]} numberOfLines={2}>
            {displayTitle}
          </Text>

          {/* Ticket Indicator - Show on non-tickets pages
          {!isTicketsPage && (
            <TicketIndicator
              compact={true}
              onPress={() => router.push("/ticket-management")}
              onSwitchTicket={() => router.push("/ticket-management")}
            />
          )} */}
        </View>

        <View style={styles.rightSection}>
          {/* Ticket Actions for Tickets Page */}
          {isTicketsPage ? (
            <View style={styles.ticketActionsContainer}>
              <TouchableOpacity
                style={styles.ticketActionButton}
                onPress={onRefreshTickets}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name="arrow.clockwise"
                  size={18}
                  color={textColor}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.ticketActionButton}
                onPress={() => setShowTicketActions(!showTicketActions)}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name="slider.horizontal.3"
                  size={18}
                  color={textColor}
                />
              </TouchableOpacity>

              {/* Manual Ticket Creation - Temporarily Hidden */}
              {/* TODO: Implement advanced ticket creation features later */}
            </View>
          ) : (
            <TouchableOpacity
              style={styles.profileButton}
              onPress={handleProfilePress}
              activeOpacity={0.7}
            >
              <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
                <Text style={styles.avatarText}>
                  {user?.name?.charAt(0).toUpperCase() || "U"}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        </View>

        {!isTicketsPage && (
          <ProfileDropdown
            visible={showProfileDropdown}
            onClose={() => setShowProfileDropdown(false)}
          />
        )}
      </View>

      {/* Ticket Stats Bar for Tickets Page */}
      {isTicketsPage && (
        <View
          style={[
            styles.ticketStatsBar,
            { backgroundColor, borderBottomColor: borderColor },
          ]}
        >
          <TouchableOpacity
            style={[
              styles.statItem,
              selectedFilter === "all" && {
                backgroundColor: primaryColor + "20",
              },
            ]}
            onPress={() => handleFilterChange("all")}
          >
            <Text style={[styles.statNumber, { color: primaryColor }]}>
              {tickets.length}
            </Text>
            <Text style={[styles.statLabel, { color: textColor }]}>All</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.statItem,
              selectedFilter === "active" && {
                backgroundColor: primaryColor + "20",
              },
            ]}
            onPress={() => handleFilterChange("active")}
          >
            <Text style={[styles.statNumber, { color: primaryColor }]}>
              {activeTickets.length}
            </Text>
            <Text style={[styles.statLabel, { color: textColor }]}>Active</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.statItem,
              selectedFilter === "paused" && {
                backgroundColor: "#FFA500" + "20",
              },
            ]}
            onPress={() => handleFilterChange("paused")}
          >
            <Text style={[styles.statNumber, { color: "#FFA500" }]}>
              {pausedTickets.length}
            </Text>
            <Text style={[styles.statLabel, { color: textColor }]}>Paused</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.statItem,
              selectedFilter === "completed" && {
                backgroundColor: "#4CAF50" + "20",
              },
            ]}
            onPress={() => handleFilterChange("completed")}
          >
            <Text style={[styles.statNumber, { color: "#4CAF50" }]}>
              {completedTickets.length}
            </Text>
            <Text style={[styles.statLabel, { color: textColor }]}>Done</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.statItem,
              selectedFilter === "dirty" && {
                backgroundColor: "#F44336" + "20",
              },
            ]}
            onPress={() => handleFilterChange("dirty")}
          >
            <Text style={[styles.statNumber, { color: "#F44336" }]}>
              {ticketStats.dirty || 0}
            </Text>
            <Text style={[styles.statLabel, { color: textColor }]}>
              Unsaved
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Quick Actions Dropdown for Tickets */}
      {isTicketsPage && showTicketActions && (
        <View
          style={[
            styles.quickActions,
            { backgroundColor, borderBottomColor: borderColor },
          ]}
        >
          <TouchableOpacity
            style={styles.quickActionItem}
            onPress={() => {
              handleFilterChange("all");
              setShowTicketActions(false);
            }}
          >
            <IconSymbol name="list.bullet" size={16} color={textColor} />
            <Text style={[styles.quickActionText, { color: textColor }]}>
              Show All
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionItem}
            onPress={() => {
              onRefreshTickets?.();
              setShowTicketActions(false);
            }}
          >
            <IconSymbol name="arrow.clockwise" size={16} color={textColor} />
            <Text style={[styles.quickActionText, { color: textColor }]}>
              Refresh
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionItem}
            onPress={() => {
              // TODO: Export tickets
              setShowTicketActions(false);
            }}
          >
            <IconSymbol
              name="square.and.arrow.up"
              size={16}
              color={textColor}
            />
            <Text style={[styles.quickActionText, { color: textColor }]}>
              Export
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    minHeight: 80, // Fixed height to accommodate 2 lines + padding
    height: 80, // Fixed height to accommodate 2 lines + padding
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: 8,
    borderBottomWidth: 1,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  leftSection: {
    alignItems: "flex-start",
  },

  centerSection: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 8,
    maxHeight: 64, // Constrain height to prevent overflow
  },
  rightSection: {
    alignItems: "flex-end",
    justifyContent: "center",
    paddingRight: 4,
  },
  actionButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
  },
  dashboardButton: {
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    marginLeft: 4,
  },
  title: {
    ...Typography.h3,
    textAlign: "center",
    lineHeight: 20, // Fixed line height for consistency
    maxHeight: 40, // Constrain to 2 lines max
    overflow: "hidden",
  },
  profileButton: {
    alignItems: "center",
    justifyContent: "center",
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
  },
  avatarText: {
    ...Typography.caption,
    color: "#FFFFFF",
    fontWeight: "600",
  },
  // Ticket-specific styles
  ticketStatsBar: {
    flexDirection: "row",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
    paddingVertical: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    borderRadius: 8,
    minWidth: 50,
  },
  statNumber: {
    ...Typography.h3,
    fontWeight: "700",
    marginBottom: 2,
  },
  statLabel: {
    ...Typography.caption,
    fontSize: 10,
    textAlign: "center",
  },
  quickActions: {
    flexDirection: "row",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    justifyContent: "space-around",
  },
  quickActionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    gap: 6,
  },
  quickActionText: {
    ...Typography.caption,
    fontSize: 12,
  },
  // Ticket action buttons
  ticketActionsContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    paddingRight: 4,
  },
  ticketActionButton: {
    width: 38,
    height: 38,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 19,
    backgroundColor: "rgba(0, 0, 0, 0.06)",
  },
  newTicketButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
});
