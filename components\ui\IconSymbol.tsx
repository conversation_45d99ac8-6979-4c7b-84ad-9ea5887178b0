// Fallback for using MaterialIcons on Android and web.

import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { SymbolViewProps, SymbolWeight } from "expo-symbols";
import { ComponentProps } from "react";
import { OpaqueColorValue, type StyleProp, type TextStyle } from "react-native";

type IconMapping = Partial<
  Record<SymbolViewProps["name"], ComponentProps<typeof MaterialIcons>["name"]>
>;
type IconSymbolName = keyof typeof MAPPING;

/**
 * Add your SF Symbols to Material Icons mappings here.
 * - see Material Icons in the [Icons Directory](https://icons.expo.fyi).
 * - see SF Symbols in the [SF Symbols](https://developer.apple.com/sf-symbols/) app.
 */
const MAPPING = {
  // Navigation & UI
  "house.fill": "home",
  "paperplane.fill": "send",
  "ticket.fill": "receipt",
  "chevron.left.forwardslash.chevron.right": "code",
  "chevron.right": "chevron-right",
  "chevron.left": "chevron-left",
  "line.3.horizontal": "menu",
  "plus.circle": "add-circle",
  plus: "add",
  xmark: "close",

  // Commerce & Shopping
  "bag.fill": "shopping-bag",
  "cart.fill": "shopping-cart",
  "list.bullet": "list",
  banknote: "attach-money",
  creditcard: "credit-card",

  // Search & Actions
  magnifyingglass: "search",
  "arrow.clockwise": "refresh",
  minus: "remove",
  trash: "delete",
  "barcode.viewfinder": "qr-code-scanner",
  barcode: "qr-code",

  // People & Users
  "person.badge.plus": "person-add",
  "person.badge.plus.fill": "person-add",
  "person.fill": "person",
  "person.2": "people",
  "person.2.fill": "group",
  "person.3.fill": "group",

  // Communication
  phone: "phone",
  envelope: "email",

  // Status & Feedback
  "checkmark.circle": "check-circle",
  "xmark.circle": "cancel",
  "exclamationmark.triangle": "warning",
  "exclamationmark.circle": "error",
  "questionmark.circle": "help",

  // Settings & Tools
  gear: "settings",
  "gear.fill": "settings",
  wrench: "build",
  "printer.fill": "print",
  bluetooth: "bluetooth",
  power: "power",
  radio: "radio",
  "shield-checkmark": "verified-user",
  refresh: "refresh",

  // Charts & Analytics
  "chart.bar.fill": "bar-chart",

  // Location & Navigation
  location: "location-on",
  map: "map",

  // Documents & Files
  "doc.text": "description",
  "doc.text.fill": "description",
  folder: "folder",
  tray: "inbox",

  // Time & Calendar
  clock: "access-time",
  calendar: "event",

  // Media & Content
  photo: "photo",
  camera: "camera-alt",

  // Network & Connectivity
  wifi: "wifi",
  "wifi.slash": "wifi-off",
  "antenna.radiowaves.left.and.right": "signal-cellular-4-bar",

  // Additional icons for sidebar
  "cube.fill": "inventory",
  "arrow.right.square": "logout",
  "checkmark.circle.fill": "check-circle",
  printer: "print",

  // Theme switching icons
  "sun.max.fill": "wb-sunny",
  "moon.fill": "brightness-2",
  "sun.max": "wb-sunny",
  moon: "brightness-2",
  "lightbulb.fill": "lightbulb-outline",
  lightbulb: "lightbulb-outline",
  "circle.lefthalf.filled": "brightness-6",
  "paintbrush.fill": "palette",

  // Ticket system icons
  "arrow.right": "arrow-forward",
  "arrow.triangle.2.circlepath": "sync",
  "slider.horizontal.3": "tune",
  "square.and.arrow.up": "share",

  // Additional status icons
  "exclamationmark.triangle.fill": "warning",
  "xmark.circle.fill": "error",

  // Store and business icons
  "storefront.fill": "store",
  "phone.fill": "phone",
  "star.fill": "star",
  "shippingbox.fill": "inventory-2",
  "crown.fill": "workspace-premium",
  "clock.fill": "schedule",
} as IconMapping;

/**
 * An icon component that uses native SF Symbols on iOS, and Material Icons on Android and web.
 * This ensures a consistent look across platforms, and optimal resource usage.
 * Icon `name`s are based on SF Symbols and require manual mapping to Material Icons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  return (
    <MaterialIcons
      color={color}
      size={size}
      name={MAPPING[name]}
      style={style}
    />
  );
}
