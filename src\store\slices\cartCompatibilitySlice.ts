/**
 * Cart Compatibility Slice
 * 
 * Provides backward compatibility layer between the old cart system
 * and the new ticket system. This allows existing components to work
 * seamlessly while gradually migrating to the ticket system.
 */

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  Cart,
  CartItem,
  CartItemDiscount,
  Customer,
  OrderDiscount,
  Salesperson,
} from "../../types/shopify";

interface CartCompatibilityState {
  isTicketModeEnabled: boolean;
  legacyCart: Cart & {
    isLoading: boolean;
    error: string | null;
    selectedSalesperson: Salesperson | null;
  };
}

const initialLegacyCart = {
  items: [],
  subtotal: 0,
  tax: 0,
  total: 0,
  discounts: [],
  customer: undefined,
  note: undefined,
  isLoading: false,
  error: null,
  selectedSalesperson: null,
};

const initialState: CartCompatibilityState = {
  isTicketModeEnabled: false,
  legacyCart: initialLegacyCart,
};

// Helper function to calculate cart totals (same as original cart logic)
const calculateCartTotals = (cart: Cart): Cart => {
  // Calculate subtotal with item-level discounts
  let subtotalBeforeOrderDiscounts = 0;
  
  cart.items.forEach((item) => {
    const lineTotal = parseFloat(item.price) * item.quantity;
    let itemDiscountAmount = 0;
    
    // Apply item-level discount
    if (item.discount && item.discount.amount > 0) {
      if (item.discount.type === "percentage") {
        itemDiscountAmount = (lineTotal * item.discount.amount) / 100;
      } else {
        itemDiscountAmount = Math.min(item.discount.amount, lineTotal);
      }
    }
    
    subtotalBeforeOrderDiscounts += Math.max(0, lineTotal - itemDiscountAmount);
  });

  const subtotal = subtotalBeforeOrderDiscounts;

  // Apply order-level discounts
  let orderDiscountAmount = 0;
  cart.discounts.forEach((discount) => {
    if (discount.type === "percentage") {
      orderDiscountAmount += subtotal * (parseFloat(discount.amount) / 100);
    } else {
      orderDiscountAmount += parseFloat(discount.amount);
    }
  });

  const finalTotal = Math.max(0, subtotal - orderDiscountAmount);

  return {
    ...cart,
    subtotal: Math.round(subtotal * 100) / 100,
    tax: 0, // Always 0 for this POS system
    total: Math.round(finalTotal * 100) / 100,
  };
};

const cartCompatibilitySlice = createSlice({
  name: "cartCompatibility",
  initialState,
  reducers: {
    // Mode switching
    enableTicketMode: (state) => {
      state.isTicketModeEnabled = true;
    },

    disableTicketMode: (state) => {
      state.isTicketModeEnabled = false;
    },

    toggleTicketMode: (state) => {
      state.isTicketModeEnabled = !state.isTicketModeEnabled;
    },

    // Legacy cart actions (for backward compatibility when ticket mode is disabled)
    addToLegacyCart: (state, action: PayloadAction<CartItem>) => {
      if (state.isTicketModeEnabled) return; // Ignore if ticket mode is enabled

      const existingItem = state.legacyCart.items.find(
        (item) => item.variantId === action.payload.variantId
      );

      if (existingItem) {
        // Check inventory before adding
        if (
          existingItem.quantity + action.payload.quantity <=
          existingItem.inventoryQuantity
        ) {
          existingItem.quantity += action.payload.quantity;
        } else {
          state.legacyCart.error = "Insufficient inventory";
          return;
        }
      } else {
        // Check inventory for new item
        if (action.payload.quantity <= action.payload.inventoryQuantity) {
          state.legacyCart.items.push(action.payload);
        } else {
          state.legacyCart.error = "Insufficient inventory";
          return;
        }
      }

      // Recalculate totals
      const updatedCart = calculateCartTotals(state.legacyCart);
      Object.assign(state.legacyCart, updatedCart);
      state.legacyCart.error = null;
    },

    removeFromLegacyCart: (state, action: PayloadAction<string>) => {
      if (state.isTicketModeEnabled) return;

      state.legacyCart.items = state.legacyCart.items.filter(
        (item) => item.variantId !== action.payload
      );

      const updatedCart = calculateCartTotals(state.legacyCart);
      Object.assign(state.legacyCart, updatedCart);
    },

    updateLegacyCartQuantity: (
      state,
      action: PayloadAction<{ variantId: string; quantity: number }>
    ) => {
      if (state.isTicketModeEnabled) return;

      const { variantId, quantity } = action.payload;
      const item = state.legacyCart.items.find((item) => item.variantId === variantId);

      if (item) {
        if (quantity <= 0) {
          state.legacyCart.items = state.legacyCart.items.filter(
            (item) => item.variantId !== variantId
          );
        } else if (quantity <= item.inventoryQuantity) {
          item.quantity = quantity;
        } else {
          state.legacyCart.error = "Insufficient inventory";
          return;
        }

        const updatedCart = calculateCartTotals(state.legacyCart);
        Object.assign(state.legacyCart, updatedCart);
        state.legacyCart.error = null;
      }
    },

    applyLegacyCartItemDiscount: (
      state,
      action: PayloadAction<{
        variantId: string;
        discount: CartItemDiscount | null;
      }>
    ) => {
      if (state.isTicketModeEnabled) return;

      const { variantId, discount } = action.payload;
      const item = state.legacyCart.items.find((item) => item.variantId === variantId);

      if (item) {
        item.discount = discount || undefined;
        
        const updatedCart = calculateCartTotals(state.legacyCart);
        Object.assign(state.legacyCart, updatedCart);
      }
    },

    updateLegacyCartItemNotes: (
      state,
      action: PayloadAction<{ variantId: string; notes: string }>
    ) => {
      if (state.isTicketModeEnabled) return;

      const { variantId, notes } = action.payload;
      const item = state.legacyCart.items.find((item) => item.variantId === variantId);

      if (item) {
        item.notes = notes.trim() || undefined;
      }
    },

    clearLegacyCart: (state) => {
      if (state.isTicketModeEnabled) return;

      state.legacyCart = { ...initialLegacyCart };
    },

    setLegacyCartCustomer: (state, action: PayloadAction<Customer | undefined>) => {
      if (state.isTicketModeEnabled) return;

      state.legacyCart.customer = action.payload;
    },

    setLegacyCartSalesperson: (state, action: PayloadAction<Salesperson | null>) => {
      if (state.isTicketModeEnabled) return;

      state.legacyCart.selectedSalesperson = action.payload;
    },

    addLegacyCartDiscount: (state, action: PayloadAction<OrderDiscount>) => {
      if (state.isTicketModeEnabled) return;

      // Remove existing discount with same code if any
      if (action.payload.code) {
        state.legacyCart.discounts = state.legacyCart.discounts.filter(
          (discount) => discount.code !== action.payload.code
        );
      }

      state.legacyCart.discounts.push(action.payload);
      
      const updatedCart = calculateCartTotals(state.legacyCart);
      Object.assign(state.legacyCart, updatedCart);
    },

    removeLegacyCartDiscount: (state, action: PayloadAction<string>) => {
      if (state.isTicketModeEnabled) return;

      state.legacyCart.discounts = state.legacyCart.discounts.filter(
        (discount) => discount.id !== action.payload
      );
      
      const updatedCart = calculateCartTotals(state.legacyCart);
      Object.assign(state.legacyCart, updatedCart);
    },

    setLegacyCartNote: (state, action: PayloadAction<string>) => {
      if (state.isTicketModeEnabled) return;

      state.legacyCart.note = action.payload;
    },

    updateLegacyCartInventoryQuantity: (
      state,
      action: PayloadAction<{ variantId: string; quantity: number }>
    ) => {
      if (state.isTicketModeEnabled) return;

      const { variantId, quantity } = action.payload;
      const item = state.legacyCart.items.find((item) => item.variantId === variantId);

      if (item) {
        item.inventoryQuantity = quantity;

        // If cart quantity exceeds available inventory, adjust it
        if (item.quantity > quantity) {
          if (quantity <= 0) {
            state.legacyCart.items = state.legacyCart.items.filter(
              (item) => item.variantId !== variantId
            );
          } else {
            item.quantity = quantity;
          }
          
          const updatedCart = calculateCartTotals(state.legacyCart);
          Object.assign(state.legacyCart, updatedCart);
        }
      }
    },

    setLegacyCartLoading: (state, action: PayloadAction<boolean>) => {
      state.legacyCart.isLoading = action.payload;
    },

    setLegacyCartError: (state, action: PayloadAction<string | null>) => {
      state.legacyCart.error = action.payload;
    },

    clearLegacyCartError: (state) => {
      state.legacyCart.error = null;
    },
  },
});

export const {
  enableTicketMode,
  disableTicketMode,
  toggleTicketMode,
  addToLegacyCart,
  removeFromLegacyCart,
  updateLegacyCartQuantity,
  applyLegacyCartItemDiscount,
  updateLegacyCartItemNotes,
  clearLegacyCart,
  setLegacyCartCustomer,
  setLegacyCartSalesperson,
  addLegacyCartDiscount,
  removeLegacyCartDiscount,
  setLegacyCartNote,
  updateLegacyCartInventoryQuantity,
  setLegacyCartLoading,
  setLegacyCartError,
  clearLegacyCartError,
} = cartCompatibilitySlice.actions;

export default cartCompatibilitySlice.reducer;

// Selectors
export const selectIsTicketModeEnabled = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.isTicketModeEnabled;

export const selectLegacyCart = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart;

export const selectLegacyCartItems = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.items;

export const selectLegacyCartTotal = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.total;

export const selectLegacyCartSubtotal = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.subtotal;

export const selectLegacyCartItemCount = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.items.reduce((total, item) => total + item.quantity, 0);

export const selectLegacyCartCustomer = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.customer;

export const selectLegacyCartSalesperson = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.selectedSalesperson;

export const selectLegacyCartDiscounts = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.discounts;

export const selectLegacyCartNote = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.note;

export const selectLegacyCartError = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.error;

export const selectLegacyCartLoading = (state: { cartCompatibility: CartCompatibilityState }) =>
  state.cartCompatibility.legacyCart.isLoading;
