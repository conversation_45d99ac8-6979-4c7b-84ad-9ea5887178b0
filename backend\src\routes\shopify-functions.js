/**
 * Shopify Functions Management API Routes
 * Handles configuration and management of Shopify Functions for discounts and loyalty
 */

const express = require("express");
const router = express.Router();
const shopifyFunctionsConfigService = require("../services/shopify-functions-config-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");

// Get commission function configuration
router.get("/commission/config", authenticateToken, async (req, res) => {
  try {
    const result = await shopifyFunctionsConfigService.getCommissionFunctionConfig();

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.configuration,
        "Commission function configuration retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Get commission function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get commission function configuration"
    );
  }
});

// Update commission function configuration (requires admin permission)
router.put("/commission/config", authenticateToken, requirePermission("manage_shopify_functions"), async (req, res) => {
  try {
    const newConfig = req.body;

    const result = await shopifyFunctionsConfigService.updateCommissionFunctionConfig(newConfig);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.configuration,
        result.message
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Update commission function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to update commission function configuration"
    );
  }
});

// Get loyalty function configuration
router.get("/loyalty/config", authenticateToken, async (req, res) => {
  try {
    const result = await shopifyFunctionsConfigService.getLoyaltyFunctionConfig();

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.configuration,
        "Loyalty function configuration retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Get loyalty function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get loyalty function configuration"
    );
  }
});

// Update loyalty function configuration (requires admin permission)
router.put("/loyalty/config", authenticateToken, requirePermission("manage_shopify_functions"), async (req, res) => {
  try {
    const newConfig = req.body;

    const result = await shopifyFunctionsConfigService.updateLoyaltyFunctionConfig(newConfig);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.configuration,
        result.message
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Update loyalty function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to update loyalty function configuration"
    );
  }
});

// Get all function configurations
router.get("/configs", authenticateToken, async (req, res) => {
  try {
    const result = await shopifyFunctionsConfigService.getAllConfigurations();

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.configurations,
        "All function configurations retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Get all function configs error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get function configurations"
    );
  }
});

// Reset commission function configuration to defaults (requires admin permission)
router.post("/commission/config/reset", authenticateToken, requirePermission("manage_shopify_functions"), async (req, res) => {
  try {
    const defaultConfig = shopifyFunctionsConfigService.defaultCommissionConfig;
    const result = await shopifyFunctionsConfigService.updateCommissionFunctionConfig(defaultConfig);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.configuration,
        "Commission function configuration reset to defaults"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Reset commission function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to reset commission function configuration"
    );
  }
});

// Reset loyalty function configuration to defaults (requires admin permission)
router.post("/loyalty/config/reset", authenticateToken, requirePermission("manage_shopify_functions"), async (req, res) => {
  try {
    const defaultConfig = shopifyFunctionsConfigService.defaultLoyaltyConfig;
    const result = await shopifyFunctionsConfigService.updateLoyaltyFunctionConfig(defaultConfig);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.configuration,
        "Loyalty function configuration reset to defaults"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Reset loyalty function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to reset loyalty function configuration"
    );
  }
});

// Validate commission function configuration
router.post("/commission/config/validate", authenticateToken, async (req, res) => {
  try {
    const config = req.body;
    const validation = shopifyFunctionsConfigService.validateCommissionConfig(config);

    return ResponseFormatter.success(
      res,
      {
        isValid: validation.isValid,
        errors: validation.errors,
        config: config
      },
      validation.isValid ? "Configuration is valid" : "Configuration validation failed"
    );
  } catch (error) {
    console.error("Validate commission function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to validate commission function configuration"
    );
  }
});

// Validate loyalty function configuration
router.post("/loyalty/config/validate", authenticateToken, async (req, res) => {
  try {
    const config = req.body;
    const validation = shopifyFunctionsConfigService.validateLoyaltyConfig(config);

    return ResponseFormatter.success(
      res,
      {
        isValid: validation.isValid,
        errors: validation.errors,
        config: config
      },
      validation.isValid ? "Configuration is valid" : "Configuration validation failed"
    );
  } catch (error) {
    console.error("Validate loyalty function config error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to validate loyalty function configuration"
    );
  }
});

// Get function deployment status and health
router.get("/status", authenticateToken, async (req, res) => {
  try {
    // This would typically check the deployment status of Shopify Functions
    // For now, we'll return a basic status based on configuration availability
    const configResult = await shopifyFunctionsConfigService.getAllConfigurations();

    const status = {
      commission_function: {
        deployed: true, // Would check actual deployment status
        configured: configResult.success && configResult.configurations.commission,
        last_updated: new Date().toISOString(),
        version: "1.0.0"
      },
      loyalty_function: {
        deployed: true, // Would check actual deployment status
        configured: configResult.success && configResult.configurations.loyalty,
        last_updated: new Date().toISOString(),
        version: "1.0.0"
      },
      overall_health: "healthy"
    };

    return ResponseFormatter.success(
      res,
      status,
      "Shopify Functions status retrieved successfully"
    );
  } catch (error) {
    console.error("Get function status error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get Shopify Functions status"
    );
  }
});

// Health check for Shopify Functions service
router.get("/health", async (req, res) => {
  try {
    return ResponseFormatter.success(
      res,
      {
        status: "healthy",
        service: "shopify-functions",
        timestamp: new Date().toISOString(),
        functions: {
          commission: "operational",
          loyalty: "operational"
        }
      },
      "Shopify Functions service health check completed"
    );
  } catch (error) {
    console.error("Shopify Functions health check error:", error);
    return ResponseFormatter.error(
      res,
      "Shopify Functions service health check failed",
      503
    );
  }
});

module.exports = router;
