@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-serif: var(--font-noto-serif-jp);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Base variables */
  --radius: 0.5rem;
  --background: #ebe9e9; /* platinum */
  --background-rgb: 235, 233, 233;
  --foreground: #1c1a17; /* eerie-black */
  --card: #ffffff;
  --card-foreground: #1c1a17; /* eerie-black */
  --popover: #ffffff;
  --popover-foreground: #1c1a17; /* eerie-black */
  --primary: #d2686f; /* Pink */
  --primary-foreground: #ffffff;
  --secondary: #9c9192; /* taupe-gray */
  --secondary-foreground: #ffffff;
  --muted: #f5f5f5;
  --muted-foreground: #9c9192; /* taupe-gray */
  --accent: #f9e8e9; /* Light pink */
  --accent-foreground: #1c1a17; /* eerie-black */
  --destructive: oklch(0.577 0.245 27.325);
  --border: #9c9192; /* taupe-gray */
  --input: #ebe9e9; /* platinum */
  --ring: #d2686f; /* Pink */
  --chart-1: oklch(0.646 0.222 41.116);

  /* Font size variables - increased for better readability */
  --font-size-xs: 0.8125rem; /* 13px */
  --font-size-sm: 0.9375rem; /* 15px */
  --font-size-base: 1.0625rem; /* 17px */
  --font-size-lg: 1.25rem; /* 20px */
  --font-size-xl: 1.5rem; /* 24px */
  --font-size-2xl: 1.875rem; /* 30px */
  --font-size-3xl: 2.25rem; /* 36px */
  --font-size-4xl: 3rem; /* 48px */

  /* Line height variables */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: #000000; /* black */
  --background-rgb: 0, 0, 0;
  --foreground: #ebe9e9; /* platinum */
  --card: #1c1a17; /* eerie-black */
  --card-foreground: #ebe9e9; /* platinum */
  --popover: #1c1a17; /* eerie-black */
  --popover-foreground: #ebe9e9; /* platinum */
  --primary: #e87c83; /* Lighter pink for dark mode */
  --primary-foreground: #000000; /* black */
  --secondary: #9c9192; /* taupe-gray */
  --secondary-foreground: #ebe9e9; /* platinum */
  --muted: #1c1a17; /* eerie-black */
  --muted-foreground: #9c9192; /* taupe-gray */
  --accent: #4a2c2e; /* Dark pink accent that complements the primary color */
  --accent-foreground: #ebe9e9; /* platinum */
  --destructive: oklch(0.704 0.191 22.216);
  --border: #1c1a17; /* eerie-black */
  --input: rgba(235, 233, 233, 0.15); /* platinum with transparency */
  --ring: #e87c83; /* Lighter pink for dark mode */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 2rem; /* Add padding for fixed headers */
  }

  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: var(--font-poppins), system-ui, sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  /* Enhanced scroll snapping */
  .snap-container {
    scroll-snap-type: y mandatory;
    overflow-y: auto;
    height: 100vh;
  }

  .snap-section {
    scroll-snap-align: start;
    scroll-snap-stop: always;
    min-height: 100vh;
  }

  /* Suppress hydration warnings */
  .suppress-hydration-warning * {
    -webkit-suppress-hydration-warning: true;
    suppress-hydration-warning: true;
  }
  pre,
  code {
    font-family: monospace;
  }

  /* Typography scale */
  h1,
  .h1 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
    font-weight: 700;
    font-family: var(--font-noto-serif-jp), serif;
  }

  h2,
  .h2 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-tight);
    font-weight: 700;
    font-family: var(--font-noto-serif-jp), serif;
  }

  h3,
  .h3 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-tight);
    font-weight: 600;
    font-family: var(--font-noto-serif-jp), serif;
  }

  h4,
  .h4 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-tight);
    font-weight: 600;
    font-family: var(--font-noto-serif-jp), serif;
  }

  h5,
  .h5 {
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-weight: 600;
  }

  h6,
  .h6 {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    font-weight: 600;
  }

  p {
    margin-bottom: 1rem;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    body {
      font-size: var(--font-size-sm);
    }

    h1,
    .h1 {
      font-size: var(--font-size-2xl);
    }

    h2,
    .h2 {
      font-size: var(--font-size-xl);
    }

    h3,
    .h3 {
      font-size: var(--font-size-lg);
    }
  }
}

/* Custom animations - Slowed down for a more serene experience */
.hover-scale {
  transition: transform 0.6s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.03);
}

.hover-lift {
  transition: transform 0.6s ease-in-out, box-shadow 0.6s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* Scroll animations - Slowed down for a more serene experience */
.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 1.2s ease-in-out, transform 1.2s ease-in-out;
}

.fade-in-up.in-view {
  opacity: 1;
  transform: translateY(0);
}

.fade-in {
  opacity: 0;
  transition: opacity 1.2s ease-in-out;
}

.fade-in.in-view {
  opacity: 1;
}

.scale-in {
  opacity: 0;
  transform: scale(0.97);
  transition: opacity 1.2s ease-in-out, transform 1.2s ease-in-out;
}

.scale-in.in-view {
  opacity: 1;
  transform: scale(1);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-20px);
  transition: opacity 1.2s ease-in-out, transform 1.2s ease-in-out;
}

.slide-in-left.in-view {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 1.2s ease-in-out, transform 1.2s ease-in-out;
}

.slide-in-right.in-view {
  opacity: 1;
  transform: translateX(0);
}

/* Subtle box shadows */
.shadow-subtle {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.dark .shadow-subtle {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Hero background fallback */
.hero-bg-fallback {
  background: linear-gradient(
    to right,
    #d2686f,
    #9c9192
  ); /* primary to taupe-gray */
}

.dark .hero-bg-fallback {
  background: linear-gradient(
    to right,
    #e87c83,
    #1c1a17
  ); /* primary to eerie-black */
}

/* Product card hover effect - Slowed down for a more serene experience */
.product-card {
  transition: transform 0.8s ease-in-out, box-shadow 0.8s ease-in-out;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.08),
    0 8px 10px -6px rgba(0, 0, 0, 0.08);
}

/* Custom button heights */
.btn-sm {
  height: 32px;
}

.btn-md {
  height: 36px;
}

.btn-lg {
  height: 40px;
}

/* Text shadow for better readability on varied backgrounds */
.text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.noto-serif-text {
  font-family: var(--font-noto-serif-jp), serif;
}

.dark .text-shadow {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Hide default navigation when using tabbed theme */
.hide-default-nav header:first-of-type {
  display: none;
}

/* Font size utility classes */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

/* Font family utility classes */
.font-serif {
  font-family: var(--font-serif), serif;
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Radial gradient for vignette effect */
.bg-radial-gradient {
  background: radial-gradient(
    circle at center,
    transparent 0%,
    rgba(0, 0, 0, 0) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
}

/* True grain texture for backgrounds */
.bg-grain {
  position: relative;
}

.bg-grain::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: url("data:image/png;base64,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");
  opacity: 0.03;
  pointer-events: none;
  z-index: 1;
}

/* Alternating section backgrounds */
.section-bg-primary {
  background-color: #ebe9e9; /* platinum */
}

.section-bg-secondary {
  background-color: #e5e5e5; /* even lighter gray, even less contrast with platinum */
}

.dark .section-bg-primary {
  background-color: #000000; /* black */
}

.dark .section-bg-secondary {
  background-color: #050505; /* extremely dark gray, minimal contrast with black */
}

/* 3D perspective effects for cinematic theme */
.perspective-1000 {
  perspective: 1000px;
}

.rotate-y-10 {
  transform: rotateY(10deg);
}

.rotate-y-0 {
  transform: rotateY(0deg);
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Text outline styles for different screen sizes */
.text-outline {
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.8);
}

@media (min-width: 1024px) {
  .text-outline {
    -webkit-text-stroke: 1.5px rgba(255, 255, 255, 0.8);
  }
}

@media (min-width: 1280px) {
  .text-outline {
    -webkit-text-stroke: 2px rgba(255, 255, 255, 0.8);
  }
}

/* Responsive container for better mobile experience */
.responsive-container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
}

/* Navigation Cards Responsive Classes */
.nav-card-container {
  padding-left: 1rem;
  padding-right: 1rem;
}

.nav-card-item {
  width: 100%;
}

@media (min-width: 640px) {
  .nav-card-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .nav-card-item {
    width: 80%;
  }
}

@media (min-width: 768px) {
  .nav-card-container {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .nav-card-item {
    width: 50%;
  }
}

@media (min-width: 1024px) {
  .nav-card-container {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .nav-card-item {
    width: 33.333%;
  }
}

/* Fix for mobile tabs spacing issue - only applies to mobile */
@media screen and (max-width: 640px) {
  /* Target only product details page tabs */
  .mobile-tabs-fix[data-orientation="horizontal"] > [role="tablist"] {
    margin-bottom: 3rem !important; /* Increased from 2rem to 3rem for more space */
  }

  .mobile-tabs-fix[data-orientation="horizontal"]
    > [data-state="active"][role="tabpanel"] {
    margin-top: 1.5rem !important; /* Increased from 1rem to 1.5rem */
    padding-top: 1rem !important; /* Added padding to create more space */
    position: relative; /* Added for positioning context */
  }

  /* Add a subtle separator between tabs and content */
  .mobile-tabs-fix[data-orientation="horizontal"]
    > [data-state="active"][role="tabpanel"]::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.05); /* Very subtle separator */
    z-index: 1;
  }
}

/* Enhanced scroll capture for category grid */
#category-grid-section {
  scroll-margin-top: 4rem;
  position: relative;
  z-index: 10;
  scroll-snap-align: start;
  scroll-snap-stop: always;
}

/* Prevent body scroll when active scroll capture is happening */
body:has(#category-grid-section.active-scroll-capture) {
  overflow: hidden !important;
}

/* Improve scroll capture sensitivity */
@media (min-width: 768px) {
  #category-grid-section {
    min-height: 80vh; /* Slightly shorter than full viewport to improve capture */
  }
}

/* Horizontal scrolling styles */
#category-grid-section .hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

#category-grid-section .hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Cursor styles for horizontal scrolling */
#category-grid-section.cursor-grab {
  cursor: grab;
}

#category-grid-section .cursor-grabbing {
  cursor: grabbing !important;
}

/* Add visual indicator for scroll capture */
#category-grid-section::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary);
  opacity: 0;
  transform: scaleX(0);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

#category-grid-section:hover::after {
  opacity: 0.5;
  transform: scaleX(1);
}

/* Active scroll capture indicator */
#category-grid-section.active-scroll-capture::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--primary);
  opacity: 0.7;
  z-index: 20;
}

/* Scroll container styles */
#category-grid-section .scroll-container {
  position: relative;
  z-index: 5;
  touch-action: pan-x;
}

/* Responsive adjustments for horizontal scrolling */
@media (max-width: 768px) {
  #category-grid-section [class*="w-[600px]"] {
    width: 85vw !important;
    height: 250px !important;
  }

  #category-grid-section [class*="w-[300px]"] {
    width: 75vw !important;
    height: 250px !important;
  }
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}
