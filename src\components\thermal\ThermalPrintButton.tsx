import React, { useState, useEffect } from "react";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { OrderData } from "../receipt/ReceiptGenerator";
import { UnifiedReceiptManager } from "../../services/UnifiedReceiptManager";

interface ThermalPrintButtonProps {
  order: OrderData;
  style?: any;
  onPrintSuccess?: () => void;
  onPrintError?: (error: string) => void;
  onSetupRequired?: () => void;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  variant?: "primary" | "secondary" | "outline";
}

export default function ThermalPrintButton({
  order,
  style,
  onPrintSuccess,
  onPrintError,
  onSetupRequired,
  disabled = false,
  size = "medium",
  variant = "primary",
}: ThermalPrintButtonProps) {
  const [printing, setPrinting] = useState(false);
  const [available, setAvailable] = useState(false);
  const [checking, setChecking] = useState(true);

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
  }>({ title: "", message: "" });

  useEffect(() => {
    checkThermalPrinterAvailability();
  }, []);

  const checkThermalPrinterAvailability = async () => {
    setChecking(true);
    try {
      // For now, assume thermal printing is available
      // UnifiedReceiptManager will handle printer availability internally
      setAvailable(true);
    } catch (error) {
      console.error("Error checking thermal printer availability:", error);
      setAvailable(false);
    } finally {
      setChecking(false);
    }
  };

  const handleThermalPrint = async () => {
    if (disabled || printing) return;

    setPrinting(true);
    try {
      // UnifiedReceiptManager handles loyalty data extraction internally

      // Use UnifiedReceiptManager for consistent thermal printing with all data
      const unifiedResult = await UnifiedReceiptManager.generateReceipt(order, {
        format: "thermal",
        autoPrint: true,
        printerType: "thermal",
      });

      if (unifiedResult.success && unifiedResult.printed) {
        // Unified thermal printing successful
        onPrintSuccess?.();
        setModalData({
          title: "Print Successful",
          message:
            "Receipt printed successfully with all order details including discounts, loyalty points, and shipping fees.",
        });
        setShowSuccessModal(true);
      } else {
        // Unified printing failed
        const errorMessage = unifiedResult.error || "Print failed";
        onPrintError?.(errorMessage);
        setModalData({
          title: "Print Failed",
          message: `Failed to print receipt: ${errorMessage}\n\nPlease check your thermal printer connection.`,
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Print failed";
      onPrintError?.(errorMessage);
      setModalData({
        title: "Print Error",
        message: errorMessage,
      });
      setShowErrorModal(true);
    } finally {
      setPrinting(false);
    }
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[size]];

    if (variant === "primary") {
      baseStyle.push(styles.primaryButton);
    } else if (variant === "secondary") {
      baseStyle.push(styles.secondaryButton);
    } else if (variant === "outline") {
      baseStyle.push(styles.outlineButton);
    }

    if (disabled || !available) {
      baseStyle.push(styles.disabledButton);
    }

    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText, styles[`${size}Text`]];

    if (variant === "primary") {
      baseStyle.push(styles.primaryButtonText);
    } else if (variant === "secondary") {
      baseStyle.push(styles.secondaryButtonText);
    } else if (variant === "outline") {
      baseStyle.push(styles.outlineButtonText);
    }

    if (disabled || !available) {
      baseStyle.push(styles.disabledButtonText);
    }

    return baseStyle;
  };

  const getIconSize = () => {
    switch (size) {
      case "small":
        return 16;
      case "large":
        return 24;
      default:
        return 20;
    }
  };

  const getIconColor = () => {
    if (disabled || !available) return "#999";

    switch (variant) {
      case "primary":
        return "#fff";
      case "secondary":
        return "#fff";
      case "outline":
        return "#007AFF";
      default:
        return "#fff";
    }
  };

  if (checking) {
    return (
      <View style={[getButtonStyle(), style]}>
        <ActivityIndicator size="small" color={getIconColor()} />
        <Text style={getTextStyle()}>Checking...</Text>
      </View>
    );
  }

  if (!available) {
    return (
      <TouchableOpacity
        style={[getButtonStyle(), style]}
        onPress={onSetupRequired}
        disabled={disabled}
      >
        <Ionicons name="settings" size={getIconSize()} color={getIconColor()} />
        <Text style={getTextStyle()}>Setup Printer</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View>
      <TouchableOpacity
        style={[getButtonStyle(), style]}
        onPress={handleThermalPrint}
        disabled={disabled || printing}
      >
        {printing ? (
          <ActivityIndicator size="small" color={getIconColor()} />
        ) : (
          <Ionicons name="print" size={getIconSize()} color={getIconColor()} />
        )}
        <Text style={getTextStyle()}>
          {printing ? "Printing..." : "Thermal Print"}
        </Text>
      </TouchableOpacity>

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowErrorModal(false)}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowSuccessModal(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    paddingHorizontal: 16,
  },
  small: {
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  medium: {
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  large: {
    paddingVertical: 14,
    paddingHorizontal: 20,
  },
  primaryButton: {
    backgroundColor: "#007AFF",
  },
  secondaryButton: {
    backgroundColor: "#34C759",
  },
  outlineButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "#007AFF",
  },
  disabledButton: {
    backgroundColor: "#f0f0f0",
    borderColor: "#ccc",
  },
  buttonText: {
    fontWeight: "600",
    marginLeft: 6,
  },
  smallText: {
    fontSize: 12,
  },
  mediumText: {
    fontSize: 14,
  },
  largeText: {
    fontSize: 16,
  },
  primaryButtonText: {
    color: "#fff",
  },
  secondaryButtonText: {
    color: "#fff",
  },
  outlineButtonText: {
    color: "#007AFF",
  },
  disabledButtonText: {
    color: "#999",
  },
});
