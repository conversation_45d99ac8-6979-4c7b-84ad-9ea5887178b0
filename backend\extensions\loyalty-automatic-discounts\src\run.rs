use crate::schema;
use shopify_function::prelude::*;
use shopify_function::Result;

#[derive(Deserialize, Default, PartialEq)]
pub struct Configuration {
    // Automatic loyalty discount configuration
    pub enabled: bool,                      // Enable automatic loyalty discounts
    pub tier_benefits: TierBenefits,        // Tier-specific benefits
    pub special_promotions: SpecialPromotions, // Special promotion rules
    pub birthday_discount: f64,             // Birthday month discount percentage
    pub anniversary_discount: f64,          // Anniversary discount percentage
    pub min_order_amount: f64,              // Minimum order for automatic discounts
    pub max_discount_per_order: f64,        // Maximum total discount per order
    pub stackable_with_other_discounts: bool, // Can stack with other discounts
}

#[derive(Deserialize, Default, PartialEq)]
pub struct TierBenefits {
    pub bronze: TierBenefit,
    pub silver: TierBenefit,
    pub gold: TierBenefit,
    pub platinum: TierBenefit,
}

#[derive(Deserialize, Default, PartialEq)]
pub struct TierBenefit {
    pub automatic_discount: f64,            // Automatic discount percentage
    pub free_shipping_threshold: f64,       // Free shipping threshold
    pub bonus_points_multiplier: f64,       // Bonus points multiplier
    pub early_access_enabled: bool,         // Early access to sales
}

#[derive(Deserialize, Default, PartialEq)]
pub struct SpecialPromotions {
    pub double_points_days: Vec<String>,    // Days of week for double points
    pub seasonal_bonus: f64,                // Seasonal bonus percentage
    pub bulk_purchase_threshold: f64,       // Bulk purchase threshold
    pub bulk_purchase_discount: f64,        // Bulk purchase discount
    pub category_specific_discounts: Vec<CategoryDiscount>, // Category-specific discounts
}

#[derive(Deserialize, Default, PartialEq)]
pub struct CategoryDiscount {
    pub product_type: String,               // Product type/category
    pub discount_percentage: f64,           // Discount percentage for this category
    pub min_tier: String,                   // Minimum tier required
}

#[shopify_function]
fn run(input: schema::run::Input) -> Result<schema::FunctionRunResult> {
    // Parse configuration or return empty discount if no configuration exists
    let config: &Configuration = match input.discount_node().metafield() {
        Some(metafield) => metafield.json_value(),
        None => return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::Maximum,
        }),
    };

    // Check if automatic discounts are enabled
    if !config.enabled {
        return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::Maximum,
        });
    }

    // Check minimum order amount
    let order_total = input.cart().cost().subtotal_amount().amount().0;
    if order_total < Decimal(config.min_order_amount) {
        return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::Maximum,
        });
    }

    // Get customer data
    let customer = input.cart().buyer_identity()
        .and_then(|identity| identity.customer());

    if customer.is_none() {
        return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::Maximum,
        });
    }

    let customer = customer.unwrap();

    // Get loyalty tier
    let loyalty_tier = customer.loyalty_tier()
        .and_then(|metafield| metafield.value().as_ref())
        .unwrap_or("bronze");

    // Check if customer is loyalty member
    let is_loyalty_member = customer.tags().iter().any(|tag| tag == "loyalty-member");
    
    if !is_loyalty_member {
        return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::Maximum,
        });
    }

    // Get tier benefits
    let tier_benefit = match loyalty_tier {
        "platinum" => &config.tier_benefits.platinum,
        "gold" => &config.tier_benefits.gold,
        "silver" => &config.tier_benefits.silver,
        "bronze" => &config.tier_benefits.bronze,
        _ => &config.tier_benefits.bronze,
    };

    let mut discounts = vec![];
    let mut total_discount_applied = 0.0;

    // Apply tier-based automatic discount
    if tier_benefit.automatic_discount > 0.0 {
        let discount_amount = (order_total.0 * tier_benefit.automatic_discount) / 100.0;
        
        if discount_amount <= config.max_discount_per_order {
            discounts.push(schema::Discount {
                value: schema::Value::Percentage(schema::Percentage {
                    value: Decimal(tier_benefit.automatic_discount),
                }),
                targets: vec![schema::Target::OrderSubtotal(schema::OrderSubtotalTarget {})],
                message: Some(format!(
                    "{} tier automatic discount: {}% off",
                    loyalty_tier.to_uppercase(),
                    tier_benefit.automatic_discount
                )),
            });
            total_discount_applied += discount_amount;
        }
    }

    // Check for birthday discount
    if let Some(birthday_attr) = input.cart().attribute("birthday_month") {
        if let Some(birthday_month) = birthday_attr.value().as_ref() {
            let current_month = chrono::Utc::now().format("%m").to_string();
            if birthday_month == &current_month && config.birthday_discount > 0.0 {
                let birthday_discount_amount = (order_total.0 * config.birthday_discount) / 100.0;
                
                if total_discount_applied + birthday_discount_amount <= config.max_discount_per_order {
                    discounts.push(schema::Discount {
                        value: schema::Value::Percentage(schema::Percentage {
                            value: Decimal(config.birthday_discount),
                        }),
                        targets: vec![schema::Target::OrderSubtotal(schema::OrderSubtotalTarget {})],
                        message: Some(format!(
                            "Happy Birthday! {}% birthday discount",
                            config.birthday_discount
                        )),
                    });
                    total_discount_applied += birthday_discount_amount;
                }
            }
        }
    }

    // Apply category-specific discounts
    for line in input.cart().lines() {
        if let schema::run::input::cart::lines::Merchandise::ProductVariant(variant) = &line.merchandise() {
            let product_type = variant.product().product_type().unwrap_or("");
            
            for category_discount in &config.special_promotions.category_specific_discounts {
                if product_type == category_discount.product_type {
                    // Check if customer meets minimum tier requirement
                    let tier_order = match loyalty_tier {
                        "platinum" => 4,
                        "gold" => 3,
                        "silver" => 2,
                        "bronze" => 1,
                        _ => 0,
                    };
                    
                    let required_tier_order = match category_discount.min_tier.as_str() {
                        "platinum" => 4,
                        "gold" => 3,
                        "silver" => 2,
                        "bronze" => 1,
                        _ => 0,
                    };
                    
                    if tier_order >= required_tier_order {
                        let line_discount_amount = (line.cost().subtotal_amount().amount().0 * category_discount.discount_percentage) / 100.0;
                        
                        if total_discount_applied + line_discount_amount <= config.max_discount_per_order {
                            discounts.push(schema::Discount {
                                value: schema::Value::Percentage(schema::Percentage {
                                    value: Decimal(category_discount.discount_percentage),
                                }),
                                targets: vec![schema::Target::CartLine(schema::CartLineTarget {
                                    id: line.id().to_string(),
                                    quantity: Some(*line.quantity()),
                                })],
                                message: Some(format!(
                                    "{}% {} category discount for {} tier",
                                    category_discount.discount_percentage,
                                    category_discount.product_type,
                                    loyalty_tier
                                )),
                            });
                            total_discount_applied += line_discount_amount;
                        }
                    }
                }
            }
        }
    }

    // Apply bulk purchase discount
    if order_total.0 >= config.special_promotions.bulk_purchase_threshold && config.special_promotions.bulk_purchase_discount > 0.0 {
        let bulk_discount_amount = (order_total.0 * config.special_promotions.bulk_purchase_discount) / 100.0;
        
        if total_discount_applied + bulk_discount_amount <= config.max_discount_per_order {
            discounts.push(schema::Discount {
                value: schema::Value::Percentage(schema::Percentage {
                    value: Decimal(config.special_promotions.bulk_purchase_discount),
                }),
                targets: vec![schema::Target::OrderSubtotal(schema::OrderSubtotalTarget {})],
                message: Some(format!(
                    "Bulk purchase discount: {}% off orders over KSh {}",
                    config.special_promotions.bulk_purchase_discount,
                    config.special_promotions.bulk_purchase_threshold
                )),
            });
        }
    }

    Ok(schema::FunctionRunResult {
        discounts,
        discount_application_strategy: schema::DiscountApplicationStrategy::Maximum,
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use shopify_function::{run_function_with_input, Result};

    #[test]
    fn test_tier_automatic_discount() -> Result<()> {
        let result = run_function_with_input(
            run,
            r#"
                {
                    "cart": {
                        "lines": [
                            {
                                "id": "gid://shopify/CartLine/1",
                                "quantity": 1,
                                "cost": {
                                    "amountPerQuantity": {
                                        "amount": "100.00"
                                    },
                                    "subtotalAmount": {
                                        "amount": "100.00"
                                    }
                                },
                                "merchandise": {
                                    "__typename": "ProductVariant",
                                    "id": "gid://shopify/ProductVariant/1",
                                    "product": {
                                        "id": "gid://shopify/Product/1",
                                        "title": "Test Product",
                                        "tags": [],
                                        "productType": "Electronics"
                                    }
                                }
                            }
                        ],
                        "cost": {
                            "subtotalAmount": {
                                "amount": "100.00"
                            },
                            "totalAmount": {
                                "amount": "100.00"
                            }
                        },
                        "buyerIdentity": {
                            "customer": {
                                "id": "gid://shopify/Customer/1",
                                "tags": ["loyalty-member"],
                                "loyaltyTier": {
                                    "value": "gold"
                                }
                            }
                        }
                    },
                    "discountNode": {
                        "metafield": {
                            "jsonValue": {
                                "enabled": true,
                                "tier_benefits": {
                                    "gold": {
                                        "automatic_discount": 10.0
                                    }
                                },
                                "min_order_amount": 50.0,
                                "max_discount_per_order": 50.0
                            }
                        }
                    }
                }
            "#,
        )?;

        assert!(!result.discounts.is_empty());
        Ok(())
    }
}
