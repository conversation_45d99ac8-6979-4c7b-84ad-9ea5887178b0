/**
 * PIN Verification Hook
 *
 * Provides PIN verification functionality for dashboard navigation
 * and other secure actions in the POS system.
 */

import { useState, useCallback } from "react";
import { Alert } from "react-native";
import { useSession } from "@/src/contexts/AuthContext";
import { useUserSwitching } from "@/src/contexts/UserSwitchingContext";

interface PinVerificationState {
  isVisible: boolean;
  isVerifying: boolean;
  pendingAction: (() => void) | null;
  title?: string;
  description?: string;
}

export const usePinVerification = () => {
  const { user } = useSession();
  const { validatePin } = useUserSwitching();

  const [state, setState] = useState<PinVerificationState>({
    isVisible: false,
    isVerifying: false,
    pendingAction: null,
  });

  /**
   * Request PIN verification before executing an action
   */
  const requestPinVerification = useCallback(
    (
      action: () => void,
      options?: {
        title?: string;
        description?: string;
      }
    ) => {
      setState({
        isVisible: true,
        isVerifying: false,
        pendingAction: action,
        title: options?.title,
        description: options?.description,
      });
    },
    []
  );

  /**
   * Verify the entered PIN
   */
  const verifyPin = useCallback(
    async (pin: string): Promise<boolean> => {
      if (!user?.id) {
        console.error("No user found for PIN verification");
        return false;
      }

      try {
        setState((prev) => ({ ...prev, isVerifying: true }));

        const isValid = await validatePin(user.id, pin);

        if (isValid && state.pendingAction) {
          // Execute the pending action
          state.pendingAction();

          // Close the modal
          setState({
            isVisible: false,
            isVerifying: false,
            pendingAction: null,
          });

          return true;
        }

        setState((prev) => ({ ...prev, isVerifying: false }));
        return isValid;
      } catch (error) {
        console.error("PIN verification error:", error);
        setState((prev) => ({ ...prev, isVerifying: false }));
        return false;
      }
    },
    [user?.id, validatePin, state.pendingAction]
  );

  /**
   * Execute pending action after external PIN verification
   */
  const executePendingAction = useCallback(() => {
    if (state.pendingAction) {
      // Execute the pending action
      state.pendingAction();

      // Close the modal
      setState({
        isVisible: false,
        isVerifying: false,
        pendingAction: null,
      });
    }
  }, [state.pendingAction]);

  /**
   * Close the PIN verification modal
   */
  const closePinVerification = useCallback(() => {
    if (!state.isVerifying) {
      setState({
        isVisible: false,
        isVerifying: false,
        pendingAction: null,
      });
    }
  }, [state.isVerifying]);

  /**
   * Check if user has a PIN set up
   */
  const hasPin = useCallback((): boolean => {
    return user?.has_pin === true;
  }, [user?.has_pin]);

  /**
   * Convenience method for dashboard navigation with PIN verification
   * Always shows the PIN modal and lets it handle PIN status internally
   */
  const navigateWithPinVerification = useCallback(
    (navigationAction: () => void, itemName?: string) => {
      // Always show the PIN modal - let it handle PIN status internally
      // This provides a consistent modal experience and proper error handling
      requestPinVerification(navigationAction, {
        title: "PIN Verification Required",
        description: itemName
          ? `Enter your PIN to access ${itemName}`
          : "Enter your PIN to continue",
      });
    },
    [requestPinVerification]
  );

  return {
    // State
    isVisible: state.isVisible,
    isVerifying: state.isVerifying,
    title: state.title || "PIN Verification Required",
    description: state.description || "Please enter your PIN to continue",

    // Actions
    requestPinVerification,
    verifyPin,
    executePendingAction,
    closePinVerification,
    navigateWithPinVerification,

    // Utilities
    hasPin,
  };
};
