/**
 * User Switching Context
 *
 * Manages PIN-based user switching state, session context,
 * and provides methods for switching between users without full logout.
 */

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  type PropsWithChildren,
} from "react";
import { getAPIClient } from "../services/api/dukalink-client";
import { useSession } from "./AuthContext";

export interface SessionUser {
  id: string;
  username: string;
  name: string;
  role: string;
  commissionRate?: number;
  permissions: string[];
}

export interface SessionContext {
  sessionId: string;
  primaryUser: SessionUser;
  currentUser: SessionUser;
  userStack: SessionUser[];
  hasActiveSwitch: boolean;
  canSwitchBack: boolean;
  lastSwitchAt?: string;
  terminalId?: string;
  locationId?: string;
}

export interface AvailableStaff {
  id: string;
  username: string;
  name: string;
  role: string;
  commission_rate?: number;
  has_pin: boolean;
}

export interface UserSwitchHistory {
  id: string;
  from_staff_name?: string;
  to_staff_name: string;
  switch_reason: string;
  switched_at: string;
  switched_back_at?: string;
}

interface UserSwitchingContextType {
  // Session state
  sessionContext: SessionContext | null;
  availableStaff: AvailableStaff[];
  switchHistory: UserSwitchHistory[];
  isLoading: boolean;
  error: string | null;

  // Switch state
  isSwitching: boolean;
  currentSwitchId: string | null;

  // Methods
  loadSessionContext: () => Promise<void>;
  loadAvailableStaff: () => Promise<void>;
  loadSwitchHistory: () => Promise<void>;
  switchUser: (
    targetStaffId: string,
    pin: string,
    reason?: string
  ) => Promise<boolean>;
  switchBack: () => Promise<boolean>;
  validatePin: (staffId: string, pin: string) => Promise<boolean>;
  initializePin: (
    staffId: string,
    pin: string,
    confirmPin: string
  ) => Promise<boolean>;
  clearError: () => void;
  refreshContext: () => Promise<void>;
}

const UserSwitchingContext = createContext<UserSwitchingContextType>({
  sessionContext: null,
  availableStaff: [],
  switchHistory: [],
  isLoading: false,
  error: null,
  isSwitching: false,
  currentSwitchId: null,
  loadSessionContext: async () => {},
  loadAvailableStaff: async () => {},
  loadSwitchHistory: async () => {},
  switchUser: async () => false,
  switchBack: async () => false,
  validatePin: async () => false,
  initializePin: async () => false,
  clearError: () => {},
  refreshContext: async () => {},
});

export function useUserSwitching() {
  const value = useContext(UserSwitchingContext);
  if (!value) {
    throw new Error(
      "useUserSwitching must be wrapped in a <UserSwitchingProvider />"
    );
  }
  return value;
}

export function UserSwitchingProvider({ children }: PropsWithChildren) {
  const [sessionContext, setSessionContext] = useState<SessionContext | null>(
    null
  );
  const [availableStaff, setAvailableStaff] = useState<AvailableStaff[]>([]);
  const [switchHistory, setSwitchHistory] = useState<UserSwitchHistory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSwitching, setIsSwitching] = useState(false);
  const [currentSwitchId, setCurrentSwitchId] = useState<string | null>(null);

  const { isPosAuthenticated, user, session, setPosAuth } = useSession();
  const loadSessionContext = useCallback(async () => {
    if (!isPosAuthenticated) return;

    try {
      setIsLoading(true);
      setError(null);

      const apiClient = getAPIClient();
      const response = await apiClient.getSessionContext();

      if (response.success && response.data) {
        setSessionContext(response.data.sessionContext);
        setCurrentSwitchId(response.data.sessionContext.lastSwitchId || null);

        // Note: Auth context update is handled separately to avoid circular dependencies
      } else {
        setError(response.error || "Failed to load session context");
      }
    } catch (error) {
      console.error("Load session context error:", error);
      setError("Failed to load session context");
    } finally {
      setIsLoading(false);
    }
  }, [isPosAuthenticated]);

  const loadAvailableStaff = useCallback(async () => {
    if (!isPosAuthenticated) return;

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getAvailableStaff();

      if (response.success && response.data) {
        setAvailableStaff(response.data.staff);
      } else {
        console.error("Failed to load available staff:", response.error);
      }
    } catch (error) {
      console.error("Load available staff error:", error);
    }
  }, [isPosAuthenticated]);

  // Load session context on mount and when authentication changes
  useEffect(() => {
    if (isPosAuthenticated) {
      loadSessionContext();
      loadAvailableStaff();
    } else {
      // Clear state when not authenticated
      setSessionContext(null);
      setAvailableStaff([]);
      setSwitchHistory([]);
      setCurrentSwitchId(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPosAuthenticated]); // Only depend on authentication status to avoid infinite loops

  const loadSwitchHistory = useCallback(async () => {
    if (!isPosAuthenticated) return;

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getUserSwitchHistory(20);

      if (response.success && response.data) {
        setSwitchHistory(response.data.history);
      } else {
        console.error("Failed to load switch history:", response.error);
      }
    } catch (error) {
      console.error("Load switch history error:", error);
    }
  }, [isPosAuthenticated]);

  const switchUser = async (
    targetStaffId: string,
    pin: string,
    reason?: string
  ): Promise<boolean> => {
    try {
      setIsSwitching(true);
      setError(null);

      const apiClient = getAPIClient();
      const response = await apiClient.switchUser(targetStaffId, pin, reason);

      if (response.success && response.data) {
        // Update session context
        setSessionContext(response.data.sessionContext);
        setCurrentSwitchId(response.data.switchId);

        // Update auth context with new current user
        const newCurrentUser = response.data.sessionContext.currentUser;

        // Find the PIN status from available staff data
        const staffWithPin = availableStaff.find(
          (staff) => staff.id === newCurrentUser.id
        );
        const hasPinStatus = staffWithPin?.has_pin || false;

        setPosAuth(session || "", {
          ...newCurrentUser,
          storeId: user?.storeId || "",
          lastLogin: user?.lastLogin,
          has_pin: hasPinStatus, // Include PIN status
        });

        // Refresh related data
        await loadSwitchHistory();

        return true;
      } else {
        setError(response.error || "Failed to switch user");
        return false;
      }
    } catch (error) {
      console.error("Switch user error:", error);
      setError("Failed to switch user");
      return false;
    } finally {
      setIsSwitching(false);
    }
  };

  const switchBack = async (): Promise<boolean> => {
    if (!currentSwitchId) {
      setError("No active switch to revert");
      return false;
    }

    try {
      setIsSwitching(true);
      setError(null);

      const apiClient = getAPIClient();
      const response = await apiClient.switchBack(currentSwitchId);

      if (response.success && response.data) {
        // Update session context
        setSessionContext(response.data.sessionContext);
        setCurrentSwitchId(null);

        // Update auth context with reverted user
        const revertedUser = response.data.sessionContext.currentUser;

        // Find the PIN status from available staff data
        const staffWithPin = availableStaff.find(
          (staff) => staff.id === revertedUser.id
        );
        const hasPinStatus = staffWithPin?.has_pin || false;

        setPosAuth(session || "", {
          ...revertedUser,
          storeId: user?.storeId || "",
          lastLogin: user?.lastLogin,
          has_pin: hasPinStatus, // Include PIN status
        });

        // Refresh related data
        await loadSwitchHistory();

        return true;
      } else {
        setError(response.error || "Failed to switch back");
        return false;
      }
    } catch (error) {
      console.error("Switch back error:", error);
      setError("Failed to switch back");
      return false;
    } finally {
      setIsSwitching(false);
    }
  };

  const validatePin = async (
    staffId: string,
    pin: string
  ): Promise<boolean> => {
    try {
      setError(null);

      const apiClient = getAPIClient();
      const response = await apiClient.validateUserPin(staffId, pin);

      if (response.success && response.data) {
        return response.data.valid;
      } else {
        setError(response.error || "PIN validation failed");
        return false;
      }
    } catch (error) {
      console.error("Validate PIN error:", error);
      setError("PIN validation failed");
      return false;
    }
  };

  const initializePin = async (
    staffId: string,
    pin: string,
    confirmPin: string
  ): Promise<boolean> => {
    try {
      setError(null);

      const apiClient = getAPIClient();
      const response = await apiClient.initializeUserPin(
        staffId,
        pin,
        confirmPin
      );

      if (response.success) {
        // Refresh available staff to update PIN status
        await loadAvailableStaff();
        return true;
      } else {
        setError(response.error || "Failed to initialize PIN");
        return false;
      }
    } catch (error) {
      console.error("Initialize PIN error:", error);
      setError("Failed to initialize PIN");
      return false;
    }
  };

  const clearError = () => {
    setError(null);
  };

  const refreshContext = useCallback(async () => {
    await Promise.all([
      loadSessionContext(),
      loadAvailableStaff(),
      loadSwitchHistory(),
    ]);
  }, [loadSessionContext, loadAvailableStaff, loadSwitchHistory]);

  return (
    <UserSwitchingContext.Provider
      value={{
        sessionContext,
        availableStaff,
        switchHistory,
        isLoading,
        error,
        isSwitching,
        currentSwitchId,
        loadSessionContext,
        loadAvailableStaff,
        loadSwitchHistory,
        switchUser,
        switchBack,
        validatePin,
        initializePin,
        clearError,
        refreshContext,
      }}
    >
      {children}
    </UserSwitchingContext.Provider>
  );
}
