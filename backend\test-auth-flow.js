/**
 * Test Authentication Flow
 * Debug the authentication process step by step
 */

require('dotenv').config();
const { authService } = require('./src/middleware/auth-mysql');

async function testAuthFlow() {
  console.log('🔍 Testing authentication flow...\n');
  
  try {
    // Test 1: Check if authService is working
    console.log('1. Testing authService connection...');
    
    // Test 2: Try to get staff by username
    console.log('2. Testing getStaffByUsername...');
    const staff = await authService.getStaffByUsername('admin1');
    
    if (!staff) {
      console.log('❌ Staff not found for username: admin1');
      return;
    }
    
    console.log('✅ Staff found:');
    console.log(`   ID: ${staff.id}`);
    console.log(`   Username: ${staff.username}`);
    console.log(`   Name: ${staff.name}`);
    console.log(`   Role: ${staff.role}`);
    console.log(`   Active: ${staff.isActive}`);
    console.log(`   Password Hash: ${staff.passwordHash.substring(0, 20)}...`);
    console.log(`   Permissions: ${staff.permissions.join(', ')}`);
    
    // Test 3: Test password verification
    console.log('\n3. Testing password verification...');
    const bcrypt = require('bcrypt');
    
    const testPasswords = ['admin123', 'wrong_password'];
    
    for (const testPassword of testPasswords) {
      try {
        const isValid = await bcrypt.compare(testPassword, staff.passwordHash);
        console.log(`   Password "${testPassword}": ${isValid ? '✅ VALID' : '❌ INVALID'}`);
      } catch (error) {
        console.log(`   Password "${testPassword}": ⚠️  ERROR - ${error.message}`);
      }
    }
    
    // Test 4: Test session creation
    console.log('\n4. Testing session creation...');
    const crypto = require('crypto');
    const jwt = require('jsonwebtoken');
    
    // Generate a test token
    const token = jwt.sign(
      {
        userId: staff.id,
        username: staff.username,
        role: staff.role,
        storeId: staff.storeId,
        permissions: staff.permissions,
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );
    
    console.log(`   Generated JWT token: ${token.substring(0, 50)}...`);
    
    // Create session
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    const sessionId = await authService.createSession(staff.id, tokenHash, {
      terminalId: 'test-terminal',
      locationId: 'test-location',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Agent'
    });
    
    if (sessionId) {
      console.log(`   ✅ Session created: ${sessionId}`);
      
      // Test session validation
      const session = await authService.validateSession(tokenHash);
      if (session) {
        console.log(`   ✅ Session validated successfully`);
      } else {
        console.log(`   ❌ Session validation failed`);
      }
    } else {
      console.log(`   ❌ Session creation failed`);
    }
    
    console.log('\n🎉 Authentication flow test completed!');
    
  } catch (error) {
    console.error('❌ Authentication flow test failed:', error.message);
    console.error('   Stack:', error.stack);
  }
}

testAuthFlow();
