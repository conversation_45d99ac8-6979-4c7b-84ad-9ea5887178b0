/**
 * Customer Card with Loyalty Information
 *
 * Enhanced customer card component that displays customer information
 * along with loyalty tier, points balance, and available discounts.
 * Used in checkout customer selection and customer management screens.
 */

import { IconSymbol } from "@/components/ui/IconSymbol";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { loyaltyService } from "@/src/services/loyalty-service";
import { useAppDispatch, useAppSelector } from "@/src/store";
import {
  fetchCustomerLoyalty,
  selectCustomerLoyalty,
  selectCustomerLoyaltyError,
  selectCustomerLoyaltyLoading,
} from "@/src/store/slices/customerSlice";
import { Customer } from "@/src/types/shopify";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { useEffect } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface CustomerCardWithLoyaltyProps {
  customer: Customer;
  onPress?: (customer: Customer) => void;
  showLoyalty?: boolean;
  compact?: boolean;
  style?: any;
}

export const CustomerCardWithLoyalty: React.FC<
  CustomerCardWithLoyaltyProps
> = ({ customer, onPress, showLoyalty = true, compact = false, style }) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const dispatch = useAppDispatch();
  const { hasPermission } = useRBAC();

  // Check if user can view loyalty information
  const canViewLoyalty =
    hasPermission("view_loyalty") || hasPermission("manage_loyalty");

  // Get loyalty data from Redux store
  const loyaltyData = useAppSelector(selectCustomerLoyalty(customer.id));
  const loyaltyLoading = useAppSelector(
    selectCustomerLoyaltyLoading(customer.id)
  );
  const loyaltyError = useAppSelector(selectCustomerLoyaltyError(customer.id));

  // Fetch loyalty data when component mounts
  useEffect(() => {
    if (
      showLoyalty &&
      canViewLoyalty &&
      customer.id &&
      !loyaltyData &&
      !loyaltyLoading
    ) {
      dispatch(fetchCustomerLoyalty(customer.id));
    }
  }, [
    customer.id,
    showLoyalty,
    canViewLoyalty,
    loyaltyData,
    loyaltyLoading,
    dispatch,
  ]);

  const handlePress = () => {
    if (onPress) {
      onPress(customer);
    }
  };

  const getTierColor = (tier: string) => {
    return loyaltyService.getTierColor(tier);
  };

  const getTierIcon = (tier: string) => {
    return loyaltyService.getTierIcon(tier);
  };

  const formatTier = (tier: string) => {
    return loyaltyService.formatLoyaltyTier(tier);
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: compact ? 12 : 16,
      marginVertical: 4,
      borderWidth: 1,
      borderColor: theme.colors.border,
      ...utils.shadow(1),
    },
    pressable: {
      opacity: 0.7,
    },
    customerRow: {
      flexDirection: "row",
      alignItems: "center",
    },
    avatar: {
      width: compact ? 40 : 48,
      height: compact ? 40 : 48,
      borderRadius: compact ? 20 : 24,
      backgroundColor: theme.colors.primary,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
    },
    avatarText: {
      color: theme.colors.surface,
      fontSize: compact ? 16 : 18,
      fontWeight: "600",
    },
    customerInfo: {
      flex: 1,
    },
    customerName: {
      fontSize: compact ? 16 : 18,
      fontWeight: "600",
      color: theme.colors.text,
      marginBottom: 2,
    },
    customerContact: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 1,
    },
    loyaltySection: {
      marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    loyaltyRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    tierContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    tierIcon: {
      marginRight: 6,
    },
    tierText: {
      fontSize: 14,
      fontWeight: "600",
    },
    pointsContainer: {
      alignItems: "flex-end",
    },
    pointsText: {
      fontSize: 14,
      fontWeight: "600",
      color: theme.colors.primary,
    },
    pointsLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    availableDiscountText: {
      fontSize: 12,
      color: theme.colors.success,
      marginTop: 2,
    },
    loyaltyLoading: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 8,
    },
    loyaltyLoadingText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginLeft: 8,
    },
    loyaltyError: {
      fontSize: 12,
      color: theme.colors.error,
      textAlign: "center",
      paddingVertical: 4,
    },
    noLoyaltyText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textAlign: "center",
      fontStyle: "italic",
    },
  });

  const renderLoyaltySection = () => {
    if (!showLoyalty || !canViewLoyalty) {
      return null;
    }

    if (loyaltyLoading) {
      return (
        <View style={styles.loyaltySection}>
          <View style={styles.loyaltyLoading}>
            <IconSymbol
              name="arrow.clockwise"
              size={12}
              color={theme.colors.textSecondary}
            />
            <Text style={styles.loyaltyLoadingText}>Loading loyalty...</Text>
          </View>
        </View>
      );
    }

    if (loyaltyError) {
      return (
        <View style={styles.loyaltySection}>
          <Text style={styles.loyaltyError}>Unable to load loyalty data</Text>
        </View>
      );
    }

    if (!loyaltyData) {
      return (
        <View style={styles.loyaltySection}>
          <Text style={styles.noLoyaltyText}>No loyalty data available</Text>
        </View>
      );
    }

    const availableDiscount =
      loyaltyData.redemptionInfo?.availableDiscount || 0;

    return (
      <View style={styles.loyaltySection}>
        <View style={styles.loyaltyRow}>
          <View style={styles.tierContainer}>
            <IconSymbol
              name={getTierIcon(loyaltyData.tier)}
              size={16}
              color={getTierColor(loyaltyData.tier)}
              style={styles.tierIcon}
            />
            <Text
              style={[
                styles.tierText,
                { color: getTierColor(loyaltyData.tier) },
              ]}
            >
              {formatTier(loyaltyData.tier)}
            </Text>
          </View>

          <View style={styles.pointsContainer}>
            <Text style={styles.pointsText}>
              {(loyaltyData.loyaltyPoints || 0).toLocaleString()} pts
            </Text>
            <Text style={styles.pointsLabel}>Loyalty Points</Text>
            {availableDiscount > 0 && (
              <Text style={styles.availableDiscountText}>
                {formatCurrency(availableDiscount)} available
              </Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      activeOpacity={onPress ? 0.7 : 1}
      disabled={!onPress}
    >
      <View style={styles.customerRow}>
        <View
          style={[styles.avatar, { backgroundColor: theme.colors.primary }]}
        >
          <Text style={styles.avatarText}>
            {(customer.firstName || customer.displayName || "U")
              .charAt(0)
              .toUpperCase()}
          </Text>
        </View>

        <View style={styles.customerInfo}>
          <Text style={styles.customerName}>
            {customer.displayName ||
              `${customer.firstName || ""} ${customer.lastName || ""}`.trim() ||
              "Unknown Customer"}
          </Text>
          {customer.email && (
            <Text style={styles.customerContact}>{customer.email}</Text>
          )}
          {customer.phone && (
            <Text style={styles.customerContact}>{customer.phone}</Text>
          )}
        </View>
      </View>

      {renderLoyaltySection()}
    </TouchableOpacity>
  );
};
