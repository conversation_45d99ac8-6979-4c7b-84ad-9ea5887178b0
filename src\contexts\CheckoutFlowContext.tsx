import React, { createContext, useContext, useState, PropsWithChildren } from 'react';

interface CheckoutFlowContextType {
  isInCheckoutFlow: boolean;
  startCheckoutFlow: () => void;
  endCheckoutFlow: () => void;
  checkoutStep: 'customer' | 'agent' | 'payment' | null;
  setCheckoutStep: (step: 'customer' | 'agent' | 'payment' | null) => void;
}

const CheckoutFlowContext = createContext<CheckoutFlowContextType>({
  isInCheckoutFlow: false,
  startCheckoutFlow: () => {},
  endCheckoutFlow: () => {},
  checkoutStep: null,
  setCheckoutStep: () => {},
});

export function useCheckoutFlow() {
  const context = useContext(CheckoutFlowContext);
  if (!context) {
    throw new Error('useCheckoutFlow must be used within a CheckoutFlowProvider');
  }
  return context;
}

export function CheckoutFlowProvider({ children }: PropsWithChildren) {
  const [isInCheckoutFlow, setIsInCheckoutFlow] = useState(false);
  const [checkoutStep, setCheckoutStep] = useState<'customer' | 'agent' | 'payment' | null>(null);

  const startCheckoutFlow = () => {
    setIsInCheckoutFlow(true);
    setCheckoutStep('customer');
  };

  const endCheckoutFlow = () => {
    setIsInCheckoutFlow(false);
    setCheckoutStep(null);
  };

  return (
    <CheckoutFlowContext.Provider
      value={{
        isInCheckoutFlow,
        startCheckoutFlow,
        endCheckoutFlow,
        checkoutStep,
        setCheckoutStep,
      }}
    >
      {children}
    </CheckoutFlowContext.Provider>
  );
}
