/**
 * Loyalty Transactions List Component
 * Displays customer loyalty transaction history with filtering and pagination
 */

import React, { useState } from "react";
import {
  View,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import {
  ThemedView,
  ThemedText,
} from "@/src/components/themed/ThemedComponents";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { CustomerLoyaltyData, LoyaltyTransaction } from "@/src/types/shopify";

interface LoyaltyTransactionsListProps {
  transactions: LoyaltyTransaction[];
  loading?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  onTransactionPress?: (transaction: LoyaltyTransaction) => void;
  showFilters?: boolean;
}

const TRANSACTION_ICONS = {
  earned: "plus.circle.fill",
  redeemed: "minus.circle.fill",
  adjusted: "pencil.circle.fill",
  expired: "clock.fill",
};

const TRANSACTION_COLORS = {
  earned: "#10B981", // Green
  redeemed: "#F59E0B", // Orange
  adjusted: "#3B82F6", // Blue
  expired: "#EF4444", // Red
};

const TRANSACTION_LABELS = {
  earned: "Points Earned",
  redeemed: "Points Redeemed",
  adjusted: "Points Adjusted",
  expired: "Points Expired",
};

export const LoyaltyTransactionsList: React.FC<
  LoyaltyTransactionsListProps
> = ({
  transactions,
  loading = false,
  onRefresh,
  onLoadMore,
  hasMore = false,
  onTransactionPress,
  showFilters = false,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const [selectedFilter, setSelectedFilter] = useState<string>("all");

  const filteredTransactions = transactions.filter((transaction) => {
    if (selectedFilter === "all") return true;
    return transaction.type === selectedFilter;
  });

  const renderTransactionItem = ({ item }: { item: LoyaltyTransaction }) => {
    const transactionColor = TRANSACTION_COLORS[item.type];
    const transactionIcon = TRANSACTION_ICONS[item.type];
    const transactionLabel = TRANSACTION_LABELS[item.type];
    const isPositive = item.points > 0;

    return (
      <TouchableOpacity
        onPress={() => onTransactionPress?.(item)}
        activeOpacity={0.8}
      >
        <ThemedView
          variant="card"
          style={[styles.transactionCard, utils.p("md")]}
        >
          <View style={styles.transactionHeader}>
            <View style={styles.transactionIcon}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: `${transactionColor}20` },
                ]}
              >
                <IconSymbol
                  name={transactionIcon}
                  size={20}
                  color={transactionColor}
                />
              </View>
              <View style={styles.transactionInfo}>
                <ThemedText variant="body" style={styles.transactionType}>
                  {transactionLabel}
                </ThemedText>
                <ThemedText variant="small" color="secondary">
                  {new Date(item.createdAt).toLocaleDateString()} at{" "}
                  {new Date(item.createdAt).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </ThemedText>
              </View>
            </View>

            <View style={styles.pointsContainer}>
              <ThemedText
                variant="h3"
                style={[
                  styles.pointsAmount,
                  {
                    color: isPositive
                      ? TRANSACTION_COLORS.earned
                      : transactionColor,
                  },
                ]}
              >
                {isPositive ? "+" : ""}
                {item.points.toLocaleString()}
              </ThemedText>
              <ThemedText variant="caption" color="secondary">
                points
              </ThemedText>
            </View>
          </View>

          {/* Description */}
          <ThemedText
            variant="small"
            color="secondary"
            style={styles.description}
          >
            {item.description}
          </ThemedText>

          {/* Order ID */}
          {item.orderId && (
            <View style={styles.orderInfo}>
              <IconSymbol
                name="receipt"
                size={14}
                color={theme.colors.textSecondary}
              />
              <ThemedText
                variant="caption"
                color="muted"
                style={styles.orderId}
              >
                Order #{item.orderId}
              </ThemedText>
            </View>
          )}

          {/* Staff Attribution */}
          {(item.staffName || item.salesAgentName) && (
            <View style={styles.attributionContainer}>
              {item.staffName && (
                <View style={styles.attributionItem}>
                  <IconSymbol
                    name="person.fill"
                    size={12}
                    color={theme.colors.textSecondary}
                  />
                  <ThemedText variant="caption" color="muted">
                    Staff: {item.staffName}
                  </ThemedText>
                </View>
              )}
              {item.salesAgentName && (
                <View style={styles.attributionItem}>
                  <IconSymbol
                    name="person.2.fill"
                    size={12}
                    color={theme.colors.textSecondary}
                  />
                  <ThemedText variant="caption" color="muted">
                    Agent: {item.salesAgentName}
                  </ThemedText>
                </View>
              )}
            </View>
          )}
        </ThemedView>
      </TouchableOpacity>
    );
  };

  const renderFilterButtons = () => {
    if (!showFilters) return null;

    const filters = [
      { key: "all", label: "All" },
      { key: "earned", label: "Earned" },
      { key: "redeemed", label: "Redeemed" },
      { key: "adjusted", label: "Adjusted" },
    ];

    return (
      <View style={styles.filtersContainer}>
        <ThemedText variant="small" color="secondary" style={utils.mb("sm")}>
          Filter by type:
        </ThemedText>
        <View style={styles.filterButtons}>
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterButton,
                {
                  backgroundColor:
                    selectedFilter === filter.key
                      ? theme.colors.primary
                      : theme.colors.surface,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={() => setSelectedFilter(filter.key)}
            >
              <ThemedText
                variant="small"
                style={{
                  color:
                    selectedFilter === filter.key
                      ? theme.colors.primaryForeground
                      : theme.colors.text,
                }}
              >
                {filter.label}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <IconSymbol name="clock" size={48} color={theme.colors.textSecondary} />
      <ThemedText variant="h3" color="secondary" style={utils.mt("md")}>
        No Transactions Yet
      </ThemedText>
      <ThemedText
        variant="body"
        color="muted"
        style={[utils.mt("sm"), styles.emptyText]}
      >
        Loyalty transactions will appear here as customers earn and redeem
        points.
      </ThemedText>
    </View>
  );

  const renderFooter = () => {
    if (!hasMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <ThemedText variant="small" color="secondary" style={utils.ml("sm")}>
          Loading more...
        </ThemedText>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderFilterButtons()}

      <FlatList
        data={filteredTransactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={[
          styles.listContent,
          filteredTransactions.length === 0 && styles.emptyListContent,
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={loading}
              onRefresh={onRefresh}
              tintColor={theme.colors.primary}
            />
          ) : undefined
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        ListFooterComponent={renderFooter}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterButtons: {
    flexDirection: "row",
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: "center",
  },
  transactionCard: {
    marginBottom: 12,
  },
  transactionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  transactionIcon: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionType: {
    fontWeight: "600",
  },
  pointsContainer: {
    alignItems: "flex-end",
  },
  pointsAmount: {
    fontWeight: "700",
  },
  description: {
    marginBottom: 8,
    lineHeight: 18,
  },
  orderInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  orderId: {
    marginLeft: 4,
  },
  attributionContainer: {
    flexDirection: "row",
    gap: 12,
    marginTop: 4,
  },
  attributionItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  emptyContainer: {
    alignItems: "center",
    paddingVertical: 48,
  },
  emptyText: {
    textAlign: "center",
    maxWidth: 280,
  },
  footerLoader: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 16,
  },
});
