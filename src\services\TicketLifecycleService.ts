/**
 * Professional Ticket Lifecycle Service
 *
 * Manages the complete lifecycle of tickets in a professional POS environment,
 * including creation, status transitions, completion, and archival.
 */

import { store } from "@/src/store";
import {
  completeActiveTicket,
  updateTicketStatus as updateTicketStatusAction,
  selectActiveTicket,
  selectAllTickets,
} from "@/src/store/slices/ticketSlice";
import {
  updateTicketStatus as updateTicketStatusThunk,
  saveTicket,
} from "@/src/store/thunks/ticketThunks";
import { clearCart } from "@/src/store/slices/cartSlice";

export interface TicketLifecycleEvent {
  ticketId: string;
  event:
    | "created"
    | "paused"
    | "resumed"
    | "completed"
    | "cancelled"
    | "archived";
  timestamp: string;
  staffId: string;
  reason?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  method: string;
  amount: number;
  timestamp: string;
  error?: string;
}

export class TicketLifecycleService {
  private static instance: TicketLifecycleService;
  private eventLog: TicketLifecycleEvent[] = [];

  static getInstance(): TicketLifecycleService {
    if (!TicketLifecycleService.instance) {
      TicketLifecycleService.instance = new TicketLifecycleService();
    }
    return TicketLifecycleService.instance;
  }

  /**
   * Complete ticket after successful payment
   */
  async completeTicketAfterPayment(
    ticketId: string,
    paymentResult: PaymentResult,
    staffId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const state = store.getState();
      const ticket = state.tickets.tickets.find((t) => t.id === ticketId);

      if (!ticket) {
        return { success: false, error: "Ticket not found" };
      }

      if (ticket.status !== "active") {
        return {
          success: false,
          error: `Cannot complete ticket with status: ${ticket.status}`,
        };
      }

      if (!paymentResult.success) {
        return {
          success: false,
          error: "Cannot complete ticket: payment failed",
        };
      }

      // Complete the ticket - UPDATE BACKEND FIRST
      try {
        // Update backend via thunk (which also updates Redux)
        await store
          .dispatch(
            updateTicketStatusThunk({
              ticketId: ticket.id,
              status: "completed",
            })
          )
          .unwrap();

        console.log(
          `✅ Backend updated: Ticket ${ticketId} marked as completed`
        );
      } catch (backendError) {
        console.error(
          `❌ Backend update failed for ticket ${ticketId}:`,
          backendError
        );

        // Fallback to local update if backend fails
        if (ticket.id === state.tickets.activeTicketId) {
          store.dispatch(completeActiveTicket());
        } else {
          store.dispatch(
            updateTicketStatusAction({
              ticketId: ticket.id,
              status: "completed",
            })
          );
        }

        console.log(`⚠️ Fallback: Local completion for ticket ${ticketId}`);
      }

      // Clear cart if this was the active ticket
      if (ticket.id === state.tickets.activeTicketId) {
        store.dispatch(clearCart());
      }

      // Log the event
      this.logEvent({
        ticketId,
        event: "completed",
        timestamp: new Date().toISOString(),
        staffId,
        metadata: {
          paymentMethod: paymentResult.method,
          transactionId: paymentResult.transactionId,
          amount: paymentResult.amount,
        },
      });

      console.log(`✅ Ticket ${ticketId} completed successfully after payment`);
      return { success: true };
    } catch (error) {
      console.error("Error completing ticket after payment:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Handle payment failure scenarios
   */
  async handlePaymentFailure(
    ticketId: string,
    paymentResult: PaymentResult,
    staffId: string
  ): Promise<{ success: boolean; action: string }> {
    try {
      const state = store.getState();
      const ticket = state.tickets.tickets.find((t) => t.id === ticketId);

      if (!ticket) {
        return { success: false, action: "ticket_not_found" };
      }

      // Keep ticket active for retry
      // Could implement different strategies based on failure type

      this.logEvent({
        ticketId,
        event: "created", // Using 'created' as placeholder for payment_failed
        timestamp: new Date().toISOString(),
        staffId,
        reason: "payment_failed",
        metadata: {
          paymentMethod: paymentResult.method,
          error: paymentResult.error,
          amount: paymentResult.amount,
        },
      });

      console.log(
        `❌ Payment failed for ticket ${ticketId}, keeping active for retry`
      );
      return { success: true, action: "keep_active_for_retry" };
    } catch (error) {
      console.error("Error handling payment failure:", error);
      return { success: false, action: "error_handling_failure" };
    }
  }

  /**
   * Cancel ticket (before payment)
   */
  async cancelTicket(
    ticketId: string,
    staffId: string,
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const state = store.getState();
      const ticket = state.tickets.tickets.find((t) => t.id === ticketId);

      if (!ticket) {
        return { success: false, error: "Ticket not found" };
      }

      if (ticket.status === "completed") {
        return { success: false, error: "Cannot cancel completed ticket" };
      }

      // Cancel the ticket - UPDATE BACKEND FIRST
      try {
        await store
          .dispatch(
            updateTicketStatusThunk({
              ticketId,
              status: "cancelled",
            })
          )
          .unwrap();
        console.log(`✅ Backend updated: Ticket ${ticketId} cancelled`);
      } catch (backendError) {
        console.error(
          `❌ Backend update failed for ticket ${ticketId}:`,
          backendError
        );
        // Fallback to local update
        store.dispatch(
          updateTicketStatusAction({
            ticketId,
            status: "cancelled",
          })
        );
        console.log(`⚠️ Fallback: Local cancellation for ticket ${ticketId}`);
      }

      // Clear cart if this was the active ticket
      if (ticket.id === state.tickets.activeTicketId) {
        store.dispatch(clearCart());
      }

      this.logEvent({
        ticketId,
        event: "cancelled",
        timestamp: new Date().toISOString(),
        staffId,
        reason,
      });

      console.log(`🚫 Ticket ${ticketId} cancelled by ${staffId}`);
      return { success: true };
    } catch (error) {
      console.error("Error cancelling ticket:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Pause ticket (customer steps away)
   */
  async pauseTicket(
    ticketId: string,
    staffId: string,
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Pause the ticket - UPDATE BACKEND FIRST
      try {
        await store
          .dispatch(
            updateTicketStatusThunk({
              ticketId,
              status: "paused",
            })
          )
          .unwrap();
        console.log(`✅ Backend updated: Ticket ${ticketId} paused`);
      } catch (backendError) {
        console.error(
          `❌ Backend update failed for ticket ${ticketId}:`,
          backendError
        );
        // Fallback to local update
        store.dispatch(
          updateTicketStatusAction({
            ticketId,
            status: "paused",
          })
        );
        console.log(`⚠️ Fallback: Local pause for ticket ${ticketId}`);
      }

      this.logEvent({
        ticketId,
        event: "paused",
        timestamp: new Date().toISOString(),
        staffId,
        reason,
      });

      console.log(`⏸️ Ticket ${ticketId} paused by ${staffId}`);
      return { success: true };
    } catch (error) {
      console.error("Error pausing ticket:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Resume paused ticket
   */
  async resumeTicket(
    ticketId: string,
    staffId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Resume the ticket - UPDATE BACKEND FIRST
      try {
        await store
          .dispatch(
            updateTicketStatusThunk({
              ticketId,
              status: "active",
            })
          )
          .unwrap();
        console.log(`✅ Backend updated: Ticket ${ticketId} resumed`);
      } catch (backendError) {
        console.error(
          `❌ Backend update failed for ticket ${ticketId}:`,
          backendError
        );
        // Fallback to local update
        store.dispatch(
          updateTicketStatusAction({
            ticketId,
            status: "active",
          })
        );
        console.log(`⚠️ Fallback: Local resume for ticket ${ticketId}`);
      }

      this.logEvent({
        ticketId,
        event: "resumed",
        timestamp: new Date().toISOString(),
        staffId,
      });

      console.log(`▶️ Ticket ${ticketId} resumed by ${staffId}`);
      return { success: true };
    } catch (error) {
      console.error("Error resuming ticket:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Get ticket lifecycle status
   */
  getTicketStatus(ticketId: string): {
    status: string;
    canComplete: boolean;
    canCancel: boolean;
    canPause: boolean;
    canResume: boolean;
    recommendedAction: string;
  } {
    const state = store.getState();
    const ticket = state.tickets.tickets.find((t) => t.id === ticketId);

    if (!ticket) {
      return {
        status: "not_found",
        canComplete: false,
        canCancel: false,
        canPause: false,
        canResume: false,
        recommendedAction: "create_new_ticket",
      };
    }

    const hasItems = ticket.items && ticket.items.length > 0;

    return {
      status: ticket.status,
      canComplete: ticket.status === "active" && hasItems,
      canCancel: ["active", "paused"].includes(ticket.status),
      canPause: ticket.status === "active",
      canResume: ticket.status === "paused",
      recommendedAction: this.getRecommendedAction(ticket),
    };
  }

  /**
   * Get recommended action for ticket
   */
  private getRecommendedAction(ticket: any): string {
    const hasItems = ticket.items && ticket.items.length > 0;

    switch (ticket.status) {
      case "active":
        return hasItems ? "process_payment" : "add_items";
      case "paused":
        return "resume_ticket";
      case "completed":
        return "view_receipt";
      case "cancelled":
        return "archive_or_duplicate";
      default:
        return "review_ticket";
    }
  }

  /**
   * Log lifecycle event
   */
  private logEvent(event: TicketLifecycleEvent): void {
    this.eventLog.push(event);

    // Keep only last 1000 events in memory
    if (this.eventLog.length > 1000) {
      this.eventLog = this.eventLog.slice(-1000);
    }
  }

  /**
   * Get event history for ticket
   */
  getTicketEventHistory(ticketId: string): TicketLifecycleEvent[] {
    return this.eventLog.filter((event) => event.ticketId === ticketId);
  }

  /**
   * Get system-wide lifecycle statistics
   */
  getLifecycleStats(): {
    totalEvents: number;
    completedToday: number;
    cancelledToday: number;
    averageTicketLifetime: number;
  } {
    const today = new Date().toDateString();
    const todayEvents = this.eventLog.filter(
      (event) => new Date(event.timestamp).toDateString() === today
    );

    return {
      totalEvents: this.eventLog.length,
      completedToday: todayEvents.filter((e) => e.event === "completed").length,
      cancelledToday: todayEvents.filter((e) => e.event === "cancelled").length,
      averageTicketLifetime: 0, // Would need more complex calculation
    };
  }
}

// Export singleton instance
export const ticketLifecycleService = TicketLifecycleService.getInstance();
