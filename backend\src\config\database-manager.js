/**
 * Centralized Database Manager
 *
 * Singleton class that manages a single MySQL connection pool for the entire application.
 * Prevents connection pool multiplication and provides proper connection lifecycle management.
 *
 * Features:
 * - Single connection pool for all services
 * - Proper timeout configurations
 * - Connection leak detection
 * - Health monitoring
 * - Automatic retry logic
 * - Environment-based configuration
 */

require("dotenv").config();
const mysql = require("mysql2/promise");

class DatabaseManager {
  constructor() {
    if (DatabaseManager.instance) {
      return DatabaseManager.instance;
    }

    this.pool = null;
    this.isInitialized = false;
    this.connectionStats = {
      totalConnections: 0,
      activeConnections: 0,
      queuedRequests: 0,
      totalQueries: 0,
      failedQueries: 0,
      lastHealthCheck: null,
    };
    this.healthCheckInterval = null;

    DatabaseManager.instance = this;
  }

  /**
   * Initialize the database connection pool
   */
  async initialize() {
    if (this.isInitialized) {
      return this.pool;
    }

    try {
      console.log("🔌 Initializing centralized database connection pool...");

      // Create connection pool with production-ready configuration
      this.pool = mysql.createPool({
        host: process.env.DB_HOST || "localhost",
        port: parseInt(process.env.DB_PORT) || 3306,
        user: process.env.DB_USER || "dukalink",
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME || "dukalink_pos",
        charset: process.env.DB_CHARSET || "utf8mb4",

        // Connection pool settings from environment
        connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 20,
        queueLimit: parseInt(process.env.DB_QUEUE_LIMIT) || 0,

        // Timeout configurations for production stability
        acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000, // 60 seconds
        timeout: parseInt(process.env.DB_TIMEOUT) || 60000, // 60 seconds
        idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT) || 300000, // 5 minutes

        // Connection management
        waitForConnections: true,
        reconnect: true,

        // Additional stability settings
        multipleStatements: false,
        dateStrings: false,
        debug: false, // Disabled for cleaner logs

        // SSL configuration for production
        ssl:
          process.env.DB_SSL === "true"
            ? {
                rejectUnauthorized:
                  process.env.DB_SSL_REJECT_UNAUTHORIZED !== "false",
              }
            : false,
      });

      // Set up connection pool event listeners
      this.setupPoolEventListeners();

      // Test the connection
      await this.testConnection();

      // Start health monitoring
      this.startHealthMonitoring();

      this.isInitialized = true;
      console.log("✅ Database connection pool initialized successfully");
      console.log(
        `📊 Pool configuration: ${
          this.pool.config?.connectionLimit || "unknown"
        } max connections`
      );

      return this.pool;
    } catch (error) {
      console.error("❌ Failed to initialize database connection pool:", error);
      throw error;
    }
  }

  /**
   * Set up connection pool event listeners for monitoring
   */
  setupPoolEventListeners() {
    this.pool.on("connection", (connection) => {
      this.connectionStats.totalConnections++;
      // Disabled verbose logging
    });

    this.pool.on("acquire", (connection) => {
      this.connectionStats.activeConnections++;
      // Disabled verbose logging
    });

    this.pool.on("release", (connection) => {
      this.connectionStats.activeConnections--;
      // Disabled verbose logging
    });

    this.pool.on("error", (error) => {
      console.error("❌ Database pool error:", error);
      this.connectionStats.failedQueries++;
    });
  }

  /**
   * Test database connection
   */
  async testConnection() {
    const connection = await this.pool.getConnection();
    try {
      await connection.execute("SELECT 1 as test");
      console.log("✅ Database connection test successful");
    } finally {
      connection.release();
    }
  }

  /**
   * Execute query with automatic connection management
   * This is the primary method all services should use
   */
  async executeQuery(query, params = []) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const connection = await this.pool.getConnection();
    const startTime = Date.now();

    try {
      this.connectionStats.totalQueries++;
      const result = await connection.execute(query, params);

      const duration = Date.now() - startTime;
      if (duration > 5000) {
        // Log slow queries
        console.warn(
          `🐌 Slow query detected (${duration}ms):`,
          query.substring(0, 100)
        );
      }

      return result;
    } catch (error) {
      this.connectionStats.failedQueries++;
      console.error("❌ Database query error:", {
        query: query.substring(0, 100),
        error: error.message,
        params: params.length,
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Execute transaction with automatic rollback on error
   */
  async executeTransaction(transactionCallback) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const connection = await this.pool.getConnection();

    try {
      await connection.beginTransaction();
      console.log("🔄 Database transaction started");

      const result = await transactionCallback(connection);

      await connection.commit();
      console.log("✅ Database transaction committed");

      return result;
    } catch (error) {
      await connection.rollback();
      console.error("❌ Database transaction rolled back:", error.message);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Get connection for manual management (use sparingly)
   */
  async getConnection() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return await this.pool.getConnection();
  }

  /**
   * Get connection pool statistics
   */
  getStats() {
    return {
      connectionStats: this.connectionStats,
      poolConfig: this.pool
        ? {
            connectionLimit:
              this.pool.config?.connectionLimit ||
              parseInt(process.env.DB_CONNECTION_LIMIT) ||
              20,
            queueLimit:
              this.pool.config?.queueLimit ||
              parseInt(process.env.DB_QUEUE_LIMIT) ||
              0,
            acquireTimeout:
              this.pool.config?.acquireTimeout ||
              parseInt(process.env.DB_ACQUIRE_TIMEOUT) ||
              60000,
            timeout:
              this.pool.config?.timeout ||
              parseInt(process.env.DB_TIMEOUT) ||
              60000,
          }
        : null,
      poolState: this.pool
        ? {
            allConnections: this.pool._allConnections?.length || 0,
            freeConnections: this.pool._freeConnections?.length || 0,
            connectionQueue: this.pool._connectionQueue?.length || 0,
          }
        : null,
    };
  }

  /**
   * Start health monitoring
   */
  startHealthMonitoring() {
    const healthCheckInterval =
      parseInt(process.env.DB_HEALTH_CHECK_INTERVAL) || 30000; // 30 seconds

    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error("❌ Database health check failed:", error.message);
      }
    }, healthCheckInterval);

    console.log(
      `💓 Database health monitoring started (${healthCheckInterval}ms interval)`
    );
  }

  /**
   * Perform health check
   */
  async performHealthCheck() {
    try {
      const stats = this.getStats();

      // Ensure connectionStats exists
      if (!stats.connectionStats) {
        console.warn("⚠️ Connection stats not available during health check");
        return;
      }

      this.connectionStats.lastHealthCheck = new Date().toISOString();

      // Log stats if in development or if there are issues
      if (
        process.env.NODE_ENV === "development" ||
        process.env.NODE_ENV === "local" ||
        stats.poolState?.connectionQueue > 0
      ) {
        console.log("💓 Database health check:", {
          activeConnections: stats.connectionStats.activeConnections || 0,
          totalQueries: stats.connectionStats.totalQueries || 0,
          failedQueries: stats.connectionStats.failedQueries || 0,
          poolState: stats.poolState,
        });
      }

      // Alert if connection queue is building up
      if (stats.poolState?.connectionQueue > 5) {
        console.warn(
          "⚠️ High connection queue detected:",
          stats.poolState.connectionQueue
        );
      }

      // Alert if too many failed queries
      if ((stats.connectionStats.failedQueries || 0) > 100) {
        console.warn(
          "⚠️ High number of failed queries:",
          stats.connectionStats.failedQueries
        );
      }
    } catch (error) {
      console.error("❌ Health check error:", error.message);
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    if (this.pool) {
      console.log("🔌 Closing database connection pool...");
      await this.pool.end();
      console.log("✅ Database connection pool closed");
    }

    this.isInitialized = false;
  }

  /**
   * Get singleton instance
   */
  static getInstance() {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }
}

// Export singleton instance
const databaseManager = DatabaseManager.getInstance();

module.exports = {
  DatabaseManager,
  databaseManager,
  // Legacy exports for backward compatibility
  getPool: () => databaseManager.pool,
  executeQuery: (query, params) => databaseManager.executeQuery(query, params),
  executeTransaction: (callback) =>
    databaseManager.executeTransaction(callback),
};
