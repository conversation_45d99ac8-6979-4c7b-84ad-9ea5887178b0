/**
 * Shopify Webhooks Handler
 * Handles incoming webhooks from Shopify for order events and loyalty synchronization
 */

const express = require("express");
const router = express.Router();
const crypto = require("crypto");
const loyaltySyncService = require("../services/loyalty-sync-service");
const loyaltyService = require("../services/loyalty-service");
const ResponseFormatter = require("../utils/response-formatter");

// Webhook verification middleware
const verifyWebhook = (req, res, next) => {
  try {
    const hmac = req.get('X-Shopify-Hmac-Sha256');
    const body = req.body;
    const webhookSecret = process.env.SHOPIFY_WEBHOOK_SECRET;

    if (!webhookSecret) {
      console.error("Shopify webhook secret not configured");
      return res.status(500).json({ error: "Webhook secret not configured" });
    }

    if (!hmac) {
      console.error("Missing HMAC header");
      return res.status(401).json({ error: "Missing HMAC header" });
    }

    // Calculate expected HMAC
    const calculatedHmac = crypto
      .createHmac('sha256', webhookSecret)
      .update(JSON.stringify(body), 'utf8')
      .digest('base64');

    // Verify HMAC
    if (!crypto.timingSafeEqual(Buffer.from(hmac), Buffer.from(calculatedHmac))) {
      console.error("HMAC verification failed");
      return res.status(401).json({ error: "HMAC verification failed" });
    }

    next();
  } catch (error) {
    console.error("Webhook verification error:", error);
    return res.status(500).json({ error: "Webhook verification error" });
  }
};

// Order created webhook
router.post("/orders/create", verifyWebhook, async (req, res) => {
  try {
    const orderData = req.body;
    console.log(`Received order created webhook for order ${orderData.id}`);

    // For order creation, we typically don't award points yet
    // Points are awarded when the order is paid/fulfilled
    console.log(`Order ${orderData.id} created, waiting for payment confirmation`);

    return res.status(200).json({ 
      success: true, 
      message: "Order created webhook processed" 
    });
  } catch (error) {
    console.error("Order created webhook error:", error);
    return res.status(500).json({ error: "Failed to process order created webhook" });
  }
});

// Order paid webhook
router.post("/orders/paid", verifyWebhook, async (req, res) => {
  try {
    const orderData = req.body;
    console.log(`Received order paid webhook for order ${orderData.id}`);

    // Award loyalty points for paid orders
    const result = await loyaltySyncService.handleOrderCompleted(orderData);

    if (result.success) {
      console.log(`Successfully processed paid order ${orderData.id}`);
      return res.status(200).json({
        success: true,
        message: "Order paid webhook processed successfully",
        pointsAdded: result.pointsAdded,
        newBalance: result.newBalance,
        tierChanged: result.tierChanged
      });
    } else {
      console.error(`Failed to process paid order ${orderData.id}:`, result.error);
      return res.status(500).json({ error: result.error });
    }
  } catch (error) {
    console.error("Order paid webhook error:", error);
    return res.status(500).json({ error: "Failed to process order paid webhook" });
  }
});

// Order fulfilled webhook
router.post("/orders/fulfilled", verifyWebhook, async (req, res) => {
  try {
    const orderData = req.body;
    console.log(`Received order fulfilled webhook for order ${orderData.id}`);

    // For fulfilled orders, we might want to add bonus points or update tier
    const customerId = orderData.customer?.id;
    
    if (customerId) {
      // Queue customer for sync to update any tier changes
      await loyaltySyncService.queueCustomerSync(customerId, 'normal');
      
      // Check if customer qualifies for tier upgrade based on total purchases
      const loyaltyResult = await loyaltyService.getCustomerSummary(customerId);
      if (loyaltyResult.success) {
        const summary = loyaltyResult.summary;
        console.log(`Customer ${customerId} current tier: ${summary.tier}, total purchases: ${summary.totalPurchases}`);
      }
    }

    return res.status(200).json({ 
      success: true, 
      message: "Order fulfilled webhook processed" 
    });
  } catch (error) {
    console.error("Order fulfilled webhook error:", error);
    return res.status(500).json({ error: "Failed to process order fulfilled webhook" });
  }
});

// Order cancelled webhook
router.post("/orders/cancelled", verifyWebhook, async (req, res) => {
  try {
    const orderData = req.body;
    console.log(`Received order cancelled webhook for order ${orderData.id}`);

    const customerId = orderData.customer?.id;
    
    if (customerId) {
      // Check if points were awarded for this order and reverse them
      const [rows] = await loyaltyService.pool.execute(
        `SELECT * FROM loyalty_transactions 
         WHERE shopify_customer_id = ? AND order_id = ? AND transaction_type = 'earned'`,
        [customerId, orderData.id.toString()]
      );

      if (rows.length > 0) {
        const transaction = rows[0];
        const pointsToReverse = transaction.points_amount;

        // Create reversal transaction
        const reversalResult = await loyaltyService.pool.execute(
          `INSERT INTO loyalty_transactions (
            id, customer_loyalty_id, shopify_customer_id, transaction_type,
            points_amount, order_id, description, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
          [
            require('uuid').v4(),
            transaction.customer_loyalty_id,
            customerId,
            'cancelled',
            -pointsToReverse,
            orderData.id.toString(),
            `Points reversed due to order cancellation: ${orderData.id}`
          ]
        );

        // Update customer loyalty points
        await loyaltyService.pool.execute(
          `UPDATE customer_loyalty 
           SET loyalty_points = loyalty_points - ?, updated_at = NOW() 
           WHERE id = ?`,
          [pointsToReverse, transaction.customer_loyalty_id]
        );

        // Queue customer for sync
        await loyaltySyncService.queueCustomerSync(customerId, 'high');

        console.log(`Reversed ${pointsToReverse} points for cancelled order ${orderData.id}`);
      }
    }

    return res.status(200).json({ 
      success: true, 
      message: "Order cancelled webhook processed" 
    });
  } catch (error) {
    console.error("Order cancelled webhook error:", error);
    return res.status(500).json({ error: "Failed to process order cancelled webhook" });
  }
});

// Order refunded webhook
router.post("/orders/refunded", verifyWebhook, async (req, res) => {
  try {
    const orderData = req.body;
    console.log(`Received order refunded webhook for order ${orderData.id}`);

    const customerId = orderData.customer?.id;
    const refundAmount = parseFloat(orderData.total_price || 0);
    
    if (customerId && refundAmount > 0) {
      // Calculate points to reverse based on refund amount
      const pointsToReverse = Math.floor(refundAmount * loyaltyService.loyaltyConfig.pointsEarningRate);

      if (pointsToReverse > 0) {
        // Get customer loyalty record
        const loyaltyResult = await loyaltyService.getCustomerLoyalty(customerId);
        
        if (loyaltyResult.success) {
          // Create refund transaction
          await loyaltyService.pool.execute(
            `INSERT INTO loyalty_transactions (
              id, customer_loyalty_id, shopify_customer_id, transaction_type,
              points_amount, order_id, description, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
            [
              require('uuid').v4(),
              loyaltyResult.loyalty.id,
              customerId,
              'refunded',
              -pointsToReverse,
              orderData.id.toString(),
              `Points reversed due to refund: KSh ${refundAmount}`
            ]
          );

          // Update customer loyalty points (ensure it doesn't go below 0)
          await loyaltyService.pool.execute(
            `UPDATE customer_loyalty 
             SET loyalty_points = GREATEST(0, loyalty_points - ?), updated_at = NOW() 
             WHERE id = ?`,
            [pointsToReverse, loyaltyResult.loyalty.id]
          );

          // Queue customer for sync
          await loyaltySyncService.queueCustomerSync(customerId, 'high');

          console.log(`Reversed ${pointsToReverse} points for refunded order ${orderData.id}`);
        }
      }
    }

    return res.status(200).json({ 
      success: true, 
      message: "Order refunded webhook processed" 
    });
  } catch (error) {
    console.error("Order refunded webhook error:", error);
    return res.status(500).json({ error: "Failed to process order refunded webhook" });
  }
});

// Customer created webhook
router.post("/customers/create", verifyWebhook, async (req, res) => {
  try {
    const customerData = req.body;
    console.log(`Received customer created webhook for customer ${customerData.id}`);

    // Initialize loyalty record for new customer
    const initResult = await loyaltyService.initializeCustomerLoyalty(customerData.id);
    
    if (initResult.success) {
      // Queue customer for initial sync
      await loyaltySyncService.queueCustomerSync(customerData.id, 'normal');
      console.log(`Initialized loyalty for new customer ${customerData.id}`);
    }

    return res.status(200).json({ 
      success: true, 
      message: "Customer created webhook processed" 
    });
  } catch (error) {
    console.error("Customer created webhook error:", error);
    return res.status(500).json({ error: "Failed to process customer created webhook" });
  }
});

// Customer updated webhook
router.post("/customers/update", verifyWebhook, async (req, res) => {
  try {
    const customerData = req.body;
    console.log(`Received customer updated webhook for customer ${customerData.id}`);

    // Queue customer for sync to update any changes
    await loyaltySyncService.queueCustomerSync(customerData.id, 'normal');

    return res.status(200).json({ 
      success: true, 
      message: "Customer updated webhook processed" 
    });
  } catch (error) {
    console.error("Customer updated webhook error:", error);
    return res.status(500).json({ error: "Failed to process customer updated webhook" });
  }
});

// Test webhook endpoint (for development)
router.post("/test", async (req, res) => {
  try {
    console.log("Test webhook received:", req.body);
    
    return res.status(200).json({ 
      success: true, 
      message: "Test webhook processed",
      receivedData: req.body
    });
  } catch (error) {
    console.error("Test webhook error:", error);
    return res.status(500).json({ error: "Failed to process test webhook" });
  }
});

// Webhook health check
router.get("/health", async (req, res) => {
  try {
    const syncStatus = loyaltySyncService.getSyncQueueStatus();
    
    return ResponseFormatter.success(
      res,
      {
        status: "healthy",
        service: "shopify-webhooks",
        timestamp: new Date().toISOString(),
        syncQueue: syncStatus
      },
      "Shopify webhooks service health check completed"
    );
  } catch (error) {
    console.error("Webhooks health check error:", error);
    return ResponseFormatter.error(
      res,
      "Shopify webhooks service health check failed",
      503
    );
  }
});

module.exports = router;
