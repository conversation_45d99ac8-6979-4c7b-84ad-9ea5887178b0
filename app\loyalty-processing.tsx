/**
 * Loyalty Processing Management Screen
 * 
 * This screen provides access to the loyalty processing manager
 * for handling failed loyalty point processing attempts.
 */

import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useNavigation } from '@/src/contexts/NavigationContext';
import LoyaltyProcessingManager from '@/src/components/loyalty/LoyaltyProcessingManager';

export default function LoyaltyProcessingScreen() {
  const { setCurrentTitle } = useNavigation();

  useEffect(() => {
    setCurrentTitle('Loyalty Processing');
  }, [setCurrentTitle]);

  return (
    <View style={styles.container}>
      <LoyaltyProcessingManager />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});
