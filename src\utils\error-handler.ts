import { Alert } from 'react-native';

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

export class ErrorHandler {
  static createError(code: string, message: string, details?: any): AppError {
    return {
      code,
      message,
      details,
      timestamp: new Date(),
    };
  }

  static handleAPIError(error: any, context?: string): AppError {
    console.error(`API Error${context ? ` in ${context}` : ''}:`, error);

    // Handle different types of errors
    if (error.response) {
      // HTTP error response
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 400:
          return this.createError(
            'VALIDATION_ERROR',
            data?.error || 'Invalid request data',
            data
          );
        case 401:
          return this.createError(
            'AUTHENTICATION_ERROR',
            'Authentication required. Please log in again.',
            data
          );
        case 403:
          return this.createError(
            'AUTHORIZATION_ERROR',
            'You do not have permission to perform this action.',
            data
          );
        case 404:
          return this.createError(
            'NOT_FOUND_ERROR',
            'The requested resource was not found.',
            data
          );
        case 429:
          return this.createError(
            'RATE_LIMIT_ERROR',
            'Too many requests. Please try again later.',
            data
          );
        case 500:
          return this.createError(
            'SERVER_ERROR',
            'Internal server error. Please try again later.',
            data
          );
        default:
          return this.createError(
            'HTTP_ERROR',
            data?.error || `HTTP ${status} error`,
            data
          );
      }
    } else if (error.request) {
      // Network error
      return this.createError(
        'NETWORK_ERROR',
        'Network connection failed. Please check your internet connection.',
        error.request
      );
    } else if (error.code === 'TIMEOUT') {
      return this.createError(
        'TIMEOUT_ERROR',
        'Request timed out. Please try again.',
        error
      );
    } else {
      // Other errors
      return this.createError(
        'UNKNOWN_ERROR',
        error.message || 'An unexpected error occurred',
        error
      );
    }
  }

  static handleShopifyError(error: any, context?: string): AppError {
    console.error(`Shopify Error${context ? ` in ${context}` : ''}:`, error);

    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      const graphQLError = error.graphQLErrors[0];
      return this.createError(
        'SHOPIFY_GRAPHQL_ERROR',
        graphQLError.message || 'Shopify GraphQL error',
        graphQLError
      );
    }

    if (error.networkError) {
      return this.handleAPIError(error.networkError, 'Shopify Network');
    }

    return this.createError(
      'SHOPIFY_ERROR',
      error.message || 'Shopify API error',
      error
    );
  }

  static showErrorAlert(error: AppError, title?: string) {
    Alert.alert(
      title || 'Error',
      error.message,
      [{ text: 'OK', style: 'default' }],
      { cancelable: true }
    );
  }

  static showRetryAlert(
    error: AppError,
    onRetry: () => void,
    title?: string
  ) {
    Alert.alert(
      title || 'Error',
      error.message,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Retry', onPress: onRetry, style: 'default' },
      ],
      { cancelable: true }
    );
  }

  static getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.message) {
      return error.message;
    }

    if (error?.error) {
      return error.error;
    }

    return 'An unexpected error occurred';
  }

  static isNetworkError(error: AppError): boolean {
    return error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT_ERROR';
  }

  static isAuthError(error: AppError): boolean {
    return error.code === 'AUTHENTICATION_ERROR' || error.code === 'AUTHORIZATION_ERROR';
  }

  static shouldRetry(error: AppError): boolean {
    return this.isNetworkError(error) || 
           error.code === 'SERVER_ERROR' || 
           error.code === 'TIMEOUT_ERROR';
  }
}

// Error boundary helper for React components
export const withErrorBoundary = (Component: React.ComponentType<any>) => {
  return class extends React.Component {
    state = { hasError: false, error: null };

    static getDerivedStateFromError(error: any) {
      return { hasError: true, error };
    }

    componentDidCatch(error: any, errorInfo: any) {
      console.error('Error Boundary caught an error:', error, errorInfo);
      const appError = ErrorHandler.createError(
        'COMPONENT_ERROR',
        'A component error occurred',
        { error, errorInfo }
      );
      // You could send this to a logging service here
    }

    render() {
      if (this.state.hasError) {
        return (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
              Something went wrong
            </Text>
            <Text style={{ textAlign: 'center', marginBottom: 20 }}>
              An unexpected error occurred. Please restart the app.
            </Text>
            <TouchableOpacity
              onPress={() => this.setState({ hasError: false, error: null })}
              style={{
                backgroundColor: '#007AFF',
                padding: 12,
                borderRadius: 8,
              }}
            >
              <Text style={{ color: 'white', fontWeight: 'bold' }}>Try Again</Text>
            </TouchableOpacity>
          </View>
        );
      }

      return <Component {...this.props} />;
    }
  };
};
