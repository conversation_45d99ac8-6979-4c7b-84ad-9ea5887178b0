import { Colors } from "@/constants/Colors";
import { useThemeColor } from "@/hooks/useThemeColor";
import { MpesaPaymentForm } from "@/src/components/payment/MpesaPaymentForm";
import { createMpesaService } from "@/src/services/mpesa-service";
import { Ionicons } from "@expo/vector-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const MpesaPaymentScreen: React.FC = () => {
  const router = useRouter();
  const params = useLocalSearchParams();

  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<
    "idle" | "processing" | "success" | "failed"
  >("idle");
  const [checkoutRequestId, setCheckoutRequestId] = useState<string | null>(
    null
  );
  const [statusMessage, setStatusMessage] = useState("");

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = useThemeColor({}, "success");
  const errorColor = useThemeColor({}, "error");

  // Get params
  const amount = parseFloat(params.amount as string) || 0;
  const customerName = (params.customerName as string) || "Walk-in Customer";
  const orderId = params.orderId as string;
  const paramCheckoutRequestId = params.checkoutRequestId as string;
  const phoneNumber = params.phoneNumber as string;
  const customerMessage = params.customerMessage as string;

  // Check if we're coming from payment processing with existing checkout request
  React.useEffect(() => {
    if (paramCheckoutRequestId && phoneNumber) {
      // We have an existing payment request, start monitoring
      setIsProcessing(true);
      setPaymentStatus("processing");
      setStatusMessage(
        customerMessage ||
          "Payment request sent! Please check your phone for M-Pesa prompt."
      );

      const mpesaService = createMpesaService("sandbox");
      pollPaymentStatus(paramCheckoutRequestId, mpesaService);
    }
  }, [paramCheckoutRequestId, phoneNumber, customerMessage]);

  const handlePaymentInitiated = async (phoneNumber: string) => {
    setIsProcessing(true);
    setPaymentStatus("processing");
    setStatusMessage("Initiating M-Pesa payment...");

    try {
      const mpesaService = createMpesaService("sandbox");

      const paymentRequest = {
        phoneNumber,
        amount,
        accountReference: `ORDER-${orderId}`,
        transactionDesc: `Payment for Order ${orderId}`,
      };

      // Use simulation for development
      const isDevelopment = process.env.NODE_ENV === "development" || __DEV__;
      const response = isDevelopment
        ? await mpesaService.simulatePayment(paymentRequest)
        : await mpesaService.initiatePayment(paymentRequest);

      if (response.success) {
        setCheckoutRequestId(response.checkoutRequestId || null);
        setStatusMessage(
          response.customerMessage ||
            "Payment request sent! Please check your phone for M-Pesa prompt."
        );

        // Start polling for payment status
        if (response.checkoutRequestId) {
          pollPaymentStatus(response.checkoutRequestId, mpesaService);
        } else {
          // For simulation, auto-complete after delay
          setTimeout(() => {
            setPaymentStatus("success");
            setStatusMessage("Payment completed successfully!");
            setIsProcessing(false);
          }, 3000);
        }
      } else {
        setPaymentStatus("failed");
        setStatusMessage(response.error || "Failed to initiate payment");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("M-Pesa payment error:", error);
      setPaymentStatus("failed");
      setStatusMessage("Payment processing failed. Please try again.");
      setIsProcessing(false);
    }
  };

  const pollPaymentStatus = async (requestId: string, mpesaService: any) => {
    const maxAttempts = 30; // Poll for 5 minutes (30 * 10 seconds)
    let attempts = 0;

    const poll = async () => {
      try {
        // Use simulation for development
        const isDevelopment = process.env.NODE_ENV === "development" || __DEV__;
        const status = isDevelopment
          ? await mpesaService.simulatePaymentStatus(requestId)
          : await mpesaService.checkPaymentStatus(requestId);

        if (status.success) {
          setPaymentStatus("success");
          setStatusMessage("Payment completed successfully!");
          setIsProcessing(false);

          // Navigate back with success
          setTimeout(() => {
            router.replace({
              pathname: "/order-receipt",
              params: {
                orderId,
                paymentMethod: "M-Pesa",
                amount: amount.toString(),
                transactionId: status.mpesaReceiptNumber || requestId,
              },
            });
          }, 2000);

          return;
        }

        attempts++;
        if (attempts >= maxAttempts) {
          setPaymentStatus("failed");
          setStatusMessage(
            "Payment timeout. Please check your M-Pesa messages."
          );
          setIsProcessing(false);
          return;
        }

        // Continue polling
        setTimeout(poll, 10000); // Poll every 10 seconds
      } catch (error) {
        console.error("Error polling payment status:", error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 10000);
        } else {
          setPaymentStatus("failed");
          setStatusMessage("Unable to verify payment status.");
          setIsProcessing(false);
        }
      }
    };

    // Start polling after initial delay
    setTimeout(poll, 5000);
  };

  const handleCancel = () => {
    if (isProcessing) {
      Alert.alert(
        "Cancel Payment",
        "Are you sure you want to cancel the payment process?",
        [
          { text: "No", style: "cancel" },
          {
            text: "Yes",
            style: "destructive",
            onPress: () => router.back(),
          },
        ]
      );
    } else {
      router.back();
    }
  };

  const handleRetry = () => {
    setPaymentStatus("idle");
    setIsProcessing(false);
    setCheckoutRequestId(null);
    setStatusMessage("");
  };

  const renderPaymentStatus = () => {
    switch (paymentStatus) {
      case "processing":
        return (
          <View
            style={[styles.statusContainer, { backgroundColor: surfaceColor }]}
          >
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.statusTitle, { color: textColor }]}>
              Processing Payment
            </Text>
            <Text style={[styles.statusMessage, { color: textSecondary }]}>
              {statusMessage}
            </Text>
            <Text style={[styles.helpText, { color: textSecondary }]}>
              Please complete the payment on your phone. This may take a few
              moments.
            </Text>
          </View>
        );

      case "success":
        return (
          <View
            style={[styles.statusContainer, { backgroundColor: surfaceColor }]}
          >
            <Ionicons name="checkmark-circle" size={64} color={successColor} />
            <Text style={[styles.statusTitle, { color: successColor }]}>
              Payment Successful!
            </Text>
            <Text style={[styles.statusMessage, { color: textSecondary }]}>
              {statusMessage}
            </Text>
            <Text style={[styles.amountText, { color: textColor }]}>
              KSh {amount.toLocaleString()}
            </Text>
          </View>
        );

      case "failed":
        return (
          <View
            style={[styles.statusContainer, { backgroundColor: surfaceColor }]}
          >
            <Ionicons name="close-circle" size={64} color={errorColor} />
            <Text style={[styles.statusTitle, { color: errorColor }]}>
              Payment Failed
            </Text>
            <Text style={[styles.statusMessage, { color: textSecondary }]}>
              {statusMessage}
            </Text>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: primaryColor }]}
              onPress={handleRetry}
            >
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        );

      default:
        return (
          <MpesaPaymentForm
            amount={amount}
            onPaymentInitiated={handlePaymentInitiated}
            onCancel={handleCancel}
            isProcessing={isProcessing}
            initialPhoneNumber={phoneNumber}
          />
        );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleCancel} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={textColor} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: textColor }]}>
          M-Pesa Payment
        </Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>{renderPaymentStatus()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    justifyContent: "center",
  },
  statusContainer: {
    alignItems: "center",
    padding: 32,
    margin: 16,
    borderRadius: 12,
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  statusMessage: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 16,
    lineHeight: 22,
  },
  helpText: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  amountText: {
    fontSize: 24,
    fontWeight: "bold",
    marginTop: 8,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default MpesaPaymentScreen;
