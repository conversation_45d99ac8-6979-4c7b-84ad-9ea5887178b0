const axios = require("axios");

class ShopifyService {
  constructor() {
    this.apiKey = process.env.SHOPIFY_API_KEY;
    this.apiSecret = process.env.SHOPIFY_API_SECRET;
    this.shopDomain =
      process.env.SHOPIFY_SHOP_DOMAIN || "0ssy5g-hg.myshopify.com";
    this.accessToken = null; // Will be set after OAuth
    this.isConnected = false;

    if (!this.apiKey || !this.apiSecret) {
      throw new Error("Shopify API credentials not configured");
    }

    this.baseURL = `https://${this.shopDomain}/admin/api/2023-10`;

    // Try to load stored access token
    this.loadStoredToken();
  }

  // Load stored access token (in production, use database)
  loadStoredToken() {
    this.accessToken = process.env.SHOPIFY_ACCESS_TOKEN || null;
    this.isConnected = !!this.accessToken;

    if (this.isConnected) {
      console.log(`✅ Shopify service initialized with access token`);
    } else {
      console.log(
        `⚠️ Shopify service initialized without access token - OAuth required`
      );
    }
  }

  // Set access token after OAuth
  setAccessToken(token) {
    this.accessToken = token;
    this.isConnected = true;
    // In production, save to database
    console.log("✅ Shopify access token set successfully");
  }

  // Create authenticated request headers
  getHeaders() {
    if (!this.accessToken) {
      throw new Error(
        "No access token available. Store needs to be connected first."
      );
    }

    return {
      "X-Shopify-Access-Token": this.accessToken,
      "Content-Type": "application/json",
    };
  }

  // Check if store is connected
  isStoreConnected() {
    return this.isConnected && !!this.accessToken;
  }

  // Test connection to Shopify
  async testConnection() {
    try {
      const response = await axios.get(`${this.baseURL}/shop.json`, {
        headers: this.getHeaders(),
      });
      return {
        success: true,
        shop: response.data.shop,
      };
    } catch (error) {
      console.error(
        "Shopify connection test failed:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get total product count using GraphQL productsCount query
  async getProductCount() {
    try {
      const query = `
        query getProductCount {
          productsCount {
            count
            precision
          }
        }
      `;

      const response = await this.graphqlRequest(query, {});

      if (response.data?.productsCount?.count !== undefined) {
        return {
          success: true,
          count: response.data.productsCount.count,
        };
      }

      return {
        success: false,
        error: "Unable to fetch product count",
      };
    } catch (error) {
      console.error(
        "Failed to fetch product count:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get products using GraphQL for better performance and consistency
  async getProducts(limit = 50, cursor = null) {
    try {
      // Dynamic query based on whether cursor is provided
      const query = cursor
        ? `
          query getProducts($first: Int!, $after: String!) {
            products(first: $first, after: $after) {
              edges {
                cursor
                node {
                  id
                  title
                  handle
                  description
                  productType
                  vendor
                  tags
                  status
                  createdAt
                  updatedAt
                  images(first: 5) {
                    edges {
                      node {
                        id
                        url
                        altText
                        width
                        height
                      }
                    }
                  }
                  variants(first: 10) {
                    edges {
                      node {
                        id
                        title
                        price
                        compareAtPrice
                        sku
                        barcode
                        inventoryQuantity
                        weight
                        weightUnit
                        availableForSale
                      }
                    }
                  }
                }
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        `
        : `
          query getProducts($first: Int!) {
            products(first: $first) {
              edges {
                cursor
                node {
                  id
                  title
                  handle
                  description
                  productType
                  vendor
                  tags
                  status
                  createdAt
                  updatedAt
                  images(first: 5) {
                    edges {
                      node {
                        id
                        url
                        altText
                        width
                        height
                      }
                    }
                  }
                  variants(first: 10) {
                    edges {
                      node {
                        id
                        title
                        price
                        compareAtPrice
                        sku
                        barcode
                        inventoryQuantity
                        weight
                        weightUnit
                        availableForSale
                      }
                    }
                  }
                }
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        `;

      const variables = {
        first: Math.min(limit, 250), // Shopify GraphQL limit is 250
      };

      // Add cursor if it exists
      if (cursor) {
        variables.after = cursor;
      }

      const response = await this.graphqlRequest(query, variables);

      if (response.data?.products) {
        const products = response.data.products.edges.map((edge) => ({
          id: edge.node.id,
          title: edge.node.title,
          handle: edge.node.handle,
          description: edge.node.description,
          productType: edge.node.productType,
          vendor: edge.node.vendor,
          tags: edge.node.tags,
          status: edge.node.status,
          createdAt: edge.node.createdAt,
          updatedAt: edge.node.updatedAt,
          images: edge.node.images.edges.map((img) => ({
            id: img.node.id,
            url: img.node.url,
            altText: img.node.altText,
            width: img.node.width,
            height: img.node.height,
            src: img.node.url, // For compatibility with existing frontend
          })),
          variants: edge.node.variants.edges.map((variant) => ({
            id: variant.node.id,
            title: variant.node.title,
            price: variant.node.price,
            compareAtPrice: variant.node.compareAtPrice,
            sku: variant.node.sku,
            barcode: variant.node.barcode,
            inventoryQuantity: variant.node.inventoryQuantity,
            weight: variant.node.weight,
            weightUnit: variant.node.weightUnit,
            availableForSale: variant.node.availableForSale,
          })),
        }));

        return {
          success: true,
          products,
          pageInfo: response.data.products.pageInfo,
          pagination: {
            hasNext: response.data.products.pageInfo.hasNextPage,
            hasPrev: response.data.products.pageInfo.hasPreviousPage,
            startCursor: response.data.products.pageInfo.startCursor,
            endCursor: response.data.products.pageInfo.endCursor,
          },
        };
      }

      return {
        success: true,
        products: [],
        pageInfo: null,
        pagination: { hasNext: false, hasPrev: false },
      };
    } catch (error) {
      console.error(
        "Failed to fetch products:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Search products using proper GraphQL search syntax
  async searchProducts(query, limit = 20, cursor = null) {
    try {
      // Dynamic query based on whether cursor is provided
      const searchQuery = cursor
        ? `
          query searchProducts($first: Int!, $query: String!, $after: String!) {
            products(first: $first, query: $query, after: $after) {
              edges {
                cursor
                node {
                  id
                  title
                  handle
                  description
                  productType
                  vendor
                  tags
                  status
                  createdAt
                  updatedAt
                  images(first: 3) {
                    edges {
                      node {
                        id
                        url
                        altText
                        width
                        height
                      }
                    }
                  }
                  variants(first: 5) {
                    edges {
                      node {
                        id
                        title
                        price
                        compareAtPrice
                        sku
                        barcode
                        inventoryQuantity
                        weight
                        weightUnit
                        availableForSale
                      }
                    }
                  }
                }
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        `
        : `
          query searchProducts($first: Int!, $query: String!) {
            products(first: $first, query: $query) {
              edges {
                cursor
                node {
                  id
                  title
                  handle
                  description
                  productType
                  vendor
                  tags
                  status
                  createdAt
                  updatedAt
                  images(first: 3) {
                    edges {
                      node {
                        id
                        url
                        altText
                        width
                        height
                      }
                    }
                  }
                  variants(first: 5) {
                    edges {
                      node {
                        id
                        title
                        price
                        compareAtPrice
                        sku
                        barcode
                        inventoryQuantity
                        weight
                        weightUnit
                        availableForSale
                      }
                    }
                  }
                }
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        `;

      // Format search query for Shopify GraphQL
      // Support searching by title, vendor, product type, or tags
      const formattedQuery = query.trim()
        ? `title:*${query}* OR vendor:*${query}* OR product_type:*${query}* OR tag:*${query}*`
        : "";

      const variables = {
        first: Math.min(limit, 250),
        query: formattedQuery,
      };

      // Add cursor if it exists
      if (cursor) {
        variables.after = cursor;
      }

      const response = await this.graphqlRequest(searchQuery, variables);

      if (response.data?.products) {
        const products = response.data.products.edges.map((edge) => ({
          id: edge.node.id,
          title: edge.node.title,
          handle: edge.node.handle,
          description: edge.node.description,
          productType: edge.node.productType,
          vendor: edge.node.vendor,
          tags: edge.node.tags,
          status: edge.node.status,
          createdAt: edge.node.createdAt,
          updatedAt: edge.node.updatedAt,
          images: edge.node.images.edges.map((img) => ({
            id: img.node.id,
            url: img.node.url,
            altText: img.node.altText,
            width: img.node.width,
            height: img.node.height,
            src: img.node.url, // For compatibility with existing frontend
          })),
          variants: edge.node.variants.edges.map((variant) => ({
            id: variant.node.id,
            title: variant.node.title,
            price: variant.node.price,
            compareAtPrice: variant.node.compareAtPrice,
            sku: variant.node.sku,
            barcode: variant.node.barcode,
            inventoryQuantity: variant.node.inventoryQuantity,
            weight: variant.node.weight,
            weightUnit: variant.node.weightUnit,
            availableForSale: variant.node.availableForSale,
          })),
        }));

        return {
          success: true,
          products,
          pageInfo: response.data.products.pageInfo,
          pagination: {
            hasNext: response.data.products.pageInfo.hasNextPage,
            hasPrev: response.data.products.pageInfo.hasPreviousPage,
            startCursor: response.data.products.pageInfo.startCursor,
            endCursor: response.data.products.pageInfo.endCursor,
          },
        };
      }

      return {
        success: true,
        products: [],
        pageInfo: null,
        pagination: { hasNext: false, hasPrev: false },
      };
    } catch (error) {
      console.error(
        "Failed to search products:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get customers with pagination and search using GraphQL
  async getCustomers(limit = 50, search = "", cursor = null) {
    try {
      const query = `
        query getCustomers($first: Int!, $query: String, $after: String) {
          customers(first: $first, query: $query, after: $after) {
            edges {
              cursor
              node {
                id
                email
                firstName
                lastName
                phone
                displayName
                createdAt
                updatedAt
                numberOfOrders
                amountSpent {
                  amount
                  currencyCode
                }
                tags
                note
                verifiedEmail
                state
                addresses {
                  id
                  firstName
                  lastName
                  address1
                  address2
                  city
                  province
                  country
                  zip
                  phone
                  company
                }
              }
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      `;

      // Format search query for GraphQL if provided
      let formattedQuery = "";
      if (search && search.trim()) {
        const searchTerm = search.trim();
        formattedQuery = `email:*${searchTerm}* OR first_name:*${searchTerm}* OR last_name:*${searchTerm}* OR phone:*${searchTerm}*`;
      }

      const variables = {
        first: Math.min(limit, 250), // GraphQL limit
        query: formattedQuery || null,
      };

      // Only add cursor if it exists
      if (cursor) {
        variables.after = cursor;
      }

      const response = await this.graphqlRequest(query, variables);

      if (response.data?.customers) {
        const customers = response.data.customers.edges.map((edge) => ({
          id: edge.node.id,
          email: edge.node.email,
          firstName: edge.node.firstName,
          lastName: edge.node.lastName,
          phone: edge.node.phone,
          displayName: edge.node.displayName,
          createdAt: edge.node.createdAt,
          updatedAt: edge.node.updatedAt,
          ordersCount: edge.node.numberOfOrders,
          totalSpent: edge.node.amountSpent?.amount || "0.00",
          tags: edge.node.tags,
          note: edge.node.note,
          verifiedEmail: edge.node.verifiedEmail,
          state: edge.node.state,
          addresses: edge.node.addresses || [],
          // For compatibility with existing frontend
          first_name: edge.node.firstName,
          last_name: edge.node.lastName,
          orders_count: edge.node.numberOfOrders,
          total_spent: edge.node.amountSpent?.amount || "0.00",
          verified_email: edge.node.verifiedEmail,
          created_at: edge.node.createdAt,
          updated_at: edge.node.updatedAt,
        }));

        return {
          success: true,
          customers,
          pageInfo: response.data.customers.pageInfo,
          pagination: {
            hasNext: response.data.customers.pageInfo.hasNextPage,
            hasPrev: response.data.customers.pageInfo.hasPreviousPage,
            startCursor: response.data.customers.pageInfo.startCursor,
            endCursor: response.data.customers.pageInfo.endCursor,
            limit,
            total: customers.length,
          },
        };
      }

      return {
        success: true,
        customers: [],
        pageInfo: null,
        pagination: { hasNext: false, hasPrev: false, limit, total: 0 },
      };
    } catch (error) {
      console.error(
        "Failed to get customers:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Create a new customer using GraphQL
  async createCustomer(customerData) {
    try {
      const mutation = `
        mutation customerCreate($input: CustomerInput!) {
          customerCreate(input: $input) {
            customer {
              id
              email
              firstName
              lastName
              phone
              displayName
              createdAt
              updatedAt
              numberOfOrders
              amountSpent {
                amount
                currencyCode
              }
              tags
              note
              verifiedEmail
              state
              addresses {
                id
                firstName
                lastName
                address1
                address2
                city
                province
                country
                zip
                phone
                company
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const customerInput = {
        firstName: customerData.firstName,
        lastName: customerData.lastName,
        email: customerData.email,
        phone: customerData.phone,
        tags: customerData.tags
          ? customerData.tags.split(",").map((tag) => tag.trim())
          : ["POS Customer"],
        note: customerData.note || "Created via Dukalink POS",
      };

      // Add addresses if provided
      if (customerData.addresses && customerData.addresses.length > 0) {
        customerInput.addresses = customerData.addresses.map((addr) => ({
          firstName: addr.firstName || customerData.firstName,
          lastName: addr.lastName || customerData.lastName,
          address1: addr.address1,
          address2: addr.address2,
          city: addr.city,
          province: addr.province,
          country: addr.country || "Kenya",
          zip: addr.zip,
          phone: addr.phone || customerData.phone,
          company: addr.company,
        }));
      }

      const variables = {
        input: customerInput,
      };

      console.log(
        "Creating Shopify customer with GraphQL:",
        JSON.stringify(variables, null, 2)
      );

      const response = await this.graphqlRequest(mutation, variables);

      if (response.data?.customerCreate?.userErrors?.length > 0) {
        const errors = response.data.customerCreate.userErrors
          .map((error) => `${error.field}: ${error.message}`)
          .join(", ");
        return {
          success: false,
          error: errors,
        };
      }

      if (response.data?.customerCreate?.customer) {
        const customer = response.data.customerCreate.customer;
        return {
          success: true,
          customer: {
            id: customer.id,
            email: customer.email,
            firstName: customer.firstName,
            lastName: customer.lastName,
            phone: customer.phone,
            displayName: customer.displayName,
            createdAt: customer.createdAt,
            updatedAt: customer.updatedAt,
            ordersCount: customer.numberOfOrders,
            totalSpent: customer.amountSpent?.amount || "0.00",
            tags: customer.tags,
            note: customer.note,
            verifiedEmail: customer.verifiedEmail,
            state: customer.state,
            addresses: customer.addresses || [],
            // For compatibility with existing frontend
            first_name: customer.firstName,
            last_name: customer.lastName,
            orders_count: customer.numberOfOrders,
            total_spent: customer.amountSpent?.amount || "0.00",
            verified_email: customer.verifiedEmail,
            created_at: customer.createdAt,
            updated_at: customer.updatedAt,
          },
        };
      }

      return {
        success: false,
        error: "Failed to create customer - no data returned",
      };
    } catch (error) {
      console.error(
        "Failed to create customer:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Helper function to normalize IDs (extract numeric ID from GID)
  normalizeId(id) {
    if (typeof id === "string" && id.includes("gid://shopify/")) {
      return id.split("/").pop();
    }
    return id;
  }

  // Create order with proper Shopify structure
  async createOrder(orderData) {
    try {
      // Transform the order data to match Shopify's expected format
      const shopifyOrder = {
        line_items: orderData.lineItems.map((item) => ({
          variant_id: this.normalizeId(item.variantId),
          quantity: item.quantity,
          price: item.price,
          title: item.title,
          sku: item.sku,
          product_id: this.normalizeId(item.productId),
        })),
        location_id: this.normalizeId(orderData.locationId), // Add location specification
        customer: orderData.customer
          ? {
              id: orderData.customer.id,
              email: orderData.customer.email,
              first_name: orderData.customer.firstName,
              last_name: orderData.customer.lastName,
              phone: orderData.customer.phone,
            }
          : undefined,
        billing_address: orderData.billingAddress
          ? {
              first_name: orderData.billingAddress.firstName,
              last_name: orderData.billingAddress.lastName,
              address1: orderData.billingAddress.address1,
              address2: orderData.billingAddress.address2,
              city: orderData.billingAddress.city,
              province: orderData.billingAddress.province,
              country: orderData.billingAddress.country,
              zip: orderData.billingAddress.zip,
              phone: orderData.billingAddress.phone,
            }
          : undefined,
        shipping_address: orderData.shippingAddress
          ? {
              first_name: orderData.shippingAddress.firstName,
              last_name: orderData.shippingAddress.lastName,
              address1: orderData.shippingAddress.address1,
              address2: orderData.shippingAddress.address2,
              city: orderData.shippingAddress.city,
              province: orderData.shippingAddress.province,
              country: orderData.shippingAddress.country,
              zip: orderData.shippingAddress.zip,
              phone: orderData.shippingAddress.phone,
            }
          : undefined,
        email: orderData.email,
        phone: orderData.phone,
        note: orderData.note,
        tags: orderData.tags || "POS",
        source_name: "Dukalink POS",
        financial_status: "paid", // Assuming POS orders are paid
        fulfillment_status: null,
        send_receipt: false, // Don't send automatic receipts
        send_fulfillment_receipt: false,
        inventory_behaviour: "decrement_obeying_policy", // Respect inventory policies and decrement stock
      };

      // Remove undefined fields
      Object.keys(shopifyOrder).forEach((key) => {
        if (shopifyOrder[key] === undefined) {
          delete shopifyOrder[key];
        }
      });

      console.log(
        "Creating Shopify order:",
        JSON.stringify(shopifyOrder, null, 2)
      );

      const response = await axios.post(
        `${this.baseURL}/orders.json`,
        {
          order: shopifyOrder,
        },
        {
          headers: this.getHeaders(),
        }
      );

      return {
        success: true,
        order: response.data.order,
      };
    } catch (error) {
      console.error(
        "Failed to create order:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get orders
  async getOrders(limit = 50, status = "any") {
    try {
      const response = await axios.get(`${this.baseURL}/orders.json`, {
        headers: this.getHeaders(),
        params: {
          limit,
          status,
        },
      });
      return {
        success: true,
        orders: response.data.orders,
      };
    } catch (error) {
      console.error(
        "Failed to fetch orders:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get single order by ID
  async getOrderById(orderId) {
    try {
      // Normalize the order ID (remove gid prefix if present)
      const normalizedOrderId = this.normalizeId(orderId);

      const response = await axios.get(
        `${this.baseURL}/orders/${normalizedOrderId}.json`,
        {
          headers: this.getHeaders(),
        }
      );

      return {
        success: true,
        order: response.data.order,
      };
    } catch (error) {
      console.error(
        "Failed to fetch order:",
        error.response?.data || error.message
      );

      if (error.response?.status === 404) {
        return {
          success: false,
          error: `Order with ID ${orderId} not found`,
        };
      }

      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Update inventory
  async updateInventory(variantId, quantity) {
    try {
      // First get the inventory item ID
      const variantResponse = await axios.get(
        `${this.baseURL}/variants/${variantId}.json`,
        {
          headers: this.getHeaders(),
        }
      );

      const inventoryItemId = variantResponse.data.variant.inventory_item_id;

      // Then update the inventory level
      const response = await axios.post(
        `${this.baseURL}/inventory_levels/set.json`,
        {
          location_id: "primary", // You might need to get the actual location ID
          inventory_item_id: inventoryItemId,
          available: quantity,
        },
        {
          headers: this.getHeaders(),
        }
      );

      return {
        success: true,
        inventory_level: response.data.inventory_level,
      };
    } catch (error) {
      console.error(
        "Failed to update inventory:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // GraphQL request helper
  async graphqlRequest(query, variables = {}) {
    try {
      const response = await axios.post(
        `https://${this.shopDomain}/admin/api/2023-10/graphql.json`,
        {
          query,
          variables,
        },
        {
          headers: {
            "X-Shopify-Access-Token": this.accessToken,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data.errors) {
        throw new Error(response.data.errors[0].message);
      }

      return response.data;
    } catch (error) {
      console.error("GraphQL request error:", error);
      throw error;
    }
  }

  // Staff Management Methods using GraphQL Admin API

  // Get all staff members with commission rates
  async getStaffMembers() {
    try {
      const query = `
        query {
          shop {
            staffMembers(first: 50) {
              edges {
                node {
                  id
                  name
                  email
                  active
                  avatar {
                    url
                  }

                }
              }
            }
          }
        }
      `;

      const response = await this.graphqlRequest(query);

      if (response.data?.shop?.staffMembers) {
        const staffMembers = response.data.shop.staffMembers.edges.map(
          (edge) => ({
            id: edge.node.id,
            name: edge.node.name,
            email: edge.node.email,
            active: edge.node.active,
            avatar: edge.node.avatar?.url,
            commissionRate: 0, // Will be fetched from custom database
          })
        );

        return {
          success: true,
          staffMembers,
        };
      }

      return {
        success: false,
        error: "No staff members found",
      };
    } catch (error) {
      console.error("Get staff members error:", error);
      return {
        success: false,
        error: "Failed to fetch staff members",
      };
    }
  }

  // Note: StaffMember objects do not support metafields in Shopify
  // Commission rates will be stored in custom database instead

  // Location Management Methods

  // Get all locations
  async getLocations() {
    try {
      const response = await axios.get(`${this.baseURL}/locations.json`, {
        headers: this.getHeaders(),
      });

      return {
        success: true,
        locations: response.data.locations.map((location) => ({
          id: location.id,
          name: location.name,
          address: {
            address1: location.address1,
            address2: location.address2,
            city: location.city,
            province: location.province,
            country: location.country,
            zip: location.zip,
            phone: location.phone,
          },
          active: location.active,
          legacy: location.legacy,
          localized_country_name: location.localized_country_name,
          localized_province_name: location.localized_province_name,
        })),
      };
    } catch (error) {
      console.error(
        "Get locations error:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get location-specific inventory levels
  async getLocationInventory(locationId, variantIds) {
    try {
      // Get inventory item IDs for the variants
      const variantPromises = variantIds.map((variantId) =>
        axios.get(`${this.baseURL}/variants/${variantId}.json`, {
          headers: this.getHeaders(),
        })
      );

      const variantResponses = await Promise.all(variantPromises);
      const inventoryItemIds = variantResponses.map(
        (response) => response.data.variant.inventory_item_id
      );

      // Get inventory levels for this location
      const inventoryPromises = inventoryItemIds.map((inventoryItemId) =>
        axios.get(`${this.baseURL}/inventory_levels.json`, {
          headers: this.getHeaders(),
          params: {
            location_ids: locationId,
            inventory_item_ids: inventoryItemId,
          },
        })
      );

      const inventoryResponses = await Promise.all(inventoryPromises);

      const locationInventory = {};
      variantIds.forEach((variantId, index) => {
        const inventoryLevel =
          inventoryResponses[index].data.inventory_levels[0];
        locationInventory[variantId] = {
          available: inventoryLevel ? inventoryLevel.available : 0,
          locationId: locationId,
          inventoryItemId: inventoryItemIds[index],
        };
      });

      return {
        success: true,
        locationInventory,
      };
    } catch (error) {
      console.error(
        "Get location inventory error:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get inventory levels for a single variant across all locations
  async getVariantInventoryAcrossLocations(variantId) {
    try {
      // Get variant to get inventory item ID
      const variantResponse = await axios.get(
        `${this.baseURL}/variants/${variantId}.json`,
        {
          headers: this.getHeaders(),
        }
      );

      const inventoryItemId = variantResponse.data.variant.inventory_item_id;

      // Get inventory levels across all locations
      const response = await axios.get(
        `${this.baseURL}/inventory_levels.json`,
        {
          headers: this.getHeaders(),
          params: {
            inventory_item_ids: inventoryItemId,
          },
        }
      );

      return {
        success: true,
        inventoryLevels: response.data.inventory_levels.map((level) => ({
          locationId: level.location_id,
          available: level.available,
          inventoryItemId: level.inventory_item_id,
        })),
      };
    } catch (error) {
      console.error(
        "Get variant inventory across locations error:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Get staff member by ID with commission rate
  async getStaffMember(staffId) {
    try {
      const query = `
        query staffMember($id: ID!) {
          staffMember(id: $id) {
            id
            name
            email
            active
            avatar {
              url
            }

          }
        }
      `;

      const response = await this.graphqlRequest(query, { id: staffId });

      if (response.data?.staffMember) {
        const staff = response.data.staffMember;
        return {
          success: true,
          staffMember: {
            id: staff.id,
            name: staff.name,
            email: staff.email,

            active: staff.active,
            avatar: staff.avatar?.url,
            commissionRate: 0, // Will be fetched from custom database
          },
        };
      }

      return {
        success: false,
        error: "Staff member not found",
      };
    } catch (error) {
      console.error("Get staff member error:", error);
      return {
        success: false,
        error: "Failed to fetch staff member",
      };
    }
  }

  // Create order with staff attribution
  async createOrderWithStaff(orderData, staffId) {
    try {
      // Get staff member info for attribution
      const staffResult = await this.getStaffMember(staffId);

      // Transform the order data to match Shopify's expected format
      const shopifyOrder = {
        line_items: orderData.lineItems.map((item) => ({
          variant_id: this.normalizeId(item.variantId),
          quantity: item.quantity,
          price: item.price,
          title: item.title,
          sku: item.sku,
          product_id: this.normalizeId(item.productId),
        })),
        customer: orderData.customer
          ? {
              id: orderData.customer.id,
              email: orderData.customer.email,
              first_name: orderData.customer.firstName,
              last_name: orderData.customer.lastName,
              phone: orderData.customer.phone,
            }
          : undefined,
        billing_address: orderData.billingAddress,
        shipping_address: orderData.shippingAddress,
        email: orderData.email,
        phone: orderData.phone,
        note: orderData.note,
        tags: orderData.tags || "POS",
        source_name: "Dukalink POS",
        financial_status: "paid",
        fulfillment_status: null,
        send_receipt: false,
        send_fulfillment_receipt: false,
        inventory_behaviour: "decrement_obeying_policy", // Respect inventory policies and decrement stock
        // Add staff attribution as custom attributes
        note_attributes: [
          {
            name: "sales_agent_id",
            value: staffId,
          },
          {
            name: "sales_agent_name",
            value: staffResult.success
              ? staffResult.staffMember.name
              : "Unknown",
          },
          {
            name: "commission_rate",
            value: staffResult.success
              ? staffResult.staffMember.commissionRate.toString()
              : "0",
          },
          {
            name: "commission_eligible",
            value: "true",
          },
        ],
      };

      // Remove undefined fields
      Object.keys(shopifyOrder).forEach((key) => {
        if (shopifyOrder[key] === undefined) {
          delete shopifyOrder[key];
        }
      });

      console.log(
        "Creating Shopify order with staff attribution:",
        JSON.stringify(shopifyOrder, null, 2)
      );

      const response = await axios.post(
        `${this.baseURL}/orders.json`,
        {
          order: shopifyOrder,
        },
        {
          headers: this.getHeaders(),
        }
      );

      return {
        success: true,
        order: response.data.order,
      };
    } catch (error) {
      console.error(
        "Failed to create order with staff attribution:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Create order with staff attribution and fulfillment integration
  async createOrderWithFulfillment(orderData, staffId, fulfillmentData = null) {
    try {
      // First create the order with staff attribution
      const orderResult = await this.createOrderWithStaff(orderData, staffId);

      if (!orderResult.success) {
        return orderResult;
      }

      const shopifyOrder = orderResult.order;

      // If fulfillment data is provided, create fulfillment record
      if (fulfillmentData) {
        // Import fulfillment service here to avoid circular dependency
        const fulfillmentService = require("./fulfillment-service");

        // Prepare fulfillment data with order information
        const fulfillmentPayload = {
          orderId: `order_${shopifyOrder.id}`, // Custom order ID for our system
          shopifyOrderId: shopifyOrder.id.toString(),
          deliveryAddress:
            fulfillmentData.deliveryAddress || orderData.shippingAddress,
          deliveryContactName:
            fulfillmentData.deliveryContactName ||
            (orderData.customer
              ? `${orderData.customer.firstName} ${orderData.customer.lastName}`
              : ""),
          deliveryContactPhone:
            fulfillmentData.deliveryContactPhone ||
            orderData.customer?.phone ||
            orderData.phone,
          deliveryInstructions: fulfillmentData.deliveryInstructions || "",
          deliveryMethod: fulfillmentData.deliveryMethod || "standard",
          estimatedDeliveryDate: fulfillmentData.estimatedDeliveryDate,
          shippingFee: fulfillmentData.shippingFee || 0,
          shippingFeeCurrency: fulfillmentData.shippingFeeCurrency || "KES",
        };

        const fulfillmentResult = await fulfillmentService.createFulfillment(
          fulfillmentPayload,
          staffId
        );

        if (fulfillmentResult.success) {
          console.log(`✅ Fulfillment created for order ${shopifyOrder.id}`);

          // Add fulfillment info to the order response
          return {
            success: true,
            order: shopifyOrder,
            fulfillment: fulfillmentResult.fulfillment,
          };
        } else {
          console.warn(
            `⚠️ Order created but fulfillment failed: ${fulfillmentResult.error}`
          );
          // Return order success even if fulfillment fails
          return {
            success: true,
            order: shopifyOrder,
            fulfillmentError: fulfillmentResult.error,
          };
        }
      }

      // Return order without fulfillment
      return {
        success: true,
        order: shopifyOrder,
      };
    } catch (error) {
      console.error(
        "Failed to create order with fulfillment:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }

  // Calculate shipping fee and add to order line items
  async calculateAndAddShippingFee(orderData, shippingData) {
    try {
      // Import fulfillment service here to avoid circular dependency
      const fulfillmentService = require("./fulfillment-service");

      // Calculate shipping fee
      const shippingResult = await fulfillmentService.calculateShippingFee(
        shippingData
      );

      if (!shippingResult.success) {
        return {
          success: false,
          error: shippingResult.error,
        };
      }

      // Add shipping fee as a line item if fee > 0
      if (shippingResult.shippingFee > 0) {
        const shippingLineItem = {
          title: `Shipping - ${shippingResult.deliveryMethod}`,
          price: shippingResult.shippingFee.toString(),
          quantity: 1,
          requires_shipping: false,
          taxable: false,
          custom: true,
          properties: [
            {
              name: "Delivery Method",
              value: shippingResult.deliveryMethod,
            },
            {
              name: "Fee Breakdown",
              value: JSON.stringify(shippingResult.breakdown),
            },
          ],
        };

        // Add shipping line item to order
        if (!orderData.lineItems) {
          orderData.lineItems = [];
        }
        orderData.lineItems.push(shippingLineItem);

        // Update order totals
        const shippingFee = parseFloat(shippingResult.shippingFee);
        orderData.subtotalPrice = (
          parseFloat(orderData.subtotalPrice || 0) + shippingFee
        ).toString();
        orderData.totalPrice = (
          parseFloat(orderData.totalPrice || 0) + shippingFee
        ).toString();
      }

      return {
        success: true,
        shippingFee: shippingResult.shippingFee,
        deliveryMethod: shippingResult.deliveryMethod,
        orderData: orderData,
      };
    } catch (error) {
      console.error("Calculate and add shipping fee error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Create order with shipping fee calculation and fulfillment
  async createOrderWithShippingAndFulfillment(
    orderData,
    staffId,
    shippingData = null,
    fulfillmentData = null
  ) {
    try {
      let processedOrderData = { ...orderData };
      let calculatedShippingFee = 0;
      let deliveryMethod = "standard";

      // Calculate and add shipping fee if shipping data provided
      if (shippingData) {
        const shippingResult = await this.calculateAndAddShippingFee(
          processedOrderData,
          shippingData
        );

        if (!shippingResult.success) {
          return {
            success: false,
            error: `Shipping calculation failed: ${shippingResult.error}`,
          };
        }

        processedOrderData = shippingResult.orderData;
        calculatedShippingFee = shippingResult.shippingFee;
        deliveryMethod = shippingResult.deliveryMethod;
      }

      // Prepare fulfillment data with calculated shipping info
      const enhancedFulfillmentData = fulfillmentData
        ? {
            ...fulfillmentData,
            shippingFee: calculatedShippingFee,
            deliveryMethod: fulfillmentData.deliveryMethod || deliveryMethod,
          }
        : null;

      // Create order with fulfillment
      const orderResult = await this.createOrderWithFulfillment(
        processedOrderData,
        staffId,
        enhancedFulfillmentData
      );

      if (orderResult.success) {
        return {
          ...orderResult,
          shippingFee: calculatedShippingFee,
          deliveryMethod: deliveryMethod,
        };
      }

      return orderResult;
    } catch (error) {
      console.error(
        "Failed to create order with shipping and fulfillment:",
        error.response?.data || error.message
      );
      return {
        success: false,
        error: error.response?.data?.errors || error.message,
      };
    }
  }
}

module.exports = new ShopifyService();
