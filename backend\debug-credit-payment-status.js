/**
 * Debug Credit Payment Status
 * 
 * This script helps debug the credit payment status issue by checking
 * the actual status of payment methods in the database.
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function debugCreditPaymentStatus() {
  let connection;
  
  try {
    console.log('🔧 Connecting to database...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'dukalink_pos',
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database');

    // Check table structure first
    console.log('🔍 Checking payment_transactions table structure...');
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM payment_transactions
    `);

    console.log('📋 Table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.Field} (${col.Type})`);
    });

    // Get the most recent payment transaction
    console.log('\n🔍 Finding most recent payment transaction...');
    const [transactions] = await connection.execute(`
      SELECT *
      FROM payment_transactions
      ORDER BY created_at DESC
      LIMIT 3
    `);

    console.log('📊 Recent transactions:');
    transactions.forEach((tx, index) => {
      console.log(`  ${index + 1}. Transaction:`, tx);
    });

    if (transactions.length === 0) {
      console.log('❌ No transactions found');
      return;
    }

    // Check payment_methods_used table structure
    console.log('\n🔍 Checking payment_methods_used table structure...');
    const [methodColumns] = await connection.execute(`
      SHOW COLUMNS FROM payment_methods_used
    `);

    console.log('📋 Payment methods table columns:');
    methodColumns.forEach(col => {
      console.log(`  - ${col.Field} (${col.Type})`);
    });

    // Get payment methods for the most recent transaction
    const latestTransaction = transactions[0];
    console.log(`\n🔍 Checking payment methods for transaction: ${latestTransaction.id}`);

    const [paymentMethods] = await connection.execute(`
      SELECT *
      FROM payment_methods_used
      WHERE transaction_id = ?
      ORDER BY created_at DESC
    `, [latestTransaction.id]);

    console.log('💳 Payment methods:');
    paymentMethods.forEach((method, index) => {
      console.log(`  ${index + 1}. ID: ${method.id}`);
      console.log(`     Type: ${method.method_type}`);
      console.log(`     Name: ${method.method_name}`);
      console.log(`     Amount: ${method.amount}`);
      console.log(`     Status: ${method.status}`);
      console.log(`     Processed At: ${method.processed_at || 'Not processed'}`);
      console.log(`     Error: ${method.error_message || 'None'}`);
      if (method.metadata) {
        try {
          const metadata = JSON.parse(method.metadata);
          console.log(`     Metadata: ${JSON.stringify(metadata, null, 6)}`);
        } catch (e) {
          console.log(`     Metadata: ${method.metadata}`);
        }
      }
      console.log('');
    });

    // Check for credit payments specifically
    const creditMethods = paymentMethods.filter(m => m.method_type === 'credit');
    if (creditMethods.length > 0) {
      console.log('🏦 Credit payment analysis:');
      creditMethods.forEach((method, index) => {
        console.log(`  Credit Payment ${index + 1}:`);
        console.log(`    Status: ${method.status}`);
        console.log(`    Should be: "authorized" for credit payments`);
        console.log(`    Is Correct: ${method.status === 'authorized' ? '✅ YES' : '❌ NO'}`);
      });
    }

    // Check if there are any pending methods
    const pendingMethods = paymentMethods.filter(m => m.status === 'pending');
    if (pendingMethods.length > 0) {
      console.log(`\n⚠️ Found ${pendingMethods.length} pending payment methods:`);
      pendingMethods.forEach((method, index) => {
        console.log(`  ${index + 1}. ${method.method_type} - ${method.method_name} (${method.amount})`);
      });
      console.log('   This would prevent Shopify order creation!');
    } else {
      console.log('\n✅ No pending payment methods found');
    }

  } catch (error) {
    console.error('❌ Error debugging credit payment status:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the debug script
if (require.main === module) {
  debugCreditPaymentStatus()
    .then(() => {
      console.log('✅ Debug completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Debug failed:', error.message);
      process.exit(1);
    });
}

module.exports = { debugCreditPaymentStatus };
