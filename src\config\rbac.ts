/**
 * Role-Based Access Control (RBAC) Configuration
 *
 * This file defines the complete RBAC system for the Dukalink POS application.
 * It matches the backend implementation and provides a centralized configuration
 * for roles, permissions, and UI access control.
 */

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface Permission {
  id: string;
  name: string;
  description: string;
  category: PermissionCategory;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  level: number; // Higher number = more privileges
}

export interface ScreenAccess {
  screenName: string;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  allowedRoles?: string[];
  deniedRoles?: string[];
}

export interface UIFeature {
  featureId: string;
  name: string;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  allowedRoles?: string[];
}

export type PermissionCategory =
  | "products"
  | "orders"
  | "customers"
  | "inventory"
  | "staff"
  | "reports"
  | "system"
  | "payments"
  | "discounts"
  | "analytics"
  | "loyalty";

export type UserRole = "cashier" | "manager" | "super_admin";

// =====================================================
// PERMISSIONS CONFIGURATION
// =====================================================

export const PERMISSIONS: Record<string, Permission> = {
  // Product Management
  read_products: {
    id: "read_products",
    name: "Read Products",
    description: "View product catalog and details",
    category: "products",
  },
  manage_products: {
    id: "manage_products",
    name: "Manage Products",
    description: "Create, update, and delete products",
    category: "products",
  },

  // Order Management
  create_orders: {
    id: "create_orders",
    name: "Create Orders",
    description: "Create new sales orders",
    category: "orders",
  },
  view_orders: {
    id: "view_orders",
    name: "View Orders",
    description: "View order history and details",
    category: "orders",
  },
  manage_orders: {
    id: "manage_orders",
    name: "Manage Orders",
    description: "Edit, cancel, and refund orders",
    category: "orders",
  },

  // Customer Management
  read_customers: {
    id: "read_customers",
    name: "Read Customers",
    description: "View customer information",
    category: "customers",
  },
  manage_customers: {
    id: "manage_customers",
    name: "Manage Customers",
    description: "Create, update, and delete customers",
    category: "customers",
  },

  // Inventory Management
  view_inventory: {
    id: "view_inventory",
    name: "View Inventory",
    description: "View inventory levels and stock information",
    category: "inventory",
  },
  manage_inventory: {
    id: "manage_inventory",
    name: "Manage Inventory",
    description: "Update inventory levels and manage stock",
    category: "inventory",
  },

  // Staff Management
  view_staff: {
    id: "view_staff",
    name: "View Staff",
    description: "View staff member information",
    category: "staff",
  },
  manage_staff: {
    id: "manage_staff",
    name: "Manage Staff",
    description: "Create, update, and manage staff members",
    category: "staff",
  },
  view_staff_performance: {
    id: "view_staff_performance",
    name: "View Staff Performance",
    description: "View staff performance metrics and reports",
    category: "staff",
  },

  // Reports and Analytics
  view_reports: {
    id: "view_reports",
    name: "View Reports",
    description: "Access sales and performance reports",
    category: "reports",
  },
  view_analytics: {
    id: "view_analytics",
    name: "View Analytics",
    description: "Access advanced analytics and insights",
    category: "analytics",
  },

  // Payment Processing
  process_payments: {
    id: "process_payments",
    name: "Process Payments",
    description: "Process customer payments and transactions",
    category: "payments",
  },

  // Discount Management
  apply_discounts: {
    id: "apply_discounts",
    name: "Apply Discounts",
    description: "Apply discounts to orders",
    category: "discounts",
  },
  manage_discounts: {
    id: "manage_discounts",
    name: "Manage Discounts",
    description: "Create and manage discount rules",
    category: "discounts",
  },

  // System Administration
  manage_system: {
    id: "manage_system",
    name: "Manage System",
    description: "System configuration and administration",
    category: "system",
  },
  manage_roles: {
    id: "manage_roles",
    name: "Manage Roles",
    description: "Create and modify user roles",
    category: "system",
  },
  manage_permissions: {
    id: "manage_permissions",
    name: "Manage Permissions",
    description: "Assign and revoke permissions",
    category: "system",
  },
  view_audit_logs: {
    id: "view_audit_logs",
    name: "View Audit Logs",
    description: "Access system audit logs and activity",
    category: "system",
  },
  manage_integrations: {
    id: "manage_integrations",
    name: "Manage Integrations",
    description: "Configure external integrations",
    category: "system",
  },

  // Loyalty System
  view_loyalty: {
    id: "view_loyalty",
    name: "View Loyalty",
    description: "View customer loyalty information and points",
    category: "loyalty",
  },
  manage_loyalty: {
    id: "manage_loyalty",
    name: "Manage Loyalty",
    description: "Manage customer loyalty points, tiers, and redemptions",
    category: "loyalty",
  },
  configure_loyalty: {
    id: "configure_loyalty",
    name: "Configure Loyalty",
    description: "Configure loyalty system settings and rules",
    category: "loyalty",
  },
  view_loyalty_analytics: {
    id: "view_loyalty_analytics",
    name: "View Loyalty Analytics",
    description: "Access loyalty system analytics and reports",
    category: "loyalty",
  },

  // Fulfillment Management
  view_fulfillments: {
    id: "view_fulfillments",
    name: "View Fulfillments",
    description: "View order fulfillment information and status",
    category: "fulfillment",
  },
  create_fulfillments: {
    id: "create_fulfillments",
    name: "Create Fulfillments",
    description: "Create new fulfillment records for orders",
    category: "fulfillment",
  },
  manage_fulfillments: {
    id: "manage_fulfillments",
    name: "Manage Fulfillments",
    description: "Update fulfillment status and delivery details",
    category: "fulfillment",
  },
  view_shipping_rates: {
    id: "view_shipping_rates",
    name: "View Shipping Rates",
    description: "View shipping rates and delivery methods",
    category: "fulfillment",
  },
  manage_shipping_rates: {
    id: "manage_shipping_rates",
    name: "Manage Shipping Rates",
    description: "Create and update shipping rates and delivery methods",
    category: "fulfillment",
  },
  calculate_shipping_fees: {
    id: "calculate_shipping_fees",
    name: "Calculate Shipping Fees",
    description: "Calculate shipping fees for orders",
    category: "fulfillment",
  },
  view_fulfillment_reports: {
    id: "view_fulfillment_reports",
    name: "View Fulfillment Reports",
    description: "Access fulfillment statistics and reports",
    category: "fulfillment",
  },
  manage_delivery_details: {
    id: "manage_delivery_details",
    name: "Manage Delivery Details",
    description: "Update delivery addresses and contact information",
    category: "fulfillment",
  },
};

// =====================================================
// ROLES CONFIGURATION
// =====================================================

export const ROLES: Record<UserRole, Role> = {
  cashier: {
    id: "cashier",
    name: "Cashier",
    description: "Basic POS operations and customer service",
    level: 1,
    permissions: [
      "read_products",
      "create_orders",
      "read_customers",
      "process_payments",
      "apply_discounts",
      "view_inventory",
      "view_loyalty",
      "view_fulfillments",
      "create_fulfillments",
      "manage_delivery_details",
      "view_shipping_rates",
      "calculate_shipping_fees",
    ],
  },
  manager: {
    id: "manager",
    name: "Manager",
    description: "Store management and staff supervision",
    level: 2,
    permissions: [
      "read_products",
      "create_orders",
      "view_orders",
      "manage_orders",
      "read_customers",
      "manage_customers",
      "process_payments",
      "view_inventory",
      "manage_inventory",
      "view_staff",
      "manage_staff",
      "view_staff_performance",
      "view_reports",
      "apply_discounts",
      "manage_discounts",
      "view_analytics",
      "view_loyalty",
      "manage_loyalty",
      "view_loyalty_analytics",
      "view_fulfillments",
      "create_fulfillments",
      "manage_fulfillments",
      "manage_delivery_details",
      "view_shipping_rates",
      "manage_shipping_rates",
      "calculate_shipping_fees",
      "view_fulfillment_reports",
    ],
  },
  super_admin: {
    id: "super_admin",
    name: "Super Admin",
    description: "Full system access and administration",
    level: 3,
    permissions: [
      "read_products",
      "manage_products",
      "create_orders",
      "view_orders",
      "manage_orders",
      "read_customers",
      "manage_customers",
      "process_payments",
      "view_inventory",
      "manage_inventory",
      "view_staff",
      "manage_staff",
      "view_staff_performance",
      "view_reports",
      "view_analytics",
      "apply_discounts",
      "manage_discounts",
      "manage_system",
      "manage_roles",
      "manage_permissions",
      "view_audit_logs",
      "manage_integrations",
      "view_loyalty",
      "manage_loyalty",
      "configure_loyalty",
      "view_loyalty_analytics",
      "view_fulfillments",
      "create_fulfillments",
      "manage_fulfillments",
      "manage_delivery_details",
      "view_shipping_rates",
      "manage_shipping_rates",
      "calculate_shipping_fees",
      "view_fulfillment_reports",
    ],
  },
};

// =====================================================
// SCREEN ACCESS CONFIGURATION
// =====================================================

export const SCREEN_ACCESS: ScreenAccess[] = [
  // Main Dashboard - All authenticated users
  {
    screenName: "(tabs)/index",
    allowedRoles: ["cashier", "manager", "super_admin"],
  },

  // Products - All authenticated users
  {
    screenName: "(tabs)/products",
    requiredPermissions: ["read_products"],
  },

  // Cart and Checkout - All authenticated users
  {
    screenName: "(tabs)/cart",
    requiredPermissions: ["create_orders"],
  },
  {
    screenName: "checkout",
    requiredPermissions: ["create_orders", "process_payments"],
  },

  // Orders - View access for all, manage for higher roles
  {
    screenName: "(tabs)/orders",
    requiredPermissions: ["view_orders"],
  },

  // Customer Management
  {
    screenName: "customer-list",
    requiredPermissions: ["read_customers"],
  },
  {
    screenName: "customer-details",
    requiredPermissions: ["read_customers"],
  },

  // Sales Agent Management
  {
    screenName: "sales-agent-list",
    allowedRoles: ["cashier", "manager", "super_admin"],
  },
  {
    screenName: "sales-agent-details",
    requiredPermissions: ["view_staff"],
  },

  // Staff Management - Manager and Super Admin only
  {
    screenName: "staff-management",
    requiredPermissions: ["manage_staff"],
  },
  {
    screenName: "staff-list",
    requiredPermissions: ["view_staff"],
  },
  {
    screenName: "staff-create",
    requiredPermissions: ["manage_staff"],
  },
  {
    screenName: "staff-edit",
    requiredPermissions: ["manage_staff"],
  },

  // Reports and Analytics
  {
    screenName: "reports",
    requiredPermissions: ["view_reports"],
  },
  {
    screenName: "analytics",
    requiredPermissions: ["view_analytics"],
  },

  // System Settings - Super Admin only
  {
    screenName: "system-settings",
    requiredRoles: ["super_admin"],
  },
  {
    screenName: "user-management",
    requiredPermissions: ["manage_roles", "manage_permissions"],
  },

  // Settings - Basic settings for all, advanced for higher roles
  {
    screenName: "settings",
    allowedRoles: ["cashier", "manager", "super_admin"],
  },

  // Loyalty Management - View loyalty for cashiers, manage for managers+
  {
    screenName: "loyalty-management",
    requiredPermissions: ["view_loyalty"],
  },

  // Discount Management - Apply discounts for cashiers, manage for managers+
  {
    screenName: "discount-management",
    requiredPermissions: ["apply_discounts"],
  },

  // Fulfillment Management - View fulfillments for all staff, manage for managers+
  {
    screenName: "fulfillment-management",
    requiredPermissions: ["view_fulfillments"],
  },
  {
    screenName: "fulfillment-details",
    requiredPermissions: ["view_fulfillments"],
  },
  {
    screenName: "shipping-rates-management",
    requiredPermissions: ["manage_shipping_rates"],
  },
];

// =====================================================
// UI FEATURES CONFIGURATION
// =====================================================

export const UI_FEATURES: UIFeature[] = [
  // Dashboard Features
  {
    featureId: "dashboard_staff_management",
    name: "Staff Management Dashboard Card",
    requiredPermissions: ["view_staff"],
  },
  {
    featureId: "dashboard_reports",
    name: "Reports Dashboard Card",
    requiredPermissions: ["view_reports"],
  },
  {
    featureId: "dashboard_analytics",
    name: "Analytics Dashboard Card",
    requiredPermissions: ["view_analytics"],
  },
  {
    featureId: "dashboard_system_settings",
    name: "System Settings Dashboard Card",
    requiredRoles: ["super_admin"],
  },
  {
    featureId: "dashboard_fulfillment_management",
    name: "Fulfillment Management Dashboard Card",
    requiredPermissions: ["view_fulfillments"],
  },

  // Order Features
  {
    featureId: "order_refund_button",
    name: "Order Refund Button",
    requiredPermissions: ["manage_orders"],
  },
  {
    featureId: "order_edit_button",
    name: "Order Edit Button",
    requiredPermissions: ["manage_orders"],
  },

  // Customer Features
  {
    featureId: "customer_create_button",
    name: "Create Customer Button",
    requiredPermissions: ["manage_customers"],
  },
  {
    featureId: "customer_edit_button",
    name: "Edit Customer Button",
    requiredPermissions: ["manage_customers"],
  },
  {
    featureId: "customer_delete_button",
    name: "Delete Customer Button",
    requiredPermissions: ["manage_customers"],
  },

  // Sales Agent Features
  {
    featureId: "sales_agent_create_button",
    name: "Create Sales Agent Button",
    requiredPermissions: ["manage_staff"],
  },
  {
    featureId: "sales_agent_edit_button",
    name: "Edit Sales Agent Button",
    requiredPermissions: ["manage_staff"],
  },
  {
    featureId: "sales_agent_performance_view",
    name: "Sales Agent Performance View",
    requiredPermissions: ["view_staff_performance"],
  },

  // Staff Features
  {
    featureId: "staff_create_button",
    name: "Create Staff Button",
    requiredPermissions: ["manage_staff"],
  },
  {
    featureId: "staff_edit_button",
    name: "Edit Staff Button",
    requiredPermissions: ["manage_staff"],
  },
  {
    featureId: "staff_permissions_edit",
    name: "Edit Staff Permissions",
    requiredPermissions: ["manage_permissions"],
  },
  {
    featureId: "staff_role_edit",
    name: "Edit Staff Role",
    requiredPermissions: ["manage_roles"],
  },

  // Inventory Features
  {
    featureId: "inventory_adjust_button",
    name: "Inventory Adjustment Button",
    requiredPermissions: ["manage_inventory"],
  },
  {
    featureId: "inventory_bulk_update",
    name: "Bulk Inventory Update",
    requiredPermissions: ["manage_inventory"],
  },

  // Discount Features
  {
    featureId: "discount_create_button",
    name: "Create Discount Button",
    requiredPermissions: ["manage_discounts"],
  },
  {
    featureId: "discount_edit_button",
    name: "Edit Discount Button",
    requiredPermissions: ["manage_discounts"],
  },

  // System Features
  {
    featureId: "system_backup_button",
    name: "System Backup Button",
    requiredRoles: ["super_admin"],
  },
  {
    featureId: "integration_settings",
    name: "Integration Settings",
    requiredPermissions: ["manage_integrations"],
  },
  {
    featureId: "audit_logs_view",
    name: "Audit Logs View",
    requiredPermissions: ["view_audit_logs"],
  },

  // Fulfillment Features
  {
    featureId: "fulfillment_create_button",
    name: "Create Fulfillment Button",
    requiredPermissions: ["create_fulfillments"],
  },
  {
    featureId: "fulfillment_edit_button",
    name: "Edit Fulfillment Button",
    requiredPermissions: ["manage_fulfillments"],
  },
  {
    featureId: "fulfillment_status_update",
    name: "Update Fulfillment Status",
    requiredPermissions: ["manage_fulfillments"],
  },
  {
    featureId: "delivery_details_edit",
    name: "Edit Delivery Details",
    requiredPermissions: ["manage_delivery_details"],
  },
  {
    featureId: "shipping_rates_create",
    name: "Create Shipping Rates",
    requiredPermissions: ["manage_shipping_rates"],
  },
  {
    featureId: "shipping_rates_edit",
    name: "Edit Shipping Rates",
    requiredPermissions: ["manage_shipping_rates"],
  },
  {
    featureId: "shipping_fee_calculator",
    name: "Shipping Fee Calculator",
    requiredPermissions: ["calculate_shipping_fees"],
  },
  {
    featureId: "fulfillment_reports_view",
    name: "Fulfillment Reports View",
    requiredPermissions: ["view_fulfillment_reports"],
  },
];

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Check if a user has a specific permission
 */
export const hasPermission = (
  userPermissions: string[],
  permission: string
): boolean => {
  return userPermissions.includes(permission);
};

/**
 * Check if a user has any of the specified permissions
 */
export const hasAnyPermission = (
  userPermissions: string[],
  permissions: string[]
): boolean => {
  return permissions.some((permission) => userPermissions.includes(permission));
};

/**
 * Check if a user has all of the specified permissions
 */
export const hasAllPermissions = (
  userPermissions: string[],
  permissions: string[]
): boolean => {
  return permissions.every((permission) =>
    userPermissions.includes(permission)
  );
};

/**
 * Check if a user has a specific role
 */
export const hasRole = (userRole: string, role: string): boolean => {
  return userRole === role;
};

/**
 * Check if a user has any of the specified roles
 */
export const hasAnyRole = (userRole: string, roles: string[]): boolean => {
  return roles.includes(userRole);
};

/**
 * Check if a user can access a specific screen
 */
export const canAccessScreen = (
  userRole: string,
  userPermissions: string[],
  screenName: string
): boolean => {
  const screenAccess = SCREEN_ACCESS.find(
    (access) => access.screenName === screenName
  );

  if (!screenAccess) {
    // If no specific access rules, allow access
    return true;
  }

  // Check denied roles first
  if (
    screenAccess.deniedRoles &&
    hasAnyRole(userRole, screenAccess.deniedRoles)
  ) {
    return false;
  }

  // Check allowed roles
  if (
    screenAccess.allowedRoles &&
    !hasAnyRole(userRole, screenAccess.allowedRoles)
  ) {
    return false;
  }

  // Check required roles
  if (
    screenAccess.requiredRoles &&
    !hasAnyRole(userRole, screenAccess.requiredRoles)
  ) {
    return false;
  }

  // Check required permissions
  if (
    screenAccess.requiredPermissions &&
    !hasAllPermissions(userPermissions, screenAccess.requiredPermissions)
  ) {
    return false;
  }

  return true;
};

/**
 * Check if a user can access a specific UI feature
 */
export const canAccessFeature = (
  userRole: string,
  userPermissions: string[],
  featureId: string
): boolean => {
  const feature = UI_FEATURES.find((f) => f.featureId === featureId);

  if (!feature) {
    // If feature not found, deny access by default
    return false;
  }

  // Check required roles
  if (feature.requiredRoles && !hasAnyRole(userRole, feature.requiredRoles)) {
    return false;
  }

  // Check allowed roles
  if (feature.allowedRoles && !hasAnyRole(userRole, feature.allowedRoles)) {
    return false;
  }

  // Check required permissions
  if (
    feature.requiredPermissions &&
    !hasAllPermissions(userPermissions, feature.requiredPermissions)
  ) {
    return false;
  }

  return true;
};

/**
 * Get all permissions for a specific role
 */
export const getRolePermissions = (role: UserRole): string[] => {
  return ROLES[role]?.permissions || [];
};

/**
 * Get role level for comparison
 */
export const getRoleLevel = (role: UserRole): number => {
  return ROLES[role]?.level || 0;
};

/**
 * Check if user role has higher or equal level than required role
 */
export const hasRoleLevel = (
  userRole: UserRole,
  requiredRole: UserRole
): boolean => {
  return getRoleLevel(userRole) >= getRoleLevel(requiredRole);
};

/**
 * Get roles that a user can assign to others
 * Users can only assign roles below their level (except super_admin can assign any role)
 */
export const getAssignableRoles = (userRole: UserRole): UserRole[] => {
  const userLevel = getRoleLevel(userRole);

  if (userRole === "super_admin") {
    // Super admin can assign any role
    return ["cashier", "manager", "super_admin"];
  }

  // Other users can only assign roles below their level
  return Object.entries(ROLES)
    .filter(([_, role]) => role.level < userLevel)
    .map(([roleKey, _]) => roleKey as UserRole);
};

/**
 * Check if a user can assign a specific role to another user
 */
export const canAssignRole = (
  userRole: UserRole,
  targetRole: UserRole
): boolean => {
  const assignableRoles = getAssignableRoles(userRole);
  return assignableRoles.includes(targetRole);
};

/**
 * Get permissions by category
 */
export const getPermissionsByCategory = (
  category: PermissionCategory
): Permission[] => {
  return Object.values(PERMISSIONS).filter(
    (permission) => permission.category === category
  );
};

/**
 * Get all available roles
 */
export const getAllRoles = (): Role[] => {
  return Object.values(ROLES);
};

/**
 * Get all available permissions
 */
export const getAllPermissions = (): Permission[] => {
  return Object.values(PERMISSIONS);
};

/**
 * Validate if a permission exists
 */
export const isValidPermission = (permission: string): boolean => {
  return permission in PERMISSIONS;
};

/**
 * Validate if a role exists
 */
export const isValidRole = (role: string): boolean => {
  return role in ROLES;
};
