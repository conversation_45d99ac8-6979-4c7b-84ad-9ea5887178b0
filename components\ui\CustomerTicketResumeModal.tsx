/**
 * Customer Ticket Resume Modal
 *
 * Shows existing customer tickets and allows user to resume, merge, or continue with current cart.
 */

import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  StyleSheet,
} from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { IconSymbol } from "./IconSymbol";
import { ModernButton } from "./ModernButton";
import { ModernCard } from "./ModernCard";
import { formatCurrency } from "@/src/utils/currencyUtils";
import {
  CustomerTicket,
  TicketResumeAction,
} from "@/src/types/customerTickets";
import { Customer, CartItem } from "@/src/types/shopify";

interface CustomerTicketResumeModalProps {
  visible: boolean;
  onClose: () => void;
  customer: Customer;
  existingTickets: CustomerTicket[];
  currentCartItems: CartItem[];
  onAction: (action: TicketResumeAction) => void;
  isLoading?: boolean;
}

export const CustomerTicketResumeModal: React.FC<
  CustomerTicketResumeModalProps
> = ({
  visible,
  onClose,
  customer,
  existingTickets,
  currentCartItems,
  onAction,
  isLoading = false,
}) => {
  const theme = useTheme();
  const [selectedAction, setSelectedAction] = useState<string | null>(null);

  const customerName =
    customer.displayName ||
    `${customer.firstName} ${customer.lastName}`.trim() ||
    "Unknown Customer";

  const currentCartTotal = currentCartItems.reduce(
    (total, item) => total + parseFloat(item.price) * item.quantity,
    0
  );

  const handleAction = (
    type: "resume" | "merge" | "continue",
    ticketId?: string
  ) => {
    setSelectedAction(`${type}-${ticketId || "current"}`);
    onAction({
      type,
      ticketId,
      customer,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const renderTicketCard = (ticket: CustomerTicket) => (
    <ModernCard key={ticket.id} style={styles.ticketCard} variant="outlined">
      <View style={styles.ticketHeader}>
        <View style={styles.ticketInfo}>
          <Text style={[styles.ticketName, { color: theme.colors.text }]}>
            {ticket.name}
          </Text>
          <Text
            style={[styles.ticketDate, { color: theme.colors.textSecondary }]}
          >
            {formatDate(ticket.updated_at)}
          </Text>
        </View>
        <View style={styles.ticketStats}>
          <Text style={[styles.ticketTotal, { color: theme.colors.primary }]}>
            {formatCurrency(ticket.total)}
          </Text>
          <Text
            style={[styles.ticketItems, { color: theme.colors.textSecondary }]}
          >
            {ticket.item_count} item{ticket.item_count !== 1 ? "s" : ""}
          </Text>
        </View>
      </View>

      {/* Item Preview */}
      {ticket.items.length > 0 && (
        <View style={styles.itemPreview}>
          {ticket.items.slice(0, 2).map((item, index) => (
            <Text
              key={index}
              style={[styles.itemText, { color: theme.colors.textSecondary }]}
              numberOfLines={1}
            >
              {item.quantity}x {item.title}
            </Text>
          ))}
          {ticket.items.length > 2 && (
            <Text
              style={[styles.moreItems, { color: theme.colors.textSecondary }]}
            >
              +{ticket.items.length - 2} more items
            </Text>
          )}
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.ticketActions}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.resumeButton,
            { borderColor: theme.colors.primary },
            selectedAction === `resume-${ticket.id}` && { opacity: 0.6 },
          ]}
          onPress={() => handleAction("resume", ticket.id)}
          disabled={isLoading}
        >
          {selectedAction === `resume-${ticket.id}` && isLoading ? (
            <ActivityIndicator size="small" color={theme.colors.primary} />
          ) : (
            <>
              <IconSymbol
                name="arrow.clockwise"
                size={16}
                color={theme.colors.primary}
              />
              <Text
                style={[
                  styles.actionButtonText,
                  { color: theme.colors.primary },
                ]}
              >
                Resume
              </Text>
            </>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.mergeButton,
            { borderColor: theme.colors.success },
            selectedAction === `merge-${ticket.id}` && { opacity: 0.6 },
          ]}
          onPress={() => handleAction("merge", ticket.id)}
          disabled={isLoading}
        >
          {selectedAction === `merge-${ticket.id}` && isLoading ? (
            <ActivityIndicator size="small" color={theme.colors.success} />
          ) : (
            <>
              <IconSymbol
                name="plus.circle"
                size={16}
                color={theme.colors.success}
              />
              <Text
                style={[
                  styles.actionButtonText,
                  { color: theme.colors.success },
                ]}
              >
                Merge
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </ModernCard>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        {/* Header */}
        <View
          style={[styles.header, { borderBottomColor: theme.colors.border }]}
        >
          <View style={styles.headerContent}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Resume Cart
            </Text>
            <Text
              style={[styles.subtitle, { color: theme.colors.textSecondary }]}
            >
              {customerName} has {existingTickets.length} saved cart
              {existingTickets.length !== 1 ? "s" : ""}
            </Text>
          </View>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView 
          style={styles.content} 
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          {/* Current Cart Info */}
          {currentCartItems.length > 0 && (
            <ModernCard style={styles.currentCartCard} variant="elevated">
              <View style={styles.currentCartHeader}>
                <Text
                  style={[
                    styles.currentCartTitle,
                    { color: theme.colors.text },
                  ]}
                >
                  Current Cart
                </Text>
                <Text
                  style={[
                    styles.currentCartTotal,
                    { color: theme.colors.primary },
                  ]}
                >
                  {formatCurrency(currentCartTotal)}
                </Text>
              </View>
              <Text
                style={[
                  styles.currentCartItems,
                  { color: theme.colors.textSecondary },
                ]}
              >
                {currentCartItems.length} item
                {currentCartItems.length !== 1 ? "s" : ""}
              </Text>
            </ModernCard>
          )}

          {/* Existing Tickets */}
          <View style={styles.ticketsSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Saved Carts
            </Text>
            {existingTickets.map(renderTicketCard)}
          </View>
        </ScrollView>

        {/* Bottom Actions */}
        <View
          style={[
            styles.bottomActions,
            { borderTopColor: theme.colors.border },
          ]}
        >
          <ModernButton
            title="Continue with Current Cart"
            onPress={() => handleAction("continue")}
            variant="outline"
            size="lg"
            loading={selectedAction === "continue-current" && isLoading}
            disabled={isLoading}
            style={styles.continueButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  currentCartCard: {
    marginBottom: 24,
  },
  currentCartHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  currentCartTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  currentCartTotal: {
    fontSize: 18,
    fontWeight: "700",
  },
  currentCartItems: {
    fontSize: 14,
  },
  ticketsSection: {
    gap: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  ticketCard: {
    padding: 16,
  },
  ticketHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  ticketInfo: {
    flex: 1,
  },
  ticketName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  ticketDate: {
    fontSize: 12,
  },
  ticketStats: {
    alignItems: "flex-end",
  },
  ticketTotal: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 2,
  },
  ticketItems: {
    fontSize: 12,
  },
  itemPreview: {
    marginBottom: 16,
    gap: 2,
  },
  itemText: {
    fontSize: 14,
  },
  moreItems: {
    fontSize: 12,
    fontStyle: "italic",
  },
  ticketActions: {
    flexDirection: "row",
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    gap: 6,
  },
  resumeButton: {
    backgroundColor: "transparent",
  },
  mergeButton: {
    backgroundColor: "transparent",
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  bottomActions: {
    padding: 16,
    borderTopWidth: 1,
  },
  continueButton: {
    width: "100%",
  },
});
