import { useThemeColor } from "@/hooks/useThemeColor";
import { useCustomer } from "@/src/contexts/CustomerContext";
import { useSalesAgent } from "@/src/contexts/SalesAgentContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { Customer } from "@/src/types/shopify";
import { Ionicons } from "@expo/vector-icons";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Modal,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

// Define SalesAgent type locally since it's defined in the context
interface SalesAgent {
  id: string;
  name: string;
  email: string;
  phone?: string;
  territory: string;
  region?: string;
  commissionRate: number;
  active: boolean;
  customerCount?: number;
  totalSales?: number;
  totalCommission?: number;
  joinDate?: string;
}

interface CheckoutStepperModalProps {
  visible: boolean;
  onClose: () => void;
  onComplete: (customer: Customer, salesAgent: SalesAgent) => void;
  onAddCustomer: () => void;
}

const CheckoutStepperModal: React.FC<CheckoutStepperModalProps> = ({
  visible,
  onClose,
  onComplete,
  onAddCustomer,
}) => {
  // Theme system with safety checks
  const theme = useTheme();
  const backgroundColor = useThemeColor({}, "background") || "#000000";
  const textColor = useThemeColor({}, "text") || "#ffffff";
  const textSecondary = useThemeColor({}, "textSecondary") || "#9ca3af";
  const primaryColor = useThemeColor({}, "primary") || "#d2686f";
  const surfaceColor = useThemeColor({}, "surface") || "#1c1a17";

  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [selectedSalesAgent, setSelectedSalesAgent] =
    useState<SalesAgent | null>(null);

  // Customer data and search
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerSearchQuery, setCustomerSearchQuery] = useState("");
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false);
  const [customerError, setCustomerError] = useState<string | null>(null);

  // Sales agent data and search
  const [salesAgents, setSalesAgents] = useState<SalesAgent[]>([]);
  const [salesAgentSearchQuery, setSalesAgentSearchQuery] = useState("");
  const [isLoadingSalesAgents, setIsLoadingSalesAgents] = useState(false);
  const [salesAgentError, setSalesAgentError] = useState<string | null>(null);

  const { selectedCustomer: contextCustomer } = useCustomer();
  const { selectedAgent: contextAgent } = useSalesAgent();

  // Load customers with search
  const loadCustomers = useCallback(async (search: string = "") => {
    try {
      setIsLoadingCustomers(true);
      setCustomerError(null);
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomers({
        limit: 50,
        search: search.trim(),
      });

      if (response.success && response.data) {
        const customersWithDisplayName = response.data.customers.map(
          (customer) => ({
            ...customer,
            displayName:
              customer.displayName ||
              `${customer.firstName || ""} ${customer.lastName || ""}`.trim() ||
              "Unknown Customer",
          })
        );
        setCustomers(customersWithDisplayName);
      } else {
        setCustomerError(response.error || "Failed to load customers");
        setCustomers([]);
      }
    } catch (error: any) {
      setCustomerError(error.message || "Network error");
      setCustomers([]);
    } finally {
      setIsLoadingCustomers(false);
    }
  }, []);

  // Load sales agents with search
  const loadSalesAgents = useCallback(async (search: string = "") => {
    try {
      setIsLoadingSalesAgents(true);
      setSalesAgentError(null);
      const apiClient = getAPIClient();
      const response = await apiClient.getSalesAgents();

      if (response.success && response.data) {
        let agents = response.data.salesAgents || [];

        // Filter by search query if provided
        if (search.trim()) {
          const searchLower = search.toLowerCase();
          agents = agents.filter(
            (agent) =>
              agent &&
              agent.active &&
              ((agent.name && agent.name.toLowerCase().includes(searchLower)) ||
                (agent.email &&
                  agent.email.toLowerCase().includes(searchLower)) ||
                (agent.territory &&
                  agent.territory.toLowerCase().includes(searchLower)))
          );
        } else {
          // Only show active agents
          agents = agents.filter((agent) => agent && agent.active);
        }

        setSalesAgents(agents);
      } else {
        setSalesAgentError(response.error || "Failed to load sales agents");
        setSalesAgents([]);
      }
    } catch (error: any) {
      setSalesAgentError(error.message || "Network error");
      setSalesAgents([]);
    } finally {
      setIsLoadingSalesAgents(false);
    }
  }, []);

  // Reset modal state when it opens
  useEffect(() => {
    if (visible) {
      setCurrentStep(1);

      // Convert SelectedCustomer to Customer if available
      if (contextCustomer) {
        const customerAsCustomer: Customer = {
          id: contextCustomer.id,
          firstName: contextCustomer.firstName,
          lastName: contextCustomer.lastName,
          email: contextCustomer.email,
          phone: contextCustomer.phone,
          displayName: contextCustomer.displayName,
          ordersCount: contextCustomer.ordersCount || 0,
          totalSpent: contextCustomer.totalSpent || "0.00",
          // Add missing Customer properties with defaults
          addresses: [],
          tags: [],
          createdAt: "",
          updatedAt: "",
        };
        setSelectedCustomer(customerAsCustomer);
      } else {
        setSelectedCustomer(null);
      }

      setSelectedSalesAgent(contextAgent || null);
      setCustomerSearchQuery("");
      setSalesAgentSearchQuery("");

      // Load initial data
      loadCustomers();
      loadSalesAgents();
    }
  }, [visible, contextCustomer, contextAgent, loadCustomers, loadSalesAgents]);

  // Debounced search for customers
  useEffect(() => {
    if (!visible || currentStep !== 1) return;

    const timeoutId = setTimeout(() => {
      loadCustomers(customerSearchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [customerSearchQuery, visible, currentStep, loadCustomers]);

  // Debounced search for sales agents
  useEffect(() => {
    if (!visible || currentStep !== 2) return;

    const timeoutId = setTimeout(() => {
      loadSalesAgents(salesAgentSearchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [salesAgentSearchQuery, visible, currentStep, loadSalesAgents]);

  const handleNext = () => {
    if (currentStep === 1) {
      if (!selectedCustomer) {
        Alert.alert("Error", "Please select a customer");
        return;
      }
      setCurrentStep(2);
    } else if (currentStep === 2) {
      if (!selectedSalesAgent) {
        Alert.alert("Error", "Please select a sales agent");
        return;
      }
      setCurrentStep(3);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    if (selectedCustomer && selectedSalesAgent) {
      onComplete(selectedCustomer, selectedSalesAgent);
      onClose();
    }
  };

  const renderCustomerStep = () => {
    const renderCustomerItem = ({ item: customer }: { item: Customer }) => (
      <TouchableOpacity
        style={[
          styles.listItem,
          {
            backgroundColor: surfaceColor,
            borderColor:
              selectedCustomer?.id === customer.id
                ? primaryColor
                : theme.colors?.border || "#374151",
            borderWidth: selectedCustomer?.id === customer.id ? 2 : 1,
          },
        ]}
        onPress={() => setSelectedCustomer(customer)}
      >
        <View style={styles.customerInfo}>
          <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
            <Text style={styles.avatarText}>
              {(
                customer.displayName ||
                `${customer.firstName || ""} ${
                  customer.lastName || ""
                }`.trim() ||
                "Unknown"
              )
                .split(" ")
                .map((n) => n.charAt(0))
                .join("")
                .slice(0, 2) || "?"}
            </Text>
          </View>
          <View style={styles.customerDetails}>
            <Text style={[styles.customerName, { color: textColor }]}>
              {customer.displayName ||
                `${customer.firstName || ""} ${
                  customer.lastName || ""
                }`.trim() ||
                "Unknown Customer"}
            </Text>
            {customer.email && (
              <Text style={[styles.customerEmail, { color: textSecondary }]}>
                {customer.email}
              </Text>
            )}
            {customer.phone && (
              <Text style={[styles.customerPhone, { color: textSecondary }]}>
                {customer.phone}
              </Text>
            )}
            <View style={styles.customerStats}>
              <Text style={[styles.customerStat, { color: textSecondary }]}>
                {`${
                  typeof customer.ordersCount === "string"
                    ? parseInt(customer.ordersCount) || 0
                    : customer.ordersCount || 0
                } orders`}
              </Text>
              {customer.totalSpent != null && customer.totalSpent !== "" && (
                <Text style={[styles.customerStat, { color: textSecondary }]}>
                  {`• KES ${customer.totalSpent}`}
                </Text>
              )}
            </View>
          </View>
        </View>
        {selectedCustomer?.id === customer.id && (
          <Ionicons name="checkmark-circle" size={24} color={primaryColor} />
        )}
      </TouchableOpacity>
    );

    return (
      <View style={[styles.stepContent, { backgroundColor }]}>
        <Text style={[styles.stepTitle, { color: textColor }]}>
          Select Customer
        </Text>

        <View
          style={[
            styles.searchContainer,
            {
              backgroundColor: surfaceColor,
              borderColor: theme.colors?.border || "#374151",
            },
          ]}
        >
          <Ionicons
            name="search"
            size={20}
            color={textSecondary}
            style={styles.searchIcon}
          />
          <TextInput
            style={[styles.searchInput, { color: textColor }]}
            placeholder="Search customers..."
            placeholderTextColor={textSecondary}
            value={customerSearchQuery}
            onChangeText={setCustomerSearchQuery}
          />
        </View>

        <TouchableOpacity
          style={[
            styles.addButton,
            { backgroundColor: surfaceColor, borderColor: primaryColor },
          ]}
          onPress={onAddCustomer}
        >
          <Ionicons name="add" size={20} color={primaryColor} />
          <Text style={[styles.addButtonText, { color: primaryColor }]}>
            Add New Customer
          </Text>
        </TouchableOpacity>

        {isLoadingCustomers ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.loadingText, { color: textColor }]}>
              Loading customers...
            </Text>
          </View>
        ) : customerError ? (
          <View style={styles.errorContainer}>
            <Text
              style={[
                styles.errorText,
                { color: theme.colors?.error || "#ef4444" },
              ]}
            >
              {customerError}
            </Text>
          </View>
        ) : customers.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="person-add" size={64} color={textSecondary} />
            <Text style={[styles.emptyTitle, { color: textColor }]}>
              {customerSearchQuery ? "No customers found" : "No customers yet"}
            </Text>
            <Text style={[styles.emptySubtitle, { color: textSecondary }]}>
              {customerSearchQuery
                ? "Try a different search term"
                : "Add your first customer to get started"}
            </Text>
          </View>
        ) : (
          <FlatList
            data={customers}
            renderItem={renderCustomerItem}
            keyExtractor={(item) => item.id}
            style={styles.listContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />
        )}
      </View>
    );
  };

  const renderSalesAgentStep = () => {
    const renderSalesAgentItem = ({ item: agent }: { item: SalesAgent }) => (
      <TouchableOpacity
        style={[
          styles.listItem,
          {
            backgroundColor: surfaceColor,
            borderColor:
              selectedSalesAgent?.id === agent.id
                ? primaryColor
                : theme.colors?.border || "#374151",
            borderWidth: selectedSalesAgent?.id === agent.id ? 2 : 1,
          },
        ]}
        onPress={() => setSelectedSalesAgent(agent)}
      >
        <View style={styles.agentInfo}>
          <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
            <Text style={styles.avatarText}>
              {(agent.name || "Unknown")
                .split(" ")
                .map((n) => n.charAt(0))
                .join("")
                .slice(0, 2)}
            </Text>
          </View>
          <View style={styles.agentDetails}>
            <Text style={[styles.agentName, { color: textColor }]}>
              {agent.name || "Unknown Agent"}
            </Text>
            {agent.email && (
              <Text style={[styles.agentEmail, { color: textSecondary }]}>
                {agent.email}
              </Text>
            )}
            {agent.territory && (
              <Text style={[styles.agentTerritory, { color: textSecondary }]}>
                {agent.territory}
              </Text>
            )}
            <View style={styles.agentStats}>
              <Text style={[styles.agentStat, { color: textSecondary }]}>
                {agent.commissionRate || 0}% commission
              </Text>
              {agent.customerCount != null && (
                <Text style={[styles.agentStat, { color: textSecondary }]}>
                  • {agent.customerCount} customers
                </Text>
              )}
            </View>
          </View>
        </View>
        {selectedSalesAgent?.id === agent.id && (
          <Ionicons name="checkmark-circle" size={24} color={primaryColor} />
        )}
      </TouchableOpacity>
    );

    return (
      <View style={[styles.stepContent, { backgroundColor }]}>
        <Text style={[styles.stepTitle, { color: textColor }]}>
          Select Sales Agent
        </Text>

        <View
          style={[
            styles.searchContainer,
            {
              backgroundColor: surfaceColor,
              borderColor: theme.colors?.border || "#374151",
            },
          ]}
        >
          <Ionicons
            name="search"
            size={20}
            color={textSecondary}
            style={styles.searchIcon}
          />
          <TextInput
            style={[styles.searchInput, { color: textColor }]}
            placeholder="Search sales agents..."
            placeholderTextColor={textSecondary}
            value={salesAgentSearchQuery}
            onChangeText={setSalesAgentSearchQuery}
          />
        </View>

        {isLoadingSalesAgents ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.loadingText, { color: textColor }]}>
              Loading sales agents...
            </Text>
          </View>
        ) : salesAgentError ? (
          <View style={styles.errorContainer}>
            <Text
              style={[
                styles.errorText,
                { color: theme.colors?.error || "#ef4444" },
              ]}
            >
              {salesAgentError}
            </Text>
          </View>
        ) : salesAgents.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="person-add" size={64} color={textSecondary} />
            <Text style={[styles.emptyTitle, { color: textColor }]}>
              {salesAgentSearchQuery
                ? "No sales agents found"
                : "No sales agents available"}
            </Text>
            <Text style={[styles.emptySubtitle, { color: textSecondary }]}>
              {salesAgentSearchQuery
                ? "Try a different search term"
                : "Contact your administrator to add sales agents"}
            </Text>
          </View>
        ) : (
          <FlatList
            data={salesAgents}
            renderItem={renderSalesAgentItem}
            keyExtractor={(item) => item.id}
            style={styles.listContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />
        )}
      </View>
    );
  };

  const renderConfirmStep = () => (
    <View style={[styles.stepContent, { backgroundColor }]}>
      <Text style={[styles.stepTitle, { color: textColor }]}>
        Confirm Selection
      </Text>

      <View
        style={[
          styles.confirmationContainer,
          { backgroundColor: surfaceColor },
        ]}
      >
        <View style={styles.confirmationItem}>
          <Text style={[styles.confirmationLabel, { color: textSecondary }]}>
            Customer:
          </Text>
          <Text style={[styles.confirmationValue, { color: textColor }]}>
            {selectedCustomer?.displayName ||
              `${selectedCustomer?.firstName || ""} ${
                selectedCustomer?.lastName || ""
              }`.trim() ||
              "Unknown Customer"}
          </Text>
          {selectedCustomer?.email && (
            <Text
              style={[styles.confirmationSubValue, { color: textSecondary }]}
            >
              {selectedCustomer.email}
            </Text>
          )}
          {selectedCustomer?.phone && (
            <Text
              style={[styles.confirmationSubValue, { color: textSecondary }]}
            >
              {selectedCustomer.phone}
            </Text>
          )}
        </View>

        <View style={styles.confirmationItem}>
          <Text style={[styles.confirmationLabel, { color: textSecondary }]}>
            Sales Agent:
          </Text>
          <Text style={[styles.confirmationValue, { color: textColor }]}>
            {selectedSalesAgent?.name || "Unknown Sales Agent"}
          </Text>
          {selectedSalesAgent?.email && (
            <Text
              style={[styles.confirmationSubValue, { color: textSecondary }]}
            >
              {selectedSalesAgent.email}
            </Text>
          )}
          {selectedSalesAgent?.territory && (
            <Text
              style={[styles.confirmationSubValue, { color: textSecondary }]}
            >
              {selectedSalesAgent.territory}
            </Text>
          )}
        </View>
      </View>
    </View>
  );

  // Early return if theme is not properly loaded (after all hooks)
  if (!theme || !theme.colors) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={[styles.container, { backgroundColor }]}>
        <View
          style={[
            styles.header,
            {
              backgroundColor: surfaceColor,
              borderBottomColor: theme.colors?.border || "#374151",
            },
          ]}
        >
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={textColor} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: textColor }]}>
            Checkout Setup
          </Text>
          <View style={styles.placeholder} />
        </View>

        <View style={[styles.stepIndicator, { backgroundColor: surfaceColor }]}>
          {[1, 2, 3].map((step) => (
            <View key={step} style={styles.stepContainer}>
              <View
                style={[
                  styles.stepCircle,
                  {
                    backgroundColor:
                      currentStep >= step
                        ? primaryColor
                        : theme.colors?.border || "#374151",
                  },
                ]}
              >
                <Text
                  style={[
                    styles.stepNumber,
                    { color: currentStep >= step ? "#fff" : textSecondary },
                  ]}
                >
                  {step}
                </Text>
              </View>
              <Text style={[styles.stepLabel, { color: textSecondary }]}>
                {step === 1
                  ? "Customer"
                  : step === 2
                  ? "Sales Agent"
                  : "Confirm"}
              </Text>
              {step < 3 && (
                <View
                  style={[
                    styles.stepConnector,
                    { backgroundColor: theme.colors?.border || "#374151" },
                  ]}
                />
              )}
            </View>
          ))}
        </View>

        <View style={[styles.content, { backgroundColor }]}>
          {currentStep === 1 && renderCustomerStep()}
          {currentStep === 2 && renderSalesAgentStep()}
          {currentStep === 3 && renderConfirmStep()}
        </View>

        <View
          style={[
            styles.footer,
            {
              backgroundColor: surfaceColor,
              borderTopColor: theme.colors?.border || "#374151",
            },
          ]}
        >
          {currentStep > 1 && (
            <TouchableOpacity
              style={[
                styles.backButton,
                { borderColor: theme.colors?.border || "#374151" },
              ]}
              onPress={handleBack}
            >
              <Text style={[styles.backButtonText, { color: textSecondary }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.nextButton,
              { backgroundColor: primaryColor },
              ((currentStep === 1 && !selectedCustomer) ||
                (currentStep === 2 && !selectedSalesAgent)) && {
                backgroundColor: theme.colors.border,
              },
            ]}
            onPress={currentStep === 3 ? handleComplete : handleNext}
            disabled={
              (currentStep === 1 && !selectedCustomer) ||
              (currentStep === 2 && !selectedSalesAgent)
            }
          >
            <Text style={[styles.nextButtonText, { color: "#fff" }]}>
              {currentStep === 3 ? "Go to Payment" : "Next"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
  },
  placeholder: {
    width: 24,
  },
  stepIndicator: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  stepContainer: {
    alignItems: "center",
    flex: 1,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
  },
  stepLabel: {
    fontSize: 12,
    textAlign: "center",
    fontFamily: "Poppins_500Medium",
  },
  stepConnector: {
    position: "absolute",
    top: 16,
    left: "50%",
    width: "100%",
    height: 2,
    zIndex: -1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
  },
  addButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "Poppins_500Medium",
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 16,
  },
  listItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  // Customer specific styles
  customerInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  customerDetails: {
    flex: 1,
    marginLeft: 12,
  },
  customerName: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
    marginBottom: 2,
  },
  customerEmail: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    marginBottom: 2,
  },
  customerPhone: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    marginBottom: 4,
  },
  customerStats: {
    flexDirection: "row",
    alignItems: "center",
  },
  customerStat: {
    fontSize: 12,
    fontFamily: "Poppins_400Regular",
  },
  // Sales agent specific styles
  agentInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  agentDetails: {
    flex: 1,
    marginLeft: 12,
  },
  agentName: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
    marginBottom: 2,
  },
  agentEmail: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    marginBottom: 2,
  },
  agentTerritory: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    marginBottom: 4,
  },
  agentStats: {
    flexDirection: "row",
    alignItems: "center",
  },
  agentStat: {
    fontSize: 12,
    fontFamily: "Poppins_400Regular",
  },
  // Avatar styles
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  avatarText: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
    color: "#fff",
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
  },
  errorText: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    textAlign: "center",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    textAlign: "center",
    paddingHorizontal: 32,
  },
  confirmationContainer: {
    borderRadius: 8,
    padding: 16,
  },
  confirmationItem: {
    marginBottom: 20,
  },
  confirmationLabel: {
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    marginBottom: 4,
  },
  confirmationValue: {
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "Poppins_500Medium",
  },
  confirmationSubValue: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    marginTop: 2,
  },
  footer: {
    flexDirection: "row",
    padding: 16,
    borderTopWidth: 1,
  },
  backButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Poppins_500Medium",
  },
  nextButton: {
    flex: 2,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    marginLeft: 8,
  },
  nextButtonText: {
    fontSize: 14,
    fontWeight: "600",
    fontFamily: "Poppins_600SemiBold",
  },
});

export default CheckoutStepperModal;
