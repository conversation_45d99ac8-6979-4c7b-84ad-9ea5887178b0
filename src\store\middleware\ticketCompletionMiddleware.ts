/**
 * Ticket Completion Middleware
 *
 * Automatically handles ticket completion based on payment processing
 * and other business events in a professional POS environment.
 */

import { Middleware } from "@reduxjs/toolkit";
import { RootState } from "../index";
import {
  completeActiveTicket,
  updateTicketStatus,
  selectActiveTicket,
  selectActiveTicketId,
} from "../slices/ticketSlice";
import { clearCart } from "../slices/cartSlice";
import { clearCartFromStorage } from "../../utils/cartPersistence";

export interface TicketCompletionConfig {
  autoCompleteOnPayment: boolean;
  autoArchiveAfterDays: number;
  requireConfirmationForLargeOrders: boolean;
  largeOrderThreshold: number;
}

const DEFAULT_CONFIG: TicketCompletionConfig = {
  autoCompleteOnPayment: true,
  autoArchiveAfterDays: 30,
  requireConfirmationForLargeOrders: true,
  largeOrderThreshold: 5000, // KES
};

export const ticketCompletionMiddleware: Middleware<{}, RootState> =
  (store) => (next) => (action) => {
    const result = next(action);
    const state = store.getState();

    // Handle successful payment completion - ONLY for final order completion
    // NOT for intermediate payment processing steps
    if (
      action.type === "orders/finalOrderCompletion" ||
      action.type === "checkout/completeOrderFlow"
    ) {
      const activeTicketId = selectActiveTicketId(state);
      const activeTicket = selectActiveTicket(state);

      if (activeTicketId && activeTicket && activeTicket.status === "active") {
        // Complete the ticket automatically
        store.dispatch(completeActiveTicket());

        // Clear the cart since order is complete
        store.dispatch(clearCart());

        // CRITICAL: Clear localStorage after successful sale
        clearCartFromStorage();

        console.log(
          `✅ Ticket ${activeTicketId} automatically completed after final order completion`
        );
        console.log("🗑️ Cart localStorage cleared after successful sale");
      }
    }

    // REMOVED: Premature cart clearing on payment/processPaymentSuccess and orders/createOrderSuccess
    // These actions happen during checkout process, not at final completion

    // Handle payment failures
    if (action.type === "payment/processPaymentFailure") {
      const activeTicket = selectActiveTicket(state);

      if (activeTicket) {
        // Keep ticket active but log the failure
        console.log(
          `❌ Payment failed for ticket ${activeTicket.id}, keeping ticket active`
        );

        // Could implement retry logic or pause ticket here
        // store.dispatch(updateTicketStatus({
        //   ticketId: activeTicket.id,
        //   status: 'paused'
        // }));
      }
    }

    // Handle order cancellation
    if (action.type === "orders/cancelOrder") {
      const activeTicketId = selectActiveTicketId(state);

      if (activeTicketId) {
        store.dispatch(
          updateTicketStatus({
            ticketId: activeTicketId,
            status: "cancelled",
          })
        );

        // Clear cart for cancelled orders
        store.dispatch(clearCart());

        console.log(`🚫 Ticket ${activeTicketId} cancelled`);
      }
    }

    // Handle system recovery scenarios
    if (action.type === "app/systemRecovery") {
      // Implement recovery logic for interrupted transactions
      const activeTicket = selectActiveTicket(state);

      if (activeTicket && activeTicket.items?.length > 0) {
        console.log(
          `🔄 System recovery: Found active ticket ${activeTicket.id} with items`
        );

        // Could implement automatic recovery or prompt user
        // This would depend on business requirements
      }
    }

    return result;
  };

/**
 * Professional Ticket Completion Rules
 */
export const TICKET_COMPLETION_RULES = {
  // Automatic completion triggers
  AUTO_COMPLETE_ON_PAYMENT_SUCCESS: true,
  AUTO_COMPLETE_ON_ORDER_CREATION: true,

  // Manual completion scenarios
  MANUAL_COMPLETION_REQUIRED: [
    "large_orders_above_threshold",
    "special_customer_requests",
    "manager_approval_needed",
  ],

  // Status transition rules
  VALID_TRANSITIONS: {
    active: ["paused", "completed", "cancelled"],
    paused: ["active", "completed", "cancelled"],
    completed: ["archived"],
    cancelled: ["archived"],
    archived: [], // Terminal state
  },

  // Edge case handling
  EDGE_CASES: {
    PAYMENT_FAILURE: "keep_active",
    PARTIAL_PAYMENT: "keep_active_with_note",
    SYSTEM_CRASH: "auto_recover_on_restart",
    NETWORK_FAILURE: "queue_for_retry",
  },

  // Data retention
  DATA_RETENTION: {
    COMPLETED_TICKETS_DAYS: 30,
    CANCELLED_TICKETS_DAYS: 7,
    ARCHIVED_TICKETS_YEARS: 7, // For compliance
  },
} as const;

/**
 * Ticket Completion Utilities
 */
export class TicketCompletionManager {
  static validateStatusTransition(
    currentStatus: string,
    newStatus: string
  ): boolean {
    const validTransitions =
      TICKET_COMPLETION_RULES.VALID_TRANSITIONS[
        currentStatus as keyof typeof TICKET_COMPLETION_RULES.VALID_TRANSITIONS
      ];

    return validTransitions?.includes(newStatus) || false;
  }

  static shouldAutoComplete(ticket: any, paymentResult: any): boolean {
    // Check if auto-completion is enabled
    if (!TICKET_COMPLETION_RULES.AUTO_COMPLETE_ON_PAYMENT_SUCCESS) {
      return false;
    }

    // Check if payment was successful
    if (!paymentResult?.success) {
      return false;
    }

    // Check if ticket is in valid state for completion
    if (ticket?.status !== "active") {
      return false;
    }

    // Check for large order threshold
    if (
      ticket?.total > DEFAULT_CONFIG.largeOrderThreshold &&
      DEFAULT_CONFIG.requireConfirmationForLargeOrders
    ) {
      return false; // Require manual confirmation
    }

    return true;
  }

  static getRecommendedAction(ticket: any, context: any): string {
    if (!ticket) return "create_new_ticket";

    switch (ticket.status) {
      case "active":
        if (ticket.items?.length === 0) return "add_items";
        return "process_payment";

      case "paused":
        return "resume_or_complete";

      case "completed":
        return "view_receipt_or_archive";

      case "cancelled":
        return "archive_or_duplicate";

      default:
        return "review_ticket";
    }
  }
}
