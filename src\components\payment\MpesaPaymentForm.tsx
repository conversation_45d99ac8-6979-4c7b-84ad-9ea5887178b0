import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Colors } from '@/constants/Colors';

interface MpesaPaymentFormProps {
  amount: number;
  onPaymentInitiated: (phoneNumber: string) => void;
  onCancel: () => void;
  isProcessing?: boolean;
}

export const MpesaPaymentForm: React.FC<MpesaPaymentFormProps> = ({
  amount,
  onPaymentInitiated,
  onCancel,
  isProcessing = false,
}) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isValidPhone, setIsValidPhone] = useState(false);

  // Theme colors
  const backgroundColor = useThemeColor({}, 'background');
  const surfaceColor = useThemeColor({}, 'surface');
  const textColor = useThemeColor({}, 'text');
  const textSecondary = useThemeColor({}, 'textSecondary');
  const primaryColor = useThemeColor({}, 'primary');
  const errorColor = useThemeColor({}, 'error');

  // Validate Kenyan phone number
  const validatePhoneNumber = (phone: string): boolean => {
    // Remove any spaces, dashes, or plus signs
    const cleanPhone = phone.replace(/[\s\-\+]/g, '');
    
    // Check for valid Kenyan phone number formats
    const kenyanPhoneRegex = /^(254|0)?[17]\d{8}$/;
    return kenyanPhoneRegex.test(cleanPhone);
  };

  const formatPhoneNumber = (phone: string): string => {
    // Remove any non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Convert to international format (254...)
    if (cleanPhone.startsWith('0')) {
      return '254' + cleanPhone.substring(1);
    } else if (cleanPhone.startsWith('254')) {
      return cleanPhone;
    } else if (cleanPhone.length === 9) {
      return '254' + cleanPhone;
    }
    
    return cleanPhone;
  };

  const handlePhoneChange = (text: string) => {
    setPhoneNumber(text);
    setIsValidPhone(validatePhoneNumber(text));
  };

  const handleInitiatePayment = () => {
    if (!isValidPhone) {
      Alert.alert(
        'Invalid Phone Number',
        'Please enter a valid Kenyan phone number (e.g., 0712345678 or 254712345678)'
      );
      return;
    }

    const formattedPhone = formatPhoneNumber(phoneNumber);
    onPaymentInitiated(formattedPhone);
  };

  return (
    <View style={[styles.container, { backgroundColor: surfaceColor }]}>
      <View style={styles.header}>
        <Ionicons name="phone" size={32} color={primaryColor} />
        <Text style={[styles.title, { color: textColor }]}>M-Pesa Payment</Text>
        <Text style={[styles.amount, { color: primaryColor }]}>
          KSh {amount.toLocaleString()}
        </Text>
      </View>

      <View style={styles.form}>
        <Text style={[styles.label, { color: textColor }]}>
          Enter M-Pesa Phone Number
        </Text>
        
        <View style={styles.phoneInputContainer}>
          <View style={[styles.countryCode, { backgroundColor: backgroundColor }]}>
            <Text style={[styles.countryCodeText, { color: textSecondary }]}>
              +254
            </Text>
          </View>
          
          <TextInput
            style={[
              styles.phoneInput,
              { 
                backgroundColor: backgroundColor,
                color: textColor,
                borderColor: isValidPhone ? primaryColor : Colors.light.border,
              }
            ]}
            value={phoneNumber}
            onChangeText={handlePhoneChange}
            placeholder="712345678"
            placeholderTextColor={textSecondary}
            keyboardType="phone-pad"
            maxLength={15}
            editable={!isProcessing}
          />
          
          {isValidPhone && (
            <Ionicons 
              name="checkmark-circle" 
              size={24} 
              color={primaryColor} 
              style={styles.validIcon}
            />
          )}
        </View>

        <Text style={[styles.helpText, { color: textSecondary }]}>
          Enter your M-Pesa registered phone number. You'll receive a payment prompt on your phone.
        </Text>

        <View style={styles.instructions}>
          <Text style={[styles.instructionTitle, { color: textColor }]}>
            Payment Instructions:
          </Text>
          <Text style={[styles.instructionText, { color: textSecondary }]}>
            1. Enter your M-Pesa phone number above
          </Text>
          <Text style={[styles.instructionText, { color: textSecondary }]}>
            2. Tap "Send Payment Request"
          </Text>
          <Text style={[styles.instructionText, { color: textSecondary }]}>
            3. Check your phone for M-Pesa prompt
          </Text>
          <Text style={[styles.instructionText, { color: textSecondary }]}>
            4. Enter your M-Pesa PIN to complete payment
          </Text>
        </View>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity
          style={[styles.cancelButton, { borderColor: Colors.light.border }]}
          onPress={onCancel}
          disabled={isProcessing}
        >
          <Text style={[styles.cancelButtonText, { color: textSecondary }]}>
            Cancel
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.payButton,
            { 
              backgroundColor: isValidPhone && !isProcessing ? primaryColor : Colors.light.border,
            }
          ]}
          onPress={handleInitiatePayment}
          disabled={!isValidPhone || isProcessing}
        >
          {isProcessing ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text style={styles.payButtonText}>
              Send Payment Request
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 12,
    margin: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 4,
  },
  amount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  form: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  countryCode: {
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  countryCodeText: {
    fontSize: 16,
    fontWeight: '500',
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
  },
  validIcon: {
    position: 'absolute',
    right: 12,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  instructions: {
    marginTop: 8,
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  payButton: {
    flex: 2,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  payButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
