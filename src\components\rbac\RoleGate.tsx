import React from 'react';
import { useSession } from '../../contexts/AuthContext';
import { hasRole, hasAnyRole, hasRoleLevel, UserRole } from '../../config/rbac';

interface RoleGateProps {
  children: React.ReactNode;
  role?: UserRole;
  roles?: UserRole[];
  minLevel?: UserRole; // Minimum role level required
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * RoleGate Component
 * 
 * Conditionally renders children based on user roles.
 * Integrates with AuthContext and RBAC configuration.
 * 
 * @param children - Content to render if user has required role
 * @param role - Single role to check
 * @param roles - Array of roles to check (user needs ANY of these roles)
 * @param minLevel - Minimum role level required (hierarchical check)
 * @param fallback - Content to render if user doesn't have role
 * @param showFallback - Whether to show fallback content or nothing
 */
export const RoleGate: React.FC<RoleGateProps> = ({
  children,
  role,
  roles,
  minLevel,
  fallback = null,
  showFallback = false,
}) => {
  const { user, isPosAuthenticated } = useSession();

  // If not authenticated, don't show anything
  if (!isPosAuthenticated || !user) {
    return showFallback ? <>{fallback}</> : null;
  }

  const userRole = user.role as UserRole;
  let hasAccess = false;

  if (role) {
    // Single role check
    hasAccess = hasRole(userRole, role);
  } else if (roles && roles.length > 0) {
    // Multiple roles check (user needs ANY of these roles)
    hasAccess = hasAnyRole(userRole, roles);
  } else if (minLevel) {
    // Hierarchical role level check
    hasAccess = hasRoleLevel(userRole, minLevel);
  } else {
    // No roles specified, allow access
    hasAccess = true;
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

// Convenience components for specific roles
export const CashierOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <RoleGate role="cashier" fallback={fallback}>
    {children}
  </RoleGate>
);

export const ManagerOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <RoleGate role="manager" fallback={fallback}>
    {children}
  </RoleGate>
);

export const SuperAdminOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <RoleGate role="super_admin" fallback={fallback}>
    {children}
  </RoleGate>
);

// Convenience components for role levels
export const ManagerOrAbove: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <RoleGate minLevel="manager" fallback={fallback}>
    {children}
  </RoleGate>
);

export const SuperAdminOrAbove: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <RoleGate minLevel="super_admin" fallback={fallback}>
    {children}
  </RoleGate>
);

// Convenience components for multiple roles
export const ManagerOrSuperAdmin: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <RoleGate roles={["manager", "super_admin"]} fallback={fallback}>
    {children}
  </RoleGate>
);

export const AllRoles: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <RoleGate roles={["cashier", "manager", "super_admin"]} fallback={fallback}>
    {children}
  </RoleGate>
);

// Higher-order component for role-based rendering
export const withRole = <P extends object>(
  Component: React.ComponentType<P>,
  role: UserRole,
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <RoleGate role={role} fallback={fallback}>
      <Component {...props} />
    </RoleGate>
  );
};

// Higher-order component for multiple roles
export const withRoles = <P extends object>(
  Component: React.ComponentType<P>,
  roles: UserRole[],
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <RoleGate roles={roles} fallback={fallback}>
      <Component {...props} />
    </RoleGate>
  );
};

// Higher-order component for minimum role level
export const withMinRoleLevel = <P extends object>(
  Component: React.ComponentType<P>,
  minLevel: UserRole,
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <RoleGate minLevel={minLevel} fallback={fallback}>
      <Component {...props} />
    </RoleGate>
  );
};
