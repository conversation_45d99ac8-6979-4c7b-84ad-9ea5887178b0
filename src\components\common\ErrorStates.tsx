import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useThemeColor } from '@/src/hooks/useThemeColor';
import { Typography, Spacing, Colors } from '@/src/constants/Styles';
import { IconSymbol } from '@/src/components/ui/IconSymbol';
import { ModernButton } from '@/src/components/ui/ModernButton';
import { AppError, ErrorHandler } from '@/src/utils/error-handler';

interface ErrorMessageProps {
  error: string | AppError;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  style,
  textStyle,
}) => {
  const errorColor = useThemeColor({}, 'error');
  const message = typeof error === 'string' ? error : error.message;

  return (
    <View style={[styles.errorMessage, style]}>
      <IconSymbol name="exclamationmark.triangle" size={16} color={errorColor} />
      <Text style={[styles.errorText, { color: errorColor }, textStyle]}>
        {message}
      </Text>
    </View>
  );
};

interface ErrorBannerProps {
  error: string | AppError;
  onDismiss?: () => void;
  style?: ViewStyle;
}

export const ErrorBanner: React.FC<ErrorBannerProps> = ({
  error,
  onDismiss,
  style,
}) => {
  const errorColor = useThemeColor({}, 'error');
  const backgroundColor = useThemeColor({}, 'background');
  const message = typeof error === 'string' ? error : error.message;

  return (
    <View style={[styles.errorBanner, { backgroundColor: errorColor + '20' }, style]}>
      <View style={styles.errorBannerContent}>
        <IconSymbol name="exclamationmark.triangle" size={20} color={errorColor} />
        <Text style={[styles.errorBannerText, { color: errorColor }]}>
          {message}
        </Text>
      </View>
      {onDismiss && (
        <ModernButton
          title=""
          onPress={onDismiss}
          variant="ghost"
          size="sm"
          icon={<IconSymbol name="xmark" size={16} color={errorColor} />}
        />
      )}
    </View>
  );
};

interface ErrorStateProps {
  error: string | AppError;
  onRetry?: () => void;
  retryText?: string;
  icon?: string;
  title?: string;
  style?: ViewStyle;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  error,
  onRetry,
  retryText = 'Try Again',
  icon = 'exclamationmark.triangle',
  title = 'Something went wrong',
  style,
}) => {
  const textColor = useThemeColor({}, 'text');
  const textSecondary = useThemeColor({}, 'textSecondary');
  const errorColor = useThemeColor({}, 'error');
  const message = typeof error === 'string' ? error : error.message;

  return (
    <View style={[styles.errorState, style]}>
      <IconSymbol name={icon} size={64} color={errorColor} />
      <Text style={[styles.errorTitle, { color: textColor }]}>
        {title}
      </Text>
      <Text style={[styles.errorDescription, { color: textSecondary }]}>
        {message}
      </Text>
      {onRetry && (
        <ModernButton
          title={retryText}
          onPress={onRetry}
          variant="outline"
          style={styles.retryButton}
          icon={<IconSymbol name="arrow.clockwise" size={16} />}
        />
      )}
    </View>
  );
};

interface NetworkErrorProps {
  onRetry?: () => void;
  style?: ViewStyle;
}

export const NetworkError: React.FC<NetworkErrorProps> = ({
  onRetry,
  style,
}) => {
  return (
    <ErrorState
      error="Please check your internet connection and try again."
      onRetry={onRetry}
      icon="wifi.slash"
      title="No Internet Connection"
      retryText="Retry"
      style={style}
    />
  );
};

interface NotFoundErrorProps {
  message?: string;
  onGoBack?: () => void;
  style?: ViewStyle;
}

export const NotFoundError: React.FC<NotFoundErrorProps> = ({
  message = "The item you're looking for doesn't exist.",
  onGoBack,
  style,
}) => {
  return (
    <ErrorState
      error={message}
      onRetry={onGoBack}
      icon="questionmark.circle"
      title="Not Found"
      retryText="Go Back"
      style={style}
    />
  );
};

interface ValidationErrorProps {
  errors: Record<string, string>;
  style?: ViewStyle;
}

export const ValidationError: React.FC<ValidationErrorProps> = ({
  errors,
  style,
}) => {
  const errorColor = useThemeColor({}, 'error');

  return (
    <View style={[styles.validationError, style]}>
      {Object.entries(errors).map(([field, message]) => (
        <View key={field} style={styles.validationItem}>
          <IconSymbol name="exclamationmark.circle" size={14} color={errorColor} />
          <Text style={[styles.validationText, { color: errorColor }]}>
            {message}
          </Text>
        </View>
      ))}
    </View>
  );
};

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: string;
  actionText?: string;
  onAction?: () => void;
  style?: ViewStyle;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon = 'tray',
  actionText,
  onAction,
  style,
}) => {
  const textColor = useThemeColor({}, 'text');
  const textSecondary = useThemeColor({}, 'textSecondary');

  return (
    <View style={[styles.emptyState, style]}>
      <IconSymbol name={icon} size={64} color={textSecondary} />
      <Text style={[styles.emptyTitle, { color: textColor }]}>
        {title}
      </Text>
      <Text style={[styles.emptyDescription, { color: textSecondary }]}>
        {description}
      </Text>
      {actionText && onAction && (
        <ModernButton
          title={actionText}
          onPress={onAction}
          style={styles.emptyAction}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  errorMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.sm,
    gap: Spacing.sm,
  },
  errorText: {
    ...Typography.caption,
    flex: 1,
  },
  errorBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.md,
    borderRadius: 8,
    marginBottom: Spacing.md,
  },
  errorBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: Spacing.sm,
  },
  errorBannerText: {
    ...Typography.body,
    flex: 1,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorTitle: {
    ...Typography.h3,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  errorDescription: {
    ...Typography.body,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  retryButton: {
    marginTop: Spacing.md,
  },
  validationError: {
    padding: Spacing.sm,
  },
  validationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  validationText: {
    ...Typography.caption,
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyTitle: {
    ...Typography.h3,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    ...Typography.body,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  emptyAction: {
    marginTop: Spacing.md,
  },
});
