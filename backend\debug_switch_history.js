/**
 * Debug Switch History Response
 */

const axios = require("axios");

async function debugSwitchHistory() {
  try {
    console.log("🔐 Login...");
    const loginResponse = await axios.post("http://localhost:3020/api/pos/login", {
      username: "admin1",
      password: "admin123"
    });

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log("✅ Login successful");

    console.log("📋 Testing switch history...");
    const historyResponse = await axios.get("http://localhost:3020/api/pos/user-switching/switch-history", { headers });

    console.log("Full response:", JSON.stringify(historyResponse.data, null, 2));

  } catch (error) {
    console.error("❌ Test failed:", error.response?.data?.error || error.message);
  }
}

debugSwitchHistory();
