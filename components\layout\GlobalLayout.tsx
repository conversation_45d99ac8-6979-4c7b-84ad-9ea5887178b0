import { useThemeColor } from "@/hooks/useThemeColor";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useRouter } from "expo-router";
import React from "react";
import { StyleSheet, View, Platform } from "react-native";
import { GlobalHeader } from "./GlobalHeader";
import { SafeAreaWrapper } from "./SafeAreaWrapper";
import { SlidingSidebar } from "./SlidingSidebar";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import { ResponsiveContainer } from "@/src/components/layout/ResponsiveContainer";

interface GlobalLayoutProps {
  children: React.ReactNode;
  showBackButton?: boolean;
}

export function GlobalLayout({
  children,
  showBackButton = false,
}: GlobalLayoutProps) {
  const router = useRouter();
  const {
    sidebarVisible,
    closeSidebar,
    toggleSidebar,
    currentTitle,
    canGoBack,
  } = useNavigation();
  const backgroundColor = useThemeColor({}, "background");
  const responsiveLayout = useResponsiveLayout();
  const isDesktop = responsiveLayout?.isDesktop || false;
  const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;

  const handleBackPress = () => {
    router.back();
  };

  // Determine if we should show back button - use navigation context or explicit prop
  const shouldShowBackButton = showBackButton || canGoBack;

  // Wrap content in responsive container for web
  const content =
    Platform.OS === "web" && isDesktop ? (
      <ResponsiveContainer
        maxWidth={true}
        centered={true}
        padding={true}
        style={styles.content}
      >
        {children}
      </ResponsiveContainer>
    ) : (
      <View style={styles.content}>{children}</View>
    );

  return (
    <SafeAreaWrapper
      style={[styles.container, { backgroundColor }]}
      useResponsiveContainer={false} // We handle responsive container manually
    >
      <GlobalHeader
        title={currentTitle}
        onMenuPress={toggleSidebar}
        showBackButton={shouldShowBackButton}
        onBackPress={shouldShowBackButton ? handleBackPress : undefined}
      />

      {content}

      <SlidingSidebar visible={sidebarVisible} onClose={closeSidebar} />
    </SafeAreaWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // Add layout constraints to prevent expansion
    minHeight: 0,
    maxHeight: "100%",
    overflow: "hidden", // Prevent content from expanding beyond container
  },
  content: {
    flex: 1,
    // Ensure content doesn't expand beyond its container
    minHeight: 0,
    overflow: "hidden",
  },
});
