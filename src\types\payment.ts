export interface PaymentMethod {
  id: string;
  name: string;
  type: "cash" | "mpesa" | "absa_till" | "credit" | "card" | "split";
  enabled: boolean;
  placeholder: boolean;
  icon: string;
  description: string;
  requiresAmount?: boolean;
  requiresReference?: boolean;
  comingSoon?: boolean;
}

export interface PaymentRequest {
  method: PaymentMethod;
  amount: number;
  currency: string;
  reference?: string;
  customerPhone?: string;
  notes?: string;
}

export interface PaymentResult {
  success: boolean;
  transactionId: string;
  method: PaymentMethod;
  amount: number;
  currency: string;
  reference?: string;
  timestamp: string;
  change?: number; // Add change property for cash/credit payments
  error?: string;
  receiptData?: PaymentReceiptData;
  cashData?: CashPayment;
  absaTillData?: {
    tillNumber: string;
    accountNumber?: string;
    referenceNumber?: string;
    transactionCode?: string;
    customerMessage?: string;
  };
}

// Split Payment Interfaces
export interface SplitPaymentMethod {
  paymentMethod: PaymentMethod;
  amount: number;
  amountTendered?: number; // For cash/credit payments
  additionalData?: any; // For ABSA Till transaction codes, etc.
}

export interface SplitPaymentRequest {
  totalAmount: number;
  payments: SplitPaymentMethod[];
  currency: string;
}

export interface SplitPaymentResult {
  success: boolean;
  totalAmount: number;
  payments: PaymentResult[];
  remainingAmount: number;
  isComplete: boolean;
  combinedTransactionId: string;
  timestamp: string;
  totalChange: number;
  error?: string;
  receiptData?: SplitPaymentReceiptData;
}

export interface SplitPaymentReceiptData {
  combinedTransactionId: string;
  totalAmount: number;
  totalChange: number;
  currency: string;
  timestamp: string;
  payments: {
    method: string;
    amount: number;
    transactionId: string;
    change?: number;
  }[];
  customerInfo?: {
    name: string;
    phone?: string;
  };
  staffInfo: {
    name: string;
    terminal: string;
  };
  locationInfo: {
    name: string;
    address?: string;
  };
}

export interface PaymentReceiptData {
  transactionId: string;
  method: string;
  amount: number;
  currency: string;
  timestamp: string;
  reference?: string;
  change?: number; // Add change property for cash/credit payments
  customerInfo?: {
    name: string;
    phone?: string;
  };
  staffInfo: {
    name: string;
    terminal: string;
  };
  locationInfo: {
    name: string;
    address?: string;
  };
}

export interface CashPayment {
  amountTendered: number;
  amountDue: number;
  change: number;
  validated: boolean;
  validatedBy: string;
  validatedAt: string;
}

export interface AbsaTillPayment {
  tillNumber: string;
  accountNumber?: string;
  transactionCode: string;
  amount: number;
  status: "pending" | "completed" | "failed";
  referenceNumber?: string;
  absaTransactionCode?: string;
}

export interface CreditPayment {
  amountTendered: number;
  amountDue: number;
  change: number;
  validated: boolean;
  validatedBy: string;
  validatedAt: string;
}

export interface CardPayment {
  cardType: "visa" | "mastercard" | "amex" | "other";
  last4Digits: string;
  authorizationCode: string;
  terminalId: string;
  batchNumber?: string;
}

// Payment method configurations for Kenya
export const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: "cash",
    name: "Cash",
    type: "cash",
    enabled: true,
    placeholder: false,
    icon: "💵",
    description: "Cash payment - immediate processing",
    requiresAmount: true,
  },
  {
    id: "mpesa",
    name: "M-Pesa",
    type: "mpesa",
    enabled: true,
    placeholder: false,
    icon: "📱",
    description: "M-Pesa STK Push with manual fallback",
    requiresAmount: true,
    requiresReference: false,
  },
  {
    id: "credit",
    name: "Credit",
    type: "credit",
    enabled: true,
    placeholder: false,
    icon: "💳",
    description: "Credit payment - immediate processing",
    requiresAmount: true,
  },
  {
    id: "absa_till",
    name: "ABSA Till",
    type: "absa_till",
    enabled: true,
    placeholder: false,
    comingSoon: false,
    icon: "📱",
    description: "Mobile money payment via ABSA Till Number",
    requiresAmount: true,
    requiresReference: true,
  },
  {
    id: "card",
    name: "Card Payment",
    type: "card",
    enabled: true,
    placeholder: false,
    comingSoon: false,
    icon: "💳",
    description: "Credit/Debit card payment with manual confirmation",
    requiresAmount: true,
  },
];

export const CURRENCY = {
  code: "KES",
  symbol: "KSh",
  name: "Kenyan Shilling",
};

// Remove default export to avoid circular dependency issues
