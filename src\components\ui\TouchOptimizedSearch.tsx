import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  Animated,
  Keyboard,
} from 'react-native';
import { useTheme } from '@/src/contexts/ThemeContext';
import { createStyleUtils } from '@/src/utils/themeUtils';
import { ResponsiveDimensions, PerformanceHelpers, HapticHelpers } from '@/src/utils/mobileUtils';
import { ThemedView, ThemedText } from '@/src/components/themed/ThemedComponents';
import { FadeInView, SlideInView } from '@/src/components/animated/AnimatedComponents';
import { IconSymbol } from '@/components/ui/IconSymbol';

interface TouchOptimizedSearchProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  onClear?: () => void;
  debounceMs?: number;
  autoFocus?: boolean;
  showClearButton?: boolean;
  style?: ViewStyle;
  containerStyle?: ViewStyle;
}

export const TouchOptimizedSearch: React.FC<TouchOptimizedSearchProps> = ({
  placeholder = 'Search products...',
  onSearch,
  onClear,
  debounceMs = 300,
  autoFocus = false,
  showClearButton = true,
  style,
  containerStyle,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  
  const inputRef = useRef<TextInput>(null);
  const animatedWidth = useRef(new Animated.Value(0)).current;
  const animatedOpacity = useRef(new Animated.Value(0)).current;
  
  // Debounced search function
  const debouncedSearch = useRef(
    PerformanceHelpers.debounce((searchQuery: string) => {
      onSearch(searchQuery);
    }, debounceMs)
  ).current;

  useEffect(() => {
    debouncedSearch(query);
  }, [query, debouncedSearch]);

  useEffect(() => {
    if (autoFocus) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [autoFocus]);

  const handleFocus = () => {
    setIsFocused(true);
    setIsExpanded(true);
    HapticHelpers.light();
    
    // Animate expansion
    Animated.parallel([
      Animated.timing(animatedWidth, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(animatedOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    
    // If no query, collapse the search
    if (!query) {
      setIsExpanded(false);
      Animated.parallel([
        Animated.timing(animatedWidth, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(animatedOpacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }),
      ]).start();
    }
  };

  const handleClear = () => {
    setQuery('');
    HapticHelpers.light();
    
    if (onClear) {
      onClear();
    }
    
    // Keep focus on input after clearing
    inputRef.current?.focus();
  };

  const handleCancel = () => {
    setQuery('');
    setIsExpanded(false);
    setIsFocused(false);
    Keyboard.dismiss();
    HapticHelpers.light();
    
    if (onClear) {
      onClear();
    }
    
    // Animate collapse
    Animated.parallel([
      Animated.timing(animatedWidth, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(animatedOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handleSearchPress = () => {
    if (!isExpanded) {
      setIsExpanded(true);
      handleFocus();
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  return (
    <ThemedView style={[styles.container, containerStyle]}>
      <ThemedView style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
        {/* Search Icon / Button */}
        <TouchableOpacity
          style={[
            styles.searchButton,
            {
              backgroundColor: isExpanded ? 'transparent' : theme.colors.primary,
              minWidth: ResponsiveDimensions.touchTargetMedium,
              minHeight: ResponsiveDimensions.touchTargetMedium,
            },
          ]}
          onPress={handleSearchPress}
          accessibilityRole="button"
          accessibilityLabel="Search"
          accessibilityHint="Tap to search for products"
        >
          <IconSymbol
            name="magnifyingglass"
            size={20}
            color={isExpanded ? theme.colors.textSecondary : theme.colors.primaryForeground}
          />
        </TouchableOpacity>

        {/* Animated Search Input */}
        <Animated.View
          style={[
            styles.inputContainer,
            {
              width: animatedWidth.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '100%'],
              }),
              opacity: animatedOpacity,
            },
          ]}
        >
          <TextInput
            ref={inputRef}
            style={[
              styles.input,
              {
                color: theme.colors.text,
                fontSize: ResponsiveDimensions.fontSize.base,
                minHeight: ResponsiveDimensions.touchTargetMedium,
              },
              style,
            ]}
            placeholder={placeholder}
            placeholderTextColor={theme.colors.placeholder}
            value={query}
            onChangeText={setQuery}
            onFocus={handleFocus}
            onBlur={handleBlur}
            returnKeyType="search"
            clearButtonMode="never" // We'll handle this ourselves
            accessibilityLabel="Search input"
            accessibilityHint="Type to search for products"
          />

          {/* Clear Button */}
          {showClearButton && query.length > 0 && (
            <FadeInView>
              <TouchableOpacity
                style={[
                  styles.clearButton,
                  {
                    minWidth: ResponsiveDimensions.touchTargetSmall,
                    minHeight: ResponsiveDimensions.touchTargetSmall,
                  },
                ]}
                onPress={handleClear}
                accessibilityRole="button"
                accessibilityLabel="Clear search"
              >
                <IconSymbol
                  name="xmark.circle"
                  size={18}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>
            </FadeInView>
          )}
        </Animated.View>

        {/* Cancel Button */}
        {isExpanded && (
          <SlideInView direction="right" duration={200}>
            <TouchableOpacity
              style={[
                styles.cancelButton,
                {
                  minWidth: ResponsiveDimensions.touchTargetMedium,
                  minHeight: ResponsiveDimensions.touchTargetMedium,
                },
              ]}
              onPress={handleCancel}
              accessibilityRole="button"
              accessibilityLabel="Cancel search"
            >
              <ThemedText variant="body" color="primary">
                Cancel
              </ThemedText>
            </TouchableOpacity>
          </SlideInView>
        )}
      </ThemedView>

      {/* Search Results Count */}
      {query.length > 0 && (
        <FadeInView delay={100}>
          <ThemedView style={styles.resultsInfo}>
            <ThemedText variant="caption" color="secondary">
              Searching for "{query}"
            </ThemedText>
          </ThemedView>
        </FadeInView>
      )}
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: ResponsiveDimensions.spacing.md,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minHeight: ResponsiveDimensions.touchTargetMedium + 8,
  },
  searchButton: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'hidden',
  },
  input: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  clearButton: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderRadius: 12,
  },
  cancelButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
    marginLeft: 8,
  },
  resultsInfo: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});
