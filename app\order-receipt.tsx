import { ErrorModal } from "@/components/ui/ErrorModal";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { Colors } from "@/constants/Colors";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import {
  OrderData,
  ReceiptData,
} from "@/src/components/receipt/ReceiptGenerator";
import { StandardizedReceiptDisplay } from "@/src/components/receipt/StandardizedReceiptDisplay";
import ThermalPrintButton from "@/src/components/thermal/ThermalPrintButton";
import { useNavigation } from "@/src/contexts/NavigationContext";
import {
  StandardizedReceiptData,
  StandardizedReceiptService,
} from "@/src/services/StandardizedReceiptService";
import { UnifiedReceiptManager } from "@/src/services/UnifiedReceiptManager";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const OrderReceiptScreen: React.FC = () => {
  const router = useRouter();
  const { setCurrentTitle } = useNavigation();
  const { orderData } = useLocalSearchParams<{ orderData: string }>();

  const [order, setOrder] = useState<OrderData | null>(null);
  const [receiptData, setReceiptData] = useState<ReceiptData | null>(null);
  const [standardizedReceiptData, setStandardizedReceiptData] =
    useState<StandardizedReceiptData | null>(null);
  const [isPrinting, setIsPrinting] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
  }>({ title: "", message: "" });

  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const surfaceColor = useThemeColor({}, "surface");
  const successColor = useThemeColor({}, "success");

  useEffect(() => {
    setCurrentTitle("Receipt Preview");

    const loadReceiptData = async () => {
      if (orderData) {
        try {
          const parsedOrder = JSON.parse(decodeURIComponent(orderData));
          setOrder(parsedOrder);

          // Generate receipt data from order using standardized service
          const receipt =
            await StandardizedReceiptService.generateStandardizedReceipt(
              parsedOrder
            );
          setReceiptData(receipt as any); // Type compatibility

          // Generate standardized receipt data
          const standardizedReceipt =
            await StandardizedReceiptService.generateStandardizedReceipt(
              parsedOrder
            );
          setStandardizedReceiptData(standardizedReceipt);
        } catch (error) {
          console.error("Failed to parse order data:", error);
          setModalData({
            title: "Error",
            message: "Invalid order data",
          });
          setShowErrorModal(true);
          setTimeout(() => router.back(), 2000);
        }
      } else {
        setModalData({
          title: "Error",
          message: "No order data provided",
        });
        setShowErrorModal(true);
        setTimeout(() => router.back(), 2000);
      }
    };

    loadReceiptData();
  }, [orderData, setCurrentTitle, router]);

  const handlePrint = async () => {
    if (!order) return;

    try {
      setIsPrinting(true);

      // Use UnifiedReceiptManager for consistent receipt generation with all data
      const unifiedResult = await UnifiedReceiptManager.generateReceipt(order, {
        format: "thermal",
        autoPrint: true,
        printerType: "thermal",
      });

      if (unifiedResult.success && unifiedResult.printed) {
        // Unified printing successful
        setModalData({
          title: "Print Successful",
          message:
            "Receipt printed successfully with all order details including discounts, loyalty points, and shipping fees.",
        });
        setShowSuccessModal(true);
      } else {
        // Unified printing failed
        setModalData({
          title: "Print Error",
          message: `Failed to print receipt: ${
            unifiedResult.error || "Unknown error"
          }\n\nPlease check your printer connection and try again.`,
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Print receipt error:", error);
      setModalData({
        title: "Print Error",
        message:
          "Failed to print receipt. Please check your printer connection and try again.",
      });
      setShowErrorModal(true);
    } finally {
      setIsPrinting(false);
    }
  };

  const handleShare = async () => {
    if (!order) return;

    try {
      setIsSharing(true);

      // Use UnifiedReceiptManager for consistent receipt sharing
      const result = await UnifiedReceiptManager.generateReceipt(order, {
        format: "whatsapp",
        shareVia: "whatsapp",
      });

      if (result.success && result.shared) {
        setModalData({
          title: "Success",
          message: "Receipt shared successfully with all order details!",
        });
        setShowSuccessModal(true);
      } else {
        setModalData({
          title: "Share Error",
          message: result.error || "Failed to share receipt",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Share error:", error);
      setModalData({
        title: "Error",
        message: "Failed to share receipt",
      });
      setShowErrorModal(true);
    } finally {
      setIsSharing(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " at " +
      date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  };

  // Use centralized currency formatting
  const formatReceiptCurrency = (amount: number) => formatCurrency(amount);

  if (!receiptData) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor }]}>
        <ActivityIndicator size="large" color={primaryColor} />
        <Text style={[styles.loadingText, { color: textColor }]}>
          Loading receipt...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: surfaceColor }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color={textColor} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: textColor }]}>
          Receipt Preview
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Receipt Content */}
      <View style={styles.content}>
        <ModernCard style={styles.receiptCard} variant="elevated">
          {standardizedReceiptData ? (
            <StandardizedReceiptDisplay
              receiptData={standardizedReceiptData}
              style={styles.receiptContent}
            />
          ) : receiptData ? (
            <>
              {/* Fallback to old format */}
              <View style={styles.storeHeader}>
                <Text style={[styles.storeName, { color: textColor }]}>
                  TREASURED SCENTS
                </Text>
                <Text style={[styles.storeSubtitle, { color: textSecondary }]}>
                  Greenhouse Mall, Ngong Road, Kenya
                </Text>
                <Text style={[styles.storePhone, { color: textSecondary }]}>
                  Mobile: +254 111 443 993
                </Text>
                <Text style={[styles.storePhone, { color: textSecondary }]}>
                  Email: <EMAIL>
                </Text>
              </View>

              <View style={styles.divider} />

              {/* Receipt Details */}
              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>
                  SALES RECEIPT
                </Text>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    Receipt No:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {receiptData.orderNumber}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    Date:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {formatDate(receiptData.orderDate)}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    Served by:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {receiptData.staff.name}
                  </Text>
                </View>
              </View>

              <View style={styles.divider} />

              {/* Customer Information */}
              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>
                  Customer:
                </Text>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    Name:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {receiptData.customer.name}
                  </Text>
                </View>
                {receiptData.customer.phone && (
                  <View style={styles.row}>
                    <Text style={[styles.label, { color: textSecondary }]}>
                      Mobile:
                    </Text>
                    <Text style={[styles.value, { color: textColor }]}>
                      {receiptData.customer.phone}
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.divider} />

              {/* Items */}
              <View style={styles.section}>
                {receiptData.items.map((item, index) => (
                  <View key={index} style={styles.item}>
                    <Text style={[styles.itemName, { color: textColor }]}>
                      {item.title}
                    </Text>
                    {item.variantTitle && (
                      <Text
                        style={[styles.itemVariant, { color: textSecondary }]}
                      >
                        {item.variantTitle}
                      </Text>
                    )}
                    <View style={styles.itemDetails}>
                      <Text
                        style={[styles.itemQuantity, { color: textSecondary }]}
                      >
                        {item.quantity} x{" "}
                        {formatReceiptCurrency(parseFloat(item.price))}
                      </Text>
                      <Text style={[styles.itemTotal, { color: textColor }]}>
                        {formatReceiptCurrency(
                          parseFloat(item.price) * item.quantity
                        )}
                      </Text>
                    </View>
                    {item.sku && (
                      <Text style={[styles.itemSku, { color: textSecondary }]}>
                        SKU: {item.sku}
                      </Text>
                    )}
                  </View>
                ))}
              </View>

              <View style={styles.divider} />

              {/* Totals */}
              <View style={styles.totals}>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    Total Items:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {receiptData.items.length}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    Subtotal:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {formatReceiptCurrency(receiptData.subtotal)}
                  </Text>
                </View>
                <View style={[styles.row, styles.totalRow]}>
                  <Text style={[styles.totalLabel, { color: textColor }]}>
                    GRAND TOTAL:
                  </Text>
                  <Text style={[styles.totalValue, { color: textColor }]}>
                    {formatReceiptCurrency(receiptData.total)}
                  </Text>
                </View>
              </View>

              <View style={styles.divider} />

              {/* Payment Details */}
              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>
                  PAYMENT DETAILS
                </Text>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    {receiptData.paymentMethod}:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {formatReceiptCurrency(receiptData.total)}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Text style={[styles.label, { color: textSecondary }]}>
                    Total Paid:
                  </Text>
                  <Text style={[styles.value, { color: textColor }]}>
                    {formatReceiptCurrency(receiptData.total)}
                  </Text>
                </View>
                {receiptData.salesAgent && (
                  <View style={styles.row}>
                    <Text style={[styles.label, { color: textSecondary }]}>
                      Sales Agent:
                    </Text>
                    <Text style={[styles.value, { color: textColor }]}>
                      {receiptData.salesAgent.name}
                    </Text>
                  </View>
                )}
              </View>

              {/* CRITICAL FIX: Add Loyalty Points Section */}
              {receiptData.loyaltyPoints &&
                receiptData.loyaltyPoints.balance > 0 && (
                  <>
                    <View style={styles.divider} />
                    <View style={styles.section}>
                      <Text style={[styles.sectionTitle, { color: textColor }]}>
                        🌟 LOYALTY REWARDS 🌟
                      </Text>
                      <View style={styles.row}>
                        <Text style={[styles.label, { color: textSecondary }]}>
                          Total TS Points:
                        </Text>
                        <Text
                          style={[
                            styles.value,
                            { color: textColor, fontWeight: "bold" },
                          ]}
                        >
                          {receiptData.loyaltyPoints.balance.toLocaleString()}
                        </Text>
                      </View>
                      {receiptData.loyaltyPoints.earned > 0 && (
                        <View style={styles.row}>
                          <Text
                            style={[styles.label, { color: textSecondary }]}
                          >
                            Points Earned:
                          </Text>
                          <Text style={[styles.value, { color: textColor }]}>
                            +{receiptData.loyaltyPoints.earned.toLocaleString()}
                          </Text>
                        </View>
                      )}
                    </View>
                  </>
                )}

              {/* Footer */}
              <View style={styles.footer}>
                <Text style={[styles.footerText, { color: textSecondary }]}>
                  You Are Treasured
                </Text>
                <Text style={[styles.footerText, { color: textSecondary }]}>
                  Thank you for shopping with us!
                </Text>
              </View>
            </>
          ) : (
            <View style={styles.centered}>
              <Text style={[styles.loadingText, { color: textColor }]}>
                Loading receipt data...
              </Text>
            </View>
          )}
        </ModernCard>
      </View>

      {/* Action Buttons */}
      <View style={[styles.actions, { backgroundColor: surfaceColor }]}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: successColor }]}
          onPress={handlePrint}
          disabled={isPrinting}
        >
          {isPrinting ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <IconSymbol name="printer.fill" size={20} color="white" />
          )}
          <Text style={styles.actionButtonText}>
            {isPrinting ? "Printing..." : "Print Options"}
          </Text>
        </TouchableOpacity>

        {order && (
          <ThermalPrintButton
            order={order}
            style={[styles.actionButton, { backgroundColor: "#FF6B35" }]}
            size="medium"
            variant="primary"
            onSetupRequired={() => router.push("/thermal-printer-setup")}
          />
        )}

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: primaryColor }]}
          onPress={handleShare}
          disabled={isSharing}
        >
          {isSharing ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <IconSymbol
              name="square.and.arrow.up.fill"
              size={20}
              color="white"
            />
          )}
          <Text style={styles.actionButtonText}>
            {isSharing ? "Sharing..." : "Share"}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowErrorModal(false)}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title={modalData.title}
        message={modalData.message}
        onClose={() => setShowSuccessModal(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
  },
  headerTitle: {
    ...Typography.h3,
    flex: 1,
    textAlign: "center",
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  receiptCard: {
    marginBottom: Spacing.lg,
  },
  receiptContent: {
    flex: 1,
  },
  storeHeader: {
    alignItems: "center",
    paddingBottom: Spacing.md,
  },
  storeName: {
    ...Typography.h2,
    marginBottom: 4,
  },
  storeSubtitle: {
    ...Typography.bodyMedium,
    marginBottom: 4,
  },
  storeAddress: {
    ...Typography.caption,
    textAlign: "center",
    marginBottom: 2,
  },
  storePhone: {
    ...Typography.caption,
    textAlign: "center",
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: Spacing.md,
  },
  section: {
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    ...Typography.bodyMedium,
    marginBottom: Spacing.sm,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  label: {
    ...Typography.body,
    flex: 1,
  },
  value: {
    ...Typography.body,
    textAlign: "right",
  },
  item: {
    marginBottom: Spacing.sm,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  itemName: {
    ...Typography.bodyMedium,
    fontWeight: "600",
    marginBottom: 4,
    lineHeight: 20,
  },
  itemVariant: {
    ...Typography.caption,
    fontStyle: "italic",
    marginBottom: 4,
  },
  itemDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 2,
    paddingTop: 2,
  },
  itemQuantity: {
    ...Typography.caption,
    flex: 1,
    textAlign: "left",
  },
  itemTotal: {
    ...Typography.bodyMedium,
    fontWeight: "600",
    textAlign: "right",
    minWidth: 80,
  },
  itemSku: {
    ...Typography.small,
  },
  totals: {
    paddingTop: Spacing.md,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },
  totalLabel: {
    ...Typography.h3,
  },
  totalValue: {
    ...Typography.h2,
  },
  footer: {
    alignItems: "center",
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  footerText: {
    ...Typography.caption,
    textAlign: "center",
    marginBottom: 2,
  },
  actions: {
    flexDirection: "row",
    padding: Spacing.md,
    gap: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Spacing.md,
    borderRadius: 8,
    gap: Spacing.sm,
  },
  actionButtonText: {
    ...Typography.bodyMedium,
    color: "white",
    fontWeight: "600",
  },
  loadingText: {
    ...Typography.body,
    marginTop: Spacing.md,
  },
});

export default OrderReceiptScreen;
