# Production Scripts Guide

This guide explains how to run database and system scripts safely in production environments using the dynamic script runner.

## Overview

The `run-script-dynamic.js` script provides a safe way to run various database and system maintenance scripts across different environments (local, staging, production) by using environment variables for configuration.

## Prerequisites

1. **Environment Variables**: Ensure all required environment variables are set
2. **Database Access**: Verify database connectivity
3. **Server Running**: For API-based scripts, ensure the backend server is running
4. **Permissions**: Ensure the database user has appropriate permissions

## Required Environment Variables

### Database Configuration
```bash
DB_HOST=your-database-host
DB_PORT=3306
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=your-database-name
```

### API Configuration (for API-based scripts)
```bash
PORT=3020
API_BASE_URL=http://localhost:3020/api  # Optional, auto-detected
ADMIN_USERNAME=admin1                   # Optional, defaults to admin1
ADMIN_PASSWORD=admin123                 # Optional, defaults to admin123
```

### Environment Identification
```bash
NODE_ENV=production  # or staging, local
```

## Available Scripts

### 1. Grant Permissions (API-based)
**Script**: `grant-permissions`
**Purpose**: Grant `collect_payments` permission to staff members via API
**Requirements**: Backend server must be running

```bash
# Local environment
node run-script-dynamic.js grant-permissions

# Production environment
NODE_ENV=production node run-script-dynamic.js grant-permissions
```

### 2. Fix Payment Status ENUM
**Script**: `fix-payment-status`
**Purpose**: Add 'authorized' to payment status ENUM
**Requirements**: Direct database access

```bash
node run-script-dynamic.js fix-payment-status
```

### 3. Fix Payment Transactions ENUM
**Script**: `fix-payment-transactions`
**Purpose**: Add 'authorized' to payment transactions status ENUM
**Requirements**: Direct database access

```bash
node run-script-dynamic.js fix-payment-transactions
```

### 4. Fix Permissions Table
**Script**: `fix-permissions-table`
**Purpose**: Create or fix the staff_permissions table
**Requirements**: Direct database access

```bash
node run-script-dynamic.js fix-permissions-table
```

### 5. Add Collect Payments Permission (Direct DB)
**Script**: `add-collect-payments`
**Purpose**: Add collect_payments permission directly via database
**Requirements**: Direct database access

```bash
node run-script-dynamic.js add-collect-payments
```

## Production Deployment Workflow

### Step 1: Prepare Environment
```bash
# Set production environment variables
export NODE_ENV=production
export DB_HOST=your-production-db-host
export DB_USER=your-production-db-user
export DB_PASSWORD=your-production-db-password
export DB_NAME=your-production-db-name
```

### Step 2: Verify Configuration
```bash
# Test environment validation
node run-script-dynamic.js --help
```

### Step 3: Run Required Scripts
```bash
# For permission updates (requires server running)
node run-script-dynamic.js grant-permissions

# For database schema updates (direct DB access)
node run-script-dynamic.js fix-payment-status
node run-script-dynamic.js fix-payment-transactions
```

## Safety Features

1. **Environment Validation**: Scripts validate required environment variables before execution
2. **Dynamic Configuration**: All database and API connections use environment variables
3. **Error Handling**: Comprehensive error handling and logging
4. **Dry Run Capability**: Scripts show what they will do before making changes
5. **Rollback Information**: Database scripts provide rollback instructions when applicable

## Troubleshooting

### Common Issues

1. **Connection Refused (API scripts)**
   - Ensure backend server is running
   - Check PORT environment variable
   - Verify API_BASE_URL if custom

2. **Database Connection Failed**
   - Verify database credentials
   - Check network connectivity
   - Ensure database server is running

3. **Permission Denied**
   - Verify database user permissions
   - Check if user can CREATE/ALTER tables (for schema scripts)
   - Ensure user can INSERT/UPDATE (for data scripts)

4. **Authentication Failed (API scripts)**
   - Verify ADMIN_USERNAME and ADMIN_PASSWORD
   - Check if admin user exists in database
   - Ensure admin user has required permissions

### Logging

All scripts provide detailed logging:
- 🌍 Environment information
- 🗄️ Database connection details
- 🚀 Script execution progress
- ✅ Success confirmations
- ❌ Error details with troubleshooting hints

## Security Considerations

1. **Environment Variables**: Never commit production credentials to version control
2. **Database Access**: Use dedicated database users with minimal required permissions
3. **API Authentication**: Use strong admin passwords in production
4. **Network Security**: Ensure database and API endpoints are properly secured
5. **Audit Trail**: All script executions are logged for audit purposes

## Backup Recommendations

Before running any production scripts:

1. **Database Backup**: Create a full database backup
2. **Schema Backup**: Export current table schemas
3. **Data Backup**: Backup critical tables (staff, permissions, orders)
4. **Configuration Backup**: Save current environment configuration

## Example Production Deployment

```bash
#!/bin/bash
# Production deployment script example

# Set environment
export NODE_ENV=production
export DB_HOST=prod-db.example.com
export DB_USER=dukalink_prod
export DB_PASSWORD=secure_password
export DB_NAME=dukalink_shopify_pos

# Validate environment
echo "Validating environment..."
node run-script-dynamic.js --help

# Run database schema updates
echo "Updating database schema..."
node run-script-dynamic.js fix-payment-status
node run-script-dynamic.js fix-payment-transactions

# Start backend server (in background)
echo "Starting backend server..."
npm start &
SERVER_PID=$!

# Wait for server to start
sleep 10

# Run API-based updates
echo "Updating permissions..."
node run-script-dynamic.js grant-permissions

# Stop server
kill $SERVER_PID

echo "Deployment completed successfully!"
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review script logs for detailed error information
3. Verify environment configuration
4. Test in staging environment first
