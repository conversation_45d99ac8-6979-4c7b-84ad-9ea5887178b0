/**
 * Ticket Cleanup Service
 * 
 * Handles background cleanup operations for tickets including:
 * - Automatic expiry of old tickets
 * - Archival of completed tickets
 * - Cleanup of orphaned data
 * - Performance optimization tasks
 */

const cron = require('node-cron');
const ticketService = require('./ticket-management-service');

class TicketCleanupService {
  constructor() {
    this.isRunning = false;
    this.jobs = new Map();
    this.stats = {
      lastCleanup: null,
      totalCleaned: 0,
      totalArchived: 0,
      errors: []
    };
  }

  /**
   * Start all cleanup jobs
   */
  start() {
    if (this.isRunning) {
      console.log('Ticket cleanup service is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting ticket cleanup service...');

    // Schedule expired ticket cleanup every hour
    const expiredTicketJob = cron.schedule('0 * * * *', async () => {
      await this.cleanupExpiredTickets();
    }, {
      scheduled: false,
      timezone: process.env.TIMEZONE || 'UTC'
    });

    // Schedule completed ticket archival daily at 2 AM
    const archivedTicketJob = cron.schedule('0 2 * * *', async () => {
      await this.archiveOldCompletedTickets();
    }, {
      scheduled: false,
      timezone: process.env.TIMEZONE || 'UTC'
    });

    // Schedule orphaned data cleanup weekly on Sunday at 3 AM
    const orphanedDataJob = cron.schedule('0 3 * * 0', async () => {
      await this.cleanupOrphanedData();
    }, {
      scheduled: false,
      timezone: process.env.TIMEZONE || 'UTC'
    });

    // Schedule statistics cleanup monthly on 1st at 4 AM
    const statsCleanupJob = cron.schedule('0 4 1 * *', async () => {
      await this.cleanupOldStatistics();
    }, {
      scheduled: false,
      timezone: process.env.TIMEZONE || 'UTC'
    });

    // Store jobs for management
    this.jobs.set('expiredTickets', expiredTicketJob);
    this.jobs.set('archivedTickets', archivedTicketJob);
    this.jobs.set('orphanedData', orphanedDataJob);
    this.jobs.set('statsCleanup', statsCleanupJob);

    // Start all jobs
    this.jobs.forEach((job, name) => {
      job.start();
      console.log(`Started cleanup job: ${name}`);
    });

    console.log('Ticket cleanup service started successfully');
  }

  /**
   * Stop all cleanup jobs
   */
  stop() {
    if (!this.isRunning) {
      console.log('Ticket cleanup service is not running');
      return;
    }

    console.log('Stopping ticket cleanup service...');

    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(`Stopped cleanup job: ${name}`);
    });

    this.jobs.clear();
    this.isRunning = false;

    console.log('Ticket cleanup service stopped');
  }

  /**
   * Cleanup expired tickets
   */
  async cleanupExpiredTickets() {
    try {
      console.log('Starting expired ticket cleanup...');
      
      const result = await ticketService.cleanupExpiredTickets();
      
      if (result.success) {
        this.stats.totalCleaned += result.cleanedCount;
        this.stats.lastCleanup = new Date().toISOString();
        
        console.log(`Expired ticket cleanup completed: ${result.cleanedCount} tickets cleaned`);
        
        if (result.cleanedCount > 0) {
          // Log significant cleanup events
          console.log('Cleaned tickets:', result.cleanedTickets);
        }
      } else {
        console.error('Expired ticket cleanup failed:', result.error);
        this.stats.errors.push({
          type: 'expired_cleanup',
          error: result.error,
          timestamp: new Date().toISOString()
        });
      }

      return result;

    } catch (error) {
      console.error('Expired ticket cleanup error:', error);
      this.stats.errors.push({
        type: 'expired_cleanup',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Archive old completed tickets
   */
  async archiveOldCompletedTickets() {
    try {
      console.log('Starting completed ticket archival...');
      
      const daysOld = parseInt(process.env.TICKET_ARCHIVE_DAYS) || 30;
      const result = await ticketService.cleanupOldCompletedTickets(daysOld);
      
      if (result.success) {
        this.stats.totalArchived += result.archivedCount;
        
        console.log(`Completed ticket archival finished: ${result.archivedCount} tickets archived`);
      } else {
        console.error('Completed ticket archival failed:', result.error);
        this.stats.errors.push({
          type: 'archive_cleanup',
          error: result.error,
          timestamp: new Date().toISOString()
        });
      }

      return result;

    } catch (error) {
      console.error('Completed ticket archival error:', error);
      this.stats.errors.push({
        type: 'archive_cleanup',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Cleanup orphaned data
   */
  async cleanupOrphanedData() {
    try {
      console.log('Starting orphaned data cleanup...');
      
      // This would include cleanup of:
      // - Ticket items without parent tickets
      // - Discounts without parent tickets
      // - Audit logs for deleted tickets (older than retention period)
      
      // For now, this is a placeholder for future implementation
      console.log('Orphaned data cleanup completed (placeholder)');
      
      return {
        success: true,
        message: 'Orphaned data cleanup completed'
      };

    } catch (error) {
      console.error('Orphaned data cleanup error:', error);
      this.stats.errors.push({
        type: 'orphaned_cleanup',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Cleanup old statistics and audit logs
   */
  async cleanupOldStatistics() {
    try {
      console.log('Starting statistics cleanup...');
      
      const retentionDays = parseInt(process.env.AUDIT_RETENTION_DAYS) || 90;
      
      // This would include cleanup of:
      // - Old audit log entries
      // - Performance statistics
      // - Error logs
      
      // For now, this is a placeholder for future implementation
      console.log(`Statistics cleanup completed (retention: ${retentionDays} days)`);
      
      return {
        success: true,
        message: 'Statistics cleanup completed'
      };

    } catch (error) {
      console.error('Statistics cleanup error:', error);
      this.stats.errors.push({
        type: 'stats_cleanup',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Run manual cleanup of all types
   */
  async runManualCleanup() {
    try {
      console.log('Starting manual cleanup of all types...');
      
      const results = {
        expired: await this.cleanupExpiredTickets(),
        archived: await this.archiveOldCompletedTickets(),
        orphaned: await this.cleanupOrphanedData(),
        statistics: await this.cleanupOldStatistics()
      };

      const allSuccessful = Object.values(results).every(r => r.success);
      
      return {
        success: allSuccessful,
        results,
        summary: {
          totalCleaned: this.stats.totalCleaned,
          totalArchived: this.stats.totalArchived,
          errors: this.stats.errors.length
        }
      };

    } catch (error) {
      console.error('Manual cleanup error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get cleanup service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeJobs: Array.from(this.jobs.keys()),
      stats: {
        ...this.stats,
        recentErrors: this.stats.errors.slice(-5) // Last 5 errors
      },
      configuration: {
        expiryHours: process.env.TICKET_EXPIRY_HOURS || 24,
        archiveDays: process.env.TICKET_ARCHIVE_DAYS || 30,
        auditRetentionDays: process.env.AUDIT_RETENTION_DAYS || 90,
        timezone: process.env.TIMEZONE || 'UTC'
      }
    };
  }

  /**
   * Clear error history
   */
  clearErrors() {
    this.stats.errors = [];
    console.log('Cleanup service error history cleared');
  }

  /**
   * Get next scheduled run times
   */
  getSchedule() {
    const schedule = {};
    
    this.jobs.forEach((job, name) => {
      // Note: node-cron doesn't provide next run time directly
      // This would need to be calculated based on cron expressions
      schedule[name] = {
        expression: this.getCronExpression(name),
        isRunning: job.running || false
      };
    });

    return schedule;
  }

  /**
   * Get cron expression for a job
   */
  getCronExpression(jobName) {
    const expressions = {
      expiredTickets: '0 * * * *',      // Every hour
      archivedTickets: '0 2 * * *',     // Daily at 2 AM
      orphanedData: '0 3 * * 0',        // Weekly on Sunday at 3 AM
      statsCleanup: '0 4 1 * *'         // Monthly on 1st at 4 AM
    };

    return expressions[jobName] || 'Unknown';
  }
}

// Export singleton instance
module.exports = new TicketCleanupService();
