/**
 * Fulfillment Management Service
 *
 * Handles POS-specific fulfillment workflows including:
 * - Staff fulfillment management interface
 * - Shipping fee calculation and management
 * - Delivery address management
 * - Integration with Shopify FulfillmentOrder API
 * - Staff attribution for fulfillment operations
 */

require("dotenv").config();
const { databaseManager } = require("../config/database-manager");
const { v4: uuidv4 } = require("uuid");
const shopifyService = require("./shopify-service");

class FulfillmentService {
  constructor() {
    if (FulfillmentService.instance) {
      return FulfillmentService.instance;
    }

    this.databaseManager = databaseManager;

    FulfillmentService.instance = this;
  }

  static getInstance() {
    if (!FulfillmentService.instance) {
      FulfillmentService.instance = new FulfillmentService();
    }
    return FulfillmentService.instance;
  }

  /**
   * Create fulfillment record for an order
   * @param {Object} fulfillmentData - Fulfillment information
   * @param {string} staffId - ID of staff member managing fulfillment
   * @returns {Object} Success/error response
   */
  async createFulfillment(fulfillmentData, staffId) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          const fulfillmentId = uuidv4();
          const now = new Date();

          // Insert fulfillment record
          await connection.execute(
            `INSERT INTO fulfillments (
          id, order_id, shopify_order_id, staff_id, 
          delivery_address, delivery_contact_name, delivery_contact_phone,
          delivery_instructions, delivery_method, estimated_delivery_date,
          shipping_fee, shipping_fee_currency, fulfillment_status,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              fulfillmentId,
              fulfillmentData.orderId || null,
              fulfillmentData.shopifyOrderId || null,
              staffId,
              fulfillmentData.deliveryAddress
                ? JSON.stringify(fulfillmentData.deliveryAddress)
                : null,
              fulfillmentData.deliveryContactName || null,
              fulfillmentData.deliveryContactPhone || null,
              fulfillmentData.deliveryInstructions || null,
              fulfillmentData.deliveryMethod || "standard",
              fulfillmentData.estimatedDeliveryDate || null,
              fulfillmentData.shippingFee || 0,
              fulfillmentData.shippingFeeCurrency || "KES",
              "pending",
              now,
              now,
            ]
          );

          // Create shipping fee record if applicable
          if (fulfillmentData.shippingFee && fulfillmentData.shippingFee > 0) {
            await this.createShippingFeeRecord(connection, {
              fulfillmentId,
              orderId: fulfillmentData.orderId,
              feeAmount: fulfillmentData.shippingFee,
              feeType: fulfillmentData.deliveryMethod,
              staffId,
            });
          }

          console.log(`✅ Fulfillment created: ${fulfillmentId}`);

          return {
            success: true,
            fulfillment: {
              id: fulfillmentId,
              orderId: fulfillmentData.orderId,
              status: "pending",
              shippingFee: fulfillmentData.shippingFee || 0,
              deliveryMethod: fulfillmentData.deliveryMethod,
              createdAt: now,
            },
          };
        }
      );
    } catch (error) {
      console.error("Create fulfillment error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update delivery details for an existing fulfillment
   * @param {string} fulfillmentId - Fulfillment ID
   * @param {Object} updateData - Updated delivery information
   * @param {string} staffId - ID of staff member making update
   * @returns {Object} Success/error response
   */
  async updateDeliveryDetails(fulfillmentId, updateData, staffId) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          // Build dynamic update query
          const updateFields = [];
          const updateValues = [];

          if (updateData.deliveryAddress) {
            updateFields.push("delivery_address = ?");
            updateValues.push(JSON.stringify(updateData.deliveryAddress));
          }

          if (updateData.deliveryContactName) {
            updateFields.push("delivery_contact_name = ?");
            updateValues.push(updateData.deliveryContactName);
          }

          if (updateData.deliveryContactPhone) {
            updateFields.push("delivery_contact_phone = ?");
            updateValues.push(updateData.deliveryContactPhone);
          }

          if (updateData.deliveryInstructions) {
            updateFields.push("delivery_instructions = ?");
            updateValues.push(updateData.deliveryInstructions);
          }

          if (updateData.deliveryMethod) {
            updateFields.push("delivery_method = ?");
            updateValues.push(updateData.deliveryMethod);
          }

          if (updateData.estimatedDeliveryDate) {
            updateFields.push("estimated_delivery_date = ?");
            updateValues.push(updateData.estimatedDeliveryDate);
          }

          if (updateData.shippingFee !== undefined) {
            updateFields.push("shipping_fee = ?");
            updateValues.push(updateData.shippingFee);
          }

          updateFields.push("updated_at = ?", "last_updated_by = ?");
          updateValues.push(new Date(), staffId);

          if (updateFields.length === 2) {
            // Only timestamp and staff fields
            return {
              success: false,
              error: "No fields to update",
            };
          }

          // Update fulfillment record
          updateValues.push(fulfillmentId);
          await connection.execute(
            `UPDATE fulfillments SET ${updateFields.join(", ")} WHERE id = ?`,
            updateValues
          );

          // Log the update for audit trail
          await this.logFulfillmentUpdate(
            connection,
            fulfillmentId,
            updateData,
            staffId
          );

          await connection.commit();

          return {
            success: true,
            message: "Delivery details updated successfully",
          };
        }
      );
    } catch (error) {
      console.error("Update delivery details error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Calculate shipping fee based on delivery method and location
   * @param {Object} shippingData - Shipping calculation parameters
   * @returns {Object} Calculated shipping fee
   */
  async calculateShippingFee(shippingData) {
    try {
      // Get shipping rates from database
      const [rates] = await this.databaseManager.executeQuery(
        `SELECT * FROM shipping_rates WHERE delivery_method = ? AND is_active = 1`,
        [shippingData.deliveryMethod || "standard"]
      );

      if (rates.length === 0) {
        return {
          success: false,
          error: "No shipping rates found for delivery method",
        };
      }

      const rate = rates[0];
      let calculatedFee = parseFloat(rate.base_fee);

      // Add distance-based calculation if applicable
      if (rate.per_km_fee && shippingData.distanceKm) {
        calculatedFee += parseFloat(rate.per_km_fee) * shippingData.distanceKm;
      }

      // Add weight-based calculation if applicable
      if (rate.per_kg_fee && shippingData.weightKg) {
        calculatedFee += parseFloat(rate.per_kg_fee) * shippingData.weightKg;
      }

      // Apply minimum fee
      if (rate.minimum_fee && calculatedFee < parseFloat(rate.minimum_fee)) {
        calculatedFee = parseFloat(rate.minimum_fee);
      }

      // Apply maximum fee
      if (rate.maximum_fee && calculatedFee > parseFloat(rate.maximum_fee)) {
        calculatedFee = parseFloat(rate.maximum_fee);
      }

      return {
        success: true,
        shippingFee: Math.round(calculatedFee * 100) / 100, // Round to 2 decimal places
        deliveryMethod: shippingData.deliveryMethod,
        currency: "KES",
        breakdown: {
          baseFee: parseFloat(rate.base_fee),
          distanceFee: rate.per_km_fee
            ? parseFloat(rate.per_km_fee) * (shippingData.distanceKm || 0)
            : 0,
          weightFee: rate.per_kg_fee
            ? parseFloat(rate.per_kg_fee) * (shippingData.weightKg || 0)
            : 0,
        },
      };
    } catch (error) {
      console.error("Calculate shipping fee error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get fulfillment details by ID
   * @param {string} fulfillmentId - Fulfillment ID
   * @returns {Object} Fulfillment details
   */
  async getFulfillmentById(fulfillmentId) {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        `SELECT f.*,
         s.name as staff_name, s.role as staff_role
         FROM fulfillments f
         LEFT JOIN pos_staff s ON f.staff_id = s.id
         WHERE f.id = ?`,
        [fulfillmentId]
      );

      if (rows.length === 0) {
        return {
          success: false,
          error: "Fulfillment not found",
        };
      }

      const fulfillment = rows[0];

      // Parse JSON fields
      if (
        fulfillment.delivery_address &&
        fulfillment.delivery_address !== "null"
      ) {
        try {
          fulfillment.delivery_address = JSON.parse(
            fulfillment.delivery_address
          );
        } catch (e) {
          console.error("Failed to parse delivery_address JSON:", e);
          fulfillment.delivery_address = null;
        }
      } else {
        fulfillment.delivery_address = null;
      }

      return {
        success: true,
        fulfillment,
      };
    } catch (error) {
      console.error("Get fulfillment error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create shipping fee record
   * @private
   */
  async createShippingFeeRecord(connection, feeData) {
    const feeId = uuidv4();

    await connection.execute(
      `INSERT INTO shipping_fees (
        id, fulfillment_id, order_id, fee_amount, fee_type, 
        currency, applied_by_staff_id, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        feeId,
        feeData.fulfillmentId,
        feeData.orderId,
        feeData.feeAmount,
        feeData.feeType,
        "KES",
        feeData.staffId,
        new Date(),
      ]
    );

    return feeId;
  }

  /**
   * Log fulfillment update for audit trail
   * @private
   */
  async logFulfillmentUpdate(connection, fulfillmentId, updateData, staffId) {
    await connection.execute(
      `INSERT INTO fulfillment_audit_log (
        id, fulfillment_id, staff_id, action_type,
        changes_made, created_at
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [
        uuidv4(),
        fulfillmentId,
        staffId,
        "update_delivery_details",
        JSON.stringify(updateData),
        new Date(),
      ]
    );
  }

  /**
   * Get fulfillments for an order
   * @param {string} orderId - Order ID
   * @returns {Object} List of fulfillments
   */
  async getFulfillmentsByOrderId(orderId) {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        `SELECT f.*, s.name as staff_name, s.role as staff_role
         FROM fulfillments f
         LEFT JOIN pos_staff s ON f.staff_id = s.id
         WHERE f.order_id = ?
         ORDER BY f.created_at DESC`,
        [orderId]
      );

      // Parse JSON fields
      const fulfillments = rows.map((fulfillment) => {
        if (
          fulfillment.delivery_address &&
          fulfillment.delivery_address !== "null"
        ) {
          try {
            fulfillment.delivery_address = JSON.parse(
              fulfillment.delivery_address
            );
          } catch (e) {
            console.error("Failed to parse delivery_address JSON:", e);
            fulfillment.delivery_address = null;
          }
        } else {
          fulfillment.delivery_address = null;
        }
        return fulfillment;
      });

      return {
        success: true,
        fulfillments,
      };
    } catch (error) {
      console.error("Get fulfillments by order error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update fulfillment status
   * @param {string} fulfillmentId - Fulfillment ID
   * @param {string} status - New status
   * @param {string} staffId - Staff member updating status
   * @returns {Object} Success/error response
   */
  async updateFulfillmentStatus(fulfillmentId, status, staffId) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          await connection.execute(
            `UPDATE fulfillments
         SET fulfillment_status = ?, updated_at = ?, last_updated_by = ?
         WHERE id = ?`,
            [status, new Date(), staffId, fulfillmentId]
          );

          // Log status change
          await connection.execute(
            `INSERT INTO fulfillment_audit_log (
          id, fulfillment_id, staff_id, action_type,
          changes_made, created_at
        ) VALUES (?, ?, ?, ?, ?, ?)`,
            [
              uuidv4(),
              fulfillmentId,
              staffId,
              "status_update",
              JSON.stringify({ newStatus: status }),
              new Date(),
            ]
          );

          return {
            success: true,
            message: "Fulfillment status updated successfully",
          };
        }
      );
    } catch (error) {
      console.error("Update fulfillment status error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get available shipping rates
   * @returns {Object} List of shipping rates
   */
  async getShippingRates() {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        `SELECT * FROM shipping_rates WHERE is_active = 1 ORDER BY delivery_method`
      );

      return {
        success: true,
        shippingRates: rows,
      };
    } catch (error) {
      console.error("Get shipping rates error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create or update shipping rate
   * @param {Object} rateData - Shipping rate information
   * @param {string} staffId - Staff member creating/updating rate
   * @returns {Object} Success/error response
   */
  async upsertShippingRate(rateData, staffId) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          const rateId = rateData.id || uuidv4();
          const now = new Date();

          await connection.execute(
            `INSERT INTO shipping_rates (
          id, delivery_method, base_fee, per_km_fee, per_kg_fee,
          minimum_fee, maximum_fee, description, is_active,
          created_by_staff_id, updated_by_staff_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          base_fee = VALUES(base_fee),
          per_km_fee = VALUES(per_km_fee),
          per_kg_fee = VALUES(per_kg_fee),
          minimum_fee = VALUES(minimum_fee),
          maximum_fee = VALUES(maximum_fee),
          description = VALUES(description),
          is_active = VALUES(is_active),
          updated_by_staff_id = VALUES(updated_by_staff_id),
          updated_at = VALUES(updated_at)`,
            [
              rateId,
              rateData.deliveryMethod,
              rateData.baseFee,
              rateData.perKmFee || null,
              rateData.perKgFee || null,
              rateData.minimumFee || null,
              rateData.maximumFee || null,
              rateData.description || "",
              rateData.isActive !== false,
              staffId,
              staffId,
              now,
              now,
            ]
          );

          return {
            success: true,
            rateId,
            message: "Shipping rate saved successfully",
          };
        }
      );
    } catch (error) {
      console.error("Upsert shipping rate error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = new FulfillmentService();
