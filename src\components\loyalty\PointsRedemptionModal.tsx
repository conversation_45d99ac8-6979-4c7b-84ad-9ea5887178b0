/**
 * Points Redemption Modal Component
 * Allows customers to redeem loyalty points for discounts
 */

import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import {
  ThemedView,
  ThemedText,
} from "@/src/components/themed/ThemedComponents";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";

interface PointsRedemptionModalProps {
  visible: boolean;
  onClose: () => void;
  onRedeem: (pointsToRedeem: number) => Promise<void>;
  customerLoyalty: {
    loyaltyPoints: number;
    tier: string;
  };
  orderTotal: number;
  pointsToKshRate?: number; // Points to KSh conversion rate
  minRedemption?: number;
  maxRedemption?: number;
}

export const PointsRedemptionModal: React.FC<PointsRedemptionModalProps> = ({
  visible,
  onClose,
  onRedeem,
  customerLoyalty,
  orderTotal,
  pointsToKshRate = 0.01, // 100 points = 1 KSh
  minRedemption = 100,
  maxRedemption = 5000,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const [pointsToRedeem, setPointsToRedeem] = useState("");
  const [isRedeeming, setIsRedeeming] = useState(false);

  const availablePoints = customerLoyalty.loyaltyPoints;
  const maxRedeemablePoints = Math.min(
    availablePoints,
    maxRedemption,
    Math.floor(orderTotal / pointsToKshRate) // Can't redeem more than order total
  );

  const pointsValue = parseInt(pointsToRedeem) || 0;
  const discountAmount = pointsValue * pointsToKshRate;
  const isValidRedemption =
    pointsValue >= minRedemption && pointsValue <= maxRedeemablePoints;

  useEffect(() => {
    if (visible) {
      setPointsToRedeem("");
    }
  }, [visible]);

  const handleQuickSelect = (percentage: number) => {
    const points = Math.floor(maxRedeemablePoints * (percentage / 100));
    setPointsToRedeem(points.toString());
  };

  const handleRedeem = async () => {
    if (!isValidRedemption) {
      Alert.alert(
        "Invalid Redemption",
        `Please enter between ${minRedemption} and ${maxRedeemablePoints.toLocaleString()} points.`
      );
      return;
    }

    try {
      setIsRedeeming(true);
      await onRedeem(pointsValue);
      onClose();
    } catch (error) {
      Alert.alert(
        "Redemption Failed",
        "Unable to redeem points. Please try again."
      );
    } finally {
      setIsRedeeming(false);
    }
  };

  const quickSelectOptions = [
    { label: "25%", value: 25 },
    { label: "50%", value: 50 },
    { label: "75%", value: 75 },
    { label: "Max", value: 100 },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <ThemedView variant="background" style={styles.container}>
        {/* Header */}
        <View
          style={[styles.header, { borderBottomColor: theme.colors.border }]}
        >
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <ThemedText variant="h2">Redeem Points</ThemedText>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Available Points */}
          <ThemedView variant="card" style={[styles.pointsCard, utils.p("lg")]}>
            <View style={styles.pointsHeader}>
              <IconSymbol
                name="star.fill"
                size={24}
                color={theme.colors.primary}
              />
              <ThemedText variant="h3" color="primary">
                Available Points
              </ThemedText>
            </View>
            <ThemedText variant="h1" style={styles.pointsBalance}>
              {availablePoints.toLocaleString()}
            </ThemedText>
            <ThemedText variant="body" color="secondary">
              Worth up to {formatCurrency(availablePoints * pointsToKshRate)}
            </ThemedText>
          </ThemedView>

          {/* Order Summary */}
          <ThemedView
            variant="card"
            style={[styles.summaryCard, utils.p("lg")]}
          >
            <ThemedText variant="h3" style={utils.mb("md")}>
              Order Summary
            </ThemedText>
            <View style={styles.summaryRow}>
              <ThemedText variant="body" color="secondary">
                Order Total
              </ThemedText>
              <ThemedText variant="body">
                {formatCurrency(orderTotal)}
              </ThemedText>
            </View>
            <View style={styles.summaryRow}>
              <ThemedText variant="body" color="secondary">
                Max Redeemable
              </ThemedText>
              <ThemedText variant="body">
                {maxRedeemablePoints.toLocaleString()} points
              </ThemedText>
            </View>
          </ThemedView>

          {/* Points Input */}
          <ThemedView variant="card" style={[styles.inputCard, utils.p("lg")]}>
            <ThemedText variant="h3" style={utils.mb("md")}>
              Points to Redeem
            </ThemedText>

            <View style={styles.inputContainer}>
              <TextInput
                style={[
                  styles.pointsInput,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: isValidRedemption
                      ? theme.colors.border
                      : theme.colors.destructive,
                    color: theme.colors.text,
                  },
                ]}
                value={pointsToRedeem}
                onChangeText={setPointsToRedeem}
                placeholder={`Min ${minRedemption} points`}
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
                maxLength={6}
              />
              <View style={styles.inputSuffix}>
                <ThemedText variant="body" color="secondary">
                  points
                </ThemedText>
              </View>
            </View>

            {/* Quick Select Buttons */}
            <View style={styles.quickSelectContainer}>
              <ThemedText
                variant="small"
                color="secondary"
                style={utils.mb("sm")}
              >
                Quick Select:
              </ThemedText>
              <View style={styles.quickSelectButtons}>
                {quickSelectOptions.map((option) => (
                  <TouchableOpacity
                    key={option.label}
                    style={[
                      styles.quickSelectButton,
                      { borderColor: theme.colors.border },
                    ]}
                    onPress={() => handleQuickSelect(option.value)}
                  >
                    <ThemedText variant="small" color="primary">
                      {option.label}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Validation Message */}
            {pointsToRedeem && !isValidRedemption && (
              <View style={styles.validationContainer}>
                <IconSymbol
                  name="exclamationmark.triangle"
                  size={16}
                  color={theme.colors.destructive}
                />
                <ThemedText
                  variant="small"
                  color="error"
                  style={styles.validationText}
                >
                  {pointsValue < minRedemption
                    ? `Minimum ${minRedemption} points required`
                    : `Maximum ${maxRedeemablePoints.toLocaleString()} points allowed`}
                </ThemedText>
              </View>
            )}
          </ThemedView>

          {/* Discount Preview */}
          {isValidRedemption && (
            <ThemedView
              variant="card"
              style={[styles.previewCard, utils.p("lg")]}
            >
              <View style={styles.previewHeader}>
                <IconSymbol
                  name="tag.fill"
                  size={20}
                  color={theme.colors.success}
                />
                <ThemedText variant="h3" color="success">
                  Discount Preview
                </ThemedText>
              </View>
              <View style={styles.previewContent}>
                <View style={styles.previewRow}>
                  <ThemedText variant="body">
                    {pointsValue.toLocaleString()} points
                  </ThemedText>
                  <ThemedText variant="h3" color="success">
                    -{formatCurrency(discountAmount)}
                  </ThemedText>
                </View>
                <View style={[styles.previewRow, styles.totalRow]}>
                  <ThemedText variant="h3">New Total</ThemedText>
                  <ThemedText variant="h3" color="primary">
                    {formatCurrency(orderTotal - discountAmount)}
                  </ThemedText>
                </View>
              </View>
            </ThemedView>
          )}
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
          <ModernButton
            title="Cancel"
            variant="outline"
            onPress={onClose}
            style={styles.cancelButton}
          />
          <ModernButton
            title={isRedeeming ? "Redeeming..." : "Redeem Points"}
            onPress={handleRedeem}
            disabled={!isValidRedemption || isRedeeming}
            loading={isRedeeming}
            style={styles.redeemButton}
          />
        </View>
      </ThemedView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  pointsCard: {
    alignItems: "center",
    marginBottom: 16,
  },
  pointsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  pointsBalance: {
    marginVertical: 8,
  },
  summaryCard: {
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  inputCard: {
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  pointsInput: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    textAlign: "center",
  },
  inputSuffix: {
    position: "absolute",
    right: 16,
  },
  quickSelectContainer: {
    marginBottom: 16,
  },
  quickSelectButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  quickSelectButton: {
    flex: 1,
    height: 36,
    borderWidth: 1,
    borderRadius: 6,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 4,
  },
  validationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  validationText: {
    marginLeft: 8,
  },
  previewCard: {
    marginBottom: 16,
  },
  previewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  previewContent: {
    gap: 8,
  },
  previewRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalRow: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
  },
  footer: {
    flexDirection: "row",
    padding: 20,
    borderTopWidth: 1,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
  },
  redeemButton: {
    flex: 2,
  },
});
