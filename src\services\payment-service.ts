import {
  CashPayment,
  CURRENCY,
  PAYMENT_METHODS,
  PaymentMethod,
  PaymentRequest,
  PaymentResult,
} from "@/src/types/payment";

export class PaymentService {
  // Get available payment methods
  static getAvailablePaymentMethods(): PaymentMethod[] {
    return PAYMENT_METHODS.filter(
      (method) => method.enabled || method.placeholder
    );
  }

  // Get enabled payment methods only
  static getEnabledPaymentMethods(): PaymentMethod[] {
    return PAYMENT_METHODS.filter((method) => method.enabled);
  }

  // Process cash payment
  static async processCashPayment(
    amount: number,
    amountTendered: number,
    staffInfo: { id: string; name: string; terminal: string },
    customerInfo?: { name: string; phone?: string }
  ): Promise<PaymentResult> {
    try {
      // Validate cash payment
      if (amountTendered < amount) {
        return {
          success: false,
          transactionId: "",
          method: PAYMENT_METHODS.find((m) => m.id === "cash")!,
          amount,
          currency: CURRENCY.code,
          timestamp: new Date().toISOString(),
          error: "Insufficient cash tendered",
        };
      }

      const change = amountTendered - amount;
      const transactionId = `CASH-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      const cashPayment: CashPayment = {
        amountTendered,
        amountDue: amount,
        change,
        validated: true,
        validatedBy: staffInfo.name,
        validatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        transactionId,
        method: PAYMENT_METHODS.find((m) => m.id === "cash")!,
        amount,
        currency: CURRENCY.code,
        timestamp: new Date().toISOString(),
        change, // Add change property
        cashData: cashPayment,
        receiptData: {
          transactionId,
          method: "Cash",
          amount,
          currency: CURRENCY.code,
          timestamp: new Date().toISOString(),
          change,
          customerInfo,
          staffInfo: {
            name: staffInfo.name,
            terminal: staffInfo.terminal,
          },
          locationInfo: {
            name: "Current Location", // Will be populated from context
          },
        },
      };
    } catch (error) {
      console.error("Cash payment processing error:", error);
      return {
        success: false,
        transactionId: "",
        method: PAYMENT_METHODS.find((m) => m.id === "cash")!,
        amount,
        currency: CURRENCY.code,
        timestamp: new Date().toISOString(),
        error: "Payment processing failed",
      };
    }
  }

  // Process ABSA Till payment (handled like cash - immediate processing)
  static async processAbsaTillPayment(
    amount: number,
    tillNumber: string,
    accountNumber: string,
    transactionCode: string,
    staffInfo: { id: string; name: string; terminal: string },
    customerInfo?: { name: string; phone?: string }
  ): Promise<PaymentResult> {
    try {
      // For ABSA Till, we treat it like cash - immediate processing
      // In a real implementation, you might want to validate the till details

      const transactionId = `TILL-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      const referenceNumber = `REF-${Date.now()}`;

      return {
        success: true,
        transactionId,
        method: PAYMENT_METHODS.find((m) => m.id === "absa_till")!,
        amount,
        currency: CURRENCY.code,
        timestamp: new Date().toISOString(),
        reference: referenceNumber,
        absaTillData: {
          tillNumber,
          accountNumber,
          referenceNumber,
          transactionCode,
          customerMessage: "ABSA Till payment processed successfully",
        },
        receiptData: {
          transactionId,
          method: "ABSA Till",
          amount,
          currency: CURRENCY.code,
          timestamp: new Date().toISOString(),
          reference: referenceNumber,
          customerInfo: customerInfo
            ? {
                name: customerInfo.name,
                phone: customerInfo.phone,
              }
            : undefined,
          staffInfo: {
            name: staffInfo.name,
            terminal: staffInfo.terminal,
          },
          locationInfo: {
            name: "Current Location", // This should be populated from location context
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        transactionId: "",
        method: PAYMENT_METHODS.find((m) => m.id === "absa_till")!,
        amount,
        currency: CURRENCY.code,
        timestamp: new Date().toISOString(),
        error:
          error instanceof Error ? error.message : "ABSA Till payment failed",
      };
    }
  }

  // Process Credit payment (handled identically to cash - immediate processing)
  static async processCreditPayment(
    amount: number,
    amountTendered: number,
    staffInfo: { id: string; name: string; terminal: string },
    customerInfo?: { name: string; phone?: string }
  ): Promise<PaymentResult> {
    try {
      // Validate payment amount
      if (amountTendered < amount) {
        return {
          success: false,
          transactionId: "",
          method: PAYMENT_METHODS.find((m) => m.id === "credit")!,
          amount,
          currency: CURRENCY.code,
          timestamp: new Date().toISOString(),
          error: "Insufficient credit amount",
        };
      }

      const change = amountTendered - amount;
      const transactionId = `CREDIT-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      return {
        success: true,
        transactionId,
        method: PAYMENT_METHODS.find((m) => m.id === "credit")!,
        amount,
        currency: CURRENCY.code,
        timestamp: new Date().toISOString(),
        change,
        cashData: {
          amountTendered,
          amountDue: amount,
          change,
          validated: true,
          validatedBy: staffInfo.name,
          validatedAt: new Date().toISOString(),
        },
        receiptData: {
          transactionId,
          method: "Credit",
          amount,
          currency: CURRENCY.code,
          timestamp: new Date().toISOString(),
          change,
          customerInfo: customerInfo
            ? {
                name: customerInfo.name,
                phone: customerInfo.phone,
              }
            : undefined,
          staffInfo: {
            name: staffInfo.name,
            terminal: staffInfo.terminal,
          },
          locationInfo: {
            name: "Current Location", // This should be populated from location context
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        transactionId: "",
        method: PAYMENT_METHODS.find((m) => m.id === "credit")!,
        amount,
        currency: CURRENCY.code,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Credit payment failed",
      };
    }
  }

  // Process card payment (placeholder)
  static async processCardPayment(
    amount: number,
    staffInfo: { id: string; name: string; terminal: string },
    customerInfo?: { name: string; phone?: string }
  ): Promise<PaymentResult> {
    // This is a placeholder - will be implemented with actual card processor
    return {
      success: false,
      transactionId: "",
      method: PAYMENT_METHODS.find((m) => m.id === "card")!,
      amount,
      currency: CURRENCY.code,
      timestamp: new Date().toISOString(),
      error: "Card payment integration coming soon",
    };
  }

  // Validate payment amount
  static validatePaymentAmount(amount: number): {
    valid: boolean;
    error?: string;
  } {
    // Basic validation for payment amounts
    if (typeof amount !== "number" || isNaN(amount)) {
      return { valid: false, error: "Invalid amount" };
    }

    if (amount <= 0) {
      return { valid: false, error: "Amount must be greater than 0" };
    }

    if (amount > 10_000_000) {
      return { valid: false, error: "Amount exceeds maximum limit" };
    }

    return { valid: true };
  }

  // Format currency for display
  static formatCurrency(amount: number): string {
    // Basic KES currency formatting
    if (typeof amount !== "number" || isNaN(amount)) {
      return "KSh 0.00";
    }

    return `KSh ${amount.toLocaleString("en-KE", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  }

  // Calculate change for cash payments
  static calculateChange(amountDue: number, amountTendered: number): number {
    return Math.max(0, amountTendered - amountDue);
  }

  // Validate cash tender amount
  static validateCashTender(
    amountDue: number,
    amountTendered: number
  ): { valid: boolean; error?: string; change?: number } {
    if (amountTendered < amountDue) {
      const shortage = amountDue - amountTendered;
      return {
        valid: false,
        error: `Insufficient amount. Need ${this.formatCurrency(
          shortage
        )} more.`,
      };
    }

    const change = this.calculateChange(amountDue, amountTendered);
    return { valid: true, change };
  }

  // Generate payment reference
  static generatePaymentReference(method: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${method.toUpperCase()}-${timestamp}-${random}`;
  }

  // Check if payment method requires additional info
  static requiresAdditionalInfo(method: PaymentMethod): boolean {
    return method.requiresReference || method.type === "absa_till";
  }

  // Get payment method by ID
  static getPaymentMethodById(id: string): PaymentMethod | undefined {
    return PAYMENT_METHODS.find((method) => method.id === id);
  }

  // Process payment based on method
  static async processPayment(
    paymentRequest: PaymentRequest,
    staffInfo: { id: string; name: string; terminal: string },
    customerInfo?: { name: string; phone?: string },
    additionalData?: any
  ): Promise<PaymentResult> {
    const { method, amount } = paymentRequest;

    // Validate amount
    const amountValidation = this.validatePaymentAmount(amount);
    if (!amountValidation.valid) {
      return {
        success: false,
        transactionId: "",
        method,
        amount,
        currency: CURRENCY.code,
        timestamp: new Date().toISOString(),
        error: amountValidation.error,
      };
    }

    // Route to appropriate payment processor
    switch (method.type) {
      case "cash":
        const amountTendered = additionalData?.amountTendered || amount;
        return this.processCashPayment(
          amount,
          amountTendered,
          staffInfo,
          customerInfo
        );

      case "credit":
        const creditAmountTendered = additionalData?.amountTendered || amount;
        return this.processCreditPayment(
          amount,
          creditAmountTendered,
          staffInfo,
          customerInfo
        );

      case "absa_till":
        const tillNumber = additionalData?.tillNumber || "";
        const accountNumber = additionalData?.accountNumber || "";
        const transactionCode = additionalData?.transactionCode || "";
        return this.processAbsaTillPayment(
          amount,
          tillNumber,
          accountNumber,
          transactionCode,
          staffInfo,
          customerInfo
        );

      case "card":
        return this.processCardPayment(amount, staffInfo, customerInfo);

      default:
        return {
          success: false,
          transactionId: "",
          method,
          amount,
          currency: CURRENCY.code,
          timestamp: new Date().toISOString(),
          error: "Unsupported payment method",
        };
    }
  }

  // ===== SPLIT PAYMENT METHODS =====

  // Validate split payment request
  static validateSplitPayment(
    totalAmount: number,
    payments: any[]
  ): { valid: boolean; error?: string; totalPaymentAmount?: number } {
    if (!payments || payments.length === 0) {
      return { valid: false, error: "At least one payment method is required" };
    }

    if (payments.length > 5) {
      return { valid: false, error: "Maximum 5 payment methods allowed" };
    }

    // Validate total amount
    const amountValidation = this.validatePaymentAmount(totalAmount);
    if (!amountValidation.valid) {
      return { valid: false, error: amountValidation.error };
    }

    // Calculate total payment amount
    const totalPaymentAmount = payments.reduce((sum, payment) => {
      return sum + (payment.amount || 0);
    }, 0);

    // Check if total payments match the required amount
    if (Math.abs(totalPaymentAmount - totalAmount) > 0.01) {
      return {
        valid: false,
        error: `Payment total (${this.formatCurrency(
          totalPaymentAmount
        )}) does not match order total (${this.formatCurrency(totalAmount)})`,
        totalPaymentAmount,
      };
    }

    // Validate each individual payment
    for (let i = 0; i < payments.length; i++) {
      const payment = payments[i];

      if (!payment.paymentMethod) {
        return {
          valid: false,
          error: `Payment ${i + 1}: Payment method is required`,
        };
      }

      if (!payment.amount || payment.amount <= 0) {
        return {
          valid: false,
          error: `Payment ${i + 1}: Amount must be greater than 0`,
        };
      }

      const paymentAmountValidation = this.validatePaymentAmount(
        payment.amount
      );
      if (!paymentAmountValidation.valid) {
        return {
          valid: false,
          error: `Payment ${i + 1}: ${paymentAmountValidation.error}`,
        };
      }

      // Validate cash/credit payments have sufficient tender
      if (
        payment.paymentMethod?.type === "cash" ||
        payment.paymentMethod?.type === "credit"
      ) {
        const amountTendered = payment.amountTendered || payment.amount;
        if (amountTendered < payment.amount) {
          return {
            valid: false,
            error: `Payment ${i + 1}: Insufficient ${
              payment.paymentMethod?.name?.toLowerCase() || "payment"
            } tendered`,
          };
        }
      }

      // ABSA Till transaction code is now optional
      // Payment can proceed without transaction code for manual entry later
      if (payment.paymentMethod?.type === "absa_till") {
        // Optional validation - transaction code can be empty
        const transactionCode = payment.additionalData?.transactionCode;
        if (transactionCode && transactionCode.trim() !== "") {
          // If provided, it should be valid format (basic check)
          if (transactionCode.length < 6) {
            return {
              valid: false,
              error: `Payment ${
                i + 1
              }: ABSA Till transaction code must be at least 6 characters if provided`,
            };
          }
        }
      }
    }

    return { valid: true, totalPaymentAmount };
  }

  // Process split payment
  static async processSplitPayment(
    splitRequest: any,
    staffInfo: { id: string; name: string; terminal: string },
    customerInfo?: { name: string; phone?: string }
  ): Promise<any> {
    try {
      const { totalAmount, payments, currency } = splitRequest;

      // Validate split payment request
      const validation = this.validateSplitPayment(totalAmount, payments);
      if (!validation.valid) {
        return {
          success: false,
          totalAmount,
          payments: [],
          remainingAmount: totalAmount,
          isComplete: false,
          combinedTransactionId: "",
          timestamp: new Date().toISOString(),
          totalChange: 0,
          error: validation.error,
        };
      }

      // Process each payment individually
      const paymentResults: any[] = [];
      let totalChange = 0;
      let allPaymentsSuccessful = true;

      for (let i = 0; i < payments.length; i++) {
        const payment = payments[i];

        try {
          const paymentRequest = {
            method: payment.paymentMethod,
            amount: payment.amount,
            currency: currency || CURRENCY.code,
          };

          const result = await this.processPayment(
            paymentRequest,
            staffInfo,
            customerInfo,
            payment.additionalData
          );

          paymentResults.push(result);

          if (result.success) {
            // Calculate change for cash/credit payments
            if (result.change) {
              totalChange += result.change;
            }
          } else {
            allPaymentsSuccessful = false;
            break; // Stop processing if any payment fails
          }
        } catch (error) {
          allPaymentsSuccessful = false;
          paymentResults.push({
            success: false,
            transactionId: "",
            method: payment.paymentMethod,
            amount: payment.amount,
            currency: currency || CURRENCY.code,
            timestamp: new Date().toISOString(),
            error:
              error instanceof Error
                ? error.message
                : "Payment processing failed",
          });
          break;
        }
      }

      // Generate combined transaction ID
      const combinedTransactionId = `SPLIT-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      const timestamp = new Date().toISOString();

      // Calculate remaining amount (should be 0 if all successful)
      const processedAmount = paymentResults
        .filter((result) => result.success)
        .reduce((sum, result) => sum + result.amount, 0);
      const remainingAmount = totalAmount - processedAmount;

      return {
        success: allPaymentsSuccessful,
        totalAmount,
        payments: paymentResults,
        remainingAmount,
        isComplete: allPaymentsSuccessful && remainingAmount === 0,
        combinedTransactionId,
        timestamp,
        totalChange,
        error: allPaymentsSuccessful
          ? undefined
          : "One or more payments failed",
        receiptData: allPaymentsSuccessful
          ? {
              combinedTransactionId,
              totalAmount,
              totalChange,
              currency: currency || CURRENCY.code,
              timestamp,
              payments: paymentResults.map((result) => ({
                method: result.method.name,
                amount: result.amount,
                transactionId: result.transactionId,
                change: result.change || 0,
              })),
              customerInfo,
              staffInfo: {
                name: staffInfo.name,
                terminal: staffInfo.terminal,
              },
              locationInfo: {
                name: "Current Location",
              },
            }
          : undefined,
      };
    } catch (error) {
      console.error("Split payment processing error:", error);
      return {
        success: false,
        totalAmount: splitRequest.totalAmount,
        payments: [],
        remainingAmount: splitRequest.totalAmount,
        isComplete: false,
        combinedTransactionId: "",
        timestamp: new Date().toISOString(),
        totalChange: 0,
        error:
          error instanceof Error
            ? error.message
            : "Split payment processing failed",
      };
    }
  }

  // Generate split payment summary for receipts/orders
  static generateSplitPaymentSummary(splitResult: any): string {
    if (!splitResult.success || !splitResult.payments) {
      return "Split payment failed";
    }

    const paymentSummary = splitResult.payments
      .map((payment: any) => {
        const changeText =
          payment.change > 0
            ? ` (Change: ${this.formatCurrency(payment.change)})`
            : "";
        return `${payment.method.name}: ${this.formatCurrency(
          payment.amount
        )}${changeText}`;
      })
      .join(" | ");

    return `Split Payment - ${paymentSummary} | Total: ${this.formatCurrency(
      splitResult.totalAmount
    )}`;
  }
}

export default PaymentService;
