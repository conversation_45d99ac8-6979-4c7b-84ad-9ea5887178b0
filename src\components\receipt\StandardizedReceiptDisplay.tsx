/**
 * Standardized Receipt Display Component
 *
 * This component displays receipts using the new standardized format
 * with proper branding, loyalty information, and two-column layout.
 */

import React from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { Spacing, Typography } from "@/constants/Design";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { StandardizedReceiptData } from "@/src/services/StandardizedReceiptService";
import { useTheme, Theme } from "@/src/contexts/ThemeContext";

interface StandardizedReceiptDisplayProps {
  receiptData: StandardizedReceiptData;
  style?: any;
}

export const StandardizedReceiptDisplay: React.FC<
  StandardizedReceiptDisplayProps
> = ({ receiptData, style }) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <ScrollView
      style={[styles.container, style]}
      showsVerticalScrollIndicator={false}
    >
      {/* Store Header */}
      <View style={styles.storeHeader}>
        <Text style={[styles.storeName, { color: theme.colors.text }]}>
          {receiptData.store.name}
        </Text>
        <Text
          style={[styles.storeAddress, { color: theme.colors.textSecondary }]}
        >
          {receiptData.store.address}
        </Text>
        <Text
          style={[styles.storeContact, { color: theme.colors.textSecondary }]}
        >
          {receiptData.store.mobile}
        </Text>
        <Text
          style={[styles.storeContact, { color: theme.colors.textSecondary }]}
        >
          {receiptData.store.email}
        </Text>
      </View>

      <View
        style={[styles.divider, { backgroundColor: theme.colors.border }]}
      />

      {/* Receipt Details */}
      <View style={styles.section}>
        <View style={styles.row}>
          <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
            Receipt No:
          </Text>
          <Text style={[styles.value, { color: theme.colors.text }]}>
            {receiptData.receiptNumber}
          </Text>
        </View>
        <View style={styles.row}>
          <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
            Date:
          </Text>
          <Text style={[styles.value, { color: theme.colors.text }]}>
            {receiptData.date}
          </Text>
        </View>
        <View style={styles.row}>
          <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
            Time:
          </Text>
          <Text style={[styles.value, { color: theme.colors.text }]}>
            {receiptData.time}
          </Text>
        </View>
        <View style={styles.row}>
          <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
            Served by:
          </Text>
          <Text style={[styles.value, { color: theme.colors.text }]}>
            {receiptData.staff.name}
          </Text>
        </View>
      </View>

      <View
        style={[styles.divider, { backgroundColor: theme.colors.border }]}
      />

      {/* Customer Information */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Customer Information
        </Text>
        <View style={styles.row}>
          <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
            Name:
          </Text>
          <Text style={[styles.value, { color: theme.colors.text }]}>
            {receiptData.customer.name}
          </Text>
        </View>
        {receiptData.customer.mobile && (
          <View style={styles.row}>
            <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
              Mobile:
            </Text>
            <Text style={[styles.value, { color: theme.colors.text }]}>
              {receiptData.customer.mobile}
            </Text>
          </View>
        )}
        {receiptData.customer.email && (
          <View style={styles.row}>
            <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
              Email:
            </Text>
            <Text style={[styles.value, { color: theme.colors.text }]}>
              {receiptData.customer.email}
            </Text>
          </View>
        )}
      </View>

      {/* Sales Agent (if applicable) */}
      {receiptData.salesAgent && (
        <>
          <View
            style={[styles.divider, { backgroundColor: theme.colors.border }]}
          />
          <View style={styles.section}>
            <View style={styles.row}>
              <Text
                style={[styles.label, { color: theme.colors.textSecondary }]}
              >
                Sales Agent:
              </Text>
              <Text style={[styles.value, { color: theme.colors.text }]}>
                {receiptData.salesAgent.name}
              </Text>
            </View>
          </View>
        </>
      )}

      <View
        style={[styles.divider, { backgroundColor: theme.colors.border }]}
      />

      {/* Items - Two Column Layout */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Items ({receiptData.totals.itemCount})
        </Text>

        {receiptData.items.map((item, index) => (
          <View key={index} style={styles.itemContainer}>
            <View style={styles.itemRow}>
              <Text style={[styles.itemName, { color: theme.colors.text }]}>
                {item.name}
              </Text>
              <Text style={[styles.itemPrice, { color: theme.colors.text }]}>
                {formatCurrency(item.totalPrice)}
              </Text>
            </View>
            <View style={styles.itemRow}>
              <Text
                style={[
                  styles.itemDetails,
                  { color: theme.colors.textSecondary },
                ]}
              >
                {item.quantity} x {formatCurrency(item.unitPrice)}
                {item.sku && ` • SKU: ${item.sku}`}
              </Text>
              <Text
                style={[
                  styles.itemNumber,
                  { color: theme.colors.textSecondary },
                ]}
              >
                #{item.number}
              </Text>
            </View>
          </View>
        ))}
      </View>

      <View
        style={[styles.divider, { backgroundColor: theme.colors.border }]}
      />

      {/* Totals */}
      <View style={styles.section}>
        <View style={styles.row}>
          <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
            Subtotal:
          </Text>
          <Text style={[styles.value, { color: theme.colors.text }]}>
            {formatCurrency(receiptData.totals.subtotal)}
          </Text>
        </View>
        {receiptData.totals.tax > 0 && (
          <View style={styles.row}>
            <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
              Tax:
            </Text>
            <Text style={[styles.value, { color: theme.colors.text }]}>
              {formatCurrency(receiptData.totals.tax)}
            </Text>
          </View>
        )}
        <View style={[styles.row, styles.totalRow]}>
          <Text style={[styles.totalLabel, { color: theme.colors.text }]}>
            TOTAL:
          </Text>
          <Text style={[styles.totalValue, { color: theme.colors.primary }]}>
            {formatCurrency(receiptData.totals.grandTotal)}
          </Text>
        </View>
      </View>

      <View
        style={[styles.divider, { backgroundColor: theme.colors.border }]}
      />

      {/* Payment Details */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Payment Details
        </Text>
        {receiptData.payment.methods.map((method, index) => (
          <View key={index} style={styles.row}>
            <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
              {method.method}:
            </Text>
            <Text style={[styles.value, { color: theme.colors.text }]}>
              {formatCurrency(method.amount)}
            </Text>
          </View>
        ))}
        {receiptData.payment.change && receiptData.payment.change > 0 && (
          <View style={styles.row}>
            <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
              Change:
            </Text>
            <Text style={[styles.value, { color: theme.colors.text }]}>
              {formatCurrency(receiptData.payment.change)}
            </Text>
          </View>
        )}
      </View>

      {/* Loyalty Information */}
      {receiptData.loyalty && (
        <>
          <View
            style={[styles.divider, { backgroundColor: theme.colors.border }]}
          />
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Loyalty Information
            </Text>
            <View style={styles.row}>
              <Text
                style={[styles.label, { color: theme.colors.textSecondary }]}
              >
                Total TS Points:
              </Text>
              <Text style={[styles.value, { color: theme.colors.primary }]}>
                {receiptData.loyalty.totalPoints.toLocaleString()}
              </Text>
            </View>
          </View>
        </>
      )}

      <View
        style={[styles.divider, { backgroundColor: theme.colors.border }]}
      />

      {/* Footer */}
      <View style={styles.footer}>
        <Text
          style={[styles.footerText, { color: theme.colors.textSecondary }]}
        >
          You&apos;re treasured! Thank you
        </Text>
        <Text
          style={[styles.footerText, { color: theme.colors.textSecondary }]}
        >
          {receiptData.store.address}
        </Text>
      </View>
    </ScrollView>
  );
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    storeHeader: {
      alignItems: "center",
      paddingBottom: Spacing.md,
    },
    storeName: {
      ...Typography.h2,
      fontWeight: "bold",
      marginBottom: 4,
    },
    storeAddress: {
      ...Typography.caption,
      textAlign: "center",
      marginBottom: 2,
    },
    storeContact: {
      ...Typography.caption,
      textAlign: "center",
      marginBottom: 2,
    },
    divider: {
      height: 1,
      marginVertical: Spacing.md,
    },
    section: {
      marginBottom: Spacing.md,
    },
    sectionTitle: {
      ...Typography.bodyMedium,
      fontWeight: "600",
      marginBottom: Spacing.sm,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 4,
    },
    label: {
      ...Typography.body,
      flex: 1,
    },
    value: {
      ...Typography.body,
      textAlign: "right",
      fontWeight: "500",
    },
    itemContainer: {
      marginBottom: Spacing.sm,
      paddingBottom: Spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    itemRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 2,
    },
    itemName: {
      ...Typography.bodyMedium,
      fontWeight: "600",
      flex: 1,
    },
    itemPrice: {
      ...Typography.bodyMedium,
      fontWeight: "600",
      textAlign: "right",
      minWidth: 80,
    },
    itemDetails: {
      ...Typography.caption,
      flex: 1,
    },
    itemNumber: {
      ...Typography.caption,
      textAlign: "right",
    },
    totalRow: {
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: Spacing.sm,
      marginTop: Spacing.sm,
    },
    totalLabel: {
      ...Typography.h3,
      fontWeight: "bold",
    },
    totalValue: {
      ...Typography.h2,
      fontWeight: "bold",
    },
    footer: {
      alignItems: "center",
      marginTop: Spacing.md,
      paddingTop: Spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    footerText: {
      ...Typography.caption,
      textAlign: "center",
      marginBottom: 2,
    },
  });

export default StandardizedReceiptDisplay;
