/**
 * Modern color system for Dukalink POS
 * Inspired by the web design with platinum, pink, and taupe-gray palette
 * Optimized for retail environments with excellent readability
 */

// Primary color palette from web design
const primaryPink = "#d2686f"; // Primary pink from web
const primaryPinkLight = "#e87c83"; // Lighter pink for dark mode
const primaryPinkDark = "#b85a61"; // Darker pink for interactions

// Background colors
const platinumBackground = "#ebe9e9"; // Platinum background from web
const eerieBlack = "#1c1a17"; // Eerie black from web
const pureWhite = "#ffffff"; // Pure white for cards
const pureBlack = "#000000"; // Pure black for dark mode

// Secondary colors
const taupeGray = "#9c9192"; // Taupe gray from web
const lightPinkAccent = "#f9e8e9"; // Light pink accent
const darkPinkAccent = "#4a2c2e"; // Dark pink accent
const mutedGray = "#f5f5f5"; // Muted background

// Status colors optimized for POS
const successGreen = "#10B981";
const warningAmber = "#F59E0B";
const errorRed = "#EF4444";
const infoBlue = "#3B82F6";

export const Colors = {
  light: {
    // Text colors
    text: eerieBlack,
    textSecondary: taupeGray,
    textMuted: "#808080",
    textInverse: pureWhite,

    // Background colors
    background: platinumBackground,
    surface: pureWhite,
    surfaceSecondary: mutedGray,
    overlay: "rgba(28, 26, 23, 0.5)",

    // Border and divider colors
    border: taupeGray,
    borderLight: "#d1d5db",
    divider: "rgba(156, 145, 146, 0.2)",

    // Primary brand colors
    primary: primaryPink,
    primaryDark: primaryPinkDark,
    primaryLight: "#e8a8ad",
    primaryForeground: pureWhite,

    // Secondary colors
    secondary: taupeGray,
    secondaryLight: "#b8aeb0",
    secondaryForeground: pureWhite,

    // Accent colors
    accent: lightPinkAccent,
    accentForeground: eerieBlack,

    // Status colors
    success: successGreen,
    successLight: "#d1fae5",
    warning: warningAmber,
    warningLight: "#fef3c7",
    error: errorRed,
    errorLight: "#fee2e2",
    errorBackground: "#fee2e2",
    info: infoBlue,
    infoLight: "#dbeafe",

    // Interactive elements
    tint: primaryPink,
    icon: taupeGray,
    iconActive: primaryPink,
    tabIconDefault: taupeGray,
    tabIconSelected: primaryPink,

    // Input and form elements
    input: platinumBackground,
    inputBorder: taupeGray,
    inputFocus: primaryPink,
    placeholder: taupeGray,

    // Card and elevation
    card: pureWhite,
    cardBorder: "rgba(156, 145, 146, 0.1)",
    shadow: "rgba(28, 26, 23, 0.1)",
  },
  dark: {
    // Text colors
    text: platinumBackground,
    textSecondary: taupeGray,
    textMuted: "#9ca3af",
    textInverse: eerieBlack,

    // Background colors
    background: pureBlack,
    surface: eerieBlack,
    surfaceSecondary: "#2d2a27",
    overlay: "rgba(235, 233, 233, 0.1)",

    // Border and divider colors
    border: "#374151",
    borderLight: "#4b5563",
    divider: "rgba(156, 145, 146, 0.2)",

    // Primary brand colors
    primary: primaryPinkLight,
    primaryDark: primaryPink,
    primaryLight: "#f0a8ad",
    primaryForeground: pureBlack,

    // Secondary colors
    secondary: taupeGray,
    secondaryLight: "#b8aeb0",
    secondaryForeground: platinumBackground,

    // Accent colors
    accent: darkPinkAccent,
    accentForeground: platinumBackground,

    // Status colors
    success: "#22c55e",
    successLight: "#166534",
    warning: "#eab308",
    warningLight: "#a16207",
    error: "#ef4444",
    errorLight: "#991b1b",
    errorBackground: "#991b1b",
    info: "#3b82f6",
    infoLight: "#1e40af",

    // Interactive elements
    tint: primaryPinkLight,
    icon: taupeGray,
    iconActive: primaryPinkLight,
    tabIconDefault: taupeGray,
    tabIconSelected: primaryPinkLight,

    // Input and form elements
    input: "rgba(235, 233, 233, 0.15)",
    inputBorder: "#4b5563",
    inputFocus: primaryPinkLight,
    placeholder: taupeGray,

    // Card and elevation
    card: eerieBlack,
    cardBorder: "rgba(156, 145, 146, 0.1)",
    shadow: "rgba(0, 0, 0, 0.3)",
  },
};
