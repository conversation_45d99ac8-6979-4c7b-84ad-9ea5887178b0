/**
 * Responsive Polish Utilities for Dukalink POS
 * Fine-tuning and optimization functions for responsive behavior
 */

import { Dimensions, Platform } from 'react-native';
import { ScreenSize } from '@/src/constants/ResponsiveConstants';

export interface ResponsivePolishConfig {
  minTouchTarget: number;
  maxContentWidth: number;
  optimalLineLength: number;
  gridGutterRatio: number;
}

export const RESPONSIVE_POLISH_CONFIG: ResponsivePolishConfig = {
  minTouchTarget: 44, // Minimum touch target size (iOS HIG recommendation)
  maxContentWidth: 1200, // Maximum content width for readability
  optimalLineLength: 75, // Optimal characters per line for readability
  gridGutterRatio: 0.05, // Gutter size as ratio of container width
};

/**
 * Validates touch target sizes for accessibility
 */
export const validateTouchTargets = (elements: Array<{ width: number; height: number; label: string }>) => {
  const issues: string[] = [];
  
  elements.forEach(element => {
    if (element.width < RESPONSIVE_POLISH_CONFIG.minTouchTarget || 
        element.height < RESPONSIVE_POLISH_CONFIG.minTouchTarget) {
      issues.push(`${element.label}: ${element.width}x${element.height} is below minimum ${RESPONSIVE_POLISH_CONFIG.minTouchTarget}px`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues,
  };
};

/**
 * Calculates optimal grid columns based on container width and item constraints
 */
export const calculateOptimalGridColumns = (
  containerWidth: number,
  minItemWidth: number,
  maxItemWidth?: number,
  spacing: number = 16
): number => {
  // Account for spacing between items
  const availableWidth = containerWidth - spacing;
  
  // Calculate maximum possible columns
  const maxColumns = Math.floor(availableWidth / (minItemWidth + spacing));
  
  if (maxItemWidth) {
    // If max width is specified, ensure items don't exceed it
    const minColumnsForMaxWidth = Math.ceil(availableWidth / (maxItemWidth + spacing));
    return Math.max(minColumnsForMaxWidth, Math.min(maxColumns, 6)); // Cap at 6 columns
  }
  
  return Math.min(maxColumns, 6); // Cap at 6 columns for usability
};

/**
 * Optimizes spacing for different screen sizes
 */
export const optimizeSpacing = (baseSpacing: number, screenSize: ScreenSize, context: 'padding' | 'margin' | 'gap') => {
  const multipliers = {
    mobile: {
      padding: 1.0,
      margin: 1.0,
      gap: 1.0,
    },
    tablet: {
      padding: 1.2,
      margin: 1.1,
      gap: 1.15,
    },
    desktop: {
      padding: 1.4,
      margin: 1.2,
      gap: 1.3,
    },
  };
  
  return Math.round(baseSpacing * multipliers[screenSize][context]);
};

/**
 * Calculates responsive font sizes with readability constraints
 */
export const optimizeFontSize = (baseFontSize: number, screenSize: ScreenSize, textType: 'body' | 'heading' | 'caption') => {
  const scales = {
    mobile: {
      body: 1.0,
      heading: 1.0,
      caption: 1.0,
    },
    tablet: {
      body: 1.1,
      heading: 1.15,
      caption: 1.05,
    },
    desktop: {
      body: 1.2,
      heading: 1.3,
      caption: 1.1,
    },
  };
  
  const scaledSize = baseFontSize * scales[screenSize][textType];
  
  // Apply readability constraints
  const constraints = {
    body: { min: 14, max: 18 },
    heading: { min: 16, max: 32 },
    caption: { min: 12, max: 14 },
  };
  
  return Math.max(
    constraints[textType].min,
    Math.min(constraints[textType].max, scaledSize)
  );
};

/**
 * Generates responsive breakpoint media queries for web
 */
export const generateMediaQueries = () => {
  return {
    mobile: '@media (max-width: 767px)',
    tablet: '@media (min-width: 768px) and (max-width: 1023px)',
    desktop: '@media (min-width: 1024px)',
    largeDesktop: '@media (min-width: 1440px)',
  };
};

/**
 * Validates responsive layout performance
 */
export const validateResponsivePerformance = (metrics: {
  renderTime: number;
  layoutShifts: number;
  memoryUsage: number;
}) => {
  const thresholds = {
    renderTime: 100, // ms
    layoutShifts: 0.1, // CLS score
    memoryUsage: 50, // MB
  };
  
  const issues: string[] = [];
  
  if (metrics.renderTime > thresholds.renderTime) {
    issues.push(`Render time ${metrics.renderTime}ms exceeds ${thresholds.renderTime}ms threshold`);
  }
  
  if (metrics.layoutShifts > thresholds.layoutShifts) {
    issues.push(`Layout shifts ${metrics.layoutShifts} exceed ${thresholds.layoutShifts} threshold`);
  }
  
  if (metrics.memoryUsage > thresholds.memoryUsage) {
    issues.push(`Memory usage ${metrics.memoryUsage}MB exceeds ${thresholds.memoryUsage}MB threshold`);
  }
  
  return {
    isOptimal: issues.length === 0,
    issues,
    score: Math.max(0, 100 - (issues.length * 25)), // Simple scoring system
  };
};

/**
 * Optimizes image sizes for responsive layouts
 */
export const optimizeImageSizes = (
  originalWidth: number,
  originalHeight: number,
  containerWidth: number,
  screenSize: ScreenSize
) => {
  const densityMultipliers = {
    mobile: Platform.OS === 'ios' ? 2 : 1.5, // Retina/high-DPI support
    tablet: Platform.OS === 'ios' ? 2 : 1.5,
    desktop: 1.5,
  };
  
  const maxWidth = Math.min(containerWidth, originalWidth);
  const aspectRatio = originalHeight / originalWidth;
  const optimizedHeight = maxWidth * aspectRatio;
  
  return {
    width: Math.round(maxWidth),
    height: Math.round(optimizedHeight),
    densityMultiplier: densityMultipliers[screenSize],
    recommendedSizes: {
      '1x': { width: Math.round(maxWidth), height: Math.round(optimizedHeight) },
      '2x': { width: Math.round(maxWidth * 2), height: Math.round(optimizedHeight * 2) },
      '3x': { width: Math.round(maxWidth * 3), height: Math.round(optimizedHeight * 3) },
    },
  };
};

/**
 * Generates accessibility improvements for responsive layouts
 */
export const generateA11yImprovements = (screenSize: ScreenSize) => {
  return {
    focusIndicatorSize: screenSize === 'mobile' ? 2 : 3,
    minimumContrastRatio: 4.5, // WCAG AA standard
    recommendedFontSizes: {
      minimum: screenSize === 'mobile' ? 16 : 14,
      comfortable: screenSize === 'mobile' ? 18 : 16,
    },
    touchTargetSpacing: screenSize === 'mobile' ? 8 : 12,
    animationDuration: screenSize === 'mobile' ? 200 : 300, // Faster on mobile
  };
};

/**
 * Debug helper to log responsive state
 */
export const logResponsiveState = (state: any) => {
  if (__DEV__) {
    console.group('🎯 Responsive State Debug');
    console.log('Screen Size:', state.screenSize);
    console.log('Dimensions:', Dimensions.get('window'));
    console.log('Platform:', Platform.OS);
    console.log('Spacing Multiplier:', state.spacingMultiplier);
    console.log('Typography Scale:', state.typographyScale);
    console.log('Grid Columns:', state.gridColumns);
    console.groupEnd();
  }
};
