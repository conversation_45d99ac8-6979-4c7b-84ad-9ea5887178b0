import { AnimationTiming } from "@/src/contexts/ThemeContext";
import React, { useEffect } from "react";
import {
  StyleProp,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewProps,
  ViewStyle,
} from "react-native";
import Animated, {
  Easing,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withSpring,
  withTiming,
} from "react-native-reanimated";

// Animation presets based on web CSS animations
const AnimationPresets = {
  fadeIn: {
    duration: AnimationTiming.normal,
    easing: Easing.out(Easing.quad),
  },
  slideIn: {
    duration: AnimationTiming.normal,
    easing: Easing.out(Easing.cubic),
  },
  scale: {
    duration: AnimationTiming.fast,
    easing: Easing.out(Easing.back(1.2)),
  },
  bounce: {
    duration: AnimationTiming.slow,
    easing: Easing.out(Easing.bounce),
  },
  gentle: {
    duration: AnimationTiming.verySlow,
    easing: Easing.out(Easing.quad),
  },
};

// Fade In Animation Component
interface FadeInViewProps extends ViewProps {
  delay?: number;
  duration?: number;
  children: React.ReactNode;
}

export const FadeInView: React.FC<FadeInViewProps> = ({
  children,
  delay = 0,
  duration = AnimationPresets.fadeIn.duration,
  style,
  ...props
}) => {
  const opacity = useSharedValue(0);

  useEffect(() => {
    opacity.value = withTiming(1, {
      duration,
      easing: AnimationPresets.fadeIn.easing,
    });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

// Slide In Animation Component
interface SlideInViewProps extends ViewProps {
  direction?: "left" | "right" | "up" | "down";
  distance?: number;
  delay?: number;
  duration?: number;
  children: React.ReactNode;
}

export const SlideInView: React.FC<SlideInViewProps> = ({
  children,
  direction = "up",
  distance = 20,
  delay = 0,
  duration = AnimationPresets.slideIn.duration,
  style,
  ...props
}) => {
  const translateX = useSharedValue(
    direction === "left" ? -distance : direction === "right" ? distance : 0
  );
  const translateY = useSharedValue(
    direction === "up" ? distance : direction === "down" ? -distance : 0
  );
  const opacity = useSharedValue(0);

  useEffect(() => {
    const animationConfig = {
      duration,
      easing: AnimationPresets.slideIn.easing,
    };

    setTimeout(() => {
      translateX.value = withTiming(0, animationConfig);
      translateY.value = withTiming(0, animationConfig);
      opacity.value = withTiming(1, animationConfig);
    }, delay);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

// Scale In Animation Component
interface ScaleInViewProps extends ViewProps {
  delay?: number;
  duration?: number;
  initialScale?: number;
  children: React.ReactNode;
}

export const ScaleInView: React.FC<ScaleInViewProps> = ({
  children,
  delay = 0,
  duration = AnimationPresets.scale.duration,
  initialScale = 0.8,
  style,
  ...props
}) => {
  const scale = useSharedValue(initialScale);
  const opacity = useSharedValue(0);

  useEffect(() => {
    const animationConfig = {
      duration,
      easing: AnimationPresets.scale.easing,
    };

    setTimeout(() => {
      scale.value = withTiming(1, animationConfig);
      opacity.value = withTiming(1, animationConfig);
    }, delay);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

// Animated Button with Press Effects
interface AnimatedButtonProps extends TouchableOpacityProps {
  scaleOnPress?: boolean;
  hapticFeedback?: boolean;
  children: React.ReactNode;
  buttonStyle?: StyleProp<ViewStyle>;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  scaleOnPress = true,
  hapticFeedback = true,
  onPress,
  style,
  buttonStyle,
  ...props
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const handlePressIn = () => {
    if (scaleOnPress) {
      scale.value = withTiming(0.95, { duration: 100 });
    }
    opacity.value = withTiming(0.8, { duration: 100 });
  };

  const handlePressOut = () => {
    if (scaleOnPress) {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
    }
    opacity.value = withTiming(1, { duration: 150 });
  };

  const handlePress = (event: any) => {
    // Add subtle bounce effect
    scale.value = withSequence(
      withTiming(0.95, { duration: 50 }),
      withSpring(1, { damping: 15, stiffness: 300 })
    );

    if (onPress) {
      onPress(event);
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[animatedStyle, style]}>
      <TouchableOpacity
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        activeOpacity={1}
        {...props}
        style={[buttonStyle]}
      >
        {children}
      </TouchableOpacity>
    </Animated.View>
  );
};

// Animated Card with Hover-like Effects
interface AnimatedCardProps extends ViewProps {
  elevateOnPress?: boolean;
  scaleOnPress?: boolean;
  children: React.ReactNode;
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  elevateOnPress = true,
  scaleOnPress = false,
  style,
  ...props
}) => {
  const scale = useSharedValue(1);
  const elevation = useSharedValue(2);

  const handlePressIn = () => {
    if (scaleOnPress) {
      scale.value = withTiming(1.02, { duration: 200 });
    }
    if (elevateOnPress) {
      elevation.value = withTiming(8, { duration: 200 });
    }
  };

  const handlePressOut = () => {
    if (scaleOnPress) {
      scale.value = withTiming(1, { duration: 200 });
    }
    if (elevateOnPress) {
      elevation.value = withTiming(2, { duration: 200 });
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    elevation: elevation.value,
    shadowOpacity: interpolate(elevation.value, [2, 8], [0.1, 0.25]),
    shadowRadius: interpolate(elevation.value, [2, 8], [2, 8]),
    shadowOffset: {
      width: 0,
      height: interpolate(elevation.value, [2, 8], [1, 4]),
    },
  }));

  return (
    <Animated.View
      style={[animatedStyle, style]}
      onTouchStart={handlePressIn}
      onTouchEnd={handlePressOut}
      {...props}
    >
      {children}
    </Animated.View>
  );
};

// Staggered Animation Container
interface StaggeredAnimationProps extends ViewProps {
  children: React.ReactNode;
  staggerDelay?: number;
  animationType?: "fadeIn" | "slideIn" | "scaleIn";
}

export const StaggeredAnimation: React.FC<StaggeredAnimationProps> = ({
  children,
  staggerDelay = 100,
  animationType = "fadeIn",
  style,
  ...props
}) => {
  const childrenArray = React.Children.toArray(children);

  const renderAnimatedChild = (child: React.ReactNode, index: number) => {
    const delay = index * staggerDelay;

    switch (animationType) {
      case "slideIn":
        return (
          <SlideInView key={index} delay={delay}>
            {child}
          </SlideInView>
        );
      case "scaleIn":
        return (
          <ScaleInView key={index} delay={delay}>
            {child}
          </ScaleInView>
        );
      default:
        return (
          <FadeInView key={index} delay={delay}>
            {child}
          </FadeInView>
        );
    }
  };

  return (
    <View style={style} {...props}>
      {childrenArray.map(renderAnimatedChild)}
    </View>
  );
};

// Pulse Animation Component
interface PulseViewProps extends ViewProps {
  pulseScale?: number;
  pulseDuration?: number;
  children: React.ReactNode;
}

export const PulseView: React.FC<PulseViewProps> = ({
  children,
  pulseScale = 1.05,
  pulseDuration = 1000,
  style,
  ...props
}) => {
  const scale = useSharedValue(1);

  useEffect(() => {
    const pulse = () => {
      scale.value = withSequence(
        withTiming(pulseScale, { duration: pulseDuration / 2 }),
        withTiming(1, { duration: pulseDuration / 2 })
      );
    };

    pulse();
    const interval = setInterval(pulse, pulseDuration);

    return () => clearInterval(interval);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};
