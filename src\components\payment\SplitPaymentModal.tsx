/**
 * Split Payment Modal Component
 *
 * Centered modal with 3-step workflow for split payment processing:
 * Step 1: Payment Method Selection with amount specification
 * Step 2: Payment Method Configuration with method-specific inputs
 * Step 3: Final Breakdown Summary before processing
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  Platform,
  Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/contexts/ThemeContext";
import { PaymentMethod, PAYMENT_METHODS } from "@/src/types/payment";
import { formatCurrency } from "@/src/utils/currencyUtils";

export interface SplitMethodSelection {
  method: PaymentMethod;
  amount: number;
  selected: boolean;
  config?: any;
}

export interface SplitPaymentModalProps {
  visible: boolean;
  totalAmount: number;
  currency?: string;
  onComplete: (result: SplitPaymentResult) => void;
  onCancel: () => void;
  customerName?: string;
}

export interface SplitPaymentResult {
  success: boolean;
  methods: SplitMethodSelection[];
  totalAmount: number;
  transactionId?: string;
  error?: string;
}

type SplitPaymentStep = 1 | 2 | 3;

const SplitPaymentModal: React.FC<SplitPaymentModalProps> = ({
  visible,
  totalAmount,
  currency = "KES",
  onComplete,
  onCancel,
  customerName,
}) => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState<SplitPaymentStep>(1);
  const [selectedMethods, setSelectedMethods] = useState<
    SplitMethodSelection[]
  >([]);
  const [paymentConfigs, setPaymentConfigs] = useState<Record<string, any>>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Debug logging for modal dimensions
  useEffect(() => {
    if (visible) {
      console.log(`📱 Split Payment Modal: Using fixed 80% height, centered`);
    }
  }, [visible]);

  // Initialize payment configurations when moving to step 2
  useEffect(() => {
    if (currentStep === 2) {
      const selectedForConfig = selectedMethods.filter((m) => m.selected);

      selectedForConfig.forEach((methodSelection) => {
        const methodId = methodSelection.method.id;

        // Only initialize if not already configured
        if (!paymentConfigs[methodId]) {
          let defaultConfig = {};

          switch (methodSelection.method.type) {
            case "cash":
              defaultConfig = { amountTendered: methodSelection.amount };
              break;
            case "credit":
              defaultConfig = { approved: true };
              break;
            case "mpesa":
            case "absa_till":
              // These require user input, so no defaults
              defaultConfig = {};
              break;
            default:
              defaultConfig = {};
          }

          setPaymentConfigs((prev) => ({
            ...prev,
            [methodId]: defaultConfig,
          }));

          console.log(
            `🔧 Initialized config for ${methodSelection.method.name}:`,
            {
              methodId,
              amount: methodSelection.amount,
              defaultConfig,
            }
          );
        }
      });
    }
  }, [currentStep, selectedMethods, paymentConfigs]);

  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      setCurrentStep(1);
      setSelectedMethods([]);
      setPaymentConfigs({});
      setIsProcessing(false);
      setError(null);
    }
  }, [visible]);

  // Get available payment methods for split payment
  const getAvailablePaymentMethods = () => {
    return PAYMENT_METHODS.filter(
      (method) =>
        method.enabled && !method.placeholder && method.type !== "card" // Exclude card for now
    );
  };

  // Calculate total of selected amounts
  const getSelectedTotal = () => {
    return selectedMethods
      .filter((method) => method.selected)
      .reduce((total, method) => total + method.amount, 0);
  };

  // Check if amounts are valid for proceeding
  const isStep1Valid = () => {
    const selectedCount = selectedMethods.filter((m) => m.selected).length;
    const total = getSelectedTotal();

    // Use Math.abs to handle floating-point precision issues
    const isAmountValid = Math.abs(total - totalAmount) < 0.01;

    console.log(`🔍 Split Payment Step 1 Validation:`, {
      selectedCount,
      total,
      totalAmount,
      difference: total - totalAmount,
      isAmountValid,
      isValid: selectedCount >= 2 && isAmountValid,
    });

    return selectedCount >= 2 && isAmountValid;
  };

  // Check if Step 2 configurations are valid
  const isStep2Valid = () => {
    const selectedForConfig = selectedMethods.filter((m) => m.selected);
    const validationResults = selectedForConfig.map((method) => {
      const config = paymentConfigs[method.method.id] || {};
      let isValid = false;

      switch (method.method.type) {
        case "cash":
          isValid =
            config.amountTendered && config.amountTendered >= method.amount;
          break;
        case "mpesa":
          // M-Pesa is valid with or without transaction code
          isValid = true;
          break;
        case "credit":
          isValid = true; // Credit is always valid if customer is selected
          break;
        case "absa_till":
          // ABSA Till is valid with or without transaction code
          isValid = true;
          break;
        default:
          isValid = true;
      }

      // Debug logging
      console.log(`🔍 Split Payment Validation - ${method.method.name}:`, {
        type: method.method.type,
        amount: method.amount,
        config,
        isValid,
      });

      return isValid;
    });

    const allValid = validationResults.every(Boolean);
    console.log(`🔍 Split Payment Step 2 Valid: ${allValid}`, {
      validationResults,
    });

    return allValid;
  };

  // Complete split payment configuration (no actual processing)
  const completeSplitPaymentConfig = () => {
    const selectedForProcessing = selectedMethods.filter((m) => m.selected);

    const result: SplitPaymentResult = {
      success: true,
      methods: selectedForProcessing.map((method) => ({
        ...method,
        config: paymentConfigs[method.method.id] || {},
      })),
      totalAmount,
      transactionId: null, // No transaction ID yet - will be created during order placement
    };

    onComplete(result);
  };

  // Handle method selection toggle
  const handleMethodToggle = (methodId: string) => {
    setSelectedMethods((prev) =>
      prev.map((method) =>
        method.method.id === methodId
          ? {
              ...method,
              selected: !method.selected,
              // Keep the current amount when selecting, reset to 0 when deselecting
              amount: method.selected ? 0 : method.amount || 0,
            }
          : method
      )
    );
  };

  // Handle amount change for a method
  const handleAmountChange = (methodId: string, amount: number) => {
    setSelectedMethods((prev) =>
      prev.map((method) =>
        method.method.id === methodId
          ? { ...method, amount: Math.max(0, amount) }
          : method
      )
    );
  };

  // Initialize methods when modal opens
  useEffect(() => {
    if (visible && selectedMethods.length === 0) {
      const availableMethods = getAvailablePaymentMethods();
      setSelectedMethods(
        availableMethods.map((method) => ({
          method,
          amount: 0,
          selected: false,
        }))
      );
    }
  }, [visible]);

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {[1, 2, 3].map((step) => (
        <View key={step} style={styles.stepIndicatorContainer}>
          <View
            style={[
              styles.stepCircle,
              {
                backgroundColor:
                  currentStep >= step
                    ? theme.colors.primary
                    : theme.colors.border,
              },
            ]}
          >
            <Text
              style={[
                styles.stepNumber,
                {
                  color:
                    currentStep >= step
                      ? theme.colors.background
                      : theme.colors.textSecondary,
                },
              ]}
            >
              {step}
            </Text>
          </View>
          {step < 3 && (
            <View
              style={[
                styles.stepLine,
                {
                  backgroundColor:
                    currentStep > step
                      ? theme.colors.primary
                      : theme.colors.border,
                },
              ]}
            />
          )}
        </View>
      ))}
    </View>
  );

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Select Payment Methods
      </Text>
      <Text
        style={[styles.stepSubtitle, { color: theme.colors.textSecondary }]}
      >
        Choose methods and specify amounts (minimum 2 methods required)
      </Text>

      <View style={styles.totalDisplay}>
        <Text
          style={[styles.totalLabel, { color: theme.colors.textSecondary }]}
        >
          Total Amount: {formatCurrency(totalAmount)}
        </Text>
        <Text style={[styles.totalSelected, { color: theme.colors.primary }]}>
          Selected: {formatCurrency(getSelectedTotal())}
        </Text>
        <Text
          style={[
            styles.totalRemaining,
            {
              color:
                Math.abs(getSelectedTotal() - totalAmount) < 0.01
                  ? theme.colors.success
                  : theme.colors.warning,
            },
          ]}
        >
          Remaining: {formatCurrency(totalAmount - getSelectedTotal())}
        </Text>
      </View>

      <ScrollView style={styles.methodsList}>
        {selectedMethods.map((methodSelection) => (
          <View
            key={methodSelection.method.id}
            style={[
              styles.methodSelectionCard,
              {
                backgroundColor: theme.colors.card,
                borderColor: methodSelection.selected
                  ? theme.colors.primary
                  : theme.colors.border,
                borderWidth: methodSelection.selected ? 2 : 1,
              },
            ]}
          >
            <TouchableOpacity
              style={styles.methodHeader}
              onPress={() => handleMethodToggle(methodSelection.method.id)}
            >
              <View style={styles.methodInfo}>
                <Text style={styles.methodIcon}>
                  {methodSelection.method.icon}
                </Text>
                <Text style={[styles.methodName, { color: theme.colors.text }]}>
                  {methodSelection.method.name}
                </Text>
              </View>
              <Ionicons
                name={methodSelection.selected ? "checkbox" : "square-outline"}
                size={24}
                color={
                  methodSelection.selected
                    ? theme.colors.primary
                    : theme.colors.textSecondary
                }
              />
            </TouchableOpacity>

            {methodSelection.selected && (
              <View style={styles.amountInputContainer}>
                <Text
                  style={[styles.amountLabel, { color: theme.colors.text }]}
                >
                  Amount:
                </Text>
                <View
                  style={[
                    styles.amountInputWrapper,
                    { borderColor: theme.colors.border },
                  ]}
                >
                  <Text
                    style={[
                      styles.currencySymbol,
                      { color: theme.colors.textSecondary },
                    ]}
                  >
                    KSh
                  </Text>
                  <TextInput
                    style={[styles.amountInput, { color: theme.colors.text }]}
                    value={
                      methodSelection.amount > 0
                        ? methodSelection.amount.toString()
                        : ""
                    }
                    onChangeText={(text) => {
                      const amount = parseFloat(text) || 0;
                      handleAmountChange(methodSelection.method.id, amount);
                    }}
                    placeholder="0"
                    placeholderTextColor={theme.colors.textSecondary}
                    keyboardType="numeric"
                  />
                </View>
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );

  const renderStep2 = () => {
    const selectedForConfig = selectedMethods.filter((m) => m.selected);

    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
          Configure Payment Methods
        </Text>
        <Text
          style={[styles.stepSubtitle, { color: theme.colors.textSecondary }]}
        >
          Enter details for each selected payment method
        </Text>

        <ScrollView style={styles.configMethodsList}>
          {selectedForConfig.map((methodSelection, index) => (
            <View
              key={methodSelection.method.id}
              style={[
                styles.configMethodCard,
                {
                  backgroundColor: theme.colors.card,
                  borderColor: theme.colors.border,
                },
              ]}
            >
              <View style={styles.configMethodHeader}>
                <View style={styles.methodInfo}>
                  <Text style={styles.methodIcon}>
                    {methodSelection.method.icon}
                  </Text>
                  <Text
                    style={[styles.methodName, { color: theme.colors.text }]}
                  >
                    {methodSelection.method.name}
                  </Text>
                  {/* Validation status indicator */}
                  {(() => {
                    const config =
                      paymentConfigs[methodSelection.method.id] || {};
                    let isValid = false;

                    switch (methodSelection.method.type) {
                      case "cash":
                        isValid =
                          config.amountTendered &&
                          config.amountTendered >= methodSelection.amount;
                        break;
                      case "mpesa":
                        // M-Pesa is valid with or without transaction code
                        isValid = true;
                        break;
                      case "credit":
                        isValid = true;
                        break;
                      case "absa_till":
                        // ABSA Till is valid with or without transaction code
                        isValid = true;
                        break;
                      default:
                        isValid = true;
                    }

                    return (
                      <Ionicons
                        name={isValid ? "checkmark-circle" : "alert-circle"}
                        size={20}
                        color={
                          isValid ? theme.colors.success : theme.colors.warning
                        }
                        style={{ marginLeft: 8 }}
                      />
                    );
                  })()}
                </View>
                <Text
                  style={[
                    styles.configMethodAmount,
                    { color: theme.colors.primary },
                  ]}
                >
                  {formatCurrency(methodSelection.amount)}
                </Text>
              </View>

              <View style={styles.configMethodContent}>
                {renderMethodSpecificConfig(methodSelection)}
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderMethodSpecificConfig = (
    methodSelection: SplitMethodSelection
  ) => {
    const methodType = methodSelection.method.type;
    const methodId = methodSelection.method.id;

    switch (methodType) {
      case "cash":
        return (
          <View style={styles.methodConfigContainer}>
            <Text style={[styles.configLabel, { color: theme.colors.text }]}>
              Amount Tendered:
            </Text>
            <View
              style={[
                styles.configInputWrapper,
                { borderColor: theme.colors.border },
              ]}
            >
              <Text
                style={[
                  styles.currencySymbol,
                  { color: theme.colors.textSecondary },
                ]}
              >
                KSh
              </Text>
              <TextInput
                style={[styles.configInput, { color: theme.colors.text }]}
                value={
                  paymentConfigs[methodId]?.amountTendered?.toString() ||
                  methodSelection.amount.toString()
                }
                onChangeText={(text) => {
                  const amount = parseFloat(text) || methodSelection.amount;
                  setPaymentConfigs((prev) => ({
                    ...prev,
                    [methodId]: { ...prev[methodId], amountTendered: amount },
                  }));
                }}
                onFocus={() => {
                  // Ensure amountTendered is initialized when field is focused
                  if (!paymentConfigs[methodId]?.amountTendered) {
                    setPaymentConfigs((prev) => ({
                      ...prev,
                      [methodId]: {
                        ...prev[methodId],
                        amountTendered: methodSelection.amount,
                      },
                    }));
                  }
                }}
                placeholder={methodSelection.amount.toString()}
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />
            </View>
          </View>
        );

      case "mpesa":
        return (
          <View style={styles.methodConfigContainer}>
            <TouchableOpacity
              style={[
                styles.stkPushButton,
                { backgroundColor: theme.colors.primary },
              ]}
              onPress={() => {
                Alert.alert(
                  "STK Push",
                  "STK Push initiated for M-Pesa payment"
                );
                setPaymentConfigs((prev) => ({
                  ...prev,
                  [methodId]: { ...prev[methodId], stkPushSent: true },
                }));
              }}
            >
              <Ionicons
                name="phone-portrait"
                size={20}
                color={theme.colors.background}
              />
              <Text
                style={[styles.stkPushText, { color: theme.colors.background }]}
              >
                Send STK Push
              </Text>
            </TouchableOpacity>

            <Text
              style={[styles.orText, { color: theme.colors.textSecondary }]}
            >
              OR
            </Text>

            <Text style={[styles.configLabel, { color: theme.colors.text }]}>
              Transaction Code:
            </Text>
            <TextInput
              style={[
                styles.configInputFull,
                {
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                },
              ]}
              value={paymentConfigs[methodId]?.transactionCode || ""}
              onChangeText={(text) => {
                setPaymentConfigs((prev) => ({
                  ...prev,
                  [methodId]: { ...prev[methodId], transactionCode: text },
                }));
              }}
              placeholder="Enter M-Pesa transaction code"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        );

      case "credit":
        return (
          <View style={styles.methodConfigContainer}>
            <View style={styles.creditInfoDisplay}>
              <Text
                style={[
                  styles.configLabel,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Customer: {customerName || "No customer selected"}
              </Text>
              <Text
                style={[
                  styles.configLabel,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Credit Limit: {formatCurrency(50000)}
              </Text>
              <Text
                style={[styles.configLabel, { color: theme.colors.success }]}
              >
                ✓ Credit payment approved
              </Text>
            </View>
          </View>
        );

      case "absa_till":
        return (
          <View style={styles.methodConfigContainer}>
            <Text
              style={[
                styles.configLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Till Number: 123456
            </Text>
            <Text style={[styles.configLabel, { color: theme.colors.text }]}>
              Transaction Code:
            </Text>
            <TextInput
              style={[
                styles.configInputFull,
                {
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                },
              ]}
              value={paymentConfigs[methodId]?.transactionCode || ""}
              onChangeText={(text) => {
                setPaymentConfigs((prev) => ({
                  ...prev,
                  [methodId]: { ...prev[methodId], transactionCode: text },
                }));
              }}
              placeholder="Enter ABSA transaction code"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        );

      default:
        return null;
    }
  };

  const renderStep3 = () => {
    const selectedForProcessing = selectedMethods.filter((m) => m.selected);

    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
          Payment Configuration Summary
        </Text>
        <Text
          style={[styles.stepSubtitle, { color: theme.colors.textSecondary }]}
        >
          Review your split payment configuration. Payment will be processed
          when you place the order.
        </Text>

        <View
          style={[
            styles.summaryContainer,
            { backgroundColor: theme.colors.card },
          ]}
        >
          <Text style={[styles.summaryTotal, { color: theme.colors.primary }]}>
            Total Amount: {formatCurrency(totalAmount)}
          </Text>

          <View style={styles.summaryMethodsList}>
            {selectedForProcessing.map((methodSelection) => (
              <View
                key={methodSelection.method.id}
                style={[
                  styles.summaryMethodCard,
                  { borderColor: theme.colors.border },
                ]}
              >
                <View style={styles.summaryMethodHeader}>
                  <Text style={styles.methodIcon}>
                    {methodSelection.method.icon}
                  </Text>
                  <Text
                    style={[styles.methodName, { color: theme.colors.text }]}
                  >
                    {methodSelection.method.name}
                  </Text>
                  <Text
                    style={[
                      styles.summaryMethodAmount,
                      { color: theme.colors.primary },
                    ]}
                  >
                    {formatCurrency(methodSelection.amount)}
                  </Text>
                </View>

                {renderSummaryDetails(methodSelection)}
              </View>
            ))}
          </View>
        </View>
      </View>
    );
  };

  const renderSummaryDetails = (methodSelection: SplitMethodSelection) => {
    const methodId = methodSelection.method.id;
    const config = paymentConfigs[methodId] || {};

    switch (methodSelection.method.type) {
      case "cash":
        return (
          <Text
            style={[
              styles.summaryDetail,
              { color: theme.colors.textSecondary },
            ]}
          >
            Amount Tendered:{" "}
            {formatCurrency(config.amountTendered || methodSelection.amount)}
          </Text>
        );
      case "mpesa":
        return (
          <Text
            style={[
              styles.summaryDetail,
              { color: theme.colors.textSecondary },
            ]}
          >
            {config.stkPushSent
              ? "STK Push Sent"
              : config.transactionCode
              ? `Code: ${config.transactionCode}`
              : "Pending configuration"}
          </Text>
        );
      case "credit":
        return (
          <Text
            style={[
              styles.summaryDetail,
              { color: theme.colors.textSecondary },
            ]}
          >
            Customer: {customerName || "Unknown"}
          </Text>
        );
      case "absa_till":
        return (
          <Text
            style={[
              styles.summaryDetail,
              { color: theme.colors.textSecondary },
            ]}
          >
            {config.transactionCode
              ? `Code: ${config.transactionCode}`
              : "Pending transaction code"}
          </Text>
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.backdrop}>
        <View
          style={[
            styles.modalContainer,
            {
              backgroundColor: theme.colors.background,
              height: "80%", // Fixed 80% height
              width: Platform.OS === "web" ? "90%" : "95%",
              maxWidth: 500,
            },
          ]}
        >
          <View
            style={[styles.header, { borderBottomColor: theme.colors.border }]}
          >
            <TouchableOpacity onPress={onCancel} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Split Payment
            </Text>
            <View style={styles.placeholder} />
          </View>

          {renderStepIndicator()}

          {error && (
            <View
              style={[
                styles.errorContainer,
                { backgroundColor: theme.colors.errorBackground },
              ]}
            >
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                {error}
              </Text>
            </View>
          )}

          <ScrollView
            style={styles.content}
            contentContainerStyle={styles.contentContainer}
            showsVerticalScrollIndicator={true}
            bounces={false}
          >
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
          </ScrollView>

          <View
            style={[styles.footer, { borderTopColor: theme.colors.border }]}
          >
            {currentStep > 1 && (
              <TouchableOpacity
                style={[
                  styles.footerButton,
                  styles.backButton,
                  { borderColor: theme.colors.border },
                ]}
                onPress={() =>
                  setCurrentStep(
                    (prev) => Math.max(1, prev - 1) as SplitPaymentStep
                  )
                }
              >
                <Text
                  style={[styles.backButtonText, { color: theme.colors.text }]}
                >
                  Back
                </Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[
                styles.footerButton,
                styles.nextButton,
                {
                  backgroundColor:
                    (currentStep === 1 && isStep1Valid()) ||
                    (currentStep === 2 && isStep2Valid()) ||
                    currentStep === 3
                      ? theme.colors.primary
                      : theme.colors.border,
                },
              ]}
              onPress={() => {
                if (currentStep === 1 && isStep1Valid()) {
                  setCurrentStep(2);
                } else if (currentStep === 2 && isStep2Valid()) {
                  setCurrentStep(3);
                } else if (currentStep === 3) {
                  completeSplitPaymentConfig();
                }
              }}
              disabled={
                (currentStep === 1 && !isStep1Valid()) ||
                (currentStep === 2 && !isStep2Valid())
              }
            >
              <Text
                style={[
                  styles.nextButtonText,
                  {
                    color:
                      (currentStep === 1 && isStep1Valid()) ||
                      (currentStep === 2 && isStep2Valid()) ||
                      currentStep === 3
                        ? theme.colors.background
                        : theme.colors.textSecondary,
                  },
                ]}
              >
                {currentStep === 3 ? "Confirm Split Payment" : "Next"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: Platform.OS === "web" ? 20 : 5,
  },
  modalContainer: {
    width: "100%",
    maxWidth: 500,
    // maxHeight and minHeight are set dynamically via inline styles
    borderRadius: 16,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    display: "flex",
    flexDirection: "column",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  placeholder: {
    width: 40,
  },
  stepIndicator: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  stepIndicatorContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: "bold",
  },
  stepLine: {
    width: 40,
    height: 2,
    marginHorizontal: 8,
  },
  errorContainer: {
    padding: 12,
    margin: 20,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
    textAlign: "center",
  },
  content: {
    flex: 1,
    minHeight: 0, // Important for nested ScrollView to work properly
  },
  contentContainer: {
    flexGrow: 1,
  },
  stepContainer: {
    flex: 1,
    padding: Platform.OS === "web" ? 20 : 15,
    display: "flex",
    flexDirection: "column",
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  stepSubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 24,
  },
  totalDisplay: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: "center",
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  totalSelected: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 4,
  },
  totalRemaining: {
    fontSize: 14,
    fontWeight: "500",
  },
  methodsList: {
    flexGrow: 1,
    flexShrink: 1,
    minHeight: 200, // Ensure minimum scroll area
  },
  methodSelectionCard: {
    borderRadius: 12,
    borderWidth: 1, // Default border width, will be overridden for selected items
    marginBottom: 12,
    padding: 16,
  },
  methodHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  methodInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  methodIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  methodName: {
    fontSize: 16,
    fontWeight: "600",
  },
  amountInputContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  amountLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  amountInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  currencySymbol: {
    fontSize: 16,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  footerButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 100,
    alignItems: "center",
  },
  backButton: {
    borderWidth: 1,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  nextButton: {
    flex: 1,
    marginLeft: 12,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  // Step 2 Configuration Styles
  configMethodsList: {
    flexGrow: 1,
    flexShrink: 1,
    minHeight: 250, // Ensure minimum scroll area for configuration
  },
  configMethodCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    padding: 16,
  },
  configMethodHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  configMethodAmount: {
    fontSize: 16,
    fontWeight: "bold",
  },
  configMethodContent: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  methodConfigContainer: {
    gap: 12,
  },
  configLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  configInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  configInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
  },
  configInputFull: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  stkPushButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  stkPushText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 8,
  },
  orText: {
    fontSize: 12,
    textAlign: "center",
    marginVertical: 8,
    fontWeight: "500",
  },
  creditInfoDisplay: {
    gap: 8,
  },
  // Step 3 Summary Styles
  summaryContainer: {
    borderRadius: 12,
    padding: 20,
    marginTop: 16,
  },
  summaryTotal: {
    fontSize: 20,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 20,
  },
  summaryMethodsList: {
    gap: 12,
  },
  summaryMethodCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  summaryMethodHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  summaryMethodAmount: {
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: "auto",
  },
  summaryDetail: {
    fontSize: 14,
    marginLeft: 36,
  },
});

export default SplitPaymentModal;
