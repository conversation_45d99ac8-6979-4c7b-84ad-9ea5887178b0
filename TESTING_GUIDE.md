# Critical Issues Testing Guide

## Issue 1: App Layout Overflow After App Switching

### 🧪 Test Scenarios

#### Test 1: Basic App Switching
1. **Setup:** Open Dukalink POS app
2. **Action:** Switch to another app (e.g., Settings, Messages)
3. **Action:** Return to Dukalink POS app
4. **Expected:** App content should NOT overflow into status bar or navigation bar
5. **Expected:** Safe areas should be properly maintained

#### Test 2: Multiple App Switches
1. **Setup:** Open Dukalink POS app
2. **Action:** Switch between 3-4 different apps multiple times
3. **Action:** Return to Dukalink POS app
4. **Expected:** Layout should remain consistent
5. **Expected:** No content should be hidden behind system UI

#### Test 3: Device Rotation During App Switch
1. **Setup:** Open Dukalink POS app in portrait
2. **Action:** Switch to another app
3. **Action:** Rotate device to landscape
4. **Action:** Return to Dukalink POS app
5. **Expected:** App should handle orientation and safe area correctly

#### Test 4: Background/Foreground Cycle
1. **Setup:** Open Dukalink POS app
2. **Action:** Put app in background for 30+ seconds
3. **Action:** Bring app back to foreground
4. **Expected:** Layout should be identical to before backgrounding

### ✅ Validation Checklist
- [ ] Status bar area is not covered by app content
- [ ] Navigation bar area is not covered by app content
- [ ] Headers are properly positioned
- [ ] Bottom navigation/buttons are accessible
- [ ] Safe area padding is consistent across screens

---

## Issue 2: Manager Role Permission Assignment Restrictions

### 🧪 Test Scenarios

#### Test 1: Manager Role Selection UI
1. **Setup:** Login as Manager user
2. **Navigate:** Go to Staff Creation screen
3. **Expected:** Only "Cashier" role should be visible in role selector
4. **Expected:** "Manager" and "Super Admin" roles should NOT be visible

#### Test 2: Manager Role Assignment Validation
1. **Setup:** Login as Manager user
2. **Action:** Try to create staff with cashier role
3. **Expected:** Should succeed without errors
4. **Action:** Attempt API call with manager/super_admin role (if possible)
5. **Expected:** Should be rejected by backend with permission error

#### Test 3: Super Admin Role Selection
1. **Setup:** Login as Super Admin user
2. **Navigate:** Go to Staff Creation screen
3. **Expected:** All roles (Cashier, Manager, Super Admin) should be visible
4. **Action:** Create staff with any role
5. **Expected:** Should succeed for all role types

#### Test 4: Cashier Role Restrictions
1. **Setup:** Login as Cashier user
2. **Navigate:** Try to access Staff Creation screen
3. **Expected:** Should be blocked due to lack of manage_staff permission
4. **Expected:** Should show "Permission Denied" message

#### Test 5: Backend API Validation
1. **Setup:** Use API testing tool (curl/Postman)
2. **Action:** Test `/assignable-roles` endpoint with different user roles
3. **Expected Manager Response:** `["cashier"]`
4. **Expected Super Admin Response:** `["cashier", "manager", "super_admin"]`

### ✅ Validation Checklist
- [ ] Managers only see "Cashier" role option
- [ ] Managers cannot assign "Manager" or "Super Admin" roles
- [ ] Super Admins can assign any role
- [ ] Cashiers cannot access staff management
- [ ] Backend validates role assignments properly
- [ ] Frontend prevents unauthorized role selection
- [ ] API endpoint returns correct assignable roles

---

## 🔧 Quick Testing Commands

### Backend API Testing
```bash
# Test assignable roles endpoint (replace TOKEN with actual auth token)
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:3020/api/staff-management/assignable-roles

# Test staff creation with unauthorized role
curl -X POST \
     -H "Authorization: Bearer MANAGER_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"username":"test","name":"Test","password":"123456","role":"manager"}' \
     http://localhost:3020/api/staff-management/staff
```

### Frontend Testing
1. **Login as different roles:** Test with seeded users (super_admin, manager, cashier)
2. **Navigate to staff creation:** Check role selector options
3. **Test form submission:** Verify validation works
4. **Check console logs:** Look for any permission errors

---

## 🚨 Critical Success Criteria

### Layout Issue Resolution
- ✅ App content never overlaps status bar
- ✅ App content never overlaps navigation bar  
- ✅ Layout remains consistent after app switching
- ✅ Safe areas update dynamically when app returns from background

### Security Issue Resolution
- ✅ Managers cannot escalate privileges
- ✅ Role hierarchy is strictly enforced
- ✅ Frontend UI only shows authorized roles
- ✅ Backend validates all role assignments
- ✅ No privilege escalation possible through any means

---

## 📋 Test Results Template

### Layout Testing Results
- [ ] **Test 1 - Basic App Switching:** PASS/FAIL
- [ ] **Test 2 - Multiple App Switches:** PASS/FAIL  
- [ ] **Test 3 - Device Rotation:** PASS/FAIL
- [ ] **Test 4 - Background/Foreground:** PASS/FAIL

### Security Testing Results
- [ ] **Test 1 - Manager UI Restrictions:** PASS/FAIL
- [ ] **Test 2 - Manager Assignment Validation:** PASS/FAIL
- [ ] **Test 3 - Super Admin Full Access:** PASS/FAIL
- [ ] **Test 4 - Cashier Restrictions:** PASS/FAIL
- [ ] **Test 5 - Backend API Validation:** PASS/FAIL

### Overall Status
- [ ] **Layout Issue:** RESOLVED/NEEDS_WORK
- [ ] **Security Issue:** RESOLVED/NEEDS_WORK
- [ ] **Production Ready:** YES/NO
