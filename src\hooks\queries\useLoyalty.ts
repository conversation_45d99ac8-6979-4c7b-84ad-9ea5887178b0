/**
 * Loyalty Query Hooks
 * 
 * TanStack React Query hooks for loyalty system operations.
 * Replaces the existing loyalty hooks with proper caching and invalidation.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { loyaltyService } from '@/src/services/loyalty-service';
import { queryKeys, getInvalidationKeys } from '@/src/lib/queryKeys';
import {
  CustomerLoyaltyData,
  LoyaltyTransaction,
  LoyaltyDiscountCalculation,
  PointsRedemptionRequest,
  PointsRedemptionResult,
  LoyaltyLeaderboardEntry,
} from '@/src/types/shopify';

// Types for query parameters
interface LoyaltyTransactionParams {
  limit?: number;
  offset?: number;
  type?: "earned" | "redeemed" | "expired" | "adjusted";
}

// Hook to fetch customer loyalty data
export const useCustomerLoyalty = (customerId: string | null) => {
  return useQuery({
    queryKey: queryKeys.loyalty.customer(customerId || ''),
    queryFn: async () => {
      if (!customerId) throw new Error('Customer ID is required');
      
      const data = await loyaltyService.getCustomerLoyaltySummary(customerId);
      return data;
    },
    enabled: !!customerId,
    staleTime: 1000 * 60 * 2, // 2 minutes - loyalty data changes frequently
    gcTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: true, // Refetch when user returns to app
  });
};

// Hook to fetch customer loyalty transactions
export const useLoyaltyTransactions = (
  customerId: string | null,
  params: LoyaltyTransactionParams = {}
) => {
  return useQuery({
    queryKey: queryKeys.loyalty.transactions(customerId || ''),
    queryFn: async () => {
      if (!customerId) throw new Error('Customer ID is required');
      
      const result = await loyaltyService.getCustomerLoyaltyTransactions(
        customerId,
        params
      );
      
      return {
        transactions: result?.transactions || [],
        count: result?.count || 0,
      };
    },
    enabled: !!customerId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });
};

// Hook to calculate loyalty discounts for a customer
export const useLoyaltyDiscounts = (customerId: string | null, orderTotal?: number) => {
  return useQuery({
    queryKey: queryKeys.loyalty.discounts(customerId || ''),
    queryFn: async () => {
      if (!customerId) throw new Error('Customer ID is required');
      
      const discounts = await loyaltyService.calculateLoyaltyDiscounts(
        customerId,
        orderTotal || 0
      );
      
      return discounts;
    },
    enabled: !!customerId,
    staleTime: 1000 * 30, // 30 seconds - discount calculations should be fresh
    gcTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to fetch loyalty tiers
export const useLoyaltyTiers = () => {
  return useQuery({
    queryKey: queryKeys.loyalty.tiers(),
    queryFn: async () => {
      const tiers = await loyaltyService.getLoyaltyTiers();
      return tiers;
    },
    staleTime: 1000 * 60 * 60, // 1 hour - tiers don't change often
    gcTime: 1000 * 60 * 60 * 24, // 24 hours
  });
};

// Hook to fetch loyalty leaderboard
export const useLoyaltyLeaderboard = (limit: number = 10) => {
  return useQuery({
    queryKey: queryKeys.loyalty.leaderboard(),
    queryFn: async () => {
      const leaderboard = await loyaltyService.getLoyaltyLeaderboard(limit);
      return leaderboard;
    },
    staleTime: 1000 * 60 * 15, // 15 minutes
    gcTime: 1000 * 60 * 60, // 1 hour
  });
};

// Hook to fetch loyalty analytics
export const useLoyaltyAnalytics = () => {
  return useQuery({
    queryKey: queryKeys.loyalty.analytics(),
    queryFn: async () => {
      const analytics = await loyaltyService.getLoyaltyAnalytics();
      return analytics;
    },
    staleTime: 1000 * 60 * 30, // 30 minutes
    gcTime: 1000 * 60 * 60 * 2, // 2 hours
  });
};

// Mutation hook to redeem loyalty points
export const useRedeemLoyaltyPoints = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({
      customerId,
      redemptionData,
    }: {
      customerId: string;
      redemptionData: PointsRedemptionRequest;
    }) => {
      const result = await loyaltyService.redeemLoyaltyPoints(customerId, redemptionData);
      return result;
    },
    onSuccess: (data, variables) => {
      // Invalidate loyalty data for the customer
      const invalidationKeys = getInvalidationKeys.onLoyaltyUpdate(variables.customerId);
      invalidationKeys.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      
      // Invalidate leaderboard and analytics
      queryClient.invalidateQueries({
        queryKey: queryKeys.loyalty.leaderboard(),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.loyalty.analytics(),
      });
    },
  });
};

// Mutation hook to add loyalty points (for order completion)
export const useAddLoyaltyPoints = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({
      customerId,
      points,
      orderId,
      orderTotal,
    }: {
      customerId: string;
      points: number;
      orderId: string;
      orderTotal: number;
    }) => {
      const result = await loyaltyService.addLoyaltyPoints(
        customerId,
        points,
        orderId,
        orderTotal
      );
      return result;
    },
    onSuccess: (data, variables) => {
      // Invalidate loyalty data for the customer
      const invalidationKeys = getInvalidationKeys.onLoyaltyUpdate(variables.customerId);
      invalidationKeys.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      
      // Invalidate leaderboard and analytics
      queryClient.invalidateQueries({
        queryKey: queryKeys.loyalty.leaderboard(),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.loyalty.analytics(),
      });
    },
  });
};

// Mutation hook to adjust loyalty points (admin function)
export const useAdjustLoyaltyPoints = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({
      customerId,
      adjustment,
      reason,
    }: {
      customerId: string;
      adjustment: number;
      reason: string;
    }) => {
      const result = await loyaltyService.adjustLoyaltyPoints(
        customerId,
        adjustment,
        reason
      );
      return result;
    },
    onSuccess: (data, variables) => {
      // Invalidate loyalty data for the customer
      const invalidationKeys = getInvalidationKeys.onLoyaltyUpdate(variables.customerId);
      invalidationKeys.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      
      // Invalidate leaderboard and analytics
      queryClient.invalidateQueries({
        queryKey: queryKeys.loyalty.leaderboard(),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.loyalty.analytics(),
      });
    },
  });
};

// Hook for cache invalidation (replaces the existing useLoyaltyCacheInvalidation)
export const useLoyaltyCacheInvalidation = () => {
  const queryClient = useQueryClient();

  const invalidateLoyaltyCaches = (customerId: string) => {
    const invalidationKeys = getInvalidationKeys.onLoyaltyUpdate(customerId);
    invalidationKeys.forEach((key) => {
      queryClient.invalidateQueries({ queryKey: key });
    });

    console.log("✅ Loyalty caches invalidated for customer:", customerId);
  };

  return { invalidateLoyaltyCaches };
};

// Hook to prefetch loyalty data for better UX
export const usePrefetchLoyalty = () => {
  const queryClient = useQueryClient();
  
  const prefetchCustomerLoyalty = (customerId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.loyalty.customer(customerId),
      queryFn: async () => {
        const data = await loyaltyService.getCustomerLoyaltySummary(customerId);
        return data;
      },
      staleTime: 1000 * 60 * 2,
    });
  };
  
  const prefetchLoyaltyTransactions = (customerId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.loyalty.transactions(customerId),
      queryFn: async () => {
        const result = await loyaltyService.getCustomerLoyaltyTransactions(customerId);
        return {
          transactions: result?.transactions || [],
          count: result?.count || 0,
        };
      },
      staleTime: 1000 * 60 * 5,
    });
  };
  
  return { prefetchCustomerLoyalty, prefetchLoyaltyTransactions };
};
