import * as SQLite from "expo-sqlite";

export class DatabaseSchema {
  private db: SQLite.SQLiteDatabase;

  constructor(db: SQLite.SQLiteDatabase) {
    this.db = db;
  }

  async initializeSchema(): Promise<void> {
    try {
      // Enable foreign keys
      await this.db.execAsync("PRAGMA foreign_keys = ON;");

      // Create stores table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS stores (
          id TEXT PRIMARY KEY,
          domain TEXT UNIQUE NOT NULL,
          name TEXT NOT NULL,
          email TEXT,
          currency TEXT DEFAULT 'KES',
          timezone TEXT,
          is_connected BOOLEAN DEFAULT 0,
          last_sync_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Create products table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS products (
          id TEXT PRIMARY KEY,
          shopify_id TEXT,
          store_id TEXT NOT NULL,
          title TEXT NOT NULL,
          description TEXT,
          handle TEXT,
          product_type TEXT,
          vendor TEXT,
          tags TEXT, -- JSON array as string
          created_at DATETIME,
          updated_at DATETIME,
          last_synced_at DATETIME,
          is_dirty BOOLEAN DEFAULT 0,
          FOREIGN KEY (store_id) REFERENCES stores (id) ON DELETE CASCADE
        );
      `);

      // Create product_variants table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS product_variants (
          id TEXT PRIMARY KEY,
          shopify_id TEXT,
          product_id TEXT NOT NULL,
          title TEXT NOT NULL,
          price TEXT NOT NULL,
          compare_at_price TEXT,
          sku TEXT,
          barcode TEXT,
          inventory_quantity INTEGER DEFAULT 0,
          weight REAL,
          weight_unit TEXT,
          requires_shipping BOOLEAN DEFAULT 1,
          taxable BOOLEAN DEFAULT 1,
          inventory_policy TEXT DEFAULT 'deny',
          fulfillment_service TEXT DEFAULT 'manual',
          inventory_management TEXT,
          position INTEGER DEFAULT 1,
          created_at DATETIME,
          updated_at DATETIME,
          last_synced_at DATETIME,
          is_dirty BOOLEAN DEFAULT 0,
          FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
        );
      `);

      // Create product_images table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS product_images (
          id TEXT PRIMARY KEY,
          shopify_id TEXT,
          product_id TEXT NOT NULL,
          src TEXT NOT NULL,
          alt_text TEXT,
          width INTEGER,
          height INTEGER,
          position INTEGER DEFAULT 1,
          FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
        );
      `);

      // Create customers table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS customers (
          id TEXT PRIMARY KEY,
          shopify_id TEXT,
          store_id TEXT NOT NULL,
          first_name TEXT NOT NULL,
          last_name TEXT NOT NULL,
          email TEXT,
          phone TEXT,
          orders_count INTEGER DEFAULT 0,
          total_spent TEXT DEFAULT '0.00',
          tags TEXT, -- JSON array as string
          accepts_marketing BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          last_synced_at DATETIME,
          is_dirty BOOLEAN DEFAULT 0,
          FOREIGN KEY (store_id) REFERENCES stores (id) ON DELETE CASCADE
        );
      `);

      // Create customer_addresses table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS customer_addresses (
          id TEXT PRIMARY KEY,
          customer_id TEXT NOT NULL,
          first_name TEXT,
          last_name TEXT,
          company TEXT,
          address1 TEXT NOT NULL,
          address2 TEXT,
          city TEXT NOT NULL,
          province TEXT,
          country TEXT NOT NULL,
          zip TEXT NOT NULL,
          phone TEXT,
          is_default BOOLEAN DEFAULT 0,
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
        );
      `);

      // Create orders table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS orders (
          id TEXT PRIMARY KEY,
          shopify_id TEXT,
          store_id TEXT NOT NULL,
          order_number TEXT,
          customer_id TEXT,
          subtotal_price TEXT NOT NULL,
          total_tax TEXT DEFAULT '0.00',
          total_price TEXT NOT NULL,
          currency TEXT DEFAULT 'KES',
          financial_status TEXT DEFAULT 'pending',
          fulfillment_status TEXT,
          note TEXT,
          tags TEXT, -- JSON array as string
          custom_attributes TEXT, -- JSON array as string
          salesperson_id TEXT,
          salesperson_name TEXT,
          commission_rate REAL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          processed_at DATETIME,
          is_synced BOOLEAN DEFAULT 0,
          sync_error TEXT,
          FOREIGN KEY (store_id) REFERENCES stores (id) ON DELETE CASCADE,
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL
        );
      `);

      // Create order_line_items table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS order_line_items (
          id TEXT PRIMARY KEY,
          order_id TEXT NOT NULL,
          variant_id TEXT NOT NULL,
          product_id TEXT NOT NULL,
          title TEXT NOT NULL,
          variant_title TEXT,
          sku TEXT,
          quantity INTEGER NOT NULL,
          price TEXT NOT NULL,
          total_discount TEXT DEFAULT '0.00',
          tax_lines TEXT, -- JSON array as string
          custom_attributes TEXT, -- JSON array as string
          FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
          FOREIGN KEY (variant_id) REFERENCES product_variants (id),
          FOREIGN KEY (product_id) REFERENCES products (id)
        );
      `);

      // Create order_discounts table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS order_discounts (
          id TEXT PRIMARY KEY,
          order_id TEXT NOT NULL,
          code TEXT,
          amount TEXT NOT NULL,
          type TEXT NOT NULL, -- 'percentage' or 'fixed_amount'
          description TEXT,
          FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
        );
      `);

      // Create salespeople table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS salespeople (
          id TEXT PRIMARY KEY,
          store_id TEXT NOT NULL,
          name TEXT NOT NULL,
          email TEXT,
          commission_rate REAL DEFAULT 0.0,
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (store_id) REFERENCES stores (id) ON DELETE CASCADE
        );
      `);

      // Create sync_queue table for offline operations
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS sync_queue (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          operation_type TEXT NOT NULL, -- 'create_order', 'update_inventory', etc.
          data TEXT NOT NULL, -- JSON data
          status TEXT DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'failed'
          error_message TEXT,
          retry_count INTEGER DEFAULT 0,
          max_retries INTEGER DEFAULT 3,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          scheduled_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Create indexes for better performance
      await this.createIndexes();
    } catch (error) {
      console.error("Error initializing database schema:", error);
      throw error;
    }
  }

  private async createIndexes(): Promise<void> {
    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_products_store_id ON products (store_id);",
      "CREATE INDEX IF NOT EXISTS idx_products_shopify_id ON products (shopify_id);",
      "CREATE INDEX IF NOT EXISTS idx_products_handle ON products (handle);",
      "CREATE INDEX IF NOT EXISTS idx_product_variants_product_id ON product_variants (product_id);",
      "CREATE INDEX IF NOT EXISTS idx_product_variants_sku ON product_variants (sku);",
      "CREATE INDEX IF NOT EXISTS idx_customers_store_id ON customers (store_id);",
      "CREATE INDEX IF NOT EXISTS idx_customers_email ON customers (email);",
      "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone);",
      "CREATE INDEX IF NOT EXISTS idx_orders_store_id ON orders (store_id);",
      "CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders (customer_id);",
      "CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders (created_at);",
      "CREATE INDEX IF NOT EXISTS idx_orders_is_synced ON orders (is_synced);",
      "CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue (status);",
      "CREATE INDEX IF NOT EXISTS idx_sync_queue_scheduled_at ON sync_queue (scheduled_at);",
    ];

    for (const indexSQL of indexes) {
      await this.db.execAsync(indexSQL);
    }
  }

  async dropAllTables(): Promise<void> {
    const tables = [
      "sync_queue",
      "order_discounts",
      "order_line_items",
      "orders",
      "customer_addresses",
      "customers",
      "product_images",
      "product_variants",
      "products",
      "salespeople",
      "stores",
    ];

    for (const table of tables) {
      await this.db.execAsync(`DROP TABLE IF EXISTS ${table};`);
    }
  }

  async resetDatabase(): Promise<void> {
    await this.dropAllTables();
    await this.initializeSchema();
  }
}
