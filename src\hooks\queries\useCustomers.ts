/**
 * Customer Query Hooks
 *
 * TanStack React Query hooks for customer-related API operations.
 * Provides caching, background updates, and optimistic updates.
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
} from "@tanstack/react-query";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { queryKeys, getInvalidationKeys } from "@/src/lib/queryKeys";
import { Customer } from "@/src/types/shopify";

// Types for query parameters
interface CustomerListParams {
  limit?: number;
  search?: string;
  cursor?: string;
  includeLoyalty?: boolean;
}

interface CustomerSearchParams {
  query: string;
  limit?: number;
  includeLoyalty?: boolean;
}

interface CreateCustomerData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  note?: string;
  tags?: string;
  addresses?: any[];
}

interface UpdateCustomerData extends Partial<CreateCustomerData> {
  id: string;
}

// Hook to fetch customers with pagination
export const useCustomers = (params: CustomerListParams = {}) => {
  return useQuery({
    queryKey: queryKeys.customers.list(params),
    queryFn: async () => {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomers(params);

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch customers");
      }

      return {
        customers: response.data?.customers || [],
        pagination: response.data?.pagination || {
          page: 1,
          limit: params.limit || 50,
          hasNext: false,
          hasPrev: false,
          total: 0,
        },
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });
};

// Hook for infinite scrolling customers
export const useInfiniteCustomers = (
  params: Omit<CustomerListParams, "cursor"> = {}
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.customers.list({ ...params, infinite: true }),
    queryFn: async ({ pageParam }) => {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomers({
        ...params,
        cursor: pageParam,
      });

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch customers");
      }

      return {
        customers: response.data?.customers || [],
        pagination: response.data?.pagination,
        nextCursor: response.data?.pagination?.endCursor,
      };
    },
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 30,
  });
};

// Hook to fetch a single customer
export const useCustomer = (customerId: string | null) => {
  return useQuery({
    queryKey: queryKeys.customers.detail(customerId || ""),
    queryFn: async () => {
      if (!customerId) throw new Error("Customer ID is required");

      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomer(customerId);

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch customer");
      }

      return response.data?.customer;
    },
    enabled: !!customerId,
    staleTime: 1000 * 60 * 10, // 10 minutes for individual customers
    gcTime: 1000 * 60 * 60, // 1 hour
  });
};

// Hook to search customers
export const useCustomerSearch = (params: CustomerSearchParams) => {
  return useQuery({
    queryKey: queryKeys.customers.search(params.query),
    queryFn: async () => {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomers({
        search: params.query,
        limit: params.limit || 50,
        includeLoyalty: params.includeLoyalty,
      });

      if (!response.success) {
        throw new Error(response.error || "Failed to search customers");
      }

      return response.data?.customers || [];
    },
    enabled: !!params.query && params.query.length > 0,
    staleTime: 1000 * 60 * 2, // 2 minutes for search results
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
};

// Mutation hook to create a new customer
export const useCreateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (customerData: CreateCustomerData) => {
      const apiClient = getAPIClient();
      const response = await apiClient.createStoreCustomer(customerData);

      if (!response.success) {
        throw new Error(response.error || "Failed to create customer");
      }

      return response.data?.customer;
    },
    onSuccess: (newCustomer) => {
      // Invalidate customer lists to include the new customer
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.lists(),
      });

      // Optionally add the new customer to the cache
      if (newCustomer?.id) {
        queryClient.setQueryData(
          queryKeys.customers.detail(newCustomer.id),
          newCustomer
        );
      }
    },
  });
};

// Mutation hook to update a customer
export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (customerData: UpdateCustomerData) => {
      const apiClient = getAPIClient();
      const response = await apiClient.updateStoreCustomer(
        customerData.id,
        customerData
      );

      if (!response.success) {
        throw new Error(response.error || "Failed to update customer");
      }

      return response.data?.customer;
    },
    onSuccess: (updatedCustomer, variables) => {
      // Update the customer in cache
      if (updatedCustomer?.id) {
        queryClient.setQueryData(
          queryKeys.customers.detail(updatedCustomer.id),
          updatedCustomer
        );
      }

      // Invalidate related queries
      const invalidationKeys = getInvalidationKeys.onCustomerUpdate(
        variables.id
      );
      invalidationKeys.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });
};

// Mutation hook to delete a customer
export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (customerId: string) => {
      const apiClient = getAPIClient();
      const response = await apiClient.deleteStoreCustomer(customerId);

      if (!response.success) {
        throw new Error(response.error || "Failed to delete customer");
      }

      return response.data;
    },
    onSuccess: (data, customerId) => {
      // Remove customer from cache
      queryClient.removeQueries({
        queryKey: queryKeys.customers.detail(customerId),
      });

      // Invalidate customer lists
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers.lists(),
      });

      // Invalidate related loyalty data
      queryClient.invalidateQueries({
        queryKey: queryKeys.loyalty.customer(customerId),
      });
    },
  });
};

// Hook to get customer order history
export const useCustomerOrderHistory = (customerId: string | null) => {
  return useQuery({
    queryKey: queryKeys.orders.history(customerId || ""),
    queryFn: async () => {
      if (!customerId) throw new Error("Customer ID is required");

      const apiClient = getAPIClient();
      const response = await apiClient.getCustomerOrderHistory(customerId);

      if (!response.success) {
        throw new Error(
          response.error || "Failed to fetch customer order history"
        );
      }

      return response.data?.orders || [];
    },
    enabled: !!customerId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });
};

// Hook to prefetch customers for better UX
export const usePrefetchCustomers = () => {
  const queryClient = useQueryClient();

  const prefetchCustomers = (params: CustomerListParams = {}) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.customers.list(params),
      queryFn: async () => {
        const apiClient = getAPIClient();
        const response = await apiClient.getStoreCustomers(params);

        if (!response.success) {
          throw new Error(response.error || "Failed to fetch customers");
        }

        return {
          customers: response.data?.customers || [],
          pagination: response.data?.pagination,
        };
      },
      staleTime: 1000 * 60 * 5,
    });
  };

  const prefetchCustomer = (customerId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.customers.detail(customerId),
      queryFn: async () => {
        const apiClient = getAPIClient();
        const response = await apiClient.getStoreCustomer(customerId);

        if (!response.success) {
          throw new Error(response.error || "Failed to fetch customer");
        }

        return response.data?.customer;
      },
      staleTime: 1000 * 60 * 10,
    });
  };

  return { prefetchCustomers, prefetchCustomer };
};
