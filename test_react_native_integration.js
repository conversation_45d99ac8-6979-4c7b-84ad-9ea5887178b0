/**
 * Test React Native Integration with MySQL Backend
 * This simulates the API calls that the React Native app will make
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020/api";

async function testReactNativeIntegration() {
  console.log("🚀 Testing React Native Integration with MySQL Backend...\n");

  try {
    // Test 1: Login as Manager (React Native Flow)
    console.log("📋 Test 1: Manager Login (React Native Flow)");
    const loginResponse = await axios.post(`${BASE_URL}/pos/login`, {
      username: "manager1",
      password: "manager123",
    });

    if (loginResponse.data.success) {
      console.log("✅ PASSED: Manager Login");
      console.log(`   User: ${loginResponse.data.data.user.name}`);
      console.log(`   Role: ${loginResponse.data.data.user.role}`);
      console.log(
        `   Permissions: ${loginResponse.data.data.user.permissions.join(", ")}`
      );
      console.log(
        `   Commission Rate: ${loginResponse.data.data.user.commissionRate}%`
      );
      console.log(`   Store ID: ${loginResponse.data.data.user.storeId}`);
    } else {
      console.log("❌ FAILED: Manager Login");
      return;
    }

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    const authHeaders = { Authorization: `Bearer ${token}` };
    console.log("");

    // Test 2: Token Verification (React Native Flow)
    console.log("📋 Test 2: Token Verification (React Native Flow)");
    const verifyResponse = await axios.get(`${BASE_URL}/pos/verify`, {
      headers: authHeaders,
    });

    if (verifyResponse.data.success) {
      console.log("✅ PASSED: Token Verification");
      console.log(`   User: ${verifyResponse.data.data.user.name}`);
      console.log(`   Last Login: ${verifyResponse.data.data.user.lastLogin}`);
      console.log(
        `   Session Terminal: ${
          verifyResponse.data.data.session.terminalId || "None"
        }`
      );
    } else {
      console.log("❌ FAILED: Token Verification");
    }
    console.log("");

    // Test 3: Sales Agents for React Native
    console.log("📋 Test 3: Sales Agents for React Native");
    const agentsResponse = await axios.get(`${BASE_URL}/sales-agents`, {
      headers: authHeaders,
    });

    if (agentsResponse.data.success) {
      console.log("✅ PASSED: Sales Agents");
      console.log(
        `   Found: ${agentsResponse.data.data.salesAgents.length} agents`
      );

      // Test React Native data structure
      const agents = agentsResponse.data.data.salesAgents;
      agents.forEach((agent, index) => {
        if (index < 2) {
          // Show first 2 agents
          console.log(`   Agent ${index + 1}:`);
          console.log(`     • ID: ${agent.id}`);
          console.log(`     • Name: ${agent.name}`);
          console.log(`     • Email: ${agent.email}`);
          console.log(`     • Phone: ${agent.phone}`);
          console.log(`     • Commission Rate: ${agent.commissionRate}%`);
          console.log(`     • Territory: ${agent.territory}`);
          console.log(`     • Customer Count: ${agent.customerCount}`);
          console.log(`     • Total Sales: KSh ${agent.totalSales}`);
          console.log(`     • Active: ${agent.active}`);
        }
      });
    } else {
      console.log("❌ FAILED: Sales Agents");
    }
    console.log("");

    // Test 4: Commission Configuration for React Native
    console.log("📋 Test 4: Commission Configuration for React Native");
    const configResponse = await axios.get(
      `${BASE_URL}/discounts/configuration`,
      {
        headers: authHeaders,
      }
    );

    if (configResponse.data.success) {
      console.log("✅ PASSED: Commission Configuration");
      const config = configResponse.data.data.configuration;
      console.log("   React Native Configuration Object:");
      console.log(`     • enabled: ${config.enabled}`);
      console.log(`     • min_order_amount: ${config.min_order_amount}`);
      console.log(`     • loyalty_multiplier: ${config.loyalty_multiplier}`);
      console.log(`     • agent_discount_rate: ${config.agent_discount_rate}`);
      console.log(`     • staff_discount_rate: ${config.staff_discount_rate}`);
      console.log(
        `     • max_discount_percentage: ${config.max_discount_percentage}`
      );
    } else {
      console.log("❌ FAILED: Commission Configuration");
    }
    console.log("");

    // Test 5: Commission Calculation (React Native Cart Flow)
    console.log("📋 Test 5: Commission Calculation (React Native Cart Flow)");
    const cartData = {
      subtotal: "150.00", // KSh 150 cart
    };

    const calcResponse = await axios.post(
      `${BASE_URL}/discounts/calculate`,
      {
        cartData: cartData,
        staffId: user.id, // Current logged in staff
        salesAgentId: "agent-001", // Alice Johnson
      },
      {
        headers: { ...authHeaders, "Content-Type": "application/json" },
      }
    );

    if (calcResponse.data.success) {
      console.log("✅ PASSED: Commission Calculation");
      const discount = calcResponse.data.data.discount;
      console.log("   React Native Discount Object:");
      console.log(`     • subtotal: KSh ${discount.subtotal}`);
      console.log(`     • totalDiscount: KSh ${discount.totalDiscount}`);
      console.log(`     • discountPercentage: ${discount.discountPercentage}%`);
      console.log(`     • staffDiscount: KSh ${discount.staffDiscount}`);
      console.log(`     • agentDiscount: KSh ${discount.agentDiscount}`);
      console.log(`     • loyaltyBonus: KSh ${discount.loyaltyBonus}`);
      console.log(`     • finalAmount: KSh ${discount.finalAmount}`);
      console.log(`     • breakdown: ${JSON.stringify(discount.breakdown)}`);
    } else {
      console.log("❌ FAILED: Commission Calculation");
    }
    console.log("");

    // Test 6: Permission-Based Access (RBAC Testing)
    console.log("📋 Test 6: Permission-Based Access (RBAC Testing)");

    // Test manager permissions
    const managerPermissions = user.permissions;
    console.log("✅ PASSED: Permission-Based Access");
    console.log("   Manager Permissions for React Native:");
    console.log(
      `     • Can create orders: ${managerPermissions.includes(
        "create_orders"
      )}`
    );
    console.log(
      `     • Can manage staff: ${managerPermissions.includes("manage_staff")}`
    );
    console.log(
      `     • Can view reports: ${managerPermissions.includes("view_reports")}`
    );
    console.log(
      `     • Can manage system: ${managerPermissions.includes(
        "manage_system"
      )}`
    );
    console.log(
      `     • Can manage permissions: ${managerPermissions.includes(
        "manage_permissions"
      )}`
    );
    console.log("");

    // Test 7: Logout (React Native Flow)
    console.log("📋 Test 7: Logout (React Native Flow)");
    const logoutResponse = await axios.post(
      `${BASE_URL}/pos/logout`,
      {},
      {
        headers: authHeaders,
      }
    );

    if (logoutResponse.data.success) {
      console.log("✅ PASSED: Logout");
      console.log("   Session invalidated successfully");
    } else {
      console.log("❌ FAILED: Logout");
    }
    console.log("");

    // Test 8: Verify Session After Logout (Should Fail)
    console.log("📋 Test 8: Verify Session After Logout (Should Fail)");
    const verifyAfterLogoutResponse = await axios.get(
      `${BASE_URL}/pos/verify`,
      {
        headers: authHeaders,
      }
    );

    if (!verifyAfterLogoutResponse.data.success) {
      console.log("✅ PASSED: Session Invalidation");
      console.log("   Token correctly invalidated after logout");
    } else {
      console.log("❌ FAILED: Session Invalidation");
    }
    console.log("");

    console.log("🎉 React Native Integration Test Completed!");
    console.log("✅ All authentication flows are working correctly.");
    console.log("✅ Sales agent management is functional.");
    console.log("✅ Commission system is integrated.");
    console.log("✅ RBAC permissions are enforced.");
    console.log("✅ Session management is working.");
    console.log("🚀 React Native app is ready for MySQL backend integration!");
  } catch (error) {
    console.error("❌ React Native integration test failed:", error.message);
    if (error.response) {
      console.error("   Status:", error.response.status);
      console.error("   Data:", error.response.data);
    }
  }
}

// Run test
testReactNativeIntegration();
