/**
 * Enhanced Payment Collection Service
 * Handles credit sales, mixed-method payments, and receipt generation
 * Implements the new payment flow with proper Shopify API integration
 */

const { v4: uuidv4 } = require("uuid");
const ShopifyPaymentIntegrationService = require("./shopify-payment-integration-service");

class EnhancedPaymentCollectionService {
  constructor() {
    this.shopifyPaymentService = new ShopifyPaymentIntegrationService();
  }

  /**
   * Create mixed-method sale (cash + credit combination)
   */
  async createMixedMethodSale(orderData, paymentMethods, customerInfo) {
    try {
      const totalAmount = parseFloat(orderData.total_price);
      let cashAmount = 0;
      let creditAmount = 0;

      // Calculate amounts by payment method
      paymentMethods.forEach((method) => {
        if (method.method_type === "cash") {
          cashAmount += parseFloat(method.amount);
        } else if (method.method_type === "credit") {
          creditAmount += parseFloat(method.amount);
        }
      });

      // Validate total
      if (cashAmount + creditAmount !== totalAmount) {
        throw new Error("Payment amounts do not match order total");
      }

      let shopifyOrderId;
      let authorizationId;

      // Step 1: Create order with proper financial status
      if (creditAmount > 0) {
        // Create order with authorization for credit portion
        const creditOrderResult = await this.shopifyPaymentService.createCreditSaleOrder(
          orderData,
          totalAmount,
          customerInfo
        );

        if (!creditOrderResult.success) {
          throw new Error(`Failed to create credit order: ${creditOrderResult.error}`);
        }

        shopifyOrderId = creditOrderResult.shopifyOrderId;
        authorizationId = creditOrderResult.authorizationId;
      } else {
        // Pure cash sale - create normally
        const orderResult = await this.shopifyPaymentService.createOrderWithPaymentDetails(
          orderData,
          null,
          paymentMethods
        );

        if (!orderResult.success) {
          throw new Error(`Failed to create order: ${orderResult.error}`);
        }

        shopifyOrderId = orderResult.shopifyOrderId;
      }

      // Step 2: Process cash payment immediately if present
      if (cashAmount > 0) {
        const cashPaymentResult = await this.shopifyPaymentService.addManualPayment(
          shopifyOrderId,
          cashAmount,
          "cash"
        );

        if (!cashPaymentResult.success) {
          throw new Error(`Failed to process cash payment: ${cashPaymentResult.error}`);
        }
      }

      // Step 3: Generate composite receipt
      const receiptEntry = {
        type: "mixed_payment",
        timestamp: new Date().toISOString(),
        orderTotal: totalAmount,
        amountPaid: cashAmount,
        remainingBalance: creditAmount,
        paymentMethods: paymentMethods.map((method) => ({
          type: method.method_type,
          amount: parseFloat(method.amount),
          status: method.method_type === "cash" ? "completed" : "pending",
        })),
        customerInfo,
      };

      // Step 4: Update receipt metafield
      await this.shopifyPaymentService.updateReceiptMetafield(shopifyOrderId, receiptEntry);

      // Step 5: Store split method data
      await this.updateSplitMethodsMetafield(shopifyOrderId, {
        totalAmount,
        cashAmount,
        creditAmount,
        paymentMethods,
        customerInfo,
        createdAt: new Date().toISOString(),
      });

      return {
        success: true,
        shopifyOrderId,
        authorizationId,
        cashAmount,
        creditAmount,
        receiptEntry,
      };
    } catch (error) {
      console.error("Error creating mixed-method sale:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Collect payment on existing credit sale
   */
  async collectPayment(shopifyOrderId, amount, paymentMethod, customerInfo) {
    try {
      // Get current order details including transactions and payment status
      const orderDetailsResponse = await this.shopifyPaymentService.getOrderDetails(shopifyOrderId);

      if (!orderDetailsResponse.success) {
        throw new Error(`Failed to get order details: ${orderDetailsResponse.error}`);
      }

      const orderDetails = orderDetailsResponse.order;
      const receipts = orderDetails.receipts || [];
      const transactions = orderDetails.transactions || [];

      // Calculate current totals using Shopify data for accuracy
      const orderTotal = orderDetails.totalAmount;
      const currentPaidTotal = orderTotal - orderDetails.outstandingAmount;

      const newPaidTotal = currentPaidTotal + amount;
      const remainingBalance = orderTotal - newPaidTotal;

      // Validate payment amount
      if (remainingBalance < 0) {
        throw new Error(`Payment amount ${amount} exceeds remaining balance. Order total: ${orderTotal}, Currently paid: ${currentPaidTotal}, Outstanding: ${orderDetails.outstandingAmount}, Attempted payment: ${amount}, Would result in: ${remainingBalance}`);
      }

      console.log('🔍 Payment collection calculation:', {
        orderTotal,
        currentPaidTotal,
        outstandingAmount: orderDetails.outstandingAmount,
        paymentAmount: amount,
        newPaidTotal,
        remainingBalance,
        financialStatus: orderDetails.financialStatus
      });

      // Determine payment processing method based on existing transactions
      let paymentResult;

      // Check if there are authorized transactions that can be captured
      const authorizedTransactions = transactions.filter(
        t => t.kind === 'AUTHORIZATION' && t.status === 'SUCCESS' && !t.parentTransaction
      );

      // If this is a credit payment collection and we have authorized transactions, use capture
      if (authorizedTransactions.length > 0 && (paymentMethod === "credit" || paymentMethod === "card")) {
        console.log('🔍 Using capture for authorized transaction');
        const authTransaction = authorizedTransactions[0]; // Use first available authorization
        paymentResult = await this.shopifyPaymentService.capturePartialPayment(
          shopifyOrderId,
          amount,
          authTransaction.id
        );
      } else {
        // Use manual payment for cash, mpesa, or additional payments beyond authorization
        console.log('🔍 Using manual payment method');
        paymentResult = await this.shopifyPaymentService.addManualPayment(
          shopifyOrderId,
          amount,
          paymentMethod
        );
      }

      if (!paymentResult.success) {
        throw new Error(`Payment processing failed: ${paymentResult.error}`);
      }

      // Generate new receipt entry
      const receiptEntry = {
        type: "payment_collection",
        timestamp: new Date().toISOString(),
        orderTotal,
        amountPaid: amount,
        cumulativePaid: newPaidTotal,
        remainingBalance,
        paymentMethod,
        customerInfo,
        transactionId: uuidv4(),
        shopifyTransactionId: paymentResult.transactionId || null,
        paymentType: authorizedTransactions.length > 0 && (paymentMethod === "credit" || paymentMethod === "card") ? "capture" : "manual",
      };

      // Update receipt metafield
      await this.shopifyPaymentService.updateReceiptMetafield(shopifyOrderId, receiptEntry);

      return {
        success: true,
        receiptEntry,
        newPaidTotal,
        remainingBalance,
        isFullyPaid: remainingBalance === 0,
      };
    } catch (error) {
      console.error("Error collecting payment:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update split methods metafield
   */
  async updateSplitMethodsMetafield(shopifyOrderId, splitData) {
    try {
      const mutation = `
        mutation orderUpdate($input: OrderInput!) {
          orderUpdate(input: $input) {
            order {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: `gid://shopify/Order/${shopifyOrderId}`,
          metafields: [
            {
              namespace: "dukalink",
              key: "split_methods",
              value: JSON.stringify(splitData),
              type: "json",
            },
          ],
        },
      };

      const response = await this.shopifyPaymentService.graphqlRequest(mutation, variables);

      if (response.data?.orderUpdate?.userErrors?.length > 0) {
        throw new Error(response.data.orderUpdate.userErrors[0].message);
      }

      return { success: true };
    } catch (error) {
      console.error("Error updating split methods metafield:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get payment history and receipt data for an order
   */
  async getPaymentHistory(shopifyOrderId) {
    try {
      const receipts = await this.shopifyPaymentService.getOrderMetafield(
        shopifyOrderId,
        "dukalink",
        "receipt"
      );

      const splitMethods = await this.shopifyPaymentService.getOrderMetafield(
        shopifyOrderId,
        "dukalink",
        "split_methods"
      );

      return {
        success: true,
        receipts: receipts ? JSON.parse(receipts) : [],
        splitMethods: splitMethods ? JSON.parse(splitMethods) : null,
      };
    } catch (error) {
      console.error("Error getting payment history:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get current order payment status from Shopify
   */
  async getOrderPaymentStatus(shopifyOrderId) {
    try {
      const orderDetailsResponse = await this.shopifyPaymentService.getOrderDetails(shopifyOrderId);

      if (!orderDetailsResponse.success) {
        throw new Error(`Failed to get order details: ${orderDetailsResponse.error}`);
      }

      const orderDetails = orderDetailsResponse.order;

      console.log("🔍 Enhanced Payment Service - Order Details:", {
        id: orderDetails.id,
        name: orderDetails.name,
        totalAmount: orderDetails.totalAmount,
        receivedAmount: orderDetails.receivedAmount,
        outstandingAmount: orderDetails.outstandingAmount,
        financialStatus: orderDetails.financialStatus
      });

      return {
        success: true,
        data: {
          orderId: orderDetails.id,
          orderName: orderDetails.name,
          totalAmount: orderDetails.totalAmount,
          paidAmount: orderDetails.receivedAmount || 0,
          outstandingAmount: orderDetails.outstandingAmount,
          financialStatus: orderDetails.financialStatus,
          transactions: orderDetails.transactions,
          receipts: orderDetails.receipts,
          canCapture: orderDetails.transactions.some(t =>
            t.kind === 'AUTHORIZATION' && t.status === 'SUCCESS' && !t.parentTransaction
          ),
        },
      };
    } catch (error) {
      console.error("Error getting order payment status:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate over-payment prevention
   */
  validatePaymentAmount(orderTotal, currentPaid, newPayment) {
    const newTotal = currentPaid + newPayment;
    if (newTotal > orderTotal) {
      throw new Error(
        `Payment amount ${newPayment} would exceed remaining balance. ` +
        `Order total: ${orderTotal}, Already paid: ${currentPaid}, ` +
        `Remaining: ${orderTotal - currentPaid}`
      );
    }
    return true;
  }
}

module.exports = EnhancedPaymentCollectionService;
