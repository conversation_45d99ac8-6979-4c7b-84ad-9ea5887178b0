# Thermal Printing System - Testing and Troubleshooting Guide

## Testing Procedures

### 1. Pre-Implementation Testing

#### Environment Verification
```bash
# Check React Native version
npx react-native --version

# Verify Android SDK
adb devices

# Check Node.js version
node --version

# Verify dependencies
npm list @tumihub/react-native-thermal-receipt-printer
npm list react-native-bluetooth-escpos-printer
```

#### Permission Testing
```bash
# Check Android permissions
adb shell dumpsys package com.yourapp | grep permission

# Test Bluetooth state
adb shell settings get global bluetooth_on

# Check USB host support
adb shell pm list features | grep usb
```

### 2. Component Testing

#### PrintService Testing

```javascript
// Create test file: __tests__/PrintService.test.js
import PrintService from '../src/services/PrintService';

describe('PrintService', () => {
  beforeEach(async () => {
    await PrintService.init();
  });

  test('should initialize successfully', async () => {
    const result = await PrintService.init();
    expect(result).toBe(true);
  });

  test('should set printer type', async () => {
    const result = await PrintService.setPrinterType('ble');
    expect(result).toBe(true);
    expect(PrintService.getPrinterType()).toBe('ble');
  });

  test('should scan for devices', async () => {
    const devices = await PrintService.scanDevices();
    expect(Array.isArray(devices)).toBe(true);
  });

  test('should handle connection errors gracefully', async () => {
    try {
      await PrintService.connectToPrinter('invalid_address');
    } catch (error) {
      expect(error).toBeDefined();
    }
  });
});
```

#### Bluetooth Permission Testing

```javascript
// Create test file: __tests__/BluetoothPermissionHelper.test.js
import BluetoothPermissionHelper from '../src/utils/BluetoothPermissionHelper';
import { Platform } from 'react-native';

describe('BluetoothPermissionHelper', () => {
  test('should request permissions on Android', async () => {
    if (Platform.OS === 'android') {
      const result = await BluetoothPermissionHelper.requestBluetoothPermissions();
      expect(typeof result).toBe('boolean');
    }
  });

  test('should check Bluetooth state', async () => {
    const isEnabled = await BluetoothPermissionHelper.isBluetoothEnabled();
    expect(typeof isEnabled).toBe('boolean');
  });
});
```

### 3. Integration Testing

#### End-to-End Print Testing

```javascript
// Create test file: __tests__/PrintIntegration.test.js
import PrintService from '../src/services/PrintService';
import EnhancedPrintService from '../src/services/EnhancedPrintService';

describe('Print Integration', () => {
  const mockSaleData = {
    id: 'TEST_001',
    customer_name: 'Test Customer',
    items: [
      { name: 'Test Item', quantity: 1, unit_price: 10.00, total_price: 10.00 }
    ],
    subtotal: 10.00,
    tax_amount: 1.00,
    total_amount: 11.00,
    payment_method: 'Cash'
  };

  test('should print test receipt', async () => {
    try {
      await PrintService.printTestReceipt();
      // If no error thrown, test passes
      expect(true).toBe(true);
    } catch (error) {
      // Expected if no printer connected
      expect(error.message).toContain('Printer not connected');
    }
  });

  test('should handle enhanced print service', async () => {
    const result = await EnhancedPrintService.printReceipt('TEST_001');
    expect(result).toHaveProperty('success');
    expect(typeof result.success).toBe('boolean');
  });
});
```

### 4. Manual Testing Procedures

#### Bluetooth Printer Testing

1. **Setup Phase**
   ```
   ✓ Enable Bluetooth on device
   ✓ Pair with thermal printer
   ✓ Grant app permissions
   ✓ Launch app and navigate to printer setup
   ```

2. **Connection Testing**
   ```
   ✓ Scan for devices
   ✓ Select paired printer
   ✓ Verify connection status
   ✓ Test print functionality
   ```

3. **Print Quality Testing**
   ```
   ✓ Print test receipt
   ✓ Verify text alignment
   ✓ Check character encoding
   ✓ Test special characters
   ✓ Verify line spacing
   ```

#### USB Printer Testing (Android Only)

1. **Hardware Setup**
   ```
   ✓ Connect USB printer to Android device
   ✓ Check USB host support
   ✓ Verify printer power
   ```

2. **App Testing**
   ```
   ✓ Select USB printer type
   ✓ Scan for USB devices
   ✓ Connect to printer
   ✓ Print test receipt
   ```

#### Network Printer Testing

1. **Network Setup**
   ```
   ✓ Connect printer to WiFi/Ethernet
   ✓ Note printer IP address
   ✓ Test network connectivity (ping)
   ✓ Verify port 9100 accessibility
   ```

2. **App Configuration**
   ```
   ✓ Select network printer type
   ✓ Enter printer IP and port
   ✓ Test connection
   ✓ Print test receipt
   ```

### 5. Performance Testing

#### Memory Usage Testing

```javascript
// Monitor memory usage during printing
const measureMemoryUsage = () => {
  if (__DEV__) {
    const memoryUsage = performance.memory;
    console.log('Memory Usage:', {
      used: Math.round(memoryUsage.usedJSHeapSize / 1048576) + ' MB',
      total: Math.round(memoryUsage.totalJSHeapSize / 1048576) + ' MB',
      limit: Math.round(memoryUsage.jsHeapSizeLimit / 1048576) + ' MB'
    });
  }
};

// Call before and after printing operations
measureMemoryUsage();
await PrintService.printReceipt(saleData);
measureMemoryUsage();
```

#### Connection Speed Testing

```javascript
// Measure connection time
const testConnectionSpeed = async (printerAddress) => {
  const startTime = Date.now();
  
  try {
    await PrintService.connectToPrinter(printerAddress);
    const connectionTime = Date.now() - startTime;
    console.log(`Connection established in ${connectionTime}ms`);
    return connectionTime;
  } catch (error) {
    console.error('Connection failed:', error);
    return -1;
  }
};
```

#### Print Speed Testing

```javascript
// Measure print time
const testPrintSpeed = async (receiptData) => {
  const startTime = Date.now();
  
  try {
    await PrintService.printReceipt(receiptData);
    const printTime = Date.now() - startTime;
    console.log(`Receipt printed in ${printTime}ms`);
    return printTime;
  } catch (error) {
    console.error('Print failed:', error);
    return -1;
  }
};
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Bluetooth Connection Issues

**Problem**: "Bluetooth device not found"
```
Symptoms:
- Device scan returns empty list
- Previously paired devices not showing

Solutions:
1. Check Bluetooth permissions
2. Verify Bluetooth is enabled
3. Clear Bluetooth cache
4. Re-pair the device
5. Check Android version compatibility

Debug Commands:
adb shell settings put global bluetooth_on 1
adb shell pm clear com.android.bluetooth
```

**Problem**: "Connection timeout"
```
Symptoms:
- Device found but connection fails
- Timeout errors during connection

Solutions:
1. Move closer to printer
2. Check printer power and paper
3. Restart Bluetooth service
4. Clear app cache
5. Increase connection timeout

Code Fix:
// Increase timeout in connection method
const CONNECTION_TIMEOUT = 30000; // 30 seconds
```

#### 2. Permission Issues

**Problem**: "Bluetooth permissions denied"
```
Symptoms:
- Permission request dialogs not showing
- Access denied errors

Solutions:
1. Check AndroidManifest.xml permissions
2. Verify targetSdkVersion compatibility
3. Request permissions at runtime
4. Check Android 12+ permission requirements

Debug Code:
import { PermissionsAndroid } from 'react-native';

const checkPermissions = async () => {
  const permissions = [
    PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
    PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
  ];
  
  for (const permission of permissions) {
    const granted = await PermissionsAndroid.check(permission);
    console.log(`${permission}: ${granted ? 'GRANTED' : 'DENIED'}`);
  }
};
```

#### 3. Print Quality Issues

**Problem**: "Garbled or incomplete text"
```
Symptoms:
- Text appears corrupted
- Missing characters
- Wrong encoding

Solutions:
1. Check character encoding settings
2. Verify ESC/POS command compatibility
3. Test with different text
4. Check printer firmware

Code Fix:
// Ensure proper text encoding
const printText = async (text) => {
  const encodedText = text.replace(/[^\x00-\x7F]/g, "?");
  await BluetoothEscposPrinter.printText(encodedText, null);
};
```

**Problem**: "Poor print alignment"
```
Symptoms:
- Text not centered properly
- Inconsistent spacing

Solutions:
1. Use proper alignment commands
2. Check paper width settings
3. Test with different printers
4. Verify ESC/POS commands

Code Fix:
// Proper alignment usage
await BluetoothEscposPrinter.printerAlign(BluetoothEscposPrinter.ALIGN.CENTER);
await BluetoothEscposPrinter.printText('Centered Text\n', null);
await BluetoothEscposPrinter.printerAlign(BluetoothEscposPrinter.ALIGN.LEFT);
```

#### 4. USB Printer Issues

**Problem**: "USB device not detected"
```
Symptoms:
- No USB devices found
- Connection fails immediately

Solutions:
1. Check USB host support
2. Verify USB cable connection
3. Test with different USB port
4. Check device permissions

Debug Commands:
adb shell lsusb
adb shell ls /dev/bus/usb/
```

#### 5. Network Printer Issues

**Problem**: "Network connection failed"
```
Symptoms:
- Cannot connect to IP address
- Timeout on port 9100

Solutions:
1. Ping printer IP address
2. Check network connectivity
3. Verify printer port settings
4. Test with telnet

Debug Commands:
ping [printer_ip]
telnet [printer_ip] 9100
nmap -p 9100 [printer_ip]
```

### Debug Tools and Utilities

#### 1. Printer Configuration Debugger

```javascript
// Create debug utility: src/utils/PrinterDebugger.js
export const PrinterDebugger = {
  async diagnoseConnection() {
    console.log('=== Printer Connection Diagnosis ===');
    
    // Check service initialization
    const serviceInit = await PrintService.init();
    console.log('Service initialized:', serviceInit);
    
    // Check printer type
    const printerType = PrintService.getPrinterType();
    console.log('Printer type:', printerType);
    
    // Check connection status
    const isConnected = PrintService.isConnectedToPrinter();
    console.log('Connection status:', isConnected);
    
    // Check saved configuration
    const savedAddress = await AsyncStorage.getItem('@dukalink_printer_address');
    console.log('Saved printer address:', savedAddress);
    
    return {
      serviceInit,
      printerType,
      isConnected,
      savedAddress
    };
  },

  async testPrintCapabilities() {
    console.log('=== Testing Print Capabilities ===');
    
    try {
      // Test basic text printing
      await BluetoothEscposPrinter.printText('Test Line 1\n', null);
      console.log('✓ Basic text printing works');
      
      // Test alignment
      await BluetoothEscposPrinter.printerAlign(BluetoothEscposPrinter.ALIGN.CENTER);
      await BluetoothEscposPrinter.printText('Centered Text\n', null);
      console.log('✓ Text alignment works');
      
      // Test line spacing
      await BluetoothEscposPrinter.printText('\n\n', null);
      console.log('✓ Line spacing works');
      
      return true;
    } catch (error) {
      console.error('✗ Print capability test failed:', error);
      return false;
    }
  }
};
```

#### 2. Automated Test Runner

```javascript
// Create test runner: scripts/runPrintTests.js
const runAllTests = async () => {
  console.log('Starting comprehensive printer tests...\n');
  
  const results = {
    permissions: await testPermissions(),
    connection: await testConnection(),
    printing: await testPrinting(),
    performance: await testPerformance()
  };
  
  console.log('\n=== Test Results Summary ===');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${test}: ${passed ? '✓ PASS' : '✗ FAIL'}`);
  });
  
  return results;
};

// Run tests
runAllTests().catch(console.error);
```

This comprehensive testing and troubleshooting guide provides the tools and procedures needed to ensure reliable thermal printing functionality across different devices and printer types.
