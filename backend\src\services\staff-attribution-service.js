/**
 * Staff Attribution Service
 *
 * Following Shopify's best practices for custom POS staff management:
 * 1. Use custom staff authentication (not Shopify Admin API)
 * 2. Store staff attribution in order metafields
 * 3. Manage staff data in custom database
 */

const shopifyService = require("./shopify-service");
const salesAgentService = require("./sales-agent-service");
const inventoryService = require("./inventory-management-service");
const staffService = require("./staff-service-mysql");

class StaffAttributionService {
  constructor() {
    // MySQL staff service is now used instead of in-memory database
  }

  // Get all staff members from MySQL database
  async getAllStaff() {
    return await staffService.getAllStaff();
  }

  // Get staff member by ID from MySQL database
  async getStaffById(staffId) {
    return await staffService.getStaffById(staffId);
  }

  // Set commission rate for staff member using MySQL database
  async setStaffCommissionRate(staffId, commissionRate) {
    return await staffService.setStaffCommissionRate(staffId, commissionRate);
  }

  // Create new staff member using MySQL database
  async createStaff(staffData) {
    return await staffService.createStaff(staffData);
  }

  // Create order with staff attribution using Shopify's recommended metafields approach
  async createOrderWithStaffAttribution(orderData, staffId) {
    try {
      // Get staff information from our custom database
      const staffResult = await this.getStaffById(staffId);

      if (!staffResult.success) {
        console.warn(
          `Staff member ${staffId} not found, proceeding without staff info`
        );
      }

      const staffInfo = staffResult.success ? staffResult.staffMember : null;

      // Create order using Shopify service
      const orderResult = await shopifyService.createOrder(orderData);

      if (!orderResult.success) {
        return orderResult;
      }

      const createdOrder = orderResult.order;

      // Add staff attribution metafields (Shopify recommended approach)
      const metafieldResult = await this.addStaffAttributionMetafields(
        createdOrder.admin_graphql_api_id,
        staffId,
        staffInfo
      );

      if (!metafieldResult.success) {
        console.warn(
          "Failed to add staff metafields, but order was created successfully"
        );
      }

      return {
        success: true,
        order: createdOrder,
        staffAttribution: {
          staffId: staffId,
          staffName: staffInfo?.name || "Unknown",
          commissionRate: staffInfo?.commissionRate || 0,
          metafieldsAdded: metafieldResult.success,
        },
        warning:
          "This method is deprecated. Use createOrderWithDualAttribution for new orders.",
      };
    } catch (error) {
      console.error("Create order with staff attribution error:", error);
      return {
        success: false,
        error: "Failed to create order with staff attribution",
      };
    }
  }

  // Create order with dual attribution (staff + sales agent) - NEW REQUIRED METHOD
  async createOrderWithDualAttribution(orderData, staffId, salesAgentId) {
    try {
      // Validate required parameters
      if (!staffId) {
        return {
          success: false,
          error:
            "Staff ID is required - every order must have a POS staff member",
        };
      }

      if (!salesAgentId) {
        return {
          success: false,
          error:
            "Sales Agent ID is required - every order must have a sales agent attribution",
        };
      }

      // Get staff information from our custom database
      const staffResult = await this.getStaffById(staffId);
      if (!staffResult.success) {
        return {
          success: false,
          error: `Staff member not found: ${staffId}`,
        };
      }

      // Get sales agent information
      const agentResult = await salesAgentService.getSalesAgentById(
        salesAgentId
      );
      if (!agentResult.success) {
        return {
          success: false,
          error: `Sales agent not found: ${salesAgentId}`,
        };
      }

      const staffInfo = staffResult.staffMember;
      const agentInfo = agentResult.salesAgent;

      // Validate that sales agent is active
      if (!agentInfo.active) {
        return {
          success: false,
          error: "Cannot create order with inactive sales agent",
        };
      }

      // Validate inventory availability before creating order
      const inventoryValidation = await inventoryService.validateOrderInventory(
        orderData.lineItems
      );

      if (!inventoryValidation.success) {
        return {
          success: false,
          error: `Inventory validation failed: ${inventoryValidation.error}`,
        };
      }

      if (!inventoryValidation.valid) {
        return {
          success: false,
          error: "Insufficient inventory for order",
          insufficientStock: inventoryValidation.insufficientStock,
          details: inventoryValidation.insufficientStock
            .map(
              (item) =>
                `${item.title}: requested ${item.requested}, available ${item.available}`
            )
            .join("; "),
        };
      }

      // Create order using Shopify service
      const orderResult = await shopifyService.createOrder(orderData);

      if (!orderResult.success) {
        return orderResult;
      }

      const createdOrder = orderResult.order;

      // Add dual attribution metafields (staff + sales agent) with payment and location info
      const metafieldResult = await this.addDualAttributionMetafields(
        createdOrder.admin_graphql_api_id,
        staffId,
        staffInfo,
        salesAgentId,
        agentInfo,
        orderData // Pass full order data for payment and location info
      );

      if (!metafieldResult.success) {
        console.warn(
          "Failed to add attribution metafields, but order was created successfully"
        );
      }

      // Link customer to sales agent if not already linked
      if (orderData.customer?.id) {
        await salesAgentService.linkCustomerToAgent(
          salesAgentId,
          orderData.customer.id
        );
      }

      return {
        success: true,
        order: createdOrder,
        attribution: {
          staff: {
            id: staffId,
            name: staffInfo.name,
            role: staffInfo.role,
            commissionRate: staffInfo.commissionRate,
          },
          salesAgent: {
            id: salesAgentId,
            name: agentInfo.name,
            territory: agentInfo.territory,
            commissionRate: agentInfo.commissionRate,
          },
          metafieldsAdded: metafieldResult.success,
          totalCommissionRate:
            staffInfo.commissionRate + agentInfo.commissionRate,
        },
      };
    } catch (error) {
      console.error("Create order with dual attribution error:", error);
      return {
        success: false,
        error: "Failed to create order with dual attribution",
      };
    }
  }

  // Add dual attribution metafields to order (staff + sales agent) - Shopify recommended approach
  async addDualAttributionMetafields(
    orderGid,
    staffId,
    staffInfo,
    salesAgentId,
    agentInfo,
    orderData = {}
  ) {
    try {
      const mutation = `
        mutation orderUpdate($input: OrderInput!) {
          orderUpdate(input: $input) {
            order {
              id
              metafields(first: 15) {
                edges {
                  node {
                    namespace
                    key
                    value
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      // Build metafields array
      const metafields = [
        // POS Staff Attribution
        {
          namespace: "pos",
          key: "staff_id",
          value: staffId,
          type: "single_line_text_field",
        },
        {
          namespace: "pos",
          key: "staff_name",
          value: staffInfo?.name || "POS Staff",
          type: "single_line_text_field",
        },
        {
          namespace: "pos",
          key: "staff_role",
          value: staffInfo?.role || "staff",
          type: "single_line_text_field",
        },
        {
          namespace: "pos",
          key: "staff_commission_rate",
          value: (staffInfo?.commissionRate || 0).toString(),
          type: "number_decimal",
        },
        // Sales Agent Attribution
        {
          namespace: "pos",
          key: "sales_agent_id",
          value: salesAgentId,
          type: "single_line_text_field",
        },
        {
          namespace: "pos",
          key: "sales_agent_name",
          value: agentInfo?.name || "Sales Agent",
          type: "single_line_text_field",
        },
        {
          namespace: "pos",
          key: "sales_agent_territory",
          value: agentInfo?.territory || "Unknown",
          type: "single_line_text_field",
        },
        {
          namespace: "pos",
          key: "sales_agent_commission_rate",
          value: (agentInfo?.commissionRate || 0).toString(),
          type: "number_decimal",
        },
        // Combined Attribution
        {
          namespace: "pos",
          key: "total_commission_rate",
          value: (
            (staffInfo?.commissionRate || 0) + (agentInfo?.commissionRate || 0)
          ).toString(),
          type: "number_decimal",
        },
        {
          namespace: "pos",
          key: "commission_eligible",
          value: "true",
          type: "boolean",
        },
        {
          namespace: "pos",
          key: "attribution_timestamp",
          value: new Date().toISOString(),
          type: "date_time",
        },
        {
          namespace: "pos",
          key: "attribution_type",
          value: "dual_attribution",
          type: "single_line_text_field",
        },
      ];

      // Add payment metafields if payment information is provided
      if (orderData.paymentMethod && orderData.paymentTransactionId) {
        metafields.push(
          {
            namespace: "dukalink_payment",
            key: "payment_method",
            value: orderData.paymentMethod,
            type: "single_line_text_field",
          },
          {
            namespace: "dukalink_payment",
            key: "transaction_id",
            value: orderData.paymentTransactionId,
            type: "single_line_text_field",
          }
        );

        if (orderData.paymentTimestamp) {
          metafields.push({
            namespace: "dukalink_payment",
            key: "payment_timestamp",
            value: orderData.paymentTimestamp,
            type: "single_line_text_field",
          });
        }

        // Handle split payment data
        if (orderData.splitPaymentData && orderData.splitPaymentData.payments) {
          const splitData = orderData.splitPaymentData;

          // Add split payment summary
          metafields.push(
            {
              namespace: "dukalink_payment",
              key: "is_split_payment",
              value: "true",
              type: "boolean",
            },
            {
              namespace: "dukalink_payment",
              key: "split_payment_count",
              value: splitData.payments.length.toString(),
              type: "number_integer",
            },
            {
              namespace: "dukalink_payment",
              key: "split_total_change",
              value: (splitData.totalChange || 0).toString(),
              type: "number_decimal",
            }
          );

          // Add individual payment method details
          splitData.payments.forEach((payment, index) => {
            const paymentIndex = index + 1;
            metafields.push(
              {
                namespace: "dukalink_payment",
                key: `payment_method_${paymentIndex}`,
                value: payment.method.name,
                type: "single_line_text_field",
              },
              {
                namespace: "dukalink_payment",
                key: `payment_amount_${paymentIndex}`,
                value: payment.amount.toString(),
                type: "number_decimal",
              },
              {
                namespace: "dukalink_payment",
                key: `transaction_id_${paymentIndex}`,
                value: payment.transactionId,
                type: "single_line_text_field",
              }
            );

            // Add change information if applicable
            if (payment.change && payment.change > 0) {
              metafields.push({
                namespace: "dukalink_payment",
                key: `payment_change_${paymentIndex}`,
                value: payment.change.toString(),
                type: "number_decimal",
              });
            }
          });
        }
      }

      // Add location metafields if location information is provided
      if (orderData.locationId && orderData.locationName) {
        metafields.push(
          {
            namespace: "dukalink_location",
            key: "location_id",
            value: orderData.locationId,
            type: "single_line_text_field",
          },
          {
            namespace: "dukalink_location",
            key: "location_name",
            value: orderData.locationName,
            type: "single_line_text_field",
          }
        );
      }

      const variables = {
        input: {
          id: orderGid,
          metafields: metafields,
        },
      };

      const response = await shopifyService.graphqlRequest(mutation, variables);

      if (response.data?.orderUpdate?.userErrors?.length > 0) {
        console.error(
          "Error adding dual attribution metafields:",
          response.data.orderUpdate.userErrors
        );
        return {
          success: false,
          error: response.data.orderUpdate.userErrors[0].message,
        };
      }

      console.log(
        "✅ Dual attribution metafields (staff + sales agent) added successfully"
      );
      return {
        success: true,
        metafields: response.data?.orderUpdate?.order?.metafields,
      };
    } catch (error) {
      console.error("Failed to add dual attribution metafields:", error);
      return {
        success: false,
        error: "Failed to add dual attribution",
      };
    }
  }

  // Add staff attribution metafields to order (legacy method)
  async addStaffAttributionMetafields(orderGid, staffId, staffInfo = null) {
    try {
      const mutation = `
        mutation orderUpdate($input: OrderInput!) {
          orderUpdate(input: $input) {
            order {
              id
              metafields(first: 10) {
                edges {
                  node {
                    namespace
                    key
                    value
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          id: orderGid,
          metafields: [
            {
              namespace: "pos",
              key: "staff_id",
              value: staffId,
              type: "single_line_text_field",
            },
            {
              namespace: "pos",
              key: "staff_name",
              value: staffInfo?.name || "POS Staff",
              type: "single_line_text_field",
            },
            {
              namespace: "pos",
              key: "commission_rate",
              value: (staffInfo?.commissionRate || 0).toString(),
              type: "number_decimal",
            },
            {
              namespace: "pos",
              key: "commission_eligible",
              value: "true",
              type: "boolean",
            },
            {
              namespace: "pos",
              key: "attribution_timestamp",
              value: new Date().toISOString(),
              type: "date_time",
            },
          ],
        },
      };

      const response = await shopifyService.graphqlRequest(mutation, variables);

      if (response.data?.orderUpdate?.userErrors?.length > 0) {
        console.error(
          "Error adding staff metafields:",
          response.data.orderUpdate.userErrors
        );
        return {
          success: false,
          error: response.data.orderUpdate.userErrors[0].message,
        };
      }

      console.log("✅ Staff attribution metafields added successfully");
      return {
        success: true,
        metafields: response.data?.orderUpdate?.order?.metafields,
      };
    } catch (error) {
      console.error("Failed to add staff attribution metafields:", error);
      return {
        success: false,
        error: "Failed to add staff attribution",
      };
    }
  }

  // Get orders by staff member (for commission tracking)
  async getOrdersByStaff(staffId, options = {}) {
    try {
      const { limit = 50, dateFrom, dateTo } = options;

      // Get orders from Shopify
      const ordersResult = await shopifyService.getOrders(limit);

      if (!ordersResult.success) {
        return ordersResult;
      }

      // Filter orders by staff attribution (this would be more efficient with proper database queries)
      const staffOrders = [];

      for (const order of ordersResult.orders) {
        // Check if order has staff attribution metafields
        // Note: In production, you'd query metafields directly or use a more efficient approach
        const hasStaffAttribution = order.note_attributes?.some(
          (attr) => attr.name === "sales_agent_id" && attr.value === staffId
        );

        if (hasStaffAttribution) {
          // Apply date filtering if provided
          if (dateFrom || dateTo) {
            const orderDate = new Date(order.created_at);
            if (dateFrom && orderDate < new Date(dateFrom)) continue;
            if (dateTo && orderDate > new Date(dateTo)) continue;
          }

          staffOrders.push(order);
        }
      }

      return {
        success: true,
        orders: staffOrders,
        totalCount: staffOrders.length,
        staffId: staffId,
      };
    } catch (error) {
      console.error("Get orders by staff error:", error);
      return {
        success: false,
        error: "Failed to fetch staff orders",
      };
    }
  }

  // Calculate staff performance metrics
  async getStaffPerformance(staffId, dateRange = {}) {
    try {
      const staffResult = await this.getStaffById(staffId);
      if (!staffResult.success) {
        return staffResult;
      }

      const ordersResult = await this.getOrdersByStaff(staffId, dateRange);
      if (!ordersResult.success) {
        return ordersResult;
      }

      const orders = ordersResult.orders;
      const staff = staffResult.staffMember;

      // Calculate performance metrics
      const totalSales = orders.reduce(
        (sum, order) => sum + parseFloat(order.total_price || 0),
        0
      );

      const totalCommission = totalSales * (staff.commissionRate / 100);

      const averageOrderValue =
        orders.length > 0 ? totalSales / orders.length : 0;

      return {
        success: true,
        performance: {
          staffMember: staff,
          totalSales: totalSales,
          totalCommission: totalCommission,
          orderCount: orders.length,
          averageOrderValue: averageOrderValue,
          commissionRate: staff.commissionRate,
          dateRange: dateRange,
          period: {
            from: dateRange.dateFrom,
            to: dateRange.dateTo,
          },
        },
        recentOrders: orders.slice(0, 10), // Latest 10 orders
      };
    } catch (error) {
      console.error("Get staff performance error:", error);
      return {
        success: false,
        error: "Failed to calculate staff performance",
      };
    }
  }
}

module.exports = new StaffAttributionService();
