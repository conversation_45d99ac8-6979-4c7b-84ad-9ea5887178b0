import React from 'react';
import { useSession } from '../../contexts/AuthContext';
import { canAccessFeature } from '../../config/rbac';

interface FeatureGateProps {
  children: React.ReactNode;
  featureId: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * FeatureGate Component
 * 
 * Conditionally renders children based on UI feature access rules.
 * Uses the UI_FEATURES configuration from RBAC config.
 * 
 * @param children - Content to render if user can access the feature
 * @param featureId - ID of the feature to check access for
 * @param fallback - Content to render if user can't access the feature
 * @param showFallback - Whether to show fallback content or nothing
 */
export const FeatureGate: React.FC<FeatureGateProps> = ({
  children,
  featureId,
  fallback = null,
  showFallback = false,
}) => {
  const { user, isPosAuthenticated } = useSession();

  // If not authenticated, don't show anything
  if (!isPosAuthenticated || !user) {
    return showFallback ? <>{fallback}</> : null;
  }

  const userRole = user.role;
  const userPermissions = user.permissions || [];

  const hasAccess = canAccessFeature(userRole, userPermissions, featureId);

  if (hasAccess) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

// Convenience components for common dashboard features
export const DashboardStaffManagement: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="dashboard_staff_management" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const DashboardReports: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="dashboard_reports" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const DashboardAnalytics: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="dashboard_analytics" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const DashboardSystemSettings: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="dashboard_system_settings" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Convenience components for order features
export const OrderRefundButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="order_refund_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const OrderEditButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="order_edit_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Convenience components for customer features
export const CustomerCreateButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="customer_create_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const CustomerEditButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="customer_edit_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const CustomerDeleteButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="customer_delete_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Convenience components for sales agent features
export const SalesAgentCreateButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="sales_agent_create_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const SalesAgentEditButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="sales_agent_edit_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const SalesAgentPerformanceView: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="sales_agent_performance_view" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Convenience components for staff features
export const StaffCreateButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="staff_create_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const StaffEditButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="staff_edit_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const StaffPermissionsEdit: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="staff_permissions_edit" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const StaffRoleEdit: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="staff_role_edit" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Convenience components for inventory features
export const InventoryAdjustButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="inventory_adjust_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const InventoryBulkUpdate: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="inventory_bulk_update" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Convenience components for discount features
export const DiscountCreateButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="discount_create_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const DiscountEditButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="discount_edit_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Convenience components for system features
export const SystemBackupButton: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="system_backup_button" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const IntegrationSettings: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="integration_settings" fallback={fallback}>
    {children}
  </FeatureGate>
);

export const AuditLogsView: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <FeatureGate featureId="audit_logs_view" fallback={fallback}>
    {children}
  </FeatureGate>
);

// Higher-order component for feature-based rendering
export const withFeature = <P extends object>(
  Component: React.ComponentType<P>,
  featureId: string,
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <FeatureGate featureId={featureId} fallback={fallback}>
      <Component {...props} />
    </FeatureGate>
  );
};
