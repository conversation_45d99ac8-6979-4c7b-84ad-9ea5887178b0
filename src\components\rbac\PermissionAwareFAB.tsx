/**
 * Permission-Aware Floating Action Button
 * 
 * Automatically checks permissions before rendering the FAB
 * Integrates with the RBAC system
 */

import React from 'react';
import { FloatingActionButton } from '@/src/components/ui/FloatingActionButton';
import { useRBAC } from '@/src/hooks/useRBAC';
import { Permission } from '@/src/config/rbac';

interface PermissionAwareFABProps {
  /** Required permission to show the FAB */
  requiredPermission: Permission;
  /** Function to call when FAB is pressed */
  onPress: () => void;
  /** Icon name to display */
  iconName?: string;
  /** Accessibility label */
  accessibilityLabel?: string;
  /** Additional props to pass to the underlying FAB */
  fabProps?: any;
}

export const PermissionAwareFAB: React.FC<PermissionAwareFABProps> = ({
  requiredPermission,
  onPress,
  iconName = 'plus',
  accessibilityLabel = 'Add new item',
  fabProps = {},
}) => {
  const { hasPermission } = useRBAC();

  // Only render if user has the required permission
  if (!hasPermission(requiredPermission)) {
    return null;
  }

  return (
    <FloatingActionButton
      iconName={iconName}
      onPress={onPress}
      accessibilityLabel={accessibilityLabel}
      {...fabProps}
    />
  );
};

// Convenience components for specific use cases
export const StaffManagementFAB: React.FC<{
  onPress: () => void;
}> = ({ onPress }) => (
  <PermissionAwareFAB
    requiredPermission="manage_staff"
    onPress={onPress}
    iconName="person.badge.plus"
    accessibilityLabel="Add new staff member"
  />
);

export const SalesAgentManagementFAB: React.FC<{
  onPress: () => void;
}> = ({ onPress }) => (
  <PermissionAwareFAB
    requiredPermission="manage_staff"
    onPress={onPress}
    iconName="person.badge.plus"
    accessibilityLabel="Add new sales agent"
  />
);
