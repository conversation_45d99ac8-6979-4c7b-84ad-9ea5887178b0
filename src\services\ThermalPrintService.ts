import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  NetPrinter,
  USBPrinter,
} from "@tumihub/react-native-thermal-receipt-printer";
import { LogBox, Platform } from "react-native";
import {
  BluetoothEscposPrinter,
  BluetoothManager,
} from "react-native-bluetooth-escpos-printer";
import { ReceiptData } from "../components/receipt/ReceiptGenerator";
import { TREASURED_LOGO_TEXT } from "../constants/logoConstants";

// Suppress warnings
LogBox.ignoreLogs([
  "`new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method",
  "`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method",
]);

export interface ThermalPrinterDevice {
  name: string;
  address: string;
  port?: number;
}

export interface ThermalPrintResult {
  success: boolean;
  error?: string;
}

class ThermalPrintService {
  private isConnected = false;
  private pairedDevices: ThermalPrinterDevice[] = [];
  private connectedDevice: ThermalPrinterDevice | null = null;
  private readonly PRINTER_STORAGE_KEY = "@dukalink_printer_address";
  private readonly PRINTER_TYPE_KEY = "@dukalink_printer_type";
  private readonly PRINTER_MODEL_KEY = "@dukalink_printer_model";
  private printerType: "ble" | "usb" | "net" | null = null;
  private printerModel: string | null = null;
  private printer: any = null;

  /**
   * Initialize the thermal print service
   */
  async init(): Promise<boolean> {
    try {
      // Load saved printer configuration
      const savedPrinterAddress = await AsyncStorage.getItem(
        this.PRINTER_STORAGE_KEY
      );
      const savedPrinterType = await AsyncStorage.getItem(
        this.PRINTER_TYPE_KEY
      );
      const savedPrinterModel = await AsyncStorage.getItem(
        this.PRINTER_MODEL_KEY
      );

      if (savedPrinterType) {
        this.printerType = savedPrinterType as "ble" | "usb" | "net";
        this.printerModel = savedPrinterModel || "generic";

        // Initialize the appropriate printer type
        switch (this.printerType) {
          case "ble":
            this.printer = BluetoothEscposPrinter;
            break;
          case "usb":
            if (Platform.OS === "android") {
              this.printer = USBPrinter;
              await USBPrinter.init();
            }
            break;
          case "net":
            this.printer = NetPrinter;
            await NetPrinter.init();
            break;
        }

        // Try to connect to the last used printer
        if (savedPrinterAddress) {
          await this.connectToPrinter(savedPrinterAddress);
        }
      } else {
        // Default to BLE printer
        this.printerType = "ble";
        this.printer = BluetoothEscposPrinter;
      }

      return true;
    } catch (error) {
      console.error("Error initializing thermal printer service:", error);
      return false;
    }
  }

  /**
   * Set printer type and initialize
   */
  async setPrinterType(type: "ble" | "usb" | "net"): Promise<boolean> {
    try {
      this.printerType = type;
      await AsyncStorage.setItem(this.PRINTER_TYPE_KEY, type);

      switch (type) {
        case "ble":
          this.printer = BluetoothEscposPrinter;
          break;
        case "usb":
          this.printer = USBPrinter;
          await USBPrinter.init();
          break;
        case "net":
          this.printer = NetPrinter;
          await NetPrinter.init();
          break;
      }

      return true;
    } catch (error) {
      console.error("Error setting printer type:", error);
      return false;
    }
  }

  /**
   * Scan for available devices with comprehensive error handling (filtered for printers only)
   */
  async scanDevices(): Promise<ThermalPrinterDevice[]> {
    try {
      switch (this.printerType) {
        case "ble":
          return await this.scanBluetoothDevices();
        case "usb":
          return await this.scanUSBDevices();
        case "net":
          return await this.getNetworkPrinters();
        default:
          return [];
      }
    } catch (error) {
      console.error("Error scanning devices:", error);
      return [];
    }
  }

  /**
   * Scan for all available devices without filtering (includes audio devices, etc.)
   */
  async scanAllDevices(): Promise<ThermalPrinterDevice[]> {
    try {
      switch (this.printerType) {
        case "ble":
          return await this.scanBluetoothDevicesUnfiltered();
        case "usb":
          return await this.scanUSBDevices(); // USB devices are already printer-specific
        case "net":
          return await this.getNetworkPrinters(); // Network printers are already printer-specific
        default:
          return [];
      }
    } catch (error) {
      console.error("Error scanning all devices:", error);
      return [];
    }
  }

  /**
   * Bluetooth device scanning using correct API from documentation
   */
  private async scanBluetoothDevices(): Promise<ThermalPrinterDevice[]> {
    try {
      // Step 1: Check if Bluetooth is enabled

      const isEnabled = await BluetoothManager.isBluetoothEnabled();

      if (!isEnabled) {
        return [];
      }

      // Step 2: Use scanDevices method as documented

      const scanResult = await BluetoothManager.scanDevices();

      // Step 3: Parse the scan result according to documentation
      // According to docs: scanResult should be a JSON string with 'paired' and 'found' arrays
      let parsedResult: any = {};

      if (typeof scanResult === "string") {
        try {
          parsedResult = JSON.parse(scanResult);
        } catch (parseError) {
          console.error("   Failed to parse scan result JSON:", parseError);
          return [];
        }
      } else if (typeof scanResult === "object" && scanResult !== null) {
        parsedResult = scanResult;
      } else {
        return [];
      }

      // Step 4: Extract devices from paired and found arrays
      const pairedDevices = parsedResult.paired || [];
      const foundDevices = parsedResult.found || [];

      // Step 5: Combine and process all devices
      const allDevices = [...pairedDevices, ...foundDevices];

      const processedDevices = allDevices
        .map((device: any, index: number) => {
          // Handle different device formats (string or object)
          let deviceObj = device;
          if (typeof device === "string") {
            try {
              deviceObj = JSON.parse(device);
            } catch (e) {
              return null;
            }
          }

          const processedDevice = {
            name:
              deviceObj.name ||
              deviceObj.device_name ||
              deviceObj.deviceName ||
              `Device ${index + 1}`,
            address:
              deviceObj.address ||
              deviceObj.inner_mac_address ||
              deviceObj.macAddress ||
              deviceObj.mac,
            // Include additional device info for filtering
            deviceClass: deviceObj.deviceClass || deviceObj.device_class,
            majorClass: deviceObj.majorClass || deviceObj.major_class,
            minorClass: deviceObj.minorClass || deviceObj.minor_class,
            rssi: deviceObj.rssi,
            rawDevice: deviceObj, // Keep raw device for debugging
          };

          return processedDevice;
        })
        .filter((device: any) => {
          // Filter 1: Basic validation
          const isValid = device !== null && device.address;
          if (!isValid) {
            return false;
          }

          // Filter 2: Check if device is likely a printer
          const isPrinter = this.isPrinterDevice(device);
          if (!isPrinter) {
            return false;
          }

          return true;
        });

      processedDevices.forEach((device, index) => {
        console.log(
          `   Device ${index + 1}: ${device.name} (${device.address})`
        );
      });

      this.pairedDevices = processedDevices;
      return processedDevices;
    } catch (error) {
      console.error("=== Bluetooth scan failed ===");
      console.error("Error details:", error);
      console.error("Error type:", typeof error);
      console.error(
        "Error message:",
        error instanceof Error ? error.message : "Unknown error"
      );

      return [];
    }
  }

  /**
   * Bluetooth device scanning without filtering (returns all devices)
   */
  private async scanBluetoothDevicesUnfiltered(): Promise<
    ThermalPrinterDevice[]
  > {
    try {
      // Step 1: Check if Bluetooth is enabled

      const isEnabled = await BluetoothManager.isBluetoothEnabled();

      if (!isEnabled) {
        return [];
      }

      // Step 2: Use scanDevices method as documented

      const scanResult = await BluetoothManager.scanDevices();

      // Step 3: Parse the scan result
      let parsedResult: any = {};

      if (typeof scanResult === "string") {
        try {
          parsedResult = JSON.parse(scanResult);
        } catch (parseError) {
          console.error("   Failed to parse scan result JSON:", parseError);
          return [];
        }
      } else if (typeof scanResult === "object" && scanResult !== null) {
        parsedResult = scanResult;
      } else {
        return [];
      }

      // Step 4: Extract devices from paired and found arrays
      const pairedDevices = parsedResult.paired || [];
      const foundDevices = parsedResult.found || [];

      // Step 5: Combine and process all devices WITHOUT filtering
      const allDevices = [...pairedDevices, ...foundDevices];

      const processedDevices = allDevices
        .map((device: any, index: number) => {
          // Handle different device formats (string or object)
          let deviceObj = device;
          if (typeof device === "string") {
            try {
              deviceObj = JSON.parse(device);
            } catch (e) {
              return null;
            }
          }

          const processedDevice = {
            name:
              deviceObj.name ||
              deviceObj.device_name ||
              deviceObj.deviceName ||
              `Device ${index + 1}`,
            address:
              deviceObj.address ||
              deviceObj.inner_mac_address ||
              deviceObj.macAddress ||
              deviceObj.mac,
            // Include additional device info
            deviceClass: deviceObj.deviceClass || deviceObj.device_class,
            majorClass: deviceObj.majorClass || deviceObj.major_class,
            minorClass: deviceObj.minorClass || deviceObj.minor_class,
            rssi: deviceObj.rssi,
            rawDevice: deviceObj,
          };

          return processedDevice;
        })
        .filter((device: any) => {
          // Only basic validation - no printer filtering
          const isValid = device !== null && device.address;
          if (!isValid) {
          }
          return isValid;
        });

      processedDevices.forEach((device, index) => {
        console.log(
          `   Device ${index + 1}: ${device.name} (${device.address})`
        );
      });

      return processedDevices;
    } catch (error) {
      console.error("=== Unfiltered Bluetooth scan failed ===");
      console.error("Error details:", error);
      return [];
    }
  }

  /**
   * Smart printer device detection to filter out audio devices and other non-printers
   */
  private isPrinterDevice(device: any): boolean {
    const deviceName = (device.name || "").toLowerCase();
    const deviceClass = device.deviceClass;
    const majorClass = device.majorClass;

    console.log(
      `      Device class: ${deviceClass}, Major class: ${majorClass}`
    );

    // 1. Check device name for printer keywords
    const printerKeywords = [
      // Generic printer terms
      "printer",
      "print",
      "pos",
      "receipt",
      "thermal",
      "escpos",

      // Popular thermal printer brands
      "xprinter",
      "epson",
      "star",
      "bixolon",
      "citizen",
      "zebra",
      "datamax",
      "honeywell",
      "intermec",
      "tsc",
      "godex",
      "argox",
      "sato",
      "cab",
      "brady",
      "dymo",
      "brother",
      "canon",

      // Model prefixes/suffixes
      "tm-",
      "rp-",
      "sp-",
      "tsp-",
      "srp-",
      "xp-",
      "ct-",
      "ptp-",
      "zd-",
      "gk-",
      "gx-",
      "gt-",
      "lp-",
      "mp-",
      "cp-",

      // Receipt printer specific
      "receipt",
      "thermal",
      "pos",
      "cash",
      "register",

      // Label printer specific
      "label",
      "barcode",
      "qr",
    ];

    const hasPrinterKeyword = printerKeywords.some((keyword) =>
      deviceName.includes(keyword)
    );

    if (hasPrinterKeyword) {
      return true;
    }

    // 2. Exclude obvious audio devices
    const audioKeywords = [
      "headphone",
      "headset",
      "earphone",
      "earbud",
      "speaker",
      "audio",
      "music",
      "sound",
      "beats",
      "sony",
      "bose",
      "jbl",
      "airpod",
      "galaxy bud",
      "pixel bud",
      "wireless",
      "bluetooth speaker",
      "soundbar",
      "subwoofer",
      "microphone",
      "mic",
      "hands-free",
      "car audio",
      "stereo",
      "radio",
      "fm",
      "am",
    ];

    const hasAudioKeyword = audioKeywords.some((keyword) =>
      deviceName.includes(keyword)
    );

    if (hasAudioKeyword) {
      return false;
    }

    // 3. Check Bluetooth device class (if available)
    if (deviceClass) {
      // Bluetooth device class format: 0xAABBCC
      // AA = Service classes, BB = Major device class, CC = Minor device class

      // Major device classes:
      // 0x02 = Phone, 0x04 = Audio/Video, 0x05 = Peripheral (includes printers)
      // 0x06 = Imaging (includes printers), 0x08 = Wearable, 0x09 = Toy

      const majorDeviceClass = (deviceClass >> 8) & 0x1f;
      console.log(
        `      Major device class: 0x${majorDeviceClass.toString(16)}`
      );

      // Audio/Video devices (exclude)
      if (majorDeviceClass === 0x04) {
        return false;
      }

      // Peripheral devices (likely printers)
      if (majorDeviceClass === 0x05) {
        return true;
      }

      // Imaging devices (includes printers)
      if (majorDeviceClass === 0x06) {
        return true;
      }
    }

    // 4. Check for common printer MAC address prefixes (OUI - Organizationally Unique Identifier)
    const address = device.address || "";
    const printerOUIs = [
      // Common thermal printer manufacturer OUIs
      "00:15:99", // Seiko Epson
      "00:22:58", // Star Micronics
      "00:07:61", // Bixolon
      "00:80:92", // Citizen
      "00:A0:D1", // Zebra Technologies
      "00:1C:7C", // Pertech Resources
      "00:12:F0", // TSC Auto ID Technology
    ];

    const hasKnownPrinterOUI = printerOUIs.some((oui) =>
      address.toUpperCase().startsWith(oui)
    );

    if (hasKnownPrinterOUI) {
      return true;
    }

    // 5. If device name is very generic, be more permissive
    const genericNames = ["device", "bluetooth", "unknown", ""];
    const isGenericName = genericNames.some(
      (generic) => deviceName === generic || deviceName === ""
    );

    if (isGenericName) {
      return true; // Let user decide
    }

    // 6. Default: exclude if no printer indicators found

    return false;
  }

  /**
   * USB device scanning (Android only)
   */
  private async scanUSBDevices(): Promise<ThermalPrinterDevice[]> {
    if (Platform.OS !== "android") {
      throw new Error("USB printing is only supported on Android");
    }

    try {
      const usbDevices = await USBPrinter.getDeviceList();

      if (!usbDevices || usbDevices.length === 0) {
        return [];
      }

      return usbDevices.map((device: any) => ({
        name:
          device.device_name ||
          device.name ||
          `USB Printer (${device.vendor_id}:${device.product_id})`,
        address: `${device.vendor_id}:${device.product_id}`,
      }));
    } catch (error) {
      console.error("USB scanning error:", error);
      throw new Error(`USB device scanning failed: ${error.message}`);
    }
  }

  /**
   * Network printer discovery
   */
  private async getNetworkPrinters(): Promise<ThermalPrinterDevice[]> {
    // For network printers, we provide manual entry option
    return [
      {
        name: "Add Network Printer Manually",
        address: "manual",
      },
    ];
  }

  /**
   * Connect to a thermal printer
   */
  async connectToPrinter(address: string, port?: number): Promise<boolean> {
    try {
      switch (this.printerType) {
        case "ble":
          await BluetoothManager.connect(address);
          break;
        case "usb":
          await USBPrinter.connectPrinter(address);
          break;
        case "net":
          await NetPrinter.connectPrinter(address, port || 9100);
          break;
      }

      this.isConnected = true;
      this.connectedDevice = { name: "Connected Printer", address, port };

      // Save the connected printer
      await AsyncStorage.setItem(this.PRINTER_STORAGE_KEY, address);

      return true;
    } catch (error) {
      console.error("Error connecting to thermal printer:", error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Format currency for KES
   */
  private formatCurrency(amount: number): string {
    // Use centralized currency formatting
    const { formatCurrency } = require("@/src/utils/currencyUtils");
    return formatCurrency(amount);
  }

  /**
   * Create properly aligned two-column line for thermal printer
   * @param leftText - Text for left column
   * @param rightText - Text for right column
   * @param totalWidth - Total character width (default: 32 for 58mm thermal)
   * @returns Properly aligned string
   */
  private createAlignedLine(
    leftText: string,
    rightText: string,
    totalWidth: number = 32
  ): string {
    // Ensure we don't exceed the total width
    const maxLeftWidth = totalWidth - rightText.length - 2; // Reserve 2 chars minimum spacing
    const truncatedLeft =
      leftText.length > maxLeftWidth
        ? leftText.substring(0, maxLeftWidth - 1) + "…"
        : leftText;

    // Calculate spacing needed
    const spacing = Math.max(
      1,
      totalWidth - truncatedLeft.length - rightText.length
    );

    return `${truncatedLeft}${" ".repeat(spacing)}${rightText}`;
  }

  /**
   * Print enhanced payment details with support for split payments
   */
  private async printPaymentDetails(
    receiptData: ReceiptData,
    delay: (ms: number) => Promise<void>
  ): Promise<void> {
    // Check if we have enhanced payment breakdown
    if (
      receiptData.paymentBreakdown &&
      receiptData.paymentBreakdown.isSplitPayment
    ) {
      await this.printSplitPaymentDetails(receiptData.paymentBreakdown, delay);
    } else {
      await this.printSinglePaymentDetails(receiptData, delay);
    }
  }

  /**
   * Print split payment details
   */
  private async printSplitPaymentDetails(
    breakdown: any,
    delay: (ms: number) => Promise<void>
  ): Promise<void> {
    // Payment header
    await BluetoothEscposPrinter.printText("PAYMENT DETAILS (SPLIT)\n", null);
    await delay(50);

    await BluetoothEscposPrinter.printText(
      `Transaction: ${breakdown.transactionId}\n`,
      null
    );
    await delay(50);

    await BluetoothEscposPrinter.printText(
      `Status: ${breakdown.paymentStatus.toUpperCase()}\n`,
      null
    );
    await delay(50);

    await BluetoothEscposPrinter.printText("------------------------\n", null);
    await delay(50);

    await BluetoothEscposPrinter.printText("PAYMENT BREAKDOWN:\n", null);
    await delay(50);

    // Print each payment method
    for (let i = 0; i < breakdown.paymentMethods.length; i++) {
      const method = breakdown.paymentMethods[i];

      // Method name and amount
      const methodLine = this.createAlignedLine(
        `${i + 1}. ${method.name}`,
        this.formatCurrency(method.amount),
        32
      );
      await BluetoothEscposPrinter.printText(methodLine + "\n", null);
      await delay(50);

      // Status
      await BluetoothEscposPrinter.printText(
        `   Status: ${method.status.toUpperCase()}\n`,
        null
      );
      await delay(50);

      // Method-specific details
      await this.printMethodSpecificDetails(method, delay);

      // Add spacing between methods
      await BluetoothEscposPrinter.printText("\n", null);
      await delay(50);
    }

    // Payment summary
    await BluetoothEscposPrinter.printText("------------------------\n", null);
    await delay(50);

    await BluetoothEscposPrinter.printText("\x1B\x45\x01", null); // Bold on
    await delay(50);

    const summaryLine = this.createAlignedLine(
      "TOTAL PAID:",
      this.formatCurrency(breakdown.completedAmount || 0),
      32
    );
    await BluetoothEscposPrinter.printText(summaryLine + "\n", null);
    await delay(50);

    await BluetoothEscposPrinter.printText("\x1B\x45\x00", null); // Bold off
    await delay(50);

    if (breakdown.remainingAmount && breakdown.remainingAmount > 0) {
      const remainingLine = this.createAlignedLine(
        "REMAINING:",
        this.formatCurrency(breakdown.remainingAmount),
        32
      );
      await BluetoothEscposPrinter.printText(remainingLine + "\n", null);
      await delay(50);
    }
  }

  /**
   * Print single payment details
   */
  private async printSinglePaymentDetails(
    receiptData: ReceiptData,
    delay: (ms: number) => Promise<void>
  ): Promise<void> {
    await BluetoothEscposPrinter.printText(
      `Payment: ${receiptData.paymentMethod}\n`,
      null
    );
    await delay(50);

    if (receiptData.paymentDetails?.transactionId) {
      await BluetoothEscposPrinter.printText(
        `Transaction: ${receiptData.paymentDetails.transactionId}\n`,
        null
      );
      await delay(50);
    }

    // Add single payment method details if available
    if (
      receiptData.paymentBreakdown &&
      receiptData.paymentBreakdown.paymentMethods.length === 1
    ) {
      const method = receiptData.paymentBreakdown.paymentMethods[0];
      await this.printMethodSpecificDetails(method, delay);
    }
  }

  /**
   * Print method-specific payment details
   */
  private async printMethodSpecificDetails(
    method: any,
    delay: (ms: number) => Promise<void>
  ): Promise<void> {
    const metadata = method.metadata || {};

    switch (method.type) {
      case "cash":
        if (metadata.amountTendered) {
          const tenderedLine = this.createAlignedLine(
            "   Amount Tendered:",
            this.formatCurrency(metadata.amountTendered),
            32
          );
          await BluetoothEscposPrinter.printText(tenderedLine + "\n", null);
          await delay(50);

          if (metadata.change && metadata.change > 0) {
            const changeLine = this.createAlignedLine(
              "   Change:",
              this.formatCurrency(metadata.change),
              32
            );
            await BluetoothEscposPrinter.printText(changeLine + "\n", null);
            await delay(50);
          }
        }
        break;

      case "mpesa":
        if (metadata.mpesaReceiptNumber) {
          await BluetoothEscposPrinter.printText(
            `   M-Pesa Receipt: ${metadata.mpesaReceiptNumber}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.transactionCode) {
          await BluetoothEscposPrinter.printText(
            `   Transaction Code: ${metadata.transactionCode}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.phoneNumber) {
          await BluetoothEscposPrinter.printText(
            `   Phone: ${metadata.phoneNumber}\n`,
            null
          );
          await delay(50);
        }
        break;

      case "absa_till":
        if (metadata.transactionCode) {
          await BluetoothEscposPrinter.printText(
            `   Transaction Code: ${metadata.transactionCode}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.tillNumber) {
          await BluetoothEscposPrinter.printText(
            `   Till Number: ${metadata.tillNumber}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.absaReceiptNumber) {
          await BluetoothEscposPrinter.printText(
            `   ABSA Receipt: ${metadata.absaReceiptNumber}\n`,
            null
          );
          await delay(50);
        }
        break;

      case "card":
        if (metadata.cardType) {
          await BluetoothEscposPrinter.printText(
            `   Card Type: ${metadata.cardType}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.lastFourDigits) {
          await BluetoothEscposPrinter.printText(
            `   Card: ****${metadata.lastFourDigits}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.authorizationCode) {
          await BluetoothEscposPrinter.printText(
            `   Auth Code: ${metadata.authorizationCode}\n`,
            null
          );
          await delay(50);
        }
        break;

      case "credit":
        if (metadata.dueDate) {
          await BluetoothEscposPrinter.printText(
            `   Due Date: ${metadata.dueDate}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.paymentTerms) {
          await BluetoothEscposPrinter.printText(
            `   Terms: ${metadata.paymentTerms}\n`,
            null
          );
          await delay(50);
        }
        if (metadata.creditLimit) {
          const creditLine = this.createAlignedLine(
            "   Credit Limit:",
            this.formatCurrency(metadata.creditLimit),
            32
          );
          await BluetoothEscposPrinter.printText(creditLine + "\n", null);
          await delay(50);
        }
        break;
    }
  }

  /**
   * Print receipt using thermal printer with KES formatting
   */
  async printThermalReceipt(
    receiptData: ReceiptData
  ): Promise<ThermalPrintResult> {
    if (!this.isConnected) {
      return { success: false, error: "Thermal printer not connected" };
    }

    try {
      // Helper function to add delay between commands
      const delay = (ms: number) =>
        new Promise((resolve) => setTimeout(resolve, ms));

      // Initialize printer with delay - CRITICAL for preventing COMMAND_NOT_SEND
      await BluetoothEscposPrinter.printerInit();
      await delay(200); // Increased delay for initialization

      // Print header
      await BluetoothEscposPrinter.printerAlign(
        BluetoothEscposPrinter.ALIGN.CENTER
      );
      await delay(50);

      await BluetoothEscposPrinter.printText(
        `${receiptData.store.name}\n`,
        null
      );
      await delay(50);

      if (receiptData.store.address) {
        await BluetoothEscposPrinter.printText(
          `${receiptData.store.address}\n`,
          null
        );
        await delay(50);
      }
      if (receiptData.store.phone) {
        // await BluetoothEscposPrinter.printText(
        //   `Mobile: ${receiptData.store.phone}\n`,
        //   null
        // );
        await BluetoothEscposPrinter.printText(
          `Mobile: +254 111 443 993\n`,
          null
        );
        await delay(50);
      }
      await BluetoothEscposPrinter.printText(
        `Email: <EMAIL>\n`,
        null
      );
      await delay(50);
      await BluetoothEscposPrinter.printText(
        `Website: www.treasuredscents.co.ke\n`,
        null
      );
      await delay(50);

      await BluetoothEscposPrinter.printText(
        "------------------------\n",
        null
      );
      await delay(50);

      // Print receipt info
      const date = new Date(receiptData.orderDate);
      const dateStr = date.toLocaleDateString();
      const timeStr = date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });

      await BluetoothEscposPrinter.printText("SALES RECEIPT\n", null);
      await delay(50);
      await BluetoothEscposPrinter.printText(
        `Receipt No: ${receiptData.orderNumber}\n`,
        null
      );
      await delay(50);
      await BluetoothEscposPrinter.printText(
        `Date: ${dateStr} ${timeStr}\n\n`,
        null
      );
      await delay(50);

      // Print customer info
      await BluetoothEscposPrinter.printerAlign(
        BluetoothEscposPrinter.ALIGN.LEFT
      );
      await delay(50);
      await BluetoothEscposPrinter.printText(
        `Customer: ${receiptData.customer.name}\n`,
        null
      );
      await delay(50);

      if (receiptData.customer.email) {
        await BluetoothEscposPrinter.printText(
          `Email: ${receiptData.customer.email}\n`,
          null
        );
        await delay(50);
      }
      if (receiptData.customer.phone) {
        await BluetoothEscposPrinter.printText(
          `Mobile: ${receiptData.customer.phone}\n`,
          null
        );
        await delay(50);
      }

      // Print staff info
      await BluetoothEscposPrinter.printText(
        `Served by: ${receiptData.staff.name}\n`,
        null
      );
      await delay(50);

      // Print sales agent if available
      if (receiptData.salesAgent) {
        await BluetoothEscposPrinter.printText(
          `Sales Agent: ${receiptData.salesAgent.name}\n`,
          null
        );
        await delay(50);
      }

      // Print items section
      await BluetoothEscposPrinter.printText("\n", null);
      await delay(50);
      await BluetoothEscposPrinter.printText(
        "------------------------\n",
        null
      );
      await delay(50);
      await BluetoothEscposPrinter.printText("ITEMS:\n", null);
      await delay(50);

      for (const item of receiptData.items) {
        // Print item name (left-aligned)
        await BluetoothEscposPrinter.printerAlign(
          BluetoothEscposPrinter.ALIGN.LEFT
        );
        await delay(50);
        await BluetoothEscposPrinter.printText(`${item.title}\n`, null);
        await delay(50);

        // Print variant and SKU if available (left-aligned, indented)
        if (item.variantTitle) {
          await BluetoothEscposPrinter.printText(
            `  Variant: ${item.variantTitle}\n`,
            null
          );
          await delay(50);
        }
        if (item.sku) {
          await BluetoothEscposPrinter.printText(`  SKU: ${item.sku}\n`, null);
          await delay(50);
        }

        // Create aligned price line (quantity x price on left, total on right)
        const qtyPrice = `${item.quantity} x ${this.formatCurrency(
          parseFloat(item.price)
        )}`;
        const total = this.formatCurrency(
          parseFloat(item.price) * item.quantity
        );

        // Use the new alignment function for better formatting
        const alignedLine = this.createAlignedLine(qtyPrice, total, 32) + "\n";

        await BluetoothEscposPrinter.printText(alignedLine, null);
        await delay(50);

        // Add spacing between items
        await BluetoothEscposPrinter.printText("\n", null);
        await delay(50);
      }

      // Print totals
      await BluetoothEscposPrinter.printText(
        "------------------------\n",
        null
      );
      await delay(50);

      // Create aligned subtotal line
      await BluetoothEscposPrinter.printerAlign(
        BluetoothEscposPrinter.ALIGN.LEFT
      );
      await delay(50);

      const subtotalLine =
        this.createAlignedLine(
          "Subtotal:",
          this.formatCurrency(receiptData.total),
          32
        ) + "\n";
      await BluetoothEscposPrinter.printText(subtotalLine, null);
      await delay(50);

      // Delivery fee (default standard delivery)
      const shippingFee = 200.0; // Default standard delivery fee
      const shippingLine =
        this.createAlignedLine(
          "Delivery Fee:",
          this.formatCurrency(shippingFee),
          32
        ) + "\n";
      await BluetoothEscposPrinter.printText(shippingLine, null);
      await delay(50);

      await BluetoothEscposPrinter.printText(
        "------------------------\n",
        null
      );
      await delay(50);

      const totalLabel = "TOTAL:";
      const totalAmount = this.formatCurrency(receiptData.total + shippingFee);

      // Use the new alignment function for better total formatting
      const totalLine =
        this.createAlignedLine(totalLabel, totalAmount, 32) + "\n";

      // Make total bold
      await BluetoothEscposPrinter.printText("\x1B\x45\x01", null); // Bold on
      await delay(50);
      await BluetoothEscposPrinter.printText(totalLine, null);
      await delay(50);
      await BluetoothEscposPrinter.printText("\x1B\x45\x00", null); // Bold off
      await delay(50);

      // Print enhanced payment info
      await this.printPaymentDetails(receiptData, delay);

      // Print loyalty points section if available (enhanced display)
      if (receiptData.loyaltyPoints && receiptData.loyaltyPoints.balance > 0) {
        await BluetoothEscposPrinter.printerAlign(
          BluetoothEscposPrinter.ALIGN.CENTER
        );
        await delay(50);
        await BluetoothEscposPrinter.printText("\n🌟 LOYALTY REWARDS 🌟\n", {
          fonttype: 1,
          widthtimes: 1,
          heigthtimes: 1,
        });
        await delay(50);
        await BluetoothEscposPrinter.printText(
          `Total TS Points: ${receiptData.loyaltyPoints.balance.toLocaleString()}\n`,
          { fonttype: 1, widthtimes: 1, heigthtimes: 1 }
        );
        await delay(50);
        // Remove member ID display as per user preference
      }

      // Print footer
      await BluetoothEscposPrinter.printerAlign(
        BluetoothEscposPrinter.ALIGN.CENTER
      );
      await delay(50);
      await BluetoothEscposPrinter.printText(
        "\nYou're treasured! Thank you\n",
        null
      );
      await delay(50);
      await BluetoothEscposPrinter.printText("Powered by Dukalink POS\n", null);
      await delay(50);
      await BluetoothEscposPrinter.printText("\n\n\n", null);
      await delay(100);

      // Flush the printer buffer to ensure all commands are sent
      await BluetoothEscposPrinter.printAndFeed(1);
      await delay(200); // Final delay to ensure completion

      return { success: true };
    } catch (error) {
      console.error("Error printing thermal receipt:", error);
      return {
        success: false,
        error: `Failed to print thermal receipt: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      };
    }
  }

  /**
   * Print test receipt
   */
  async printTestReceipt(): Promise<ThermalPrintResult> {
    const testReceiptData: ReceiptData = {
      orderNumber: "TEST-001",
      orderDate: new Date().toISOString(),
      customer: {
        name: "John Doe",
        email: "<EMAIL>",
        phone: "+254700000000",
      },
      staff: {
        name: "Test Staff",
        role: "Cashier",
      },
      items: [
        {
          id: "1",
          title: "Test Item 1",
          quantity: 1,
          price: "100.00",
          sku: "TEST-001",
        },
        {
          id: "2",
          title: "Test Item 2",
          quantity: 2,
          price: "50.00",
          sku: "TEST-002",
        },
      ],
      subtotal: 200.0,
      tax: 0, // No tax calculations in POS system
      total: 200.0,
      paymentMethod: "Cash",
      paymentDetails: {
        transactionId: "TEST-TXN-001",
      },
      loyaltyPoints: {
        earned: 25, // Test data: Points earned this transaction
        balance: 1250, // Test data: Total TS Points balance
      },
      store: {
        name: "TREASURED SCENTS",
        address: "Greenhouse Mall, Ngong Road, Kenya",
        phone: "+***********-933",
      },
    };

    return await this.printThermalReceipt(testReceiptData);
  }

  /**
   * Disconnect from thermal printer
   */
  async disconnect(): Promise<void> {
    try {
      switch (this.printerType) {
        case "ble":
          await BluetoothManager.disconnect();
          break;
        case "usb":
          await USBPrinter.closeConn();
          break;
        case "net":
          await NetPrinter.closeConn();
          break;
      }

      this.isConnected = false;
      this.connectedDevice = null;
    } catch (error) {
      console.error("Error disconnecting thermal printer:", error);
    }
  }

  // Getters
  getPrinterType(): string | null {
    return this.printerType;
  }

  getConnectedDevice(): ThermalPrinterDevice | null {
    return this.connectedDevice;
  }

  isConnectedToPrinter(): boolean {
    return this.isConnected;
  }

  /**
   * Check if specifically connected to P502A printer
   */
  async isConnectedToP502A(): Promise<boolean> {
    if (!this.isConnected || !this.connectedDevice) {
      return false;
    }

    // Check if connected device is the P502A printer
    const targetAddress = "A5:9B:34:04:1B:11";
    return this.connectedDevice.address === targetAddress;
  }

  /**
   * Verify actual printer connection (not just state)
   * FIXED: BluetoothManager.isConnected() doesn't exist in the API
   */
  async verifyPrinterConnection(): Promise<boolean> {
    if (!this.isConnected || !this.connectedDevice) {
      return false;
    }

    try {
      // For Bluetooth printers, try to verify the connection by attempting a simple operation
      if (this.printerType === "ble") {
        // Try to reconnect to the same device to verify connection
        // If it fails, the connection was lost
        try {
          await BluetoothManager.connect(this.connectedDevice.address);
          // If connect succeeds, we're still connected
          return true;
        } catch (connectError) {
          // Connection was lost, update our state
          console.log(
            "Printer connection lost, attempting to reconnect...",
            connectError
          );
          this.isConnected = false;
          this.connectedDevice = null;
          return false;
        }
      }

      // For other printer types, assume connection is valid if state says so
      return true;
    } catch (error) {
      console.error("Error verifying printer connection:", error);
      // If verification fails, assume connection is lost
      this.isConnected = false;
      this.connectedDevice = null;
      return false;
    }
  }
}

// Export singleton instance
export default new ThermalPrintService();
