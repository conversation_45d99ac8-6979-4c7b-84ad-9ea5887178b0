/**
 * RBAC Test Screen
 *
 * Development screen for testing role-based access control
 * across all loyalty and discount management features.
 *
 * This screen should only be accessible in development mode.
 */

import React from "react";
import { View, StyleSheet, Alert } from "react-native";
import { router } from "expo-router";
import { useTheme } from "@/src/contexts/ThemeContext";
import { RBACTestRunner } from "@/src/components/testing/RBACTestRunner";
import { ScreenWrapper } from "@/components/layout/ScreenWrapper";

export default function RBACTestScreen() {
  const theme = useTheme();

  // Only allow access in development mode
  if (__DEV__ !== true) {
    Alert.alert(
      "Access Denied",
      "RBAC testing is only available in development mode.",
      [{ text: "OK", onPress: () => router.back() }]
    );
    return null;
  }

  const handleClose = () => {
    router.back();
  };

  return (
    <ScreenWrapper title="RBAC Testing" showBackButton>
      <View
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        <RBACTestRunner onClose={handleClose} />
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
