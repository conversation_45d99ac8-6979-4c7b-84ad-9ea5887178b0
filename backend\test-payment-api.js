/**
 * Payment API Testing Script
 * Tests all payment endpoints with proper authentication
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";
let authToken = "";

// Test data
const testData = {
  login: {
    username: "admin1",
    password: "admin123",
  },
  transaction: {
    totalAmount: 1000,
    currency: "KES",
    customerId: "test-customer-123",
    staffId: "test-staff-123",
    terminalId: "TERMINAL_001",
    locationId: "test-location-123",
    paymentMethods: [
      {
        methodType: "cash",
        methodName: "Cash Payment",
        amount: 600,
        metadata: {},
      },
      {
        methodType: "mpesa",
        methodName: "M-Pesa Payment",
        amount: 400,
        metadata: {},
      },
    ],
    metadata: {
      customerName: "Test Customer",
      orderData: { test: true },
    },
  },
};

// Helper function to make authenticated requests
async function makeRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        "Content-Type": "application/json",
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
      },
      ...(data && { data }),
    };

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

// Test functions
async function testLogin() {
  console.log("\n🔐 Testing Authentication...");

  const result = await makeRequest("POST", "/api/pos/login", testData.login);

  if (result.success && result.data.success) {
    authToken = result.data.data.token;
    console.log("✅ Login successful");
    console.log(`   Token: ${authToken.substring(0, 20)}...`);
    console.log(
      `   User: ${result.data.data.user.name} (${result.data.data.user.role})`
    );
    return true;
  } else {
    console.log("❌ Login failed:", result.error);
    return false;
  }
}

async function testInitiateTransaction() {
  console.log("\n💰 Testing Transaction Initiation...");

  const result = await makeRequest(
    "POST",
    "/api/payments/initiate",
    testData.transaction
  );

  if (result.success && result.data.success) {
    console.log("✅ Transaction initiated successfully");
    console.log(`   Transaction ID: ${result.data.data.transactionId}`);
    console.log(`   Status: ${result.data.data.status}`);
    console.log(`   Total Amount: ${result.data.data.totalAmount}`);
    console.log(`   Remaining: ${result.data.data.remainingAmount}`);
    return result.data.data.transactionId;
  } else {
    console.log("❌ Transaction initiation failed:", result.error);
    return null;
  }
}

async function testAddPaymentMethod(transactionId) {
  console.log("\n💳 Testing Add Payment Method...");

  const paymentMethod = {
    methodType: "card",
    methodName: "Credit Card",
    amount: 500,
    metadata: { cardType: "visa" },
  };

  const result = await makeRequest(
    "POST",
    `/api/payments/${transactionId}/methods`,
    paymentMethod
  );

  if (result.success && result.data.success) {
    console.log("✅ Payment method added successfully");
    console.log(`   Method ID: ${result.data.data.methodId}`);
    console.log(`   Status: ${result.data.data.status}`);
    return result.data.data.methodId;
  } else {
    console.log("❌ Add payment method failed:", result.error);
    return null;
  }
}

async function testProcessPaymentMethod(methodId) {
  console.log("\n⚡ Testing Process Payment Method...");

  const processingData = {
    confirmed: true,
    cardType: "visa",
    authorizationCode: "AUTH123456",
  };

  const result = await makeRequest(
    "POST",
    `/api/payments/methods/${methodId}/process`,
    processingData
  );

  if (result.success && result.data.success) {
    console.log("✅ Payment method processed successfully");
    console.log(`   Status: ${result.data.data.status}`);
    return true;
  } else {
    console.log("❌ Process payment method failed:", result.error);
    return false;
  }
}

async function testGetTransactionStatus(transactionId) {
  console.log("\n📊 Testing Get Transaction Status...");

  const result = await makeRequest(
    "GET",
    `/api/payments/${transactionId}/status`
  );

  if (result.success && result.data.success) {
    console.log("✅ Transaction status retrieved successfully");
    console.log(`   Status: ${result.data.data.status}`);
    console.log(`   Total Amount: ${result.data.data.totalAmount}`);
    console.log(`   Completed Amount: ${result.data.data.completedAmount}`);
    console.log(`   Remaining Amount: ${result.data.data.remainingAmount}`);
    console.log(`   Payment Methods: ${result.data.data.summary.totalMethods}`);
    return true;
  } else {
    console.log("❌ Get transaction status failed:", result.error);
    return false;
  }
}

async function testSplitPaymentSummary(transactionId) {
  console.log("\n📋 Testing Split Payment Summary...");

  const result = await makeRequest(
    "GET",
    `/api/payments/${transactionId}/split-summary`
  );

  if (result.success && result.data.success) {
    console.log("✅ Split payment summary retrieved successfully");
    console.log(
      `   Transaction Status: ${result.data.data.transaction.status}`
    );
    console.log(`   Total Methods: ${result.data.data.paymentMethods.total}`);
    console.log(
      `   Completed: ${result.data.data.paymentMethods.byStatus.completed}`
    );
    console.log(
      `   Pending: ${result.data.data.paymentMethods.byStatus.pending}`
    );
    return true;
  } else {
    console.log("❌ Get split payment summary failed:", result.error);
    return false;
  }
}

async function testValidateSplitPayment(transactionId) {
  console.log("\n✅ Testing Validate Split Payment...");

  const paymentMethods = [
    { methodType: "cash", amount: 300 },
    { methodType: "mpesa", amount: 200 },
  ];

  const result = await makeRequest(
    "POST",
    `/api/payments/${transactionId}/validate-split`,
    { paymentMethods }
  );

  if (result.success && result.data.success) {
    console.log("✅ Split payment validation successful");
    console.log(`   Is Valid: ${result.data.data.isValid}`);
    console.log(`   Total Amount: ${result.data.data.totalAmount}`);
    console.log(`   Method Count: ${result.data.data.methodCount}`);
    return true;
  } else {
    console.log("❌ Validate split payment failed:", result.error);
    return false;
  }
}

async function testCompleteWithShopify(transactionId) {
  console.log("\n🛍️ Testing Complete with Shopify...");

  const orderData = {
    line_items: [
      {
        variant_id: "test-variant-123",
        quantity: 2,
        price: "500.00",
        title: "Test Product",
        sku: "TEST-SKU-001",
      },
    ],
    customer: {
      id: "test-customer-123",
      first_name: "Test",
      last_name: "Customer",
      email: "<EMAIL>",
      phone: "+254700000000",
    },
  };

  const result = await makeRequest(
    "POST",
    `/api/payments/${transactionId}/complete-with-shopify`,
    { orderData }
  );

  if (result.success && result.data.success) {
    console.log("✅ Shopify order creation successful");
    console.log(`   Shopify Order ID: ${result.data.data.shopifyOrderId}`);
    console.log(`   Order Number: ${result.data.data.orderNumber}`);
    console.log(`   Payments Created: ${result.data.data.paymentsCreated}`);
    console.log(`   Financial Status: ${result.data.data.financialStatus}`);
    return true;
  } else {
    console.log("❌ Complete with Shopify failed:", result.error);
    return false;
  }
}

async function testCompleteWorkflow() {
  console.log("\n🔄 Testing Complete Payment Workflow...");

  // Create a simple transaction with one payment method
  const simpleTransaction = {
    totalAmount: 500,
    currency: "KES",
    customerId: "test-customer-456",
    staffId: "test-staff-456",
    terminalId: "TERMINAL_002",
    locationId: "test-location-456",
    paymentMethods: [
      {
        methodType: "cash",
        methodName: "Cash Payment",
        amount: 500,
        metadata: {},
      },
    ],
    metadata: {
      customerName: "Complete Test Customer",
      orderData: { workflow: "complete" },
    },
  };

  // Initiate transaction
  const initResult = await makeRequest(
    "POST",
    "/api/payments/initiate",
    simpleTransaction
  );
  if (!initResult.success) {
    console.log("❌ Complete workflow failed at initiation:", initResult.error);
    return false;
  }

  console.log("   Init result:", JSON.stringify(initResult, null, 2));
  const transactionId = initResult.data?.transactionId;
  console.log(`   Transaction created: ${transactionId}`);

  // Get the payment method ID from the transaction
  const statusResult = await makeRequest(
    "GET",
    `/api/payments/${transactionId}/status`
  );
  if (!statusResult.success) {
    console.log(
      "❌ Complete workflow failed getting status:",
      statusResult.error
    );
    return false;
  }

  const methodId = statusResult.data.paymentMethods[0].id;
  console.log(`   Payment method ID: ${methodId}`);

  // Process the payment method
  const processResult = await makeRequest(
    "POST",
    `/api/payments/methods/${methodId}/process`,
    {
      confirmed: true,
      amountTendered: 500,
    }
  );

  if (!processResult.success) {
    console.log(
      "❌ Complete workflow failed processing payment:",
      processResult.error
    );
    return false;
  }

  console.log(`   Payment processed: ${processResult.data.status}`);

  // Check final status
  const finalStatus = await makeRequest(
    "GET",
    `/api/payments/${transactionId}/status`
  );
  if (finalStatus.success) {
    console.log(`   Final transaction status: ${finalStatus.data.status}`);
    console.log(`   Completed amount: ${finalStatus.data.completedAmount}`);
    console.log(`   Remaining amount: ${finalStatus.data.remainingAmount}`);
  }

  console.log("✅ Complete workflow test successful");
  return true;
}

// Main test runner
async function runAllTests() {
  console.log("🚀 Starting Payment API Tests...");
  console.log("=====================================");

  // Test authentication first
  const loginSuccess = await testLogin();
  if (!loginSuccess) {
    console.log("\n❌ Cannot proceed without authentication");
    return;
  }

  // Test transaction initiation
  const transactionId = await testInitiateTransaction();
  if (!transactionId) {
    console.log("\n❌ Cannot proceed without transaction ID");
    return;
  }

  // Test adding payment method
  const methodId = await testAddPaymentMethod(transactionId);
  if (!methodId) {
    console.log("\n❌ Cannot proceed without method ID");
    return;
  }

  // Test processing payment method
  await testProcessPaymentMethod(methodId);

  // Test getting transaction status
  await testGetTransactionStatus(transactionId);

  // Test split payment summary
  await testSplitPaymentSummary(transactionId);

  // Test validate split payment
  await testValidateSplitPayment(transactionId);

  // Test complete with Shopify (this might fail if Shopify is not configured)
  await testCompleteWithShopify(transactionId);

  // Test complete workflow
  await testCompleteWorkflow();

  console.log("\n🎉 Payment API Tests Completed!");
  console.log("=====================================");
}

// Run tests
runAllTests().catch(console.error);
