use crate::schema;
use shopify_function::prelude::*;
use shopify_function::Result;

#[derive(Deserialize, Default, PartialEq)]
pub struct Configuration {
    // Commission-based discount configuration
    pub staff_discount_rate: f64,           // Discount rate based on staff commission
    pub agent_discount_rate: f64,           // Discount rate based on sales agent commission
    pub loyalty_multiplier: f64,            // Multiplier for loyal customers
    pub min_order_amount: f64,              // Minimum order amount for discount eligibility
    pub max_discount_percentage: f64,       // Maximum discount percentage allowed
    pub commission_threshold: f64,          // Minimum commission rate to qualify for discount

    // Enhanced loyalty configuration
    pub loyalty_enabled: bool,              // Enable loyalty-based discounts
    pub tier_discounts: TierDiscounts,      // Tier-specific discount rates
    pub points_redemption_enabled: bool,    // Enable points redemption
    pub points_to_currency_rate: f64,       // Points to currency conversion rate (e.g., 100 points = 1 KSh)
    pub min_points_redemption: i32,         // Minimum points required for redemption
    pub max_points_per_order: i32,          // Maximum points that can be redeemed per order
    pub loyalty_points_earning_rate: f64,   // Points earned per currency unit spent
}

#[derive(Deserialize, Default, PartialEq)]
pub struct TierDiscounts {
    pub bronze: f64,    // Bronze tier discount percentage
    pub silver: f64,    // Silver tier discount percentage
    pub gold: f64,      // Gold tier discount percentage
    pub platinum: f64,  // Platinum tier discount percentage
}

#[shopify_function]
fn run(input: schema::run::Input) -> Result<schema::FunctionRunResult> {
    // Parse configuration or return empty discount if no configuration exists
    let config: &Configuration = match input.discount_node().metafield() {
        Some(metafield) => metafield.json_value(),
        None => return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::First,
        }),
    };

    // Check minimum order amount
    let order_total = input.cart().cost().subtotal_amount().amount().0;
    if order_total < Decimal(config.min_order_amount) {
        return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::First,
        });
    }

    // Extract staff and sales agent information from cart attributes
    let staff_id = input.cart().attribute("staff_id")
        .and_then(|attr| attr.value().as_ref());
    let sales_agent_id = input.cart().attribute("sales_agent_id")
        .and_then(|attr| attr.value().as_ref());

    // Get customer data for loyalty processing
    let customer = input.cart().buyer_identity()
        .and_then(|identity| identity.customer());

    // Get loyalty tier (try new namespace first, fallback to legacy)
    let loyalty_tier = customer
        .and_then(|c| c.loyalty_tier())
        .and_then(|metafield| metafield.value().as_ref())
        .or_else(|| {
            customer
                .and_then(|c| c.metafield("dukalink", "loyalty_tier"))
                .and_then(|metafield| metafield.value().as_ref())
        })
        .unwrap_or("bronze");

    // Get loyalty points balance
    let loyalty_points: i32 = customer
        .and_then(|c| c.loyalty_points())
        .and_then(|metafield| metafield.value().as_ref())
        .and_then(|value| value.parse().ok())
        .unwrap_or(0);

    // Check if customer has loyalty member tag
    let is_loyalty_member = customer
        .map(|c| c.tags().iter().any(|tag| tag == "loyalty-member"))
        .unwrap_or(false);

    // Calculate commission-based discount rate
    let mut discount_rate = 0.0;

    // Add staff-based discount
    if staff_id.is_some() {
        discount_rate += config.staff_discount_rate;
    }

    // Add sales agent-based discount
    if sales_agent_id.is_some() {
        discount_rate += config.agent_discount_rate;
    }

    // Apply loyalty tier-based discounts if enabled
    if config.loyalty_enabled && is_loyalty_member {
        let tier_discount = match loyalty_tier {
            "platinum" => config.tier_discounts.platinum,
            "gold" => config.tier_discounts.gold,
            "silver" => config.tier_discounts.silver,
            "bronze" => config.tier_discounts.bronze,
            _ => 0.0,
        };
        discount_rate += tier_discount;
    }

    // Apply legacy loyalty multiplier for backward compatibility
    if !config.loyalty_enabled {
        let loyalty_multiplier = match loyalty_tier {
            "gold" => config.loyalty_multiplier * 1.5,
            "silver" => config.loyalty_multiplier * 1.2,
            "bronze" => config.loyalty_multiplier,
            _ => 1.0,
        };
        discount_rate *= loyalty_multiplier;
    }

    // Cap the discount rate
    if discount_rate > config.max_discount_percentage {
        discount_rate = config.max_discount_percentage;
    }

    // Calculate points redemption discount if enabled
    let mut points_discount_amount = 0.0;
    let mut points_to_redeem = 0;

    if config.points_redemption_enabled && loyalty_points >= config.min_points_redemption {
        // Check for points redemption request in cart attributes
        if let Some(requested_points_str) = input.cart().attribute("redeem_points")
            .and_then(|attr| attr.value().as_ref()) {
            if let Ok(requested_points) = requested_points_str.parse::<i32>() {
                // Validate redemption limits
                let max_redeemable = std::cmp::min(
                    loyalty_points,
                    std::cmp::min(config.max_points_per_order, requested_points)
                );

                if max_redeemable >= config.min_points_redemption {
                    points_to_redeem = max_redeemable;
                    points_discount_amount = (points_to_redeem as f64) * config.points_to_currency_rate;
                }
            }
        }
    }

    // Only apply discount if it meets the commission threshold or has points redemption
    if discount_rate < config.commission_threshold && points_discount_amount == 0.0 {
        return Ok(schema::FunctionRunResult {
            discounts: vec![],
            discount_application_strategy: schema::DiscountApplicationStrategy::First,
        });
    }

    let mut discounts = vec![];

    // Apply percentage discount to eligible cart lines
    if discount_rate >= config.commission_threshold {
        for line in input.cart().lines() {
            // Check if the product is commission-eligible
            let is_commission_eligible = match &line.merchandise() {
                schema::run::input::cart::lines::Merchandise::ProductVariant(variant) => {
                    variant.product().metafield("dukalink", "commission_eligible")
                        .and_then(|metafield| metafield.value().as_ref())
                        .map(|value| value == "true")
                        .unwrap_or(true) // Default to eligible if not specified
                }
                _ => false,
            };

            if is_commission_eligible {
                let discount_message = format!(
                    "{}% loyalty & commission discount (Staff: {}, Agent: {}, Tier: {})",
                    discount_rate,
                    staff_id.unwrap_or("N/A"),
                    sales_agent_id.unwrap_or("N/A"),
                    loyalty_tier
                );

                discounts.push(schema::Discount {
                    value: schema::Value::Percentage(schema::Percentage {
                        value: Decimal(discount_rate),
                    }),
                    targets: vec![schema::Target::CartLine(schema::CartLineTarget {
                        id: line.id().to_string(),
                        quantity: Some(*line.quantity()),
                    })],
                    message: Some(discount_message),
                });
            }
        }
    }

    // Apply points redemption discount as fixed amount on order subtotal
    if points_discount_amount > 0.0 {
        let points_message = format!(
            "Loyalty points redemption: {} points = KSh {:.2}",
            points_to_redeem,
            points_discount_amount
        );

        discounts.push(schema::Discount {
            value: schema::Value::FixedAmount(schema::FixedAmount {
                amount: Decimal(points_discount_amount),
            }),
            targets: vec![schema::Target::OrderSubtotal(schema::OrderSubtotalTarget {})],
            message: Some(points_message),
        });
    }

    Ok(schema::FunctionRunResult {
        discounts,
        discount_application_strategy: schema::DiscountApplicationStrategy::First,
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use shopify_function::{run_function_with_input, Result};

    #[test]
    fn test_commission_based_discount_applied() -> Result<()> {
        let result = run_function_with_input(
            run,
            r#"
                {
                    "cart": {
                        "lines": [
                            {
                                "id": "gid://shopify/CartLine/1",
                                "quantity": 2,
                                "cost": {
                                    "amountPerQuantity": {
                                        "amount": "50.00"
                                    },
                                    "subtotalAmount": {
                                        "amount": "100.00"
                                    }
                                },
                                "merchandise": {
                                    "__typename": "ProductVariant",
                                    "id": "gid://shopify/ProductVariant/1",
                                    "product": {
                                        "id": "gid://shopify/Product/1",
                                        "title": "Test Product",
                                        "tags": ["commission-eligible"],
                                        "metafield": {
                                            "value": "true"
                                        }
                                    }
                                }
                            }
                        ],
                        "cost": {
                            "subtotalAmount": {
                                "amount": "100.00"
                            },
                            "totalAmount": {
                                "amount": "100.00"
                            }
                        },
                        "buyerIdentity": {
                            "customer": {
                                "id": "gid://shopify/Customer/1",
                                "metafield": {
                                    "value": "gold"
                                }
                            }
                        },
                        "attribute": {
                            "value": "pos-002"
                        }
                    },
                    "discountNode": {
                        "metafield": {
                            "jsonValue": {
                                "staff_discount_rate": 3.0,
                                "agent_discount_rate": 5.0,
                                "loyalty_multiplier": 1.2,
                                "min_order_amount": 50.0,
                                "max_discount_percentage": 15.0,
                                "commission_threshold": 2.0
                            }
                        }
                    }
                }
            "#,
        )?;

        assert!(!result.discounts.is_empty());
        Ok(())
    }

    #[test]
    fn test_no_discount_below_minimum_order() -> Result<()> {
        let result = run_function_with_input(
            run,
            r#"
                {
                    "cart": {
                        "lines": [
                            {
                                "id": "gid://shopify/CartLine/1",
                                "quantity": 1,
                                "cost": {
                                    "amountPerQuantity": {
                                        "amount": "25.00"
                                    },
                                    "subtotalAmount": {
                                        "amount": "25.00"
                                    }
                                },
                                "merchandise": {
                                    "__typename": "ProductVariant",
                                    "id": "gid://shopify/ProductVariant/1",
                                    "product": {
                                        "id": "gid://shopify/Product/1",
                                        "title": "Test Product",
                                        "tags": ["commission-eligible"],
                                        "metafield": {
                                            "value": "true"
                                        }
                                    }
                                }
                            }
                        ],
                        "cost": {
                            "subtotalAmount": {
                                "amount": "25.00"
                            },
                            "totalAmount": {
                                "amount": "25.00"
                            }
                        },
                        "buyerIdentity": null,
                        "attribute": null
                    },
                    "discountNode": {
                        "metafield": {
                            "jsonValue": {
                                "staff_discount_rate": 3.0,
                                "agent_discount_rate": 5.0,
                                "loyalty_multiplier": 1.2,
                                "min_order_amount": 50.0,
                                "max_discount_percentage": 15.0,
                                "commission_threshold": 2.0
                            }
                        }
                    }
                }
            "#,
        )?;

        assert!(result.discounts.is_empty());
        Ok(())
    }
}
