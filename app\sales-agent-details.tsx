import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StyleSheet,
  TextInput,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ModernCard } from "@/components/ui/ModernCard";
import { ModernButton } from "@/components/ui/ModernButton";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import {
  SalesAgentEditButton,
  SalesAgentPerformanceView,
} from "@/src/components/rbac";
import { useRBAC } from "@/src/hooks/useRBAC";

interface SalesAgent {
  id: string;
  name: string;
  email: string;
  phone: string;
  commissionRate: number;
  totalSales: number;
  customerCount: number;
  active: boolean;
  territory: string;
  region?: string;
  joinDate?: string;
  totalCommission?: number;
}

interface PerformanceData {
  totalSales: number;
  totalCommission: number;
  customerCount: number;
  averageOrderValue: number;
  monthlyGrowth: number;
  topCustomers: Array<{
    id: string;
    name: string;
    totalSpent: number;
  }>;
}

export default function SalesAgentDetailsScreen() {
  const router = useRouter();
  const { agentId } = useLocalSearchParams<{ agentId: string }>();
  const { setCurrentTitle } = useNavigation();
  const { canManageStaff, canViewStaffPerformance } = useRBAC();

  const [agent, setAgent] = useState<SalesAgent | null>(null);
  const [performance, setPerformance] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingCommission, setEditingCommission] = useState(false);
  const [newCommissionRate, setNewCommissionRate] = useState("");

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = "#4CAF50";
  const warningColor = "#FF9500";
  const errorColor = "#FF3B30";

  useEffect(() => {
    if (agentId) {
      loadAgentDetails();
    }
  }, [agentId]);

  const loadAgentDetails = async () => {
    if (!agentId) return;

    try {
      setLoading(true);
      const apiClient = getAPIClient();

      // Load agent details
      const agentResponse = await apiClient.getSalesAgentById(agentId);
      if (agentResponse.success && agentResponse.data) {
        const agentData = agentResponse.data.salesAgent;
        setAgent(agentData);
        setCurrentTitle(agentData.name);
        setNewCommissionRate(agentData.commissionRate.toString());

        // Load performance data if user has permission
        if (canViewStaffPerformance) {
          const performanceResponse = await apiClient.getSalesAgentPerformance(
            agentId
          );
          if (performanceResponse.success && performanceResponse.data) {
            setPerformance(performanceResponse.data.performance);
          }
        }
      } else {
        Alert.alert(
          "Error",
          agentResponse.error || "Failed to load agent details"
        );
        router.back();
      }
    } catch (error) {
      console.error("Load agent details error:", error);
      Alert.alert("Error", "Failed to load agent details");
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCommission = async () => {
    if (!agent || !canManageStaff) return;

    const rate = parseFloat(newCommissionRate);
    if (isNaN(rate) || rate < 0 || rate > 100) {
      Alert.alert("Error", "Commission rate must be between 0 and 100");
      return;
    }

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.updateSalesAgent(agent.id, {
        commissionRate: rate,
      });

      if (response.success) {
        setAgent({ ...agent, commissionRate: rate });
        setEditingCommission(false);
        Alert.alert("Success", "Commission rate updated successfully");
      } else {
        Alert.alert(
          "Error",
          response.error || "Failed to update commission rate"
        );
      }
    } catch (error) {
      console.error("Update commission error:", error);
      Alert.alert("Error", "Failed to update commission rate");
    }
  };

  const handleToggleStatus = async () => {
    if (!agent || !canManageStaff) return;

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.updateSalesAgent(agent.id, {
        active: !agent.active,
      });

      if (response.success) {
        setAgent({ ...agent, active: !agent.active });
        Alert.alert(
          "Success",
          `Agent ${agent.active ? "deactivated" : "activated"} successfully`
        );
      } else {
        Alert.alert("Error", response.error || "Failed to update agent status");
      }
    } catch (error) {
      console.error("Update status error:", error);
      Alert.alert("Error", "Failed to update agent status");
    }
  };

  const formatCurrency = (amount: number) => {
    return `KSh ${amount.toLocaleString("en-KE", {
      minimumFractionDigits: 2,
    })}`;
  };

  const formatJoinDate = (joinDate?: string) => {
    if (!joinDate) return "Unknown";
    return new Date(joinDate).toLocaleDateString();
  };

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary,
    primaryColor
  );

  if (loading) {
    return (
      <ScreenWrapper title="Loading..." showBackButton>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: textSecondary }]}>
            Loading agent details...
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  if (!agent) {
    return (
      <ScreenWrapper title="Agent Not Found" showBackButton>
        <View style={styles.errorContainer}>
          <Ionicons name="person-remove" size={64} color={textSecondary} />
          <Text style={[styles.errorText, { color: textSecondary }]}>
            Sales agent not found
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper title={agent.name} showBackButton>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Agent Profile Card */}
        <ModernCard
          style={[styles.profileCard, { backgroundColor: surfaceColor }]}
        >
          <View style={styles.profileHeader}>
            <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
              <Text style={styles.avatarText}>
                {agent.name
                  .split(" ")
                  .map((n) => n.charAt(0))
                  .join("")
                  .slice(0, 2)}
              </Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={[styles.profileName, { color: textColor }]}>
                {agent.name}
              </Text>
              <Text style={[styles.profileEmail, { color: textSecondary }]}>
                {agent.email}
              </Text>
              {agent.phone && (
                <Text style={[styles.profilePhone, { color: textSecondary }]}>
                  {agent.phone}
                </Text>
              )}
            </View>
            <View style={styles.profileStatus}>
              <View
                style={[
                  styles.statusIndicator,
                  { backgroundColor: agent.active ? successColor : errorColor },
                ]}
              />
              <Text
                style={[
                  styles.statusText,
                  { color: agent.active ? successColor : errorColor },
                ]}
              >
                {agent.active ? "Active" : "Inactive"}
              </Text>
            </View>
          </View>

          <View style={styles.profileDetails}>
            <View style={styles.detailRow}>
              <Ionicons name="location" size={20} color={textSecondary} />
              <Text style={[styles.detailLabel, { color: textSecondary }]}>
                Territory:
              </Text>
              <Text style={[styles.detailValue, { color: textColor }]}>
                {agent.territory} {agent.region && `• ${agent.region}`}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Ionicons name="calendar" size={20} color={textSecondary} />
              <Text style={[styles.detailLabel, { color: textSecondary }]}>
                Joined:
              </Text>
              <Text style={[styles.detailValue, { color: textColor }]}>
                {formatJoinDate(agent.joinDate)}
              </Text>
            </View>
          </View>

          {/* Action Buttons */}
          <SalesAgentEditButton>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  {
                    backgroundColor: agent.active
                      ? errorColor + "20"
                      : successColor + "20",
                    borderColor: agent.active ? errorColor : successColor,
                  },
                ]}
                onPress={handleToggleStatus}
              >
                <Ionicons
                  name={agent.active ? "pause" : "play"}
                  size={16}
                  color={agent.active ? errorColor : successColor}
                />
                <Text
                  style={[
                    styles.actionButtonText,
                    {
                      color: agent.active ? errorColor : successColor,
                    },
                  ]}
                >
                  {agent.active ? "Deactivate" : "Activate"}
                </Text>
              </TouchableOpacity>
            </View>
          </SalesAgentEditButton>
        </ModernCard>

        {/* Commission Rate Card */}
        <ModernCard
          style={[styles.commissionCard, { backgroundColor: surfaceColor }]}
        >
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: textColor }]}>
              Commission Rate
            </Text>
            <SalesAgentEditButton>
              <TouchableOpacity
                onPress={() => setEditingCommission(!editingCommission)}
                style={styles.editButton}
              >
                <Ionicons
                  name={editingCommission ? "close" : "pencil"}
                  size={20}
                  color={primaryColor}
                />
              </TouchableOpacity>
            </SalesAgentEditButton>
          </View>

          {editingCommission ? (
            <View style={styles.editCommissionContainer}>
              <TextInput
                style={[
                  styles.commissionInput,
                  {
                    borderColor: primaryColor,
                    color: textColor,
                  },
                ]}
                value={newCommissionRate}
                onChangeText={setNewCommissionRate}
                placeholder="Enter commission rate"
                placeholderTextColor={textSecondary}
                keyboardType="numeric"
                maxLength={5}
              />
              <Text style={[styles.percentSymbol, { color: textSecondary }]}>
                %
              </Text>
              <ModernButton
                title="Update"
                onPress={handleUpdateCommission}
                style={styles.updateButton}
              />
            </View>
          ) : (
            <View style={styles.commissionDisplay}>
              <Text style={[styles.commissionValue, { color: primaryColor }]}>
                {agent.commissionRate}%
              </Text>
              <Text style={[styles.commissionLabel, { color: textSecondary }]}>
                Commission on sales
              </Text>
            </View>
          )}
        </ModernCard>

        {/* Performance Card */}
        <SalesAgentPerformanceView>
          <ModernCard
            style={[styles.performanceCard, { backgroundColor: surfaceColor }]}
          >
            <Text style={[styles.cardTitle, { color: textColor }]}>
              Performance Overview
            </Text>

            <View style={styles.performanceGrid}>
              <View style={styles.performanceItem}>
                <Text
                  style={[styles.performanceValue, { color: primaryColor }]}
                >
                  {formatCurrency(agent.totalSales)}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Total Sales
                </Text>
              </View>

              <View style={styles.performanceItem}>
                <Text
                  style={[styles.performanceValue, { color: successColor }]}
                >
                  {agent.totalCommission
                    ? formatCurrency(agent.totalCommission)
                    : "N/A"}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Total Commission
                </Text>
              </View>

              <View style={styles.performanceItem}>
                <Text style={[styles.performanceValue, { color: textColor }]}>
                  {agent.customerCount}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Customers
                </Text>
              </View>

              <View style={styles.performanceItem}>
                <Text style={[styles.performanceValue, { color: textColor }]}>
                  {agent.customerCount > 0
                    ? formatCurrency(agent.totalSales / agent.customerCount)
                    : "N/A"}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Avg per Customer
                </Text>
              </View>
            </View>

            {performance && (
              <View style={styles.additionalMetrics}>
                <Text style={[styles.metricsTitle, { color: textColor }]}>
                  Additional Metrics
                </Text>
                {performance.topCustomers &&
                  performance.topCustomers.length > 0 && (
                    <View style={styles.topCustomers}>
                      <Text
                        style={[
                          styles.topCustomersTitle,
                          { color: textSecondary },
                        ]}
                      >
                        Top Customers
                      </Text>
                      {performance.topCustomers
                        .slice(0, 3)
                        .map((customer, index) => (
                          <View key={customer.id} style={styles.customerRow}>
                            <Text
                              style={[
                                styles.customerName,
                                { color: textColor },
                              ]}
                            >
                              {customer.name}
                            </Text>
                            <Text
                              style={[
                                styles.customerSpent,
                                { color: primaryColor },
                              ]}
                            >
                              {formatCurrency(customer.totalSpent)}
                            </Text>
                          </View>
                        ))}
                    </View>
                  )}
              </View>
            )}
          </ModernCard>
        </SalesAgentPerformanceView>
      </ScrollView>
    </ScreenWrapper>
  );
}

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string,
  primaryColor: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    loadingText: {
      fontSize: 16,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 40,
    },
    errorText: {
      fontSize: 16,
      textAlign: "center",
      marginTop: 16,
    },
    profileCard: {
      margin: 20,
      padding: 20,
    },
    profileHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 20,
    },
    avatar: {
      width: 64,
      height: 64,
      borderRadius: 32,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 16,
    },
    avatarText: {
      color: "white",
      fontSize: 20,
      fontWeight: "bold",
    },
    profileInfo: {
      flex: 1,
    },
    profileName: {
      fontSize: 20,
      fontWeight: "bold",
      marginBottom: 4,
    },
    profileEmail: {
      fontSize: 16,
      marginBottom: 4,
    },
    profilePhone: {
      fontSize: 14,
    },
    profileStatus: {
      alignItems: "flex-end",
    },
    statusIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginBottom: 4,
    },
    statusText: {
      fontSize: 14,
      fontWeight: "500",
    },
    profileDetails: {
      borderTopWidth: 1,
      borderTopColor: textSecondary + "20",
      paddingTop: 16,
      marginBottom: 16,
    },
    detailRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
    },
    detailLabel: {
      fontSize: 14,
      marginLeft: 12,
      marginRight: 8,
      minWidth: 80,
    },
    detailValue: {
      fontSize: 14,
      fontWeight: "500",
      flex: 1,
    },
    actionButtons: {
      flexDirection: "row",
      gap: 12,
    },
    actionButton: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
      gap: 8,
    },
    actionButtonText: {
      fontSize: 14,
      fontWeight: "500",
    },
    commissionCard: {
      marginHorizontal: 20,
      marginBottom: 20,
      padding: 20,
    },
    cardHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 16,
    },
    cardTitle: {
      fontSize: 18,
      fontWeight: "bold",
    },
    editButton: {
      padding: 8,
    },
    editCommissionContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    commissionInput: {
      flex: 1,
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      fontSize: 16,
      marginRight: 8,
    },
    percentSymbol: {
      fontSize: 16,
      marginRight: 12,
    },
    updateButton: {
      paddingHorizontal: 16,
    },
    commissionDisplay: {
      alignItems: "center",
    },
    commissionValue: {
      fontSize: 32,
      fontWeight: "bold",
      marginBottom: 4,
    },
    commissionLabel: {
      fontSize: 14,
    },
    performanceCard: {
      marginHorizontal: 20,
      marginBottom: 20,
      padding: 20,
    },
    performanceGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginTop: 16,
    },
    performanceItem: {
      width: "50%",
      alignItems: "center",
      marginBottom: 20,
    },
    performanceValue: {
      fontSize: 16,
      fontWeight: "bold",
      marginBottom: 4,
    },
    performanceLabel: {
      fontSize: 12,
      textAlign: "center",
    },
    additionalMetrics: {
      marginTop: 20,
      paddingTop: 20,
      borderTopWidth: 1,
      borderTopColor: textSecondary + "20",
    },
    metricsTitle: {
      fontSize: 16,
      fontWeight: "bold",
      marginBottom: 12,
    },
    topCustomers: {
      marginTop: 12,
    },
    topCustomersTitle: {
      fontSize: 14,
      fontWeight: "500",
      marginBottom: 8,
    },
    customerRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 4,
    },
    customerName: {
      fontSize: 14,
      flex: 1,
    },
    customerSpent: {
      fontSize: 14,
      fontWeight: "500",
    },
  });
