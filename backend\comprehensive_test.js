/**
 * Comprehensive Test Suite for POS User Switching and Ticketing
 */

require("dotenv").config();
const axios = require("axios");

const BASE_URL = "http://localhost:3020";
let adminToken = "";
let sessionId = "";

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, details = "") {
  testResults.tests.push({ name, passed, details });
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${details}`);
  }
}

async function runTest(name, testFn) {
  try {
    await testFn();
    logTest(name, true);
  } catch (error) {
    logTest(name, false, error.message);
  }
}

async function login() {
  console.log("\n🔐 === AUTHENTICATION TESTS ===");
  
  const response = await axios.post(`${BASE_URL}/api/pos/login`, {
    username: "admin1",
    password: "admin123"
  });

  if (response.data.success) {
    adminToken = response.data.data.token;
    sessionId = response.data.data.sessionId;
    logTest("Admin Login", true);
    console.log(`   Token: ${adminToken.substring(0, 50)}...`);
    console.log(`   Session ID: ${sessionId}`);
  } else {
    throw new Error("Login failed");
  }
}

async function testUserSwitching() {
  console.log("\n👥 === USER SWITCHING TESTS ===");

  const headers = { Authorization: `Bearer ${adminToken}` };

  // Test 1: Get available staff
  await runTest("Get Available Staff", async () => {
    const response = await axios.get(`${BASE_URL}/api/pos/user-switching/available-staff`, { headers });
    if (!response.data.success || !Array.isArray(response.data.data.staff)) {
      throw new Error("Invalid staff list response");
    }
    console.log(`   Found ${response.data.data.staff.length} staff members`);
  });

  // Test 2: Initialize PIN for a staff member
  await runTest("Initialize Staff PIN", async () => {
    const response = await axios.post(`${BASE_URL}/api/pos/user-switching/initialize-pin`, {
      staffId: "pos-001", // cashier1
      pin: "1234",
      confirmPin: "1234"
    }, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "PIN initialization failed");
    }
  });

  // Test 3: Validate PIN
  await runTest("Validate PIN", async () => {
    const response = await axios.post(`${BASE_URL}/api/pos/user-switching/validate-pin`, {
      staffId: "pos-001",
      pin: "1234"
    }, { headers });
    
    if (!response.data.success || !response.data.data.valid) {
      throw new Error("PIN validation failed");
    }
  });

  // Test 4: Switch User
  await runTest("Switch User", async () => {
    const response = await axios.post(`${BASE_URL}/api/pos/user-switching/switch-user`, {
      targetStaffId: "pos-001",
      pin: "1234",
      reason: "testing"
    }, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "User switch failed");
    }
    console.log(`   Switched to: ${response.data.data.currentUser.name}`);
  });

  // Test 5: Get Session Context
  await runTest("Get Session Context", async () => {
    const response = await axios.get(`${BASE_URL}/api/pos/user-switching/session-context`, { headers });
    
    if (!response.data.success) {
      throw new Error("Failed to get session context");
    }
    
    const context = response.data.data.sessionContext;
    console.log(`   Current User: ${context.currentUser.name}`);
    console.log(`   Has Active Switch: ${response.data.data.hasActiveSwitch}`);
  });

  // Test 6: Switch Back
  await runTest("Switch Back to Previous User", async () => {
    const response = await axios.post(`${BASE_URL}/api/pos/user-switching/switch-back`, {}, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "Switch back failed");
    }
    console.log(`   Switched back to: ${response.data.data.currentUser.name}`);
  });

  // Test 7: Get Switch History
  await runTest("Get Switch History", async () => {
    const response = await axios.get(`${BASE_URL}/api/pos/user-switching/switch-history`, { headers });
    
    if (!response.data.success) {
      throw new Error("Failed to get switch history");
    }
    console.log(`   Found ${response.data.data.switches.length} switch records`);
  });
}

async function testTicketing() {
  console.log("\n🎫 === TICKETING TESTS ===");

  const headers = { Authorization: `Bearer ${adminToken}` };
  let ticketId = "";

  // Test 1: Create Ticket
  await runTest("Create Ticket", async () => {
    const response = await axios.post(`${BASE_URL}/api/tickets`, {
      name: "Test Ticket",
      note: "Testing ticket creation"
    }, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "Ticket creation failed");
    }
    
    ticketId = response.data.data.ticket.id;
    console.log(`   Created ticket: ${ticketId}`);
  });

  // Test 2: Get Tickets
  await runTest("Get Tickets", async () => {
    const response = await axios.get(`${BASE_URL}/api/tickets`, { headers });
    
    if (!response.data.success || !Array.isArray(response.data.data.tickets)) {
      throw new Error("Failed to get tickets");
    }
    console.log(`   Found ${response.data.data.tickets.length} tickets`);
  });

  // Test 3: Get Ticket by ID
  await runTest("Get Ticket by ID", async () => {
    const response = await axios.get(`${BASE_URL}/api/tickets/${ticketId}`, { headers });
    
    if (!response.data.success) {
      throw new Error("Failed to get ticket by ID");
    }
    console.log(`   Retrieved ticket: ${response.data.data.ticket.name}`);
  });

  // Test 4: Update Ticket
  await runTest("Update Ticket", async () => {
    const response = await axios.put(`${BASE_URL}/api/tickets/${ticketId}`, {
      name: "Updated Test Ticket",
      note: "Updated note"
    }, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "Ticket update failed");
    }
  });

  // Test 5: Add Item to Ticket
  await runTest("Add Item to Ticket", async () => {
    const response = await axios.post(`${BASE_URL}/api/tickets/${ticketId}/items`, {
      variantId: "test-variant-1",
      productId: "test-product-1",
      title: "Test Product",
      price: 29.99,
      quantity: 2
    }, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "Failed to add item to ticket");
    }
  });

  // Test 6: Auto-save Ticket
  await runTest("Auto-save Ticket", async () => {
    const response = await axios.post(`${BASE_URL}/api/tickets/${ticketId}/auto-save`, {
      name: "Auto-saved Ticket",
      items: [
        {
          variantId: "test-variant-1",
          productId: "test-product-1",
          title: "Test Product",
          price: 29.99,
          quantity: 2
        }
      ],
      subtotal: 59.98,
      total: 59.98
    }, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "Auto-save failed");
    }
  });

  // Test 7: Delete Ticket
  await runTest("Delete Ticket", async () => {
    const response = await axios.delete(`${BASE_URL}/api/tickets/${ticketId}`, { headers });
    
    if (!response.data.success) {
      throw new Error(response.data.error || "Ticket deletion failed");
    }
  });
}

async function runAllTests() {
  console.log("🚀 Starting Comprehensive POS Testing Suite\n");
  
  try {
    await login();
    await testUserSwitching();
    await testTicketing();
    
    console.log("\n📊 === TEST RESULTS ===");
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
      console.log("\n❌ Failed Tests:");
      testResults.tests.filter(t => !t.passed).forEach(test => {
        console.log(`   - ${test.name}: ${test.details}`);
      });
    }
    
  } catch (error) {
    console.error("❌ Test suite failed:", error.message);
  }
}

runAllTests();
