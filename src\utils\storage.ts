import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';

/**
 * Cross-platform secure storage utility
 * Uses SecureStore on mobile and localStorage on web
 */
export class CrossPlatformStorage {
  static async getItemAsync(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage on web
        return localStorage.getItem(key);
      } else {
        // Use SecureStore on mobile
        return await SecureStore.getItemAsync(key);
      }
    } catch (error) {
      console.warn(`Failed to get item ${key}:`, error);
      return null;
    }
  }

  static async setItemAsync(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage on web
        localStorage.setItem(key, value);
      } else {
        // Use SecureStore on mobile
        await SecureStore.setItemAsync(key, value);
      }
    } catch (error) {
      console.warn(`Failed to set item ${key}:`, error);
      throw error;
    }
  }

  static async deleteItemAsync(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage on web
        localStorage.removeItem(key);
      } else {
        // Use SecureStore on mobile
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.warn(`Failed to delete item ${key}:`, error);
      throw error;
    }
  }

  static async clearAll(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Clear all localStorage items
        localStorage.clear();
      } else {
        // On mobile, we need to manually delete known keys
        // This is a limitation of SecureStore - no clear all method
        const knownKeys = ['session_token', 'user_data', 'location_data'];
        for (const key of knownKeys) {
          try {
            await SecureStore.deleteItemAsync(key);
          } catch (error) {
            // Ignore errors for non-existent keys
          }
        }
      }
    } catch (error) {
      console.warn('Failed to clear storage:', error);
      throw error;
    }
  }
}
