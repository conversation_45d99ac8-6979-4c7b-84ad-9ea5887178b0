/**
 * User Switching Modal Component
 *
 * A centered popup modal for PIN-based user switching with enhanced UX,
 * featuring loading states, success/error feedback, and smooth transitions.
 */

import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  useUserSwitching,
  type AvailableStaff,
} from "@/src/contexts/UserSwitchingContext";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { ModernButton } from "./ModernButton";
import { PinInput } from "./PinInput";

interface UserSwitchingModalProps {
  visible: boolean;
  onClose: () => void;
  onSwitchComplete?: (success: boolean) => void;
  // Simple PIN verification mode - just verify current user's PIN
  pinVerificationMode?: boolean;
  title?: string;
  description?: string;
}

type ModalStep =
  | "loading"
  | "select"
  | "pin"
  | "verify_current" // New step for PIN verification mode
  | "switching"
  | "success"
  | "error";

export const UserSwitchingModal: React.FC<UserSwitchingModalProps> = ({
  visible,
  onClose,
  onSwitchComplete,
  pinVerificationMode = false,
  title,
  description,
}) => {
  const theme = useTheme();
  const { user } = useSession();
  const {
    sessionContext,
    availableStaff,
    isLoading,
    error,
    isSwitching,
    switchUser,
    switchBack,
    validatePin,
    clearError,
    loadAvailableStaff,
  } = useUserSwitching();

  const [currentStep, setCurrentStep] = useState<ModalStep>("loading");
  const [selectedStaff, setSelectedStaff] = useState<AvailableStaff | null>(
    null
  );
  const [pin, setPin] = useState("");
  const [pinError, setPinError] = useState("");

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      setSelectedStaff(null);
      setPin("");
      setPinError("");
      clearError();

      if (pinVerificationMode) {
        // For PIN verification mode, go directly to PIN entry for current user
        setCurrentStep("verify_current");
      } else {
        // For user switching mode, load available staff
        setCurrentStep("loading");
        loadAvailableStaff();
      }
    }
  }, [visible, pinVerificationMode, clearError, loadAvailableStaff]);

  // Update step based on loading state
  useEffect(() => {
    if (visible) {
      if (isLoading) {
        setCurrentStep("loading");
      } else if (error) {
        setCurrentStep("error");
      } else if (availableStaff.length > 0) {
        setCurrentStep("select");
      }
    }
  }, [visible, isLoading, error, availableStaff]);

  const handleStaffSelect = (staff: AvailableStaff) => {
    if (!staff.has_pin) {
      Alert.alert(
        "PIN Required",
        `${staff.name} does not have a PIN set up. Please contact an administrator.`,
        [{ text: "OK" }]
      );
      return;
    }
    setSelectedStaff(staff);
    setCurrentStep("pin");
  };

  const handlePinComplete = async (enteredPin: string) => {
    if (currentStep === "verify_current") {
      // PIN verification mode - validate current user's PIN
      if (!user?.id) return;

      try {
        const isValid = await validatePin(user.id, enteredPin);
        if (isValid) {
          setCurrentStep("success");
          setTimeout(() => {
            onSwitchComplete?.(true);
            onClose();
          }, 1500);
        } else {
          setPinError("Invalid PIN. Please try again.");
          setPin("");
        }
      } catch (err) {
        setPinError("Failed to verify PIN. Please try again.");
        setPin("");
      }
      return;
    }

    // User switching mode
    if (!selectedStaff) return;

    setCurrentStep("switching");
    try {
      const success = await switchUser(
        selectedStaff.id,
        enteredPin,
        "manual_switch"
      );

      if (success) {
        setCurrentStep("success");
        setTimeout(() => {
          onSwitchComplete?.(true);
          onClose();
        }, 2000);
      } else {
        setPinError(error || "Invalid PIN. Please try again.");
        setPin("");
        setCurrentStep("pin");
      }
    } catch (err) {
      setPinError("Failed to switch user. Please try again.");
      setPin("");
      setCurrentStep("pin");
    }
  };

  const handleSwitchBack = async () => {
    setCurrentStep("switching");
    try {
      const success = await switchBack();

      if (success) {
        setCurrentStep("success");
        setTimeout(() => {
          onSwitchComplete?.(true);
          onClose();
        }, 2000);
      } else {
        setCurrentStep("error");
      }
    } catch (err) {
      setCurrentStep("error");
    }
  };

  const handleBack = () => {
    if (currentStep === "pin") {
      setCurrentStep("select");
      setSelectedStaff(null);
      setPin("");
      setPinError("");
    } else {
      onClose();
    }
  };

  const handleRetry = () => {
    clearError();
    loadAvailableStaff();
    setCurrentStep("loading");
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "loading":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
              Loading Staff...
            </Text>
            <Text
              style={[
                styles.stepMessage,
                { color: theme.colors.textSecondary },
              ]}
            >
              Please wait while we fetch available staff members.
            </Text>
          </View>
        );

      case "verify_current":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: theme.colors.primary + "20" },
              ]}
            >
              <Ionicons
                name="lock-closed"
                size={32}
                color={theme.colors.primary}
              />
            </View>
            <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
              {title || "Verify Your PIN"}
            </Text>
            <Text
              style={[
                styles.stepMessage,
                { color: theme.colors.textSecondary },
              ]}
            >
              {description || "Please enter your PIN to continue"}
            </Text>

            {/* Current User Info */}
            {user && (
              <View
                style={[
                  styles.currentUserCard,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.currentUserLabel,
                    { color: theme.colors.textSecondary },
                  ]}
                >
                  Verifying PIN for:
                </Text>
                <Text
                  style={[styles.currentUserName, { color: theme.colors.text }]}
                >
                  {user.name}
                </Text>
                <Text
                  style={[
                    styles.currentUserRole,
                    { color: theme.colors.textSecondary },
                  ]}
                >
                  {user.role}
                </Text>
              </View>
            )}

            <View style={styles.pinContainer}>
              <PinInput
                length={4}
                value={pin}
                onChangeText={setPin}
                onComplete={handlePinComplete}
                error={!!pinError}
                errorMessage={pinError}
                testID="current-user-pin-input"
              />
            </View>
          </View>
        );

      case "select":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: theme.colors.primary + "20" },
              ]}
            >
              <Ionicons name="people" size={32} color={theme.colors.primary} />
            </View>
            <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
              Select Staff Member
            </Text>
            <Text
              style={[
                styles.stepMessage,
                { color: theme.colors.textSecondary },
              ]}
            >
              Choose a staff member to switch to:
            </Text>

            {/* Current User Info */}
            {sessionContext && (
              <View
                style={[
                  styles.currentUserCard,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.currentUserLabel,
                    { color: theme.colors.textSecondary },
                  ]}
                >
                  Currently logged in as:
                </Text>
                <Text
                  style={[styles.currentUserName, { color: theme.colors.text }]}
                >
                  {sessionContext.currentUser.name}
                </Text>
                <Text
                  style={[
                    styles.currentUserRole,
                    { color: theme.colors.textSecondary },
                  ]}
                >
                  {sessionContext.currentUser.role}
                </Text>

                {sessionContext.hasActiveSwitch && (
                  <ModernButton
                    title="Switch Back"
                    onPress={handleSwitchBack}
                    variant="outline"
                    style={styles.switchBackButton}
                  />
                )}
              </View>
            )}

            {/* Staff List */}
            <ScrollView
              style={styles.staffList}
              showsVerticalScrollIndicator={false}
            >
              {availableStaff.map((staff) => (
                <TouchableOpacity
                  key={staff.id}
                  style={[
                    styles.staffItem,
                    {
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                      opacity: staff.has_pin ? 1 : 0.5,
                    },
                  ]}
                  onPress={() => handleStaffSelect(staff)}
                  disabled={!staff.has_pin}
                >
                  <View style={styles.staffInfo}>
                    <Text
                      style={[styles.staffName, { color: theme.colors.text }]}
                    >
                      {staff.name}
                    </Text>
                    <Text
                      style={[
                        styles.staffRole,
                        { color: theme.colors.textSecondary },
                      ]}
                    >
                      {staff.role} • {staff.username}
                    </Text>
                  </View>
                  <View style={styles.staffStatus}>
                    {staff.has_pin ? (
                      <View
                        style={[
                          styles.pinBadge,
                          { backgroundColor: "#10B981" },
                        ]}
                      >
                        <Ionicons name="checkmark" size={12} color="white" />
                        <Text style={styles.pinBadgeText}>PIN Set</Text>
                      </View>
                    ) : (
                      <View
                        style={[
                          styles.pinBadge,
                          { backgroundColor: "#EF4444" },
                        ]}
                      >
                        <Ionicons name="close" size={12} color="white" />
                        <Text style={styles.pinBadgeText}>No PIN</Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        );

      case "pin":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: theme.colors.primary + "20" },
              ]}
            >
              <Ionicons
                name="lock-closed"
                size={32}
                color={theme.colors.primary}
              />
            </View>
            <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
              Enter PIN
            </Text>
            <Text
              style={[
                styles.stepMessage,
                { color: theme.colors.textSecondary },
              ]}
            >
              Enter PIN for {selectedStaff?.name}
            </Text>

            <View style={styles.pinContainer}>
              <PinInput
                length={4}
                value={pin}
                onChangeText={setPin}
                onComplete={handlePinComplete}
                error={!!pinError}
                errorMessage={pinError}
                testID="user-switching-pin-input"
              />
            </View>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Back"
                onPress={handleBack}
                variant="outline"
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "switching":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
              Switching User...
            </Text>
            <Text
              style={[
                styles.stepMessage,
                { color: theme.colors.textSecondary },
              ]}
            >
              Please wait while we switch to{" "}
              {selectedStaff?.name || "the selected user"}.
            </Text>
          </View>
        );

      case "success":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: "#10B981" + "20" },
              ]}
            >
              <Ionicons name="checkmark-circle" size={32} color="#10B981" />
            </View>
            <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
              User Switched Successfully!
            </Text>
            <Text
              style={[
                styles.stepMessage,
                { color: theme.colors.textSecondary },
              ]}
            >
              You are now logged in as{" "}
              {selectedStaff?.name || "the selected user"}.
            </Text>
          </View>
        );

      case "error":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: "#EF4444" + "20" },
              ]}
            >
              <Ionicons name="alert-circle" size={32} color="#EF4444" />
            </View>
            <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
              Error
            </Text>
            <Text
              style={[
                styles.stepMessage,
                { color: theme.colors.textSecondary },
              ]}
            >
              {error || "Failed to load staff members. Please try again."}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Retry"
                onPress={handleRetry}
                variant="primary"
                style={styles.actionButton}
              />
              <ModernButton
                title="Close"
                onPress={onClose}
                variant="outline"
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={currentStep === "switching" ? undefined : onClose}
      statusBarTranslucent
      presentationStyle="overFullScreen"
    >
      <View style={styles.overlay}>
        <View
          style={[
            styles.modal,
            {
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
            },
          ]}
        >
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.header}>
              <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
                {pinVerificationMode
                  ? title || "PIN Verification"
                  : "User Switching"}
              </Text>
              <Text
                style={[
                  styles.headerSubtitle,
                  { color: theme.colors.textSecondary },
                ]}
              >
                {pinVerificationMode
                  ? "Secure access verification"
                  : "Switch to another staff member"}
              </Text>

              {currentStep !== "switching" && currentStep !== "success" && (
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={onClose}
                  activeOpacity={0.7}
                >
                  <Ionicons name="close" size={24} color={theme.colors.text} />
                </TouchableOpacity>
              )}
            </View>

            {/* Step Content */}
            {renderStepContent()}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modal: {
    width: "100%",
    maxWidth: 500,
    minHeight: 400,
    maxHeight: "80%",
    borderRadius: 16,
    borderWidth: 1,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 24,
  },
  header: {
    alignItems: "center",
    marginBottom: 24,
    position: "relative",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    textAlign: "center",
  },
  closeButton: {
    position: "absolute",
    top: 0,
    right: 0,
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 16,
  },
  stepContainer: {
    alignItems: "center",
    justifyContent: "flex-start",
    minHeight: 300,
    width: "100%",
    paddingVertical: 20,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 8,
  },
  stepMessage: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 24,
  },
  currentUserCard: {
    width: "100%",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: "center",
  },
  currentUserLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  currentUserName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  currentUserRole: {
    fontSize: 14,
    marginBottom: 12,
  },
  switchBackButton: {
    minWidth: 120,
  },
  staffList: {
    width: "100%",
    maxHeight: 200,
  },
  staffItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  staffInfo: {
    flex: 1,
  },
  staffName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  staffRole: {
    fontSize: 14,
  },
  staffStatus: {
    alignItems: "flex-end",
  },
  pinBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  pinBadgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  pinContainer: {
    width: "100%",
    marginBottom: 24,
  },
  actionsContainer: {
    flexDirection: "row",
    gap: 12,
    justifyContent: "center",
  },
  actionButton: {
    minWidth: 100,
  },
});
