#!/usr/bin/env node

/**
 * Fix Singleton Syntax Errors Script
 * 
 * This script fixes malformed singleton conversions that have syntax errors.
 */

const fs = require('fs');
const path = require('path');

// Services that need fixing
const servicesToFix = [
  'backend/src/services/sales-agent-service.js',
  'backend/src/services/sales-agent-service-mysql.js',
  'backend/src/services/staff-service-mysql.js',
  'backend/src/services/multi-user-session-service.js',
  'backend/src/services/terminal-management-service.js',
  'backend/src/services/terminal-management-service-mysql.js',
  'backend/src/services/commission-service-mysql.js',
  'backend/src/services/commission-discount-service.js',
  'backend/src/services/fulfillment-service.js',
];

function fixSingletonSyntax(filePath) {
  console.log(`\n🔧 Fixing ${filePath}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Extract class name from file
  const classMatch = content.match(/class\s+(\w+)\s*{/);
  if (!classMatch) {
    console.log(`⚠️ No class found in ${filePath}`);
    return;
  }
  
  const className = classMatch[1];
  console.log(`  📝 Found class: ${className}`);
  
  // Fix malformed constructor and static method placement
  const malformedPattern = new RegExp(
    `(class\\s+${className}\\s*{[\\s\\S]*?constructor\\s*\\([^)]*\\)\\s*{[\\s\\S]*?)\\s*static\\s+getInstance\\(\\)[\\s\\S]*?}\\s*}([\\s\\S]*?)this\\.databaseManager\\s*=\\s*databaseManager;[\\s\\S]*?}`,
    'g'
  );
  
  if (malformedPattern.test(content)) {
    // Rebuild the class structure properly
    content = content.replace(malformedPattern, (match) => {
      return `class ${className} {
  constructor() {
    if (${className}.instance) {
      return ${className}.instance;
    }

    this.databaseManager = databaseManager;
    
    ${className}.instance = this;
  }
  
  static getInstance() {
    if (!${className}.instance) {
      ${className}.instance = new ${className}();
    }
    return ${className}.instance;
  }`;
    });
    
    modified = true;
    console.log('  ✅ Fixed malformed singleton structure');
  }
  
  // Fix any remaining syntax issues with static methods outside class
  const staticOutsideClass = new RegExp(`}\\s*static\\s+getInstance\\(\\)`, 'g');
  if (staticOutsideClass.test(content)) {
    content = content.replace(staticOutsideClass, `
  
  static getInstance() {
    if (!${className}.instance) {
      ${className}.instance = new ${className}();
    }
    return ${className}.instance;
  }
}`);
    modified = true;
    console.log('  ✅ Fixed static method placement');
  }
  
  // Remove duplicate constructor assignments
  const duplicateAssignment = /this\.databaseManager\s*=\s*databaseManager;\s*}\s*this\.databaseManager\s*=\s*databaseManager;/g;
  if (duplicateAssignment.test(content)) {
    content = content.replace(duplicateAssignment, 'this.databaseManager = databaseManager;\n  }');
    modified = true;
    console.log('  ✅ Removed duplicate assignments');
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Successfully fixed ${filePath}`);
  } else {
    console.log(`  ℹ️ No fixes needed for ${filePath}`);
  }
}

function main() {
  console.log('🚀 Starting singleton syntax fixes...\n');
  
  servicesToFix.forEach(fixSingletonSyntax);
  
  console.log('\n✅ Singleton syntax fixes completed!');
}

if (require.main === module) {
  main();
}

module.exports = { fixSingletonSyntax };
