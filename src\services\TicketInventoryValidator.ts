/**
 * Ticket Inventory Validation Service
 * 
 * Provides real-time inventory validation for ticket items,
 * automatic adjustment suggestions, and conflict resolution.
 */

import { CartItem } from '@/src/types/shopify';
import { Ticket } from '@/src/store/slices/ticketSlice';
import { getAPIClient } from '@/src/services/api/dukalink-client';

export interface InventoryValidationResult {
  isValid: boolean;
  errors: InventoryError[];
  warnings: InventoryWarning[];
  suggestions: InventoryAdjustment[];
  totalIssues: number;
}

export interface InventoryError {
  itemId: string;
  variantId: string;
  title: string;
  type: 'out_of_stock' | 'insufficient_quantity' | 'invalid_item';
  requested: number;
  available: number;
  message: string;
}

export interface InventoryWarning {
  itemId: string;
  variantId: string;
  title: string;
  type: 'low_stock' | 'price_change' | 'discontinued';
  message: string;
  severity: 'low' | 'medium' | 'high';
}

export interface InventoryAdjustment {
  itemId: string;
  variantId: string;
  title: string;
  action: 'reduce_quantity' | 'remove_item' | 'update_price' | 'substitute_item';
  currentQuantity: number;
  suggestedQuantity: number;
  reason: string;
  autoApplicable: boolean;
}

export interface InventorySnapshot {
  variantId: string;
  availableQuantity: number;
  price: string;
  isActive: boolean;
  lastUpdated: string;
}

class TicketInventoryValidator {
  private inventoryCache: Map<string, InventorySnapshot> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_DURATION_MS = 60000; // 1 minute
  private readonly LOW_STOCK_THRESHOLD = 5;

  /**
   * Validate all items in a ticket against current inventory
   */
  async validateTicketInventory(ticket: Ticket): Promise<InventoryValidationResult> {
    const errors: InventoryError[] = [];
    const warnings: InventoryWarning[] = [];
    const suggestions: InventoryAdjustment[] = [];

    // Refresh inventory data for all items
    await this.refreshInventoryData(ticket.items.map(item => item.variantId));

    for (const item of ticket.items) {
      const validation = await this.validateItem(item);
      
      errors.push(...validation.errors);
      warnings.push(...validation.warnings);
      suggestions.push(...validation.suggestions);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      totalIssues: errors.length + warnings.length,
    };
  }

  /**
   * Validate a single cart item
   */
  async validateItem(item: CartItem): Promise<InventoryValidationResult> {
    const errors: InventoryError[] = [];
    const warnings: InventoryWarning[] = [];
    const suggestions: InventoryAdjustment[] = [];

    const inventory = await this.getInventorySnapshot(item.variantId);

    if (!inventory) {
      errors.push({
        itemId: item.variantId,
        variantId: item.variantId,
        title: item.title,
        type: 'invalid_item',
        requested: item.quantity,
        available: 0,
        message: 'Product variant not found or discontinued',
      });

      suggestions.push({
        itemId: item.variantId,
        variantId: item.variantId,
        title: item.title,
        action: 'remove_item',
        currentQuantity: item.quantity,
        suggestedQuantity: 0,
        reason: 'Product no longer available',
        autoApplicable: false,
      });

      return { isValid: false, errors, warnings, suggestions, totalIssues: 1 };
    }

    // Check if product is active
    if (!inventory.isActive) {
      warnings.push({
        itemId: item.variantId,
        variantId: item.variantId,
        title: item.title,
        type: 'discontinued',
        message: 'Product has been discontinued',
        severity: 'high',
      });
    }

    // Check quantity availability
    if (item.quantity > inventory.availableQuantity) {
      errors.push({
        itemId: item.variantId,
        variantId: item.variantId,
        title: item.title,
        type: inventory.availableQuantity === 0 ? 'out_of_stock' : 'insufficient_quantity',
        requested: item.quantity,
        available: inventory.availableQuantity,
        message: `Requested ${item.quantity}, but only ${inventory.availableQuantity} available`,
      });

      if (inventory.availableQuantity > 0) {
        suggestions.push({
          itemId: item.variantId,
          variantId: item.variantId,
          title: item.title,
          action: 'reduce_quantity',
          currentQuantity: item.quantity,
          suggestedQuantity: inventory.availableQuantity,
          reason: `Adjust to available stock (${inventory.availableQuantity})`,
          autoApplicable: true,
        });
      } else {
        suggestions.push({
          itemId: item.variantId,
          variantId: item.variantId,
          title: item.title,
          action: 'remove_item',
          currentQuantity: item.quantity,
          suggestedQuantity: 0,
          reason: 'Product is out of stock',
          autoApplicable: true,
        });
      }
    }

    // Check for low stock warning
    if (inventory.availableQuantity <= this.LOW_STOCK_THRESHOLD && inventory.availableQuantity > 0) {
      warnings.push({
        itemId: item.variantId,
        variantId: item.variantId,
        title: item.title,
        type: 'low_stock',
        message: `Low stock: Only ${inventory.availableQuantity} remaining`,
        severity: inventory.availableQuantity <= 2 ? 'high' : 'medium',
      });
    }

    // Check for price changes
    if (parseFloat(item.price) !== parseFloat(inventory.price)) {
      warnings.push({
        itemId: item.variantId,
        variantId: item.variantId,
        title: item.title,
        type: 'price_change',
        message: `Price changed from ${item.price} to ${inventory.price}`,
        severity: 'medium',
      });

      suggestions.push({
        itemId: item.variantId,
        variantId: item.variantId,
        title: item.title,
        action: 'update_price',
        currentQuantity: item.quantity,
        suggestedQuantity: item.quantity,
        reason: `Update to current price (${inventory.price})`,
        autoApplicable: true,
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      totalIssues: errors.length + warnings.length,
    };
  }

  /**
   * Apply automatic adjustments to ticket items
   */
  async applyAutoAdjustments(ticket: Ticket, adjustments: InventoryAdjustment[]): Promise<CartItem[]> {
    const adjustedItems: CartItem[] = [...ticket.items];

    for (const adjustment of adjustments) {
      if (!adjustment.autoApplicable) continue;

      const itemIndex = adjustedItems.findIndex(item => item.variantId === adjustment.variantId);
      
      if (itemIndex === -1) continue;

      switch (adjustment.action) {
        case 'reduce_quantity':
          adjustedItems[itemIndex].quantity = adjustment.suggestedQuantity;
          break;
          
        case 'remove_item':
          adjustedItems.splice(itemIndex, 1);
          break;
          
        case 'update_price':
          const inventory = await this.getInventorySnapshot(adjustment.variantId);
          if (inventory) {
            adjustedItems[itemIndex].price = inventory.price;
          }
          break;
      }
    }

    return adjustedItems;
  }

  /**
   * Get real-time inventory snapshot for a variant
   */
  async getInventorySnapshot(variantId: string): Promise<InventorySnapshot | null> {
    // Check cache first
    const cached = this.inventoryCache.get(variantId);
    const cacheTime = this.cacheExpiry.get(variantId);
    
    if (cached && cacheTime && Date.now() < cacheTime) {
      return cached;
    }

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getProductVariant(variantId);

      if (response.success && response.data) {
        const variant = response.data;
        const snapshot: InventorySnapshot = {
          variantId,
          availableQuantity: variant.inventoryQuantity || 0,
          price: variant.price || '0.00',
          isActive: variant.available !== false,
          lastUpdated: new Date().toISOString(),
        };

        // Cache the result
        this.inventoryCache.set(variantId, snapshot);
        this.cacheExpiry.set(variantId, Date.now() + this.CACHE_DURATION_MS);

        return snapshot;
      }
    } catch (error) {
      console.error(`Failed to fetch inventory for variant ${variantId}:`, error);
    }

    return null;
  }

  /**
   * Refresh inventory data for multiple variants
   */
  async refreshInventoryData(variantIds: string[]): Promise<void> {
    const refreshPromises = variantIds.map(variantId => 
      this.getInventorySnapshot(variantId)
    );

    await Promise.allSettled(refreshPromises);
  }

  /**
   * Clear inventory cache
   */
  clearCache(): void {
    this.inventoryCache.clear();
    this.cacheExpiry.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const now = Date.now();
    const validEntries = Array.from(this.cacheExpiry.entries())
      .filter(([_, expiry]) => now < expiry).length;

    return {
      totalEntries: this.inventoryCache.size,
      validEntries,
      expiredEntries: this.inventoryCache.size - validEntries,
    };
  }

  /**
   * Validate multiple tickets in batch
   */
  async batchValidateTickets(tickets: Ticket[]): Promise<Map<string, InventoryValidationResult>> {
    const results = new Map<string, InventoryValidationResult>();

    // Collect all unique variant IDs
    const allVariantIds = new Set<string>();
    tickets.forEach(ticket => {
      ticket.items.forEach(item => allVariantIds.add(item.variantId));
    });

    // Refresh inventory data for all variants
    await this.refreshInventoryData(Array.from(allVariantIds));

    // Validate each ticket
    for (const ticket of tickets) {
      const validation = await this.validateTicketInventory(ticket);
      results.set(ticket.id, validation);
    }

    return results;
  }

  /**
   * Get inventory health summary
   */
  async getInventoryHealthSummary(tickets: Ticket[]) {
    const validationResults = await this.batchValidateTickets(tickets);
    
    let totalErrors = 0;
    let totalWarnings = 0;
    let ticketsWithIssues = 0;
    const issuesByType = new Map<string, number>();

    validationResults.forEach(result => {
      totalErrors += result.errors.length;
      totalWarnings += result.warnings.length;
      
      if (result.totalIssues > 0) {
        ticketsWithIssues++;
      }

      result.errors.forEach(error => {
        issuesByType.set(error.type, (issuesByType.get(error.type) || 0) + 1);
      });
    });

    return {
      totalTickets: tickets.length,
      ticketsWithIssues,
      totalErrors,
      totalWarnings,
      issuesByType: Object.fromEntries(issuesByType),
      healthScore: Math.max(0, 100 - (totalErrors * 10) - (totalWarnings * 2)),
    };
  }
}

// Export singleton instance
export const ticketInventoryValidator = new TicketInventoryValidator();
export default TicketInventoryValidator;
