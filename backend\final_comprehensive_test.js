/**
 * Final Comprehensive Test Suite - All Functionality
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";
let adminToken = "";
let testResults = { passed: 0, failed: 0, tests: [] };

function logTest(name, passed, details = "") {
  testResults.tests.push({ name, passed, details });
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${details}`);
  }
}

async function runTest(name, testFn) {
  try {
    await testFn();
    logTest(name, true);
  } catch (error) {
    logTest(name, false, error.message);
  }
}

async function testAuthentication() {
  console.log("\n🔐 === AUTHENTICATION TESTS ===");

  await runTest("Admin Login", async () => {
    const response = await axios.post(`${BASE_URL}/api/pos/login`, {
      username: "admin1",
      password: "admin123",
    });

    if (!response.data.success) throw new Error("Login failed");
    adminToken = response.data.data.token;
    console.log(`   Token received: ${adminToken.substring(0, 30)}...`);
  });
}

async function testUserSwitching() {
  console.log("\n👥 === USER SWITCHING TESTS ===");

  const headers = { Authorization: `Bearer ${adminToken}` };
  let targetStaff;

  await runTest("Get Available Staff", async () => {
    const response = await axios.get(
      `${BASE_URL}/api/pos/user-switching/available-staff`,
      { headers }
    );
    if (!response.data.success) throw new Error("Failed to get staff");
    targetStaff = response.data.data.staff[0];
    console.log(`   Found ${response.data.data.staff.length} staff members`);
  });

  await runTest("Initialize PIN", async () => {
    const response = await axios.post(
      `${BASE_URL}/api/pos/user-switching/initialize-pin`,
      {
        staffId: targetStaff.id,
        pin: "1234",
        confirmPin: "1234",
      },
      { headers }
    );

    if (!response.data.success) throw new Error(response.data.error);
  });

  await runTest("Validate PIN", async () => {
    const response = await axios.post(
      `${BASE_URL}/api/pos/user-switching/validate-pin`,
      {
        staffId: targetStaff.id,
        pin: "1234",
      },
      { headers }
    );

    if (!response.data.success || !response.data.data.valid)
      throw new Error("PIN validation failed");
  });

  await runTest("Switch User", async () => {
    const response = await axios.post(
      `${BASE_URL}/api/pos/user-switching/switch-user`,
      {
        targetStaffId: targetStaff.id,
        pin: "1234",
        reason: "testing",
      },
      { headers }
    );

    if (!response.data.success) throw new Error(response.data.error);
    console.log(`   Switched to: ${response.data.data.currentUser.name}`);
  });

  await runTest("Get Session Context", async () => {
    const response = await axios.get(
      `${BASE_URL}/api/pos/user-switching/session-context`,
      { headers }
    );

    if (!response.data.success)
      throw new Error("Failed to get session context");
    if (!response.data.data.hasActiveSwitch)
      throw new Error("No active switch detected");
    console.log(`   Active switch confirmed`);
  });

  await runTest("Switch Back", async () => {
    const response = await axios.post(
      `${BASE_URL}/api/pos/user-switching/switch-back`,
      {},
      { headers }
    );

    if (!response.data.success) throw new Error(response.data.error);
    console.log(`   Switched back to: ${response.data.data.currentUser.name}`);
  });

  await runTest("Get Switch History", async () => {
    const response = await axios.get(
      `${BASE_URL}/api/pos/user-switching/switch-history`,
      { headers }
    );

    if (!response.data.success) throw new Error("Failed to get switch history");
    console.log(`   Found ${response.data.data.history.length} switch records`);
  });
}

async function testTicketing() {
  console.log("\n🎫 === TICKETING TESTS ===");

  const headers = { Authorization: `Bearer ${adminToken}` };
  let ticketId = "";

  await runTest("Create Ticket", async () => {
    const response = await axios.post(
      `${BASE_URL}/api/tickets`,
      {
        name: "Test Ticket",
        note: "Testing ticket creation",
      },
      { headers }
    );

    if (!response.data.success) throw new Error(response.data.error);
    ticketId = response.data.data.ticket.id;
    console.log(`   Created ticket: ${ticketId}`);
  });

  await runTest("Get Tickets", async () => {
    const response = await axios.get(`${BASE_URL}/api/tickets`, { headers });

    if (!response.data.success) throw new Error("Failed to get tickets");
    console.log(`   Found ${response.data.data.tickets.length} tickets`);
  });

  await runTest("Get Ticket by ID", async () => {
    const response = await axios.get(`${BASE_URL}/api/tickets/${ticketId}`, {
      headers,
    });

    if (!response.data.success) throw new Error("Failed to get ticket by ID");
    console.log(`   Retrieved ticket: ${response.data.data.ticket.name}`);
  });

  await runTest("Add Item to Ticket", async () => {
    const response = await axios.post(
      `${BASE_URL}/api/tickets/${ticketId}/items`,
      {
        variantId: "test-variant-1",
        productId: "test-product-1",
        title: "Test Product",
        price: 29.99,
        quantity: 2,
      },
      { headers }
    );

    if (!response.data.success) throw new Error(response.data.error);
  });

  await runTest("Update Ticket", async () => {
    const response = await axios.put(
      `${BASE_URL}/api/tickets/${ticketId}`,
      {
        name: "Updated Test Ticket",
        note: "Updated note",
      },
      { headers }
    );

    if (!response.data.success) throw new Error(response.data.error);
  });

  await runTest("Auto-save Ticket", async () => {
    const response = await axios.post(
      `${BASE_URL}/api/tickets/${ticketId}/auto-save`,
      {
        name: "Auto-saved Ticket",
        items: [
          {
            variantId: "test-variant-1",
            productId: "test-product-1",
            title: "Test Product",
            price: 29.99,
            quantity: 2,
          },
        ],
        subtotal: 59.98,
        total: 59.98,
      },
      { headers }
    );

    if (!response.data.success) throw new Error(response.data.error);
  });

  await runTest("Delete Ticket", async () => {
    const response = await axios.delete(`${BASE_URL}/api/tickets/${ticketId}`, {
      headers,
    });

    if (!response.data.success) throw new Error(response.data.error);
  });
}

async function runAllTests() {
  console.log("🚀 Starting Final Comprehensive Test Suite\n");

  try {
    await testAuthentication();
    await testUserSwitching();
    await testTicketing();

    console.log("\n📊 === FINAL TEST RESULTS ===");
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(
      `📈 Success Rate: ${(
        (testResults.passed / (testResults.passed + testResults.failed)) *
        100
      ).toFixed(1)}%`
    );

    if (testResults.failed === 0) {
      console.log(
        "\n🎉 ALL TESTS PASSED! Backend is fully functional and demo-ready!"
      );
    } else {
      console.log("\n❌ Some tests failed:");
      testResults.tests
        .filter((t) => !t.passed)
        .forEach((test) => {
          console.log(`   - ${test.name}: ${test.details}`);
        });
    }
  } catch (error) {
    console.error("❌ Test suite failed:", error.message);
  }
}

runAllTests();
