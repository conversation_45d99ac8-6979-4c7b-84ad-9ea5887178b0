/**
 * Enhanced Ticket Management Hook
 * 
 * Provides comprehensive ticket management with real-time synchronization,
 * validation, conflict resolution, and automatic error handling.
 */

import { useState, useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/src/store';
import {
  selectAllTickets,
  selectActiveTicket,
  selectActiveTicketId,
  createTicket,
  setActiveTicket,
  deleteTicket,
} from '@/src/store/slices/ticketSlice';
import {
  ticketSyncService,
  SyncResult,
  SyncConflict,
} from '@/src/services/TicketSyncService';
import {
  ticketInventoryValidator,
  InventoryValidationResult,
} from '@/src/services/TicketInventoryValidator';
import { useSession } from '@/src/contexts/AuthContext';

export interface TicketManagementState {
  // Sync status
  isSyncing: boolean;
  lastSyncResult: SyncResult | null;
  syncError: string | null;
  
  // Validation status
  validationResults: Map<string, InventoryValidationResult>;
  isValidating: boolean;
  
  // Conflicts
  pendingConflicts: SyncConflict[];
  
  // General state
  isLoading: boolean;
  error: string | null;
}

export interface TicketManagementActions {
  // Ticket operations
  createNewTicket: (name?: string) => Promise<string | null>;
  switchToTicket: (ticketId: string) => void;
  deleteTicketById: (ticketId: string) => Promise<boolean>;
  
  // Sync operations
  performManualSync: () => Promise<SyncResult>;
  resolveConflict: (conflictId: string, resolution: 'local' | 'server' | 'merge') => Promise<boolean>;
  
  // Validation operations
  validateTicket: (ticketId: string) => Promise<InventoryValidationResult | null>;
  validateAllTickets: () => Promise<void>;
  applyAutoAdjustments: (ticketId: string) => Promise<boolean>;
  
  // Utility operations
  clearErrors: () => void;
  refreshTicketData: () => Promise<void>;
}

export const useEnhancedTicketManagement = (): [TicketManagementState, TicketManagementActions] => {
  const dispatch = useAppDispatch();
  const { user } = useSession();
  
  // Redux state
  const tickets = useAppSelector(selectAllTickets);
  const activeTicket = useAppSelector(selectActiveTicket);
  const activeTicketId = useAppSelector(selectActiveTicketId);
  
  // Local state
  const [state, setState] = useState<TicketManagementState>({
    isSyncing: false,
    lastSyncResult: null,
    syncError: null,
    validationResults: new Map(),
    isValidating: false,
    pendingConflicts: [],
    isLoading: false,
    error: null,
  });

  // Update conflicts from sync service
  useEffect(() => {
    const interval = setInterval(() => {
      const conflicts = ticketSyncService.getPendingConflicts();
      setState(prev => ({ ...prev, pendingConflicts: conflicts }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Auto-validate active ticket when it changes
  useEffect(() => {
    if (activeTicket) {
      validateTicket(activeTicket.id);
    }
  }, [activeTicket?.id, activeTicket?.items]);

  // Ticket operations
  const createNewTicket = useCallback(async (name?: string): Promise<string | null> => {
    if (!user?.id) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return null;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const ticketName = name || `Ticket ${new Date().toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      })}`;

      const result = await dispatch(createTicket({
        name: ticketName,
        staffId: user.id,
        terminalId: user.terminalId || 'terminal-1',
        locationId: user.locationId || 'location-1',
      })).unwrap();

      return result.id;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create ticket';
      setState(prev => ({ ...prev, error: errorMessage }));
      return null;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [dispatch, user]);

  const switchToTicket = useCallback((ticketId: string) => {
    dispatch(setActiveTicket(ticketId));
  }, [dispatch]);

  const deleteTicketById = useCallback(async (ticketId: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      await dispatch(deleteTicket(ticketId)).unwrap();
      
      // Remove validation results for deleted ticket
      setState(prev => {
        const newValidationResults = new Map(prev.validationResults);
        newValidationResults.delete(ticketId);
        return { ...prev, validationResults: newValidationResults };
      });

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete ticket';
      setState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [dispatch]);

  // Sync operations
  const performManualSync = useCallback(async (): Promise<SyncResult> => {
    setState(prev => ({ ...prev, isSyncing: true, syncError: null }));

    try {
      const result = await ticketSyncService.performSync(true);
      setState(prev => ({ 
        ...prev, 
        lastSyncResult: result,
        syncError: result.success ? null : result.errors.join(', '),
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sync failed';
      setState(prev => ({ ...prev, syncError: errorMessage }));
      throw error;
    } finally {
      setState(prev => ({ ...prev, isSyncing: false }));
    }
  }, []);

  const resolveConflict = useCallback(async (
    conflictId: string, 
    resolution: 'local' | 'server' | 'merge'
  ): Promise<boolean> => {
    try {
      const success = await ticketSyncService.resolveConflict(conflictId, resolution);
      
      if (success) {
        // Update conflicts list
        const updatedConflicts = ticketSyncService.getPendingConflicts();
        setState(prev => ({ ...prev, pendingConflicts: updatedConflicts }));
      }
      
      return success;
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      return false;
    }
  }, []);

  // Validation operations
  const validateTicket = useCallback(async (ticketId: string): Promise<InventoryValidationResult | null> => {
    const ticket = tickets.find(t => t.id === ticketId);
    if (!ticket) return null;

    setState(prev => ({ ...prev, isValidating: true }));

    try {
      const result = await ticketInventoryValidator.validateTicketInventory(ticket);
      
      setState(prev => {
        const newValidationResults = new Map(prev.validationResults);
        newValidationResults.set(ticketId, result);
        return { ...prev, validationResults: newValidationResults };
      });

      return result;
    } catch (error) {
      console.error('Validation failed:', error);
      return null;
    } finally {
      setState(prev => ({ ...prev, isValidating: false }));
    }
  }, [tickets]);

  const validateAllTickets = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, isValidating: true }));

    try {
      const results = await ticketInventoryValidator.batchValidateTickets(tickets);
      setState(prev => ({ ...prev, validationResults: results }));
    } catch (error) {
      console.error('Batch validation failed:', error);
    } finally {
      setState(prev => ({ ...prev, isValidating: false }));
    }
  }, [tickets]);

  const applyAutoAdjustments = useCallback(async (ticketId: string): Promise<boolean> => {
    const ticket = tickets.find(t => t.id === ticketId);
    const validation = state.validationResults.get(ticketId);
    
    if (!ticket || !validation) return false;

    try {
      const adjustedItems = await ticketInventoryValidator.applyAutoAdjustments(
        ticket,
        validation.suggestions.filter(s => s.autoApplicable)
      );

      // Update ticket with adjusted items
      // This would need to be implemented in the ticket slice
      // For now, we'll just re-validate
      setTimeout(() => validateTicket(ticketId), 500);

      return true;
    } catch (error) {
      console.error('Failed to apply adjustments:', error);
      return false;
    }
  }, [tickets, state.validationResults, validateTicket]);

  // Utility operations
  const clearErrors = useCallback(() => {
    setState(prev => ({ ...prev, error: null, syncError: null }));
  }, []);

  const refreshTicketData = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Perform sync and validation
      await performManualSync();
      await validateAllTickets();
    } catch (error) {
      console.error('Failed to refresh ticket data:', error);
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [performManualSync, validateAllTickets]);

  // Return state and actions
  return [
    state,
    {
      createNewTicket,
      switchToTicket,
      deleteTicketById,
      performManualSync,
      resolveConflict,
      validateTicket,
      validateAllTickets,
      applyAutoAdjustments,
      clearErrors,
      refreshTicketData,
    },
  ];
};

export default useEnhancedTicketManagement;
