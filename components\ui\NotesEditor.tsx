/**
 * NotesEditor Component
 * 
 * Provides inline notes editing with expandable text input,
 * character count, and quick note templates.
 */

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  Modal,
  ScrollView,
} from 'react-native';
import { IconSymbol } from './IconSymbol';
import { ModernButton } from './ModernButton';
import { useTheme } from '@/src/contexts/ThemeContext';
import { createStyleUtils } from '@/src/utils/styleUtils';

interface NotesEditorProps {
  value?: string;
  onNotesChange: (notes: string) => void;
  placeholder?: string;
  maxLength?: number;
  disabled?: boolean;
  style?: ViewStyle;
  size?: 'small' | 'medium' | 'large';
  showTemplates?: boolean;
}

const NOTE_TEMPLATES = [
  'Customer request',
  'Special instructions',
  'Gift wrapping',
  'Fragile item',
  'Rush order',
  'Customer pickup',
  'Delivery instructions',
  'Price match',
  'Return/exchange',
  'Loyalty discount applied',
];

export function NotesEditor({
  value = '',
  onNotesChange,
  placeholder = 'Add notes...',
  maxLength = 200,
  disabled = false,
  style,
  size = 'medium',
  showTemplates = true,
}: NotesEditorProps) {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const [modalVisible, setModalVisible] = useState(false);
  const [tempNotes, setTempNotes] = useState(value);
  const inputRef = useRef<TextInput>(null);

  const styles = createStyles(theme, size);

  const hasNotes = value.trim().length > 0;
  const characterCount = tempNotes.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  const handleOpenModal = () => {
    if (disabled) return;
    setTempNotes(value);
    setModalVisible(true);
  };

  const handleSaveNotes = () => {
    const trimmedNotes = tempNotes.trim();
    if (trimmedNotes.length <= maxLength) {
      onNotesChange(trimmedNotes);
      setModalVisible(false);
    }
  };

  const handleCancelEdit = () => {
    setTempNotes(value);
    setModalVisible(false);
  };

  const handleClearNotes = () => {
    setTempNotes('');
  };

  const handleTemplateSelect = (template: string) => {
    const currentNotes = tempNotes.trim();
    const newNotes = currentNotes 
      ? `${currentNotes}\n${template}` 
      : template;
    
    if (newNotes.length <= maxLength) {
      setTempNotes(newNotes);
    }
  };

  const getTruncatedNotes = (text: string, maxChars: number = 30): string => {
    if (text.length <= maxChars) return text;
    return text.substring(0, maxChars) + '...';
  };

  return (
    <View style={[styles.container, style]}>
      {/* Notes Button */}
      <TouchableOpacity
        style={[
          styles.notesButton,
          {
            backgroundColor: hasNotes 
              ? theme.colors.primary + '20' 
              : theme.colors.backgroundSecondary,
            borderColor: hasNotes 
              ? theme.colors.primary 
              : theme.colors.border,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={handleOpenModal}
        disabled={disabled}
      >
        <IconSymbol
          name={hasNotes ? "note.text" : "note"}
          size={size === 'small' ? 14 : size === 'large' ? 20 : 16}
          color={hasNotes ? theme.colors.primary : theme.colors.textSecondary}
        />
        {hasNotes && size !== 'small' && (
          <Text
            style={[
              styles.notesPreview,
              {
                color: theme.colors.primary,
                fontSize: size === 'large' ? 12 : 10,
              },
            ]}
          >
            {getTruncatedNotes(value, size === 'large' ? 20 : 15)}
          </Text>
        )}
      </TouchableOpacity>

      {/* Notes Modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={handleCancelEdit}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
                Item Notes
              </Text>
              <TouchableOpacity onPress={handleCancelEdit}>
                <IconSymbol name="xmark" size={24} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {/* Notes Input */}
            <View style={styles.inputSection}>
              <TextInput
                ref={inputRef}
                style={[
                  styles.notesInput,
                  {
                    color: theme.colors.text,
                    borderColor: isOverLimit 
                      ? theme.colors.error 
                      : theme.colors.border,
                    backgroundColor: theme.colors.backgroundSecondary,
                  },
                ]}
                value={tempNotes}
                onChangeText={setTempNotes}
                placeholder={placeholder}
                placeholderTextColor={theme.colors.textSecondary}
                multiline
                textAlignVertical="top"
                maxLength={maxLength + 10} // Allow slight overflow for warning
                autoFocus
              />
              
              {/* Character Count */}
              <View style={styles.characterCount}>
                <Text
                  style={[
                    styles.characterCountText,
                    {
                      color: isOverLimit 
                        ? theme.colors.error 
                        : isNearLimit 
                        ? theme.colors.warning 
                        : theme.colors.textSecondary,
                    },
                  ]}
                >
                  {characterCount}/{maxLength}
                </Text>
                {tempNotes.length > 0 && (
                  <TouchableOpacity onPress={handleClearNotes}>
                    <Text style={[styles.clearButton, { color: theme.colors.primary }]}>
                      Clear
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>

            {/* Quick Templates */}
            {showTemplates && (
              <View style={styles.templatesSection}>
                <Text style={[styles.sectionLabel, { color: theme.colors.text }]}>
                  Quick Templates
                </Text>
                <ScrollView 
                  horizontal 
                  showsHorizontalScrollIndicator={false}
                  style={styles.templatesScroll}
                >
                  <View style={styles.templatesContainer}>
                    {NOTE_TEMPLATES.map((template, index) => (
                      <TouchableOpacity
                        key={index}
                        style={[
                          styles.templateChip,
                          {
                            backgroundColor: theme.colors.backgroundSecondary,
                            borderColor: theme.colors.border,
                          },
                        ]}
                        onPress={() => handleTemplateSelect(template)}
                      >
                        <Text
                          style={[
                            styles.templateText,
                            { color: theme.colors.text },
                          ]}
                        >
                          {template}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <ModernButton
                title="Cancel"
                onPress={handleCancelEdit}
                variant="outline"
                style={styles.actionButton}
              />
              <ModernButton
                title="Save Notes"
                onPress={handleSaveNotes}
                style={styles.actionButton}
                disabled={isOverLimit}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const createStyles = (theme: any, size: 'small' | 'medium' | 'large') => {
  const buttonSize = size === 'small' ? 28 : size === 'large' ? 40 : 32;

  return StyleSheet.create({
    container: {
      alignItems: 'center',
    },
    notesButton: {
      minWidth: buttonSize,
      height: buttonSize,
      borderRadius: theme.borderRadius.small,
      borderWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      paddingHorizontal: 6,
      maxWidth: size === 'small' ? buttonSize : 120,
    },
    notesPreview: {
      marginLeft: 4,
      fontWeight: '500',
      flex: 1,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      borderTopLeftRadius: theme.borderRadius.large,
      borderTopRightRadius: theme.borderRadius.large,
      padding: 20,
      paddingBottom: 40,
      maxHeight: '80%',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 5,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    inputSection: {
      marginBottom: 20,
    },
    notesInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.medium,
      paddingHorizontal: 12,
      paddingVertical: 12,
      fontSize: 16,
      minHeight: 120,
      maxHeight: 200,
    },
    characterCount: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 8,
    },
    characterCountText: {
      fontSize: 12,
      fontWeight: '500',
    },
    clearButton: {
      fontSize: 12,
      fontWeight: '600',
    },
    templatesSection: {
      marginBottom: 20,
    },
    sectionLabel: {
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 12,
    },
    templatesScroll: {
      flexGrow: 0,
    },
    templatesContainer: {
      flexDirection: 'row',
      gap: 8,
      paddingRight: 20,
    },
    templateChip: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: theme.borderRadius.medium,
      borderWidth: 1,
    },
    templateText: {
      fontSize: 12,
      fontWeight: '500',
    },
    actionButtons: {
      flexDirection: 'row',
      gap: 12,
    },
    actionButton: {
      flex: 1,
    },
  });
};
