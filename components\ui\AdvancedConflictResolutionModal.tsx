/**
 * Advanced Conflict Resolution Modal Component
 *
 * Provides sophisticated field-level conflict resolution with intelligent
 * suggestions, user learning, and advanced merge strategies.
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import {
  ConflictField,
  DetailedConflict,
} from "@/src/services/AdvancedConflictResolver";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  <PERSON>ert,
  Modal,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface AdvancedConflictResolutionModalProps {
  visible: boolean;
  conflicts: DetailedConflict[];
  onResolve: (
    conflictId: string,
    userChoices: Record<string, "local" | "server" | "merge">
  ) => Promise<boolean>;
  onClose: () => void;
  onAutoResolveAll?: () => Promise<void>;
}

export const AdvancedConflictResolutionModal: React.FC<
  AdvancedConflictResolutionModalProps
> = ({ visible, conflicts, onResolve, onClose, onAutoResolveAll }) => {
  const { theme } = useTheme();
  const [currentConflictIndex, setCurrentConflictIndex] = useState(0);
  const [isResolving, setIsResolving] = useState(false);
  const [fieldChoices, setFieldChoices] = useState<
    Record<string, "local" | "server" | "merge">
  >({});
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [autoApplyChoices, setAutoApplyChoices] = useState(false);

  const currentConflict = conflicts[currentConflictIndex];

  // Reset field choices when conflict changes
  useEffect(() => {
    setFieldChoices({});
  }, [currentConflictIndex]);

  const getSeverityColor = (severity: DetailedConflict["severity"]) => {
    switch (severity) {
      case "low":
        return theme.colors.success;
      case "medium":
        return theme.colors.warning;
      case "high":
        return theme.colors.error;
      case "critical":
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getSeverityIcon = (severity: DetailedConflict["severity"]) => {
    switch (severity) {
      case "low":
        return "checkmark-circle";
      case "medium":
        return "warning";
      case "high":
        return "alert-circle";
      case "critical":
        return "close-circle";
      default:
        return "help-circle";
    }
  };

  const getFieldIcon = (field: string) => {
    switch (field) {
      case "items":
        return "list";
      case "total":
        return "calculator";
      case "customer":
        return "person";
      case "salesperson":
        return "people";
      case "note":
        return "document-text";
      case "discounts":
        return "pricetag";
      case "name":
        return "text";
      case "status":
        return "flag";
      default:
        return "help";
    }
  };

  const handleFieldChoice = (
    field: string,
    choice: "local" | "server" | "merge"
  ) => {
    setFieldChoices((prev) => ({
      ...prev,
      [field]: choice,
    }));
  };

  const handleResolveConflict = async () => {
    if (!currentConflict) return;

    // Check if all fields have choices
    const unselectedFields = currentConflict.fields.filter(
      (f) => !fieldChoices[f.field]
    );

    if (unselectedFields.length > 0) {
      Alert.alert(
        "Incomplete Selection",
        `Please select resolution for: ${unselectedFields
          .map((f) => f.field)
          .join(", ")}`,
        [{ text: "OK" }]
      );
      return;
    }

    setIsResolving(true);
    try {
      const success = await onResolve(currentConflict.ticketId, fieldChoices);

      if (success) {
        // Move to next conflict or close if done
        if (currentConflictIndex < conflicts.length - 1) {
          setCurrentConflictIndex(currentConflictIndex + 1);
        } else {
          onClose();
        }
      } else {
        Alert.alert("Error", "Failed to resolve conflict. Please try again.");
      }
    } catch (error) {
      Alert.alert(
        "Error",
        "An unexpected error occurred while resolving the conflict."
      );
    } finally {
      setIsResolving(false);
    }
  };

  const handleAutoResolveAll = async () => {
    if (!onAutoResolveAll) return;

    Alert.alert(
      "Auto-Resolve All Conflicts",
      "This will automatically resolve all conflicts using intelligent suggestions. Continue?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Auto-Resolve",
          style: "default",
          onPress: async () => {
            setIsResolving(true);
            try {
              await onAutoResolveAll();
              onClose();
            } catch (error) {
              Alert.alert("Error", "Failed to auto-resolve conflicts.");
            } finally {
              setIsResolving(false);
            }
          },
        },
      ]
    );
  };

  const renderConflictHeader = () => {
    if (!currentConflict) return null;

    return (
      <View style={styles.conflictHeader}>
        <View style={styles.conflictInfo}>
          <View style={styles.conflictTitleRow}>
            <Ionicons
              name={getSeverityIcon(currentConflict.severity)}
              size={24}
              color={getSeverityColor(currentConflict.severity)}
            />
            <Text style={[styles.conflictTitle, { color: theme.colors.text }]}>
              {currentConflict.conflictType.replace("-", " ").toUpperCase()}{" "}
              Conflict
            </Text>
          </View>

          <View style={styles.conflictMeta}>
            <Text
              style={[
                styles.conflictMetaText,
                { color: theme.colors.textSecondary },
              ]}
            >
              Severity: {currentConflict.severity.toUpperCase()}
            </Text>
            <Text
              style={[
                styles.conflictMetaText,
                { color: theme.colors.textSecondary },
              ]}
            >
              Strategy: {currentConflict.resolutionStrategy}
            </Text>
            <Text
              style={[
                styles.conflictMetaText,
                { color: theme.colors.textSecondary },
              ]}
            >
              Fields: {currentConflict.fields.length}
            </Text>
          </View>
        </View>

        <View style={styles.progressIndicator}>
          <Text
            style={[styles.progressText, { color: theme.colors.textSecondary }]}
          >
            {currentConflictIndex + 1} of {conflicts.length}
          </Text>
        </View>
      </View>
    );
  };

  const renderFieldConflict = (field: ConflictField, index: number) => {
    const choice = fieldChoices[field.field];
    const hasUserPreference = field.userPreference !== undefined;

    return (
      <View
        key={field.field}
        style={[styles.fieldCard, { backgroundColor: theme.colors.surface }]}
      >
        <View style={styles.fieldHeader}>
          <View style={styles.fieldTitleRow}>
            <Ionicons
              name={getFieldIcon(field.field)}
              size={20}
              color={theme.colors.primary}
            />
            <Text style={[styles.fieldTitle, { color: theme.colors.text }]}>
              {field.field.charAt(0).toUpperCase() + field.field.slice(1)}
            </Text>
            {hasUserPreference && (
              <View
                style={[
                  styles.preferenceIndicator,
                  { backgroundColor: theme.colors.warning },
                ]}
              >
                <Text
                  style={[
                    styles.preferenceText,
                    { color: theme.colors.onWarning },
                  ]}
                >
                  Learned
                </Text>
              </View>
            )}
          </View>

          <View style={styles.confidenceBar}>
            <View
              style={[
                styles.confidenceFill,
                {
                  width: `${field.confidence * 100}%`,
                  backgroundColor:
                    field.confidence > 0.7
                      ? theme.colors.success
                      : field.confidence > 0.4
                      ? theme.colors.warning
                      : theme.colors.error,
                },
              ]}
            />
          </View>
        </View>

        <View style={styles.fieldComparison}>
          <TouchableOpacity
            style={[
              styles.valueOption,
              choice === "local" && {
                backgroundColor: theme.colors.primary + "20",
                borderColor: theme.colors.primary,
              },
            ]}
            onPress={() => handleFieldChoice(field.field, "local")}
          >
            <Text
              style={[
                styles.optionLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Local
            </Text>
            <Text
              style={[styles.optionValue, { color: theme.colors.text }]}
              numberOfLines={2}
            >
              {formatFieldValue(field.localValue)}
            </Text>
            {choice === "local" && (
              <Ionicons
                name="checkmark-circle"
                size={20}
                color={theme.colors.primary}
              />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.valueOption,
              choice === "server" && {
                backgroundColor: theme.colors.success + "20",
                borderColor: theme.colors.success,
              },
            ]}
            onPress={() => handleFieldChoice(field.field, "server")}
          >
            <Text
              style={[
                styles.optionLabel,
                { color: theme.colors.textSecondary },
              ]}
            >
              Server
            </Text>
            <Text
              style={[styles.optionValue, { color: theme.colors.text }]}
              numberOfLines={2}
            >
              {formatFieldValue(field.serverValue)}
            </Text>
            {choice === "server" && (
              <Ionicons
                name="checkmark-circle"
                size={20}
                color={theme.colors.success}
              />
            )}
          </TouchableOpacity>

          {canMergeField(field.field) && (
            <TouchableOpacity
              style={[
                styles.valueOption,
                choice === "merge" && {
                  backgroundColor: theme.colors.warning + "20",
                  borderColor: theme.colors.warning,
                },
              ]}
              onPress={() => handleFieldChoice(field.field, "merge")}
            >
              <Text
                style={[
                  styles.optionLabel,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Merge
              </Text>
              <Text
                style={[styles.optionValue, { color: theme.colors.text }]}
                numberOfLines={2}
              >
                Smart merge
              </Text>
              {choice === "merge" && (
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={theme.colors.warning}
                />
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const formatFieldValue = (value: any): string => {
    if (value === null || value === undefined) return "None";
    if (typeof value === "string") return value;
    if (typeof value === "number") return value.toString();
    if (typeof value === "boolean") return value ? "Yes" : "No";
    if (Array.isArray(value)) return `${value.length} items`;
    if (typeof value === "object")
      return JSON.stringify(value).slice(0, 50) + "...";
    return String(value);
  };

  const canMergeField = (field: string): boolean => {
    return ["note", "items", "discounts"].includes(field);
  };

  if (!visible || conflicts.length === 0) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        {/* Header */}
        <View
          style={[styles.header, { borderBottomColor: theme.colors.border }]}
        >
          <View style={styles.headerLeft}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Advanced Conflict Resolution
            </Text>
            <Text
              style={[
                styles.headerSubtitle,
                { color: theme.colors.textSecondary },
              ]}
            >
              Field-level resolution with intelligent suggestions
            </Text>
          </View>

          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Conflict Header */}
        {renderConflictHeader()}

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {currentConflict?.fields.map((field, index) =>
            renderFieldConflict(field, index)
          )}
        </ScrollView>

        {/* Actions */}
        <View style={[styles.actions, { borderTopColor: theme.colors.border }]}>
          {showAdvancedOptions && (
            <View style={styles.advancedOptions}>
              <View style={styles.optionRow}>
                <Text
                  style={[styles.optionLabel, { color: theme.colors.text }]}
                >
                  Auto-apply choices to similar conflicts
                </Text>
                <Switch
                  value={autoApplyChoices}
                  onValueChange={setAutoApplyChoices}
                  trackColor={{
                    false: theme.colors.disabled,
                    true: theme.colors.primary,
                  }}
                />
              </View>
            </View>
          )}

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[
                styles.secondaryButton,
                { borderColor: theme.colors.border },
              ]}
              onPress={() => setShowAdvancedOptions(!showAdvancedOptions)}
            >
              <Ionicons
                name={showAdvancedOptions ? "chevron-up" : "chevron-down"}
                size={16}
                color={theme.colors.textSecondary}
              />
              <Text
                style={[
                  styles.secondaryButtonText,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Advanced
              </Text>
            </TouchableOpacity>

            {onAutoResolveAll && (
              <TouchableOpacity
                style={[
                  styles.secondaryButton,
                  { borderColor: theme.colors.warning },
                ]}
                onPress={handleAutoResolveAll}
                disabled={isResolving}
              >
                <Ionicons name="flash" size={16} color={theme.colors.warning} />
                <Text
                  style={[
                    styles.secondaryButtonText,
                    { color: theme.colors.warning },
                  ]}
                >
                  Auto-Resolve All
                </Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[
                styles.primaryButton,
                {
                  backgroundColor: theme.colors.primary,
                  opacity: isResolving ? 0.6 : 1,
                },
              ]}
              onPress={handleResolveConflict}
              disabled={isResolving}
            >
              <Text
                style={[
                  styles.primaryButtonText,
                  { color: theme.colors.onPrimary },
                ]}
              >
                {isResolving ? "Resolving..." : "Apply Resolution"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  closeButton: {
    padding: 8,
  },
  conflictHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  conflictInfo: {
    flex: 1,
  },
  conflictTitleRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  conflictTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  conflictMeta: {
    flexDirection: "row",
    gap: 16,
  },
  conflictMetaText: {
    fontSize: 12,
  },
  progressIndicator: {
    alignItems: "flex-end",
  },
  progressText: {
    fontSize: 14,
    fontWeight: "500",
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  fieldCard: {
    marginVertical: 8,
    padding: 16,
    borderRadius: 8,
  },
  fieldHeader: {
    marginBottom: 12,
  },
  fieldTitleRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  fieldTitle: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  preferenceIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  preferenceText: {
    fontSize: 10,
    fontWeight: "600",
  },
  confidenceBar: {
    height: 4,
    backgroundColor: "#E5E5E5",
    borderRadius: 2,
    overflow: "hidden",
  },
  confidenceFill: {
    height: "100%",
    borderRadius: 2,
  },
  fieldComparison: {
    flexDirection: "row",
    gap: 8,
  },
  valueOption: {
    flex: 1,
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    alignItems: "center",
    minHeight: 80,
    justifyContent: "space-between",
  },
  optionLabel: {
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 4,
  },
  optionValue: {
    fontSize: 14,
    textAlign: "center",
    flex: 1,
  },
  actions: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  advancedOptions: {
    marginBottom: 16,
  },
  optionRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 8,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
  },
  secondaryButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: "500",
  },
  primaryButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
});

export default AdvancedConflictResolutionModal;
