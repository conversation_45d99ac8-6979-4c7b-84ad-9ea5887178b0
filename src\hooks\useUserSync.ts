/**
 * User Sync Hook
 *
 * Bridges the gap between AuthContext and Redux by syncing user state
 * from AuthContext to Redux store for middleware access.
 */

import { useEffect } from "react";
import { useSession } from "../contexts/AuthContext";
import { useAppDispatch } from "../store";
import { setCurrentUser, clearCurrentUser } from "../store/slices/userSlice";

export const useUserSync = () => {
  const { user, isPosAuthenticated } = useSession();
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (isPosAuthenticated && user) {
      // Sync user to Redux store
      dispatch(setCurrentUser(user));
    } else {
      // Clear user from Redux store
      dispatch(clearCurrentUser());
    }
  }, [user, isPosAuthenticated, dispatch]);

  return { user, isPosAuthenticated };
};
