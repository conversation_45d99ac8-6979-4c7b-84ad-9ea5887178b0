/**
 * Responsive Layout Hook for Dukalink POS
 * Provides responsive utilities and screen size detection
 */

import { useState, useEffect } from "react";
import { Dimensions, Platform } from "react-native";
import {
  BREAKPOINTS,
  CONTAINER_MAX_WIDTHS,
  SPACING_MULTIPLIERS,
  MODAL_SIZES,
  BUTTON_SIZES,
  TYPOGRAPHY_SCALES,
  GRID_COLUMNS,
  NAVIGATION_HEIGHTS,
  isWeb,
  type ScreenSize,
} from "@/src/constants/ResponsiveConstants";

interface ResponsiveLayout {
  screenSize: ScreenSize;
  isDesktop: boolean;
  isTablet: boolean;
  isMobile: boolean;
  isLarge: boolean;
  width: number;
  height: number;
  containerMaxWidth: string | number;
  spacingMultiplier: number;
  typographyScale: number;
  gridColumns: number;
  navigationHeight: number;
  getModalSize: (size: "small" | "medium" | "large") => {
    width: string | number;
    maxWidth: number;
  };
  getButtonSize: (size: "small" | "medium" | "large") => {
    height: number;
    paddingHorizontal: number;
    fontSize: number;
  };
  getResponsiveValue: <T>(values: {
    mobile: T;
    tablet?: T;
    desktop?: T;
    large?: T;
  }) => T;
}

export const useResponsiveLayout = (): ResponsiveLayout => {
  const [dimensions, setDimensions] = useState(() => {
    try {
      return Dimensions.get("window");
    } catch (error) {
      console.warn("Failed to get window dimensions, using fallback", error);
      return { width: 375, height: 667 }; // iPhone SE fallback
    }
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener("change", ({ window }) => {
      setDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  // Determine screen size based on width
  const getScreenSize = (width: number): ScreenSize => {
    if (width >= BREAKPOINTS.large) return "large";
    if (width >= BREAKPOINTS.desktop) return "desktop";
    if (width >= BREAKPOINTS.tablet) return "tablet";
    return "mobile";
  };

  const screenSize = isWeb ? getScreenSize(dimensions.width) : "mobile";

  // Screen size booleans
  const isDesktop = screenSize === "desktop" || screenSize === "large";
  const isTablet = screenSize === "tablet";
  const isMobile = screenSize === "mobile";
  const isLarge = screenSize === "large";

  // Get responsive values
  const containerMaxWidth = CONTAINER_MAX_WIDTHS[screenSize];
  const spacingMultiplier = SPACING_MULTIPLIERS[screenSize];
  const typographyScale = TYPOGRAPHY_SCALES[screenSize];
  const gridColumns = GRID_COLUMNS[screenSize];
  const navigationHeight = NAVIGATION_HEIGHTS[screenSize];

  // Modal size getter
  const getModalSize = (
    size: "small" | "medium" | "large"
  ): { width: string | number; maxWidth: number } => {
    const modalSizeConfig = MODAL_SIZES[size];
    if (!modalSizeConfig) {
      // Fallback for missing size
      return { width: "90%", maxWidth: 400 };
    }

    const sizeForScreen = modalSizeConfig[screenSize];
    if (!sizeForScreen) {
      // Fallback to mobile if screen size not found
      const fallback = modalSizeConfig.mobile || {
        width: "90%",
        maxWidth: 400,
      };
      return {
        width: fallback.width as string | number,
        maxWidth: fallback.maxWidth,
      };
    }

    return {
      width: sizeForScreen.width as string | number,
      maxWidth: sizeForScreen.maxWidth,
    };
  };

  // Button size getter
  const getButtonSize = (size: "small" | "medium" | "large") => {
    const buttonSizeConfig = BUTTON_SIZES[size];
    if (!buttonSizeConfig) {
      // Fallback for missing size
      return { height: 44, paddingHorizontal: 16, fontSize: 16 };
    }

    const sizeForScreen = buttonSizeConfig[screenSize];
    if (!sizeForScreen) {
      // Fallback to mobile if screen size not found
      return (
        buttonSizeConfig.mobile || {
          height: 44,
          paddingHorizontal: 16,
          fontSize: 16,
        }
      );
    }

    return sizeForScreen;
  };

  // Generic responsive value getter
  const getResponsiveValue = <T>(values: {
    mobile: T;
    tablet?: T;
    desktop?: T;
    large?: T;
  }): T => {
    switch (screenSize) {
      case "large":
        return values.large ?? values.desktop ?? values.tablet ?? values.mobile;
      case "desktop":
        return values.desktop ?? values.tablet ?? values.mobile;
      case "tablet":
        return values.tablet ?? values.mobile;
      default:
        return values.mobile;
    }
  };

  const responsiveState = {
    screenSize,
    isDesktop,
    isTablet,
    isMobile,
    isLarge,
    width: dimensions.width,
    height: dimensions.height,
    containerMaxWidth,
    spacingMultiplier,
    typographyScale,
    gridColumns,
    navigationHeight,
    getModalSize,
    getButtonSize,
    getResponsiveValue,
  };

  // Log state changes in development (only when screen size changes)
  useEffect(() => {
    if (__DEV__) {
      console.log("🎯 Responsive Layout State:", {
        screenSize,
        dimensions: { width: dimensions.width, height: dimensions.height },
        platform: Platform.OS,
        isWeb,
      });
    }
  }, [screenSize, dimensions.width, dimensions.height]);

  return responsiveState;
};
