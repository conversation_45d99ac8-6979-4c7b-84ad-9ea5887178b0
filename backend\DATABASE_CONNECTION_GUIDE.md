# Database Connection Pool Configuration Guide

## 🚨 **CRITICAL PRODUCTION ISSUE RESOLVED**

This guide documents the solution to the database connection pool exhaustion issue that was causing production outages.

## **Root Cause Analysis**

### **Problems Identified:**
1. **Connection Pool Multiplication**: 10+ services each creating separate pools (10 connections each = 100+ total connections)
2. **Improper Connection Management**: Mixed patterns of connection usage without proper release
3. **No Singleton Pattern**: Multiple service instances creating duplicate connection pools
4. **Missing Timeout Configurations**: No production-ready timeout settings
5. **Hardcoded Configuration**: Connection limits not using environment variables

## **Solution Implemented**

### **1. Centralized Database Manager**
- **Single connection pool** for entire application
- **Proper timeout configurations** for production stability
- **Connection leak detection** and health monitoring
- **Automatic connection release** with try/finally patterns

### **2. Service Singleton Pattern**
- **All services converted to singletons** to prevent multiple instances
- **Consistent connection management** across all services
- **Reduced connection usage** from 100+ to 20 connections total

### **3. Production Environment Configuration**

#### **Required Environment Variables:**
```bash
# Database Connection Pool Settings (CRITICAL)
DB_CONNECTION_LIMIT=20          # Increased from 10 per service
DB_QUEUE_LIMIT=0               # Unlimited queue
DB_ACQUIRE_TIMEOUT=60000       # 60 seconds to acquire connection
DB_TIMEOUT=60000               # 60 seconds query timeout
DB_IDLE_TIMEOUT=300000         # 5 minutes idle timeout
DB_HEALTH_CHECK_INTERVAL=30000 # 30 seconds health check

# Database SSL Settings (Production)
DB_SSL=true                    # Enable SSL in production
DB_SSL_REJECT_UNAUTHORIZED=true

# Basic Database Settings
DB_HOST=your_mysql_host
DB_PORT=3306
DB_USER=dukalink
DB_PASSWORD=your_secure_password
DB_NAME=dukalink_pos
DB_CHARSET=utf8mb4
```

#### **Production Recommendations:**
```bash
# For high-traffic production environments
DB_CONNECTION_LIMIT=50         # Scale based on concurrent users
DB_ACQUIRE_TIMEOUT=30000       # Faster timeout for responsiveness
DB_IDLE_TIMEOUT=180000         # 3 minutes for high turnover

# For development environments
DB_CONNECTION_LIMIT=10         # Lower limit for development
DB_ACQUIRE_TIMEOUT=60000       # Longer timeout for debugging
DB_IDLE_TIMEOUT=600000         # 10 minutes for development
```

## **Monitoring and Health Checks**

### **New Monitoring Endpoints:**
- `GET /health` - Overall system health with database status
- `GET /api/database/health` - Detailed database health report
- `GET /api/database/stats` - Connection pool statistics
- `GET /api/database/leaks` - Connection leak detection

### **Health Check Example:**
```bash
curl http://localhost:3020/health
```

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "services": {
    "database": {
      "status": "OK",
      "connectionPool": {
        "active": 5,
        "limit": 20,
        "queued": 0
      }
    }
  },
  "monitoring": {
    "endpoint": "/api/database/health",
    "stats": "/api/database/stats",
    "leaks": "/api/database/leaks"
  }
}
```

## **Connection Leak Prevention**

### **Before (Problematic Pattern):**
```javascript
// BAD: Direct pool usage without proper management
const [rows] = await this.pool.execute(query, params);
```

### **After (Correct Pattern):**
```javascript
// GOOD: Centralized connection management
const [rows] = await this.databaseManager.executeQuery(query, params);
```

### **Transaction Pattern:**
```javascript
// GOOD: Automatic transaction management
const result = await this.databaseManager.executeTransaction(async (connection) => {
  await connection.execute('INSERT INTO table1 VALUES (?)', [value1]);
  await connection.execute('UPDATE table2 SET field = ?', [value2]);
  return { success: true };
});
```

## **Performance Monitoring**

### **Key Metrics to Monitor:**
1. **Connection Utilization**: Should stay below 80%
2. **Queue Length**: Should be 0 most of the time
3. **Query Success Rate**: Should be above 95%
4. **Response Time**: Database queries should complete within 5 seconds

### **Alert Thresholds:**
- **WARNING**: Connection utilization > 70%
- **CRITICAL**: Connection utilization > 90%
- **CRITICAL**: Queue length > 10
- **CRITICAL**: Query error rate > 10%

## **Deployment Checklist**

### **Before Deployment:**
1. ✅ Update `.env` file with new database configuration
2. ✅ Verify all services use centralized DatabaseManager
3. ✅ Test connection pool under load
4. ✅ Verify monitoring endpoints work

### **After Deployment:**
1. ✅ Monitor `/health` endpoint for database status
2. ✅ Check connection pool utilization
3. ✅ Verify no connection leaks in logs
4. ✅ Test system under normal load

## **Troubleshooting**

### **High Connection Utilization:**
```bash
# Check current stats
curl http://localhost:3020/api/database/stats

# Check for leaks
curl http://localhost:3020/api/database/leaks
```

### **Connection Pool Exhaustion:**
1. Check logs for unreleased connections
2. Monitor queue length
3. Increase `DB_CONNECTION_LIMIT` if needed
4. Restart server to reset pool

### **Slow Queries:**
1. Monitor query response times
2. Check for long-running transactions
3. Optimize database queries
4. Consider connection timeout adjustments

## **Migration Steps**

### **1. Update Environment:**
```bash
# Add new environment variables to .env
DB_CONNECTION_LIMIT=20
DB_IDLE_TIMEOUT=300000
DB_HEALTH_CHECK_INTERVAL=30000
```

### **2. Restart Application:**
```bash
# The application will automatically:
# - Initialize centralized database manager
# - Use singleton service instances
# - Start health monitoring
```

### **3. Verify Operation:**
```bash
# Check health
curl http://localhost:3020/health

# Monitor stats
curl http://localhost:3020/api/database/stats
```

## **Expected Results**

### **Before Fix:**
- 100+ potential database connections
- Intermittent connection exhaustion
- Server restarts required
- No connection monitoring

### **After Fix:**
- 20 total database connections
- Stable connection pool
- Automatic health monitoring
- Connection leak detection
- Production-ready configuration

## **Support**

If you experience any issues after implementing these changes:

1. Check the health endpoint: `/health`
2. Review connection stats: `/api/database/stats`
3. Check for leaks: `/api/database/leaks`
4. Review server logs for connection errors
5. Adjust `DB_CONNECTION_LIMIT` based on load

This solution should completely resolve the database connection pool exhaustion issue and provide stable production operation.
