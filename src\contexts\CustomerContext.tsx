import React, { createContext, useContext, useState, ReactNode } from "react";
import { CustomerLoyaltyData } from "../types/shopify";

export interface SelectedCustomer {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  displayName: string;
  ordersCount?: number;
  totalSpent?: string;
  loyaltyData?: CustomerLoyaltyData | null; // Include loyalty data for receipt generation
}

interface CustomerContextType {
  selectedCustomer: SelectedCustomer | null;
  setSelectedCustomer: (customer: SelectedCustomer | null) => void;
  clearSelectedCustomer: () => void;
}

const CustomerContext = createContext<CustomerContextType | undefined>(
  undefined
);

interface CustomerProviderProps {
  children: ReactNode;
}

export const CustomerProvider: React.FC<CustomerProviderProps> = ({
  children,
}) => {
  const [selectedCustomer, setSelectedCustomer] =
    useState<SelectedCustomer | null>(null);

  const clearSelectedCustomer = () => {
    setSelectedCustomer(null);
  };

  const value: CustomerContextType = {
    selectedCustomer,
    setSelectedCustomer,
    clearSelectedCustomer,
  };

  return (
    <CustomerContext.Provider value={value}>
      {children}
    </CustomerContext.Provider>
  );
};

export const useCustomer = (): CustomerContextType => {
  const context = useContext(CustomerContext);
  if (context === undefined) {
    throw new Error("useCustomer must be used within a CustomerProvider");
  }
  return context;
};
