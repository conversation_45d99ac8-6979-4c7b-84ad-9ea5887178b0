/**
 * Server Restart Script
 * Kills existing server and starts new one
 */

const { exec } = require("child_process");
const util = require("util");
const execAsync = util.promisify(exec);

async function restartServer() {
  try {
    console.log("🔄 Restarting server...");

    // Find process using port 3020
    try {
      const { stdout } = await execAsync("netstat -ano | findstr :3020");
      const lines = stdout
        .split("\n")
        .filter((line) => line.includes("LISTENING"));

      if (lines.length > 0) {
        const pid = lines[0].trim().split(/\s+/).pop();
        console.log(`🔍 Found server process: ${pid}`);

        // Kill the process
        try {
          await execAsync(`taskkill /F /PID ${pid}`);
          console.log("✅ Server process killed");
        } catch (killError) {
          console.log(
            "⚠️  Could not kill process, it may have already stopped"
          );
        }
      }
    } catch (netstatError) {
      console.log("⚠️  No server process found on port 3020");
    }

    // Wait a moment
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Start new server
    console.log("🚀 Starting new server...");
    const serverProcess = exec("npm run dev", { cwd: __dirname });

    serverProcess.stdout.on("data", (data) => {
      console.log(data.toString());
    });

    serverProcess.stderr.on("data", (data) => {
      console.error(data.toString());
    });

    // Wait for server to start
    await new Promise((resolve) => setTimeout(resolve, 5000));

    console.log("✅ Server restart completed");
  } catch (error) {
    console.error("❌ Server restart failed:", error.message);
  }
}

// Run restart
restartServer();
