#!/usr/bin/env node

/**
 * Database Manager Test Script
 * 
 * This script tests the centralized database manager to verify it's working correctly
 * and resolving the connection pool exhaustion issue.
 */

require('dotenv').config();
const { databaseManager } = require('./src/config/database-manager');

async function testDatabaseManager() {
  console.log('🧪 Testing Database Manager...\n');
  
  try {
    // Test 1: Initialize database manager
    console.log('1️⃣ Testing database manager initialization...');
    await databaseManager.initialize();
    console.log('✅ Database manager initialized successfully\n');
    
    // Test 2: Test basic connectivity
    console.log('2️⃣ Testing database connectivity...');
    const [rows] = await databaseManager.executeQuery('SELECT 1 as test, NOW() as current_time');
    console.log('✅ Database connectivity test passed:', rows[0]);
    console.log('');
    
    // Test 3: Test connection pool statistics
    console.log('3️⃣ Testing connection pool statistics...');
    const stats = databaseManager.getStats();
    console.log('📊 Connection Pool Stats:');
    console.log('   - Total Connections:', stats.connectionStats.totalConnections);
    console.log('   - Active Connections:', stats.connectionStats.activeConnections);
    console.log('   - Total Queries:', stats.connectionStats.totalQueries);
    console.log('   - Failed Queries:', stats.connectionStats.failedQueries);
    console.log('   - Connection Limit:', stats.poolConfig?.connectionLimit || 'N/A');
    console.log('   - Queue Limit:', stats.poolConfig?.queueLimit || 'N/A');
    console.log('');
    
    // Test 4: Test multiple concurrent queries
    console.log('4️⃣ Testing concurrent query execution...');
    const concurrentQueries = [];
    for (let i = 0; i < 5; i++) {
      concurrentQueries.push(
        databaseManager.executeQuery('SELECT ? as query_number, SLEEP(0.1) as delay', [i + 1])
      );
    }
    
    const results = await Promise.all(concurrentQueries);
    console.log('✅ Concurrent queries completed successfully');
    results.forEach((result, index) => {
      console.log(`   Query ${index + 1}:`, result[0][0]);
    });
    console.log('');
    
    // Test 5: Test transaction functionality
    console.log('5️⃣ Testing transaction functionality...');
    const transactionResult = await databaseManager.executeTransaction(async (connection) => {
      // Test transaction with multiple queries
      await connection.execute('SELECT 1 as step1');
      await connection.execute('SELECT 2 as step2');
      return { success: true, message: 'Transaction completed' };
    });
    console.log('✅ Transaction test passed:', transactionResult);
    console.log('');
    
    // Test 6: Test error handling
    console.log('6️⃣ Testing error handling...');
    try {
      await databaseManager.executeQuery('SELECT * FROM non_existent_table');
    } catch (error) {
      console.log('✅ Error handling test passed - caught expected error');
      console.log('   Error message:', error.message.substring(0, 50) + '...');
    }
    console.log('');
    
    // Test 7: Final statistics check
    console.log('7️⃣ Final connection pool statistics...');
    const finalStats = databaseManager.getStats();
    console.log('📊 Final Stats:');
    console.log('   - Total Connections:', finalStats.connectionStats.totalConnections);
    console.log('   - Active Connections:', finalStats.connectionStats.activeConnections);
    console.log('   - Total Queries:', finalStats.connectionStats.totalQueries);
    console.log('   - Failed Queries:', finalStats.connectionStats.failedQueries);
    console.log('   - Success Rate:', 
      finalStats.connectionStats.totalQueries > 0 
        ? Math.round(((finalStats.connectionStats.totalQueries - finalStats.connectionStats.failedQueries) / finalStats.connectionStats.totalQueries) * 100) + '%'
        : '100%'
    );
    console.log('');
    
    // Test 8: Connection pool health check
    console.log('8️⃣ Testing connection pool health...');
    await databaseManager.performHealthCheck();
    console.log('✅ Health check completed successfully');
    console.log('');
    
    console.log('🎉 All database manager tests passed!');
    console.log('');
    console.log('📋 Summary:');
    console.log('✅ Centralized database manager is working correctly');
    console.log('✅ Connection pool is properly configured');
    console.log('✅ Query execution is functioning normally');
    console.log('✅ Transaction support is working');
    console.log('✅ Error handling is proper');
    console.log('✅ Health monitoring is active');
    console.log('');
    console.log('🔧 The database connection pool exhaustion issue should be resolved!');
    
  } catch (error) {
    console.error('❌ Database manager test failed:', error);
    process.exit(1);
  } finally {
    // Graceful shutdown
    await databaseManager.shutdown();
    console.log('🔌 Database manager shut down gracefully');
    process.exit(0);
  }
}

// Run the test
testDatabaseManager();
