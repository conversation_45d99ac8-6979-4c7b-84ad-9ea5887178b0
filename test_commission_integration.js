/**
 * Test Commission System Integration for React Native
 * This tests the complete commission calculation flow that the React Native app will use
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020/api";

async function testCommissionIntegration() {
  console.log("💰 Testing Commission System Integration for React Native...\n");

  try {
    // <PERSON><PERSON> as manager first
    const loginResponse = await axios.post(`${BASE_URL}/pos/login`, {
      username: "manager1",
      password: "manager123",
    });

    if (!loginResponse.data.success) {
      console.log("❌ FAILED: Login required for commission testing");
      return;
    }

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    const authHeaders = { Authorization: `Bearer ${token}` };

    console.log("✅ Logged in as:", user.name);
    console.log("");

    // Test 1: Get Commission Configuration
    console.log("📋 Test 1: Get Commission Configuration");
    const configResponse = await axios.get(
      `${BASE_URL}/discounts/configuration`,
      {
        headers: authHeaders,
      }
    );

    if (configResponse.data.success) {
      console.log("✅ PASSED: Commission Configuration");
      const config = configResponse.data.data.configuration;
      console.log("   Configuration for React Native:");
      console.log(`     • System Enabled: ${config.enabled}`);
      console.log(`     • Min Order Amount: KSh ${config.min_order_amount}`);
      console.log(`     • Staff Discount Rate: ${config.staff_discount_rate}%`);
      console.log(`     • Agent Discount Rate: ${config.agent_discount_rate}%`);
      console.log(`     • Loyalty Multiplier: ${config.loyalty_multiplier}x`);
      console.log(`     • Max Discount: ${config.max_discount_percentage}%`);
    } else {
      console.log("❌ FAILED: Commission Configuration");
      return;
    }
    console.log("");

    // Test 2: Get Sales Agents for Commission
    console.log("📋 Test 2: Get Sales Agents for Commission");
    const agentsResponse = await axios.get(`${BASE_URL}/sales-agents`, {
      headers: authHeaders,
    });

    if (agentsResponse.data.success) {
      console.log("✅ PASSED: Sales Agents");
      const agents = agentsResponse.data.data.salesAgents;
      console.log(
        `   Found: ${agents.length} agents for commission calculation`
      );

      // Show agent commission rates
      agents.slice(0, 3).forEach((agent, index) => {
        console.log(
          `   Agent ${index + 1}: ${agent.name} - ${
            agent.commissionRate
          }% commission`
        );
      });
    } else {
      console.log("❌ FAILED: Sales Agents");
      return;
    }
    console.log("");

    // Test 3: Commission Calculation Scenarios
    console.log("📋 Test 3: Commission Calculation Scenarios");

    const testScenarios = [
      {
        name: "Small Cart (Below Min)",
        cartData: { subtotal: "30.00" },
        staffId: user.id,
        salesAgentId: "agent-001",
        expected: "No discount (below minimum)",
      },
      {
        name: "Medium Cart (Staff + Agent)",
        cartData: { subtotal: "100.00" },
        staffId: user.id,
        salesAgentId: "agent-001",
        expected: "Staff + Agent discount",
      },
      {
        name: "Large Cart (Staff + Agent + Loyalty)",
        cartData: { subtotal: "200.00" },
        staffId: user.id,
        salesAgentId: "agent-001",
        expected: "Full discount with loyalty bonus",
      },
      {
        name: "Staff Only (No Agent)",
        cartData: { subtotal: "150.00" },
        staffId: user.id,
        salesAgentId: null,
        expected: "Staff discount only",
      },
    ];

    for (const scenario of testScenarios) {
      console.log(`   Scenario: ${scenario.name}`);

      const calcData = {
        cartData: scenario.cartData,
        staffId: scenario.staffId,
      };

      if (scenario.salesAgentId) {
        calcData.salesAgentId = scenario.salesAgentId;
      }

      const calcResponse = await axios.post(
        `${BASE_URL}/discounts/calculate`,
        calcData,
        {
          headers: { ...authHeaders, "Content-Type": "application/json" },
        }
      );

      if (calcResponse.data.success) {
        const discount = calcResponse.data.data.discount;
        console.log(`     ✅ ${scenario.expected}`);
        console.log(`     • Subtotal: KSh ${discount.subtotal}`);
        console.log(
          `     • Total Discount: KSh ${discount.totalDiscount} (${discount.discountPercentage}%)`
        );
        console.log(
          `     • Staff Discount: KSh ${discount.staffDiscount || 0}`
        );
        console.log(
          `     • Agent Discount: KSh ${discount.agentDiscount || 0}`
        );
        console.log(`     • Loyalty Bonus: KSh ${discount.loyaltyBonus || 0}`);
        console.log(`     • Final Amount: KSh ${discount.finalAmount}`);
        console.log(`     • Savings: KSh ${discount.totalDiscount}`);
      } else {
        console.log(`     ❌ FAILED: ${scenario.name}`);
        console.log(`     Error: ${calcResponse.data.error}`);
      }
      console.log("");
    }

    // Test 4: React Native Cart Integration Simulation
    console.log("📋 Test 4: React Native Cart Integration Simulation");

    // Simulate a typical React Native cart
    const reactNativeCart = {
      items: [
        { id: "item1", name: "Product A", price: 50.0, quantity: 2 },
        { id: "item2", name: "Product B", price: 30.0, quantity: 1 },
        { id: "item3", name: "Product C", price: 20.0, quantity: 3 },
      ],
      subtotal: 170.0, // 100 + 30 + 60
      tax: 0,
      total: 170.0,
    };

    console.log("   React Native Cart Simulation:");
    console.log(`     • Items: ${reactNativeCart.items.length}`);
    console.log(`     • Subtotal: KSh ${reactNativeCart.subtotal}`);

    const cartCalcResponse = await axios.post(
      `${BASE_URL}/discounts/calculate`,
      {
        cartData: { subtotal: reactNativeCart.subtotal.toString() },
        staffId: user.id,
        salesAgentId: "agent-002", // Bob Kimani (7.5% commission)
      },
      {
        headers: { ...authHeaders, "Content-Type": "application/json" },
      }
    );

    if (cartCalcResponse.data.success) {
      const discount = cartCalcResponse.data.data.discount;
      console.log("   ✅ React Native Cart Commission Applied:");
      console.log(`     • Original Total: KSh ${reactNativeCart.total}`);
      console.log(`     • Commission Discount: KSh ${discount.totalDiscount}`);
      console.log(`     • New Total: KSh ${discount.finalAmount}`);
      console.log(`     • Customer Saves: KSh ${discount.totalDiscount}`);
      console.log(`     • Staff Commission: KSh ${discount.staffDiscount}`);
      console.log(`     • Agent Commission: KSh ${discount.agentDiscount}`);
      console.log(`     • Loyalty Bonus: KSh ${discount.loyaltyBonus}`);

      // Calculate what React Native would display
      const savingsPercentage = (
        (discount.totalDiscount / reactNativeCart.total) *
        100
      ).toFixed(1);
      console.log(`     • Savings Percentage: ${savingsPercentage}%`);
    } else {
      console.log("   ❌ FAILED: React Native Cart Commission");
    }
    console.log("");

    // Test 5: Different Agent Commission Rates
    console.log("📋 Test 5: Different Agent Commission Rates");

    const agents = agentsResponse.data.data.salesAgents;
    const testAmount = "120.00";

    for (const agent of agents.slice(0, 3)) {
      const agentCalcResponse = await axios.post(
        `${BASE_URL}/discounts/calculate`,
        {
          cartData: { subtotal: testAmount },
          staffId: user.id,
          salesAgentId: agent.id,
        },
        {
          headers: { ...authHeaders, "Content-Type": "application/json" },
        }
      );

      if (agentCalcResponse.data.success) {
        const discount = agentCalcResponse.data.data.discount;
        console.log(`   Agent: ${agent.name} (${agent.commissionRate}%)`);
        console.log(`     • Agent Discount: KSh ${discount.agentDiscount}`);
        console.log(`     • Total Discount: KSh ${discount.totalDiscount}`);
        console.log(`     • Final Amount: KSh ${discount.finalAmount}`);
      }
    }
    console.log("");

    console.log("🎉 Commission System Integration Test Completed!");
    console.log("✅ Commission configuration is accessible");
    console.log("✅ Real-time commission calculations work");
    console.log("✅ Multiple discount scenarios handled");
    console.log("✅ React Native cart integration ready");
    console.log("✅ Different agent commission rates supported");
    console.log("💰 Commission system is fully integrated with React Native!");
  } catch (error) {
    console.error("❌ Commission integration test failed:", error.message);
    if (error.response) {
      console.error("   Status:", error.response.status);
      console.error("   Data:", error.response.data);
    }
  }
}

// Run test
testCommissionIntegration();
