/**
 * Loyalty Management Dashboard Screen
 *
 * Comprehensive loyalty management interface for managers and admins.
 * Provides customer leaderboards, tier distribution analytics, loyalty configuration,
 * and points redemption reports with proper RBAC implementation.
 */

import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { PermissionGate } from "@/src/components/rbac";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import {
  LoyaltyAnalytics,
  loyaltyService,
} from "@/src/services/loyalty-service";
import { LoyaltyLeaderboardEntry } from "@/src/types/shopify";
import { createStyleUtils } from "@/src/utils/themeUtils";
import { router } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

interface TierDistributionData {
  tier: string;
  count: number;
  percentage: number;
  color: string;
}

export default function LoyaltyManagementScreen() {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const { hasPermission, hasAnyPermission } = useRBAC();

  // Check permissions
  const canViewLoyalty = hasPermission("view_loyalty");
  const canManageLoyalty = hasPermission("manage_loyalty");
  const canViewAnalytics = hasPermission("view_analytics");

  // State management
  const [activeTab, setActiveTab] = useState<
    "overview" | "leaderboard" | "analytics" | "config"
  >("overview");
  const [analytics, setAnalytics] = useState<LoyaltyAnalytics | null>(null);
  const [leaderboard, setLeaderboard] = useState<LoyaltyLeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch data
  const fetchData = useCallback(async () => {
    try {
      setError(null);

      const [analyticsData, leaderboardData] = await Promise.all([
        loyaltyService.getLoyaltyAnalytics(),
        loyaltyService.getLoyaltyLeaderboard({ limit: 20 }),
      ]);

      setAnalytics(analyticsData);
      setLeaderboard(leaderboardData);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch loyalty data"
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    if (canViewLoyalty || canViewAnalytics) {
      fetchData();
    }
  }, [fetchData, canViewLoyalty, canViewAnalytics]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchData();
  }, [fetchData]);

  // Permission check
  if (!canViewLoyalty && !canViewAnalytics) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol
              name="chevron.left"
              size={24}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <ThemedText variant="h2">Loyalty Management</ThemedText>
        </View>
        <View style={styles.centerContent}>
          <IconSymbol
            name="lock.fill"
            size={48}
            color={theme.colors.textSecondary}
          />
          <ThemedText
            variant="body"
            color="secondary"
            style={styles.noAccessText}
          >
            You don&apos;t have permission to access loyalty management
            features.
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  // Render tier distribution chart
  const renderTierDistribution = () => {
    if (!analytics?.tierDistribution) return null;

    const tierColors = {
      bronze: "#CD7F32",
      silver: "#C0C0C0",
      gold: "#FFD700",
      platinum: "#E5E4E2",
    };

    const tierData: TierDistributionData[] = Object.entries(
      analytics.tierDistribution
    ).map(([tier, count]) => ({
      tier: tier.charAt(0).toUpperCase() + tier.slice(1),
      count,
      percentage: (count / analytics.totalCustomers) * 100,
      color:
        tierColors[tier as keyof typeof tierColors] || theme.colors.primary,
    }));

    return (
      <ModernCard style={styles.chartCard}>
        <ThemedText variant="h3" style={styles.cardTitle}>
          Tier Distribution
        </ThemedText>
        <View style={styles.tierChart}>
          {tierData.map((item) => (
            <View key={item.tier} style={styles.tierItem}>
              <View
                style={[
                  styles.tierBar,
                  { backgroundColor: item.color, width: `${item.percentage}%` },
                ]}
              />
              <View style={styles.tierInfo}>
                <ThemedText variant="small" style={styles.tierName}>
                  {item.tier}
                </ThemedText>
                <ThemedText variant="small" color="secondary">
                  {item.count} customers ({item.percentage.toFixed(1)}%)
                </ThemedText>
              </View>
            </View>
          ))}
        </View>
      </ModernCard>
    );
  };

  // Render analytics overview
  const renderOverview = () => (
    <ScrollView
      style={styles.tabContent}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      showsVerticalScrollIndicator={false}
    >
      {analytics && (
        <>
          {/* Key Metrics */}
          <View style={styles.metricsGrid}>
            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="person.2.fill"
                size={24}
                color={theme.colors.primary}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {analytics.totalCustomers}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Total Customers
              </ThemedText>
            </ModernCard>

            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="star.fill"
                size={24}
                color={theme.colors.primary}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {analytics.totalPoints.toLocaleString()}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Total Points
              </ThemedText>
            </ModernCard>

            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="arrow.up.circle.fill"
                size={24}
                color={theme.colors.success}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {analytics.pointsEarned.toLocaleString()}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Points Earned
              </ThemedText>
            </ModernCard>

            <ModernCard style={styles.metricCard}>
              <IconSymbol
                name="arrow.down.circle.fill"
                size={24}
                color={theme.colors.warning}
              />
              <ThemedText variant="h2" style={styles.metricValue}>
                {analytics.pointsRedeemed.toLocaleString()}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Points Redeemed
              </ThemedText>
            </ModernCard>
          </View>

          {/* Tier Distribution */}
          {renderTierDistribution()}

          {/* Average Points */}
          <ModernCard style={styles.infoCard}>
            <View style={styles.infoRow}>
              <IconSymbol
                name="chart.bar.fill"
                size={20}
                color={theme.colors.primary}
              />
              <ThemedText variant="body">
                Average Points per Customer
              </ThemedText>
              <ThemedText variant="h3" color="primary">
                {analytics.averagePointsPerCustomer.toFixed(0)}
              </ThemedText>
            </View>
          </ModernCard>
        </>
      )}
    </ScrollView>
  );

  // Render leaderboard
  const renderLeaderboard = () => (
    <FlatList
      style={styles.tabContent}
      data={leaderboard}
      keyExtractor={(item) => item.customerId}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      renderItem={({ item, index }) => (
        <ModernCard style={styles.leaderboardItem}>
          <View style={styles.leaderboardRank}>
            <ThemedText variant="h3" color="primary">
              #{index + 1}
            </ThemedText>
          </View>
          <View style={styles.leaderboardInfo}>
            <ThemedText variant="body" style={styles.customerName}>
              {item.customerName}
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              {item.tier.charAt(0).toUpperCase() + item.tier.slice(1)} Tier
            </ThemedText>
          </View>
          <View style={styles.leaderboardStats}>
            <ThemedText variant="h3">
              {item.loyaltyPoints.toLocaleString()}
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              points
            </ThemedText>
          </View>
        </ModernCard>
      )}
      ListEmptyComponent={
        <View style={styles.emptyState}>
          <IconSymbol
            name="star"
            size={48}
            color={theme.colors.textSecondary}
          />
          <ThemedText variant="body" color="secondary">
            No leaderboard data available
          </ThemedText>
        </View>
      }
    />
  );

  // Render tab bar
  const renderTabBar = () => {
    const tabs = [
      { key: "overview", label: "Overview", icon: "chart.pie.fill" },
      { key: "leaderboard", label: "Leaderboard", icon: "trophy.fill" },
    ];

    if (canViewAnalytics) {
      tabs.push({
        key: "analytics",
        label: "Analytics",
        icon: "chart.bar.fill",
      });
    }

    if (canManageLoyalty) {
      tabs.push({ key: "config", label: "Config", icon: "gear" });
    }

    return (
      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              activeTab === tab.key && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <IconSymbol
              name={tab.icon}
              size={20}
              color={
                activeTab === tab.key
                  ? theme.colors.primary
                  : theme.colors.textSecondary
              }
            />
            <ThemedText
              variant="small"
              color={activeTab === tab.key ? "primary" : "secondary"}
              style={styles.tabLabel}
            >
              {tab.label}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol
              name="chevron.left"
              size={24}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <ThemedText variant="h2">Loyalty Management</ThemedText>
        </View>
        <View style={styles.centerContent}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <ThemedText
            variant="body"
            color="secondary"
            style={styles.loadingText}
          >
            Loading loyalty data...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol
              name="chevron.left"
              size={24}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <ThemedText variant="h2">Loyalty Management</ThemedText>
        </View>
        <View style={styles.centerContent}>
          <IconSymbol
            name="exclamationmark.triangle.fill"
            size={48}
            color={theme.colors.error}
          />
          <ThemedText variant="body" color="error" style={styles.errorText}>
            {error}
          </ThemedText>
          <ModernButton
            title="Retry"
            onPress={fetchData}
            style={styles.retryButton}
          />
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <ThemedText variant="h2">Loyalty Management</ThemedText>
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Tab Content */}
      {activeTab === "overview" && renderOverview()}
      {activeTab === "leaderboard" && renderLeaderboard()}
      {activeTab === "analytics" && (
        <PermissionGate requiredPermissions={["view_analytics"]}>
          <ThemedText variant="body" style={styles.comingSoon}>
            Advanced Analytics Coming Soon
          </ThemedText>
        </PermissionGate>
      )}
      {activeTab === "config" && (
        <PermissionGate requiredPermissions={["manage_loyalty"]}>
          <ThemedText variant="body" style={styles.comingSoon}>
            Loyalty Configuration Coming Soon
          </ThemedText>
        </PermissionGate>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  backButton: {
    marginRight: 12,
    padding: 8,
  },
  centerContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
  },
  noAccessText: {
    textAlign: "center",
    marginTop: 16,
  },
  loadingText: {
    marginTop: 16,
  },
  errorText: {
    textAlign: "center",
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    minWidth: 120,
  },
  tabBar: {
    flexDirection: "row",
    backgroundColor: "rgba(0,0,0,0.02)",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tabButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  activeTabButton: {
    backgroundColor: "rgba(0,0,0,0.05)",
  },
  tabLabel: {
    marginLeft: 6,
  },
  tabContent: {
    flex: 1,
    paddingHorizontal: 16,
  },
  metricsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginVertical: 16,
    gap: 12,
  },
  metricCard: {
    flex: 1,
    minWidth: "45%",
    alignItems: "center",
    paddingVertical: 20,
  },
  metricValue: {
    marginVertical: 8,
  },
  chartCard: {
    marginVertical: 8,
    padding: 16,
  },
  cardTitle: {
    marginBottom: 16,
  },
  tierChart: {
    gap: 12,
  },
  tierItem: {
    marginBottom: 8,
  },
  tierBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  tierInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  tierName: {
    fontWeight: "600",
  },
  infoCard: {
    marginVertical: 8,
    padding: 16,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  leaderboardItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginVertical: 4,
  },
  leaderboardRank: {
    width: 50,
    alignItems: "center",
  },
  leaderboardInfo: {
    flex: 1,
    marginLeft: 16,
  },
  customerName: {
    fontWeight: "600",
  },
  leaderboardStats: {
    alignItems: "flex-end",
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 64,
  },
  comingSoon: {
    textAlign: "center",
    marginTop: 64,
    fontStyle: "italic",
  },
});
