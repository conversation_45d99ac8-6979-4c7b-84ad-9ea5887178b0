/**
 * Payment Error Handler Service
 * 
 * Comprehensive error handling for payment processing
 * Provides user-friendly error messages and recovery suggestions
 */

export interface PaymentError {
  code: string;
  message: string;
  userMessage: string;
  recoverable: boolean;
  retryable: boolean;
  suggestions?: string[];
}

export interface ErrorContext {
  paymentMethod?: string;
  amount?: number;
  transactionId?: string;
  step?: string;
  metadata?: any;
}

class PaymentErrorHandler {
  private errorMap: Map<string, PaymentError> = new Map();

  constructor() {
    this.initializeErrorMap();
  }

  private initializeErrorMap() {
    // Network and API errors
    this.errorMap.set('NETWORK_ERROR', {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed',
      userMessage: 'Unable to connect to payment server. Please check your internet connection.',
      recoverable: true,
      retryable: true,
      suggestions: [
        'Check your internet connection',
        'Try again in a few moments',
        'Contact support if the problem persists'
      ]
    });

    this.errorMap.set('API_TIMEOUT', {
      code: 'API_TIMEOUT',
      message: 'Request timeout',
      userMessage: 'Payment processing is taking longer than expected.',
      recoverable: true,
      retryable: true,
      suggestions: [
        'Please wait and try again',
        'Check your internet connection',
        'Contact support if timeouts continue'
      ]
    });

    this.errorMap.set('SERVER_ERROR', {
      code: 'SERVER_ERROR',
      message: 'Internal server error',
      userMessage: 'Payment server is temporarily unavailable.',
      recoverable: true,
      retryable: true,
      suggestions: [
        'Try again in a few minutes',
        'Contact support if the problem persists'
      ]
    });

    // Authentication errors
    this.errorMap.set('AUTH_FAILED', {
      code: 'AUTH_FAILED',
      message: 'Authentication failed',
      userMessage: 'Session expired. Please log in again.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Log out and log back in',
        'Contact your manager if login issues persist'
      ]
    });

    this.errorMap.set('INSUFFICIENT_PERMISSIONS', {
      code: 'INSUFFICIENT_PERMISSIONS',
      message: 'Insufficient permissions',
      userMessage: 'You do not have permission to process payments.',
      recoverable: false,
      retryable: false,
      suggestions: [
        'Contact your manager for payment permissions',
        'Use a different staff account'
      ]
    });

    // Payment validation errors
    this.errorMap.set('INVALID_AMOUNT', {
      code: 'INVALID_AMOUNT',
      message: 'Invalid payment amount',
      userMessage: 'The payment amount is invalid.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Check the payment amount',
        'Ensure amount is greater than zero',
        'Verify the total matches the order'
      ]
    });

    this.errorMap.set('AMOUNT_MISMATCH', {
      code: 'AMOUNT_MISMATCH',
      message: 'Payment amounts do not match total',
      userMessage: 'Payment method amounts do not equal the order total.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Adjust payment amounts to match the total',
        'Remove and re-add payment methods',
        'Check for calculation errors'
      ]
    });

    this.errorMap.set('INSUFFICIENT_FUNDS', {
      code: 'INSUFFICIENT_FUNDS',
      message: 'Insufficient funds',
      userMessage: 'Insufficient funds for this payment.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Try a different payment method',
        'Reduce the payment amount',
        'Ask customer to use another account'
      ]
    });

    // M-Pesa specific errors
    this.errorMap.set('MPESA_STK_FAILED', {
      code: 'MPESA_STK_FAILED',
      message: 'M-Pesa STK Push failed',
      userMessage: 'M-Pesa payment request failed.',
      recoverable: true,
      retryable: true,
      suggestions: [
        'Verify the phone number is correct',
        'Ensure the phone has sufficient balance',
        'Try manual transaction code entry',
        'Ask customer to check their M-Pesa PIN'
      ]
    });

    this.errorMap.set('MPESA_TIMEOUT', {
      code: 'MPESA_TIMEOUT',
      message: 'M-Pesa transaction timeout',
      userMessage: 'M-Pesa payment timed out.',
      recoverable: true,
      retryable: true,
      suggestions: [
        'Ask customer to check their phone',
        'Try the payment again',
        'Use manual transaction code if payment was completed'
      ]
    });

    this.errorMap.set('MPESA_INVALID_PHONE', {
      code: 'MPESA_INVALID_PHONE',
      message: 'Invalid M-Pesa phone number',
      userMessage: 'Invalid phone number for M-Pesa.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Check the phone number format',
        'Ensure number starts with 254 or 07',
        'Verify with the customer'
      ]
    });

    this.errorMap.set('MPESA_INVALID_CODE', {
      code: 'MPESA_INVALID_CODE',
      message: 'Invalid M-Pesa transaction code',
      userMessage: 'Invalid M-Pesa transaction code.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Check the transaction code format',
        'Ensure code is from M-Pesa SMS',
        'Ask customer to provide correct code'
      ]
    });

    // ABSA Till errors
    this.errorMap.set('ABSA_INVALID_CODE', {
      code: 'ABSA_INVALID_CODE',
      message: 'Invalid ABSA Till transaction code',
      userMessage: 'Invalid ABSA Till transaction code.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Check the transaction code format',
        'Ensure code is 8-15 characters',
        'Verify with the customer'
      ]
    });

    this.errorMap.set('ABSA_VALIDATION_FAILED', {
      code: 'ABSA_VALIDATION_FAILED',
      message: 'ABSA Till validation failed',
      userMessage: 'ABSA Till transaction could not be validated.',
      recoverable: true,
      retryable: true,
      suggestions: [
        'Verify the transaction code',
        'Check with ABSA Till system',
        'Try again in a few minutes'
      ]
    });

    // Card payment errors
    this.errorMap.set('CARD_DECLINED', {
      code: 'CARD_DECLINED',
      message: 'Card payment declined',
      userMessage: 'Card payment was declined.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Try a different card',
        'Check card details',
        'Contact card issuer',
        'Use alternative payment method'
      ]
    });

    this.errorMap.set('CARD_EXPIRED', {
      code: 'CARD_EXPIRED',
      message: 'Card has expired',
      userMessage: 'The payment card has expired.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Use a different card',
        'Ask customer for updated card',
        'Try alternative payment method'
      ]
    });

    // Credit payment errors
    this.errorMap.set('CREDIT_LIMIT_EXCEEDED', {
      code: 'CREDIT_LIMIT_EXCEEDED',
      message: 'Credit limit exceeded',
      userMessage: 'Customer credit limit exceeded.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Reduce the credit amount',
        'Use partial credit with other payment',
        'Contact manager for credit increase',
        'Use alternative payment method'
      ]
    });

    this.errorMap.set('CREDIT_CUSTOMER_REQUIRED', {
      code: 'CREDIT_CUSTOMER_REQUIRED',
      message: 'Customer information required for credit',
      userMessage: 'Customer details required for credit payment.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Add customer information',
        'Select existing customer',
        'Create new customer profile'
      ]
    });

    // Transaction errors
    this.errorMap.set('TRANSACTION_NOT_FOUND', {
      code: 'TRANSACTION_NOT_FOUND',
      message: 'Transaction not found',
      userMessage: 'Payment transaction could not be found.',
      recoverable: false,
      retryable: false,
      suggestions: [
        'Start a new payment',
        'Contact support with transaction details'
      ]
    });

    this.errorMap.set('TRANSACTION_ALREADY_PROCESSED', {
      code: 'TRANSACTION_ALREADY_PROCESSED',
      message: 'Transaction already processed',
      userMessage: 'This payment has already been processed.',
      recoverable: false,
      retryable: false,
      suggestions: [
        'Check transaction status',
        'Start a new payment if needed'
      ]
    });

    this.errorMap.set('DUPLICATE_PAYMENT', {
      code: 'DUPLICATE_PAYMENT',
      message: 'Duplicate payment detected',
      userMessage: 'Duplicate payment detected.',
      recoverable: true,
      retryable: false,
      suggestions: [
        'Check if payment was already completed',
        'Verify transaction history',
        'Contact support if unsure'
      ]
    });
  }

  /**
   * Handle and categorize payment errors
   */
  handleError(error: any, context?: ErrorContext): PaymentError {
    let errorCode = 'UNKNOWN_ERROR';
    let originalMessage = 'Unknown error occurred';

    if (error instanceof Error) {
      originalMessage = error.message;
      
      // Try to extract error code from message
      if (error.message.includes('network') || error.message.includes('fetch')) {
        errorCode = 'NETWORK_ERROR';
      } else if (error.message.includes('timeout')) {
        errorCode = 'API_TIMEOUT';
      } else if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
        errorCode = 'AUTH_FAILED';
      } else if (error.message.includes('permission')) {
        errorCode = 'INSUFFICIENT_PERMISSIONS';
      } else if (error.message.includes('amount') && error.message.includes('invalid')) {
        errorCode = 'INVALID_AMOUNT';
      } else if (error.message.includes('amount') && error.message.includes('match')) {
        errorCode = 'AMOUNT_MISMATCH';
      } else if (error.message.includes('insufficient')) {
        errorCode = 'INSUFFICIENT_FUNDS';
      } else if (error.message.includes('M-Pesa') || error.message.includes('mpesa')) {
        if (error.message.includes('phone')) {
          errorCode = 'MPESA_INVALID_PHONE';
        } else if (error.message.includes('code')) {
          errorCode = 'MPESA_INVALID_CODE';
        } else if (error.message.includes('timeout')) {
          errorCode = 'MPESA_TIMEOUT';
        } else {
          errorCode = 'MPESA_STK_FAILED';
        }
      } else if (error.message.includes('ABSA') || error.message.includes('absa')) {
        if (error.message.includes('code') || error.message.includes('format')) {
          errorCode = 'ABSA_INVALID_CODE';
        } else {
          errorCode = 'ABSA_VALIDATION_FAILED';
        }
      } else if (error.message.includes('card') || error.message.includes('Card')) {
        if (error.message.includes('declined')) {
          errorCode = 'CARD_DECLINED';
        } else if (error.message.includes('expired')) {
          errorCode = 'CARD_EXPIRED';
        }
      } else if (error.message.includes('credit') || error.message.includes('Credit')) {
        if (error.message.includes('limit')) {
          errorCode = 'CREDIT_LIMIT_EXCEEDED';
        } else if (error.message.includes('customer') || error.message.includes('required')) {
          errorCode = 'CREDIT_CUSTOMER_REQUIRED';
        }
      } else if (error.message.includes('transaction')) {
        if (error.message.includes('not found')) {
          errorCode = 'TRANSACTION_NOT_FOUND';
        } else if (error.message.includes('already')) {
          errorCode = 'TRANSACTION_ALREADY_PROCESSED';
        } else if (error.message.includes('duplicate')) {
          errorCode = 'DUPLICATE_PAYMENT';
        }
      } else if (error.message.includes('server') || error.message.includes('500')) {
        errorCode = 'SERVER_ERROR';
      }
    }

    // Get predefined error or create default
    let paymentError = this.errorMap.get(errorCode);
    
    if (!paymentError) {
      paymentError = {
        code: errorCode,
        message: originalMessage,
        userMessage: 'An unexpected error occurred during payment processing.',
        recoverable: true,
        retryable: true,
        suggestions: [
          'Try the payment again',
          'Check your internet connection',
          'Contact support if the problem persists'
        ]
      };
    }

    // Add context-specific suggestions
    if (context) {
      paymentError = this.addContextualSuggestions(paymentError, context);
    }

    return paymentError;
  }

  /**
   * Add contextual suggestions based on error context
   */
  private addContextualSuggestions(error: PaymentError, context: ErrorContext): PaymentError {
    const contextualSuggestions = [...(error.suggestions || [])];

    if (context.paymentMethod) {
      switch (context.paymentMethod) {
        case 'mpesa':
          if (!contextualSuggestions.some(s => s.includes('manual'))) {
            contextualSuggestions.push('Try manual transaction code entry');
          }
          break;
        case 'card':
          if (!contextualSuggestions.some(s => s.includes('different'))) {
            contextualSuggestions.push('Try a different payment method');
          }
          break;
        case 'credit':
          if (!contextualSuggestions.some(s => s.includes('partial'))) {
            contextualSuggestions.push('Consider partial credit with cash payment');
          }
          break;
      }
    }

    if (context.step === 'split_payment' && !contextualSuggestions.some(s => s.includes('single'))) {
      contextualSuggestions.push('Try using a single payment method instead');
    }

    return {
      ...error,
      suggestions: contextualSuggestions
    };
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(error: any, context?: ErrorContext): string {
    const paymentError = this.handleError(error, context);
    return paymentError.userMessage;
  }

  /**
   * Check if error is retryable
   */
  isRetryable(error: any, context?: ErrorContext): boolean {
    const paymentError = this.handleError(error, context);
    return paymentError.retryable;
  }

  /**
   * Check if error is recoverable
   */
  isRecoverable(error: any, context?: ErrorContext): boolean {
    const paymentError = this.handleError(error, context);
    return paymentError.recoverable;
  }

  /**
   * Get recovery suggestions
   */
  getSuggestions(error: any, context?: ErrorContext): string[] {
    const paymentError = this.handleError(error, context);
    return paymentError.suggestions || [];
  }
}

export const paymentErrorHandler = new PaymentErrorHandler();
export default paymentErrorHandler;
