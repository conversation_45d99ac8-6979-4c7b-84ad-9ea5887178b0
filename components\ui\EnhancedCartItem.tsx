/**
 * EnhancedCartItem Component
 *
 * Advanced cart item component with inline editing capabilities including:
 * - Inline quantity editing with real-time validation
 * - Discount application (percentage/fixed amount)
 * - Notes editing with templates
 * - Enhanced visual feedback and animations
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import { CartItem } from "@/src/types/shopify";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { useState } from "react";
import {
  Alert,
  Animated,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { DiscountApplicator } from "./DiscountApplicator";
import { IconSymbol } from "./IconSymbol";
import { InlineQuantityEditor } from "./InlineQuantityEditor";
import { ModernCard } from "./ModernCard";
import { NotesEditor } from "./NotesEditor";

interface EnhancedCartItemDiscount {
  type: "percentage" | "fixed_amount";
  amount: number;
  description?: string;
}

interface EnhancedCartItem extends CartItem {
  discount?: EnhancedCartItemDiscount;
  notes?: string;
}

interface EnhancedCartItemProps {
  item: EnhancedCartItem;
  onUpdateQuantity: (variantId: string, quantity: number) => void;
  onApplyDiscount: (
    variantId: string,
    discount: EnhancedCartItemDiscount | null
  ) => void;
  onUpdateNotes: (variantId: string, notes: string) => void;
  onRemove: (variantId: string) => void;
  style?: ViewStyle;
  index?: number;
  showAdvancedControls?: boolean;
  compactMode?: boolean;
}

export function EnhancedCartItem({
  item,
  onUpdateQuantity,
  onApplyDiscount,
  onUpdateNotes,
  onRemove,
  style,
  index = 0,
  showAdvancedControls = true,
  compactMode = false,
}: EnhancedCartItemProps) {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const [isExpanded, setIsExpanded] = useState(false);
  const [animatedHeight] = useState(new Animated.Value(0));

  const styles = createStyles(theme, compactMode);

  const basePrice = parseFloat(item.price);
  const lineTotal = basePrice * item.quantity;
  const discountAmount = item.discount
    ? item.discount.type === "percentage"
      ? (lineTotal * item.discount.amount) / 100
      : Math.min(item.discount.amount, lineTotal)
    : 0;
  const finalTotal = lineTotal - discountAmount;

  const isLowStock = item.inventoryQuantity <= 5 && item.inventoryQuantity > 0;
  const isOutOfStock = item.inventoryQuantity <= 0;
  const hasDiscount = item.discount && item.discount.amount > 0;
  const hasNotes = item.notes && item.notes.trim().length > 0;

  const handleQuantityChange = (quantity: number) => {
    onUpdateQuantity(item.variantId, quantity);
  };

  const handleDiscountChange = (discount: EnhancedCartItemDiscount | null) => {
    onApplyDiscount(item.variantId, discount);
  };

  const handleNotesChange = (notes: string) => {
    onUpdateNotes(item.variantId, notes);
  };

  const handleRemoveItem = () => {
    Alert.alert("Remove Item", `Remove "${item.title}" from cart?`, [
      { text: "Cancel", style: "cancel" },
      {
        text: "Remove",
        style: "destructive",
        onPress: () => onRemove(item.variantId),
      },
    ]);
  };

  const toggleExpanded = () => {
    const toValue = isExpanded ? 0 : 1;
    setIsExpanded(!isExpanded);

    Animated.timing(animatedHeight, {
      toValue,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const getStockStatusColor = () => {
    if (isOutOfStock) return theme.colors.error;
    if (isLowStock) return theme.colors.warning;
    return theme.colors.success;
  };

  const getStockStatusText = () => {
    if (isOutOfStock) return "Out of Stock";
    if (isLowStock) return `${item.inventoryQuantity} left`;
    return `${item.inventoryQuantity} in stock`;
  };

  return (
    <ModernCard style={[styles.container, style]}>
      {/* Main Item Content */}
      <View style={styles.mainContent}>
        {/* Product Image */}
        <View style={styles.imageContainer}>
          {item.image ? (
            <Image source={{ uri: item.image }} style={styles.productImage} />
          ) : (
            <View
              style={[
                styles.placeholderImage,
                { backgroundColor: theme.colors.backgroundSecondary },
              ]}
            >
              <IconSymbol
                name="photo"
                size={24}
                color={theme.colors.textSecondary}
              />
            </View>
          )}
        </View>

        {/* Product Info */}
        <View style={styles.productInfo}>
          <Text
            style={[styles.productTitle, { color: theme.colors.text }]}
            numberOfLines={2}
          >
            {item.title}
          </Text>

          {item.variantTitle && item.variantTitle !== "Default Title" && (
            <Text
              style={[
                styles.variantTitle,
                { color: theme.colors.textSecondary },
              ]}
            >
              {item.variantTitle}
            </Text>
          )}

          {item.sku && (
            <Text style={[styles.sku, { color: theme.colors.textSecondary }]}>
              SKU: {item.sku}
            </Text>
          )}

          {/* Stock Status */}
          <Text style={[styles.stockStatus, { color: getStockStatusColor() }]}>
            {getStockStatusText()}
          </Text>

          {/* Price and Discount Info */}
          <View style={styles.priceContainer}>
            <Text
              style={[styles.unitPrice, { color: theme.colors.textSecondary }]}
            >
              {formatCurrency(basePrice)} each
            </Text>

            {hasDiscount && (
              <View style={styles.discountInfo}>
                <Text
                  style={[
                    styles.originalTotal,
                    { color: theme.colors.textSecondary },
                  ]}
                >
                  {formatCurrency(lineTotal)}
                </Text>
                <Text
                  style={[
                    styles.discountedTotal,
                    { color: theme.colors.success },
                  ]}
                >
                  {formatCurrency(finalTotal)}
                </Text>
              </View>
            )}

            {!hasDiscount && (
              <Text style={[styles.lineTotal, { color: theme.colors.text }]}>
                {formatCurrency(lineTotal)}
              </Text>
            )}
          </View>
        </View>

        {/* Controls */}
        <View style={styles.controlsContainer}>
          {/* Quantity Editor */}
          <InlineQuantityEditor
            value={item.quantity}
            max={item.inventoryQuantity}
            onQuantityChange={handleQuantityChange}
            size={compactMode ? "small" : "medium"}
            showStock={false}
          />

          {/* Advanced Controls */}
          {showAdvancedControls && (
            <View style={styles.advancedControls}>
              <DiscountApplicator
                currentDiscount={item.discount}
                itemPrice={basePrice}
                itemQuantity={item.quantity}
                onApplyDiscount={handleDiscountChange}
                size={compactMode ? "small" : "medium"}
              />

              <NotesEditor
                value={item.notes}
                onNotesChange={handleNotesChange}
                size={compactMode ? "small" : "medium"}
              />
            </View>
          )}

          {/* Expand/Collapse Button */}
          {(hasDiscount || hasNotes) && (
            <TouchableOpacity
              style={styles.expandButton}
              onPress={toggleExpanded}
            >
              <IconSymbol
                name={isExpanded ? "chevron.up" : "chevron.down"}
                size={16}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Remove Button */}
        <TouchableOpacity
          style={[
            styles.removeButton,
            { backgroundColor: theme.colors.error + "20" },
          ]}
          onPress={handleRemoveItem}
        >
          <IconSymbol name="trash" size={18} color={theme.colors.error} />
        </TouchableOpacity>
      </View>

      {/* Expanded Details */}
      {(hasDiscount || hasNotes) && (
        <Animated.View
          style={[
            styles.expandedContent,
            {
              opacity: animatedHeight,
              maxHeight: animatedHeight.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 200],
              }),
            },
          ]}
        >
          {hasDiscount && (
            <View style={styles.discountDetails}>
              <IconSymbol
                name="tag.fill"
                size={14}
                color={theme.colors.success}
              />
              <Text
                style={[styles.discountText, { color: theme.colors.success }]}
              >
                {item.discount!.type === "percentage"
                  ? `${item.discount!.amount}% discount`
                  : `${formatCurrency(item.discount!.amount)} discount`}
                {item.discount!.description &&
                  ` - ${item.discount!.description}`}
              </Text>
            </View>
          )}

          {hasNotes && (
            <View style={styles.notesDetails}>
              <IconSymbol
                name="note.text"
                size={14}
                color={theme.colors.primary}
              />
              <Text style={[styles.notesText, { color: theme.colors.text }]}>
                {item.notes}
              </Text>
            </View>
          )}
        </Animated.View>
      )}
    </ModernCard>
  );
}

const createStyles = (theme: any, compactMode: boolean) => {
  return StyleSheet.create({
    container: {
      marginVertical: 4,
      overflow: "hidden",
    },
    mainContent: {
      flexDirection: "row",
      padding: compactMode ? 12 : 16,
      alignItems: "flex-start",
    },
    imageContainer: {
      marginRight: 12,
    },
    productImage: {
      width: compactMode ? 50 : 60,
      height: compactMode ? 50 : 60,
      borderRadius: theme.borderRadius.medium,
    },
    placeholderImage: {
      width: compactMode ? 50 : 60,
      height: compactMode ? 50 : 60,
      borderRadius: theme.borderRadius.medium,
      justifyContent: "center",
      alignItems: "center",
    },
    productInfo: {
      flex: 1,
      marginRight: 12,
    },
    productTitle: {
      fontSize: compactMode ? 14 : 16,
      fontWeight: "600",
      marginBottom: 4,
    },
    variantTitle: {
      fontSize: compactMode ? 12 : 13,
      marginBottom: 2,
    },
    sku: {
      fontSize: 11,
      marginBottom: 4,
    },
    stockStatus: {
      fontSize: 11,
      fontWeight: "500",
      marginBottom: 8,
    },
    priceContainer: {
      alignItems: "flex-start",
    },
    unitPrice: {
      fontSize: 12,
      marginBottom: 4,
    },
    discountInfo: {
      flexDirection: "row",
      alignItems: "center",
      gap: 8,
    },
    originalTotal: {
      fontSize: 14,
      textDecorationLine: "line-through",
    },
    discountedTotal: {
      fontSize: 16,
      fontWeight: "600",
    },
    lineTotal: {
      fontSize: 16,
      fontWeight: "600",
    },
    controlsContainer: {
      alignItems: "center",
      gap: 8,
    },
    advancedControls: {
      flexDirection: "row",
      gap: 8,
    },
    expandButton: {
      padding: 4,
    },
    removeButton: {
      position: "absolute",
      top: 8,
      right: 8,
      width: 28,
      height: 28,
      borderRadius: 14,
      justifyContent: "center",
      alignItems: "center",
    },
    expandedContent: {
      paddingHorizontal: 16,
      paddingBottom: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    discountDetails: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
      gap: 8,
    },
    discountText: {
      fontSize: 12,
      fontWeight: "500",
      flex: 1,
    },
    notesDetails: {
      flexDirection: "row",
      alignItems: "flex-start",
      gap: 8,
    },
    notesText: {
      fontSize: 12,
      flex: 1,
      lineHeight: 16,
    },
  });
};
