#!/usr/bin/env node

/**
 * Test script to check store customers endpoint with loyalty data
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3020/api';
let authToken = '';

async function login() {
  console.log('🔐 Logging in...');
  try {
    const response = await axios.post(`${BASE_URL}/pos/login`, {
      username: 'admin1',
      password: 'admin123'
    });

    if (response.data.success) {
      authToken = response.data.data.token;
      console.log('✅ Login successful');
      console.log(`   Token: ${authToken.substring(0, 30)}...`);
      return true;
    } else {
      console.log('❌ Login failed:', response.data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return false;
  }
}

async function testStoreCustomersWithLoyalty() {
  console.log('\n📋 Testing /api/store/customers with loyalty data...');
  try {
    const response = await axios.get(`${BASE_URL}/store/customers`, {
      params: {
        includeLoyalty: true,
        limit: 5
      },
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', response.headers['content-type']);

    if (response.data.success) {
      console.log('✅ Store customers fetched successfully');
      console.log(`   Total customers: ${response.data.data.customers.length}`);
      
      // Check each customer for loyalty data
      response.data.data.customers.forEach((customer, index) => {
        console.log(`\n   Customer ${index + 1}:`);
        console.log(`   Name: ${customer.displayName || customer.email || 'No name'}`);
        console.log(`   ID: ${customer.id}`);
        console.log(`   Email: ${customer.email || 'No email'}`);
        console.log(`   Orders: ${customer.ordersCount || 0}`);
        console.log(`   Total Spent: KSh ${customer.totalSpent || '0.00'}`);
        
        if (customer.loyaltyData) {
          console.log('   ✅ Loyalty data found:');
          console.log(`      Points: ${customer.loyaltyData.loyaltyPoints}`);
          console.log(`      Tier: ${customer.loyaltyData.tier}`);
          console.log(`      Total Purchases: KSh ${customer.loyaltyData.totalPurchases}`);
          console.log(`      Total Orders: ${customer.loyaltyData.totalOrders}`);
          console.log(`      Member Since: ${customer.loyaltyData.memberSince}`);
          if (customer.loyaltyData.progressToNextTier) {
            console.log(`      Next Tier: ${customer.loyaltyData.progressToNextTier.nextTier}`);
          }
        } else {
          console.log('   ❌ No loyalty data found');
        }
      });
      
      return true;
    } else {
      console.log('❌ Failed to fetch store customers:', response.data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error fetching store customers:', error.message);
    if (error.response) {
      console.log('   Response status:', error.response.status);
      console.log('   Response data:', JSON.stringify(error.response.data, null, 2));
    }
    if (error.code === 'ECONNABORTED') {
      console.log('   ⏰ Request timed out - this might indicate a slow response');
    }
    return false;
  }
}

async function testStoreCustomersWithoutLoyalty() {
  console.log('\n📋 Testing /api/store/customers without loyalty data...');
  try {
    const response = await axios.get(`${BASE_URL}/store/customers`, {
      params: {
        includeLoyalty: false,
        limit: 5
      },
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    if (response.data.success) {
      console.log('✅ Store customers fetched successfully (without loyalty)');
      console.log(`   Total customers: ${response.data.data.customers.length}`);
      
      // Check that no loyalty data is included
      const hasLoyaltyData = response.data.data.customers.some(customer => customer.loyaltyData);
      if (hasLoyaltyData) {
        console.log('   ⚠️  Unexpected: Loyalty data found when not requested');
      } else {
        console.log('   ✅ No loyalty data included (as expected)');
      }
      
      return true;
    } else {
      console.log('❌ Failed to fetch store customers:', response.data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error fetching store customers:', error.message);
    return false;
  }
}

async function testSpecificCustomerLoyalty() {
  console.log('\n🎯 Testing specific customer loyalty data...');
  
  // First get a customer ID from the store customers
  try {
    const customersResponse = await axios.get(`${BASE_URL}/store/customers`, {
      params: { limit: 1 },
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (customersResponse.data.success && customersResponse.data.data.customers.length > 0) {
      const customerId = customersResponse.data.data.customers[0].id;
      console.log(`   Testing customer ID: ${customerId}`);
      
      // Now test the loyalty endpoint directly
      const loyaltyResponse = await axios.get(`${BASE_URL}/loyalty/customers/${customerId}/summary`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (loyaltyResponse.data.success) {
        console.log('   ✅ Direct loyalty API works:');
        console.log(`      Points: ${loyaltyResponse.data.data.loyaltyPoints}`);
        console.log(`      Tier: ${loyaltyResponse.data.data.tier}`);
        return true;
      } else {
        console.log('   ❌ Direct loyalty API failed:', loyaltyResponse.data.error);
        return false;
      }
    } else {
      console.log('   ❌ No customers found to test');
      return false;
    }
  } catch (error) {
    console.log('   ❌ Error testing specific customer loyalty:', error.message);
    return false;
  }
}

async function main() {
  console.log('🧪 Starting Store Customers Loyalty Test\n');
  
  // Login first
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    process.exit(1);
  }

  // Test store customers with loyalty data
  const withLoyaltySuccess = await testStoreCustomersWithLoyalty();
  
  // Test store customers without loyalty data
  const withoutLoyaltySuccess = await testStoreCustomersWithoutLoyalty();
  
  // Test specific customer loyalty
  const specificLoyaltySuccess = await testSpecificCustomerLoyalty();
  
  console.log('\n📊 Test Results:');
  console.log(`   Store customers with loyalty: ${withLoyaltySuccess ? '✅' : '❌'}`);
  console.log(`   Store customers without loyalty: ${withoutLoyaltySuccess ? '✅' : '❌'}`);
  console.log(`   Specific customer loyalty: ${specificLoyaltySuccess ? '✅' : '❌'}`);
  
  if (withLoyaltySuccess && withoutLoyaltySuccess && specificLoyaltySuccess) {
    console.log('\n🎉 All tests passed! The API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
}

// Run the tests
main().catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
