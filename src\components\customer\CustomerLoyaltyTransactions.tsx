/**
 * Customer Loyalty Transactions Component
 *
 * Displays a list of loyalty transactions for a customer including
 * points earned, redeemed, expired, and adjusted.
 */

import React from "react";
import { View, Text, FlatList, StyleSheet } from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { ModernCard } from "@/components/ui/ModernCard";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { LoyaltyTransaction } from "@/src/types/shopify";

interface CustomerLoyaltyTransactionsProps {
  transactions: LoyaltyTransaction[];
  loading?: boolean;
  useScrollView?: boolean; // If true, renders items directly without FlatList
}

export const CustomerLoyaltyTransactions: React.FC<
  CustomerLoyaltyTransactionsProps
> = ({ transactions, loading = false, useScrollView = false }) => {
  const theme = useTheme();

  // Helper function to format transaction type
  const formatTransactionType = (type: string) => {
    switch (type) {
      case "earned":
        return "Points Earned";
      case "redeemed":
        return "Points Redeemed";
      case "expired":
        return "Points Expired";
      case "adjusted":
        return "Points Adjusted";
      default:
        return type;
    }
  };

  // Helper function to get transaction icon
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "earned":
        return "plus.circle.fill";
      case "redeemed":
        return "minus.circle.fill";
      case "expired":
        return "clock.fill";
      case "adjusted":
        return "pencil.circle.fill";
      default:
        return "circle.fill";
    }
  };

  // Helper function to get transaction color
  const getTransactionColor = (type: string) => {
    switch (type) {
      case "earned":
        return theme.colors.success;
      case "redeemed":
        return theme.colors.warning;
      case "expired":
        return theme.colors.error;
      case "adjusted":
        return theme.colors.primary;
      default:
        return theme.colors.textSecondary;
    }
  };

  const renderTransaction = ({ item }: { item: LoyaltyTransaction }) => (
    <ModernCard style={styles.transactionCard} variant="outlined">
      <View style={styles.transactionHeader}>
        <View style={styles.transactionIcon}>
          <IconSymbol
            name={getTransactionIcon(item.type)}
            size={20}
            color={getTransactionColor(item.type)}
          />
        </View>
        <View style={styles.transactionInfo}>
          <Text style={[styles.transactionType, { color: theme.colors.text }]}>
            {formatTransactionType(item.type)}
          </Text>
          <Text
            style={[
              styles.transactionDescription,
              { color: theme.colors.textSecondary },
            ]}
          >
            {item.description}
          </Text>
        </View>
        <View style={styles.transactionAmount}>
          <Text
            style={[
              styles.pointsAmount,
              {
                color:
                  item.type === "earned"
                    ? theme.colors.success
                    : item.type === "redeemed"
                    ? theme.colors.warning
                    : theme.colors.textSecondary,
              },
            ]}
          >
            {item.type === "earned" ? "+" : item.type === "redeemed" ? "-" : ""}
            {Math.abs(item.points).toLocaleString()}
          </Text>
          <Text
            style={[styles.pointsLabel, { color: theme.colors.textSecondary }]}
          >
            points
          </Text>
        </View>
      </View>

      <View style={styles.transactionFooter}>
        <Text
          style={[
            styles.transactionDate,
            { color: theme.colors.textSecondary },
          ]}
        >
          {new Date(item.createdAt).toLocaleDateString("en-KE", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
          })}
        </Text>

        {item.orderId && (
          <Text style={[styles.orderId, { color: theme.colors.textSecondary }]}>
            Order: {item.orderId.slice(-8)}
          </Text>
        )}

        {item.staffName && (
          <Text
            style={[styles.staffName, { color: theme.colors.textSecondary }]}
          >
            by {item.staffName}
          </Text>
        )}
      </View>
    </ModernCard>
  );

  if (loading) {
    return (
      <ModernCard style={styles.card}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Transaction History
        </Text>
        <Text
          style={[styles.loadingText, { color: theme.colors.textSecondary }]}
        >
          Loading transactions...
        </Text>
      </ModernCard>
    );
  }

  if (transactions.length === 0) {
    return (
      <ModernCard style={styles.card}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Transaction History
        </Text>
        <View style={styles.emptyState}>
          <IconSymbol
            name="list.bullet"
            size={48}
            color={theme.colors.textSecondary}
          />
          <Text
            style={[styles.emptyText, { color: theme.colors.textSecondary }]}
          >
            No loyalty transactions found
          </Text>
        </View>
      </ModernCard>
    );
  }

  if (useScrollView) {
    // Render items directly without FlatList to avoid nesting issues
    return (
      <View style={styles.container}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Transaction History ({transactions.length})
        </Text>
        <View style={styles.listContent}>
          {transactions.map((item) => (
            <View key={item.id}>{renderTransaction({ item })}</View>
          ))}
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Transaction History ({transactions.length})
      </Text>
      <FlatList
        data={transactions}
        renderItem={renderTransaction}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    padding: 20,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  loadingText: {
    textAlign: "center",
    fontStyle: "italic",
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 32,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 14,
    fontStyle: "italic",
  },
  listContent: {
    paddingBottom: 20,
  },
  transactionCard: {
    padding: 16,
    marginBottom: 8,
  },
  transactionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  transactionIcon: {
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionType: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 2,
  },
  transactionDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  transactionAmount: {
    alignItems: "flex-end",
  },
  pointsAmount: {
    fontSize: 16,
    fontWeight: "700",
  },
  pointsLabel: {
    fontSize: 10,
    marginTop: 1,
  },
  transactionFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.05)",
  },
  transactionDate: {
    fontSize: 11,
    flex: 1,
  },
  orderId: {
    fontSize: 11,
    marginHorizontal: 8,
  },
  staffName: {
    fontSize: 11,
  },
});
