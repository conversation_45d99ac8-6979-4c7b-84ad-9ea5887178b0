/**
 * Enhanced Authentication Middleware for Multi-User Sessions
 * 
 * Supports PIN-based user switching while maintaining session context
 * and providing proper user context for switched users.
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const MultiUserSessionService = require('../services/multi-user-session-service');
const { getPool } = require('../config/database-mysql');

// Initialize multi-user session service
let multiUserSessionService;
const initializeService = () => {
  if (!multiUserSessionService) {
    const pool = getPool();
    multiUserSessionService = new MultiUserSessionService(pool);
  }
  return multiUserSessionService;
};

/**
 * Enhanced authentication middleware that supports multi-user sessions
 */
const authenticateTokenWithMultiUser = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'No token provided'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'your-secret-key'
    );

    // Create token hash for session lookup
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    // Get session context (includes user switching info)
    const service = initializeService();
    const sessionContext = await service.getSessionContext(decoded.sessionId || null);

    if (!sessionContext) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired session'
      });
    }

    // Check if session is expired
    if (new Date() > new Date(sessionContext.expiresAt)) {
      return res.status(401).json({
        success: false,
        error: 'Session expired'
      });
    }

    // Check if current user is active
    if (!sessionContext.currentUser) {
      return res.status(401).json({
        success: false,
        error: 'No active user in session'
      });
    }

    // Add enhanced user info to request
    req.user = {
      id: sessionContext.currentUser.id,
      username: sessionContext.currentUser.username,
      name: sessionContext.currentUser.name,
      role: sessionContext.currentUser.role,
      storeId: decoded.storeId,
      permissions: sessionContext.currentUser.permissions,
      commissionRate: sessionContext.currentUser.commissionRate,
      terminalId: sessionContext.terminalId,
      locationId: sessionContext.locationId,
    };

    // Add session context info
    req.sessionContext = {
      sessionId: sessionContext.sessionId,
      primaryUser: sessionContext.primaryUser,
      currentUser: sessionContext.currentUser,
      userStack: sessionContext.userStack,
      hasActiveSwitch: sessionContext.userStack.length > 0,
      canSwitchBack: sessionContext.userStack.length > 0,
      lastSwitchAt: sessionContext.lastSwitchAt,
      terminalId: sessionContext.terminalId,
      locationId: sessionContext.locationId
    };

    // Add session ID for compatibility
    req.sessionId = sessionContext.sessionId;

    next();
  } catch (error) {
    console.error('Multi-user authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired'
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Authentication failed'
    });
  }
};

/**
 * Middleware to check if user has specific permission
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (!req.user.permissions.includes(permission) && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        error: `Permission required: ${permission}`
      });
    }

    next();
  };
};

/**
 * Middleware to check if user has any of the specified roles
 */
const requireRole = (roles) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return (req, res, next) => {
    if (!req.user || !req.user.role) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: `Role required: ${allowedRoles.join(' or ')}`
      });
    }

    next();
  };
};

/**
 * Middleware to ensure user is the primary session user (not switched)
 */
const requirePrimaryUser = (req, res, next) => {
  if (!req.sessionContext) {
    return res.status(401).json({
      success: false,
      error: 'Session context required'
    });
  }

  if (req.sessionContext.hasActiveSwitch) {
    return res.status(403).json({
      success: false,
      error: 'This action requires the primary session user. Please switch back first.'
    });
  }

  next();
};

/**
 * Middleware to log user actions for audit trail
 */
const auditAction = (actionType) => {
  return async (req, res, next) => {
    // Store audit info in request for later logging
    req.auditInfo = {
      actionType,
      userId: req.user?.id,
      sessionId: req.sessionId,
      isPrimaryUser: !req.sessionContext?.hasActiveSwitch,
      timestamp: new Date().toISOString(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    };

    // Continue to next middleware
    next();
  };
};

/**
 * Middleware to validate terminal access
 */
const validateTerminalAccess = async (req, res, next) => {
  try {
    const { terminalId } = req.body;
    
    if (!terminalId) {
      return next(); // Skip validation if no terminal specified
    }

    // Check if user has access to this terminal
    const pool = getPool();
    const [terminalRows] = await pool.execute(
      `SELECT id, location_id, is_active 
       FROM pos_terminals 
       WHERE id = ? AND is_active = 1`,
      [terminalId]
    );

    if (terminalRows.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or inactive terminal'
      });
    }

    const terminal = terminalRows[0];

    // Check if user has access to terminal's location
    if (req.user.locationId && req.user.locationId !== terminal.location_id) {
      return res.status(403).json({
        success: false,
        error: 'Access denied to this terminal location'
      });
    }

    req.terminal = terminal;
    next();
  } catch (error) {
    console.error('Terminal validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Terminal validation failed'
    });
  }
};

/**
 * Middleware to check session health and auto-cleanup
 */
const sessionHealthCheck = async (req, res, next) => {
  try {
    if (req.sessionContext && req.sessionContext.sessionId) {
      const service = initializeService();
      
      // Perform periodic cleanup (every 100 requests approximately)
      if (Math.random() < 0.01) {
        setImmediate(() => service.cleanup());
      }
    }
    
    next();
  } catch (error) {
    console.error('Session health check error:', error);
    next(); // Don't block request on health check failure
  }
};

/**
 * Enhanced error handler for authentication errors
 */
const authErrorHandler = (error, req, res, next) => {
  console.error('Authentication error:', error);

  // Log security event if it's an authentication failure
  if (req.user?.id) {
    const service = initializeService();
    setImmediate(() => {
      service.logSecurityEvent(req.user.id, 'AUTH_ERROR', {
        error: error.message,
        sessionId: req.sessionId,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
    });
  }

  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      error: 'Authentication failed'
    });
  }

  if (error.name === 'ForbiddenError') {
    return res.status(403).json({
      success: false,
      error: 'Access denied'
    });
  }

  // Default error response
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
};

module.exports = {
  authenticateTokenWithMultiUser,
  requirePermission,
  requireRole,
  requirePrimaryUser,
  auditAction,
  validateTerminalAccess,
  sessionHealthCheck,
  authErrorHandler
};
