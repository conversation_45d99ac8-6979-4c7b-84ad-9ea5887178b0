/**
 * Test Terminal Management System
 */

require('dotenv').config();
const terminalService = require('./src/services/terminal-management-service');

async function testTerminalManagement() {
  try {
    console.log('🔧 Testing Terminal Management System...\n');
    
    // Test 1: Get all terminals
    console.log('📋 Test 1: Get All Terminals');
    const allTerminals = await terminalService.getAllTerminals();
    
    if (allTerminals.success) {
      console.log('✅ PASSED: Get All Terminals');
      console.log(`   Found ${allTerminals.terminals.length} terminals`);
      allTerminals.terminals.forEach(terminal => {
        console.log(`   • ${terminal.name} (${terminal.id}) - ${terminal.isOnline ? 'Online' : 'Offline'}`);
      });
    } else {
      console.log('❌ FAILED: Get All Terminals');
      console.log('   Error:', allTerminals.error);
    }
    
    console.log('');
    
    // Test 2: Get specific terminal
    console.log('📋 Test 2: Get Terminal by ID');
    const terminal = await terminalService.getTerminalById('terminal-001');
    
    if (terminal.success) {
      console.log('✅ PASSED: Get Terminal by ID');
      console.log(`   Terminal: ${terminal.terminal.name}`);
      console.log(`   Location: ${terminal.terminal.locationName}`);
      console.log(`   Type: ${terminal.terminal.terminalType}`);
      console.log(`   Status: ${terminal.terminal.isOnline ? 'Online' : 'Offline'}`);
    } else {
      console.log('❌ FAILED: Get Terminal by ID');
      console.log('   Error:', terminal.error);
    }
    
    console.log('');
    
    // Test 3: Register new terminal
    console.log('📋 Test 3: Register New Terminal');
    const newTerminal = await terminalService.registerTerminal({
      name: 'Test Terminal MySQL',
      locationId: 'location-test',
      locationName: 'Test Location',
      storeId: 'default-store',
      terminalType: 'mobile',
      hardwareInfo: {
        model: 'Test Device',
        serialNumber: 'TEST-001'
      },
      networkInfo: {
        ipAddress: '*************',
        connectionType: 'wifi'
      }
    });
    
    if (newTerminal.success) {
      console.log('✅ PASSED: Register New Terminal');
      console.log(`   Created terminal: ${newTerminal.terminal.id}`);
      console.log(`   Name: ${newTerminal.terminal.name}`);
    } else {
      console.log('❌ FAILED: Register New Terminal');
      console.log('   Error:', newTerminal.error);
    }
    
    console.log('');
    
    // Test 4: Update terminal status
    console.log('📋 Test 4: Update Terminal Status');
    const statusUpdate = await terminalService.updateTerminalStatus('terminal-001', {
      isOnline: true,
      currentStaffId: 'pos-001'
    });
    
    if (statusUpdate.success) {
      console.log('✅ PASSED: Update Terminal Status');
      console.log('   Status updated successfully');
    } else {
      console.log('❌ FAILED: Update Terminal Status');
      console.log('   Error:', statusUpdate.error);
    }
    
    console.log('');
    
    // Test 5: Assign staff to terminal
    console.log('📋 Test 5: Assign Staff to Terminal');
    const staffAssignment = await terminalService.assignStaffToTerminal(
      'terminal-002', 
      'pos-002', 
      'session-test-001'
    );
    
    if (staffAssignment.success) {
      console.log('✅ PASSED: Assign Staff to Terminal');
      console.log(`   Assigned staff ${staffAssignment.assignment.staffId} to terminal ${staffAssignment.assignment.terminalId}`);
    } else {
      console.log('❌ FAILED: Assign Staff to Terminal');
      console.log('   Error:', staffAssignment.error);
    }
    
    console.log('');
    
    // Test 6: Get terminals by location
    console.log('📋 Test 6: Get Terminals by Location');
    const locationTerminals = await terminalService.getTerminalsByLocation('location-001');
    
    if (locationTerminals.success) {
      console.log('✅ PASSED: Get Terminals by Location');
      console.log(`   Found ${locationTerminals.terminals.length} terminals in location-001`);
      locationTerminals.terminals.forEach(terminal => {
        console.log(`   • ${terminal.name} - ${terminal.isOnline ? 'Online' : 'Offline'}`);
      });
    } else {
      console.log('❌ FAILED: Get Terminals by Location');
      console.log('   Error:', locationTerminals.error);
    }
    
    console.log('');
    
    // Test 7: Get terminal statistics
    console.log('📋 Test 7: Get Terminal Statistics');
    const stats = await terminalService.getTerminalStatistics();
    
    if (stats.success) {
      console.log('✅ PASSED: Get Terminal Statistics');
      console.log(`   Total terminals: ${stats.statistics.total}`);
      console.log(`   Active terminals: ${stats.statistics.active}`);
      console.log(`   Online terminals: ${stats.statistics.online}`);
      console.log(`   Offline terminals: ${stats.statistics.offline}`);
      console.log('   By type:', stats.statistics.byType);
    } else {
      console.log('❌ FAILED: Get Terminal Statistics');
      console.log('   Error:', stats.error);
    }
    
    console.log('\n🎉 Terminal Management testing completed!');
    
  } catch (error) {
    console.error('❌ Terminal Management test failed:', error.message);
    console.error('   Stack:', error.stack);
  }
}

// Run test
testTerminalManagement();
