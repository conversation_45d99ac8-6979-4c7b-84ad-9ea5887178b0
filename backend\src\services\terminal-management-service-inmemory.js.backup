const { v4: uuidv4 } = require('uuid');

class TerminalManagementService {
  constructor() {
    // In production, this would be stored in a database
    this.terminals = new Map();
    this.initializeDefaultTerminals();
  }

  // Initialize some default terminals for demo
  initializeDefaultTerminals() {
    const defaultTerminals = [
      {
        id: 'terminal-001',
        name: 'Main Counter POS',
        locationId: '77757743241', // Ngong Road location ID from Shopify
        locationName: 'Ngong Road',
        deviceId: 'DEVICE-MAIN-001',
        status: 'active',
        type: 'main_counter',
        capabilities: ['sales', 'returns', 'inventory_check', 'customer_management'],
        assignedStaff: null,
        lastActivity: null,
        createdAt: new Date().toISOString(),
      },
      {
        id: 'terminal-002',
        name: 'Mobile POS 1',
        locationId: '77757743241', // Same location for demo
        locationName: 'Ngong Road',
        deviceId: 'DEVICE-MOBILE-001',
        status: 'active',
        type: 'mobile',
        capabilities: ['sales', 'inventory_check'],
        assignedStaff: null,
        lastActivity: null,
        createdAt: new Date().toISOString(),
      },
      {
        id: 'terminal-003',
        name: 'Manager Terminal',
        locationId: '77757743241', // Same location for demo
        locationName: 'Ngong Road',
        deviceId: 'DEVICE-MANAGER-001',
        status: 'active',
        type: 'manager',
        capabilities: ['sales', 'returns', 'inventory_check', 'customer_management', 'reports', 'staff_management'],
        assignedStaff: null,
        lastActivity: null,
        createdAt: new Date().toISOString(),
      },
    ];

    defaultTerminals.forEach(terminal => {
      this.terminals.set(terminal.id, terminal);
    });

    console.log(`✅ Initialized ${defaultTerminals.length} default POS terminals`);
  }

  // Get terminal by device ID or create new one
  async getOrCreateTerminal(deviceInfo) {
    try {
      // Try to find existing terminal by device ID
      const existingTerminal = Array.from(this.terminals.values()).find(
        terminal => terminal.deviceId === deviceInfo.deviceId
      );

      if (existingTerminal) {
        // Update last activity
        existingTerminal.lastActivity = new Date().toISOString();
        return {
          success: true,
          terminal: existingTerminal,
        };
      }

      // Create new terminal if not found
      const newTerminal = {
        id: `terminal-${uuidv4()}`,
        name: deviceInfo.name || `POS Terminal ${Date.now()}`,
        locationId: deviceInfo.locationId || '77757743241', // Default to Ngong Road
        locationName: deviceInfo.locationName || 'Ngong Road',
        deviceId: deviceInfo.deviceId,
        status: 'active',
        type: deviceInfo.type || 'mobile',
        capabilities: deviceInfo.capabilities || ['sales', 'inventory_check'],
        assignedStaff: null,
        lastActivity: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      };

      this.terminals.set(newTerminal.id, newTerminal);

      console.log(`📱 Created new terminal: ${newTerminal.name} at ${newTerminal.locationName}`);

      return {
        success: true,
        terminal: newTerminal,
      };
    } catch (error) {
      console.error('Get or create terminal error:', error);
      return {
        success: false,
        error: 'Failed to get or create terminal',
      };
    }
  }

  // Assign staff to terminal
  async assignStaffToTerminal(terminalId, staffInfo) {
    try {
      const terminal = this.terminals.get(terminalId);
      if (!terminal) {
        return {
          success: false,
          error: 'Terminal not found',
        };
      }

      terminal.assignedStaff = {
        id: staffInfo.id,
        username: staffInfo.username,
        name: staffInfo.name,
        role: staffInfo.role,
        assignedAt: new Date().toISOString(),
      };
      terminal.lastActivity = new Date().toISOString();

      console.log(`👤 Assigned ${staffInfo.name} to terminal ${terminal.name}`);

      return {
        success: true,
        terminal,
      };
    } catch (error) {
      console.error('Assign staff to terminal error:', error);
      return {
        success: false,
        error: 'Failed to assign staff to terminal',
      };
    }
  }

  // Unassign staff from terminal
  async unassignStaffFromTerminal(terminalId) {
    try {
      const terminal = this.terminals.get(terminalId);
      if (!terminal) {
        return {
          success: false,
          error: 'Terminal not found',
        };
      }

      const previousStaff = terminal.assignedStaff;
      terminal.assignedStaff = null;
      terminal.lastActivity = new Date().toISOString();

      console.log(`👤 Unassigned ${previousStaff?.name || 'staff'} from terminal ${terminal.name}`);

      return {
        success: true,
        terminal,
      };
    } catch (error) {
      console.error('Unassign staff from terminal error:', error);
      return {
        success: false,
        error: 'Failed to unassign staff from terminal',
      };
    }
  }

  // Get all terminals
  async getAllTerminals() {
    try {
      const terminals = Array.from(this.terminals.values());
      return {
        success: true,
        terminals,
      };
    } catch (error) {
      console.error('Get all terminals error:', error);
      return {
        success: false,
        error: 'Failed to get terminals',
      };
    }
  }

  // Get terminals by location
  async getTerminalsByLocation(locationId) {
    try {
      const terminals = Array.from(this.terminals.values()).filter(
        terminal => terminal.locationId === locationId
      );
      return {
        success: true,
        terminals,
      };
    } catch (error) {
      console.error('Get terminals by location error:', error);
      return {
        success: false,
        error: 'Failed to get terminals by location',
      };
    }
  }

  // Update terminal location (admin function)
  async updateTerminalLocation(terminalId, locationId, locationName) {
    try {
      const terminal = this.terminals.get(terminalId);
      if (!terminal) {
        return {
          success: false,
          error: 'Terminal not found',
        };
      }

      terminal.locationId = locationId;
      terminal.locationName = locationName;
      terminal.lastActivity = new Date().toISOString();

      console.log(`📍 Updated terminal ${terminal.name} location to ${locationName}`);

      return {
        success: true,
        terminal,
      };
    } catch (error) {
      console.error('Update terminal location error:', error);
      return {
        success: false,
        error: 'Failed to update terminal location',
      };
    }
  }

  // Get terminal info with location details
  async getTerminalWithLocation(terminalId) {
    try {
      const terminal = this.terminals.get(terminalId);
      if (!terminal) {
        return {
          success: false,
          error: 'Terminal not found',
        };
      }

      return {
        success: true,
        terminal: {
          ...terminal,
          location: {
            id: terminal.locationId,
            name: terminal.locationName,
          },
        },
      };
    } catch (error) {
      console.error('Get terminal with location error:', error);
      return {
        success: false,
        error: 'Failed to get terminal with location',
      };
    }
  }

  // Auto-detect terminal based on device characteristics
  async autoDetectTerminal(deviceInfo) {
    try {
      // Simple device detection logic
      let terminalType = 'mobile';
      let capabilities = ['sales', 'inventory_check'];

      // Detect based on device characteristics
      if (deviceInfo.platform === 'web') {
        terminalType = 'main_counter';
        capabilities = ['sales', 'returns', 'inventory_check', 'customer_management'];
      } else if (deviceInfo.isTablet) {
        terminalType = 'tablet';
        capabilities = ['sales', 'returns', 'inventory_check'];
      }

      const detectedDeviceInfo = {
        ...deviceInfo,
        type: terminalType,
        capabilities,
        name: `${terminalType.replace('_', ' ').toUpperCase()} - ${deviceInfo.deviceId}`,
      };

      return this.getOrCreateTerminal(detectedDeviceInfo);
    } catch (error) {
      console.error('Auto-detect terminal error:', error);
      return {
        success: false,
        error: 'Failed to auto-detect terminal',
      };
    }
  }
}

module.exports = new TerminalManagementService();
