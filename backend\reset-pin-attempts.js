#!/usr/bin/env node

/**
 * Reset PIN attempts for testing
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function resetPinAttempts() {
  console.log('🔄 Resetting PIN attempts for testing...');

  try {
    // Create database connection
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: process.env.DB_CHARSET || 'utf8mb4'
    });

    // Reset PIN attempts for all users
    const [result] = await connection.execute(`
      UPDATE pos_staff 
      SET pin_attempts = 0, pin_locked_until = NULL 
      WHERE pin_attempts > 0 OR pin_locked_until IS NOT NULL
    `);

    console.log(`✅ Reset PIN attempts for ${result.affectedRows} users`);

    // Show current status
    const [users] = await connection.execute(`
      SELECT id, username, name, pin_attempts, pin_locked_until 
      FROM pos_staff 
      WHERE pin IS NOT NULL
      ORDER BY id
    `);

    console.log('\n📊 Current PIN status:');
    users.forEach(user => {
      const locked = user.pin_locked_until ? 'LOCKED' : 'UNLOCKED';
      console.log(`   ${user.username} (${user.id}): ${user.pin_attempts} attempts, ${locked}`);
    });

    await connection.end();

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

resetPinAttempts().then(() => {
  console.log('\n✅ PIN reset completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Reset failed:', error);
  process.exit(1);
});
