/**
 * TicketOptionsModal Component
 *
 * Modal for managing individual ticket options including rename,
 * duplicate, pause/resume, and delete actions.
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import { useAppDispatch } from "@/src/store";
import {
  Ticket,
  updateTicketName,
  updateTicketStatus,
  duplicateTicket,
} from "@/src/store/slices/ticketSlice";
import { formatCurrency } from "@/src/utils/currencyUtils";
import {
  createTicketStyles,
  getTicketModalStyles,
} from "@/src/design-system/TicketDesignSystem";
import React, { useState } from "react";
import {
  Alert,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { IconSymbol } from "./IconSymbol";
import { ModernButton } from "./ModernButton";

interface TicketOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  ticket: Ticket | null;
  onDeleteTicket: (ticketId: string) => void;
  onDuplicateTicket?: (ticketId: string) => void;
}

export function TicketOptionsModal({
  visible,
  onClose,
  ticket,
  onDeleteTicket,
  onDuplicateTicket,
}: TicketOptionsModalProps) {
  const theme = useTheme();
  const dispatch = useAppDispatch();

  const [isRenaming, setIsRenaming] = useState(false);
  const [newName, setNewName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const styles = createTicketStyles(theme);
  const modalStyles = getTicketModalStyles(theme);

  if (!ticket) return null;

  const ticketItemCount = ticket.items.reduce(
    (total, item) => total + item.quantity,
    0
  );
  const hasItems = ticket.items.length > 0;

  const handleStartRename = () => {
    setNewName(ticket.name || `Ticket ${ticket.id.slice(-4)}`);
    setIsRenaming(true);
  };

  const handleSaveRename = () => {
    const trimmedName = newName.trim();
    if (trimmedName.length < 2) {
      Alert.alert(
        "Invalid Name",
        "Ticket name must be at least 2 characters long."
      );
      return;
    }

    dispatch(updateTicketName({ ticketId: ticket.id, name: trimmedName }));
    setIsRenaming(false);
    onClose();
  };

  const handleCancelRename = () => {
    setIsRenaming(false);
    setNewName("");
  };

  const handleDuplicateTicket = () => {
    if (!ticket) return;

    setIsProcessing(true);
    try {
      dispatch(
        duplicateTicket({
          originalTicketId: ticket.id,
          newName: `${ticket.name} (Copy)`,
        })
      );

      if (onDuplicateTicket) {
        onDuplicateTicket(ticket.id);
      }

      onClose();
    } catch (error) {
      Alert.alert("Error", "Failed to duplicate ticket. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePauseTicket = () => {
    if (!ticket) return;

    const newStatus = ticket.status === "active" ? "paused" : "active";

    setIsProcessing(true);
    try {
      dispatch(
        updateTicketStatus({
          ticketId: ticket.id,
          status: newStatus,
        })
      );
      onClose();
    } catch (error) {
      Alert.alert("Error", "Failed to update ticket status. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteTicket = () => {
    if (hasItems) {
      Alert.alert(
        "Delete Ticket",
        `"${ticket.name || "Unnamed Ticket"}" contains ${ticketItemCount} item${
          ticketItemCount === 1 ? "" : "s"
        }. Are you sure you want to delete it? This action cannot be undone.`,
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Delete",
            style: "destructive",
            onPress: () => {
              onDeleteTicket(ticket.id);
            },
          },
        ]
      );
    } else {
      Alert.alert(
        "Delete Empty Ticket",
        `Delete "${ticket.name || "Unnamed Ticket"}"?`,
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Delete",
            style: "destructive",
            onPress: () => {
              onDeleteTicket(ticket.id);
            },
          },
        ]
      );
    }
  };

  const handleClose = () => {
    if (isRenaming) {
      handleCancelRename();
    } else {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={modalStyles.overlay}>
        <View style={modalStyles.container}>
          {/* Header */}
          <View style={modalStyles.header}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "flex-start",
                flex: 1,
              }}
            >
              <IconSymbol
                name="ticket"
                size={24}
                color={theme.colors.primary}
                style={{ marginRight: theme.spacing.md, marginTop: 2 }}
              />
              <View style={{ flex: 1 }}>
                {isRenaming ? (
                  <TextInput
                    style={{
                      fontSize: 18,
                      fontWeight: "600",
                      marginBottom: 4,
                      borderWidth: 1,
                      borderColor: theme.colors.border,
                      backgroundColor: theme.colors.surface,
                      color: theme.colors.text,
                      borderRadius: theme.borderRadius.md,
                      paddingHorizontal: theme.spacing.md,
                      paddingVertical: theme.spacing.sm,
                    }}
                    value={newName}
                    onChangeText={setNewName}
                    placeholder="Enter ticket name..."
                    placeholderTextColor={theme.colors.textSecondary}
                    maxLength={50}
                    autoFocus
                    selectTextOnFocus
                    returnKeyType="done"
                    onSubmitEditing={handleSaveRename}
                  />
                ) : (
                  <Text
                    style={[
                      styles.text.title,
                      { fontSize: 18, marginBottom: 4 },
                    ]}
                  >
                    {ticket.name || `Ticket ${ticket.id.slice(-4)}`}
                  </Text>
                )}
                <Text style={[styles.text.subtitle, { fontSize: 14 }]}>
                  {formatCurrency(ticket.total)} • {ticketItemCount} item
                  {ticketItemCount === 1 ? "" : "s"}
                </Text>
              </View>
            </View>

            {!isRenaming && (
              <TouchableOpacity
                onPress={onClose}
                style={{
                  padding: theme.spacing.sm,
                  marginLeft: theme.spacing.md,
                }}
              >
                <IconSymbol
                  name="xmark"
                  size={20}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* Content */}
          <View style={modalStyles.content}>
            {/* Ticket Status */}
            <View style={{ marginBottom: theme.spacing.xl }}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  alignSelf: "flex-start",
                  paddingHorizontal: theme.spacing.md,
                  paddingVertical: theme.spacing.sm,
                  borderRadius: theme.borderRadius.lg,
                  marginBottom: theme.spacing.sm,
                  gap: theme.spacing.xs,
                  backgroundColor:
                    ticket.status === "active"
                      ? theme.colors.success + "20"
                      : theme.colors.warning + "20",
                }}
              >
                <IconSymbol
                  name={
                    ticket.status === "active"
                      ? "checkmark.circle"
                      : "pause.circle"
                  }
                  size={16}
                  color={
                    ticket.status === "active"
                      ? theme.colors.success
                      : theme.colors.warning
                  }
                />
                <Text
                  style={[
                    styles.text.status,
                    {
                      color:
                        ticket.status === "active"
                          ? theme.colors.success
                          : theme.colors.warning,
                    },
                  ]}
                >
                  {ticket.status.charAt(0).toUpperCase() +
                    ticket.status.slice(1)}
                </Text>
              </View>

              <Text style={[styles.text.timestamp, { fontSize: 12 }]}>
                Updated {new Date(ticket.updatedAt).toLocaleString()}
              </Text>
            </View>

            {/* Actions */}
            {isRenaming ? (
              <View style={{ flexDirection: "row", gap: theme.spacing.md }}>
                <ModernButton
                  title="Cancel"
                  onPress={handleCancelRename}
                  variant="outline"
                  style={{ flex: 1 }}
                />
                <ModernButton
                  title="Save"
                  onPress={handleSaveRename}
                  style={{ flex: 1 }}
                  disabled={newName.trim().length < 2}
                />
              </View>
            ) : (
              <View style={{ gap: theme.spacing.lg }}>
                {/* Primary Actions */}
                <View style={{ flexDirection: "row", gap: theme.spacing.md }}>
                  <TouchableOpacity
                    style={[
                      styles.buttons.secondary,
                      {
                        flex: 1,
                        alignItems: "center",
                        padding: theme.spacing.lg,
                        gap: theme.spacing.sm,
                      },
                    ]}
                    onPress={handleStartRename}
                    disabled={isProcessing}
                  >
                    <IconSymbol
                      name="pencil"
                      size={20}
                      color={theme.colors.primary}
                    />
                    <Text
                      style={[
                        styles.text.subtitle,
                        { color: theme.colors.text },
                      ]}
                    >
                      Rename
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.buttons.secondary,
                      {
                        flex: 1,
                        alignItems: "center",
                        padding: theme.spacing.lg,
                        gap: theme.spacing.sm,
                      },
                    ]}
                    onPress={handleDuplicateTicket}
                    disabled={isProcessing}
                  >
                    <IconSymbol
                      name="doc.on.doc"
                      size={20}
                      color={theme.colors.primary}
                    />
                    <Text
                      style={[
                        styles.text.subtitle,
                        { color: theme.colors.text },
                      ]}
                    >
                      Duplicate
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.buttons.secondary,
                      {
                        flex: 1,
                        alignItems: "center",
                        padding: theme.spacing.lg,
                        gap: theme.spacing.sm,
                      },
                    ]}
                    onPress={handlePauseTicket}
                    disabled={isProcessing}
                  >
                    <IconSymbol
                      name={ticket.status === "active" ? "pause" : "play"}
                      size={20}
                      color={theme.colors.warning}
                    />
                    <Text
                      style={[
                        styles.text.subtitle,
                        { color: theme.colors.text },
                      ]}
                    >
                      {ticket.status === "active" ? "Pause" : "Resume"}
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* Danger Zone */}
                <View>
                  <Text
                    style={[
                      styles.text.subtitle,
                      { marginBottom: theme.spacing.md },
                    ]}
                  >
                    Danger Zone
                  </Text>
                  <TouchableOpacity
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: theme.spacing.lg,
                      borderRadius: theme.borderRadius.lg,
                      backgroundColor: theme.colors.error + "10",
                      borderWidth: 1,
                      borderColor: theme.colors.error + "30",
                      gap: theme.spacing.sm,
                    }}
                    onPress={handleDeleteTicket}
                    disabled={isProcessing}
                  >
                    <IconSymbol
                      name="trash"
                      size={20}
                      color={theme.colors.error}
                    />
                    <Text
                      style={[
                        styles.text.subtitle,
                        { color: theme.colors.error },
                      ]}
                    >
                      Delete Ticket
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}
