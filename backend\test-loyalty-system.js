/**
 * Loyalty System Diagnostic Test
 * 
 * Tests the complete loyalty system flow to identify issues
 */

require('dotenv').config();
const mysql = require('mysql2/promise');
const loyaltyService = require('./src/services/loyalty-service');

class LoyaltySystemDiagnostic {
  constructor() {
    this.pool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'dukalink',
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || 'dukalink_pos',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      charset: 'utf8mb4',
    });
  }

  async runDiagnostics() {
    console.log('🔍 Starting Loyalty System Diagnostics...\n');

    try {
      // Test 1: Database Connection
      await this.testDatabaseConnection();

      // Test 2: Check Tables Exist
      await this.checkTablesExist();

      // Test 3: Check Sample Data
      await this.checkSampleData();

      // Test 4: Test Loyalty Service Methods
      await this.testLoyaltyServiceMethods();

      // Test 5: Test API Endpoints (if server is running)
      await this.testAPIEndpoints();

      console.log('\n✅ Loyalty System Diagnostics Complete!');

    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
    } finally {
      await this.pool.end();
    }
  }

  async testDatabaseConnection() {
    console.log('1️⃣ Testing Database Connection...');
    
    try {
      const connection = await this.pool.getConnection();
      console.log('   ✅ Database connection successful');
      connection.release();
    } catch (error) {
      console.error('   ❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async checkTablesExist() {
    console.log('\n2️⃣ Checking Required Tables...');

    const requiredTables = [
      'customer_loyalty',
      'loyalty_transactions', 
      'loyalty_redemptions'
    ];

    for (const table of requiredTables) {
      try {
        const [rows] = await this.pool.execute(`SHOW TABLES LIKE '${table}'`);
        if (rows.length > 0) {
          console.log(`   ✅ Table '${table}' exists`);
          
          // Check table structure
          const [columns] = await this.pool.execute(`DESCRIBE ${table}`);
          console.log(`      - ${columns.length} columns defined`);
        } else {
          console.error(`   ❌ Table '${table}' missing`);
        }
      } catch (error) {
        console.error(`   ❌ Error checking table '${table}':`, error.message);
      }
    }
  }

  async checkSampleData() {
    console.log('\n3️⃣ Checking Sample Data...');

    try {
      // Check customer loyalty records
      const [loyaltyRows] = await this.pool.execute(
        'SELECT COUNT(*) as count FROM customer_loyalty'
      );
      console.log(`   📊 Customer loyalty records: ${loyaltyRows[0].count}`);

      // Check loyalty transactions
      const [transRows] = await this.pool.execute(
        'SELECT COUNT(*) as count FROM loyalty_transactions'
      );
      console.log(`   📊 Loyalty transactions: ${transRows[0].count}`);

      // Check loyalty redemptions
      const [redemptionRows] = await this.pool.execute(
        'SELECT COUNT(*) as count FROM loyalty_redemptions'
      );
      console.log(`   📊 Loyalty redemptions: ${redemptionRows[0].count}`);

      // Show sample customer data
      const [sampleCustomers] = await this.pool.execute(
        'SELECT shopify_customer_id, loyalty_points, loyalty_tier, total_purchases FROM customer_loyalty LIMIT 3'
      );
      
      if (sampleCustomers.length > 0) {
        console.log('   📋 Sample customer loyalty data:');
        sampleCustomers.forEach(customer => {
          console.log(`      - Customer ${customer.shopify_customer_id}: ${customer.loyalty_points} points (${customer.loyalty_tier} tier, KSh ${customer.total_purchases} purchases)`);
        });
      } else {
        console.log('   ⚠️ No sample customer loyalty data found');
      }

    } catch (error) {
      console.error('   ❌ Error checking sample data:', error.message);
    }
  }

  async testLoyaltyServiceMethods() {
    console.log('\n4️⃣ Testing Loyalty Service Methods...');

    try {
      // Test 1: Get customer loyalty (should create if not exists)
      console.log('   🧪 Testing getCustomerLoyalty...');
      const testCustomerId = '8095960301705'; // Sample customer ID
      const loyaltyResult = await loyaltyService.getCustomerLoyalty(testCustomerId);
      
      if (loyaltyResult.success) {
        console.log('   ✅ getCustomerLoyalty working');
        console.log(`      - Customer: ${testCustomerId}`);
        console.log(`      - Points: ${loyaltyResult.loyalty.loyalty_points}`);
        console.log(`      - Tier: ${loyaltyResult.loyalty.loyalty_tier}`);
      } else {
        console.error('   ❌ getCustomerLoyalty failed:', loyaltyResult.error);
      }

      // Test 2: Calculate loyalty discount
      console.log('   🧪 Testing calculateLoyaltyDiscount...');
      const discountResult = await loyaltyService.calculateLoyaltyDiscount(testCustomerId, 1000);
      
      if (discountResult.success) {
        console.log('   ✅ calculateLoyaltyDiscount working');
        console.log(`      - Tier discount: KSh ${discountResult.discounts.tier.amount} (${discountResult.discounts.tier.percentage}%)`);
        console.log(`      - Available points: ${discountResult.discounts.points.availablePoints}`);
        console.log(`      - Max points discount: KSh ${discountResult.discounts.points.maxDiscount}`);
      } else {
        console.error('   ❌ calculateLoyaltyDiscount failed:', discountResult.error);
      }

      // Test 3: Get customer summary
      console.log('   🧪 Testing getCustomerSummary...');
      const summaryResult = await loyaltyService.getCustomerSummary(testCustomerId);
      
      if (summaryResult.success) {
        console.log('   ✅ getCustomerSummary working');
        console.log(`      - Total purchases: KSh ${summaryResult.summary.totalPurchases}`);
        console.log(`      - Total orders: ${summaryResult.summary.totalOrders}`);
        console.log(`      - Available discount: KSh ${summaryResult.summary.redemptionInfo.availableDiscount}`);
      } else {
        console.error('   ❌ getCustomerSummary failed:', summaryResult.error);
      }

    } catch (error) {
      console.error('   ❌ Error testing loyalty service methods:', error.message);
    }
  }

  async testAPIEndpoints() {
    console.log('\n5️⃣ Testing API Endpoints...');
    
    try {
      const axios = require('axios');
      const baseURL = process.env.API_BASE_URL || 'http://localhost:3001';
      
      // Test if server is running
      console.log('   🧪 Testing server connectivity...');
      try {
        const response = await axios.get(`${baseURL}/api/health`, { timeout: 5000 });
        console.log('   ✅ Server is running');
      } catch (error) {
        console.log('   ⚠️ Server not running or not accessible');
        console.log('      Skipping API endpoint tests');
        return;
      }

      // Test loyalty endpoints (would need authentication token)
      console.log('   ⚠️ API endpoint testing requires authentication');
      console.log('      Manual testing recommended using Postman or similar tool');
      
    } catch (error) {
      console.log('   ⚠️ API testing skipped:', error.message);
    }
  }

  async testLoyaltyFlow() {
    console.log('\n6️⃣ Testing Complete Loyalty Flow...');

    try {
      const testCustomerId = '8095960301705';
      const testOrderTotal = 500;
      const testOrderId = `TEST_ORDER_${Date.now()}`;

      console.log('   🧪 Testing points addition...');
      
      // Get initial state
      const initialState = await loyaltyService.getCustomerLoyalty(testCustomerId);
      const initialPoints = initialState.success ? initialState.loyalty.loyalty_points : 0;
      
      // Add points
      const addPointsResult = await loyaltyService.addPoints(
        testCustomerId,
        testOrderTotal,
        testOrderId,
        'test-staff-001'
      );

      if (addPointsResult.success) {
        console.log('   ✅ Points addition working');
        console.log(`      - Points added: ${addPointsResult.pointsAdded}`);
        console.log(`      - New balance: ${addPointsResult.newBalance}`);
        console.log(`      - Tier changed: ${addPointsResult.tierChanged}`);
        
        // Verify the points were actually added
        const finalState = await loyaltyService.getCustomerLoyalty(testCustomerId);
        if (finalState.success) {
          const finalPoints = finalState.loyalty.loyalty_points;
          const expectedPoints = initialPoints + addPointsResult.pointsAdded;
          
          if (finalPoints === expectedPoints) {
            console.log('   ✅ Points balance verification successful');
          } else {
            console.error(`   ❌ Points balance mismatch: expected ${expectedPoints}, got ${finalPoints}`);
          }
        }
      } else {
        console.error('   ❌ Points addition failed:', addPointsResult.error);
      }

    } catch (error) {
      console.error('   ❌ Error testing loyalty flow:', error.message);
    }
  }
}

// Run diagnostics
const diagnostic = new LoyaltySystemDiagnostic();
diagnostic.runDiagnostics()
  .then(() => {
    console.log('\n🎉 Diagnostics completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Diagnostics failed:', error);
    process.exit(1);
  });
