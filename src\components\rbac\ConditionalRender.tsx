import React from "react";
import { useSession } from "../../contexts/AuthContext";
import {
  canAccessScreen,
  canAccessFeature,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasRole,
  hasAnyRole,
  hasRoleLevel,
  UserRole,
} from "../../config/rbac";

interface ConditionalRenderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;

  // Permission-based conditions
  permission?: string;
  permissions?: string[];
  requireAllPermissions?: boolean;

  // Role-based conditions
  role?: UserRole;
  roles?: UserRole[];
  minRoleLevel?: UserRole;

  // Screen access conditions
  screenName?: string;

  // Feature access conditions
  featureId?: string;

  // Custom condition function
  condition?: (user: any) => boolean;

  // Logical operators
  operator?: "AND" | "OR"; // How to combine multiple conditions
}

/**
 * ConditionalRender Component
 *
 * A flexible component that can handle complex permission and role-based rendering logic.
 * Supports multiple conditions with AND/OR operators.
 *
 * @param children - Content to render if conditions are met
 * @param fallback - Content to render if conditions are not met
 * @param showFallback - Whether to show fallback content or nothing
 * @param permission - Single permission to check
 * @param permissions - Array of permissions to check
 * @param requireAllPermissions - If true, user must have ALL permissions
 * @param role - Single role to check
 * @param roles - Array of roles to check
 * @param minRoleLevel - Minimum role level required
 * @param screenName - Screen name to check access for
 * @param featureId - Feature ID to check access for
 * @param condition - Custom condition function
 * @param operator - How to combine multiple conditions (AND/OR)
 */
export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  children,
  fallback = null,
  showFallback = false,
  permission,
  permissions,
  requireAllPermissions = false,
  role,
  roles,
  minRoleLevel,
  screenName,
  featureId,
  condition,
  operator = "AND",
}) => {
  const { user, isPosAuthenticated } = useSession();

  // If not authenticated, don't show anything
  if (!isPosAuthenticated || !user) {
    return showFallback ? (fallback as React.ReactElement) : null;
  }

  const userRole = user.role as UserRole;
  const userPermissions = user.permissions || [];

  // Collect all condition results
  const conditionResults: boolean[] = [];

  // Permission checks
  if (permission) {
    conditionResults.push(hasPermission(userPermissions, permission));
  }

  if (permissions && permissions.length > 0) {
    if (requireAllPermissions) {
      conditionResults.push(hasAllPermissions(userPermissions, permissions));
    } else {
      conditionResults.push(hasAnyPermission(userPermissions, permissions));
    }
  }

  // Role checks
  if (role) {
    conditionResults.push(hasRole(userRole, role));
  }

  if (roles && roles.length > 0) {
    conditionResults.push(hasAnyRole(userRole, roles));
  }

  if (minRoleLevel) {
    conditionResults.push(hasRoleLevel(userRole, minRoleLevel));
  }

  // Screen access check
  if (screenName) {
    conditionResults.push(
      canAccessScreen(userRole, userPermissions, screenName)
    );
  }

  // Feature access check
  if (featureId) {
    conditionResults.push(
      canAccessFeature(userRole, userPermissions, featureId)
    );
  }

  // Custom condition check
  if (condition) {
    conditionResults.push(condition(user));
  }

  // If no conditions specified, allow access
  if (conditionResults.length === 0) {
    return children as React.ReactElement;
  }

  // Apply logical operator
  let hasAccess: boolean;
  if (operator === "AND") {
    hasAccess = conditionResults.every((result) => result);
  } else {
    hasAccess = conditionResults.some((result) => result);
  }

  if (hasAccess) {
    return children as React.ReactElement;
  }

  return showFallback ? (fallback as React.ReactElement) : null;
};

// Convenience hook for conditional logic in components
export const useConditionalAccess = () => {
  const { user, isPosAuthenticated } = useSession();

  const checkAccess = React.useCallback(
    (
      conditions: Omit<
        ConditionalRenderProps,
        "children" | "fallback" | "showFallback"
      >
    ) => {
      if (!isPosAuthenticated || !user) {
        return false;
      }

      const userRole = user.role as UserRole;
      const userPermissions = user.permissions || [];

      const {
        permission,
        permissions,
        requireAllPermissions = false,
        role,
        roles,
        minRoleLevel,
        screenName,
        featureId,
        condition,
        operator = "AND",
      } = conditions;

      const conditionResults: boolean[] = [];

      // Permission checks
      if (permission) {
        conditionResults.push(hasPermission(userPermissions, permission));
      }

      if (permissions && permissions.length > 0) {
        if (requireAllPermissions) {
          conditionResults.push(
            hasAllPermissions(userPermissions, permissions)
          );
        } else {
          conditionResults.push(hasAnyPermission(userPermissions, permissions));
        }
      }

      // Role checks
      if (role) {
        conditionResults.push(hasRole(userRole, role));
      }

      if (roles && roles.length > 0) {
        conditionResults.push(hasAnyRole(userRole, roles));
      }

      if (minRoleLevel) {
        conditionResults.push(hasRoleLevel(userRole, minRoleLevel));
      }

      // Screen access check
      if (screenName) {
        conditionResults.push(
          canAccessScreen(userRole, userPermissions, screenName)
        );
      }

      // Feature access check
      if (featureId) {
        conditionResults.push(
          canAccessFeature(userRole, userPermissions, featureId)
        );
      }

      // Custom condition check
      if (condition) {
        conditionResults.push(condition(user));
      }

      // If no conditions specified, allow access
      if (conditionResults.length === 0) {
        return true;
      }

      // Apply logical operator
      if (operator === "AND") {
        return conditionResults.every((result) => result);
      } else {
        return conditionResults.some((result) => result);
      }
    },
    [user, isPosAuthenticated]
  );

  return {
    checkAccess,
    user,
    isPosAuthenticated,
    hasPermission: (permission: string) => checkAccess({ permission }),
    hasRole: (role: UserRole) => checkAccess({ role }),
    hasMinRoleLevel: (minRoleLevel: UserRole) => checkAccess({ minRoleLevel }),
    canAccessScreen: (screenName: string) => checkAccess({ screenName }),
    canAccessFeature: (featureId: string) => checkAccess({ featureId }),
  };
};

// Higher-order component for complex conditional rendering
export const withConditionalAccess = <P extends object>(
  Component: React.ComponentType<P>,
  conditions: Omit<
    ConditionalRenderProps,
    "children" | "fallback" | "showFallback"
  >,
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <ConditionalRender {...conditions} fallback={fallback}>
      <Component {...props} />
    </ConditionalRender>
  );
};
