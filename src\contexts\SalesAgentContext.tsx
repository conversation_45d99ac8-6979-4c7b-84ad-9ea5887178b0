import AsyncStorage from "@react-native-async-storage/async-storage";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

interface SalesAgent {
  id: string;
  name: string;
  email: string;
  phone?: string;
  territory: string;
  region?: string;
  commissionRate: number;
  active: boolean;
  customerCount?: number;
  totalSales?: number;
  totalCommission?: number;
  joinDate?: string;
}

interface SalesAgentContextType {
  selectedAgent: SalesAgent | null;
  setSelectedAgent: (agent: SalesAgent | null) => void;
  clearSelectedAgent: () => void;
  isAgentSelected: boolean;
}

const SalesAgentContext = createContext<SalesAgentContextType | undefined>(
  undefined
);

const STORAGE_KEY = "@dukalink_selected_sales_agent";

interface SalesAgentProviderProps {
  children: ReactNode;
}

export const SalesAgentProvider: React.FC<SalesAgentProviderProps> = ({
  children,
}) => {
  const [selectedAgent, setSelectedAgentState] = useState<SalesAgent | null>(
    null
  );

  // Clear any previously selected agent on app start to prevent auto-selection
  // Users should explicitly select sales agents for each transaction
  useEffect(() => {
    const clearStoredAgent = async () => {
      try {
        setSelectedAgentState(null);
        await AsyncStorage.removeItem(STORAGE_KEY);
      } catch (error) {
        console.error("Failed to clear stored sales agent on startup:", error);
      }
    };
    clearStoredAgent();
  }, []);

  const loadSelectedAgent = async () => {
    try {
      const storedAgent = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedAgent) {
        setSelectedAgentState(JSON.parse(storedAgent));
      }
    } catch (error) {
      console.error("Failed to load selected sales agent:", error);
    }
  };

  const setSelectedAgent = async (agent: SalesAgent | null) => {
    try {
      setSelectedAgentState(agent);
      if (agent) {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(agent));
      } else {
        await AsyncStorage.removeItem(STORAGE_KEY);
      }
    } catch (error) {
      console.error("Failed to save selected sales agent:", error);
    }
  };

  const clearSelectedAgent = async () => {
    try {
      setSelectedAgentState(null);
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error("Failed to clear selected sales agent:", error);
    }
  };

  const value: SalesAgentContextType = {
    selectedAgent,
    setSelectedAgent,
    clearSelectedAgent,
    isAgentSelected: !!selectedAgent,
  };

  return (
    <SalesAgentContext.Provider value={value}>
      {children}
    </SalesAgentContext.Provider>
  );
};

export const useSalesAgent = (): SalesAgentContextType => {
  const context = useContext(SalesAgentContext);
  if (context === undefined) {
    throw new Error("useSalesAgent must be used within a SalesAgentProvider");
  }
  return context;
};

export default SalesAgentContext;
