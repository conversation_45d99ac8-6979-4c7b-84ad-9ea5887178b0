import { IconSymbol } from "@/src/components/ui/IconSymbol";
import { Spacing, Typography } from "@/src/constants/Styles";
import { useThemeColor } from "@/src/hooks/useThemeColor";
import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from "react-native";

interface LoadingSpinnerProps {
  size?: "small" | "large";
  color?: string;
  style?: ViewStyle;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "large",
  color,
  style,
}) => {
  const primaryColor = useThemeColor({}, "primary");

  return (
    <ActivityIndicator
      size={size}
      color={color || primaryColor}
      style={style}
    />
  );
};

interface LoadingOverlayProps {
  visible: boolean;
  message?: string;
  style?: ViewStyle;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  message = "Loading...",
  style,
}) => {
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const primaryColor = useThemeColor({}, "primary");

  if (!visible) return null;

  return (
    <View
      style={[
        styles.overlay,
        { backgroundColor: backgroundColor + "E6" },
        style,
      ]}
    >
      <View style={styles.overlayContent}>
        <LoadingSpinner size="large" color={primaryColor} />
        <Text style={[styles.overlayText, { color: textColor }]}>
          {message}
        </Text>
      </View>
    </View>
  );
};

interface FullScreenLoadingProps {
  message?: string;
  icon?: string;
  style?: ViewStyle;
}

export const FullScreenLoading: React.FC<FullScreenLoadingProps> = ({
  message = "Loading...",
  icon,
  style,
}) => {
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const primaryColor = useThemeColor({}, "primary");

  return (
    <View style={[styles.fullScreen, { backgroundColor }, style]}>
      {icon && (
        <IconSymbol
          name={icon}
          size={64}
          color={primaryColor}
          style={styles.loadingIcon}
        />
      )}
      <LoadingSpinner size="large" color={primaryColor} />
      <Text style={[styles.loadingText, { color: textColor }]}>{message}</Text>
    </View>
  );
};

interface InlineLoadingProps {
  message?: string;
  size?: "small" | "large";
  horizontal?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
  message = "Loading...",
  size = "small",
  horizontal = true,
  style,
  textStyle,
}) => {
  const textColor = useThemeColor({}, "text");
  const primaryColor = useThemeColor({}, "primary");

  return (
    <View
      style={[
        horizontal ? styles.inlineHorizontal : styles.inlineVertical,
        style,
      ]}
    >
      <LoadingSpinner size={size} color={primaryColor} />
      <Text style={[styles.inlineText, { color: textColor }, textStyle]}>
        {message}
      </Text>
    </View>
  );
};

interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = "100%",
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const surfaceColor = useThemeColor({}, "surface");

  return (
    <View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
          backgroundColor: surfaceColor,
        },
        style,
      ]}
    />
  );
};

interface SkeletonListProps {
  count?: number;
  itemHeight?: number;
  spacing?: number;
  style?: ViewStyle;
}

export const SkeletonList: React.FC<SkeletonListProps> = ({
  count = 5,
  itemHeight = 60,
  spacing = 12,
  style,
}) => {
  return (
    <View style={style}>
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} style={{ marginBottom: spacing }}>
          <View style={styles.skeletonItem}>
            <Skeleton width={50} height={50} borderRadius={25} />
            <View style={styles.skeletonContent}>
              <Skeleton width="70%" height={16} />
              <Skeleton width="50%" height={12} style={{ marginTop: 8 }} />
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

interface LoadingButtonProps {
  loading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  style?: ViewStyle;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  loading,
  children,
  loadingText,
  style,
}) => {
  if (loading) {
    return (
      <View style={[styles.loadingButton, style]}>
        <LoadingSpinner size="small" />
        {loadingText && (
          <Text style={styles.loadingButtonText}>{loadingText}</Text>
        )}
      </View>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 5000,
  },
  overlayContent: {
    alignItems: "center",
    padding: Spacing.xl,
  },
  overlayText: {
    ...Typography.body,
    marginTop: Spacing.md,
    textAlign: "center",
  },
  fullScreen: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: Spacing.xl,
  },
  loadingIcon: {
    marginBottom: Spacing.lg,
  },
  loadingText: {
    ...Typography.body,
    marginTop: Spacing.md,
    textAlign: "center",
  },
  inlineHorizontal: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  inlineVertical: {
    alignItems: "center",
    justifyContent: "center",
  },
  inlineText: {
    ...Typography.body,
    marginLeft: Spacing.sm,
  },
  skeleton: {
    opacity: 0.3,
  },
  skeletonItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  skeletonContent: {
    flex: 1,
    marginLeft: Spacing.md,
  },
  loadingButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  loadingButtonText: {
    ...Typography.body,
    marginLeft: Spacing.sm,
  },
});
