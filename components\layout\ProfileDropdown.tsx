import { IconSymbol } from "@/components/ui/IconSymbol";
import { UserSwitchingModal } from "@/components/ui/UserSwitchingModal";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useUserSwitching } from "@/src/contexts/UserSwitchingContext";
import { useRouter } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  Modal,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface ProfileDropdownProps {
  visible: boolean;
  onClose: () => void;
}

export function ProfileDropdown({ visible, onClose }: ProfileDropdownProps) {
  const { user, signOut, hasPermission } = useSession();
  const { sessionContext } = useUserSwitching();
  const router = useRouter();
  const theme = useTheme();

  const [showUserSwitchingModal, setShowUserSwitchingModal] = useState(false);

  // Theme colors with solid fallbacks
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const borderColor = useThemeColor({}, "border");
  const primaryColor = useThemeColor({}, "primary");

  // Animation values
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  // Animation effects
  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, scaleAnim, opacityAnim]);

  const handleThemeToggle = () => {
    theme.toggleTheme();
    onClose();
  };

  const handleDashboardPress = () => {
    onClose();
    router.replace("/(tabs)");
  };
  const handleSignOut = async () => {
    onClose();
    await signOut();
  };

  const menuItems = [
    {
      id: "dashboard",
      title: "Dashboard",
      icon: "house.fill",
      onPress: handleDashboardPress,
    },
    {
      id: "theme",
      title: theme.isDark ? "Switch to Light Mode" : "Switch to Dark Mode",
      icon: theme.isDark ? "sun.max" : "moon",
      onPress: handleThemeToggle,
    },
    {
      id: "profile",
      title: "Profile Settings",
      icon: "person.fill",
      onPress: () => {
        onClose();
        // TODO: Navigate to profile settings
      },
    },
    {
      id: "logout",
      title: "Sign Out",
      icon: "arrow.right.square",
      onPress: handleSignOut,
      destructive: true,
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      {/* Functional transparent backdrop */}
      <Pressable style={styles.overlay} onPress={onClose}>
        {/* Modal content container positioned at top-right */}
        <View style={styles.modalContainer}>
          <Animated.View
            style={[
              styles.dropdown,
              {
                backgroundColor: surfaceColor,
                borderColor: borderColor,
                opacity: opacityAnim,
                transform: [
                  {
                    scale: scaleAnim,
                  },
                ],
              },
            ]}
          >
            {/* User Info Header */}
            <View style={styles.userHeader}>
              <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
                <Text style={styles.avatarText}>
                  {user?.name?.charAt(0).toUpperCase() || "U"}
                </Text>
              </View>
              <View style={styles.userInfo}>
                <Text
                  style={[styles.userName, { color: textColor }]}
                  numberOfLines={1}
                >
                  {user?.name || "User"}
                </Text>
                <Text
                  style={[styles.userRole, { color: textSecondary }]}
                  numberOfLines={1}
                >
                  {user?.role || "Staff"}
                </Text>
              </View>
            </View>

            {/* Divider */}
            <View style={[styles.divider, { backgroundColor: borderColor }]} />

            {/* Menu Items */}
            {menuItems.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={styles.menuItem}
                onPress={item.onPress}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name={item.icon as any}
                  size={20}
                  color={item.destructive ? "#FF3B30" : textColor}
                />
                <Text
                  style={[
                    styles.menuText,
                    {
                      color: item.destructive ? "#FF3B30" : textColor,
                    },
                  ]}
                >
                  {item.title}
                </Text>
              </TouchableOpacity>
            ))}
          </Animated.View>
        </View>
      </Pressable>

      {/* User Switching Modal */}
      <UserSwitchingModal
        visible={showUserSwitchingModal}
        onClose={() => setShowUserSwitchingModal(false)}
        onSwitchComplete={(success: boolean) => {
          if (success) {
            setShowUserSwitchingModal(false);
          }
        }}
      />
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.3)", // Functional transparent backdrop
  },
  modalContainer: {
    flex: 1,
    justifyContent: "flex-start", // Align to top
    alignItems: "flex-end", // Align to right
    paddingTop: 70, // Below header
    paddingRight: 16, // From right edge
  },
  dropdown: {
    minWidth: 240,
    maxWidth: 280,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 12,
    maxHeight: "70%", // Prevent overflow
  },
  userHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: Spacing.lg,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: Spacing.md,
  },
  avatarText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    ...Typography.bodyLarge,
    fontWeight: "600",
    marginBottom: 2,
  },
  userRole: {
    ...Typography.caption,
  },
  divider: {
    height: 1,
    marginHorizontal: Spacing.lg,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  menuText: {
    ...Typography.body,
    marginLeft: Spacing.md,
    flex: 1,
  },
});
