/**
 * Debug Switch Back Issue
 */

const axios = require("axios");

async function debugSwitchBack() {
  try {
    console.log("🔐 Step 1: Login as admin...");
    const loginResponse = await axios.post("http://localhost:3020/api/pos/login", {
      username: "admin1",
      password: "admin123"
    });

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log("✅ Login successful");

    console.log("\n👥 Step 2: Get available staff...");
    const staffResponse = await axios.get("http://localhost:3020/api/pos/user-switching/available-staff", { headers });
    const targetStaff = staffResponse.data.data.staff[0]; // manager1
    console.log(`✅ Target staff: ${targetStaff.username} (${targetStaff.name})`);

    console.log("\n📋 Step 3: Check session context BEFORE switch...");
    const contextBefore = await axios.get("http://localhost:3020/api/pos/user-switching/session-context", { headers });
    console.log("Session context BEFORE switch:");
    console.log(`   Current user: ${contextBefore.data.data.sessionContext.currentUser.name}`);
    console.log(`   Last switch ID: ${contextBefore.data.data.sessionContext.lastSwitchId}`);
    console.log(`   User stack length: ${contextBefore.data.data.sessionContext.userStack.length}`);

    console.log("\n🔄 Step 4: Perform user switch...");
    const switchResponse = await axios.post("http://localhost:3020/api/pos/user-switching/switch-user", {
      targetStaffId: targetStaff.id,
      pin: "1234",
      reason: "debugging"
    }, { headers });

    if (switchResponse.data.success) {
      console.log("✅ User switch successful");
      console.log(`   Switched to: ${switchResponse.data.data.currentUser.name}`);
    } else {
      console.log("❌ User switch failed:", switchResponse.data.error);
      return;
    }

    console.log("\n📋 Step 5: Check session context AFTER switch...");
    const contextAfter = await axios.get("http://localhost:3020/api/pos/user-switching/session-context", { headers });
    console.log("Session context AFTER switch:");
    console.log(`   Current user: ${contextAfter.data.data.sessionContext.currentUser.name}`);
    console.log(`   Last switch ID: ${contextAfter.data.data.sessionContext.lastSwitchId}`);
    console.log(`   User stack length: ${contextAfter.data.data.sessionContext.userStack.length}`);
    console.log(`   Has active switch: ${contextAfter.data.data.hasActiveSwitch}`);
    console.log(`   Can switch back: ${contextAfter.data.data.canSwitchBack}`);

    if (contextAfter.data.data.sessionContext.lastSwitchId) {
      console.log("\n🔙 Step 6: Attempt switch back...");
      const switchBackResponse = await axios.post("http://localhost:3020/api/pos/user-switching/switch-back", {}, { headers });

      if (switchBackResponse.data.success) {
        console.log("✅ Switch back successful");
        console.log(`   Back to user: ${switchBackResponse.data.data.currentUser.name}`);
      } else {
        console.log("❌ Switch back failed:", switchBackResponse.data.error);
      }
    } else {
      console.log("❌ Cannot test switch back - lastSwitchId is null/undefined");
    }

  } catch (error) {
    console.error("❌ Debug failed:", error.response?.data?.error || error.message);
  }
}

debugSwitchBack();
