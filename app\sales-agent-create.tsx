import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  StyleSheet,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ScreenWrapper } from '@/components/layout/ScreenWrapper';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernButton } from '@/components/ui/ModernButton';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useNavigation } from '@/src/contexts/NavigationContext';
import { getAPIClient } from '@/src/services/api/dukalink-client';
import { useRBAC } from '@/src/hooks/useRBAC';

interface CreateAgentForm {
  name: string;
  email: string;
  phone: string;
  commissionRate: string;
  territory: string;
  region: string;
}

export default function SalesAgentCreateScreen() {
  const router = useRouter();
  const { setCurrentTitle } = useNavigation();
  const { canManageStaff } = useRBAC();
  
  const [form, setForm] = useState<CreateAgentForm>({
    name: '',
    email: '',
    phone: '',
    commissionRate: '5',
    territory: '',
    region: '',
  });
  const [loading, setLoading] = useState(false);

  // Theme colors
  const backgroundColor = useThemeColor({}, 'background');
  const surfaceColor = useThemeColor({}, 'surface');
  const textColor = useThemeColor({}, 'text');
  const textSecondary = useThemeColor({}, 'textSecondary');
  const primaryColor = useThemeColor({}, 'primary');

  React.useEffect(() => {
    setCurrentTitle('Create Sales Agent');
  }, [setCurrentTitle]);

  // Check permissions
  if (!canManageStaff) {
    return (
      <ScreenWrapper title="Create Sales Agent" showBackButton>
        <View style={styles.noPermissionContainer}>
          <Ionicons name="lock-closed" size={64} color={textSecondary} />
          <Text style={[styles.noPermissionText, { color: textSecondary }]}>
            You don't have permission to create sales agents
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  const validateForm = (): string | null => {
    if (!form.name.trim()) return 'Name is required';
    if (!form.email.trim()) return 'Email is required';
    if (!form.territory.trim()) return 'Territory is required';
    
    const commissionRate = parseFloat(form.commissionRate);
    if (isNaN(commissionRate) || commissionRate < 0 || commissionRate > 100) {
      return 'Commission rate must be between 0 and 100';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(form.email)) return 'Please enter a valid email address';

    // Phone validation (optional but if provided should be valid)
    if (form.phone && form.phone.length < 10) {
      return 'Please enter a valid phone number';
    }

    return null;
  };

  const handleCreateAgent = async () => {
    const validationError = validateForm();
    if (validationError) {
      Alert.alert('Validation Error', validationError);
      return;
    }

    setLoading(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.createSalesAgent({
        name: form.name.trim(),
        email: form.email.trim().toLowerCase(),
        phone: form.phone.trim(),
        commissionRate: parseFloat(form.commissionRate),
        territory: form.territory.trim(),
        region: form.region.trim() || undefined,
      });

      if (response.success) {
        Alert.alert('Success', 'Sales agent created successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        Alert.alert('Error', response.error || 'Failed to create sales agent');
      }
    } catch (error) {
      console.error('Create sales agent error:', error);
      Alert.alert('Error', 'Failed to create sales agent');
    } finally {
      setLoading(false);
    }
  };

  const styles = createStyles(backgroundColor, surfaceColor, textColor, textSecondary, primaryColor);

  return (
    <ScreenWrapper title="Create Sales Agent" showBackButton>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <ModernCard style={[styles.card, { backgroundColor: surfaceColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Basic Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>Full Name *</Text>
            <TextInput
              style={[styles.textInput, { borderColor: textSecondary + '40', color: textColor }]}
              value={form.name}
              onChangeText={(text) => setForm(prev => ({ ...prev, name: text }))}
              placeholder="Enter full name"
              placeholderTextColor={textSecondary}
              autoCapitalize="words"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>Email Address *</Text>
            <TextInput
              style={[styles.textInput, { borderColor: textSecondary + '40', color: textColor }]}
              value={form.email}
              onChangeText={(text) => setForm(prev => ({ ...prev, email: text }))}
              placeholder="Enter email address"
              placeholderTextColor={textSecondary}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>Phone Number</Text>
            <TextInput
              style={[styles.textInput, { borderColor: textSecondary + '40', color: textColor }]}
              value={form.phone}
              onChangeText={(text) => setForm(prev => ({ ...prev, phone: text }))}
              placeholder="Enter phone number"
              placeholderTextColor={textSecondary}
              keyboardType="phone-pad"
            />
          </View>
        </ModernCard>

        {/* Territory & Commission */}
        <ModernCard style={[styles.card, { backgroundColor: surfaceColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Territory & Commission</Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>Territory *</Text>
            <TextInput
              style={[styles.textInput, { borderColor: textSecondary + '40', color: textColor }]}
              value={form.territory}
              onChangeText={(text) => setForm(prev => ({ ...prev, territory: text }))}
              placeholder="e.g., Nairobi Central, Mombasa North"
              placeholderTextColor={textSecondary}
              autoCapitalize="words"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>Region</Text>
            <TextInput
              style={[styles.textInput, { borderColor: textSecondary + '40', color: textColor }]}
              value={form.region}
              onChangeText={(text) => setForm(prev => ({ ...prev, region: text }))}
              placeholder="e.g., Central Kenya, Coast"
              placeholderTextColor={textSecondary}
              autoCapitalize="words"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>Commission Rate (%) *</Text>
            <View style={styles.commissionInputContainer}>
              <TextInput
                style={[styles.commissionInput, { 
                  borderColor: textSecondary + '40', 
                  color: textColor 
                }]}
                value={form.commissionRate}
                onChangeText={(text) => setForm(prev => ({ ...prev, commissionRate: text }))}
                placeholder="5"
                placeholderTextColor={textSecondary}
                keyboardType="numeric"
                maxLength={5}
              />
              <Text style={[styles.percentSymbol, { color: textSecondary }]}>%</Text>
            </View>
            <Text style={[styles.inputHint, { color: textSecondary }]}>
              Recommended: 3-8% for retail sales
            </Text>
          </View>
        </ModernCard>

        {/* Preview Card */}
        <ModernCard style={[styles.previewCard, { backgroundColor: surfaceColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Preview</Text>
          
          <View style={styles.previewContent}>
            <View style={[styles.previewAvatar, { backgroundColor: primaryColor }]}>
              <Text style={styles.previewAvatarText}>
                {form.name.split(' ').map(n => n.charAt(0)).join('').slice(0, 2) || 'SA'}
              </Text>
            </View>
            
            <View style={styles.previewInfo}>
              <Text style={[styles.previewName, { color: textColor }]}>
                {form.name || 'Sales Agent Name'}
              </Text>
              <Text style={[styles.previewEmail, { color: textSecondary }]}>
                {form.email || '<EMAIL>'}
              </Text>
              {form.phone && (
                <Text style={[styles.previewPhone, { color: textSecondary }]}>
                  {form.phone}
                </Text>
              )}
              <Text style={[styles.previewTerritory, { color: textSecondary }]}>
                {form.territory || 'Territory'} {form.region && `• ${form.region}`}
              </Text>
              <Text style={[styles.previewCommission, { color: primaryColor }]}>
                {form.commissionRate || '0'}% Commission
              </Text>
            </View>
          </View>
        </ModernCard>

        {/* Create Button */}
        <View style={styles.buttonContainer}>
          <ModernButton
            title={loading ? "Creating..." : "Create Sales Agent"}
            onPress={handleCreateAgent}
            disabled={loading}
            style={styles.createButton}
          />
        </View>
      </ScrollView>
    </ScreenWrapper>
  );
}

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string,
  primaryColor: string
) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor,
  },
  noPermissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  noPermissionText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  card: {
    margin: 20,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  commissionInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commissionInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    marginRight: 8,
  },
  percentSymbol: {
    fontSize: 16,
    fontWeight: '500',
  },
  inputHint: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  previewCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 20,
  },
  previewContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  previewAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  previewAvatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  previewEmail: {
    fontSize: 14,
    marginBottom: 2,
  },
  previewPhone: {
    fontSize: 12,
    marginBottom: 2,
  },
  previewTerritory: {
    fontSize: 12,
    marginBottom: 4,
  },
  previewCommission: {
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    padding: 20,
  },
  createButton: {
    marginTop: 20,
  },
});
