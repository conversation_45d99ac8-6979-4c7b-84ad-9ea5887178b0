/**
 * Test Script: Backend Loyalty Points Fix
 *
 * This script tests the new automatic loyalty point addition in the backend
 * when orders are completed through the payment transaction service.
 */

const axios = require("axios");

// Configuration
const API_BASE_URL = "http://localhost:3020/api";
const TEST_CUSTOMER_ID = "7988938735753"; // Use existing customer
const TEST_STAFF_ID = "staff-001";

// Create API client with authentication
const createAuthenticatedClient = async () => {
  try {
    // Login to get token
    const loginResponse = await axios.post(`${API_BASE_URL}/pos/login`, {
      username: "admin1",
      password: "admin123",
    });

    if (!loginResponse.data.success) {
      throw new Error("Login failed: " + loginResponse.data.error);
    }

    const token = loginResponse.data.data.token;
    console.log("✅ Authentication successful");

    // Create authenticated client
    return axios.create({
      baseURL: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("❌ Authentication failed:", error.message);
    throw error;
  }
};

// Test the complete flow
const testLoyaltyBackendFix = async () => {
  console.log("🧪 Testing Backend Loyalty Points Fix\n");

  try {
    const apiClient = await createAuthenticatedClient();

    // Step 1: Get customer's current loyalty points
    console.log("1️⃣ Getting customer current loyalty points...");
    const loyaltyBefore = await apiClient.get(
      `/loyalty/customers/${TEST_CUSTOMER_ID}/summary`
    );
    const pointsBefore = loyaltyBefore.data.data.loyaltyPoints;
    console.log(`   Current points: ${pointsBefore}`);

    // Step 2: Create a test payment transaction
    console.log("\n2️⃣ Creating test payment transaction...");
    const transactionData = {
      customerId: TEST_CUSTOMER_ID,
      totalAmount: 500, // 500 KSh order
      currency: "KES",
      terminalId: "TERMINAL_001",
      locationId: "location-001",
      metadata: {
        testOrder: true,
        items: [
          {
            productId: "test-product-1",
            variantId: "test-variant-1",
            quantity: 1,
            price: 500,
            title: "Test Product for Loyalty",
          },
        ],
      },
    };

    const transactionResponse = await apiClient.post(
      "/payments/initiate",
      transactionData
    );
    const transactionId = transactionResponse.data.data.transactionId;
    console.log(`   Transaction created: ${transactionId}`);

    // Step 3: Add a payment method
    console.log("\n3️⃣ Adding payment method...");
    const paymentMethodData = {
      methodType: "cash",
      methodName: "Cash Payment",
      amount: 500,
      metadata: {},
    };

    const methodResponse = await apiClient.post(
      `/payments/${transactionId}/methods`,
      paymentMethodData
    );
    const methodId = methodResponse.data.data.methodId;
    console.log("   Payment method added");

    // Step 3.5: Process the payment method
    console.log("\n3️⃣.5 Processing payment method...");
    await apiClient.post(`/payments/methods/${methodId}/process`, {
      confirmed: true,
      amountTendered: 500,
    });
    console.log("   Payment method processed");

    // Step 4: Complete transaction with Shopify (this should now add loyalty points automatically)
    console.log(
      "\n4️⃣ Completing transaction with Shopify (testing automatic loyalty processing)..."
    );
    const orderData = {
      customer: {
        id: TEST_CUSTOMER_ID,
      },
      salesAgentId: "agent-001",
      line_items: [
        {
          title: "Test Product for Loyalty",
          quantity: 1,
          price: "500.00",
          sku: "TEST-SKU-001",
          variant_id: null,
          product_id: null,
          taxable: true,
          requires_shipping: false,
        },
      ],
      financial_status: "paid",
      fulfillment_status: null,
      total_price: "500.00",
      subtotal_price: "500.00",
      total_tax: "0.00",
      currency: "KES",
    };

    const completionResponse = await apiClient.post(
      `/payments/${transactionId}/complete-with-shopify`,
      { orderData }
    );

    if (completionResponse.data.success) {
      console.log("   ✅ Transaction completed successfully");
      console.log(
        "   📊 Shopify Order ID:",
        completionResponse.data.data.shopifyOrderId
      );

      // Check if loyalty was processed automatically
      if (completionResponse.data.data.loyaltyResult) {
        const loyaltyResult = completionResponse.data.data.loyaltyResult;
        console.log("   🎉 LOYALTY PROCESSED AUTOMATICALLY BY BACKEND!");
        console.log(`   📈 Points added: ${loyaltyResult.pointsAdded}`);
        console.log(`   💰 New balance: ${loyaltyResult.newBalance}`);
        console.log(`   🏆 Tier changed: ${loyaltyResult.tierChanged}`);
        console.log(
          "   🔍 Full loyalty result:",
          JSON.stringify(loyaltyResult, null, 2)
        );
      } else {
        console.log("   ⚠️ No loyalty result in response - check backend logs");
      }
    } else {
      console.log(
        "   ❌ Transaction completion failed:",
        completionResponse.data.error
      );
    }

    // Step 5: Verify loyalty points were actually added
    console.log("\n5️⃣ Verifying loyalty points were added...");
    const loyaltyAfter = await apiClient.get(
      `/loyalty/customers/${TEST_CUSTOMER_ID}/summary`
    );
    const pointsAfter = loyaltyAfter.data.data.loyaltyPoints;
    const pointsAdded = pointsAfter - pointsBefore;

    console.log(`   Points before: ${pointsBefore}`);
    console.log(`   Points after: ${pointsAfter}`);
    console.log(`   Points added: ${pointsAdded}`);

    if (pointsAdded > 0) {
      console.log("\n🎉 SUCCESS! Backend automatically added loyalty points!");
      console.log(
        "✅ The fix is working correctly - no more frontend API calls needed!"
      );

      // Test receipt generation to verify correct balance display
      if (completionResponse.data.data.loyaltyResult) {
        const loyaltyResult = completionResponse.data.data.loyaltyResult;
        console.log("\n📄 Testing Receipt Generation:");
        console.log(`   Points Added: ${loyaltyResult.pointsAdded}`);
        console.log(`   Total Balance: ${loyaltyResult.newBalance}`);
        console.log(
          `   ✅ Receipt should show: "Total TS Points: ${loyaltyResult.newBalance}"`
        );
        console.log(
          `   ❌ Receipt should NOT show: "Total TS Points: ${loyaltyResult.pointsAdded}"`
        );
      }
    } else {
      console.log("\n❌ FAILED! No loyalty points were added automatically");
      console.log("🔍 Check backend logs for errors in loyalty processing");
    }
  } catch (error) {
    console.error("\n❌ Test failed:", error.response?.data || error.message);

    if (error.response?.status === 401) {
      console.log(
        "💡 Tip: Make sure the backend server is running and credentials are correct"
      );
    }
  }
};

// Run the test
if (require.main === module) {
  testLoyaltyBackendFix()
    .then(() => {
      console.log("\n🏁 Test completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Test crashed:", error.message);
      process.exit(1);
    });
}

module.exports = { testLoyaltyBackendFix };
