/**
 * Test Environment Variables Loading
 */

require('dotenv').config();

console.log('🔧 Testing Environment Variables...\n');

console.log('Database Configuration:');
console.log('  DB_HOST:', process.env.DB_HOST);
console.log('  DB_USER:', process.env.DB_USER);
console.log('  DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'NOT SET');
console.log('  DB_NAME:', process.env.DB_NAME);
console.log('  MYSQL_ROOT_PASSWORD:', process.env.MYSQL_ROOT_PASSWORD ? '***' : 'NOT SET');

console.log('\nJWT Configuration:');
console.log('  JWT_SECRET:', process.env.JWT_SECRET ? '***' : 'NOT SET');

console.log('\nShopify Configuration:');
console.log('  SHOPIFY_API_KEY:', process.env.SHOPIFY_API_KEY ? '***' : 'NOT SET');
console.log('  SHOPIFY_API_SECRET:', process.env.SHOPIFY_API_SECRET ? '***' : 'NOT SET');

console.log('\n✅ Environment variables loaded successfully!');
