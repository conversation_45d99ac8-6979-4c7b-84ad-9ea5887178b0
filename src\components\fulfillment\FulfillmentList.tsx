/**
 * Fulfillment List Component
 *
 * Displays a list of fulfillments with filtering, sorting, and management capabilities.
 * Includes proper RBAC for different user roles and actions.
 */

import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { PermissionGate } from "@/src/components/auth/PermissionGate";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import { FulfillmentResponse } from "@/src/services/fulfillmentApiService";
import { useRBAC } from "@/src/hooks/useRBAC";
import { createStyleUtils } from "@/src/utils/themeUtils";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

interface FulfillmentListProps {
  fulfillments: FulfillmentResponse[];
  isLoading?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

export const FulfillmentList: React.FC<FulfillmentListProps> = ({
  fulfillments,
  isLoading = false,
  onRefresh,
  refreshing = false,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const router = useRouter();
  const { hasPermission } = useRBAC();

  // State management
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Create theme-aware styles
  const styles = createStyles(theme);

  // Check permissions
  const canManageFulfillments = hasPermission("manage_fulfillments");
  const canViewDetails = hasPermission("view_fulfillments");

  // Filter fulfillments based on status
  const filteredFulfillments = fulfillments.filter((fulfillment) => {
    if (statusFilter === "all") return true;
    return fulfillment.fulfillmentStatus === statusFilter;
  });

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "pending":
        return theme.colors.warning;
      case "processing":
        return theme.colors.info;
      case "shipped":
        return theme.colors.primary;
      case "delivered":
        return theme.colors.success;
      case "cancelled":
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case "pending":
        return "clock.fill";
      case "processing":
        return "gear.fill";
      case "shipped":
        return "truck.box.fill";
      case "delivered":
        return "checkmark.circle.fill";
      case "cancelled":
        return "xmark.circle.fill";
      default:
        return "questionmark.circle.fill";
    }
  };

  const handleFulfillmentPress = (fulfillment: FulfillmentResponse) => {
    if (canViewDetails) {
      router.push(`/fulfillment-details?fulfillmentId=${fulfillment.id}`);
    }
  };

  const renderFilterButtons = () => {
    const filters = [
      { key: "all", label: "All" },
      { key: "pending", label: "Pending" },
      { key: "processing", label: "Processing" },
      { key: "shipped", label: "Shipped" },
      { key: "delivered", label: "Delivered" },
    ];

    return (
      <View style={styles.filterContainer}>
        <ThemedText variant="bodyMedium" style={utils.mb("sm")}>
          Filter by Status:
        </ThemedText>
        <View style={styles.filterButtons}>
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterButton,
                statusFilter === filter.key && styles.activeFilterButton,
              ]}
              onPress={() => setStatusFilter(filter.key)}
            >
              <ThemedText
                variant="small"
                style={[
                  styles.filterButtonText,
                  statusFilter === filter.key && styles.activeFilterButtonText,
                ]}
              >
                {filter.label}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderFulfillmentItem = ({ item }: { item: FulfillmentResponse }) => (
    <ModernCard style={styles.fulfillmentCard}>
      <TouchableOpacity
        style={styles.fulfillmentContent}
        onPress={() => handleFulfillmentPress(item)}
        disabled={!canViewDetails}
      >
        {/* Header */}
        <View style={styles.fulfillmentHeader}>
          <View style={styles.fulfillmentInfo}>
            <ThemedText variant="bodyMedium" style={styles.fulfillmentId}>
              #{item.id.slice(-8)}
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              Order: {item.orderId || "N/A"}
            </ThemedText>
          </View>
          <View style={styles.statusContainer}>
            <IconSymbol
              name={getStatusIcon(item.fulfillmentStatus)}
              size={16}
              color={getStatusColor(item.fulfillmentStatus)}
            />
            <ThemedText
              variant="small"
              style={[
                styles.statusText,
                { color: getStatusColor(item.fulfillmentStatus) },
              ]}
            >
              {item.fulfillmentStatus?.toUpperCase() || "PENDING"}
            </ThemedText>
          </View>
        </View>

        {/* Details */}
        <View style={styles.fulfillmentDetails}>
          <View style={styles.detailRow}>
            <IconSymbol
              name="truck.box"
              size={14}
              color={theme.colors.textSecondary}
            />
            <ThemedText
              variant="small"
              color="secondary"
              style={utils.ml("xs")}
            >
              {item.deliveryMethod.charAt(0).toUpperCase() +
                item.deliveryMethod.slice(1)}
            </ThemedText>
          </View>

          <View style={styles.detailRow}>
            <IconSymbol
              name="dollarsign.circle"
              size={14}
              color={theme.colors.textSecondary}
            />
            <ThemedText
              variant="small"
              color="secondary"
              style={utils.ml("xs")}
            >
              KES {item.shippingFee.toLocaleString()}
            </ThemedText>
          </View>

          {item.trackingNumber && (
            <View style={styles.detailRow}>
              <IconSymbol
                name="location"
                size={14}
                color={theme.colors.textSecondary}
              />
              <ThemedText
                variant="small"
                color="secondary"
                style={utils.ml("xs")}
              >
                {item.trackingNumber}
              </ThemedText>
            </View>
          )}
        </View>

        {/* Staff Attribution */}
        {item.staffName && (
          <View style={styles.staffInfo}>
            <IconSymbol
              name="person.circle"
              size={14}
              color={theme.colors.textSecondary}
            />
            <ThemedText
              variant="small"
              color="secondary"
              style={utils.ml("xs")}
            >
              Managed by {item.staffName}
            </ThemedText>
          </View>
        )}

        {/* Timestamps */}
        <View style={styles.timestampContainer}>
          <ThemedText variant="small" color="muted">
            Created: {new Date(item.createdAt).toLocaleDateString()}
          </ThemedText>
          <ThemedText variant="small" color="muted">
            Updated: {new Date(item.updatedAt).toLocaleDateString()}
          </ThemedText>
        </View>

        {/* Action Indicator */}
        {canViewDetails && (
          <View style={styles.actionIndicator}>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={theme.colors.textSecondary}
            />
          </View>
        )}
      </TouchableOpacity>
    </ModernCard>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <IconSymbol
        name="shippingbox"
        size={48}
        color={theme.colors.textSecondary}
      />
      <ThemedText variant="h3" color="secondary" style={utils.mt("md")}>
        No Fulfillments Found
      </ThemedText>
      <ThemedText
        variant="body"
        color="muted"
        style={[utils.mt("sm"), styles.emptyText]}
      >
        {statusFilter === "all"
          ? "No fulfillments have been created yet."
          : `No fulfillments with status "${statusFilter}" found.`}
      </ThemedText>
    </View>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
      <ThemedText variant="body" style={utils.mt("md")}>
        Loading fulfillments...
      </ThemedText>
    </View>
  );

  if (isLoading) {
    return renderLoadingState();
  }

  return (
    <ThemedView style={styles.container}>
      {/* Filter Controls */}
      {renderFilterButtons()}

      {/* Results Summary */}
      <View style={styles.summaryContainer}>
        <ThemedText variant="small" color="secondary">
          Showing {filteredFulfillments.length} of {fulfillments.length}{" "}
          fulfillments
        </ThemedText>
      </View>

      {/* Fulfillment List */}
      <FlatList
        data={filteredFulfillments}
        renderItem={renderFulfillmentItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        onRefresh={onRefresh}
        refreshing={refreshing}
        ListEmptyComponent={renderEmptyState}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </ThemedView>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    filterContainer: {
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    filterButtons: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 8,
    },
    filterButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      backgroundColor: theme.colors.background,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    activeFilterButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    filterButtonText: {
      fontWeight: "500",
    },
    activeFilterButtonText: {
      color: "#FFFFFF",
    },
    summaryContainer: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.surface,
    },
    listContainer: {
      padding: 16,
    },
    fulfillmentCard: {
      marginBottom: 12,
    },
    fulfillmentContent: {
      padding: 16,
    },
    fulfillmentHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: 12,
    },
    fulfillmentInfo: {
      flex: 1,
    },
    fulfillmentId: {
      fontWeight: "600",
      marginBottom: 2,
    },
    statusContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: 4,
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    statusText: {
      fontSize: 11,
      fontWeight: "600",
    },
    fulfillmentDetails: {
      gap: 6,
      marginBottom: 12,
    },
    detailRow: {
      flexDirection: "row",
      alignItems: "center",
    },
    staffInfo: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.borderLight,
    },
    timestampContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.borderLight,
    },
    actionIndicator: {
      position: "absolute",
      right: 16,
      top: "50%",
      transform: [{ translateY: -8 }],
    },
    separator: {
      height: 8,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    emptyText: {
      textAlign: "center",
      maxWidth: 280,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
  });
