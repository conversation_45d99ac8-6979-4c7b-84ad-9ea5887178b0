/**
 * M-Pesa Integration Service for Backend Payment Processing
 *
 * Integrates the existing M-Pesa service with the payment transaction system
 * Handles STK Push initiation, status checking, and manual transaction code fallback
 */

const fetch = require("node-fetch");

class MpesaIntegrationService {
  constructor() {
    this.config = {
      consumerKey: process.env.MPESA_CONSUMER_KEY || "your_consumer_key",
      consumerSecret:
        process.env.MPESA_CONSUMER_SECRET || "your_consumer_secret",
      businessShortCode: process.env.MPESA_SHORTCODE || "174379",
      passkey: process.env.MPESA_PASSKEY || "your_passkey",
      environment: process.env.MPESA_ENVIRONMENT || "sandbox",
    };

    this.baseUrl =
      this.config.environment === "production"
        ? "https://api.safaricom.co.ke"
        : "https://sandbox.safaricom.co.ke";

    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Get OAuth access token from M-Pesa API
   */
  async getAccessToken() {
    // Check if we have a valid token
    if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
      return this.accessToken;
    }

    try {
      const credentials = Buffer.from(
        `${this.config.consumerKey}:${this.config.consumerSecret}`
      ).toString("base64");

      const response = await fetch(
        `${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
        {
          method: "GET",
          headers: {
            Authorization: `Basic ${credentials}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to get access token: ${response.statusText}`);
      }

      const data = await response.json();
      this.accessToken = data.access_token;

      // Set expiry time (usually 1 hour, but we'll refresh 5 minutes early)
      this.tokenExpiry = new Date(Date.now() + (data.expires_in - 300) * 1000);

      return this.accessToken;
    } catch (error) {
      console.error("Error getting M-Pesa access token:", error);
      throw new Error("Failed to authenticate with M-Pesa API");
    }
  }

  /**
   * Generate timestamp for M-Pesa API
   */
  generateTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hour = String(now.getHours()).padStart(2, "0");
    const minute = String(now.getMinutes()).padStart(2, "0");
    const second = String(now.getSeconds()).padStart(2, "0");

    return `${year}${month}${day}${hour}${minute}${second}`;
  }

  /**
   * Generate password for M-Pesa API
   */
  generatePassword(timestamp) {
    const data = `${this.config.businessShortCode}${this.config.passkey}${timestamp}`;
    return Buffer.from(data).toString("base64");
  }

  /**
   * Format phone number to M-Pesa format (254XXXXXXXXX)
   */
  formatPhoneNumber(phoneNumber) {
    // Remove any non-digit characters
    const cleanPhone = phoneNumber.replace(/\D/g, "");

    // Convert to M-Pesa format
    if (cleanPhone.startsWith("0")) {
      return "254" + cleanPhone.substring(1);
    } else if (cleanPhone.startsWith("254")) {
      return cleanPhone;
    } else if (cleanPhone.length === 9) {
      return "254" + cleanPhone;
    }

    throw new Error("Invalid phone number format");
  }

  /**
   * Initiate M-Pesa STK Push payment
   */
  async initiateSTKPush(paymentData) {
    try {
      const { phoneNumber, amount, accountReference, transactionDesc } =
        paymentData;

      const accessToken = await this.getAccessToken();
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);
      const formattedPhone = this.formatPhoneNumber(phoneNumber);

      const payload = {
        BusinessShortCode: this.config.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: "CustomerPayBillOnline",
        Amount: Math.round(amount), // M-Pesa requires integer amounts
        PartyA: formattedPhone,
        PartyB: this.config.businessShortCode,
        PhoneNumber: formattedPhone,
        CallBackURL:
          process.env.MPESA_CALLBACK_URL ||
          "https://your-callback-url.com/mpesa/callback",
        AccountReference: accountReference,
        TransactionDesc: transactionDesc,
      };

      const response = await fetch(
        `${this.baseUrl}/mpesa/stkpush/v1/processrequest`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (response.ok && data.ResponseCode === "0") {
        return {
          success: true,
          checkoutRequestId: data.CheckoutRequestID,
          merchantRequestId: data.MerchantRequestID,
          responseCode: data.ResponseCode,
          responseDescription: data.ResponseDescription,
          customerMessage: data.CustomerMessage,
          metadata: {
            phoneNumber: formattedPhone,
            amount: amount,
            timestamp: timestamp,
            method: "stk_push",
          },
        };
      } else {
        return {
          success: false,
          error:
            data.ResponseDescription ||
            data.errorMessage ||
            "STK Push initiation failed",
          metadata: {
            phoneNumber: formattedPhone,
            amount: amount,
            responseCode: data.ResponseCode,
          },
        };
      }
    } catch (error) {
      console.error("Error initiating M-Pesa STK Push:", error);
      return {
        success: false,
        error: "Failed to initiate STK Push. Please try again.",
        metadata: {
          error: error.message,
        },
      };
    }
  }

  /**
   * Check STK Push payment status
   */
  async checkSTKPushStatus(checkoutRequestId) {
    try {
      const accessToken = await this.getAccessToken();
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);

      const payload = {
        BusinessShortCode: this.config.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId,
      };

      const response = await fetch(
        `${this.baseUrl}/mpesa/stkpushquery/v1/query`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (response.ok) {
        return {
          success: data.ResultCode === "0",
          resultCode: data.ResultCode,
          resultDesc: data.ResultDesc,
          mpesaReceiptNumber: data.MpesaReceiptNumber,
          transactionDate: data.TransactionDate,
          phoneNumber: data.PhoneNumber,
          amount: data.Amount,
          metadata: {
            checkoutRequestId: checkoutRequestId,
            method: "stk_push_query",
          },
        };
      } else {
        return {
          success: false,
          error: data.ResponseDescription || "Failed to check payment status",
          metadata: {
            checkoutRequestId: checkoutRequestId,
          },
        };
      }
    } catch (error) {
      console.error("Error checking M-Pesa payment status:", error);
      return {
        success: false,
        error: "Failed to check payment status",
        metadata: {
          error: error.message,
        },
      };
    }
  }

  /**
   * Validate manual M-Pesa transaction code
   * This is a placeholder - in production you would validate against M-Pesa API
   */
  async validateManualTransaction(transactionCode, phoneNumber, amount) {
    try {
      // Basic validation - transaction code is now optional
      if (!transactionCode) {
        console.log(
          "⚠️ No transaction code provided, using placeholder for development"
        );
        transactionCode = `PENDING_MPESA_${Date.now()}`;
      } else if (transactionCode.length < 6) {
        return {
          success: false,
          error:
            "Invalid transaction code format. Transaction code must be at least 6 characters.",
        };
      }

      // More flexible validation for development - accept various formats
      const cleanCode = transactionCode.trim().toUpperCase();

      // Check if this is a placeholder code we generated
      const isPlaceholderCode = cleanCode.startsWith("PENDING_MPESA_");

      if (!isPlaceholderCode) {
        // Only validate format for user-provided codes, not our placeholders
        // Accept alphanumeric codes of 6-15 characters for real M-Pesa codes
        const isValidFormat = /^[A-Z0-9]{6,15}$/.test(cleanCode);

        if (!isValidFormat) {
          return {
            success: false,
            error:
              "Transaction code must be 6-15 alphanumeric characters (letters and numbers only)",
          };
        }
      }

      // Handle phone number formatting with fallback
      let formattedPhone = "254700000000"; // Default fallback
      if (phoneNumber) {
        try {
          formattedPhone = this.formatPhoneNumber(phoneNumber);
        } catch (phoneError) {
          console.log(
            `⚠️ Phone number formatting failed, using default: ${phoneError.message}`
          );
        }
      }

      // In development, simulate validation success for any properly formatted code
      // In production, you would call M-Pesa API to validate the transaction
      console.log(
        `✅ M-Pesa manual transaction validation (development mode): ${cleanCode}`
      );

      return {
        success: true,
        mpesaReceiptNumber: cleanCode,
        transactionDate: new Date().toISOString(),
        phoneNumber: formattedPhone,
        amount: amount,
        metadata: {
          method: "manual_code",
          validatedAt: new Date().toISOString(),
          developmentMode: true,
          originalCode: transactionCode,
        },
      };
    } catch (error) {
      console.error("Error validating manual M-Pesa transaction:", error);
      return {
        success: false,
        error: "Failed to validate transaction code: " + error.message,
        metadata: {
          error: error.message,
          originalCode: transactionCode,
        },
      };
    }
  }

  /**
   * Simulate payment for development/testing
   */
  async simulatePayment(paymentData) {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const { phoneNumber, amount, accountReference } = paymentData;
    const formattedPhone = this.formatPhoneNumber(phoneNumber);

    // Simulate success/failure based on phone number
    const shouldSucceed = !formattedPhone.endsWith("0000"); // Fail if phone ends with 0000

    if (shouldSucceed) {
      return {
        success: true,
        checkoutRequestId: `ws_CO_${Date.now()}`,
        merchantRequestId: `29115-********-1`,
        responseCode: "0",
        responseDescription: "Success. Request accepted for processing",
        customerMessage: `Payment request sent to ${formattedPhone}. Please check your phone and enter your M-Pesa PIN to complete the payment.`,
        metadata: {
          phoneNumber: formattedPhone,
          amount: amount,
          method: "stk_push_simulation",
        },
      };
    } else {
      return {
        success: false,
        error:
          "Payment request failed. Please check the phone number and try again.",
        metadata: {
          phoneNumber: formattedPhone,
          amount: amount,
        },
      };
    }
  }

  /**
   * Simulate payment status check for development
   */
  async simulatePaymentStatus(checkoutRequestId) {
    // Simulate processing delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Simulate success for demo purposes
    return {
      success: true,
      resultCode: "0",
      resultDesc: "The service request is processed successfully.",
      mpesaReceiptNumber: `NLJ7RT61SV`,
      transactionDate: new Date().toISOString(),
      phoneNumber: "254712345678",
      amount: 100,
      metadata: {
        checkoutRequestId: checkoutRequestId,
        method: "stk_push_simulation",
      },
    };
  }
}

module.exports = MpesaIntegrationService;
