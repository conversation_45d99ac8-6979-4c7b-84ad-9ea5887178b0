/**
 * DiscountApplicator Component
 *
 * Provides inline discount application with percentage and fixed amount options,
 * visual discount indicators, and discount removal functionality.
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import { formatCurrency } from "@/src/utils/currencyUtils";

import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { useState } from "react";
import {
  Alert,
  Modal,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { IconSymbol } from "./IconSymbol";
import { ModernButton } from "./ModernButton";

interface Discount {
  type: "percentage" | "fixed_amount";
  amount: number;
  description?: string;
}

interface DiscountApplicatorProps {
  currentDiscount?: Discount;
  itemPrice: number;
  itemQuantity: number;
  onApplyDiscount: (discount: Discount | null) => void;
  disabled?: boolean;
  style?: ViewStyle;
  size?: "small" | "medium" | "large";
  showDescription?: boolean;
}

export function DiscountApplicator({
  currentDiscount,
  itemPrice,
  itemQuantity,
  onApplyDiscount,
  disabled = false,
  style,
  size = "medium",
  showDescription = true,
}: DiscountApplicatorProps) {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const [modalVisible, setModalVisible] = useState(false);
  const [discountType, setDiscountType] = useState<
    "percentage" | "fixed_amount"
  >("percentage");
  const [discountAmount, setDiscountAmount] = useState("");
  const [discountDescription, setDiscountDescription] = useState("");

  const styles = createStyles(theme, size);

  const lineTotal = itemPrice * itemQuantity;
  const hasDiscount = currentDiscount && currentDiscount.amount > 0;

  const calculateDiscountAmount = (discount: Discount): number => {
    if (discount.type === "percentage") {
      return (lineTotal * discount.amount) / 100;
    } else {
      return Math.min(discount.amount, lineTotal); // Can't discount more than line total
    }
  };

  const getDiscountedTotal = (): number => {
    if (!hasDiscount) return lineTotal;
    const discountAmount = calculateDiscountAmount(currentDiscount!);
    return Math.max(0, lineTotal - discountAmount);
  };

  const getDiscountDisplayText = (): string => {
    if (!hasDiscount) return "";

    if (currentDiscount!.type === "percentage") {
      return `-${currentDiscount!.amount}%`;
    } else {
      return `-${formatCurrency(currentDiscount!.amount)}`;
    }
  };

  const handleOpenModal = () => {
    if (disabled) return;

    if (hasDiscount) {
      setDiscountType(currentDiscount!.type);
      setDiscountAmount(currentDiscount!.amount.toString());
      setDiscountDescription(currentDiscount!.description || "");
    } else {
      setDiscountType("percentage");
      setDiscountAmount("");
      setDiscountDescription("");
    }
    setModalVisible(true);
  };

  const handleApplyDiscount = () => {
    const amount = parseFloat(discountAmount);

    if (isNaN(amount) || amount <= 0) {
      Alert.alert("Invalid Discount", "Please enter a valid discount amount");
      return;
    }

    if (discountType === "percentage" && amount > 100) {
      Alert.alert("Invalid Discount", "Percentage discount cannot exceed 100%");
      return;
    }

    if (discountType === "fixed_amount" && amount > lineTotal) {
      Alert.alert(
        "Invalid Discount",
        `Fixed amount discount cannot exceed line total of ${formatCurrency(
          lineTotal
        )}`
      );
      return;
    }

    const discount: Discount = {
      type: discountType,
      amount: amount,
      description: discountDescription.trim() || undefined,
    };

    onApplyDiscount(discount);
    setModalVisible(false);
  };

  const handleRemoveDiscount = () => {
    Alert.alert(
      "Remove Discount",
      "Are you sure you want to remove this discount?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: () => {
            onApplyDiscount(null);
            setModalVisible(false);
          },
        },
      ]
    );
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  return (
    <View style={[styles.container, style]}>
      {/* Discount Button */}
      <TouchableOpacity
        style={[
          styles.discountButton,
          {
            backgroundColor: hasDiscount
              ? theme.colors.success + "20"
              : theme.colors.backgroundSecondary,
            borderColor: hasDiscount
              ? theme.colors.success
              : theme.colors.border,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={handleOpenModal}
        disabled={disabled}
      >
        <IconSymbol
          name={hasDiscount ? "tag.fill" : "tag"}
          size={size === "small" ? 14 : size === "large" ? 20 : 16}
          color={
            hasDiscount ? theme.colors.success : theme.colors.textSecondary
          }
        />
        {hasDiscount && (
          <Text
            style={[
              styles.discountText,
              {
                color: theme.colors.success,
                fontSize: size === "small" ? 10 : size === "large" ? 14 : 12,
              },
            ]}
          >
            {getDiscountDisplayText()}
          </Text>
        )}
      </TouchableOpacity>

      {/* Discount Modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: theme.colors.background },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
                Apply Discount
              </Text>
              <TouchableOpacity onPress={handleCloseModal}>
                <IconSymbol
                  name="xmark"
                  size={24}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>
            </View>

            {/* Line Total Display */}
            <View style={styles.totalDisplay}>
              <Text
                style={[
                  styles.totalLabel,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Line Total: {formatCurrency(lineTotal)}
              </Text>
              {hasDiscount && (
                <Text
                  style={[
                    styles.discountedTotal,
                    { color: theme.colors.success },
                  ]}
                >
                  After Discount: {formatCurrency(getDiscountedTotal())}
                </Text>
              )}
            </View>

            {/* Discount Type Selection */}
            <View style={styles.typeSelection}>
              <Text style={[styles.sectionLabel, { color: theme.colors.text }]}>
                Discount Type
              </Text>
              <View style={styles.typeButtons}>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    {
                      backgroundColor:
                        discountType === "percentage"
                          ? theme.colors.primary
                          : theme.colors.backgroundSecondary,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => setDiscountType("percentage")}
                >
                  <Text
                    style={[
                      styles.typeButtonText,
                      {
                        color:
                          discountType === "percentage"
                            ? theme.colors.background
                            : theme.colors.text,
                      },
                    ]}
                  >
                    Percentage (%)
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    {
                      backgroundColor:
                        discountType === "fixed_amount"
                          ? theme.colors.primary
                          : theme.colors.backgroundSecondary,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => setDiscountType("fixed_amount")}
                >
                  <Text
                    style={[
                      styles.typeButtonText,
                      {
                        color:
                          discountType === "fixed_amount"
                            ? theme.colors.background
                            : theme.colors.text,
                      },
                    ]}
                  >
                    Fixed Amount
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Discount Amount Input */}
            <View style={styles.inputSection}>
              <Text style={[styles.sectionLabel, { color: theme.colors.text }]}>
                {discountType === "percentage" ? "Percentage" : "Amount"}
              </Text>
              <TextInput
                style={[
                  styles.amountInput,
                  {
                    color: theme.colors.text,
                    borderColor: theme.colors.border,
                    backgroundColor: theme.colors.backgroundSecondary,
                  },
                ]}
                value={discountAmount}
                onChangeText={setDiscountAmount}
                placeholder={discountType === "percentage" ? "10" : "5.00"}
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
                maxLength={discountType === "percentage" ? 3 : 10}
              />
            </View>

            {/* Description Input */}
            {showDescription && (
              <View style={styles.inputSection}>
                <Text
                  style={[styles.sectionLabel, { color: theme.colors.text }]}
                >
                  Description (Optional)
                </Text>
                <TextInput
                  style={[
                    styles.descriptionInput,
                    {
                      color: theme.colors.text,
                      borderColor: theme.colors.border,
                      backgroundColor: theme.colors.backgroundSecondary,
                    },
                  ]}
                  value={discountDescription}
                  onChangeText={setDiscountDescription}
                  placeholder="Staff discount, promotion, etc."
                  placeholderTextColor={theme.colors.textSecondary}
                  maxLength={100}
                />
              </View>
            )}

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              {hasDiscount && (
                <ModernButton
                  title="Remove Discount"
                  onPress={handleRemoveDiscount}
                  variant="outline"
                  style={[
                    styles.actionButton,
                    { borderColor: theme.colors.error },
                  ]}
                  textStyle={{ color: theme.colors.error }}
                />
              )}
              <ModernButton
                title="Apply Discount"
                onPress={handleApplyDiscount}
                style={styles.actionButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const createStyles = (theme: any, size: "small" | "medium" | "large") => {
  const buttonSize = size === "small" ? 28 : size === "large" ? 40 : 32;

  return StyleSheet.create({
    container: {
      alignItems: "center",
    },
    discountButton: {
      width: buttonSize,
      height: buttonSize,
      borderRadius: theme.borderRadius.small,
      borderWidth: 1,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row",
      paddingHorizontal: 4,
    },
    discountText: {
      marginLeft: 4,
      fontWeight: "600",
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
      padding: 20,
    },
    modalContent: {
      width: "100%",
      maxWidth: 400,
      borderRadius: theme.borderRadius.large,
      padding: 20,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 5,
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: "600",
    },
    totalDisplay: {
      marginBottom: 20,
      padding: 12,
      backgroundColor: theme.colors.backgroundSecondary,
      borderRadius: theme.borderRadius.medium,
    },
    totalLabel: {
      fontSize: 14,
      fontWeight: "500",
    },
    discountedTotal: {
      fontSize: 16,
      fontWeight: "600",
      marginTop: 4,
    },
    typeSelection: {
      marginBottom: 20,
    },
    sectionLabel: {
      fontSize: 14,
      fontWeight: "600",
      marginBottom: 8,
    },
    typeButtons: {
      flexDirection: "row",
      gap: 8,
    },
    typeButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: theme.borderRadius.medium,
      borderWidth: 1,
      alignItems: "center",
    },
    typeButtonText: {
      fontSize: 14,
      fontWeight: "500",
    },
    inputSection: {
      marginBottom: 16,
    },
    amountInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.medium,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
    },
    descriptionInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.medium,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 14,
      minHeight: 40,
    },
    actionButtons: {
      flexDirection: "row",
      gap: 12,
      marginTop: 20,
    },
    actionButton: {
      flex: 1,
    },
  });
};
