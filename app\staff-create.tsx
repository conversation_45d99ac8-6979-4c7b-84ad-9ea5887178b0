import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { ROLES, getAllPermissions } from "@/src/config/rbac";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface CreateStaffForm {
  username: string;
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: "cashier" | "manager" | "super_admin";
  commissionRate: string;
  selectedPermissions: string[];
}

export default function StaffCreateScreen() {
  const router = useRouter();
  const { setCurrentTitle } = useNavigation();
  const { canManageStaff, getAssignableRoles, canAssignRole } = useRBAC();

  const [form, setForm] = useState<CreateStaffForm>({
    username: "",
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "cashier",
    commissionRate: "0",
    selectedPermissions: [],
  });
  const [loading, setLoading] = useState(false);
  const [showPermissions, setShowPermissions] = useState(false);

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const errorColor = "#FF3B30";

  React.useEffect(() => {
    setCurrentTitle("Create Staff Member");

    // Set default permissions based on role
    const rolePermissions = ROLES[form.role].permissions;
    setForm((prev) => ({ ...prev, selectedPermissions: rolePermissions }));
  }, [setCurrentTitle, form.role]);

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary,
    primaryColor
  );

  // Check permissions
  if (!canManageStaff) {
    return (
      <ScreenWrapper title="Create Staff Member" showBackButton>
        <View style={styles.noPermissionContainer}>
          <Ionicons name="lock-closed" size={64} color={textSecondary} />
          <Text style={[styles.noPermissionText, { color: textSecondary }]}>
            You don't have permission to create staff members
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  const validateForm = (): string | null => {
    if (!form.username.trim()) return "Username is required";
    if (!form.name.trim()) return "Name is required";
    if (!form.email.trim()) return "Email is required";
    if (!form.password) return "Password is required";
    if (form.password !== form.confirmPassword) return "Passwords do not match";
    if (form.password.length < 6)
      return "Password must be at least 6 characters";

    const commissionRate = parseFloat(form.commissionRate);
    if (isNaN(commissionRate) || commissionRate < 0 || commissionRate > 100) {
      return "Commission rate must be between 0 and 100";
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(form.email))
      return "Please enter a valid email address";

    return null;
  };

  const handleCreateStaff = async () => {
    const validationError = validateForm();
    if (validationError) {
      Alert.alert("Validation Error", validationError);
      return;
    }

    // Validate role assignment permission
    if (!canAssignRole(form.role)) {
      Alert.alert(
        "Permission Denied",
        `You don't have permission to assign the '${
          ROLES[form.role].name
        }' role.`
      );
      return;
    }

    setLoading(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.createStaff({
        username: form.username.trim(),
        name: form.name.trim(),
        email: form.email.trim().toLowerCase(),
        password: form.password,
        role: form.role,
        commissionRate: parseFloat(form.commissionRate),
        permissions: form.selectedPermissions,
      });

      if (response.success) {
        Alert.alert("Success", "Staff member created successfully", [
          { text: "OK", onPress: () => router.back() },
        ]);
      } else {
        Alert.alert("Error", response.error || "Failed to create staff member");
      }
    } catch (error) {
      console.error("Create staff error:", error);
      Alert.alert("Error", "Failed to create staff member");
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = (role: "cashier" | "manager" | "super_admin") => {
    const rolePermissions = ROLES[role].permissions;
    setForm((prev) => ({
      ...prev,
      role,
      selectedPermissions: rolePermissions,
    }));
  };

  const togglePermission = (permission: string) => {
    setForm((prev) => ({
      ...prev,
      selectedPermissions: prev.selectedPermissions.includes(permission)
        ? prev.selectedPermissions.filter((p) => p !== permission)
        : [...prev.selectedPermissions, permission],
    }));
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return errorColor;
      case "manager":
        return "#FF9500";
      case "cashier":
        return "#4CAF50";
      default:
        return textSecondary;
    }
  };

  return (
    <ScreenWrapper title="Create Staff Member" showBackButton>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <ModernCard
          style={StyleSheet.flatten([
            styles.card,
            { backgroundColor: surfaceColor },
          ])}
        >
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Basic Information
          </Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Username *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                { borderColor: textSecondary + "40", color: textColor },
              ]}
              value={form.username}
              onChangeText={(text) =>
                setForm((prev) => ({ ...prev, username: text }))
              }
              placeholder="Enter username"
              placeholderTextColor={textSecondary}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Full Name *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                { borderColor: textSecondary + "40", color: textColor },
              ]}
              value={form.name}
              onChangeText={(text) =>
                setForm((prev) => ({ ...prev, name: text }))
              }
              placeholder="Enter full name"
              placeholderTextColor={textSecondary}
              autoCapitalize="words"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Email *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                { borderColor: textSecondary + "40", color: textColor },
              ]}
              value={form.email}
              onChangeText={(text) =>
                setForm((prev) => ({ ...prev, email: text }))
              }
              placeholder="Enter email address"
              placeholderTextColor={textSecondary}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        </ModernCard>

        {/* Security */}
        <ModernCard
          style={StyleSheet.flatten([
            styles.card,
            { backgroundColor: surfaceColor },
          ])}
        >
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Security
          </Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Password *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                { borderColor: textSecondary + "40", color: textColor },
              ]}
              value={form.password}
              onChangeText={(text) =>
                setForm((prev) => ({ ...prev, password: text }))
              }
              placeholder="Enter password (min 6 characters)"
              placeholderTextColor={textSecondary}
              secureTextEntry
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Confirm Password *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                { borderColor: textSecondary + "40", color: textColor },
              ]}
              value={form.confirmPassword}
              onChangeText={(text) =>
                setForm((prev) => ({ ...prev, confirmPassword: text }))
              }
              placeholder="Confirm password"
              placeholderTextColor={textSecondary}
              secureTextEntry
              autoCapitalize="none"
            />
          </View>
        </ModernCard>

        {/* Role & Permissions */}
        <ModernCard
          style={StyleSheet.flatten([
            styles.card,
            { backgroundColor: surfaceColor },
          ])}
        >
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Role & Permissions
          </Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Role *
            </Text>
            <View style={styles.roleSelector}>
              {getAssignableRoles().map((roleKey) => {
                const roleData = ROLES[roleKey];
                return (
                  <TouchableOpacity
                    key={roleKey}
                    style={[
                      styles.roleOption,
                      {
                        backgroundColor:
                          form.role === roleKey
                            ? getRoleColor(roleKey) + "20"
                            : "transparent",
                        borderColor:
                          form.role === roleKey
                            ? getRoleColor(roleKey)
                            : textSecondary + "40",
                      },
                    ]}
                    onPress={() => handleRoleChange(roleKey as any)}
                  >
                    <Text
                      style={[
                        styles.roleText,
                        {
                          color:
                            form.role === roleKey
                              ? getRoleColor(roleKey)
                              : textColor,
                        },
                      ]}
                    >
                      {roleData.name}
                    </Text>
                    <Text
                      style={[
                        styles.roleDescription,
                        {
                          color:
                            form.role === roleKey
                              ? getRoleColor(roleKey)
                              : textSecondary,
                        },
                      ]}
                    >
                      {roleData.description}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: textSecondary }]}>
              Commission Rate (%)
            </Text>
            <TextInput
              style={[
                styles.textInput,
                { borderColor: textSecondary + "40", color: textColor },
              ]}
              value={form.commissionRate}
              onChangeText={(text) =>
                setForm((prev) => ({ ...prev, commissionRate: text }))
              }
              placeholder="0"
              placeholderTextColor={textSecondary}
              keyboardType="numeric"
              maxLength={5}
            />
          </View>

          <TouchableOpacity
            style={styles.permissionsToggle}
            onPress={() => setShowPermissions(!showPermissions)}
          >
            <Text
              style={[styles.permissionsToggleText, { color: primaryColor }]}
            >
              {showPermissions ? "Hide" : "Show"} Permissions (
              {form.selectedPermissions.length})
            </Text>
            <Ionicons
              name={showPermissions ? "chevron-up" : "chevron-down"}
              size={20}
              color={primaryColor}
            />
          </TouchableOpacity>

          {showPermissions && (
            <View style={styles.permissionsList}>
              {getAllPermissions().map((permission) => (
                <TouchableOpacity
                  key={permission.id}
                  style={[
                    styles.permissionItem,
                    {
                      backgroundColor: form.selectedPermissions.includes(
                        permission.id
                      )
                        ? primaryColor + "20"
                        : "transparent",
                      borderColor: form.selectedPermissions.includes(
                        permission.id
                      )
                        ? primaryColor
                        : textSecondary + "40",
                    },
                  ]}
                  onPress={() => togglePermission(permission.id)}
                >
                  <View style={styles.permissionInfo}>
                    <Text
                      style={[
                        styles.permissionName,
                        {
                          color: form.selectedPermissions.includes(
                            permission.id
                          )
                            ? primaryColor
                            : textColor,
                        },
                      ]}
                    >
                      {permission.name}
                    </Text>
                    <Text
                      style={[
                        styles.permissionDescription,
                        {
                          color: form.selectedPermissions.includes(
                            permission.id
                          )
                            ? primaryColor
                            : textSecondary,
                        },
                      ]}
                    >
                      {permission.description}
                    </Text>
                  </View>
                  {form.selectedPermissions.includes(permission.id) && (
                    <Ionicons
                      name="checkmark-circle"
                      size={20}
                      color={primaryColor}
                    />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </ModernCard>

        {/* Create Button */}
        <View style={styles.buttonContainer}>
          <ModernButton
            title={loading ? "Creating..." : "Create Staff Member"}
            onPress={handleCreateStaff}
            disabled={loading}
            style={styles.createButton}
          />
        </View>
      </ScrollView>
    </ScreenWrapper>
  );
}

const createStyles = (
  backgroundColor: string,
  _surfaceColor: string,
  _textColor: string,
  _textSecondary: string,
  _primaryColor: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
    },
    noPermissionContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 40,
    },
    noPermissionText: {
      fontSize: 16,
      textAlign: "center",
      marginTop: 16,
    },
    card: {
      margin: 20,
      padding: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: "bold",
      marginBottom: 16,
    },
    inputGroup: {
      marginBottom: 16,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: "500",
      marginBottom: 8,
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 12,
      fontSize: 16,
    },
    roleSelector: {
      gap: 12,
    },
    roleOption: {
      borderWidth: 1,
      borderRadius: 8,
      padding: 12,
    },
    roleText: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 4,
    },
    roleDescription: {
      fontSize: 14,
    },
    permissionsToggle: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 12,
      marginTop: 8,
    },
    permissionsToggleText: {
      fontSize: 16,
      fontWeight: "500",
    },
    permissionsList: {
      marginTop: 12,
      gap: 8,
    },
    permissionItem: {
      flexDirection: "row",
      alignItems: "center",
      borderWidth: 1,
      borderRadius: 8,
      padding: 12,
    },
    permissionInfo: {
      flex: 1,
    },
    permissionName: {
      fontSize: 14,
      fontWeight: "500",
      marginBottom: 2,
    },
    permissionDescription: {
      fontSize: 12,
    },
    buttonContainer: {
      padding: 20,
    },
    createButton: {
      marginTop: 20,
    },
  });
