# Ticket Design System Style Guide

## Overview

This style guide defines the comprehensive design system for all ticket/cart management components in the Dukalink POS application. It ensures consistency, accessibility, and maintainability across all ticket-related UI elements.

## Design Principles

### 1. **Clarity First**
- Clear visual hierarchy with proper typography
- Consistent spacing and alignment
- High contrast for readability in retail environments

### 2. **Efficiency**
- Quick recognition of ticket states
- Minimal cognitive load for users
- Fast interaction patterns

### 3. **Consistency**
- Unified color system across all components
- Consistent spacing and sizing
- Predictable interaction patterns

## Color System

### Ticket States
- **Default**: Neutral gray tones for inactive tickets
- **Active**: Primary brand color (pink) for current working ticket
- **Paused**: Warning amber for temporarily stopped tickets
- **Completed**: Success green for finished tickets
- **Error**: Error red for problematic tickets

### Usage Guidelines
```typescript
// ✅ Correct usage
const colors = getTicketColors(theme, 'active');

// ❌ Avoid hardcoded colors
const badStyle = { backgroundColor: '#d2686f' };
```

## Typography

### Hierarchy
1. **Ticket Title**: `h3` - Main ticket identifier
2. **Ticket Total**: `h2` - Most prominent financial information
3. **Item Count**: `small` - Supporting information
4. **Status**: `overline` - State indicator
5. **Timestamp**: `small` - Metadata

### Best Practices
- Use semantic typography variants
- Maintain consistent line heights
- Ensure proper contrast ratios

## Spacing System

### Consistent Spacing
- **xs**: 4px - Minimal spacing
- **sm**: 8px - Small gaps
- **md**: 16px - Standard spacing
- **lg**: 24px - Section spacing
- **xl**: 32px - Large spacing
- **xxl**: 48px - Major sections

### Layout Guidelines
```typescript
// ✅ Use theme spacing
padding: theme.spacing.lg

// ❌ Avoid magic numbers
padding: 24
```

## Component Variants

### Ticket Cards

#### Sizes
- **Compact**: Minimal height, essential info only
- **Default**: Standard size with all key information
- **Expanded**: Maximum detail view

#### States
- **Default**: Normal inactive state
- **Active**: Currently selected ticket
- **Selected**: Multi-select mode
- **Disabled**: Non-interactive state

### Buttons

#### Variants
- **Primary**: Main actions (Create, Save)
- **Secondary**: Alternative actions (Cancel, Edit)
- **Ghost**: Subtle actions (Options, More)

#### Sizes
- **Small**: Compact interfaces
- **Medium**: Standard size
- **Large**: Prominent actions

## Animation Guidelines

### Interaction Feedback
- **Card Press**: Subtle scale down (0.98)
- **Status Change**: Brief scale up (1.05)
- **Modal Entry**: Slide up with fade

### Timing
- **Fast**: 150ms for immediate feedback
- **Normal**: 300ms for transitions
- **Slow**: 500ms for complex animations

## Accessibility

### Color Contrast
- Minimum 4.5:1 ratio for normal text
- Minimum 3:1 ratio for large text
- Status indicators use both color and text

### Touch Targets
- Minimum 44px touch target size
- Adequate spacing between interactive elements
- Clear visual feedback for interactions

## Usage Examples

### Basic Ticket Card
```typescript
import { createTicketStyles } from '@/src/design-system/TicketDesignSystem';

const TicketCard = ({ ticket, isActive }) => {
  const theme = useTheme();
  const styles = createTicketStyles(theme);
  
  return (
    <View style={isActive ? styles.activeCard : styles.card}>
      <Text style={styles.text.title}>{ticket.name}</Text>
      <Text style={styles.text.total}>{formatCurrency(ticket.total)}</Text>
    </View>
  );
};
```

### Modal Implementation
```typescript
const TicketModal = ({ visible, onClose }) => {
  const theme = useTheme();
  const styles = createTicketStyles(theme);
  
  return (
    <Modal visible={visible} transparent>
      <View style={styles.modal.overlay}>
        <View style={styles.modal.container}>
          <View style={styles.modal.header}>
            <Text style={styles.modal.title}>Ticket Options</Text>
          </View>
          <View style={styles.modal.content}>
            {/* Content */}
          </View>
          <View style={styles.modal.footer}>
            {/* Actions */}
          </View>
        </View>
      </View>
    </Modal>
  );
};
```

## Testing Guidelines

### Visual Testing
- Test all variants in both light and dark themes
- Verify proper contrast ratios
- Check spacing consistency

### Interaction Testing
- Verify touch targets are adequate
- Test animation performance
- Ensure accessibility features work

## Migration Guide

### From Old Components
1. Replace hardcoded styles with design system functions
2. Update color references to use theme colors
3. Standardize spacing using theme values
4. Implement proper typography hierarchy

### Breaking Changes
- Remove custom color definitions
- Update component prop interfaces
- Standardize animation timings

## Maintenance

### Adding New Variants
1. Define new variant in type definitions
2. Add color mappings in `getTicketColors`
3. Update style functions
4. Document usage examples

### Theme Updates
1. Validate theme compatibility with `validateTicketTheme`
2. Test all components with new theme
3. Update documentation if needed

## Resources

- [Theme Context Documentation](../contexts/ThemeContext.tsx)
- [Design Constants](../../constants/Design.ts)
- [Color System](../../constants/Colors.ts)
- [Component Examples](../components/ui/)
