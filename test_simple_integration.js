/**
 * Simple React Native Integration Test
 * Tests core functionality without edge cases
 */

const axios = require("axios");
const BASE_URL = "http://localhost:3020/api";

async function testSimpleIntegration() {
  console.log("🚀 Testing Simple React Native Integration...\n");

  try {
    // Test 1: Login Flow
    console.log("📋 Test 1: Login Flow");
    const loginResponse = await axios.post(`${BASE_URL}/pos/login`, {
      username: "manager1",
      password: "manager123",
    });

    if (loginResponse.data.success) {
      console.log("✅ PASSED: Login");
      const user = loginResponse.data.data.user;
      console.log(`   User: ${user.name} (${user.role})`);
      console.log(`   Permissions: ${user.permissions.length} permissions`);
    } else {
      console.log("❌ FAILED: Login");
      return;
    }

    const token = loginResponse.data.data.token;
    const authHeaders = { Authorization: `Bearer ${token}` };
    console.log("");

    // Test 2: Sales Agents
    console.log("📋 Test 2: Sales Agents");
    const agentsResponse = await axios.get(`${BASE_URL}/sales-agents`, {
      headers: authHeaders,
    });

    if (agentsResponse.data.success) {
      console.log("✅ PASSED: Sales Agents");
      console.log(
        `   Found: ${agentsResponse.data.data.salesAgents.length} agents`
      );
    } else {
      console.log("❌ FAILED: Sales Agents");
    }
    console.log("");

    // Test 3: Commission Configuration
    console.log("📋 Test 3: Commission Configuration");
    const configResponse = await axios.get(
      `${BASE_URL}/discounts/configuration`,
      {
        headers: authHeaders,
      }
    );

    if (configResponse.data.success) {
      console.log("✅ PASSED: Commission Configuration");
      const config = configResponse.data.data.configuration;
      console.log(`   Enabled: ${config.enabled}`);
      console.log(`   Min Order: KSh ${config.min_order_amount}`);
    } else {
      console.log("❌ FAILED: Commission Configuration");
    }
    console.log("");

    // Test 4: Valid Commission Calculation (Above minimum)
    console.log("📋 Test 4: Valid Commission Calculation");
    const calcResponse = await axios.post(
      `${BASE_URL}/discounts/calculate`,
      {
        cartData: { subtotal: "100.00" },
        staffId: "pos-002",
        salesAgentId: "agent-001",
      },
      {
        headers: { ...authHeaders, "Content-Type": "application/json" },
      }
    );

    if (calcResponse.data.success) {
      console.log("✅ PASSED: Commission Calculation");
      const discount = calcResponse.data.data.discount;
      console.log(`   Subtotal: KSh ${discount.subtotal}`);
      console.log(`   Discount: KSh ${discount.totalDiscount}`);
      console.log(`   Final: KSh ${discount.finalAmount}`);
    } else {
      console.log("❌ FAILED: Commission Calculation");
    }
    console.log("");

    console.log("🎉 Simple Integration Test Completed!");
    console.log("✅ Core React Native integration is working");
    console.log("🚀 Ready to proceed with frontend implementation");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

testSimpleIntegration();
