#!/usr/bin/env node

/**
 * Dynamic Script Runner for Production/Development Environments
 * 
 * This script allows running various database and system scripts
 * dynamically based on environment variables, making it safe for
 * production use.
 * 
 * Usage:
 *   node run-script-dynamic.js <script-name> [options]
 * 
 * Available scripts:
 *   - grant-permissions: Grant collect_payments permission to staff
 *   - fix-payment-status: Fix payment status ENUM
 *   - fix-payment-transactions: Fix payment transactions status ENUM
 *   - fix-permissions-table: Create/fix staff permissions table
 *   - add-collect-payments: Add collect_payments permission (direct DB)
 * 
 * Environment Variables:
 *   NODE_ENV: Environment (local, staging, production)
 *   DB_HOST: Database host
 *   DB_PORT: Database port
 *   DB_USER: Database user
 *   DB_PASSWORD: Database password
 *   DB_NAME: Database name
 *   API_BASE_URL: API base URL (for API-based scripts)
 *   ADMIN_USERNAME: Admin username (for API-based scripts)
 *   ADMIN_PASSWORD: Admin password (for API-based scripts)
 */

require('dotenv').config();
const { spawn } = require('child_process');
const path = require('path');

// Available scripts mapping
const AVAILABLE_SCRIPTS = {
  'grant-permissions': {
    file: 'grant-collect-payments-via-api.js',
    description: 'Grant collect_payments permission to staff via API',
    type: 'api',
    requiresServer: true
  },
  'fix-payment-status': {
    file: 'fix-payment-status-enum.js',
    description: 'Fix payment status ENUM to include authorized',
    type: 'database',
    requiresServer: false
  },
  'fix-payment-transactions': {
    file: 'fix-payment-transactions-status-enum.js',
    description: 'Fix payment transactions status ENUM',
    type: 'database',
    requiresServer: false
  },
  'fix-permissions-table': {
    file: 'fix_permissions_table.js',
    description: 'Create/fix staff permissions table',
    type: 'database',
    requiresServer: false
  },
  'add-collect-payments': {
    file: 'add-collect-payments-permission.js',
    description: 'Add collect_payments permission (direct DB access)',
    type: 'database',
    requiresServer: false
  }
};

function showUsage() {
  console.log('🚀 Dynamic Script Runner for DukaLink Shopify');
  console.log('');
  console.log('Usage: node run-script-dynamic.js <script-name> [options]');
  console.log('');
  console.log('Available scripts:');
  
  Object.entries(AVAILABLE_SCRIPTS).forEach(([name, config]) => {
    console.log(`  ${name.padEnd(20)} - ${config.description}`);
    console.log(`  ${' '.repeat(20)}   Type: ${config.type}, Server required: ${config.requiresServer}`);
  });
  
  console.log('');
  console.log('Environment Variables:');
  console.log('  NODE_ENV         - Environment (local, staging, production)');
  console.log('  DB_HOST          - Database host');
  console.log('  DB_PORT          - Database port');
  console.log('  DB_USER          - Database user');
  console.log('  DB_PASSWORD      - Database password');
  console.log('  DB_NAME          - Database name');
  console.log('  API_BASE_URL     - API base URL (for API-based scripts)');
  console.log('  ADMIN_USERNAME   - Admin username (for API-based scripts)');
  console.log('  ADMIN_PASSWORD   - Admin password (for API-based scripts)');
  console.log('');
  console.log('Examples:');
  console.log('  node run-script-dynamic.js grant-permissions');
  console.log('  node run-script-dynamic.js fix-payment-status');
  console.log('  NODE_ENV=production node run-script-dynamic.js grant-permissions');
}

function validateEnvironment() {
  const NODE_ENV = process.env.NODE_ENV || 'local';
  const requiredVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
  
  console.log(`🌍 Environment: ${NODE_ENV}`);
  console.log(`🗄️  Database: ${process.env.DB_NAME}@${process.env.DB_HOST}:${process.env.DB_PORT || 3306}`);
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => console.error(`   - ${varName}`));
    return false;
  }
  
  return true;
}

async function runScript(scriptName, options = []) {
  const scriptConfig = AVAILABLE_SCRIPTS[scriptName];
  
  if (!scriptConfig) {
    console.error(`❌ Unknown script: ${scriptName}`);
    console.error('Available scripts:', Object.keys(AVAILABLE_SCRIPTS).join(', '));
    return false;
  }
  
  const scriptPath = path.join(__dirname, scriptConfig.file);
  
  console.log(`🚀 Running script: ${scriptConfig.description}`);
  console.log(`📁 Script file: ${scriptConfig.file}`);
  
  if (scriptConfig.requiresServer) {
    console.log('⚠️  This script requires the backend server to be running');
    console.log('   Make sure the server is started before running this script');
  }
  
  return new Promise((resolve, reject) => {
    const child = spawn('node', [scriptPath, ...options], {
      stdio: 'inherit',
      cwd: __dirname
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ Script completed successfully`);
        resolve(true);
      } else {
        console.error(`❌ Script failed with exit code ${code}`);
        resolve(false);
      }
    });
    
    child.on('error', (error) => {
      console.error(`❌ Failed to start script: ${error.message}`);
      reject(error);
    });
  });
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showUsage();
    return;
  }
  
  const scriptName = args[0];
  const scriptOptions = args.slice(1);
  
  console.log('🔧 DukaLink Shopify - Dynamic Script Runner');
  console.log('==========================================');
  
  if (!validateEnvironment()) {
    process.exit(1);
  }
  
  try {
    const success = await runScript(scriptName, scriptOptions);
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Script execution failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runScript,
  AVAILABLE_SCRIPTS,
  validateEnvironment
};
