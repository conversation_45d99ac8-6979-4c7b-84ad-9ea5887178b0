/**
 * Ticket Auto-Creation Middleware
 *
 * Automatically creates tickets when items are added to cart and syncs
 * cart operations with the active ticket for seamless POS workflow.
 */

import { Middleware } from "@reduxjs/toolkit";
import { RootState } from "../index";
import {
  createTicket,
  selectActiveTicket,
  selectActiveTicketId,
  addItemToActiveTicket,
  removeItemFromActiveTicket,
  updateItemQuantityInActiveTicket,
  applyItemDiscountInActiveTicket,
  updateItemNotesInActiveTicket,
  setActiveTicketCustomer,
  setActiveTicketSalesperson,
  addDiscountToActiveTicket,
  removeDiscountFromActiveTicket,
  setActiveTicketNote,
} from "../slices/ticketSlice";
import { selectCurrentUser } from "../slices/userSlice";
import { saveTicket } from "../thunks/ticketThunks";

// Track recent ticket creation to prevent rapid successive operations
let lastTicketCreationTime = 0;
const TICKET_CREATION_COOLDOWN = 1000; // 1 second cooldown

export const ticketAutoCreationMiddleware: Middleware<{}, RootState> =
  (store) => (next) => (action) => {
    const result = next(action);
    const state = store.getState();

    // DISABLED: Auto-create ticket when first item is added to cart
    // This functionality has been moved to addItemToTicketWithAutoCreate action
    // to eliminate race conditions and dual cart system issues
    if (action.type === "cart/addToCart") {
      console.log(
        "⚠️ Legacy cart/addToCart detected - this should not be used in ticket mode"
      );
      console.log(
        "🔄 Use addItemToTicketWithAutoCreate instead for proper ticket handling"
      );
      // Don't process this action to avoid conflicts
      return result;
    }

    // Sync cart operations with active ticket
    if (action.type === "cart/removeFromCart") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(removeItemFromActiveTicket(action.payload));
      }
    }

    if (action.type === "cart/updateQuantity") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(updateItemQuantityInActiveTicket(action.payload));
      }
    }

    if (action.type === "cart/applyItemDiscount") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(applyItemDiscountInActiveTicket(action.payload));
      }
    }

    if (action.type === "cart/updateItemNotes") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(updateItemNotesInActiveTicket(action.payload));
      }
    }

    if (action.type === "cart/setCustomer") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(setActiveTicketCustomer(action.payload));
      }
    }

    if (action.type === "cart/setSalesperson") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(setActiveTicketSalesperson(action.payload));
      }
    }

    if (action.type === "cart/addDiscount") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(addDiscountToActiveTicket(action.payload));
      }
    }

    if (action.type === "cart/removeDiscount") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(removeDiscountFromActiveTicket(action.payload));
      }
    }

    if (action.type === "cart/setNote") {
      const activeTicket = selectActiveTicket(state);
      if (activeTicket) {
        store.dispatch(setActiveTicketNote(action.payload));
      }
    }

    return result;
  };

/**
 * Professional POS Ticket Creation Rules
 *
 * 1. **Auto-Creation**: Tickets are automatically created when the first item is added to cart
 * 2. **User Context**: Tickets are created with current user's staff ID and location
 * 3. **Naming Convention**: Auto-generated names with timestamp for easy identification
 * 4. **Sync Operations**: All cart operations are automatically synced to the active ticket
 * 5. **State Consistency**: Cart and ticket state remain synchronized at all times
 */

export const TICKET_CREATION_RULES = {
  AUTO_CREATE_ON_FIRST_ITEM: true,
  MAX_TICKETS_PER_USER: 5,
  MAX_TICKETS_PER_TERMINAL: 10,
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  TICKET_EXPIRY_HOURS: 24,
  REQUIRE_CUSTOMER_FOR_LARGE_ORDERS: true,
  LARGE_ORDER_THRESHOLD: 1000, // KES
} as const;
