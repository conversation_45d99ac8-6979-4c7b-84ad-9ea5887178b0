/**
 * Auto-Save and Resume Demo Component
 * 
 * Demonstrates the auto-save and resume functionality with
 * interactive controls and real-time status display.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/src/contexts/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/src/store';
import { 
  selectAllTickets, 
  selectActiveTicketId,
  createTicket,
  setActiveTicket,
  addItemToActiveTicket,
} from '@/src/store/slices/ticketSlice';
import useAutoSaveResume from '@/src/hooks/useAutoSaveResume';
import AutoSaveIndicator from '@/components/ui/AutoSaveIndicator';
import ResumeModal from '@/components/ui/ResumeModal';

export const AutoSaveResumeDemo: React.FC = () => {
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  
  // Redux state
  const tickets = useAppSelector(selectAllTickets);
  const activeTicketId = useAppSelector(selectActiveTicketId);
  
  // Auto-save and resume hook
  const [autoSaveState, autoSaveActions] = useAutoSaveResume();
  
  // Local state
  const [showResumeModal, setShowResumeModal] = useState(false);
  const [demoStep, setDemoStep] = useState(0);

  // Demo steps
  const demoSteps = [
    'Create a new ticket',
    'Add items to trigger auto-save',
    'Simulate app restart',
    'Resume from previous session',
    'Test offline functionality',
  ];

  useEffect(() => {
    // Show resume modal if resume is available
    if (autoSaveState.resumeAvailable && autoSaveState.lastResumeResult) {
      setShowResumeModal(true);
    }
  }, [autoSaveState.resumeAvailable, autoSaveState.lastResumeResult]);

  const handleCreateDemoTicket = async () => {
    try {
      const result = await dispatch(createTicket({
        name: `Demo Ticket ${Date.now()}`,
        staffId: 'demo-staff',
        terminalId: 'demo-terminal',
        locationId: 'demo-location',
      })).unwrap();

      await dispatch(setActiveTicket(result.id));
      setDemoStep(1);
    } catch (error) {
      Alert.alert('Error', 'Failed to create demo ticket');
    }
  };

  const handleAddDemoItem = async () => {
    if (!activeTicketId) {
      Alert.alert('Error', 'No active ticket');
      return;
    }

    try {
      await dispatch(addItemToActiveTicket({
        variantId: `demo-variant-${Date.now()}`,
        productId: `demo-product-${Date.now()}`,
        title: `Demo Product ${Math.floor(Math.random() * 100)}`,
        variantTitle: 'Default',
        sku: `DEMO-${Math.floor(Math.random() * 1000)}`,
        price: (Math.random() * 50 + 10).toFixed(2),
        quantity: Math.floor(Math.random() * 3) + 1,
        inventoryQuantity: 100,
      }));

      setDemoStep(2);
    } catch (error) {
      Alert.alert('Error', 'Failed to add demo item');
    }
  };

  const handleSimulateRestart = async () => {
    try {
      // Save current session
      await autoSaveActions.saveCurrentSession();
      
      // Force auto-save
      await autoSaveActions.forceAutoSave();
      
      Alert.alert(
        'Simulated App Restart',
        'Session saved! In a real scenario, the app would restart and resume from this point.',
        [
          {
            text: 'Trigger Resume',
            onPress: async () => {
              const result = await autoSaveActions.triggerResume();
              if (result.success) {
                setShowResumeModal(true);
                setDemoStep(3);
              }
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to simulate restart');
    }
  };

  const handleResumeTickets = async (ticketIds: string[]) => {
    try {
      // In a real implementation, this would restore the tickets
      // For demo purposes, we'll just show success
      Alert.alert(
        'Resume Successful',
        `Resumed ${ticketIds.length} ticket(s) successfully!`
      );
      
      setShowResumeModal(false);
      setDemoStep(4);
    } catch (error) {
      Alert.alert('Error', 'Failed to resume tickets');
    }
  };

  const handleTestOffline = () => {
    Alert.alert(
      'Offline Mode Test',
      'In offline mode, auto-save will queue changes locally and sync when connection is restored.',
      [
        {
          text: 'Simulate Offline',
          onPress: () => {
            // This would normally be handled by network detection
            Alert.alert('Offline Mode', 'Changes will be queued for sync when online.');
          },
        },
      ]
    );
  };

  const getCurrentStepAction = () => {
    switch (demoStep) {
      case 0:
        return handleCreateDemoTicket;
      case 1:
        return handleAddDemoItem;
      case 2:
        return handleSimulateRestart;
      case 3:
        return () => setDemoStep(4);
      case 4:
        return handleTestOffline;
      default:
        return () => setDemoStep(0);
    }
  };

  const getCurrentStepText = () => {
    if (demoStep < demoSteps.length) {
      return demoSteps[demoStep];
    }
    return 'Demo Complete - Start Over';
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Auto-Save & Resume Demo
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
          Interactive demonstration of persistent ticket functionality
        </Text>
      </View>

      {/* Auto-Save Status */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Auto-Save Status
        </Text>
        <AutoSaveIndicator
          isAutoSaving={autoSaveState.isAutoSaving}
          autoSaveEnabled={autoSaveState.autoSaveEnabled}
          stats={autoSaveState.autoSaveStats}
          isOnline={autoSaveState.isOnline}
          error={autoSaveState.error}
          onToggleAutoSave={autoSaveState.autoSaveEnabled ? autoSaveActions.disableAutoSave : autoSaveActions.enableAutoSave}
          onForceAutoSave={autoSaveActions.forceAutoSave}
          onClearError={autoSaveActions.clearError}
        />
      </View>

      {/* Demo Progress */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Demo Progress
        </Text>
        <View style={styles.progressContainer}>
          {demoSteps.map((step, index) => (
            <View key={index} style={styles.progressStep}>
              <View style={[
                styles.progressIndicator,
                {
                  backgroundColor: index <= demoStep 
                    ? theme.colors.primary 
                    : theme.colors.disabled,
                }
              ]}>
                <Text style={[
                  styles.progressNumber,
                  { color: index <= demoStep ? theme.colors.onPrimary : theme.colors.textSecondary }
                ]}>
                  {index + 1}
                </Text>
              </View>
              <Text style={[
                styles.progressText,
                { 
                  color: index <= demoStep 
                    ? theme.colors.text 
                    : theme.colors.textSecondary,
                  fontWeight: index === demoStep ? '600' : '400',
                }
              ]}>
                {step}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* Current Tickets */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Current Tickets ({tickets.length})
        </Text>
        {tickets.length === 0 ? (
          <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
            No tickets created yet
          </Text>
        ) : (
          <View style={styles.ticketsList}>
            {tickets.map((ticket) => (
              <View 
                key={ticket.id}
                style={[
                  styles.ticketCard,
                  { 
                    backgroundColor: theme.colors.surface,
                    borderColor: ticket.id === activeTicketId 
                      ? theme.colors.primary 
                      : theme.colors.border,
                  }
                ]}
              >
                <View style={styles.ticketHeader}>
                  <Text style={[styles.ticketName, { color: theme.colors.text }]}>
                    {ticket.name}
                  </Text>
                  {ticket.isDirty && (
                    <View style={[styles.dirtyIndicator, { backgroundColor: theme.colors.warning }]}>
                      <Text style={[styles.dirtyText, { color: theme.colors.onWarning }]}>
                        Unsaved
                      </Text>
                    </View>
                  )}
                </View>
                <Text style={[styles.ticketInfo, { color: theme.colors.textSecondary }]}>
                  {ticket.items.length} items • Total: {ticket.total.toFixed(2)}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Demo Actions */}
      <View style={styles.section}>
        <TouchableOpacity
          style={[styles.demoButton, { backgroundColor: theme.colors.primary }]}
          onPress={getCurrentStepAction()}
        >
          <Ionicons name="play" size={20} color={theme.colors.onPrimary} />
          <Text style={[styles.demoButtonText, { color: theme.colors.onPrimary }]}>
            {getCurrentStepText()}
          </Text>
        </TouchableOpacity>

        <View style={styles.actionGrid}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.surface }]}
            onPress={autoSaveActions.forceAutoSave}
            disabled={autoSaveState.isAutoSaving}
          >
            <Ionicons name="save" size={16} color={theme.colors.primary} />
            <Text style={[styles.actionButtonText, { color: theme.colors.text }]}>
              Force Save
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => autoSaveActions.triggerResume().then(() => setShowResumeModal(true))}
          >
            <Ionicons name="refresh" size={16} color={theme.colors.primary} />
            <Text style={[styles.actionButtonText, { color: theme.colors.text }]}>
              Test Resume
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.surface }]}
            onPress={autoSaveActions.clearResumeData}
          >
            <Ionicons name="trash" size={16} color={theme.colors.error} />
            <Text style={[styles.actionButtonText, { color: theme.colors.text }]}>
              Clear Data
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Resume Modal */}
      <ResumeModal
        visible={showResumeModal}
        resumeResult={autoSaveState.lastResumeResult}
        onResume={handleResumeTickets}
        onDismiss={() => setShowResumeModal(false)}
        onClearAll={autoSaveActions.clearResumeData}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  progressContainer: {
    gap: 12,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressNumber: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressText: {
    flex: 1,
    fontSize: 16,
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 20,
  },
  ticketsList: {
    gap: 8,
  },
  ticketCard: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  ticketHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  ticketName: {
    fontSize: 16,
    fontWeight: '600',
  },
  dirtyIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  dirtyText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ticketInfo: {
    fontSize: 14,
  },
  demoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    paddingVertical: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  demoButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AutoSaveResumeDemo;
