import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { getAPIClient } from '../../services/api/dukalink-client';

interface SyncState {
  isOnline: boolean;
  lastSyncAt: string | null;
  pendingOperations: number;
  failedOperations: number;
  isSyncing: boolean;
  error: string | null;
}

const initialState: SyncState = {
  isOnline: true,
  lastSyncAt: null,
  pendingOperations: 0,
  failedOperations: 0,
  isSyncing: false,
  error: null,
};

// Async thunks
export const getSyncStatus = createAsyncThunk(
  'sync/getSyncStatus',
  async (storeId: string, { rejectWithValue }) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getSyncStatus(storeId);
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to get sync status');
      }
      
      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const triggerSync = createAsyncThunk(
  'sync/triggerSync',
  async (
    { storeId, type }: {
      storeId: string;
      type: 'products' | 'customers' | 'orders' | 'all';
    },
    { rejectWithValue }
  ) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.triggerSync(storeId, type);
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to trigger sync');
      }
      
      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

const syncSlice = createSlice({
  name: 'sync',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    updateLastSync: (state) => {
      state.lastSyncAt = new Date().toISOString();
    },
    incrementPendingOperations: (state) => {
      state.pendingOperations += 1;
    },
    decrementPendingOperations: (state) => {
      state.pendingOperations = Math.max(0, state.pendingOperations - 1);
    },
    incrementFailedOperations: (state) => {
      state.failedOperations += 1;
    },
    resetFailedOperations: (state) => {
      state.failedOperations = 0;
    },
  },
  extraReducers: (builder) => {
    // Get Sync Status
    builder
      .addCase(getSyncStatus.pending, (state) => {
        state.error = null;
      })
      .addCase(getSyncStatus.fulfilled, (state, action) => {
        state.isOnline = action.payload.isOnline;
        state.lastSyncAt = action.payload.lastSyncAt || null;
        state.pendingOperations = action.payload.pendingOperations;
        state.failedOperations = action.payload.failedOperations;
      })
      .addCase(getSyncStatus.rejected, (state, action) => {
        state.error = action.payload as string;
        state.isOnline = false;
      });

    // Trigger Sync
    builder
      .addCase(triggerSync.pending, (state) => {
        state.isSyncing = true;
        state.error = null;
      })
      .addCase(triggerSync.fulfilled, (state, action) => {
        state.isSyncing = false;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(triggerSync.rejected, (state, action) => {
        state.isSyncing = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setOnlineStatus,
  updateLastSync,
  incrementPendingOperations,
  decrementPendingOperations,
  incrementFailedOperations,
  resetFailedOperations,
} = syncSlice.actions;

export default syncSlice.reducer;
