/**
 * Modern design system for Dukalink POS
 * Inspired by web design with enhanced typography and spacing
 */

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

export const BorderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  xxl: 32,
  round: 9999,
};

// Font families based on web design
export const FontFamily = {
  sans: "Poppins_400Regular", // Primary font family
  sansMedium: "Poppins_500Medium",
  sansSemiBold: "Poppins_600SemiBold",
  sansBold: "Poppins_700Bold",
  serif: "NotoSerifJP_400Regular", // For headings and emphasis
  serifSemiBold: "NotoSerifJP_600SemiBold",
  serifBold: "NotoSerifJP_700Bold",
  mono: "SpaceMono", // For code and numbers
  system: "System", // Fallback system font
};

// Font sizes optimized for mobile POS interface (smaller, more practical)
export const FontSize = {
  xs: 11, // Very small text (badges, labels)
  sm: 13, // Small text (captions, secondary info)
  base: 15, // Base text (body, most UI elements)
  lg: 17, // Large text (important info, buttons)
  xl: 19, // Extra large (section headers)
  "2xl": 22, // 2X large (page titles)
  "3xl": 26, // 3X large (main headings)
  "4xl": 32, // 4X large (display text, rarely used)
};

// Line heights based on web CSS variables
export const LineHeight = {
  tight: 1.2, // --line-height-tight
  normal: 1.5, // --line-height-normal
  relaxed: 1.75, // --line-height-relaxed
};

// Font weights
export const FontWeight = {
  normal: "400" as const,
  medium: "500" as const,
  semibold: "600" as const,
  bold: "700" as const,
};

// Enhanced typography system optimized for mobile POS interface
export const Typography = {
  // Display headings (using sans-serif for better readability)
  display1: {
    fontFamily: FontFamily.sansBold,
    fontSize: FontSize["3xl"],
    lineHeight: FontSize["3xl"] * LineHeight.tight,
  },
  display2: {
    fontFamily: FontFamily.sansBold,
    fontSize: FontSize["2xl"],
    lineHeight: FontSize["2xl"] * LineHeight.tight,
  },

  // Headings (using sans-serif for modern POS interface)
  h1: {
    fontFamily: FontFamily.sansBold,
    fontSize: FontSize.xl,
    lineHeight: FontSize.xl * LineHeight.tight,
  },
  h2: {
    fontFamily: FontFamily.sansSemiBold,
    fontSize: FontSize.lg,
    lineHeight: FontSize.lg * LineHeight.tight,
  },
  h3: {
    fontFamily: FontFamily.sansSemiBold,
    fontSize: FontSize.base,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  h4: {
    fontFamily: FontFamily.sansMedium,
    fontSize: FontSize.sm,
    lineHeight: FontSize.sm * LineHeight.normal,
  },

  // Body text (using sans-serif font)
  body: {
    fontFamily: FontFamily.sans,
    fontSize: FontSize.base,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  bodyMedium: {
    fontFamily: FontFamily.sansMedium,
    fontSize: FontSize.base,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  bodySemiBold: {
    fontFamily: FontFamily.sansSemiBold,
    fontSize: FontSize.base,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  bodyLarge: {
    fontFamily: FontFamily.sans,
    fontSize: FontSize.lg,
    lineHeight: FontSize.lg * LineHeight.normal,
  },

  // Smaller text
  caption: {
    fontFamily: FontFamily.sans,
    fontSize: FontSize.sm,
    lineHeight: FontSize.sm * LineHeight.normal,
  },
  captionMedium: {
    fontFamily: FontFamily.sansMedium,
    fontSize: FontSize.sm,
    lineHeight: FontSize.sm * LineHeight.normal,
  },
  small: {
    fontFamily: FontFamily.sans,
    fontSize: FontSize.xs,
    lineHeight: FontSize.xs * LineHeight.normal,
  },

  // Interactive elements
  button: {
    fontFamily: FontFamily.sansSemiBold,
    fontSize: FontSize.base,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  buttonLarge: {
    fontFamily: FontFamily.sansSemiBold,
    fontSize: FontSize.lg,
    lineHeight: FontSize.lg * LineHeight.normal,
  },
  buttonSmall: {
    fontFamily: FontFamily.sansSemiBold,
    fontSize: FontSize.sm,
    lineHeight: FontSize.sm * LineHeight.normal,
  },

  // Special purpose
  label: {
    fontFamily: FontFamily.sansMedium,
    fontSize: FontSize.sm,
    lineHeight: FontSize.sm * LineHeight.normal,
  },
  overline: {
    fontFamily: FontFamily.sansMedium,
    fontSize: FontSize.xs,
    lineHeight: FontSize.xs * LineHeight.normal,
    textTransform: "uppercase" as const,
    letterSpacing: 0.5,
  },

  // Monospace for numbers and codes
  mono: {
    fontFamily: FontFamily.mono,
    fontSize: FontSize.base,
    lineHeight: FontSize.base * LineHeight.normal,
  },
  monoSmall: {
    fontFamily: FontFamily.mono,
    fontSize: FontSize.sm,
    lineHeight: FontSize.sm * LineHeight.normal,
  },
};

// Shadow system for elevation and depth
export const Shadows = {
  xs: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 16,
  },
};

// Duplicate removed - using the more comprehensive Shadows above

export const Layout = {
  headerHeight: 60,
  sidebarWidth: 280,
  gridGap: 16,
  cardMinHeight: 120,
};
