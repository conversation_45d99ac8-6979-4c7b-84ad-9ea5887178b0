/**
 * Test Backend Connectivity for React Native Integration
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";

async function testBackendConnectivity() {
  console.log(
    "🔧 Testing Backend Connectivity for React Native Integration...\n"
  );

  try {
    // Test 1: Health Check
    console.log("📋 Test 1: Health Check");
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log("✅ PASSED: Health Check");
    console.log(`   Status: ${healthResponse.data.status}`);
    console.log(`   Auth: ${healthResponse.data.auth}`);
    console.log(`   Commission: ${healthResponse.data.commission}`);
    console.log(`   Terminals: ${healthResponse.data.terminals}`);
    console.log("");

    // Test 2: Login with Manager (for React Native)
    console.log("📋 Test 2: Manager Login (React Native Compatible)");
    const loginResponse = await axios.post(`${BASE_URL}/api/pos/login`, {
      username: "manager1",
      password: "manager123",
    });

    if (loginResponse.data.success) {
      console.log("✅ PASSED: Manager Login");
      console.log(`   User: ${loginResponse.data.data.user.name}`);
      console.log(`   Role: ${loginResponse.data.data.user.role}`);
      console.log(
        `   Permissions: ${loginResponse.data.data.user.permissions.length} permissions`
      );
      console.log(
        `   Token: ${loginResponse.data.data.token.substring(0, 20)}...`
      );
    } else {
      console.log("❌ FAILED: Manager Login");
      return;
    }

    const token = loginResponse.data.data.token;
    const authHeaders = { Authorization: `Bearer ${token}` };
    console.log("");

    // Test 3: Get Sales Agents (React Native Format)
    console.log("📋 Test 3: Get Sales Agents (React Native Format)");
    const agentsResponse = await axios.get(`${BASE_URL}/api/sales-agents`, {
      headers: authHeaders,
    });

    if (agentsResponse.data.success) {
      console.log("✅ PASSED: Get Sales Agents");
      console.log(
        `   Found: ${agentsResponse.data.data.salesAgents.length} agents`
      );

      // Show first agent details for React Native compatibility
      const firstAgent = agentsResponse.data.data.salesAgents[0];
      console.log("   Sample Agent:");
      console.log(`     • ID: ${firstAgent.id}`);
      console.log(`     • Name: ${firstAgent.name}`);
      console.log(`     • Email: ${firstAgent.email}`);
      console.log(`     • Commission Rate: ${firstAgent.commissionRate}%`);
      console.log(`     • Territory: ${firstAgent.territory}`);
      console.log(`     • Customer Count: ${firstAgent.customerCount}`);
      console.log(`     • Total Sales: KSh ${firstAgent.totalSales}`);
      console.log(`     • Active: ${firstAgent.active}`);
    } else {
      console.log("❌ FAILED: Get Sales Agents");
    }
    console.log("");

    // Test 4: Commission Configuration (React Native Format)
    console.log("📋 Test 4: Commission Configuration (React Native Format)");
    const configResponse = await axios.get(
      `${BASE_URL}/api/discounts/configuration`,
      {
        headers: authHeaders,
      }
    );

    if (configResponse.data.success) {
      console.log("✅ PASSED: Commission Configuration");
      const config = configResponse.data.data.configuration;
      console.log("   Configuration:");
      console.log(`     • Staff Discount Rate: ${config.staff_discount_rate}%`);
      console.log(`     • Agent Discount Rate: ${config.agent_discount_rate}%`);
      console.log(`     • Loyalty Multiplier: ${config.loyalty_multiplier}x`);
      console.log(`     • Min Order Amount: KSh ${config.min_order_amount}`);
      console.log(`     • Max Discount: ${config.max_discount_percentage}%`);
      console.log(`     • System Enabled: ${config.enabled}`);
    } else {
      console.log("❌ FAILED: Commission Configuration");
    }
    console.log("");

    // Test 5: Commission Calculation (React Native Format)
    console.log("📋 Test 5: Commission Calculation (React Native Format)");
    const calcResponse = await axios.post(
      `${BASE_URL}/api/discounts/calculate`,
      {
        cartData: { subtotal: "200.00" },
        staffId: "pos-002", // Manager
        salesAgentId: "agent-001", // Alice Johnson
      },
      {
        headers: { ...authHeaders, "Content-Type": "application/json" },
      }
    );

    if (calcResponse.data.success) {
      console.log("✅ PASSED: Commission Calculation");
      const discount = calcResponse.data.data.discount;
      console.log("   Calculation Results:");
      console.log(`     • Subtotal: KSh ${discount.subtotal}`);
      console.log(`     • Total Discount: KSh ${discount.totalDiscount}`);
      console.log(`     • Discount Rate: ${discount.rate}%`);
      console.log(`     • Staff Discount: KSh ${discount.staffDiscount}`);
      console.log(`     • Agent Discount: KSh ${discount.agentDiscount}`);
      console.log(`     • Loyalty Bonus: KSh ${discount.loyaltyBonus}`);
      console.log(`     • Final Amount: KSh ${discount.finalAmount}`);
    } else {
      console.log("❌ FAILED: Commission Calculation");
    }
    console.log("");

    // Test 6: Store Products (Shopify Integration)
    console.log("📋 Test 6: Store Products (Shopify Integration)");
    const productsResponse = await axios.get(
      `${BASE_URL}/api/store/products?limit=3`,
      {
        headers: authHeaders,
      }
    );

    if (productsResponse.data.success) {
      console.log("✅ PASSED: Store Products");
      console.log(
        `   Found: ${productsResponse.data.data.products.length} products`
      );

      // Show first product for React Native compatibility
      const firstProduct = productsResponse.data.data.products[0];
      console.log("   Sample Product:");
      console.log(`     • ID: ${firstProduct.id}`);
      console.log(`     • Title: ${firstProduct.title}`);
      console.log(`     • Price: KSh ${firstProduct.price}`);
      console.log(`     • Inventory: ${firstProduct.inventory_quantity} units`);
    } else {
      console.log("❌ FAILED: Store Products");
    }
    console.log("");

    // Test 7: Store Customers (Shopify Integration)
    console.log("📋 Test 7: Store Customers (Shopify Integration)");
    const customersResponse = await axios.get(
      `${BASE_URL}/api/store/customers?limit=3`,
      {
        headers: authHeaders,
      }
    );

    if (customersResponse.data.success) {
      console.log("✅ PASSED: Store Customers");
      console.log(
        `   Found: ${customersResponse.data.data.customers.length} customers`
      );

      // Show first customer for React Native compatibility
      const firstCustomer = customersResponse.data.data.customers[0];
      console.log("   Sample Customer:");
      console.log(`     • ID: ${firstCustomer.id}`);
      console.log(
        `     • Name: ${firstCustomer.first_name} ${firstCustomer.last_name}`
      );
      console.log(`     • Email: ${firstCustomer.email}`);
      console.log(`     • Orders Count: ${firstCustomer.orders_count}`);
    } else {
      console.log("❌ FAILED: Store Customers");
    }
    console.log("");

    console.log("🎉 Backend Connectivity Test Completed!");
    console.log(
      "✅ All endpoints are working correctly for React Native integration."
    );
    console.log("🚀 Ready to proceed with React Native frontend integration!");
  } catch (error) {
    console.error("❌ Backend connectivity test failed:", error.message);
    if (error.response) {
      console.error("   Status:", error.response.status);
      console.error("   Data:", error.response.data);
    }
  }
}

// Run test
testBackendConnectivity();
