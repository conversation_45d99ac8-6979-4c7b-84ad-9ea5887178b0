import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import React from "react";
import { Modal, StyleSheet, Text, View, Platform } from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

export interface ConfirmationAction {
  title: string;
  onPress: () => void;
  variant?: "primary" | "outline" | "ghost" | "secondary";
  icon?: string;
}

interface ConfirmationModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  message: string;

  // New interface for simple confirm/cancel pattern
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmStyle?: "default" | "destructive";
  showCancel?: boolean;

  // Original interface for complex actions
  actions?: ConfirmationAction[];

  icon?: string;
  iconColor?: string;
  showCloseButton?: boolean;
}

export function ConfirmationModal({
  visible,
  onClose,
  title,
  message,

  // Simple interface props
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  confirmStyle = "default",
  showCancel = true,

  // Complex interface props
  actions,

  icon = "questionmark.circle",
  iconColor,
  showCloseButton = true,
}: ConfirmationModalProps) {
  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");
  const warningColor = "#F59E0B"; // Amber color for warnings
  const { getModalSize, spacingMultiplier } = useResponsiveLayout();

  const defaultIconColor = iconColor || warningColor;
  const modalSize = getModalSize("small");

  // Generate actions from simple interface if actions array not provided
  const finalActions = React.useMemo(() => {
    if (actions && actions.length > 0) {
      return actions;
    }

    // Generate actions from simple interface
    const generatedActions: ConfirmationAction[] = [];

    if (showCancel && cancelText && onCancel) {
      generatedActions.push({
        title: cancelText,
        onPress: onCancel,
        variant: "outline",
      });
    }

    if (confirmText && onConfirm) {
      generatedActions.push({
        title: confirmText,
        onPress: onConfirm,
        variant: confirmStyle === "destructive" ? "secondary" : "primary",
      });
    }

    return generatedActions;
  }, [
    actions,
    confirmText,
    cancelText,
    onConfirm,
    onCancel,
    confirmStyle,
    showCancel,
  ]);

  const handleActionPress = (action: ConfirmationAction) => {
    action.onPress();
    // Only call onClose if we're using the complex actions interface
    // For simple interface, onConfirm/onCancel handle their own modal closing
    if (actions && actions.length > 0) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}} // Disable Android back button
    >
      <View style={styles.overlay}>
        <View
          style={[
            styles.modal,
            {
              backgroundColor,
              borderColor,
              padding: Spacing.lg * spacingMultiplier,
              width: modalSize.width as any,
              maxWidth: modalSize.maxWidth,
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: defaultIconColor + "20" },
                ]}
              >
                <IconSymbol name={icon as any} size={32} color={defaultIconColor} />
              </View>
              <Text style={[styles.title, { color: textColor }]}>{title}</Text>
            </View>
          </View>

          {/* Message */}
          <View style={styles.messageSection}>
            <Text style={[styles.message, { color: textSecondary }]}>
              {message}
            </Text>
          </View>

          {/* Actions */}
          <View style={styles.actionsSection}>
            {finalActions.map((action, index) => (
              <ModernButton
                key={index}
                title={action.title}
                onPress={() => handleActionPress(action)}
                variant={action.variant || "outline"}
                icon={
                  action.icon ? (
                    <IconSymbol name={action.icon as any} size={16} color="white" />
                  ) : undefined
                }
                style={[
                  styles.actionButton,
                  finalActions.length === 2 ? styles.halfWidthButton : {},
                ]}
              />
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modal: {
    borderRadius: 16,
    borderWidth: 1,
    // Responsive sizing handled by modalSize prop
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: Spacing.lg,
  },
  headerContent: {
    flex: 1,
    alignItems: "center",
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Spacing.md,
  },
  title: {
    ...Typography.h3,
    fontWeight: "600",
    textAlign: "center",
  },
  messageSection: {
    marginBottom: Spacing.lg,
  },
  message: {
    ...Typography.body,
    textAlign: "center",
    lineHeight: 22,
  },
  actionsSection: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.md,
  },
  actionButton: {
    flex: 1,
    minWidth: "100%",
  },
  halfWidthButton: {
    minWidth: "45%",
    flex: 0,
  },
});
