/**
 * Query Key Factory
 * 
 * Centralized query key management for consistent caching and invalidation.
 * Follows TanStack Query best practices for hierarchical key structure.
 */

export const queryKeys = {
  // Store-related queries
  store: {
    all: ['store'] as const,
    info: () => [...queryKeys.store.all, 'info'] as const,
    locations: () => [...queryKeys.store.all, 'locations'] as const,
    location: (id: string) => [...queryKeys.store.locations(), id] as const,
  },

  // Product-related queries
  products: {
    all: ['products'] as const,
    lists: () => [...queryKeys.products.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.products.lists(), filters] as const,
    details: () => [...queryKeys.products.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.products.details(), id] as const,
    search: (query: string) => [...queryKeys.products.all, 'search', query] as const,
    inventory: (id: string) => [...queryKeys.products.detail(id), 'inventory'] as const,
  },

  // Customer-related queries
  customers: {
    all: ['customers'] as const,
    lists: () => [...queryKeys.customers.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.customers.lists(), filters] as const,
    details: () => [...queryKeys.customers.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.customers.details(), id] as const,
    search: (query: string) => [...queryKeys.customers.all, 'search', query] as const,
  },

  // Order-related queries
  orders: {
    all: ['orders'] as const,
    lists: () => [...queryKeys.orders.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.orders.lists(), filters] as const,
    details: () => [...queryKeys.orders.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.orders.details(), id] as const,
    history: (customerId: string) => [...queryKeys.orders.all, 'history', customerId] as const,
  },

  // Staff-related queries
  staff: {
    all: ['staff'] as const,
    lists: () => [...queryKeys.staff.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.staff.lists(), filters] as const,
    details: () => [...queryKeys.staff.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.staff.details(), id] as const,
    current: () => [...queryKeys.staff.all, 'current'] as const,
  },

  // Sales Agent-related queries
  salesAgents: {
    all: ['salesAgents'] as const,
    lists: () => [...queryKeys.salesAgents.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.salesAgents.lists(), filters] as const,
    details: () => [...queryKeys.salesAgents.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.salesAgents.details(), id] as const,
    performance: (id: string) => [...queryKeys.salesAgents.detail(id), 'performance'] as const,
  },

  // Loyalty-related queries
  loyalty: {
    all: ['loyalty'] as const,
    customer: (customerId: string) => [...queryKeys.loyalty.all, 'customer', customerId] as const,
    transactions: (customerId: string) => [...queryKeys.loyalty.customer(customerId), 'transactions'] as const,
    discounts: (customerId: string) => [...queryKeys.loyalty.customer(customerId), 'discounts'] as const,
    tiers: () => [...queryKeys.loyalty.all, 'tiers'] as const,
    leaderboard: () => [...queryKeys.loyalty.all, 'leaderboard'] as const,
    analytics: () => [...queryKeys.loyalty.all, 'analytics'] as const,
  },

  // Discount-related queries
  discounts: {
    all: ['discounts'] as const,
    lists: () => [...queryKeys.discounts.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.discounts.lists(), filters] as const,
    details: () => [...queryKeys.discounts.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.discounts.details(), id] as const,
    active: () => [...queryKeys.discounts.all, 'active'] as const,
    customer: (customerId: string) => [...queryKeys.discounts.all, 'customer', customerId] as const,
  },

  // Inventory-related queries
  inventory: {
    all: ['inventory'] as const,
    product: (productId: string) => [...queryKeys.inventory.all, 'product', productId] as const,
    location: (locationId: string) => [...queryKeys.inventory.all, 'location', locationId] as const,
    sync: () => [...queryKeys.inventory.all, 'sync'] as const,
  },

  // Sync-related queries
  sync: {
    all: ['sync'] as const,
    status: () => [...queryKeys.sync.all, 'status'] as const,
    history: () => [...queryKeys.sync.all, 'history'] as const,
    trigger: (type: string) => [...queryKeys.sync.all, 'trigger', type] as const,
  },

  // Analytics-related queries
  analytics: {
    all: ['analytics'] as const,
    sales: () => [...queryKeys.analytics.all, 'sales'] as const,
    performance: () => [...queryKeys.analytics.all, 'performance'] as const,
    inventory: () => [...queryKeys.analytics.all, 'inventory'] as const,
    loyalty: () => [...queryKeys.analytics.all, 'loyalty'] as const,
  },

  // Authentication-related queries
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
    permissions: () => [...queryKeys.auth.all, 'permissions'] as const,
    session: () => [...queryKeys.auth.all, 'session'] as const,
  },
} as const;

// Type helpers for query keys
export type QueryKeys = typeof queryKeys;

// Utility function to invalidate related queries
export const getInvalidationKeys = {
  // When a product is updated, invalidate related queries
  onProductUpdate: (productId: string) => [
    queryKeys.products.detail(productId),
    queryKeys.products.lists(),
    queryKeys.inventory.product(productId),
  ],

  // When a customer is updated, invalidate related queries
  onCustomerUpdate: (customerId: string) => [
    queryKeys.customers.detail(customerId),
    queryKeys.customers.lists(),
    queryKeys.loyalty.customer(customerId),
    queryKeys.orders.history(customerId),
  ],

  // When an order is created, invalidate related queries
  onOrderCreate: (customerId: string, productIds: string[]) => [
    queryKeys.orders.lists(),
    queryKeys.customers.detail(customerId),
    queryKeys.loyalty.customer(customerId),
    ...productIds.map(id => queryKeys.inventory.product(id)),
  ],

  // When loyalty data changes, invalidate related queries
  onLoyaltyUpdate: (customerId: string) => [
    queryKeys.loyalty.customer(customerId),
    queryKeys.loyalty.transactions(customerId),
    queryKeys.loyalty.discounts(customerId),
    queryKeys.customers.detail(customerId),
  ],
};
