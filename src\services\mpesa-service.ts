/**
 * M-Pesa Integration Service for Dukalink POS
 * Handles M-Pesa STK Push and payment verification
 */

export interface MpesaConfig {
  consumerKey: string;
  consumerSecret: string;
  businessShortCode: string;
  passkey: string;
  environment: "sandbox" | "production";
}

export interface MpesaPaymentRequest {
  phoneNumber: string;
  amount: number;
  accountReference: string;
  transactionDesc: string;
}

export interface MpesaPaymentResponse {
  success: boolean;
  checkoutRequestId?: string;
  merchantRequestId?: string;
  responseCode?: string;
  responseDescription?: string;
  customerMessage?: string;
  error?: string;
}

export interface MpesaPaymentStatus {
  success: boolean;
  resultCode?: string;
  resultDesc?: string;
  mpesaReceiptNumber?: string;
  transactionDate?: string;
  phoneNumber?: string;
  amount?: number;
  error?: string;
}

export class MpesaService {
  private config: MpesaConfig;
  private baseUrl: string;
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor(config: MpesaConfig) {
    this.config = config;
    this.baseUrl =
      config.environment === "production"
        ? "https://api.safaricom.co.ke"
        : "https://sandbox.safaricom.co.ke";
  }

  /**
   * Get OAuth access token from M-Pesa API
   */
  private async getAccessToken(): Promise<string> {
    // Check if we have a valid token
    if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
      return this.accessToken;
    }

    try {
      const credentials = Buffer.from(
        `${this.config.consumerKey}:${this.config.consumerSecret}`
      ).toString("base64");

      const response = await fetch(
        `${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
        {
          method: "GET",
          headers: {
            Authorization: `Basic ${credentials}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to get access token: ${response.statusText}`);
      }

      const data = await response.json();
      this.accessToken = data.access_token;

      // Set expiry time (usually 1 hour, but we'll refresh 5 minutes early)
      this.tokenExpiry = new Date(Date.now() + (data.expires_in - 300) * 1000);

      return this.accessToken;
    } catch (error) {
      console.error("Error getting M-Pesa access token:", error);
      throw new Error("Failed to authenticate with M-Pesa API");
    }
  }

  /**
   * Generate timestamp for M-Pesa API
   */
  private generateTimestamp(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hour = String(now.getHours()).padStart(2, "0");
    const minute = String(now.getMinutes()).padStart(2, "0");
    const second = String(now.getSeconds()).padStart(2, "0");

    return `${year}${month}${day}${hour}${minute}${second}`;
  }

  /**
   * Generate password for M-Pesa API
   */
  private generatePassword(timestamp: string): string {
    const data = `${this.config.businessShortCode}${this.config.passkey}${timestamp}`;
    return Buffer.from(data).toString("base64");
  }

  /**
   * Format phone number to M-Pesa format (254XXXXXXXXX)
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    const cleanPhone = phoneNumber.replace(/\D/g, "");

    // Convert to M-Pesa format
    if (cleanPhone.startsWith("0")) {
      return "254" + cleanPhone.substring(1);
    } else if (cleanPhone.startsWith("254")) {
      return cleanPhone;
    } else if (cleanPhone.length === 9) {
      return "254" + cleanPhone;
    }

    throw new Error("Invalid phone number format");
  }

  /**
   * Initiate M-Pesa STK Push payment
   */
  async initiatePayment(
    request: MpesaPaymentRequest
  ): Promise<MpesaPaymentResponse> {
    try {
      const accessToken = await this.getAccessToken();
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);
      const formattedPhone = this.formatPhoneNumber(request.phoneNumber);

      const payload = {
        BusinessShortCode: this.config.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: "CustomerPayBillOnline",
        Amount: Math.round(request.amount), // M-Pesa requires integer amounts
        PartyA: formattedPhone,
        PartyB: this.config.businessShortCode,
        PhoneNumber: formattedPhone,
        CallBackURL: "https://your-callback-url.com/mpesa/callback", // Replace with your callback URL
        AccountReference: request.accountReference,
        TransactionDesc: request.transactionDesc,
      };

      const response = await fetch(
        `${this.baseUrl}/mpesa/stkpush/v1/processrequest`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (response.ok && data.ResponseCode === "0") {
        return {
          success: true,
          checkoutRequestId: data.CheckoutRequestID,
          merchantRequestId: data.MerchantRequestID,
          responseCode: data.ResponseCode,
          responseDescription: data.ResponseDescription,
          customerMessage: data.CustomerMessage,
        };
      } else {
        return {
          success: false,
          error:
            data.ResponseDescription ||
            data.errorMessage ||
            "Payment initiation failed",
        };
      }
    } catch (error) {
      console.error("Error initiating M-Pesa payment:", error);
      return {
        success: false,
        error: "Failed to initiate payment. Please try again.",
      };
    }
  }

  /**
   * Check payment status using STK Push Query
   */
  async checkPaymentStatus(
    checkoutRequestId: string
  ): Promise<MpesaPaymentStatus> {
    try {
      const accessToken = await this.getAccessToken();
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);

      const payload = {
        BusinessShortCode: this.config.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId,
      };

      const response = await fetch(
        `${this.baseUrl}/mpesa/stkpushquery/v1/query`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (response.ok) {
        return {
          success: data.ResultCode === "0",
          resultCode: data.ResultCode,
          resultDesc: data.ResultDesc,
          mpesaReceiptNumber: data.MpesaReceiptNumber,
          transactionDate: data.TransactionDate,
          phoneNumber: data.PhoneNumber,
          amount: data.Amount,
        };
      } else {
        return {
          success: false,
          error: data.ResponseDescription || "Failed to check payment status",
        };
      }
    } catch (error) {
      console.error("Error checking M-Pesa payment status:", error);
      return {
        success: false,
        error: "Failed to check payment status",
      };
    }
  }

  /**
   * Simulate payment for development/testing
   */
  async simulatePayment(
    request: MpesaPaymentRequest
  ): Promise<MpesaPaymentResponse> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Simulate success/failure based on phone number
    const formattedPhone = this.formatPhoneNumber(request.phoneNumber);
    const shouldSucceed = !formattedPhone.endsWith("0000"); // Fail if phone ends with 0000

    if (shouldSucceed) {
      return {
        success: true,
        checkoutRequestId: `ws_CO_${Date.now()}`,
        merchantRequestId: `29115-34620561-1`,
        responseCode: "0",
        responseDescription: "Success. Request accepted for processing",
        customerMessage: `Payment request sent to ${formattedPhone}. Please check your phone and enter your M-Pesa PIN to complete the payment.`,
      };
    } else {
      return {
        success: false,
        error:
          "Payment request failed. Please check the phone number and try again.",
      };
    }
  }

  /**
   * Simulate payment status check for development
   */
  async simulatePaymentStatus(
    checkoutRequestId: string
  ): Promise<MpesaPaymentStatus> {
    // Simulate processing delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Simulate success for demo purposes
    return {
      success: true,
      resultCode: "0",
      resultDesc: "The service request is processed successfully.",
      mpesaReceiptNumber: `NLJ7RT61SV`,
      transactionDate: new Date().toISOString(),
      phoneNumber: "254712345678",
      amount: 100,
    };
  }
}

// Default configuration for development
export const createMpesaService = (
  environment: "sandbox" | "production" = "sandbox"
): MpesaService => {
  const config: MpesaConfig = {
    consumerKey:
      process.env.EXPO_PUBLIC_MPESA_CONSUMER_KEY || "your_consumer_key",
    consumerSecret:
      process.env.EXPO_PUBLIC_MPESA_CONSUMER_SECRET || "your_consumer_secret",
    businessShortCode: process.env.EXPO_PUBLIC_MPESA_SHORTCODE || "174379",
    passkey: process.env.EXPO_PUBLIC_MPESA_PASSKEY || "your_passkey",
    environment,
  };

  return new MpesaService(config);
};
