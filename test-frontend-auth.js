/**
 * Test Frontend Authentication for Loyalty API
 * 
 * This script tests if the frontend can make authenticated loyalty API calls
 * using the same authentication mechanism as the checkout process.
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";

// Test customer ID (one that exists in the database)
const TEST_CUSTOMER_ID = "8095960301705";

// Authentication credentials
const AUTH_CREDENTIALS = {
  username: "manager1",
  password: "manager123",
};

let authToken = null;

const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use((config) => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// Authenticate with POS credentials
async function authenticate() {
  console.log("🔐 Authenticating with POS credentials...");
  
  try {
    const response = await apiClient.post("/api/pos/login", {
      username: AUTH_CREDENTIALS.username,
      password: AUTH_CREDENTIALS.password,
    });

    if (response.data && response.data.data && response.data.data.token) {
      authToken = response.data.data.token;
      console.log("   ✅ Authentication successful");
      console.log(`   👤 User: ${response.data.data.user?.name} (${response.data.data.user?.role})`);
      console.log(`   🔑 Token: ${authToken.substring(0, 20)}...`);
      return true;
    } else {
      console.log("   ❌ Authentication failed: No token received");
      return false;
    }
  } catch (error) {
    console.log("   ❌ Authentication failed:", error.response?.data?.error || error.message);
    return false;
  }
}

// Test loyalty API calls that would be made during checkout
async function testLoyaltyAPICalls() {
  console.log("\n🧪 Testing Loyalty API calls that happen during checkout...\n");

  // Test 1: Get customer loyalty summary (happens during customer selection)
  console.log("1️⃣ Testing GET /api/loyalty/customers/:customerId/summary...");
  try {
    const summaryResponse = await apiClient.get(`/api/loyalty/customers/${TEST_CUSTOMER_ID}/summary`);
    console.log("   ✅ Customer summary retrieved:", {
      points: summaryResponse.data.data.loyaltyPoints,
      tier: summaryResponse.data.data.tier,
      customerId: summaryResponse.data.data.customerId
    });
  } catch (error) {
    console.log("   ❌ Customer summary failed:", error.response?.data?.error || error.message);
    if (error.response?.status === 401) {
      console.log("   🚨 AUTHENTICATION ERROR - This could be the issue!");
    }
  }

  // Test 2: Add loyalty points (happens during order completion)
  console.log("\n2️⃣ Testing POST /api/loyalty/customers/:customerId/points/add...");
  try {
    const addPointsResponse = await apiClient.post(`/api/loyalty/customers/${TEST_CUSTOMER_ID}/points/add`, {
      orderTotal: 1000,
      orderId: `FRONTEND_TEST_${Date.now()}`,
      salesAgentId: "agent-001"
    });
    console.log("   ✅ Points added successfully:", {
      pointsAdded: addPointsResponse.data.data.pointsAdded,
      newBalance: addPointsResponse.data.data.newBalance,
      transactionId: addPointsResponse.data.data.transactionId
    });
  } catch (error) {
    console.log("   ❌ Add points failed:", error.response?.data?.error || error.message);
    if (error.response?.status === 401) {
      console.log("   🚨 AUTHENTICATION ERROR - This is likely the root cause!");
    }
    if (error.response?.status === 403) {
      console.log("   🚨 PERMISSION ERROR - User lacks loyalty management permissions!");
    }
  }

  // Test 3: Verify token is still valid
  console.log("\n3️⃣ Testing token validity with /api/pos/verify...");
  try {
    const verifyResponse = await apiClient.get("/api/pos/verify");
    console.log("   ✅ Token is valid:", {
      user: verifyResponse.data.data.user?.name,
      role: verifyResponse.data.data.user?.role
    });
  } catch (error) {
    console.log("   ❌ Token verification failed:", error.response?.data?.error || error.message);
    if (error.response?.status === 401) {
      console.log("   🚨 TOKEN EXPIRED OR INVALID!");
    }
  }
}

// Test authentication flow that mimics frontend
async function testFrontendAuthFlow() {
  console.log("\n🔄 Testing Frontend-style Authentication Flow...\n");

  // Simulate how the frontend stores and retrieves tokens
  let storedToken = null;

  // Step 1: Login and store token
  console.log("📱 Step 1: Login and store token (like frontend does)...");
  try {
    const loginResponse = await apiClient.post("/api/pos/login", AUTH_CREDENTIALS);
    if (loginResponse.data?.data?.token) {
      storedToken = loginResponse.data.data.token;
      console.log("   ✅ Token stored successfully");
    }
  } catch (error) {
    console.log("   ❌ Login failed:", error.message);
    return;
  }

  // Step 2: Create new API client with stored token (like frontend does)
  console.log("\n📱 Step 2: Create new API client with stored token...");
  const frontendStyleClient = axios.create({
    baseURL: BASE_URL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${storedToken}`
    },
  });

  // Step 3: Test loyalty API call with new client
  console.log("\n📱 Step 3: Test loyalty API call with frontend-style client...");
  try {
    const loyaltyResponse = await frontendStyleClient.post(`/api/loyalty/customers/${TEST_CUSTOMER_ID}/points/add`, {
      orderTotal: 500,
      orderId: `FRONTEND_STYLE_TEST_${Date.now()}`,
      salesAgentId: "agent-001"
    });
    console.log("   ✅ Frontend-style loyalty call successful:", {
      pointsAdded: loyaltyResponse.data.data.pointsAdded,
      newBalance: loyaltyResponse.data.data.newBalance
    });
  } catch (error) {
    console.log("   ❌ Frontend-style loyalty call failed:", error.response?.data?.error || error.message);
    console.log("   📋 Status Code:", error.response?.status);
    console.log("   📋 Full Error:", JSON.stringify(error.response?.data, null, 2));
  }
}

// Run all tests
async function runAllTests() {
  console.log("🚀 Starting Frontend Authentication Test for Loyalty API...\n");

  // Test 1: Basic authentication
  const authSuccess = await authenticate();
  if (!authSuccess) {
    console.log("❌ Cannot proceed without authentication");
    return;
  }

  // Test 2: Loyalty API calls with authenticated client
  await testLoyaltyAPICalls();

  // Test 3: Frontend-style authentication flow
  await testFrontendAuthFlow();

  console.log("\n✅ All authentication tests completed!");
  console.log("\n📋 Summary:");
  console.log("   - If loyalty API calls failed with 401 errors, the issue is authentication");
  console.log("   - If loyalty API calls failed with 403 errors, the issue is permissions");
  console.log("   - If loyalty API calls succeeded, the issue is in the frontend checkout flow");
}

// Execute tests
runAllTests().catch(console.error);
