const express = require("express");
const router = express.Router();
const commissionDiscountService = require("../services/commission-discount-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");

// Calculate commission-based discount for a cart
router.post("/discounts/calculate", authenticateToken, async (req, res) => {
  try {
    const { cartData, staffId, salesAgentId, customerId } = req.body;

    // Validate required fields
    if (!cartData) {
      return res.status(400).json({
        success: false,
        error: "Cart data is required",
      });
    }

    // Use current user as staff if not specified
    const effectiveStaffId = staffId || req.user.id;

    const result = await commissionDiscountService.calculateCommissionDiscount(
      cartData,
      effectiveStaffId,
      salesAgentId,
      customerId
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          discount: result.discount,
          reason: result.reason,
          message: result.discount
            ? `Commission discount calculated: ${result.discount.rate.toFixed(
                1
              )}%`
            : result.reason,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Calculate discount error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to calculate discount",
    });
  }
});

// Create a commission-based discount
router.post("/discounts/create", authenticateToken, async (req, res) => {
  try {
    const { discountData, staffId, salesAgentId } = req.body;

    // Validate required fields
    if (!discountData || !discountData.cartData) {
      return res.status(400).json({
        success: false,
        error: "Discount data with cart information is required",
      });
    }

    if (!salesAgentId) {
      return res.status(400).json({
        success: false,
        error: "Sales agent ID is required for commission discounts",
      });
    }

    // Use current user as staff if not specified
    const effectiveStaffId = staffId || req.user.id;

    const result = await commissionDiscountService.createCommissionDiscount(
      discountData,
      effectiveStaffId,
      salesAgentId
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          discount: result.discount,
          calculation: result.calculation,
          message: "Commission discount created successfully",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Create discount error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to create discount",
    });
  }
});

// Get discount configuration
router.get("/discounts/configuration", authenticateToken, async (req, res) => {
  try {
    const configuration =
      await commissionDiscountService.getDiscountConfiguration();

    res.json({
      success: true,
      data: {
        configuration: configuration.configuration || configuration,
        loyaltyTiers: {
          bronze: "Base tier (1.0x multiplier)",
          silver: "Silver tier (1.2x multiplier)",
          gold: "Gold tier (1.5x multiplier)",
          platinum: "Platinum tier (2.0x multiplier)",
        },
      },
    });
  } catch (error) {
    console.error("Get discount configuration error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch discount configuration",
    });
  }
});

// Update discount configuration (requires manage_staff permission)
router.put(
  "/discounts/configuration",
  authenticateToken,
  requirePermission("manage_staff"),
  async (req, res) => {
    try {
      const newConfig = req.body;

      const result =
        await commissionDiscountService.updateDiscountConfiguration(newConfig);

      if (result.success) {
        res.json({
          success: true,
          data: {
            configuration: result.configuration,
            message: "Discount configuration updated successfully",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Update discount configuration error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update discount configuration",
      });
    }
  }
);

// Get customer loyalty tier
router.get(
  "/customers/:customerId/loyalty-tier",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;

      const loyaltyTier =
        await commissionDiscountService.getCustomerLoyaltyTier(customerId);

      res.json({
        success: true,
        data: {
          customerId: customerId,
          loyaltyTier: loyaltyTier,
          benefits: {
            bronze: "Base commission discounts",
            silver: "20% bonus on commission discounts",
            gold: "50% bonus on commission discounts",
            platinum: "100% bonus on commission discounts",
          }[loyaltyTier],
        },
      });
    } catch (error) {
      console.error("Get customer loyalty tier error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch customer loyalty tier",
      });
    }
  }
);

// Get discount analytics (requires view_reports permission)
router.get(
  "/discounts/analytics",
  authenticateToken,
  requirePermission("view_reports"),
  async (req, res) => {
    try {
      const { dateFrom, dateTo } = req.query;

      const dateRange = {};
      if (dateFrom) dateRange.dateFrom = dateFrom;
      if (dateTo) dateRange.dateTo = dateTo;

      const result = await commissionDiscountService.getDiscountAnalytics(
        dateRange
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            analytics: result.analytics,
            message: "Discount analytics retrieved successfully",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Get discount analytics error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch discount analytics",
      });
    }
  }
);

// Test discount calculation with sample data
router.post("/discounts/test", authenticateToken, async (req, res) => {
  try {
    // Sample cart data for testing
    const sampleCartData = {
      subtotal: 150.0,
      items: [
        {
          id: "test-item-1",
          title: "Test Product",
          price: 75.0,
          quantity: 2,
          commission_eligible: true,
        },
      ],
    };

    const { staffId, salesAgentId, customerId } = req.body;

    const result = await commissionDiscountService.calculateCommissionDiscount(
      sampleCartData,
      staffId || req.user.id,
      salesAgentId || "agent-001",
      customerId || "8095960301705"
    );

    res.json({
      success: true,
      data: {
        testData: {
          cartData: sampleCartData,
          staffId: staffId || req.user.id,
          salesAgentId: salesAgentId || "agent-001",
          customerId: customerId || "8095960301705",
        },
        result: result,
        message: "Test discount calculation completed",
      },
    });
  } catch (error) {
    console.error("Test discount calculation error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to test discount calculation",
    });
  }
});

module.exports = router;
