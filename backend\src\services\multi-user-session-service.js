/**
 * Multi-User Session Management Service
 *
 * Handles PIN-based user switching, session context management,
 * and audit trail for multi-user POS terminals.
 */

require("dotenv").config();
const bcrypt = require("bcrypt");
const crypto = require("crypto");
const { databaseManager } = require("../config/database-manager");

class MultiUserSessionService {
  constructor() {
    if (MultiUserSessionService.instance) {
      return MultiUserSessionService.instance;
    }

    this.databaseManager = databaseManager;

    // Configuration constants
    this.PIN_HASH_ROUNDS = 12;
    this.MAX_PIN_ATTEMPTS = 5;
    this.PIN_LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
    this.SESSION_CONTEXT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
    this.AUTO_SWITCH_BACK_TIMEOUT = 30 * 60 * 1000; // 30 minutes

    // In-memory caches
    this.activeSessions = new Map();
    this.switchTimeouts = new Map();

    MultiUserSessionService.instance = this;
  }

  static getInstance() {
    if (!MultiUserSessionService.instance) {
      MultiUserSessionService.instance = new MultiUserSessionService();
    }
    return MultiUserSessionService.instance;
  }

  /**
   * Initialize user PIN (for new users or PIN reset)
   */
  async initializeUserPin(staffId, pin, adminStaffId) {
    try {
      // Validate PIN format (4-6 digits)
      if (!/^\d{4,6}$/.test(pin)) {
        throw new Error("PIN must be 4-6 digits");
      }

      // Hash the PIN
      const pinHash = await bcrypt.hash(pin, this.PIN_HASH_ROUNDS);

      // Update staff record
      await this.databaseManager.executeQuery(
        `UPDATE pos_staff 
         SET pin = ?, pin_set_at = NOW(), pin_set_by = ?
         WHERE id = ? AND is_active = 1`,
        [pinHash, adminStaffId, staffId]
      );

      // Log PIN initialization
      await this.logSecurityEvent(staffId, "PIN_INITIALIZED", {
        adminStaffId,
        timestamp: new Date().toISOString(),
      });

      return { success: true, message: "PIN initialized successfully" };
    } catch (error) {
      console.error("PIN initialization error:", error);
      throw new Error("Failed to initialize PIN");
    }
  }

  /**
   * Validate PIN for user switching
   */
  async validatePin(staffId, pin, sessionId, ipAddress) {
    try {
      // Get staff with PIN and attempt tracking
      const [staffRows] = await this.databaseManager.executeQuery(
        `SELECT id, username, name, role, pin, pin_attempts, pin_locked_until,
                commission_rate, is_active
         FROM pos_staff 
         WHERE id = ? AND is_active = 1`,
        [staffId]
      );

      if (staffRows.length === 0) {
        throw new Error("Staff member not found or inactive");
      }

      const staff = staffRows[0];

      // Check if PIN is set
      if (!staff.pin) {
        throw new Error("PIN not set for this user");
      }

      // Check if account is locked
      if (
        staff.pin_locked_until &&
        new Date() < new Date(staff.pin_locked_until)
      ) {
        const lockTimeRemaining = Math.ceil(
          (new Date(staff.pin_locked_until) - new Date()) / 60000
        );
        throw new Error(
          `Account locked. Try again in ${lockTimeRemaining} minutes`
        );
      }

      // Validate PIN
      const isValidPin = await bcrypt.compare(pin, staff.pin);

      if (!isValidPin) {
        // Increment failed attempts
        const newAttempts = (staff.pin_attempts || 0) + 1;
        let lockUntil = null;

        if (newAttempts >= this.MAX_PIN_ATTEMPTS) {
          lockUntil = new Date(Date.now() + this.PIN_LOCKOUT_DURATION);
        }

        await this.databaseManager.executeQuery(
          `UPDATE pos_staff
           SET pin_attempts = ?, pin_locked_until = ?
           WHERE id = ?`,
          [newAttempts, lockUntil, staffId]
        );

        // Log failed attempt
        await this.logSecurityEvent(staffId, "PIN_FAILED", {
          sessionId,
          ipAddress,
          attempts: newAttempts,
          locked: !!lockUntil,
        });

        // Calculate remaining attempts (ensure it doesn't go below 0)
        const remainingAttempts = Math.max(
          0,
          this.MAX_PIN_ATTEMPTS - newAttempts
        );

        if (lockUntil) {
          throw new Error("Account locked due to too many failed PIN attempts");
        } else {
          throw new Error(
            `Invalid PIN. ${remainingAttempts} attempts remaining`
          );
        }
      }

      // Reset failed attempts on successful validation
      await this.databaseManager.executeQuery(
        `UPDATE pos_staff 
         SET pin_attempts = 0, pin_locked_until = NULL, last_pin_used = NOW()
         WHERE id = ?`,
        [staffId]
      );

      // Log successful PIN validation
      await this.logSecurityEvent(staffId, "PIN_VALIDATED", {
        sessionId,
        ipAddress,
      });

      return {
        success: true,
        staff: {
          id: staff.id,
          username: staff.username,
          name: staff.name,
          role: staff.role,
          commissionRate: staff.commission_rate,
        },
      };
    } catch (error) {
      console.error("PIN validation error:", error);
      throw error;
    }
  }

  /**
   * Switch user context within existing session
   */
  async switchUser(sessionId, targetStaffId, pin, reason = "manual_switch") {
    try {
      // Get current session context
      const sessionContext = await this.getSessionContext(sessionId);
      if (!sessionContext) {
        throw new Error("Session not found");
      }

      // Validate PIN for target user
      const pinValidation = await this.validatePin(
        targetStaffId,
        pin,
        sessionId,
        sessionContext.ipAddress
      );

      if (!pinValidation.success) {
        throw new Error("PIN validation failed");
      }

      const targetStaff = pinValidation.staff;

      // Get target staff permissions
      const permissions = await this.getStaffPermissions(targetStaffId);

      // Create user switch record
      const switchId = crypto.randomUUID();
      await this.databaseManager.executeQuery(
        `INSERT INTO pos_user_switches 
         (id, session_id, from_staff_id, to_staff_id, terminal_id, location_id,
          switch_reason, ip_address, user_agent)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          switchId,
          sessionId,
          sessionContext.currentUser.id,
          targetStaffId,
          sessionContext.terminalId,
          sessionContext.locationId,
          reason,
          sessionContext.ipAddress,
          sessionContext.userAgent,
        ]
      );

      // Update session context
      const newContext = {
        ...sessionContext,
        currentUser: {
          ...targetStaff,
          permissions,
        },
        userStack: [...sessionContext.userStack, sessionContext.currentUser],
        lastSwitchId: switchId,
        lastSwitchAt: new Date().toISOString(),
      };

      // Cache updated context
      this.activeSessions.set(sessionId, {
        context: newContext,
        cachedAt: Date.now(),
      });

      // Set auto-switch back timeout if configured
      if (this.AUTO_SWITCH_BACK_TIMEOUT > 0) {
        this.setAutoSwitchBackTimeout(sessionId, switchId);
      }

      // Log user switch
      await this.logSecurityEvent(targetStaffId, "USER_SWITCHED", {
        sessionId,
        fromStaffId: sessionContext.currentUser.id,
        toStaffId: targetStaffId,
        switchId,
        reason,
      });

      return {
        success: true,
        sessionContext: newContext,
        switchId,
      };
    } catch (error) {
      console.error("User switch error:", error);
      throw error;
    }
  }

  /**
   * Switch back to previous user
   */
  async switchBack(sessionId, switchId) {
    try {
      const sessionContext = await this.getSessionContext(sessionId);
      if (!sessionContext || sessionContext.userStack.length === 0) {
        throw new Error("No previous user to switch back to");
      }

      // Get the previous user from stack
      const previousUser = sessionContext.userStack.pop();

      // Update switch record
      await this.databaseManager.executeQuery(
        `UPDATE pos_user_switches 
         SET switched_back_at = NOW()
         WHERE id = ?`,
        [switchId]
      );

      // Update session context
      const newContext = {
        ...sessionContext,
        currentUser: previousUser,
        userStack: sessionContext.userStack,
        lastSwitchId: null,
        lastSwitchAt: new Date().toISOString(),
      };

      // Cache updated context
      this.activeSessions.set(sessionId, {
        context: newContext,
        cachedAt: Date.now(),
      });

      // Clear auto-switch timeout
      this.clearAutoSwitchBackTimeout(sessionId);

      // Log switch back
      await this.logSecurityEvent(previousUser.id, "USER_SWITCHED_BACK", {
        sessionId,
        switchId,
        fromStaffId: sessionContext.currentUser.id,
        toStaffId: previousUser.id,
      });

      return {
        success: true,
        sessionContext: newContext,
      };
    } catch (error) {
      console.error("Switch back error:", error);
      throw error;
    }
  }

  /**
   * Get current session context with user stack
   */
  async getSessionContext(sessionId) {
    try {
      // Check cache first
      const cached = this.activeSessions.get(sessionId);
      if (
        cached &&
        Date.now() - cached.cachedAt < this.SESSION_CONTEXT_CACHE_TTL
      ) {
        return cached.context;
      }

      // Get session from database
      const [sessionRows] = await this.databaseManager.executeQuery(
        `SELECT s.*, st.id as staff_id, st.username, st.name, st.role, st.commission_rate
         FROM pos_sessions s
         JOIN pos_staff st ON s.staff_id = st.id
         WHERE s.id = ? AND s.expires_at > NOW()`,
        [sessionId]
      );

      if (sessionRows.length === 0) {
        return null;
      }

      const session = sessionRows[0];

      // Get staff permissions
      const permissions = await this.getStaffPermissions(session.staff_id);

      // Get current user switches for this session
      const [switchRows] = await this.databaseManager.executeQuery(
        `SELECT * FROM pos_user_switches 
         WHERE session_id = ? AND switched_back_at IS NULL
         ORDER BY switched_at DESC
         LIMIT 1`,
        [sessionId]
      );

      let currentUser = {
        id: session.staff_id,
        username: session.username,
        name: session.name,
        role: session.role,
        commissionRate: session.commission_rate,
        permissions,
      };

      let userStack = [];
      let lastSwitchId = null;
      let lastSwitchAt = null;

      // If there's an active switch, get the switched user
      if (switchRows.length > 0) {
        const activeSwitch = switchRows[0];
        lastSwitchId = activeSwitch.id;
        lastSwitchAt = activeSwitch.switched_at;

        // Get switched user details
        const [switchedUserRows] = await this.databaseManager.executeQuery(
          `SELECT id, username, name, role, commission_rate FROM pos_staff WHERE id = ?`,
          [activeSwitch.to_staff_id]
        );

        if (switchedUserRows.length > 0) {
          const switchedUser = switchedUserRows[0];
          const switchedPermissions = await this.getStaffPermissions(
            switchedUser.id
          );

          // Original user goes to stack, switched user becomes current
          userStack = [currentUser];
          currentUser = {
            ...switchedUser,
            commissionRate: switchedUser.commission_rate,
            permissions: switchedPermissions,
          };
        }
      }

      const context = {
        sessionId: session.id,
        primaryUser: {
          id: session.staff_id,
          username: session.username,
          name: session.name,
          role: session.role,
          commissionRate: session.commission_rate,
          permissions,
        },
        currentUser,
        userStack,
        terminalId: session.terminal_id,
        locationId: session.location_id,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        lastSwitchId,
        lastSwitchAt,
        expiresAt: session.expires_at,
      };

      // Cache the context
      this.activeSessions.set(sessionId, {
        context,
        cachedAt: Date.now(),
      });

      return context;
    } catch (error) {
      console.error("Get session context error:", error);
      return null;
    }
  }

  /**
   * Get staff permissions
   */
  async getStaffPermissions(staffId) {
    try {
      const [permissionRows] = await this.databaseManager.executeQuery(
        `SELECT permission FROM staff_permissions WHERE staff_id = ?`,
        [staffId]
      );

      return permissionRows.map((row) => row.permission);
    } catch (error) {
      console.error("Get staff permissions error:", error);
      return [];
    }
  }

  /**
   * Set auto-switch back timeout
   */
  setAutoSwitchBackTimeout(sessionId, switchId) {
    // Clear existing timeout
    this.clearAutoSwitchBackTimeout(sessionId);

    // Set new timeout
    const timeoutId = setTimeout(async () => {
      try {
        await this.switchBack(sessionId, switchId);
        console.log(`Auto-switched back session ${sessionId} after timeout`);
      } catch (error) {
        console.error("Auto-switch back error:", error);
      }
    }, this.AUTO_SWITCH_BACK_TIMEOUT);

    this.switchTimeouts.set(sessionId, timeoutId);
  }

  /**
   * Clear auto-switch back timeout
   */
  clearAutoSwitchBackTimeout(sessionId) {
    const timeoutId = this.switchTimeouts.get(sessionId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.switchTimeouts.delete(sessionId);
    }
  }

  /**
   * Log security events
   */
  async logSecurityEvent(staffId, eventType, eventData) {
    try {
      await this.databaseManager.executeQuery(
        `INSERT INTO pos_security_events 
         (id, staff_id, event_type, event_data, created_at)
         VALUES (?, ?, ?, ?, NOW())`,
        [crypto.randomUUID(), staffId, eventType, JSON.stringify(eventData)]
      );
    } catch (error) {
      console.error("Security event logging error:", error);
      // Don't throw - logging failures shouldn't break functionality
    }
  }

  /**
   * Get user switch history for audit
   */
  async getUserSwitchHistory(sessionId, limit = 50) {
    try {
      // Use string interpolation for LIMIT to avoid MySQL parameter binding issues
      const [rows] = await this.databaseManager.executeQuery(
        `SELECT us.*,
                from_staff.name as from_staff_name,
                to_staff.name as to_staff_name
         FROM pos_user_switches us
         LEFT JOIN pos_staff from_staff ON us.from_staff_id = from_staff.id
         JOIN pos_staff to_staff ON us.to_staff_id = to_staff.id
         WHERE us.session_id = ?
         ORDER BY us.switched_at DESC
         LIMIT ${parseInt(limit)}`,
        [sessionId]
      );

      return rows;
    } catch (error) {
      console.error("Get switch history error:", error);
      return [];
    }
  }

  /**
   * Clean up expired sessions and timeouts
   */
  async cleanup() {
    try {
      // Clean up expired session contexts from cache
      for (const [sessionId, cached] of this.activeSessions.entries()) {
        if (Date.now() - cached.cachedAt > this.SESSION_CONTEXT_CACHE_TTL) {
          this.activeSessions.delete(sessionId);
          this.clearAutoSwitchBackTimeout(sessionId);
        }
      }

      // Clean up expired sessions from database
      await this.databaseManager.executeQuery(
        `DELETE FROM pos_sessions WHERE expires_at < NOW()`
      );

      console.log("Multi-user session cleanup completed");
    } catch (error) {
      console.error("Session cleanup error:", error);
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats() {
    try {
      const [stats] = await this.databaseManager.executeQuery(`
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active_sessions,
          (SELECT COUNT(*) FROM pos_user_switches WHERE switched_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as switches_24h,
          (SELECT COUNT(DISTINCT session_id) FROM pos_user_switches WHERE switched_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)) as sessions_with_switches_24h
        FROM pos_sessions
      `);

      return {
        totalSessions: stats[0].total_sessions,
        activeSessions: stats[0].active_sessions,
        cachedSessions: this.activeSessions.size,
        switches24h: stats[0].switches_24h,
        sessionsWithSwitches24h: stats[0].sessions_with_switches_24h,
        activeTimeouts: this.switchTimeouts.size,
      };
    } catch (error) {
      console.error("Get session stats error:", error);
      return {};
    }
  }
}

// Create and export service instance following the existing pattern
const multiUserSessionService = new MultiUserSessionService();

module.exports = {
  MultiUserSessionService,
  multiUserSessionService,
};
