/**
 * Grant 'collect_payments' permission to existing staff members via API
 * 
 * This script uses the staff management API to grant the 'collect_payments' 
 * permission to all staff members who have 'process_orders' permission.
 */

const axios = require('axios');
require('dotenv').config();

// Dynamic API URL based on environment
const PORT = process.env.PORT || 3020;
const NODE_ENV = process.env.NODE_ENV || 'local';
const API_BASE_URL = process.env.API_BASE_URL || `http://localhost:${PORT}/api`;

console.log(`🌍 Environment: ${NODE_ENV}`);
console.log(`🔗 API Base URL: ${API_BASE_URL}`);

async function grantCollectPaymentsPermission() {
  try {
    console.log('🔧 Starting permission grant process via API...');
    
    // First, we need to authenticate as an admin user
    console.log('🔐 Authenticating as admin...');

    // Use environment variables for admin credentials, with fallbacks
    const adminUsername = process.env.ADMIN_USERNAME || 'admin1';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

    console.log(`👤 Using admin username: ${adminUsername}`);

    const loginResponse = await axios.post(`${API_BASE_URL}/pos/login`, {
      username: adminUsername,
      password: adminPassword
    });

    if (!loginResponse.data.success) {
      throw new Error('Failed to authenticate: ' + loginResponse.data.error);
    }

    const token = loginResponse.data.token || loginResponse.data.data?.token;
    console.log('✅ Authenticated successfully');
    console.log('🔑 Token received:', token ? 'Yes' : 'No');

    if (!token) {
      console.log('📋 Login response:', JSON.stringify(loginResponse.data, null, 2));
      throw new Error('No token received from login response');
    }

    // Get all staff members
    console.log('🔍 Fetching all staff members...');
    
    const staffResponse = await axios.get(`${API_BASE_URL}/staff`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!staffResponse.data.success) {
      throw new Error('Failed to fetch staff: ' + staffResponse.data.error);
    }

    const staffMembers = staffResponse.data.data?.staffMembers || staffResponse.data.staffMembers || [];
    console.log(`Found ${staffMembers.length} staff members`);

    if (!Array.isArray(staffMembers)) {
      console.log('📋 Staff response structure:', JSON.stringify(staffResponse.data, null, 2));
      throw new Error('Staff data is not an array');
    }

    // Filter staff who have process_orders permission but not collect_payments
    const staffToUpdate = staffMembers.filter(staff => {
      const permissions = Array.isArray(staff.permissions) ? staff.permissions :
                         (staff.permissions ? staff.permissions.split(',') : []);
      const hasProcessOrders = permissions.includes('process_orders');
      const hasCollectPayments = permissions.includes('collect_payments');

      return hasProcessOrders && !hasCollectPayments;
    });

    console.log(`${staffToUpdate.length} staff members need collect_payments permission`);

    if (staffToUpdate.length === 0) {
      console.log('ℹ️ No staff members need the collect_payments permission');
      return;
    }

    // Update each staff member's permissions
    let updatedCount = 0;
    
    for (const staff of staffToUpdate) {
      try {
        const currentPermissions = Array.isArray(staff.permissions) ? staff.permissions :
                                  (staff.permissions ? staff.permissions.split(',') : []);
        const newPermissions = [...currentPermissions, 'collect_payments'];

        console.log(`🔄 Updating permissions for ${staff.name} (${staff.username})...`);
        
        const updateResponse = await axios.put(
          `${API_BASE_URL}/staff/${staff.id}/permissions`,
          {
            permissions: newPermissions
          },
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        if (updateResponse.data.success) {
          updatedCount++;
          console.log(`✅ Added collect_payments permission to ${staff.name}`);
        } else {
          console.error(`❌ Failed to update ${staff.name}: ${updateResponse.data.error}`);
          console.error(`📋 Update response:`, JSON.stringify(updateResponse.data, null, 2));
        }
      } catch (error) {
        console.error(`❌ Error updating ${staff.name}:`, error.response?.data?.error || error.message);
        if (error.response) {
          console.error(`📋 Error response:`, JSON.stringify(error.response.data, null, 2));
          console.error(`📋 Status:`, error.response.status);
        }
      }
    }

    console.log(`🎉 Successfully updated ${updatedCount} staff members`);

    // Verify the results
    console.log('🔍 Verifying results...');
    
    const verifyResponse = await axios.get(`${API_BASE_URL}/staff`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (verifyResponse.data.success) {
      const updatedStaff = verifyResponse.data.data?.staffMembers || verifyResponse.data.staffMembers || [];
      const staffWithCollectPayments = updatedStaff.filter(staff => {
        const permissions = Array.isArray(staff.permissions) ? staff.permissions :
                           (staff.permissions ? staff.permissions.split(',') : []);
        return permissions.includes('collect_payments');
      });
      
      console.log(`✅ Total staff members with collect_payments permission: ${staffWithCollectPayments.length}`);
      
      staffWithCollectPayments.forEach(staff => {
        console.log(`   - ${staff.name} (${staff.username}) - ${staff.role}`);
      });
    }

  } catch (error) {
    console.error('❌ Error granting collect_payments permission:', error.response?.data?.error || error.message);
    
    if (error.response?.status === 401) {
      console.error('💡 Authentication failed. Please check admin credentials in the script.');
    } else if (error.response?.status === 403) {
      console.error('💡 Permission denied. Make sure the admin user has manage_staff permission.');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 Cannot connect to API. Make sure the backend server is running.');
    }
    
    throw error;
  }
}

// Run the script
if (require.main === module) {
  grantCollectPaymentsPermission()
    .then(() => {
      console.log('🏁 Permission grant process completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Permission grant process failed:', error.message);
      process.exit(1);
    });
}

module.exports = { grantCollectPaymentsPermission };
