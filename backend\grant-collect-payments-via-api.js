/**
 * Grant 'collect_payments' permission to existing staff members via API
 * 
 * This script uses the staff management API to grant the 'collect_payments' 
 * permission to all staff members who have 'process_orders' permission.
 */

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001/api';

async function grantCollectPaymentsPermission() {
  try {
    console.log('🔧 Starting permission grant process via API...');
    
    // First, we need to authenticate as an admin user
    console.log('🔐 Authenticating as admin...');
    
    const loginResponse = await axios.post(`${API_BASE_URL}/pos/login`, {
      username: 'admin1', // Default admin username from test_db_connection.js
      password: 'admin123' // Default admin password
    });

    if (!loginResponse.data.success) {
      throw new Error('Failed to authenticate: ' + loginResponse.data.error);
    }

    const token = loginResponse.data.token;
    console.log('✅ Authenticated successfully');

    // Get all staff members
    console.log('🔍 Fetching all staff members...');
    
    const staffResponse = await axios.get(`${API_BASE_URL}/staff`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!staffResponse.data.success) {
      throw new Error('Failed to fetch staff: ' + staffResponse.data.error);
    }

    const staffMembers = staffResponse.data.data;
    console.log(`Found ${staffMembers.length} staff members`);

    // Filter staff who have process_orders permission but not collect_payments
    const staffToUpdate = staffMembers.filter(staff => {
      const permissions = staff.permissions ? staff.permissions.split(',') : [];
      const hasProcessOrders = permissions.includes('process_orders');
      const hasCollectPayments = permissions.includes('collect_payments');
      
      return hasProcessOrders && !hasCollectPayments;
    });

    console.log(`${staffToUpdate.length} staff members need collect_payments permission`);

    if (staffToUpdate.length === 0) {
      console.log('ℹ️ No staff members need the collect_payments permission');
      return;
    }

    // Update each staff member's permissions
    let updatedCount = 0;
    
    for (const staff of staffToUpdate) {
      try {
        const currentPermissions = staff.permissions ? staff.permissions.split(',') : [];
        const newPermissions = [...currentPermissions, 'collect_payments'];
        
        console.log(`🔄 Updating permissions for ${staff.name} (${staff.username})...`);
        
        const updateResponse = await axios.put(
          `${API_BASE_URL}/staff/${staff.id}/permissions`,
          {
            permissions: newPermissions
          },
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        if (updateResponse.data.success) {
          updatedCount++;
          console.log(`✅ Added collect_payments permission to ${staff.name}`);
        } else {
          console.error(`❌ Failed to update ${staff.name}: ${updateResponse.data.error}`);
        }
      } catch (error) {
        console.error(`❌ Error updating ${staff.name}:`, error.response?.data?.error || error.message);
      }
    }

    console.log(`🎉 Successfully updated ${updatedCount} staff members`);

    // Verify the results
    console.log('🔍 Verifying results...');
    
    const verifyResponse = await axios.get(`${API_BASE_URL}/staff`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (verifyResponse.data.success) {
      const updatedStaff = verifyResponse.data.data;
      const staffWithCollectPayments = updatedStaff.filter(staff => {
        const permissions = staff.permissions ? staff.permissions.split(',') : [];
        return permissions.includes('collect_payments');
      });
      
      console.log(`✅ Total staff members with collect_payments permission: ${staffWithCollectPayments.length}`);
      
      staffWithCollectPayments.forEach(staff => {
        console.log(`   - ${staff.name} (${staff.username}) - ${staff.role}`);
      });
    }

  } catch (error) {
    console.error('❌ Error granting collect_payments permission:', error.response?.data?.error || error.message);
    
    if (error.response?.status === 401) {
      console.error('💡 Authentication failed. Please check admin credentials in the script.');
    } else if (error.response?.status === 403) {
      console.error('💡 Permission denied. Make sure the admin user has manage_staff permission.');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 Cannot connect to API. Make sure the backend server is running.');
    }
    
    throw error;
  }
}

// Run the script
if (require.main === module) {
  grantCollectPaymentsPermission()
    .then(() => {
      console.log('🏁 Permission grant process completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Permission grant process failed:', error.message);
      process.exit(1);
    });
}

module.exports = { grantCollectPaymentsPermission };
