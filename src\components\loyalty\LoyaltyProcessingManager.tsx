/**
 * Loyalty Processing Manager
 * 
 * This component manages failed loyalty processing attempts and provides
 * manual retry functionality for orders where loyalty points weren't
 * processed automatically during checkout.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { CrossPlatformStorage } from '@/src/utils/storage';
import { loyaltyOrderCompletionService } from '@/src/services/loyalty-order-completion';
import { formatCurrency } from '@/src/utils/currencyUtils';

interface FailedLoyaltyProcessing {
  timestamp: string;
  orderData: {
    orderId: string;
    orderNumber: string;
    customerId: string;
    orderTotal: number;
    salesAgentId?: string;
    staffId: string;
  };
  error: string;
  customerId: string;
  orderTotal: number;
  orderId: string;
  retryCount: number;
  status: 'pending_retry' | 'retrying' | 'completed' | 'failed';
}

export const LoyaltyProcessingManager: React.FC = () => {
  const [failedProcessings, setFailedProcessings] = useState<FailedLoyaltyProcessing[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [retryingItems, setRetryingItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadFailedProcessings();
  }, []);

  const loadFailedProcessings = async () => {
    try {
      setIsLoading(true);
      const stored = await CrossPlatformStorage.getItemAsync('failed_loyalty_processings');
      if (stored) {
        const failures = JSON.parse(stored);
        // Filter out completed items and sort by timestamp
        const pendingFailures = failures
          .filter((f: FailedLoyaltyProcessing) => f.status !== 'completed')
          .sort((a: FailedLoyaltyProcessing, b: FailedLoyaltyProcessing) => 
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
          );
        setFailedProcessings(pendingFailures);
      }
    } catch (error) {
      console.error('Error loading failed processings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const retryLoyaltyProcessing = async (item: FailedLoyaltyProcessing) => {
    const itemKey = `${item.orderId}-${item.timestamp}`;
    
    try {
      setRetryingItems(prev => new Set(prev).add(itemKey));
      
      console.log('🔄 Retrying loyalty processing for order:', item.orderId);
      
      // Prepare order completion data
      const orderCompletionData = {
        orderId: item.orderData.orderId,
        orderNumber: item.orderData.orderNumber,
        customerId: item.orderData.customerId,
        orderTotal: item.orderData.orderTotal,
        salesAgentId: item.orderData.salesAgentId,
        staffId: item.orderData.staffId,
        lineItems: [], // We don't have line items for retry, but backend should still work
        paymentMethod: 'Manual Retry',
        transactionId: `RETRY-${item.orderId}`,
      };

      const result = await loyaltyOrderCompletionService.processLoyaltyCompletion(
        orderCompletionData
      );

      if (result.success) {
        console.log('✅ Loyalty processing retry successful:', result);
        
        // Mark as completed
        await markProcessingAsCompleted(item, result);
        
        Alert.alert(
          'Success!',
          `Loyalty points processed successfully!\n\n` +
          `Points Added: ${result.pointsAdded}\n` +
          `New Balance: ${result.newBalance}\n` +
          `${result.tierChanged ? `Tier upgraded to ${result.newTier}!` : ''}`
        );
        
        // Reload the list
        await loadFailedProcessings();
      } else {
        console.warn('⚠️ Loyalty processing retry failed:', result.error);
        
        // Update retry count
        await updateRetryCount(item);
        
        Alert.alert(
          'Retry Failed',
          `Loyalty processing failed again: ${result.error}\n\nRetry count: ${item.retryCount + 1}`
        );
      }
    } catch (error) {
      console.error('❌ Error during loyalty processing retry:', error);
      
      Alert.alert(
        'Error',
        `An error occurred during retry: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setRetryingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemKey);
        return newSet;
      });
    }
  };

  const markProcessingAsCompleted = async (item: FailedLoyaltyProcessing, result: any) => {
    try {
      const stored = await CrossPlatformStorage.getItemAsync('failed_loyalty_processings');
      if (stored) {
        const failures = JSON.parse(stored);
        const updatedFailures = failures.map((f: FailedLoyaltyProcessing) => {
          if (f.orderId === item.orderId && f.timestamp === item.timestamp) {
            return {
              ...f,
              status: 'completed',
              completedAt: new Date().toISOString(),
              completionResult: result,
            };
          }
          return f;
        });
        
        await CrossPlatformStorage.setItemAsync(
          'failed_loyalty_processings',
          JSON.stringify(updatedFailures)
        );
      }
    } catch (error) {
      console.error('Error marking processing as completed:', error);
    }
  };

  const updateRetryCount = async (item: FailedLoyaltyProcessing) => {
    try {
      const stored = await CrossPlatformStorage.getItemAsync('failed_loyalty_processings');
      if (stored) {
        const failures = JSON.parse(stored);
        const updatedFailures = failures.map((f: FailedLoyaltyProcessing) => {
          if (f.orderId === item.orderId && f.timestamp === item.timestamp) {
            return {
              ...f,
              retryCount: f.retryCount + 1,
              lastRetryAt: new Date().toISOString(),
            };
          }
          return f;
        });
        
        await CrossPlatformStorage.setItemAsync(
          'failed_loyalty_processings',
          JSON.stringify(updatedFailures)
        );
      }
    } catch (error) {
      console.error('Error updating retry count:', error);
    }
  };

  const removeProcessing = async (item: FailedLoyaltyProcessing) => {
    Alert.alert(
      'Remove Failed Processing',
      'Are you sure you want to remove this failed processing? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              const stored = await CrossPlatformStorage.getItemAsync('failed_loyalty_processings');
              if (stored) {
                const failures = JSON.parse(stored);
                const updatedFailures = failures.filter(
                  (f: FailedLoyaltyProcessing) => 
                    !(f.orderId === item.orderId && f.timestamp === item.timestamp)
                );
                
                await CrossPlatformStorage.setItemAsync(
                  'failed_loyalty_processings',
                  JSON.stringify(updatedFailures)
                );
                
                await loadFailedProcessings();
              }
            } catch (error) {
              console.error('Error removing processing:', error);
            }
          },
        },
      ]
    );
  };

  const renderFailedProcessing = ({ item }: { item: FailedLoyaltyProcessing }) => {
    const itemKey = `${item.orderId}-${item.timestamp}`;
    const isRetrying = retryingItems.has(itemKey);
    
    return (
      <View style={styles.itemContainer}>
        <View style={styles.itemHeader}>
          <Text style={styles.orderNumber}>Order #{item.orderData.orderNumber}</Text>
          <Text style={styles.timestamp}>
            {new Date(item.timestamp).toLocaleDateString()} {new Date(item.timestamp).toLocaleTimeString()}
          </Text>
        </View>
        
        <View style={styles.itemDetails}>
          <Text style={styles.detailText}>Customer ID: {item.customerId}</Text>
          <Text style={styles.detailText}>Order Total: {formatCurrency(item.orderTotal)}</Text>
          <Text style={styles.detailText}>Retry Count: {item.retryCount}</Text>
          <Text style={styles.errorText}>Error: {item.error}</Text>
        </View>
        
        <View style={styles.itemActions}>
          <TouchableOpacity
            style={[styles.retryButton, isRetrying && styles.disabledButton]}
            onPress={() => retryLoyaltyProcessing(item)}
            disabled={isRetrying}
          >
            {isRetrying ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.retryButtonText}>Retry</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeProcessing(item)}
          >
            <Text style={styles.removeButtonText}>Remove</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading failed processings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Failed Loyalty Processing</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={loadFailedProcessings}>
          <Text style={styles.refreshButtonText}>Refresh</Text>
        </TouchableOpacity>
      </View>
      
      {failedProcessings.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No failed loyalty processings found</Text>
          <Text style={styles.emptySubtext}>All loyalty points have been processed successfully!</Text>
        </View>
      ) : (
        <FlatList
          data={failedProcessings}
          renderItem={renderFailedProcessing}
          keyExtractor={(item) => `${item.orderId}-${item.timestamp}`}
          style={styles.list}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  list: {
    flex: 1,
  },
  itemContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  itemDetails: {
    marginBottom: 16,
  },
  detailText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: '#d32f2f',
    marginTop: 8,
    fontStyle: 'italic',
  },
  itemActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  removeButton: {
    backgroundColor: '#f44336',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    alignItems: 'center',
  },
  removeButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default LoyaltyProcessingManager;
