/**
 * Current System Testing Script
 * Tests all existing functionality before migration
 * Run from backend directory: node testing/test_current_system.js
 */

const axios = require("axios");
const assert = require("assert");

class CurrentSystemTester {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || "http://localhost:3020/api";
    this.authToken = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: [],
    };
  }

  // Helper method to make API requests
  async makeRequest(method, endpoint, data = null, headers = {}) {
    try {
      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          "Content-Type": "application/json",
          ...headers,
        },
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      throw new Error(
        `API request failed: ${error.response?.data?.error || error.message}`
      );
    }
  }

  // Test helper
  async test(testName, testFunction) {
    try {
      console.log(`🧪 Testing: ${testName}`);
      await testFunction();
      console.log(`✅ PASSED: ${testName}`);
      this.testResults.passed++;
    } catch (error) {
      console.error(`❌ FAILED: ${testName} - ${error.message}`);
      this.testResults.failed++;
      this.testResults.errors.push({ test: testName, error: error.message });
    }
  }

  // Test POS Staff Authentication
  async testStaffAuthentication() {
    await this.test("Staff Login - Cashier", async () => {
      const response = await this.makeRequest("POST", "/pos/login", {
        username: "cashier1",
        password: "password123",
      });

      assert(response.success, "Login should succeed");
      assert(response.data.token, "Should return JWT token");
      assert(
        response.data.user.role === "cashier",
        "Should return cashier role"
      );

      this.authToken = response.data.token;
    });

    await this.test("Token Verification", async () => {
      const response = await this.makeRequest("GET", "/pos/verify", null, {
        Authorization: `Bearer ${this.authToken}`,
      });

      assert(response.success, "Token verification should succeed");
      assert(
        response.data.user.username === "cashier1",
        "Should return correct user"
      );
    });
  }

  // Test Sales Agent Management
  async testSalesAgentManagement() {
    await this.test("Get All Sales Agents", async () => {
      const response = await this.makeRequest("GET", "/sales-agents", null, {
        Authorization: `Bearer ${this.authToken}`,
      });

      assert(response.success, "Should succeed");
      assert(Array.isArray(response.data.salesAgents), "Should return array");
      assert(
        response.data.salesAgents.length >= 3,
        "Should have at least 3 default agents"
      );

      // Check specific agent
      const alice = response.data.salesAgents.find((a) => a.id === "agent-001");
      assert(alice, "Should find Alice Johnson");
      assert(alice.name === "Alice Johnson", "Should have correct name");
      assert(
        alice.commissionRate === 8.0,
        "Should have correct commission rate"
      );
    });

    await this.test("Get Sales Agent by ID", async () => {
      const response = await this.makeRequest(
        "GET",
        "/sales-agents/agent-001",
        null,
        {
          Authorization: `Bearer ${this.authToken}`,
        }
      );

      assert(response.success, "Should succeed");
      assert(
        response.data.salesAgent.id === "agent-001",
        "Should return correct agent"
      );
      assert(
        response.data.salesAgent.customerCount >= 1,
        "Should have customer count"
      );
      assert(
        Array.isArray(response.data.salesAgent.customers),
        "Should return customers array"
      );
    });
  }

  // Test Staff Management
  async testStaffManagement() {
    await this.test("Get All Staff", async () => {
      const response = await this.makeRequest("GET", "/staff", null, {
        Authorization: `Bearer ${this.authToken}`,
      });

      assert(response.success, "Should succeed");
      assert(Array.isArray(response.data.staffMembers), "Should return array");
      assert(
        response.data.staffMembers.length >= 2,
        "Should have at least 2 default staff"
      );
    });
  }

  // Test Commission Calculation
  async testCommissionCalculation() {
    await this.test("Get Discount Configuration", async () => {
      const response = await this.makeRequest(
        "GET",
        "/discounts/configuration",
        null,
        {
          Authorization: `Bearer ${this.authToken}`,
        }
      );

      assert(response.success, "Should succeed");
      assert(response.data.configuration, "Should return configuration");
      assert(
        typeof response.data.configuration.staff_discount_rate === "number",
        "Should have staff rate"
      );
    });

    await this.test("Calculate Commission Discount", async () => {
      const cartData = {
        subtotal: "100.00",
        items: [{ id: "1", quantity: 2, price: "50.00" }],
      };

      const response = await this.makeRequest(
        "POST",
        "/discounts/calculate",
        {
          cartData,
          staffId: "pos-001",
          salesAgentId: "agent-001",
          customerId: "8095960301705",
        },
        {
          Authorization: `Bearer ${this.authToken}`,
        }
      );

      assert(response.success, "Should succeed");
      if (response.data.discount) {
        assert(
          typeof response.data.discount.rate === "number",
          "Should have discount rate"
        );
        assert(
          typeof response.data.discount.amount === "number",
          "Should have discount amount"
        );
      }
    });
  }

  // Run all tests
  async runAllTests() {
    console.log("🚀 Starting Current System Tests...\n");

    try {
      await this.testStaffAuthentication();
      await this.testSalesAgentManagement();
      await this.testStaffManagement();
      await this.testCommissionCalculation();
    } catch (error) {
      console.error("❌ Test suite failed:", error.message);
    }

    this.printResults();
  }

  // Print test results
  printResults() {
    console.log("\n📊 TEST RESULTS:");
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(
      `📈 Success Rate: ${(
        (this.testResults.passed /
          (this.testResults.passed + this.testResults.failed)) *
        100
      ).toFixed(1)}%`
    );

    if (this.testResults.errors.length > 0) {
      console.log("\n🚨 FAILED TESTS:");
      this.testResults.errors.forEach((error) => {
        console.log(`   • ${error.test}: ${error.error}`);
      });
    }

    if (this.testResults.failed === 0) {
      console.log("\n🎉 All tests passed! System is ready for migration.");
    } else {
      console.log(
        "\n⚠️  Some tests failed. Fix issues before proceeding with migration."
      );
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new CurrentSystemTester();
  tester.runAllTests().catch(console.error);
}

module.exports = CurrentSystemTester;
