import { ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { Theme } from '@/src/contexts/ThemeContext';

// Type for style objects
type Style = ViewStyle | TextStyle | ImageStyle;

// Utility to create responsive spacing
export const createSpacing = (theme: Theme) => ({
  // Margin utilities
  m: (value: keyof typeof theme.spacing): ViewStyle => ({
    margin: theme.spacing[value],
  }),
  mt: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginTop: theme.spacing[value],
  }),
  mb: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginBottom: theme.spacing[value],
  }),
  ml: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginLeft: theme.spacing[value],
  }),
  mr: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginRight: theme.spacing[value],
  }),
  mx: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginHorizontal: theme.spacing[value],
  }),
  my: (value: keyof typeof theme.spacing): ViewStyle => ({
    marginVertical: theme.spacing[value],
  }),

  // Padding utilities
  p: (value: keyof typeof theme.spacing): ViewStyle => ({
    padding: theme.spacing[value],
  }),
  pt: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingTop: theme.spacing[value],
  }),
  pb: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingBottom: theme.spacing[value],
  }),
  pl: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingLeft: theme.spacing[value],
  }),
  pr: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingRight: theme.spacing[value],
  }),
  px: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingHorizontal: theme.spacing[value],
  }),
  py: (value: keyof typeof theme.spacing): ViewStyle => ({
    paddingVertical: theme.spacing[value],
  }),
});

// Utility to create border radius styles
export const createBorderRadius = (theme: Theme) => ({
  rounded: (value: keyof typeof theme.borderRadius): ViewStyle => ({
    borderRadius: theme.borderRadius[value],
  }),
  roundedTop: (value: keyof typeof theme.borderRadius): ViewStyle => ({
    borderTopLeftRadius: theme.borderRadius[value],
    borderTopRightRadius: theme.borderRadius[value],
  }),
  roundedBottom: (value: keyof typeof theme.borderRadius): ViewStyle => ({
    borderBottomLeftRadius: theme.borderRadius[value],
    borderBottomRightRadius: theme.borderRadius[value],
  }),
  roundedLeft: (value: keyof typeof theme.borderRadius): ViewStyle => ({
    borderTopLeftRadius: theme.borderRadius[value],
    borderBottomLeftRadius: theme.borderRadius[value],
  }),
  roundedRight: (value: keyof typeof theme.borderRadius): ViewStyle => ({
    borderTopRightRadius: theme.borderRadius[value],
    borderBottomRightRadius: theme.borderRadius[value],
  }),
});

// Utility to create shadow styles
export const createShadows = (theme: Theme) => ({
  shadow: (value: keyof typeof theme.shadows): ViewStyle => ({
    ...theme.shadows[value],
  }),
  shadowColor: (color: string): ViewStyle => ({
    shadowColor: color,
  }),
});

// Utility to create flex layouts
export const createFlex = () => ({
  flex: (value: number = 1): ViewStyle => ({
    flex: value,
  }),
  flexRow: (): ViewStyle => ({
    flexDirection: 'row',
  }),
  flexCol: (): ViewStyle => ({
    flexDirection: 'column',
  }),
  justifyCenter: (): ViewStyle => ({
    justifyContent: 'center',
  }),
  justifyBetween: (): ViewStyle => ({
    justifyContent: 'space-between',
  }),
  justifyAround: (): ViewStyle => ({
    justifyContent: 'space-around',
  }),
  justifyEvenly: (): ViewStyle => ({
    justifyContent: 'space-evenly',
  }),
  justifyStart: (): ViewStyle => ({
    justifyContent: 'flex-start',
  }),
  justifyEnd: (): ViewStyle => ({
    justifyContent: 'flex-end',
  }),
  alignCenter: (): ViewStyle => ({
    alignItems: 'center',
  }),
  alignStart: (): ViewStyle => ({
    alignItems: 'flex-start',
  }),
  alignEnd: (): ViewStyle => ({
    alignItems: 'flex-end',
  }),
  alignStretch: (): ViewStyle => ({
    alignItems: 'stretch',
  }),
  selfCenter: (): ViewStyle => ({
    alignSelf: 'center',
  }),
  selfStart: (): ViewStyle => ({
    alignSelf: 'flex-start',
  }),
  selfEnd: (): ViewStyle => ({
    alignSelf: 'flex-end',
  }),
  selfStretch: (): ViewStyle => ({
    alignSelf: 'stretch',
  }),
});

// Utility to create position styles
export const createPosition = () => ({
  absolute: (): ViewStyle => ({
    position: 'absolute',
  }),
  relative: (): ViewStyle => ({
    position: 'relative',
  }),
  top: (value: number): ViewStyle => ({
    top: value,
  }),
  bottom: (value: number): ViewStyle => ({
    bottom: value,
  }),
  left: (value: number): ViewStyle => ({
    left: value,
  }),
  right: (value: number): ViewStyle => ({
    right: value,
  }),
  inset: (value: number): ViewStyle => ({
    top: value,
    bottom: value,
    left: value,
    right: value,
  }),
  zIndex: (value: number): ViewStyle => ({
    zIndex: value,
  }),
});

// Utility to create size styles
export const createSize = () => ({
  width: (value: number | string): ViewStyle => ({
    width: value,
  }),
  height: (value: number | string): ViewStyle => ({
    height: value,
  }),
  size: (value: number | string): ViewStyle => ({
    width: value,
    height: value,
  }),
  minWidth: (value: number | string): ViewStyle => ({
    minWidth: value,
  }),
  minHeight: (value: number | string): ViewStyle => ({
    minHeight: value,
  }),
  maxWidth: (value: number | string): ViewStyle => ({
    maxWidth: value,
  }),
  maxHeight: (value: number | string): ViewStyle => ({
    maxHeight: value,
  }),
  fullWidth: (): ViewStyle => ({
    width: '100%',
  }),
  fullHeight: (): ViewStyle => ({
    height: '100%',
  }),
  fullSize: (): ViewStyle => ({
    width: '100%',
    height: '100%',
  }),
});

// Utility to create border styles
export const createBorder = (theme: Theme) => ({
  border: (width: number = 1, color?: string): ViewStyle => ({
    borderWidth: width,
    borderColor: color || theme.colors.border,
  }),
  borderTop: (width: number = 1, color?: string): ViewStyle => ({
    borderTopWidth: width,
    borderTopColor: color || theme.colors.border,
  }),
  borderBottom: (width: number = 1, color?: string): ViewStyle => ({
    borderBottomWidth: width,
    borderBottomColor: color || theme.colors.border,
  }),
  borderLeft: (width: number = 1, color?: string): ViewStyle => ({
    borderLeftWidth: width,
    borderLeftColor: color || theme.colors.border,
  }),
  borderRight: (width: number = 1, color?: string): ViewStyle => ({
    borderRightWidth: width,
    borderRightColor: color || theme.colors.border,
  }),
});

// Utility to create background styles
export const createBackground = (theme: Theme) => ({
  bg: (color: string): ViewStyle => ({
    backgroundColor: color,
  }),
  bgPrimary: (): ViewStyle => ({
    backgroundColor: theme.colors.primary,
  }),
  bgSecondary: (): ViewStyle => ({
    backgroundColor: theme.colors.secondary,
  }),
  bgSurface: (): ViewStyle => ({
    backgroundColor: theme.colors.surface,
  }),
  bgCard: (): ViewStyle => ({
    backgroundColor: theme.colors.card,
  }),
  bgTransparent: (): ViewStyle => ({
    backgroundColor: 'transparent',
  }),
});

// Utility to create text color styles
export const createTextColor = (theme: Theme) => ({
  textColor: (color: string): TextStyle => ({
    color: color,
  }),
  textPrimary: (): TextStyle => ({
    color: theme.colors.text,
  }),
  textSecondary: (): TextStyle => ({
    color: theme.colors.textSecondary,
  }),
  textMuted: (): TextStyle => ({
    color: theme.colors.textMuted,
  }),
  textAccent: (): TextStyle => ({
    color: theme.colors.primary,
  }),
  textSuccess: (): TextStyle => ({
    color: theme.colors.success,
  }),
  textWarning: (): TextStyle => ({
    color: theme.colors.warning,
  }),
  textError: (): TextStyle => ({
    color: theme.colors.error,
  }),
});

// Main utility function that combines all utilities
export const createStyleUtils = (theme: Theme) => ({
  ...createSpacing(theme),
  ...createBorderRadius(theme),
  ...createShadows(theme),
  ...createFlex(),
  ...createPosition(),
  ...createSize(),
  ...createBorder(theme),
  ...createBackground(theme),
  ...createTextColor(theme),
});

// Helper function to merge styles
export const mergeStyles = <T extends Style>(...styles: (T | undefined | false)[]): T => {
  return Object.assign({}, ...styles.filter(Boolean)) as T;
};

// Helper function to conditionally apply styles
export const conditionalStyle = <T extends Style>(
  condition: boolean,
  trueStyle: T,
  falseStyle?: T
): T | undefined => {
  return condition ? trueStyle : falseStyle;
};
