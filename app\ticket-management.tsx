/**
 * Modern Ticket Management Screen
 *
 * Redesigned ticket management interface with clean UI, improved UX,
 * and consistent design system implementation.
 */

import { GlobalHeader } from "@/components/layout/GlobalHeader";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  createTicketStyles,
  getTicketStatusIndicator,
  TicketVariant,
} from "@/src/design-system/TicketDesignSystem";
import { useScreenNavigation } from "@/src/hooks/useScreenNavigation";
import { useProfessionalTicketWorkflow } from "@/src/hooks/useProfessionalTicketWorkflow";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const TicketManagementScreen: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();

  // Use professional ticket workflow
  const {
    allTickets: tickets,
    activeTicketId,
    createNewTicket,
    switchToTicket,
    deleteTicketSafely,
  } = useProfessionalTicketWorkflow();

  const [refreshing, setRefreshing] = useState(false);
  const [ticketFilter, setTicketFilter] = useState("all");

  const styles = createTicketStyles(theme);

  // Use enhanced navigation hook
  useScreenNavigation({
    title: "Tickets",
    forceTitle: true,
  });

  const activeTickets = tickets.filter((t) => t.status === "active");
  const pausedTickets = tickets.filter((t) => t.status === "paused");
  const completedTickets = tickets.filter((t) => t.status === "completed");
  const dirtyTickets = tickets.filter((t) => t.isDirty);

  // Filter tickets based on selected filter
  const getFilteredTickets = () => {
    switch (ticketFilter) {
      case "active":
        return activeTickets;
      case "paused":
        return pausedTickets;
      case "completed":
        return completedTickets;
      case "dirty":
        return dirtyTickets;
      default:
        return tickets;
    }
  };

  const filteredTickets = getFilteredTickets();

  const handleRefresh = async () => {
    setRefreshing(true);
    // TODO: Implement refresh logic
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleTicketPress = async (ticketId: string) => {
    const success = await switchToTicket(ticketId);
    if (success) {
      router.push("/(tabs)/products");
    }
  };

  const handleTicketOptions = (ticketId: string) => {
    // TODO: Show ticket options modal
    Alert.alert("Ticket Options", "Choose an action for this ticket", [
      { text: "Edit", onPress: () => console.log("Edit ticket") },
      { text: "Duplicate", onPress: () => console.log("Duplicate ticket") },
      { text: "Pause", onPress: () => console.log("Pause ticket") },
      {
        text: "Delete",
        style: "destructive",
        onPress: () => handleDeleteTicket(ticketId),
      },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const handleDeleteTicket = async (ticketId: string) => {
    await deleteTicketSafely(ticketId);
  };

  const handleCreateTicket = async () => {
    await createNewTicket();
  };

  const getTicketDisplayName = (ticket: any) => {
    return ticket.name || `Ticket ${ticket.id.slice(-4)}`;
  };

  const getTicketItemCount = (ticket: any) => {
    return (
      ticket.items?.reduce(
        (total: number, item: any) => total + item.quantity,
        0
      ) || 0
    );
  };

  const getTicketVariant = (ticket: any): TicketVariant => {
    if (ticket.id === activeTicketId) return "active";
    return ticket.status as TicketVariant;
  };

  const renderTicketCard = (ticket: any) => {
    const variant = getTicketVariant(ticket);
    const itemCount = getTicketItemCount(ticket);
    const statusIndicator = getTicketStatusIndicator(theme, variant);

    return (
      <TouchableOpacity
        key={ticket.id}
        style={styles.card}
        onPress={() => handleTicketPress(ticket.id)}
        onLongPress={() => handleTicketOptions(ticket.id)}
        activeOpacity={0.7}
      >
        <View style={styles.list.section}>
          {/* Enhanced Header with title, status, and CTA arrow */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: theme.spacing.sm,
            }}
          >
            <View style={{ flex: 1, marginRight: theme.spacing.md }}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginBottom: 4,
                }}
              >
                <Text style={styles.text.title}>
                  {getTicketDisplayName(ticket)}
                </Text>
                {/* Status Indicator */}
                <View
                  style={[
                    statusIndicator.container,
                    { marginLeft: theme.spacing.sm },
                  ]}
                >
                  <View style={statusIndicator.dot} />
                  <Text style={statusIndicator.text}>
                    {variant.charAt(0).toUpperCase() + variant.slice(1)}
                  </Text>
                </View>
              </View>
              <Text style={styles.text.subtitle}>
                {itemCount} item{itemCount === 1 ? "" : "s"}
              </Text>
            </View>

            {/* CTA Arrow - Visual indicator that card is clickable */}
            <View
              style={{
                backgroundColor: theme.colors.primary + "15",
                borderRadius: 20,
                padding: 8,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <IconSymbol
                name="arrow.right"
                size={16}
                color={theme.colors.primary}
              />
            </View>
          </View>

          {/* Total and timestamp */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: theme.spacing.md,
            }}
          >
            <Text style={styles.text.total}>
              {formatCurrency(ticket.total || 0)}
            </Text>
            <Text style={styles.text.timestamp}>
              {new Date(
                ticket.updatedAt || ticket.createdAt
              ).toLocaleTimeString()}
            </Text>
          </View>

          {/* Note if present */}
          {ticket.note && (
            <Text
              style={[styles.text.subtitle, { marginTop: theme.spacing.sm }]}
              numberOfLines={2}
            >
              📝 {ticket.note}
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderTicketSection = (
    title: string,
    tickets: any[],
    emptyMessage: string
  ) => {
    return (
      <View style={styles.list.section}>
        <View style={styles.list.sectionHeader}>
          <Text style={styles.list.sectionTitle}>{title}</Text>
          <Text style={styles.list.sectionCount}>{tickets.length}</Text>
        </View>

        {tickets.length === 0 ? (
          <View style={styles.list.emptyState}>
            <IconSymbol
              name="doc.text"
              size={48}
              color={theme.colors.textSecondary}
            />
            <Text style={styles.list.emptyText}>{emptyMessage}</Text>
          </View>
        ) : (
          tickets.map(renderTicketCard)
        )}
      </View>
    );
  };

  const handleFilterTickets = (filter: string) => {
    setTicketFilter(filter);
  };

  return (
    <SafeAreaView style={styles.list.container}>
      <GlobalHeader
        title="Tickets"
        onMenuPress={() => router.back()}
        showTicketConfig={true}
        onCreateTicket={handleCreateTicket}
        onRefreshTickets={handleRefresh}
        onFilterTickets={handleFilterTickets}
      />

      {/* Tickets List */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: theme.spacing.lg }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
          />
        }
      >
        {ticketFilter === "all" ? (
          <>
            {renderTicketSection(
              "Active Tickets",
              activeTickets,
              "No active tickets. Create a new ticket to get started."
            )}

            {renderTicketSection(
              "Paused Tickets",
              pausedTickets,
              "No paused tickets."
            )}

            {renderTicketSection(
              "Recent Completed",
              completedTickets.slice(0, 5),
              "No completed tickets yet."
            )}
          </>
        ) : (
          renderTicketSection(
            `${
              ticketFilter.charAt(0).toUpperCase() + ticketFilter.slice(1)
            } Tickets`,
            filteredTickets,
            `No ${ticketFilter} tickets found.`
          )
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default TicketManagementScreen;
