/**
 * Responsive Container Component for Dukalink POS
 * Provides web-native layout containers with responsive behavior
 */

import React from "react";
import { View, ViewStyle } from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import { useTheme } from "@/src/contexts/ThemeContext";

interface ResponsiveContainerProps {
  children: React.ReactNode;
  style?: ViewStyle;
  maxWidth?: boolean;
  centered?: boolean;
  padding?: boolean;
  variant?: "default" | "card" | "modal";
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  style,
  maxWidth = true,
  centered = true,
  padding = true,
  variant = "default",
}) => {
  const responsiveLayout = useResponsiveLayout();
  const containerMaxWidth = responsiveLayout?.containerMaxWidth || "100%";
  const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
  const isDesktop = responsiveLayout?.isDesktop || false;
  const theme = useTheme();

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      width: "100%",
    };

    // Apply max width for desktop
    if (maxWidth && isDesktop) {
      baseStyle.maxWidth = containerMaxWidth;
    }

    // Center the container
    if (centered && isDesktop) {
      baseStyle.alignSelf = "center";
      baseStyle.marginHorizontal = "auto";
    }

    // Apply responsive padding
    if (padding) {
      const basePadding = theme?.spacing?.lg || 16; // Fallback to 16 if undefined
      const safeSpacingMultiplier = spacingMultiplier || 1; // Fallback to 1 if undefined
      baseStyle.paddingHorizontal = basePadding * safeSpacingMultiplier;
      baseStyle.paddingVertical = basePadding * safeSpacingMultiplier * 0.8;
    }

    // Variant-specific styles
    switch (variant) {
      case "card":
        return {
          ...baseStyle,
          backgroundColor: theme?.colors?.card || "#ffffff",
          borderRadius: theme?.borderRadius?.lg || 8,
          borderWidth: 1,
          borderColor:
            theme?.colors?.cardBorder || theme?.colors?.border || "#e0e0e0",
          ...(theme?.shadows?.md || {}),
        };
      case "modal":
        return {
          ...baseStyle,
          backgroundColor: theme?.colors?.surface || "#ffffff",
          borderRadius: theme?.borderRadius?.lg || 8,
          ...(theme?.shadows?.lg || {}),
        };
      default:
        return baseStyle;
    }
  };

  return <View style={[getContainerStyle(), style]}>{children}</View>;
};

// Specialized container components
export const ResponsiveCardContainer: React.FC<
  Omit<ResponsiveContainerProps, "variant">
> = (props) => <ResponsiveContainer {...props} variant="card" />;

export const ResponsiveModalContainer: React.FC<
  Omit<ResponsiveContainerProps, "variant">
> = (props) => <ResponsiveContainer {...props} variant="modal" />;

// Grid container for responsive layouts
interface ResponsiveGridProps {
  children: React.ReactNode;
  style?: ViewStyle;
  spacing?: number;
  minItemWidth?: number;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  style,
  spacing = 16,
  minItemWidth = 200,
}) => {
  const responsiveLayout = useResponsiveLayout();
  const width = responsiveLayout?.width || 375;
  const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
  const theme = useTheme();

  const adjustedSpacing = spacing * spacingMultiplier;

  // Better responsive calculation for dashboard cards
  let targetColumns = 1;
  if (width >= 1440) {
    targetColumns = 4; // Large screens
  } else if (width >= 1024) {
    targetColumns = 3; // Desktop
  } else if (width >= 768) {
    targetColumns = 2; // Tablet
  } else {
    targetColumns = 1; // Mobile
  }

  // Verify columns fit with minItemWidth
  const availableWidth = width - adjustedSpacing * 2;
  const itemWidthWithSpacing = minItemWidth + adjustedSpacing;
  const maxFittingColumns = Math.floor(availableWidth / itemWidthWithSpacing);
  const actualItemsPerRow = Math.max(
    1,
    Math.min(targetColumns, maxFittingColumns)
  );

  const gridStyle: ViewStyle = {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -adjustedSpacing / 2,
    ...style,
  };

  const childrenArray = React.Children.toArray(children);

  return (
    <View style={gridStyle}>
      {childrenArray.map((child, index) => (
        <View
          key={index}
          style={{
            width: `${100 / actualItemsPerRow}%`,
            paddingHorizontal: adjustedSpacing / 2,
            marginBottom: adjustedSpacing,
          }}
        >
          {child}
        </View>
      ))}
    </View>
  );
};
