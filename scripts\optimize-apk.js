#!/usr/bin/env node

/**
 * APK Optimization Script for Dukalink POS
 * This script helps identify and fix common APK size issues
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting APK Optimization Analysis...\n');

// 1. Analyze bundle size
console.log('📊 Analyzing bundle composition...');
try {
  console.log('Exporting bundle with Atlas for analysis...');
  execSync('EXPO_ATLAS=true npx expo export --platform android', { stdio: 'inherit' });
  
  if (fs.existsSync('.expo/atlas.jsonl')) {
    console.log('✅ Atlas file generated. You can analyze it with:');
    console.log('   npx expo-atlas .expo/atlas.jsonl');
  }
} catch (error) {
  console.log('⚠️  Bundle analysis failed:', error.message);
}

// 2. Check asset sizes
console.log('\n📁 Analyzing asset sizes...');
const assetDir = path.join(__dirname, '../assets');
if (fs.existsSync(assetDir)) {
  const getDirectorySize = (dirPath) => {
    let totalSize = 0;
    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const file of files) {
      const filePath = path.join(dirPath, file.name);
      if (file.isDirectory()) {
        totalSize += getDirectorySize(filePath);
      } else {
        const stats = fs.statSync(filePath);
        totalSize += stats.size;
        if (stats.size > 100 * 1024) { // Files larger than 100KB
          console.log(`   📄 Large asset: ${file.name} (${(stats.size / 1024).toFixed(1)}KB)`);
        }
      }
    }
    return totalSize;
  };
  
  const totalAssetSize = getDirectorySize(assetDir);
  console.log(`   Total asset size: ${(totalAssetSize / 1024 / 1024).toFixed(2)}MB`);
}

// 3. Check for unused dependencies
console.log('\n📦 Checking for potentially unused dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const dependencies = packageJson.dependencies || {};

const potentiallyUnused = [
  'expo-blur',
  'expo-sharing', 
  'expo-system-ui',
  'react-native-webview',
  'react-dom'
];

potentiallyUnused.forEach(dep => {
  if (dependencies[dep]) {
    console.log(`   ⚠️  Consider reviewing: ${dep}`);
  }
});

// 4. Optimization recommendations
console.log('\n💡 Optimization Recommendations:');
console.log('   1. ✅ Metro config with tree shaking enabled');
console.log('   2. ✅ EAS build profile for APK optimization');
console.log('   3. ✅ Autolinking exclusions configured');
console.log('   4. 🔄 Run: npm run analyze:bundle');
console.log('   5. 🔄 Build with: eas build -p android --profile optimized');

// 5. Check current configuration
console.log('\n⚙️  Current Configuration:');
const easJson = JSON.parse(fs.readFileSync('eas.json', 'utf8'));
if (easJson.build?.optimized) {
  console.log('   ✅ Optimized build profile configured');
} else {
  console.log('   ⚠️  No optimized build profile found');
}

if (fs.existsSync('metro.config.js')) {
  console.log('   ✅ Metro config exists');
} else {
  console.log('   ⚠️  No Metro config found');
}

console.log('\n🎯 Next Steps:');
console.log('   1. Run: npm run analyze:bundle');
console.log('   2. Build optimized APK: eas build -p android --profile optimized');
console.log('   3. Compare sizes and iterate');
console.log('\n✨ Optimization script completed!');
