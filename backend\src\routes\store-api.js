const express = require("express");
const shopifyService = require("../services/shopify-service");
const { authenticateToken } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");
const router = express.Router();

// Get store info (test connection)
router.get("/info", async (req, res) => {
  try {
    const result = await shopifyService.testConnection();

    if (result.success) {
      // Get total product count
      const productCountResult = await shopifyService.getProductCount();

      const storeData = {
        store: {
          id: "default-store",
          name: result.shop.name,
          domain: result.shop.domain,
          email: result.shop.email,
          currency: result.shop.currency,
          timezone: result.shop.timezone,
          plan: result.shop.plan_name,
          totalProducts: productCountResult.success
            ? productCountResult.count
            : 0,
          // Add address information if available
          address: result.shop.address1
            ? {
                address1: result.shop.address1,
                address2: result.shop.address2,
                city: result.shop.city,
                province: result.shop.province,
                country: result.shop.country,
                zip: result.shop.zip,
              }
            : undefined,
          phone: result.shop.phone,
        },
      };
      return ResponseFormatter.success(
        res,
        storeData,
        "Store information retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Store info error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get store information"
    );
  }
});

// Get products
router.get("/products", async (req, res) => {
  try {
    const { page = 1, limit = 50, search, cursor } = req.query;

    let result;
    if (search) {
      result = await shopifyService.searchProducts(
        search,
        parseInt(limit),
        cursor
      );
    } else {
      // For page-based pagination, we need to handle cursor conversion
      // For now, we'll use cursor directly if provided, otherwise start from beginning
      result = await shopifyService.getProducts(parseInt(limit), cursor);
    }

    if (result.success) {
      // Products are already transformed in the shopify service
      const transformedProducts = result.products.map((product) => ({
        id: product.id,
        title: product.title,
        description: product.description,
        vendor: product.vendor,
        productType: product.productType,
        tags: Array.isArray(product.tags)
          ? product.tags
          : product.tags
          ? product.tags.split(",").map((tag) => tag.trim())
          : [],
        status: product.status,
        images: product.images || [],
        variants: product.variants || [],
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      }));

      const pagination = {
        page: cursor ? parseInt(page) : parseInt(page), // Keep the requested page number
        limit: parseInt(limit),
        hasNext: result.pagination?.hasNext || false,
        hasPrev: result.pagination?.hasPrev || false,
        startCursor: result.pagination?.startCursor,
        endCursor: result.pagination?.endCursor,
        total: transformedProducts.length,
      };

      return ResponseFormatter.paginated(
        res,
        { products: transformedProducts },
        pagination,
        "Products retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Products fetch error:", error);
    return ResponseFormatter.serverError(res, "Failed to fetch products");
  }
});

// Search products
router.get("/products/search", authenticateToken, async (req, res) => {
  try {
    const { query = "", limit = 20 } = req.query;

    if (!query.trim()) {
      return ResponseFormatter.success(
        res,
        { products: [] },
        "No search query provided",
        200,
        {
          pagination: {
            hasNext: false,
            hasPrev: false,
            total: 0,
          },
        }
      );
    }

    const result = await shopifyService.searchProducts(query, parseInt(limit));

    if (result.success) {
      // Products are already transformed in the shopify service
      const transformedProducts = result.products.map((product) => ({
        id: product.id,
        title: product.title,
        description: product.description,
        vendor: product.vendor,
        productType: product.productType,
        tags: Array.isArray(product.tags)
          ? product.tags
          : product.tags
          ? product.tags.split(",").map((tag) => tag.trim())
          : [],
        status: product.status,
        images: product.images || [],
        variants: product.variants || [],
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      }));

      const pagination = result.pagination || {
        limit: parseInt(limit),
        total: transformedProducts.length,
        hasNext: false,
        hasPrev: false,
      };

      return ResponseFormatter.paginated(
        res,
        { products: transformedProducts },
        pagination,
        `Found ${transformedProducts.length} products matching "${query}"`
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Product search error:", error);
    return ResponseFormatter.serverError(res, "Failed to search products");
  }
});

// Get customers with search
router.get("/customers", authenticateToken, async (req, res) => {
  try {
    const { limit = 50, search = "", includeLoyalty = false } = req.query;
    const result = await shopifyService.getCustomers(parseInt(limit), search);

    if (result.success) {
      // Customers are already transformed in the shopify service
      let transformedCustomers = result.customers.map((customer) => ({
        id: customer.id,
        email: customer.email,
        firstName: customer.firstName,
        lastName: customer.lastName,
        phone: customer.phone,
        addresses: customer.addresses,
        ordersCount: customer.ordersCount,
        totalSpent: customer.totalSpent,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
        displayName:
          customer.displayName ||
          `${customer.firstName || ""} ${customer.lastName || ""}`.trim() ||
          customer.email,
        tags: customer.tags,
        note: customer.note,
        verifiedEmail: customer.verifiedEmail,
        state: customer.state,
      }));

      // Include loyalty data if requested
      if (includeLoyalty === "true" || includeLoyalty === true) {
        const loyaltyService = require("../services/loyalty-service");

        // Fetch loyalty data for each customer
        transformedCustomers = await Promise.all(
          transformedCustomers.map(async (customer) => {
            try {
              const loyaltyResult = await loyaltyService.getCustomerSummary(
                customer.id
              );
              return {
                ...customer,
                loyaltyData: loyaltyResult.success
                  ? loyaltyResult.summary
                  : null,
              };
            } catch (error) {
              console.warn(
                `Failed to fetch loyalty for customer ${customer.id}:`,
                error
              );
              return {
                ...customer,
                loyaltyData: null,
              };
            }
          })
        );
      }

      return ResponseFormatter.paginated(
        res,
        { customers: transformedCustomers },
        result.pagination,
        `Found ${transformedCustomers.length} customers`
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Customers fetch error:", error);
    return ResponseFormatter.serverError(res, "Failed to fetch customers");
  }
});

// Create a new customer
router.post("/customers", authenticateToken, async (req, res) => {
  try {
    const customerData = req.body;

    // Validate required fields
    if (!customerData.firstName || !customerData.lastName) {
      return ResponseFormatter.validationError(res, {
        firstName: !customerData.firstName ? "First name is required" : null,
        lastName: !customerData.lastName ? "Last name is required" : null,
      });
    }

    // Add POS staff info to customer note
    const staffInfo = req.user ? ` by ${req.user.name} (${req.user.role})` : "";
    customerData.note =
      (customerData.note || "Created via Dukalink POS") + staffInfo;

    const result = await shopifyService.createCustomer(customerData);

    if (result.success) {
      // Customer is already transformed in the shopify service
      const transformedCustomer = {
        id: result.customer.id,
        email: result.customer.email,
        firstName: result.customer.firstName,
        lastName: result.customer.lastName,
        phone: result.customer.phone,
        addresses: result.customer.addresses,
        ordersCount: result.customer.ordersCount,
        totalSpent: result.customer.totalSpent,
        createdAt: result.customer.createdAt,
        updatedAt: result.customer.updatedAt,
        displayName:
          result.customer.displayName ||
          `${result.customer.firstName || ""} ${
            result.customer.lastName || ""
          }`.trim() ||
          result.customer.email,
        tags: result.customer.tags,
        note: result.customer.note,
        verifiedEmail: result.customer.verifiedEmail,
        state: result.customer.state,
      };

      return ResponseFormatter.created(
        res,
        { customer: transformedCustomer },
        "Customer created successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Error creating customer:", error);
    return ResponseFormatter.serverError(res, "Failed to create customer");
  }
});

// Create order
router.post("/orders", async (req, res) => {
  try {
    const orderData = req.body;
    const result = await shopifyService.createOrder(orderData);

    if (result.success) {
      res.json({
        success: true,
        data: {
          order: result.order,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Order creation error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to create order",
    });
  }
});

// Get orders
router.get("/orders", async (req, res) => {
  try {
    const { limit = 50, status = "any" } = req.query;
    const result = await shopifyService.getOrders(parseInt(limit), status);

    if (result.success) {
      res.json({
        success: true,
        data: {
          orders: result.orders,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Orders fetch error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch orders",
    });
  }
});

// Update inventory
router.put("/inventory/:variantId", async (req, res) => {
  try {
    const { variantId } = req.params;
    const { quantity } = req.body;

    const result = await shopifyService.updateInventory(variantId, quantity);

    if (result.success) {
      res.json({
        success: true,
        data: result.inventory_level,
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Inventory update error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to update inventory",
    });
  }
});

module.exports = router;
