/**
 * Customer Loyalty Card Component
 * Displays customer loyalty information including points balance, tier status, and progress
 */

import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import {
  ThemedView,
  ThemedText,
} from "@/src/components/themed/ThemedComponents";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { formatCurrency } from "@/src/utils/currencyUtils";
import {
  getEffectiveLoyaltyPoints,
  formatLoyaltyPoints,
} from "@/src/utils/loyaltyUtils";

export interface CustomerLoyaltyData {
  customerId: string;
  loyaltyPoints: number;
  tier: "bronze" | "silver" | "gold" | "platinum";
  totalPurchases: number;
  totalOrders: number;
  progressToNextTier?: {
    currentAmount: number;
    requiredAmount: number;
    percentage: number;
  };
  lastPurchase?: string;
}

interface CustomerLoyaltyCardProps {
  loyaltyData: CustomerLoyaltyData;
  onPress?: () => void;
  showProgress?: boolean;
  compact?: boolean;
}

const TIER_COLORS = {
  bronze: "#CD7F32",
  silver: "#C0C0C0",
  gold: "#FFD700",
  platinum: "#E5E4E2",
};

const TIER_ICONS = {
  bronze: "star.fill",
  silver: "star.fill",
  gold: "crown.fill",
  platinum: "crown.fill",
};

const TIER_LABELS = {
  bronze: "Bronze",
  silver: "Silver",
  gold: "Gold",
  platinum: "Platinum",
};

export const CustomerLoyaltyCard: React.FC<CustomerLoyaltyCardProps> = ({
  loyaltyData,
  onPress,
  showProgress = true,
  compact = false,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const tierColor = TIER_COLORS[loyaltyData.tier];
  const tierIcon = TIER_ICONS[loyaltyData.tier];
  const tierLabel = TIER_LABELS[loyaltyData.tier];

  const renderProgressBar = () => {
    if (!showProgress || !loyaltyData.progressToNextTier) return null;

    const { percentage } = loyaltyData.progressToNextTier;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <ThemedText variant="small" color="secondary">
            Progress to next tier
          </ThemedText>
          <ThemedText variant="small" color="secondary">
            {percentage.toFixed(0)}%
          </ThemedText>
        </View>
        <View
          style={[
            styles.progressTrack,
            { backgroundColor: theme.colors.border },
          ]}
        >
          <View
            style={[
              styles.progressFill,
              {
                backgroundColor: tierColor,
                width: `${Math.min(percentage, 100)}%`,
              },
            ]}
          />
        </View>
        <View style={styles.progressLabels}>
          <ThemedText variant="caption" color="muted">
            {formatCurrency(loyaltyData.progressToNextTier.currentAmount)}
          </ThemedText>
          <ThemedText variant="caption" color="muted">
            {formatCurrency(loyaltyData.progressToNextTier.requiredAmount)}
          </ThemedText>
        </View>
      </View>
    );
  };

  const renderCompactView = () => (
    <ThemedView variant="card" style={[styles.compactCard, utils.p("md")]}>
      <View style={styles.compactHeader}>
        <View style={styles.tierBadge}>
          <IconSymbol name={tierIcon} size={16} color={tierColor} />
          <ThemedText
            variant="small"
            style={[styles.tierText, { color: tierColor }]}
          >
            {tierLabel}
          </ThemedText>
        </View>
        <View style={styles.pointsContainer}>
          <ThemedText variant="h3" color="primary">
            {(loyaltyData.loyaltyPoints || 0).toLocaleString()}
          </ThemedText>
          <ThemedText variant="caption" color="secondary">
            points
          </ThemedText>
        </View>
      </View>
    </ThemedView>
  );

  const renderFullView = () => (
    <ThemedView variant="card" style={[styles.fullCard, utils.p("lg")]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.tierSection}>
          <View
            style={[styles.tierIcon, { backgroundColor: `${tierColor}20` }]}
          >
            <IconSymbol name={tierIcon} size={24} color={tierColor} />
          </View>
          <View>
            <ThemedText
              variant="h3"
              style={[styles.tierTitle, { color: tierColor }]}
            >
              {tierLabel} Member
            </ThemedText>
            <ThemedText variant="small" color="secondary">
              Loyalty Tier
            </ThemedText>
          </View>
        </View>

        {onPress && (
          <TouchableOpacity onPress={onPress} style={styles.detailsButton}>
            <IconSymbol
              name="chevron.right"
              size={20}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Points Balance */}
      <View style={styles.pointsSection}>
        <View style={styles.pointsDisplay}>
          <ThemedText variant="h1" color="primary">
            {formatLoyaltyPoints(loyaltyData.loyaltyPoints || 0)}
          </ThemedText>
          <ThemedText variant="body" color="secondary">
            Available Points
          </ThemedText>
        </View>

        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <ThemedText variant="h3" color="primary">
              {formatCurrency(loyaltyData.totalPurchases)}
            </ThemedText>
            <ThemedText variant="caption" color="secondary">
              Total Spent
            </ThemedText>
          </View>
          <View style={styles.statItem}>
            <ThemedText variant="h3" color="primary">
              {loyaltyData.totalOrders}
            </ThemedText>
            <ThemedText variant="caption" color="secondary">
              Orders
            </ThemedText>
          </View>
        </View>
      </View>

      {/* Progress Bar */}
      {renderProgressBar()}

      {/* Last Purchase */}
      {loyaltyData.lastPurchase && (
        <View style={styles.lastPurchaseSection}>
          <ThemedText variant="caption" color="muted">
            Last purchase:{" "}
            {new Date(loyaltyData.lastPurchase).toLocaleDateString()}
          </ThemedText>
        </View>
      )}
    </ThemedView>
  );

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent onPress={onPress} activeOpacity={0.8}>
      {compact ? renderCompactView() : renderFullView()}
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  compactCard: {
    marginVertical: 4,
  },
  compactHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  fullCard: {
    marginVertical: 8,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  tierSection: {
    flexDirection: "row",
    alignItems: "center",
  },
  tierIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  tierBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  tierText: {
    marginLeft: 4,
    fontWeight: "600",
  },
  tierTitle: {
    fontWeight: "700",
  },
  detailsButton: {
    padding: 8,
  },
  pointsSection: {
    marginBottom: 16,
  },
  pointsDisplay: {
    alignItems: "center",
    marginBottom: 16,
  },
  pointsContainer: {
    alignItems: "flex-end",
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  progressTrack: {
    height: 6,
    borderRadius: 3,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    borderRadius: 3,
  },
  progressLabels: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 4,
  },
  lastPurchaseSection: {
    alignItems: "center",
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
  },
});
