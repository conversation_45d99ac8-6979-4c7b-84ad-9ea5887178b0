import AsyncStorage from "@react-native-async-storage/async-storage";
import { Alert } from "react-native";
import { BluetoothPermissionHelper } from "../utils/BluetoothPermissionHelper";
import ThermalPrintService, { ThermalPrintResult } from "./ThermalPrintService";
import { TREASURED_LOGO_TEXT } from "../constants/logoConstants";

// Define interfaces locally to avoid circular dependency
export interface OrderData {
  id: string;
  orderNumber?: string;
  number?: string;
  name?: string;
  totalPrice: string;
  total_price?: string; // Shopify API format
  createdAt: string;
  created_at?: string; // Shopify API format
  note?: string; // For staff info
  customAttributes?: { key: string; value: string }[]; // For metadata
  customer?: {
    firstName?: string;
    lastName?: string;
    first_name?: string; // Shopify API format
    last_name?: string; // Shopify API format
    email?: string;
    phone?: string;
  };
  salespersonName?: string;
  salespersonId?: string;
  paymentMethod?: string;
  paymentTransactionId?: string;
  lineItems: {
    id: string;
    title: string;
    name?: string; // Alternative field name
    quantity: number;
    price: string;
    sku?: string;
    variantTitle?: string;
    variant_title?: string; // Shopify API format
    variantId?: string;
    variant_id?: string; // Shopify API format
    productId?: string;
    product_id?: string; // Shopify API format
  }[];
  line_items?: any[]; // Shopify API format
}

export interface ReceiptData {
  orderNumber: string;
  orderDate: string;
  customer: {
    name: string;
    email?: string;
    phone?: string;
  };
  staff: {
    name: string;
    role: string;
  };
  salesAgent?: {
    name: string;
    territory?: string;
  };
  items: {
    id: string;
    title: string;
    quantity: number;
    price: string;
    sku?: string;
    variantTitle?: string;
  }[];
  subtotal: number;
  tax: number;
  total: number;
  paymentMethod: string;
  paymentDetails?: {
    transactionId?: string;
  };
  // Enhanced payment breakdown
  paymentBreakdown?: {
    isSplitPayment: boolean;
    totalAmount: number;
    paymentMethods: any[];
    transactionId: string;
    paymentStatus: string;
    completedAmount?: number;
    pendingAmount?: number;
    remainingAmount?: number;
  };
  store: {
    name: string;
    address?: string;
    phone?: string;
  };
}

const PRINTER_STORAGE_KEY = "@dukalink_printer_address";
const OFFLINE_RECEIPT_PREFIX = "offline_receipt_";

export interface EnhancedPrintResult {
  success: boolean;
  error?: string;
  cancelled?: boolean;
  redirected?: boolean;
  orderId?: string;
}

class EnhancedThermalPrintService {
  private isConnected = false;
  private printerAddress: string | null = null;

  /**
   * Print standardized receipt data directly
   */
  async printStandardizedReceipt(
    standardizedData: any
  ): Promise<EnhancedPrintResult> {
    try {
      console.log("🖨️ Enhanced thermal printing with standardized data...");

      // Ensure thermal printer connection
      await this.ensurePrinterConnection();

      // Generate thermal content using Universal Template
      const thermalContent = await this.generateUniversalThermalContent(
        standardizedData
      );

      // For now, convert standardized data back to legacy format for printing
      // TODO: Create a direct thermal content printing method in ThermalPrintService
      const legacyReceiptData =
        this.convertStandardizedToLegacy(standardizedData);
      const result = await ThermalPrintService.printThermalReceipt(
        legacyReceiptData
      );

      if (result.success) {
        console.log(
          "✅ Enhanced thermal printing successful with standardized data"
        );
        return { success: true, orderId: standardizedData.receiptNumber };
      } else {
        throw new Error(result.error || "Thermal print failed");
      }
    } catch (error) {
      console.error("❌ Enhanced thermal print error:", error);
      return await this.handlePrintError(
        error,
        standardizedData.receiptNumber,
        standardizedData
      );
    }
  }

  /**
   * Convert standardized receipt data to legacy format
   */
  private convertStandardizedToLegacy(standardizedData: any): ReceiptData {
    return {
      orderNumber: standardizedData.receiptNumber,
      orderDate: standardizedData.date + " " + standardizedData.time,
      customer: {
        name: standardizedData.customer.name,
        email: standardizedData.customer.email,
        phone: standardizedData.customer.mobile,
      },
      staff: {
        name: standardizedData.staff.name,
        role: standardizedData.staff.role || "Cashier",
      },
      salesAgent: standardizedData.salesAgent
        ? {
            name: standardizedData.salesAgent.name,
            territory: standardizedData.salesAgent.territory,
          }
        : undefined,
      items: standardizedData.items.map((item: any) => ({
        id: item.id || item.sku || `item_${Date.now()}`,
        title: item.name,
        quantity: item.quantity,
        price: item.unitPrice.toString(),
        sku: item.sku,
        variantTitle: item.variant,
      })),
      subtotal: standardizedData.subtotal,
      tax: standardizedData.tax || 0,
      total: standardizedData.grandTotal,
      paymentMethod: standardizedData.paymentMethods?.[0]?.method || "Cash",
      paymentDetails: {
        transactionId:
          standardizedData.paymentMethods?.[0]?.transactionCode ||
          `TXN-${standardizedData.receiptNumber}`,
      },
      store: {
        name: standardizedData.store.name,
        address: standardizedData.store.address,
        phone: standardizedData.store.mobile,
      },
      // CRITICAL FIX: Include loyalty points for thermal printing
      loyaltyPoints: standardizedData.loyalty
        ? {
            balance: standardizedData.loyalty.totalPoints,
            earned: standardizedData.loyalty.pointsEarned,
            tier: standardizedData.loyalty.tier,
          }
        : undefined,
    };
  }

  /**
   * Print receipt with enhanced error handling and Universal Template integration
   */
  async printReceipt(
    orderData: OrderData,
    loyaltyCompletionData?: any
  ): Promise<EnhancedPrintResult> {
    try {
      console.log("🖨️ Enhanced thermal printing with Universal Template...");

      // Ensure thermal printer connection
      await this.ensurePrinterConnection();

      // Generate standardized receipt data using Universal Template system
      const standardizedData = await this.generateStandardizedReceiptData(
        orderData,
        loyaltyCompletionData // CRITICAL FIX: Pass loyalty completion data
      );

      // Generate thermal receipt content using Universal Template
      const thermalContent = await this.generateUniversalThermalContent(
        standardizedData
      );

      // Execute thermal print with Universal Template content
      // CRITICAL FIX: Use standardized data with loyalty instead of legacy method
      const legacyReceiptData =
        this.convertStandardizedToLegacy(standardizedData);
      const result = await ThermalPrintService.printThermalReceipt(
        legacyReceiptData
      );

      if (result.success) {
        console.log(
          "✅ Enhanced thermal printing successful with Universal Template"
        );
        return { success: true, orderId: orderData.id };
      } else {
        throw new Error(result.error || "Thermal print failed");
      }
    } catch (error) {
      console.error("❌ Enhanced thermal print error:", error);
      return await this.handlePrintError(error, orderData.id, orderData);
    }
  }

  /**
   * Generate standardized receipt data using Universal Template system
   */
  private async generateStandardizedReceiptData(
    orderData: OrderData,
    loyaltyCompletionData?: any
  ): Promise<any> {
    try {
      console.log("🔄 Generating standardized receipt data...");

      // Import StandardizedReceiptService
      const { StandardizedReceiptService } = await import(
        "./StandardizedReceiptService"
      );

      // Convert OrderData to standardized format
      const standardizedData =
        await StandardizedReceiptService.generateStandardizedReceipt(
          orderData,
          null, // No additional payment data for now
          loyaltyCompletionData // CRITICAL FIX: Pass loyalty completion data
        );

      console.log("✅ Standardized receipt data generated:", {
        receiptNumber: standardizedData.receiptNumber,
        customerName: standardizedData.customer.name,
        loyaltyPoints: standardizedData.loyalty?.totalPoints,
        itemCount: standardizedData.items.length,
      });

      return standardizedData;
    } catch (error) {
      console.error("❌ Failed to generate standardized receipt data:", error);
      throw error;
    }
  }

  /**
   * Generate thermal content using Universal Template
   */
  private async generateUniversalThermalContent(
    standardizedData: any
  ): Promise<string> {
    try {
      console.log("🔄 Generating Universal thermal content...");

      // Import StandardizedReceiptService for thermal generation
      const { StandardizedReceiptService } = await import(
        "./StandardizedReceiptService"
      );

      // Generate thermal receipt using Universal Template
      const thermalContent = StandardizedReceiptService.generateThermalReceipt(
        standardizedData,
        32 // 58mm thermal printer width
      );

      console.log("✅ Universal thermal content generated successfully");
      return thermalContent;
    } catch (error) {
      console.error("❌ Failed to generate Universal thermal content:", error);
      throw error;
    }
  }

  /**
   * Generate receipt data from order data (legacy method for fallback)
   */
  private async generateReceiptDataFromOrder(
    orderData: OrderData
  ): Promise<ReceiptData> {
    const customerName = orderData.customer
      ? `${orderData.customer.firstName || ""} ${
          orderData.customer.lastName || ""
        }`.trim()
      : "Walk-in Customer";

    // Try to fetch payment breakdown if transaction ID is available
    let paymentBreakdown: any = undefined;
    if (orderData.paymentTransactionId) {
      try {
        console.log(
          "🔄 Fetching payment breakdown for transaction:",
          orderData.paymentTransactionId
        );
        const { enhancedPaymentService } = await import(
          "../services/enhanced-payment-service"
        );
        const statusResponse =
          await enhancedPaymentService.getTransactionStatus(
            orderData.paymentTransactionId
          );

        console.log("📊 Payment status response:", {
          success: statusResponse.success,
          hasData: !!statusResponse.data,
          error: statusResponse.error,
        });

        if (statusResponse.success && statusResponse.data) {
          const transactionData = statusResponse.data;
          const paymentMethods = transactionData.paymentMethods || [];

          console.log("🔍 Transaction data structure:", {
            keys: Object.keys(transactionData),
            transactionId: transactionData.transactionId,
            status: transactionData.status,
            totalAmount: transactionData.totalAmount,
            completedAmount: transactionData.completedAmount,
            remainingAmount: transactionData.remainingAmount,
            isSplitPayment: transactionData.isSplitPayment,
            paymentMethodsCount: paymentMethods.length,
          });

          paymentBreakdown = {
            isSplitPayment:
              transactionData.isSplitPayment || paymentMethods.length > 1,
            totalAmount: parseFloat(
              transactionData.totalAmount?.toString() || "0"
            ),
            paymentMethods: paymentMethods.map((method: any) => ({
              id: method.id,
              type: method.type || method.method_type || "unknown",
              name: method.name || method.method_name || "Unknown Method",
              amount: parseFloat(method.amount?.toString() || "0"),
              status: method.status || "unknown",
              processedAt: method.processedAt || method.processed_at,
              metadata: method.metadata || {},
            })),
            transactionId: transactionData.transactionId || "unknown",
            paymentStatus: transactionData.status || "unknown",
            completedAmount: parseFloat(
              transactionData.completedAmount?.toString() || "0"
            ),
            pendingAmount: parseFloat(
              transactionData.remainingAmount?.toString() || "0"
            ),
            remainingAmount: parseFloat(
              transactionData.remainingAmount?.toString() || "0"
            ),
          };

          console.log("✅ Payment breakdown created:", {
            isSplitPayment: paymentBreakdown.isSplitPayment,
            totalAmount: paymentBreakdown.totalAmount,
            completedAmount: paymentBreakdown.completedAmount,
            paymentMethodsCount: paymentBreakdown.paymentMethods.length,
          });
        }
      } catch (error) {
        console.error(
          "❌ Failed to fetch payment breakdown for thermal receipt:",
          error
        );
        console.error("Error details:", {
          message: error instanceof Error ? error.message : "Unknown error",
          transactionId: orderData.paymentTransactionId,
          orderData: {
            id: orderData.id,
            orderNumber: orderData.orderNumber,
            totalPrice: orderData.totalPrice,
          },
        });
        // Continue without payment breakdown - this is not a critical failure
        paymentBreakdown = null;
      }
    } else {
      console.log(
        "ℹ️ No payment transaction ID available, skipping payment breakdown fetch"
      );
    }

    return {
      orderNumber:
        orderData.orderNumber ||
        orderData.number ||
        orderData.name ||
        orderData.id,
      orderDate: orderData.createdAt,
      customer: {
        name: customerName,
        email: orderData.customer?.email,
        phone: orderData.customer?.phone,
      },
      staff: {
        name: orderData.salespersonName || "POS Staff",
        role: "Cashier",
      },
      items: orderData.lineItems.map((item) => ({
        id: item.id,
        title: item.title,
        quantity: item.quantity,
        price: item.price,
        sku: item.sku,
        variantTitle: item.variantTitle,
      })),
      subtotal: parseFloat(orderData.totalPrice), // No tax calculations
      tax: 0, // POS system no longer calculates taxes
      total: parseFloat(orderData.totalPrice),
      paymentMethod: orderData.paymentMethod || "Cash",
      paymentDetails: {
        transactionId: orderData.paymentTransactionId || `TXN-${orderData.id}`,
      },
      // Enhanced payment breakdown
      paymentBreakdown,
      store: {
        name: "Treasured Scents",
        address: "Nairobi, Kenya",
        phone: "+254722924706",
      },
    };
  }

  /**
   * Print receipt by order ID (for reprinting)
   */
  async printReceiptById(orderId: string): Promise<EnhancedPrintResult> {
    try {
      // Try to get receipt data from offline storage first
      let receiptData = await this.getOfflineReceiptData(orderId);

      // If no offline data, try to fetch order from API and generate receipt data
      if (!receiptData) {
        receiptData = await this.fetchOrderAndGenerateReceiptData(orderId);
      }

      if (receiptData) {
        // Ensure thermal printer connection
        await this.ensurePrinterConnection();

        // Execute thermal print
        const result = await ThermalPrintService.printThermalReceipt(
          receiptData
        );

        if (result.success) {
          // Remove from offline storage after successful print
          await this.removeOfflineReceipt(orderId);
          return { success: true, orderId };
        } else {
          throw new Error(result.error || "Thermal print failed");
        }
      } else {
        throw new Error("Unable to fetch order data for order ID: " + orderId);
      }
    } catch (error) {
      return await this.handlePrintError(error, orderId);
    }
  }

  /**
   * Fetch order from API and generate receipt data
   */
  private async fetchOrderAndGenerateReceiptData(
    orderId: string
  ): Promise<ReceiptData | null> {
    try {
      console.log(`Fetching order data for ID: ${orderId}`);

      // Import the API client dynamically to avoid circular dependencies
      const { getAPIClient } = await import("./api/dukalink-client");
      const apiClient = getAPIClient();

      // Try to fetch the order from the API
      const response = await apiClient.getOrder("default", orderId);

      if (response.success && response.data) {
        console.log(`Order fetched successfully, generating receipt data...`);

        const order = response.data;

        // Convert API order to OrderData format for receipt generation
        const orderData: OrderData = {
          id: order.id,
          orderNumber: order.name || order.orderNumber || order.id,
          totalPrice: order.totalPrice || order.total_price || "0.00",
          createdAt:
            order.createdAt || order.created_at || new Date().toISOString(),
          salespersonName: order.note?.includes("Staff:")
            ? order.note.split("Staff:")[1]?.split("|")[0]?.trim() ||
              "POS Staff"
            : "POS Staff",
          salespersonId: order.customAttributes?.find(
            (attr) => attr.key === "posStaffId"
          )?.value,
          paymentMethod:
            order.customAttributes?.find((attr) => attr.key === "paymentMethod")
              ?.value || "Cash",
          customer: order.customer
            ? {
                firstName:
                  order.customer.firstName || order.customer.first_name || "",
                lastName:
                  order.customer.lastName || order.customer.last_name || "",
                email: order.customer.email || "",
                phone: order.customer.phone || "",
              }
            : undefined,
          lineItems: (order.lineItems || order.line_items || []).map(
            (item: any) => ({
              id: item.id,
              title: item.title || item.name,
              quantity: item.quantity,
              price: item.price,
              sku: item.sku,
              variantId: item.variantId || item.variant_id,
              productId: item.productId || item.product_id,
              variantTitle: item.variantTitle || item.variant_title,
            })
          ),
        };

        // Generate receipt data from order data
        const receiptData = await this.generateReceiptDataFromOrder(orderData);

        console.log(`Receipt data generated successfully for order ${orderId}`);
        return receiptData;
      } else {
        console.error(`Failed to fetch order ${orderId}:`, response.error);
        return null;
      }
    } catch (error) {
      console.error(`Error fetching order ${orderId}:`, error);
      return null;
    }
  }

  /**
   * Ensure thermal printer connection
   */
  private async ensurePrinterConnection(): Promise<void> {
    // First check if ThermalPrintService has a verified connection
    const hasVerifiedConnection =
      await ThermalPrintService.verifyPrinterConnection();
    if (hasVerifiedConnection) {
      this.isConnected = true;
      this.printerAddress = await AsyncStorage.getItem(PRINTER_STORAGE_KEY);
      return;
    }

    // Step 1: Ensure Bluetooth permissions and setup
    try {
      const bluetoothReady =
        await BluetoothPermissionHelper.ensureBluetoothReady();
      if (!bluetoothReady.granted) {
        throw new Error(
          bluetoothReady.error || "Bluetooth permissions not granted"
        );
      }
    } catch (error) {
      throw new Error(
        `Bluetooth setup failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }

    // Step 2: Try to get saved printer address
    this.printerAddress = await AsyncStorage.getItem(PRINTER_STORAGE_KEY);

    if (!this.printerAddress) {
      throw new Error(
        "No thermal printer configured. Please set up printer first."
      );
    }

    // Step 3: Use ThermalPrintService to connect (this ensures consistent state)
    try {
      const connected = await ThermalPrintService.connectToPrinter(
        this.printerAddress
      );
      if (!connected) {
        throw new Error("Failed to establish connection");
      }
      this.isConnected = true;
    } catch (error) {
      throw new Error(
        `Failed to connect to thermal printer: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Handle print errors with user guidance
   */
  private async handlePrintError(
    error: any,
    orderId: string,
    orderData?: OrderData
  ): Promise<EnhancedPrintResult> {
    // Store receipt for offline printing
    await this.storeOfflineReceipt(orderId, orderData);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    if (errorMessage.includes("No thermal printer configured")) {
      return this.handlePrinterConfigurationError(orderId);
    }

    if (errorMessage.includes("Failed to connect")) {
      return this.handleConnectionError(orderId, errorMessage);
    }

    return { success: false, error: errorMessage, orderId };
  }

  /**
   * Store receipt data for offline printing
   */
  private async storeOfflineReceipt(
    orderId: string,
    orderData?: OrderData
  ): Promise<void> {
    try {
      const offlineReceiptKey = `${OFFLINE_RECEIPT_PREFIX}${orderId}`;

      // If we have order data, generate and store the full receipt data
      if (orderData) {
        const receiptData = await this.generateReceiptDataFromOrder(orderData);
        const offlineData = {
          orderId,
          timestamp: new Date().toISOString(),
          status: "pending_print",
          receiptData, // Store the actual receipt data
        };

        await AsyncStorage.setItem(
          offlineReceiptKey,
          JSON.stringify(offlineData)
        );
        console.log(`Stored offline receipt data for order ${orderId}`);
      } else {
        // Fallback: store placeholder that indicates offline receipt is needed
        const offlineData = {
          orderId,
          timestamp: new Date().toISOString(),
          status: "pending_print",
        };

        await AsyncStorage.setItem(
          offlineReceiptKey,
          JSON.stringify(offlineData)
        );
        console.log(`Stored offline receipt placeholder for order ${orderId}`);
      }
    } catch (error) {
      console.error("Failed to store offline receipt:", error);
    }
  }

  /**
   * Get offline receipt data
   */
  private async getOfflineReceiptData(
    orderId: string
  ): Promise<ReceiptData | null> {
    try {
      const offlineReceiptKey = `${OFFLINE_RECEIPT_PREFIX}${orderId}`;
      const storedData = await AsyncStorage.getItem(offlineReceiptKey);

      if (storedData) {
        const offlineData = JSON.parse(storedData);

        // If we have stored receipt data, return it
        if (offlineData.receiptData) {
          console.log(`Found stored receipt data for order ${orderId}`);
          return offlineData.receiptData;
        } else {
          console.log(
            `Found offline receipt placeholder for order ${orderId}, but no receipt data`
          );
          return null;
        }
      }

      console.log(`No offline receipt data found for order ${orderId}`);
      return null;
    } catch (error) {
      console.error("Failed to get offline receipt data:", error);
      return null;
    }
  }

  /**
   * Remove offline receipt after successful print
   */
  private async removeOfflineReceipt(orderId: string): Promise<void> {
    try {
      const offlineReceiptKey = `${OFFLINE_RECEIPT_PREFIX}${orderId}`;
      await AsyncStorage.removeItem(offlineReceiptKey);
    } catch (error) {
      console.error("Failed to remove offline receipt:", error);
    }
  }

  /**
   * Handle printer configuration errors
   */
  private handlePrinterConfigurationError(
    orderId: string
  ): Promise<EnhancedPrintResult> {
    return new Promise((resolve) => {
      Alert.alert(
        "Thermal Printer Setup Required",
        "No thermal printer is configured. Would you like to set up a thermal printer now?",
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () =>
              resolve({
                success: false,
                error: "No thermal printer configured",
                cancelled: true,
                orderId,
              }),
          },
          {
            text: "Setup Printer",
            onPress: () => {
              // Navigate to thermal printer setup screen
              resolve({
                success: false,
                error: "Redirected to thermal printer setup",
                redirected: true,
                orderId,
              });
            },
          },
        ]
      );
    });
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(
    orderId: string,
    errorMessage: string
  ): Promise<EnhancedPrintResult> {
    return new Promise((resolve) => {
      Alert.alert(
        "Thermal Printer Connection Error",
        `Failed to connect to thermal printer: ${errorMessage}\n\nWould you like to retry or use standard printing?`,
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () =>
              resolve({
                success: false,
                error: errorMessage,
                cancelled: true,
                orderId,
              }),
          },
          {
            text: "Retry",
            onPress: async () => {
              // Reset connection and retry
              this.isConnected = false;
              this.printerAddress = null;
              const retryResult = await this.printReceiptById(orderId);
              resolve(retryResult);
            },
          },
          {
            text: "Use Standard Print",
            onPress: () => {
              resolve({
                success: false,
                error: "User chose standard printing",
                redirected: true,
                orderId,
              });
            },
          },
        ]
      );
    });
  }

  /**
   * Test thermal printer connection and print
   */
  async testThermalPrint(): Promise<ThermalPrintResult> {
    try {
      await this.ensurePrinterConnection();
      return await ThermalPrintService.printTestReceipt();
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Test print failed",
      };
    }
  }

  /**
   * Get list of offline receipts pending print
   */
  async getOfflineReceipts(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      return keys
        .filter((key) => key.startsWith(OFFLINE_RECEIPT_PREFIX))
        .map((key) => key.replace(OFFLINE_RECEIPT_PREFIX, ""));
    } catch (error) {
      console.error("Failed to get offline receipts:", error);
      return [];
    }
  }

  /**
   * Print all offline receipts
   */
  async printOfflineReceipts(): Promise<{ success: number; failed: number }> {
    const offlineOrderIds = await this.getOfflineReceipts();
    let success = 0;
    let failed = 0;

    for (const orderId of offlineOrderIds) {
      const result = await this.printReceiptById(orderId);
      if (result.success) {
        success++;
      } else {
        failed++;
      }
    }

    return { success, failed };
  }

  /**
   * Check if thermal printer is available and connected
   */
  async isThermalPrinterAvailable(): Promise<boolean> {
    try {
      // Always verify actual connection instead of just checking state
      await this.ensurePrinterConnection();
      return true;
    } catch (error) {
      console.log("Thermal printer not available:", error);
      return false;
    }
  }

  /**
   * Static method to check if thermal printer is available (with permission check)
   */
  static async isThermalPrinterAvailable(): Promise<boolean> {
    try {
      // First check if Bluetooth permissions are available
      const permissionResult =
        await BluetoothPermissionHelper.checkBluetoothPermissions();
      if (!permissionResult.granted) {
        console.log(
          "Thermal printer not available: Bluetooth permissions not granted"
        );
        return false;
      }

      // Check if printer is configured
      const printerAddress = await AsyncStorage.getItem(PRINTER_STORAGE_KEY);
      return !!printerAddress;
    } catch (error) {
      console.error("Error checking thermal printer availability:", error);
      return false;
    }
  }

  /**
   * Get thermal printer status
   */
  getThermalPrinterStatus(): {
    isConnected: boolean;
    printerType: string | null;
    connectedDevice: any;
  } {
    return {
      isConnected: ThermalPrintService.isConnectedToPrinter(),
      printerType: ThermalPrintService.getPrinterType(),
      connectedDevice: ThermalPrintService.getConnectedDevice(),
    };
  }
}

// Export singleton instance
export default new EnhancedThermalPrintService();
