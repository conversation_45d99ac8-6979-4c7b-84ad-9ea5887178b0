/**
 * POS User Switching API Routes
 *
 * Handles PIN-based user switching, session context management,
 * and audit trail endpoints for multi-user POS terminals.
 */

const express = require("express");
const rateLimit = require("express-rate-limit");
const { body, param, query, validationResult } = require("express-validator");
const { authenticateToken } = require("../middleware/auth-mysql");
const {
  multiUserSessionService,
} = require("../services/multi-user-session-service");
const { databaseManager } = require("../config/database-manager");

const router = express.Router();

// Rate limiting for PIN operations
const pinRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 PIN attempts per windowMs
  message: {
    success: false,
    error: "Too many PIN attempts. Please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for user switching
const switchRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Limit each IP to 20 switch attempts per windowMs
  message: {
    success: false,
    error: "Too many user switch attempts. Please try again later.",
  },
});

/**
 * Initialize or reset user PIN
 * POST /api/pos/user-switching/initialize-pin
 */
router.post(
  "/initialize-pin",
  authenticateToken,
  pinRateLimit,
  [
    body("staffId").notEmpty().withMessage("Staff ID is required"),
    body("pin")
      .matches(/^\d{4,6}$/)
      .withMessage("PIN must be 4-6 digits"),
    body("confirmPin").custom((value, { req }) => {
      if (value !== req.body.pin) {
        throw new Error("PIN confirmation does not match");
      }
      return true;
    }),
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          details: errors.array(),
        });
      }

      const { staffId, pin } = req.body;
      const adminStaffId = req.user.id;

      // Check if admin has permission to set PINs
      if (
        !req.user.permissions.includes("manage_staff") &&
        req.user.role !== "super_admin"
      ) {
        return res.status(403).json({
          success: false,
          error: "Insufficient permissions to set user PINs",
        });
      }

      const result = await multiUserSessionService.initializeUserPin(
        staffId,
        pin,
        adminStaffId
      );

      res.json({
        success: true,
        message: "PIN initialized successfully",
        data: result,
      });
    } catch (error) {
      console.error("PIN initialization error:", error);
      res.status(400).json({
        success: false,
        error: error.message || "Failed to initialize PIN",
      });
    }
  }
);

/**
 * Switch to another user using PIN
 * POST /api/pos/user-switching/switch-user
 */
router.post(
  "/switch-user",
  authenticateToken,
  switchRateLimit,
  [
    body("targetStaffId").notEmpty().withMessage("Target staff ID is required"),
    body("pin")
      .matches(/^\d{4,6}$/)
      .withMessage("PIN must be 4-6 digits"),
    body("reason")
      .optional()
      .isLength({ max: 255 })
      .withMessage("Reason too long"),
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          details: errors.array(),
        });
      }

      const { targetStaffId, pin, reason = "manual_switch" } = req.body;

      // Get session ID from token
      const sessionId = req.sessionId; // Set by auth middleware

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: "Session ID not found",
        });
      }

      const result = await multiUserSessionService.switchUser(
        sessionId,
        targetStaffId,
        pin,
        reason
      );

      res.json({
        success: true,
        message: "User switched successfully",
        data: {
          sessionContext: result.sessionContext,
          switchId: result.switchId,
          currentUser: result.sessionContext.currentUser,
        },
      });
    } catch (error) {
      console.error("User switch error:", error);
      res.status(400).json({
        success: false,
        error: error.message || "Failed to switch user",
      });
    }
  }
);

/**
 * Switch back to previous user
 * POST /api/pos/user-switching/switch-back
 */
router.post("/switch-back", authenticateToken, async (req, res) => {
  try {
    const sessionId = req.sessionId;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: "Session ID not found",
      });
    }

    // Get current session context to find the active switch ID
    const sessionContext = await multiUserSessionService.getSessionContext(
      sessionId
    );
    if (!sessionContext || !sessionContext.lastSwitchId) {
      return res.status(400).json({
        success: false,
        error: "No active user switch found",
      });
    }

    const result = await multiUserSessionService.switchBack(
      sessionId,
      sessionContext.lastSwitchId
    );

    res.json({
      success: true,
      message: "Switched back to previous user",
      data: {
        sessionContext: result.sessionContext,
        currentUser: result.sessionContext.currentUser,
      },
    });
  } catch (error) {
    console.error("Switch back error:", error);
    res.status(400).json({
      success: false,
      error: error.message || "Failed to switch back",
    });
  }
});

/**
 * Get current session context
 * GET /api/pos/user-switching/session-context
 */
router.get("/session-context", authenticateToken, async (req, res) => {
  try {
    const sessionId = req.sessionId;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: "Session ID not found",
      });
    }

    const sessionContext = await multiUserSessionService.getSessionContext(
      sessionId
    );

    if (!sessionContext) {
      return res.status(404).json({
        success: false,
        error: "Session context not found",
      });
    }

    res.json({
      success: true,
      data: {
        sessionContext,
        hasActiveSwitch: sessionContext.userStack.length > 0,
        canSwitchBack: sessionContext.userStack.length > 0,
      },
    });
  } catch (error) {
    console.error("Get session context error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get session context",
    });
  }
});

/**
 * Get user switch history for audit
 * GET /api/pos/user-switching/switch-history
 */
router.get(
  "/switch-history",
  authenticateToken,
  [
    query("limit")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Limit must be between 1 and 100"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          details: errors.array(),
        });
      }

      const sessionId = req.sessionId;
      const limit = parseInt(req.query.limit) || 50;

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: "Session ID not found",
        });
      }

      const history = await multiUserSessionService.getUserSwitchHistory(
        sessionId,
        limit
      );

      res.json({
        success: true,
        data: {
          history,
          count: history.length,
        },
      });
    } catch (error) {
      console.error("Get switch history error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get switch history",
      });
    }
  }
);

/**
 * Get available staff for switching
 * GET /api/pos/user-switching/available-staff
 */
router.get("/available-staff", authenticateToken, async (req, res) => {
  try {
    // Get all active staff with PINs set (excluding current user)
    const [staffRows] = await databaseManager.executeQuery(
      `SELECT id, username, name, role, commission_rate,
                CASE WHEN pin IS NOT NULL THEN 1 ELSE 0 END as has_pin
         FROM pos_staff 
         WHERE is_active = 1 AND id != ?
         ORDER BY name`,
      [req.user.id]
    );

    res.json({
      success: true,
      data: {
        staff: staffRows,
        availableCount: staffRows.filter((s) => s.has_pin).length,
        totalCount: staffRows.length,
      },
    });
  } catch (error) {
    console.error("Get available staff error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get available staff",
    });
  }
});

/**
 * Get session statistics (admin only)
 * GET /api/pos/user-switching/session-stats
 */
router.get("/session-stats", authenticateToken, async (req, res) => {
  try {
    // Check admin permissions
    if (
      !req.user.permissions.includes("view_reports") &&
      req.user.role !== "super_admin"
    ) {
      return res.status(403).json({
        success: false,
        error: "Insufficient permissions to view session statistics",
      });
    }

    const stats = await multiUserSessionService.getSessionStats();

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Get session stats error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get session statistics",
    });
  }
});

/**
 * Validate PIN without switching (for verification)
 * POST /api/pos/user-switching/validate-pin
 */
router.post(
  "/validate-pin",
  authenticateToken,
  pinRateLimit,
  [
    body("staffId").notEmpty().withMessage("Staff ID is required"),
    body("pin")
      .matches(/^\d{4,6}$/)
      .withMessage("PIN must be 4-6 digits"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          details: errors.array(),
        });
      }

      const { staffId, pin } = req.body;
      const sessionId = req.sessionId;

      const result = await multiUserSessionService.validatePin(
        staffId,
        pin,
        sessionId,
        req.ip
      );

      res.json({
        success: true,
        message: "PIN validated successfully",
        data: {
          valid: result.success,
          staff: result.staff,
        },
      });
    } catch (error) {
      console.error("PIN validation error:", error);
      res.status(400).json({
        success: false,
        error: error.message || "PIN validation failed",
      });
    }
  }
);

/**
 * Get audit trail for security monitoring (admin only)
 * GET /api/pos/user-switching/audit-trail
 */
router.get(
  "/audit-trail",
  authenticateToken,
  [
    query("staffId").optional().isString(),
    query("eventTypes").optional().isString(),
    query("startDate").optional().isISO8601(),
    query("endDate").optional().isISO8601(),
    query("limit")
      .optional()
      .isInt({ min: 1, max: 500 })
      .withMessage("Limit must be between 1 and 500"),
  ],
  async (req, res) => {
    try {
      // Check admin permissions
      if (
        !req.user.permissions.includes("view_audit_logs") &&
        req.user.role !== "super_admin"
      ) {
        return res.status(403).json({
          success: false,
          error: "Insufficient permissions to view audit trail",
        });
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          details: errors.array(),
        });
      }

      const AuditService = require("../services/audit-service");
      const auditService = new AuditService(databaseManager);

      const criteria = {
        staffId: req.query.staffId,
        eventTypes: req.query.eventTypes
          ? req.query.eventTypes.split(",")
          : null,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        limit: parseInt(req.query.limit) || 100,
        offset: parseInt(req.query.offset) || 0,
      };

      const auditLogs = await auditService.searchAuditLogs(criteria);
      const stats = await auditService.getSecurityStats("24h");

      res.json({
        success: true,
        data: {
          auditLogs,
          stats,
          criteria,
        },
      });
    } catch (error) {
      console.error("Get audit trail error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get audit trail",
      });
    }
  }
);

module.exports = router;
