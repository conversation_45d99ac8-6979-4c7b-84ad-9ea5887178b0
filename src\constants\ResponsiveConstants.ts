/**
 * Responsive Design Constants for Dukalink POS
 * Defines breakpoints and responsive behavior for web-native experience
 */

import { Platform } from "react-native";

// Breakpoint definitions
export const BREAKPOINTS = {
  mobile: 0,
  tablet: 768,
  desktop: 1024,
  large: 1440,
} as const;

// Screen size categories
export type ScreenSize = "mobile" | "tablet" | "desktop" | "large";

// Platform detection
export const isWeb = Platform.OS === "web";
export const isMobile = Platform.OS === "ios" || Platform.OS === "android";

// Container max widths for different screen sizes
export const CONTAINER_MAX_WIDTHS = {
  mobile: "100%",
  tablet: 768,
  desktop: 1200,
  large: 1400,
} as const;

// Responsive spacing multipliers
export const SPACING_MULTIPLIERS = {
  mobile: 1,
  tablet: 1.2,
  desktop: 1.5,
  large: 1.8,
} as const;

// Modal sizing for different screen sizes
export const MODAL_SIZES = {
  small: {
    mobile: { width: "90%" as const, maxWidth: 400 },
    tablet: { width: "70%" as const, maxWidth: 500 },
    desktop: { width: "50%" as const, maxWidth: 600 },
    large: { width: "40%" as const, maxWidth: 700 },
  },
  medium: {
    mobile: { width: "95%" as const, maxWidth: 500 },
    tablet: { width: "80%" as const, maxWidth: 700 },
    desktop: { width: "60%" as const, maxWidth: 800 },
    large: { width: "50%" as const, maxWidth: 900 },
  },
  large: {
    mobile: { width: "98%" as const, maxWidth: 600 },
    tablet: { width: "90%" as const, maxWidth: 900 },
    desktop: { width: "70%" as const, maxWidth: 1000 },
    large: { width: "60%" as const, maxWidth: 1200 },
  },
} as const;

// Button sizing for different screen sizes
export const BUTTON_SIZES = {
  small: {
    mobile: { height: 36, paddingHorizontal: 12, fontSize: 14 },
    tablet: { height: 40, paddingHorizontal: 16, fontSize: 15 },
    desktop: { height: 44, paddingHorizontal: 20, fontSize: 16 },
    large: { height: 48, paddingHorizontal: 24, fontSize: 17 },
  },
  medium: {
    mobile: { height: 44, paddingHorizontal: 16, fontSize: 16 },
    tablet: { height: 48, paddingHorizontal: 20, fontSize: 17 },
    desktop: { height: 52, paddingHorizontal: 24, fontSize: 18 },
    large: { height: 56, paddingHorizontal: 28, fontSize: 19 },
  },
  large: {
    mobile: { height: 52, paddingHorizontal: 20, fontSize: 18 },
    tablet: { height: 56, paddingHorizontal: 24, fontSize: 19 },
    desktop: { height: 60, paddingHorizontal: 28, fontSize: 20 },
    large: { height: 64, paddingHorizontal: 32, fontSize: 21 },
  },
} as const;

// Typography scaling for different screen sizes
export const TYPOGRAPHY_SCALES = {
  mobile: 1,
  tablet: 1.1,
  desktop: 1.2,
  large: 1.3,
} as const;

// Grid column counts for different screen sizes
export const GRID_COLUMNS = {
  mobile: 1,
  tablet: 2,
  desktop: 3,
  large: 4,
} as const;

// Navigation heights for different screen sizes
export const NAVIGATION_HEIGHTS = {
  mobile: 60,
  tablet: 70,
  desktop: 80,
  large: 90,
} as const;
