import { Platform } from "react-native";
import { ReceiptData } from "../components/receipt/ReceiptGenerator";

export interface T220MDPrintResult {
  success: boolean;
  error?: string;
  method?: "web-serial" | "web-usb" | "network" | "browser" | "cloud";
}

export interface T220MDConfig {
  // Web Serial API configuration (USB - no server required)
  serialBaudRate?: number;
  serialDataBits?: number;
  serialStopBits?: number;
  serialParity?: "none" | "even" | "odd";

  // Network configuration
  printerIP?: string;
  printerPort?: number;

  // Cloud print configuration
  cloudPrintService?: "printnode" | "ezeep" | "custom";
  cloudApiKey?: string;
  cloudPrinterId?: string;

  // Paper configuration
  paperWidth: number; // 80mm for T220MD
  charactersPerLine: number; // Usually 48 chars for 80mm

  // ESC/POS settings
  encoding: string;
  cutPaper: boolean;
  openDrawer: boolean;
}

/**
 * T220MD Thermal Printer Service for Web
 * Supports multiple connection methods for T220MD printer
 */
export class T220MDPrintService {
  private static config: T220MDConfig = {
    // Web Serial API settings (USB - no server required)
    serialBaudRate: 9600, // Standard for T220MD
    serialDataBits: 8,
    serialStopBits: 1,
    serialParity: "none",

    // Paper configuration
    paperWidth: 80, // 80mm paper width
    charactersPerLine: 48, // Standard for 80mm thermal printers
    encoding: "utf-8",
    cutPaper: true,
    openDrawer: false,

    // Network fallback
    printerPort: 9100, // Standard ESC/POS port
  };

  private static serialPort: any = null;
  private static isSerialSupported = false;

  /**
   * Create properly aligned two-column line for T220MD printer
   * @param leftText - Text for left column
   * @param rightText - Text for right column
   * @param totalWidth - Total character width (default: 48 for 80mm thermal)
   * @returns Properly aligned string
   */
  private static createAlignedLine(
    leftText: string,
    rightText: string,
    totalWidth: number = 48
  ): string {
    // Ensure we don't exceed the total width
    const maxLeftWidth = totalWidth - rightText.length - 2; // Reserve 2 chars minimum spacing
    const truncatedLeft =
      leftText.length > maxLeftWidth
        ? leftText.substring(0, maxLeftWidth - 1) + "…"
        : leftText;

    // Calculate spacing needed
    const spacing = Math.max(
      1,
      totalWidth - truncatedLeft.length - rightText.length
    );

    return `${truncatedLeft}${" ".repeat(spacing)}${rightText}`;
  }

  /**
   * Add enhanced payment details with support for split payments
   */
  private static addPaymentDetails(
    receiptData: ReceiptData,
    addText: (text: string) => void,
    addCommand: (command: number[]) => void
  ): void {
    // Check if we have enhanced payment breakdown
    if (
      receiptData.paymentBreakdown &&
      receiptData.paymentBreakdown.isSplitPayment
    ) {
      T220MDPrintService.addSplitPaymentDetails(
        receiptData.paymentBreakdown,
        addText,
        addCommand
      );
    } else {
      T220MDPrintService.addSinglePaymentDetails(
        receiptData,
        addText,
        addCommand
      );
    }
  }

  /**
   * Add split payment details
   */
  private static addSplitPaymentDetails(
    breakdown: any,
    addText: (text: string) => void,
    addCommand: (command: number[]) => void
  ): void {
    // Payment header
    addText("PAYMENT DETAILS (SPLIT)\n");
    addText(`Transaction: ${breakdown.transactionId}\n`);
    addText(`Status: ${breakdown.paymentStatus.toUpperCase()}\n`);
    addText("=".repeat(48) + "\n");
    addText("PAYMENT BREAKDOWN:\n");

    // Print each payment method
    breakdown.paymentMethods.forEach((method: any, index: number) => {
      // Method name and amount
      const methodLine = T220MDPrintService.createAlignedLine(
        `${index + 1}. ${method.name}`,
        `KSh ${method.amount.toFixed(2)}`,
        48
      );
      addText(methodLine + "\n");

      // Status
      addText(`   Status: ${method.status.toUpperCase()}\n`);

      // Method-specific details
      T220MDPrintService.addMethodSpecificDetails(method, addText);

      // Add spacing between methods
      addText("\n");
    });

    // Payment summary
    addText("=".repeat(48) + "\n");

    addCommand([0x1b, 0x45, 0x01]); // Bold on
    const summaryLine = T220MDPrintService.createAlignedLine(
      "TOTAL PAID:",
      `KSh ${(breakdown.completedAmount || 0).toFixed(2)}`,
      48
    );
    addText(summaryLine + "\n");
    addCommand([0x1b, 0x45, 0x00]); // Bold off

    if (breakdown.remainingAmount && breakdown.remainingAmount > 0) {
      const remainingLine = T220MDPrintService.createAlignedLine(
        "REMAINING:",
        `KSh ${breakdown.remainingAmount.toFixed(2)}`,
        48
      );
      addText(remainingLine + "\n");
    }
  }

  /**
   * Add single payment details
   */
  private static addSinglePaymentDetails(
    receiptData: ReceiptData,
    addText: (text: string) => void,
    addCommand: (command: number[]) => void
  ): void {
    addText(`Payment: ${receiptData.paymentMethod || "Cash"}\n`);

    if (receiptData.paymentDetails?.transactionId) {
      addText(`Transaction: ${receiptData.paymentDetails.transactionId}\n`);
    }

    // Add single payment method details if available
    if (
      receiptData.paymentBreakdown &&
      receiptData.paymentBreakdown.paymentMethods.length === 1
    ) {
      const method = receiptData.paymentBreakdown.paymentMethods[0];
      T220MDPrintService.addMethodSpecificDetails(method, addText);
    }
  }

  /**
   * Add method-specific payment details
   */
  private static addMethodSpecificDetails(
    method: any,
    addText: (text: string) => void
  ): void {
    const metadata = method.metadata || {};

    switch (method.type) {
      case "cash":
        if (metadata.amountTendered) {
          const tenderedLine = T220MDPrintService.createAlignedLine(
            "   Amount Tendered:",
            `KSh ${metadata.amountTendered.toFixed(2)}`,
            48
          );
          addText(tenderedLine + "\n");

          if (metadata.change && metadata.change > 0) {
            const changeLine = T220MDPrintService.createAlignedLine(
              "   Change:",
              `KSh ${metadata.change.toFixed(2)}`,
              48
            );
            addText(changeLine + "\n");
          }
        }
        break;

      case "mpesa":
        if (metadata.mpesaReceiptNumber) {
          addText(`   M-Pesa Receipt: ${metadata.mpesaReceiptNumber}\n`);
        }
        if (metadata.transactionCode) {
          addText(`   Transaction Code: ${metadata.transactionCode}\n`);
        }
        if (metadata.phoneNumber) {
          addText(`   Phone: ${metadata.phoneNumber}\n`);
        }
        break;

      case "absa_till":
        if (metadata.transactionCode) {
          addText(`   Transaction Code: ${metadata.transactionCode}\n`);
        }
        if (metadata.tillNumber) {
          addText(`   Till Number: ${metadata.tillNumber}\n`);
        }
        if (metadata.absaReceiptNumber) {
          addText(`   ABSA Receipt: ${metadata.absaReceiptNumber}\n`);
        }
        break;

      case "card":
        if (metadata.cardType) {
          addText(`   Card Type: ${metadata.cardType}\n`);
        }
        if (metadata.lastFourDigits) {
          addText(`   Card: ****${metadata.lastFourDigits}\n`);
        }
        if (metadata.authorizationCode) {
          addText(`   Auth Code: ${metadata.authorizationCode}\n`);
        }
        break;

      case "credit":
        if (metadata.dueDate) {
          addText(`   Due Date: ${metadata.dueDate}\n`);
        }
        if (metadata.paymentTerms) {
          addText(`   Terms: ${metadata.paymentTerms}\n`);
        }
        if (metadata.creditLimit) {
          const creditLine = T220MDPrintService.createAlignedLine(
            "   Credit Limit:",
            `KSh ${metadata.creditLimit.toFixed(2)}`,
            48
          );
          addText(creditLine + "\n");
        }
        break;
    }
  }

  /**
   * Initialize T220MD print service
   */
  static async init(config?: Partial<T220MDConfig>): Promise<boolean> {
    try {
      if (Platform.OS !== "web") {
        console.log("T220MD service is for web platform only");
        return false;
      }

      if (config) {
        this.config = { ...this.config, ...config };
      }

      // Check Web Serial API support
      this.isSerialSupported = "serial" in navigator;

      if (this.isSerialSupported) {
        console.log(
          "✅ Web Serial API supported - Direct USB printing available"
        );
      } else {
        console.log("⚠️ Web Serial API not supported - Using fallback methods");
      }

      console.log("T220MD Print Service initialized:", {
        serialSupported: this.isSerialSupported,
        config: this.config,
      });

      return true;
    } catch (error) {
      console.error("Error initializing T220MD print service:", error);
      return false;
    }
  }

  /**
   * Print receipt using the best available method for T220MD
   */
  static async printReceipt(
    receiptData: ReceiptData
  ): Promise<T220MDPrintResult> {
    if (Platform.OS !== "web") {
      return {
        success: false,
        error: "T220MD service only works on web platform",
      };
    }

    try {
      // Try different printing methods in order of preference

      // 1. Try Web Serial API (USB - no server required)
      if (this.isSerialSupported) {
        const serialResult = await this.printViaWebSerial(receiptData);
        if (serialResult.success) return serialResult;
      }

      // 2. Try network printing (if IP configured)
      if (this.config.printerIP) {
        const networkResult = await this.printViaNetwork(receiptData);
        if (networkResult.success) return networkResult;
      }

      // 3. Try cloud printing (if configured)
      if (this.config.cloudApiKey && this.config.cloudPrinterId) {
        const cloudResult = await this.printViaCloud(receiptData);
        if (cloudResult.success) return cloudResult;
      }

      // 4. Fall back to browser printing with thermal formatting
      return await this.printViaBrowser(receiptData);
    } catch (error) {
      console.error("T220MD print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "T220MD print failed",
      };
    }
  }

  /**
   * Print via Web Serial API (USB - no server required)
   */
  private static async printViaWebSerial(
    receiptData: ReceiptData
  ): Promise<T220MDPrintResult> {
    try {
      if (!this.isSerialSupported) {
        throw new Error("Web Serial API not supported");
      }

      // Request serial port access if not already connected
      if (!this.serialPort) {
        // @ts-ignore - Web Serial API types
        this.serialPort = await navigator.serial.requestPort({
          filters: [
            // Common USB-to-Serial chip vendors for thermal printers
            { usbVendorId: 0x067b }, // Prolific
            { usbVendorId: 0x0403 }, // FTDI
            { usbVendorId: 0x1a86 }, // CH340
            { usbVendorId: 0x10c4 }, // Silicon Labs
          ],
        });
      }

      // Open the serial port with T220MD settings
      if (!this.serialPort.readable) {
        await this.serialPort.open({
          baudRate: this.config.serialBaudRate || 9600,
          dataBits: this.config.serialDataBits || 8,
          stopBits: this.config.serialStopBits || 1,
          parity: this.config.serialParity || "none",
        });
      }

      // Generate ESC/POS commands for T220MD
      const escPosCommands = this.generateT220MDCommands(receiptData);

      // Send commands to printer
      const writer = this.serialPort.writable.getWriter();
      await writer.write(new Uint8Array(escPosCommands));
      writer.releaseLock();

      console.log("✅ T220MD print via Web Serial API successful");

      return {
        success: true,
        method: "web-serial",
      };
    } catch (error) {
      console.error("Web Serial API print error:", error);

      // Reset serial port on error
      if (this.serialPort) {
        try {
          await this.serialPort.close();
        } catch (closeError) {
          console.error("Error closing serial port:", closeError);
        }
        this.serialPort = null;
      }

      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Web Serial print failed",
      };
    }
  }

  /**
   * Print via network (TCP/IP) to T220MD
   */
  private static async printViaNetwork(
    receiptData: ReceiptData
  ): Promise<T220MDPrintResult> {
    try {
      if (!this.config.printerIP) {
        throw new Error("Printer IP not configured");
      }

      const escPosCommands = this.generateT220MDCommands(receiptData);

      // Use fetch to send raw ESC/POS commands to network printer
      const response = await fetch(
        `http://${this.config.printerIP}:${this.config.printerPort}/print`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/octet-stream",
          },
          body: new Uint8Array(escPosCommands),
        }
      );

      if (!response.ok) {
        throw new Error(`Network print failed: ${response.statusText}`);
      }

      return {
        success: true,
        method: "network",
      };
    } catch (error) {
      console.error("Network print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Network print failed",
      };
    }
  }

  /**
   * Print via cloud service (PrintNode, ezeep, etc.)
   */
  private static async printViaCloud(
    receiptData: ReceiptData
  ): Promise<T220MDPrintResult> {
    try {
      if (!this.config.cloudApiKey || !this.config.cloudPrinterId) {
        throw new Error("Cloud print service not configured");
      }

      const printData = {
        printerId: this.config.cloudPrinterId,
        title: `Receipt ${receiptData.orderNumber}`,
        contentType: "raw_base64",
        content: btoa(
          String.fromCharCode(...this.generateT220MDCommands(receiptData))
        ),
        source: "Dukalink POS",
      };

      let apiUrl = "";
      let headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      // Configure for different cloud print services
      switch (this.config.cloudPrintService) {
        case "printnode":
          apiUrl = "https://api.printnode.com/printjobs";
          headers["Authorization"] = `Basic ${btoa(
            this.config.cloudApiKey + ":"
          )}`;
          break;
        case "ezeep":
          apiUrl = "https://api.ezeep.com/sfapi/PrintJob/";
          headers["Authorization"] = `Bearer ${this.config.cloudApiKey}`;
          break;
        default:
          throw new Error(
            `Unsupported cloud print service: ${this.config.cloudPrintService}`
          );
      }

      const response = await fetch(apiUrl, {
        method: "POST",
        headers,
        body: JSON.stringify(printData),
      });

      if (!response.ok) {
        throw new Error(`Cloud print API error: ${response.statusText}`);
      }

      const result = await response.json();
      console.log("✅ Cloud print successful:", result);

      return {
        success: true,
        method: "cloud",
      };
    } catch (error) {
      console.error("Cloud print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Cloud print failed",
      };
    }
  }

  /**
   * Print via browser with T220MD thermal formatting
   */
  private static async printViaBrowser(
    receiptData: ReceiptData
  ): Promise<T220MDPrintResult> {
    try {
      console.log(
        "T220MDPrintService: Using browser fallback with updated alignment - v2.0"
      );
      const thermalHTML = this.generateT220MDThermalHTML(receiptData);
      return await this.printHTML(thermalHTML);
    } catch (error) {
      console.error("Browser print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Browser print failed",
      };
    }
  }

  /**
   * Generate ESC/POS commands optimized for T220MD (80mm paper)
   */
  private static generateT220MDCommands(receiptData: ReceiptData): number[] {
    const commands: number[] = [];

    // ESC/POS command helper functions
    const addCommand = (cmd: number[]) => commands.push(...cmd);
    const addText = (text: string) => {
      const encoder = new TextEncoder();
      commands.push(...Array.from(encoder.encode(text)));
    };

    // Initialize printer
    addCommand([0x1b, 0x40]); // ESC @ - Initialize printer

    // Set character set to UTF-8
    addCommand([0x1b, 0x74, 0x06]); // ESC t 6 - Select character code table

    // Center alignment
    addCommand([0x1b, 0x61, 0x01]); // ESC a 1 - Center alignment

    // Store header (bold, large)
    addCommand([0x1b, 0x45, 0x01]); // ESC E 1 - Bold on
    addCommand([0x1d, 0x21, 0x11]); // GS ! 17 - Double width and height
    addText(`${receiptData.store?.name || "Store Name"}\n`);
    addCommand([0x1d, 0x21, 0x00]); // GS ! 0 - Normal size
    addCommand([0x1b, 0x45, 0x00]); // ESC E 0 - Bold off

    // Store details
    if (receiptData.store?.address) {
      addText(`${receiptData.store.address}\n`);
    }
    if (receiptData.store?.phone) {
      // addText(`Mobile: ${receiptData.store.phone}\n`);
      addText(`Mobile: +254 111 443 993\n`);
    }
    addText(`Email: <EMAIL>\n`);
    addText(`Website: www.treasuredscents.co.ke\n`);

    // Separator line (48 characters for 80mm)
    addText("=".repeat(48) + "\n");

    // Left alignment for receipt details
    addCommand([0x1b, 0x61, 0x00]); // ESC a 0 - Left alignment

    // Receipt header
    addCommand([0x1b, 0x45, 0x01]); // Bold on
    addText("SALES RECEIPT\n");
    addCommand([0x1b, 0x45, 0x00]); // Bold off

    // Receipt details
    addText(`Receipt No: ${receiptData.orderNumber}\n`);
    addText(
      `Date: ${new Date(receiptData.orderDate).toLocaleDateString()} ${new Date(
        receiptData.orderDate
      ).toLocaleTimeString()}\n`
    );
    addText(`Served by: ${receiptData.staff?.name || "POS Staff"}\n`);

    if (receiptData.customer?.name) {
      addText(`Customer: ${receiptData.customer.name}\n`);
    }
    if (receiptData.customer?.phone) {
      addText(`Mobile: ${receiptData.customer.phone}\n`);
    }
    if (receiptData.salesAgent?.name) {
      addText(`Sales Agent: ${receiptData.salesAgent.name}\n`);
    }

    addText("-".repeat(48) + "\n");

    // Items
    (receiptData.items || []).forEach((item) => {
      addText(`${item.title || "Item"}\n`);
      if (item.variantTitle) {
        addText(`  Variant: ${item.variantTitle}\n`);
      }
      if (item.sku) {
        addText(`  SKU: ${item.sku}\n`);
      }

      // Create aligned price line for T220MD (48 characters width)
      const qtyPrice = `${item.quantity || 1} x KSh ${parseFloat(
        (item.price || "0").toString()
      ).toFixed(2)}`;
      const total = `KSh ${(
        parseFloat((item.price || "0").toString()) * (item.quantity || 1)
      ).toFixed(2)}`;

      // Use the new alignment function for better formatting
      const alignedLine = T220MDPrintService.createAlignedLine(
        qtyPrice,
        total,
        48
      );
      addText(alignedLine + "\n");

      // Add spacing between items
      addText("\n");
    });

    addText("=".repeat(48) + "\n");

    // Subtotal
    const subtotalLine = T220MDPrintService.createAlignedLine(
      "Subtotal:",
      `KSh ${(receiptData.total || 0).toFixed(2)}`,
      48
    );
    addText(subtotalLine + "\n");

    addText("-".repeat(48) + "\n");

    // Total (bold, larger, aligned)
    addCommand([0x1b, 0x45, 0x01]); // Bold on
    addCommand([0x1d, 0x21, 0x01]); // GS ! 1 - Double height
    const totalLabel = "TOTAL:";
    const totalAmount = `KSh ${(receiptData.total || 0).toFixed(2)}`;

    // Use the new alignment function for better total formatting
    const totalLine = T220MDPrintService.createAlignedLine(
      totalLabel,
      totalAmount,
      48
    );
    addText(totalLine + "\n");
    addCommand([0x1d, 0x21, 0x00]); // Normal size
    addCommand([0x1b, 0x45, 0x00]); // Bold off

    // Enhanced payment details
    T220MDPrintService.addPaymentDetails(receiptData, addText, addCommand);

    addText("-".repeat(48) + "\n");

    // Center alignment for footer
    addCommand([0x1b, 0x61, 0x01]); // Center alignment
    addText("Thank you for your business!\n");
    addText("Powered by Dukalink POS\n");

    // Feed paper to ensure receipt comes out completely
    addText("\n\n\n\n\n"); // Extra line feeds for proper paper feed

    // ESC/POS paper feed command (feed 5 lines)
    addCommand([0x1b, 0x64, 0x05]); // ESC d 5 - Feed 5 lines

    if (this.config.cutPaper) {
      addCommand([0x1d, 0x56, 0x42, 0x00]); // GS V B 0 - Full cut
    }

    // Open cash drawer (if enabled)
    if (this.config.openDrawer) {
      addCommand([0x1b, 0x70, 0x00, 0x19, 0xfa]); // ESC p 0 25 250 - Open drawer
    }

    return commands;
  }

  /**
   * Generate HTML optimized for T220MD thermal printing (80mm width)
   */
  private static generateT220MDThermalHTML(receiptData: ReceiptData): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>T220MD Receipt ${receiptData.orderNumber} - ${Date.now()}</title>
    <style>
        /* T220MD Thermal Printer Optimized CSS - Updated v2.0 with proper alignment */
        @page {
            size: 80mm auto;
            margin: 0mm;
            padding: 0mm;
        }

        * {
            box-sizing: border-box;
        }

        html {
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Courier New', 'Lucida Console', monospace !important;
            font-size: 12px !important;
            line-height: 1.2 !important;
            margin: 0 !important;
            padding: 3mm 2mm !important;
            width: 80mm !important;
            max-width: 80mm !important;
            color: #000 !important;
            background: #fff !important;
            text-align: center !important;
            overflow: hidden;
        }

        .center {
            text-align: center !important;
            width: 100% !important;
            display: block !important;
        }

        .left {
            text-align: left !important;
            width: 100% !important;
            display: block !important;
        }

        .bold {
            font-weight: bold !important;
        }

        .large {
            font-size: 14px !important;
            font-weight: bold !important;
        }

        .separator {
            border-top: 2px solid #000 !important;
            margin: 3mm 0 !important;
            width: 100% !important;
            height: 0 !important;
        }

        .dashed {
            border-top: 1px dashed #000 !important;
            margin: 2mm 0 !important;
            width: 100% !important;
            height: 0 !important;
        }

        .item {
            margin: 1mm 0 !important;
            width: 100% !important;
        }

        .item-line {
            width: 100% !important;
            border-collapse: collapse !important;
            font-family: 'Courier New', monospace !important;
            font-size: 11px !important;
            margin-top: 2px !important;
        }

        .item-line td {
            padding: 0 !important;
            vertical-align: top !important;
        }

        .item-line .left-col {
            text-align: left !important;
        }

        .item-line .right-col {
            text-align: right !important;
        }

        .item-name {
            text-align: left !important;
            font-weight: bold !important;
            margin-bottom: 2px !important;
        }

        .item-details {
            text-align: left !important;
            font-size: 10px !important;
            margin-left: 4px !important;
            margin-bottom: 1px !important;
        }

        .total-section {
            border-top: 2px solid #000 !important;
            margin-top: 3mm !important;
            padding-top: 2mm !important;
            width: 100% !important;
        }

        .total-line {
            font-weight: bold !important;
            font-size: 13px !important;
        }

        .paper-feed {
            height: 25mm !important;
            width: 100% !important;
            page-break-after: always !important;
        }

        /* Print-specific optimizations */
        @media print {
            html, body {
                margin: 0 !important;
                padding: 0 !important;
                width: 80mm !important;
                max-width: 80mm !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                padding: 3mm 2mm !important;
                text-align: center !important;
            }

            .center {
                text-align: center !important;
                margin: 0 auto !important;
            }

            .left {
                text-align: left !important;
            }

            .separator, .dashed {
                border-color: #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .paper-feed {
                height: 25mm !important;
                page-break-after: always !important;
            }

            /* Force thermal printer layout */
            @page {
                size: 80mm auto !important;
                margin: 0mm !important;
            }
        }

        /* Thermal printer specific adjustments */
        @media print and (max-width: 80mm) {
            body {
                font-size: 11px !important;
                padding: 2mm 1mm !important;
            }
        }
    </style>
</head>
<body>
    <div class="center bold large">${
      receiptData.store?.name || "TREASURED SCENTS"
    }</div>
    ${
      receiptData.store?.address
        ? `<div class="center">${receiptData.store.address}</div>`
        : ""
    }
    ${
      receiptData.store?.phone
        ? `<div class="center">Mobile: +254 111 443 993</div>`
        : ""
      // receiptData.store?.phone
      //   ? `<div class="center">Mobile: ${receiptData.store.phone}</div>`
      //   : ""
    }
    <div class="center">Email: <EMAIL></div>
    <div class="center">Website: www.treasuredscents.co.ke</div>
    
    <div class="separator"></div>
    
    <div class="center bold">SALES RECEIPT</div>
    <table class="item-line">
        <tr>
            <td class="left-col">Receipt No:</td>
            <td class="right-col">${receiptData.orderNumber || "N/A"}</td>
        </tr>
        <tr>
            <td class="left-col">Date:</td>
            <td class="right-col">${new Date(
              receiptData.orderDate || new Date()
            ).toLocaleDateString()} ${new Date(
      receiptData.orderDate || new Date()
    ).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}</td>
        </tr>
        <tr>
            <td class="left-col">Served by:</td>
            <td class="right-col">${receiptData.staff?.name || "POS Staff"}</td>
        </tr>
        ${
          receiptData.customer?.name
            ? `
        <tr>
            <td class="left-col">Customer:</td>
            <td class="right-col">${receiptData.customer.name}</td>
        </tr>`
            : ""
        }
        ${
          receiptData.customer?.phone
            ? `
        <tr>
            <td class="left-col">Mobile:</td>
            <td class="right-col">${receiptData.customer.phone}</td>
        </tr>`
            : ""
        }
        ${
          receiptData.salesAgent?.name
            ? `
        <tr>
            <td class="left-col">Sales Agent:</td>
            <td class="right-col">${receiptData.salesAgent.name}</td>
        </tr>`
            : ""
        }
    </table>
    
    <div class="dashed"></div>
    
    ${(receiptData.items || [])
      .map(
        (item) => `
    <div class="item">
        <div class="item-name">${item.title || "Item"}</div>
        ${
          item.variantTitle
            ? `<div class="item-details">Variant: ${item.variantTitle}</div>`
            : ""
        }
        ${item.sku ? `<div class="item-details">SKU: ${item.sku}</div>` : ""}
        <table class="item-line">
            <tr>
                <td class="left-col">${item.quantity || 1} x KSh ${parseFloat(
          (item.price || "0").toString()
        ).toFixed(2)}</td>
                <td class="right-col">KSh ${(
                  parseFloat((item.price || "0").toString()) *
                  (item.quantity || 1)
                ).toFixed(2)}</td>
            </tr>
        </table>
    </div>
    `
      )
      .join("")}
    
    <div class="subtotal-section">
        <table class="item-line">
            <tr>
                <td class="left-col">Subtotal</td>
                <td class="right-col">KSh ${(receiptData.total || 0).toFixed(
                  2
                )}</td>
            </tr>
        </table>
        <table class="item-line">
            <tr>
                <td class="left-col">Shipping Fee</td>
                <td class="right-col">KSh 200.00</td>
            </tr>
        </table>
    </div>

    <div class="total-section">
        <table class="item-line total-line">
            <tr>
                <td class="left-col">TOTAL</td>
                <td class="right-col">KSh ${(
                  (receiptData.total || 0) + 200
                ).toFixed(2)}</td>
            </tr>
        </table>
    </div>

    <table class="item-line">
        <tr>
            <td class="left-col">Payment:</td>
            <td class="right-col">${receiptData.paymentMethod || "Cash"}</td>
        </tr>
    </table>

    <div class="dashed"></div>

    ${
      receiptData.loyaltyPoints
        ? `
    <div class="center bold">🌟 LOYALTY REWARDS 🌟</div>
    <table class="item-line">
        <tr>
            <td class="left-col">Total TS Points:</td>
            <td class="right-col">${receiptData.loyaltyPoints.balance}</td>
        </tr>
        <tr>
            <td class="left-col">Member ID:</td>
            <td class="right-col">${receiptData.loyaltyPoints.membershipId}</td>
        </tr>
    </table>

    <div class="dashed"></div>
    `
        : ""
    }

    <div class="center">You're treasured! Thank you</div>
    <div class="center">Powered by Dukalink POS</div>

    <div class="paper-feed"></div>
</body>
</html>`;
  }

  /**
   * Print HTML using browser print dialog
   */
  private static async printHTML(html: string): Promise<T220MDPrintResult> {
    return new Promise((resolve) => {
      try {
        const printFrame = document.createElement("iframe");
        printFrame.style.position = "absolute";
        printFrame.style.top = "-9999px";
        printFrame.style.left = "-9999px";
        printFrame.style.width = "0px";
        printFrame.style.height = "0px";
        printFrame.style.border = "none";

        document.body.appendChild(printFrame);

        const printDocument =
          printFrame.contentDocument || printFrame.contentWindow?.document;
        if (!printDocument) {
          throw new Error("Could not access print document");
        }

        printDocument.open();
        printDocument.write(html);
        printDocument.close();

        printFrame.onload = () => {
          try {
            printFrame.contentWindow?.focus();
            printFrame.contentWindow?.print();

            setTimeout(() => {
              document.body.removeChild(printFrame);
            }, 1000);

            resolve({
              success: true,
              method: "browser",
            });
          } catch (error) {
            document.body.removeChild(printFrame);
            resolve({
              success: false,
              error:
                error instanceof Error ? error.message : "Print dialog failed",
            });
          }
        };

        setTimeout(() => {
          if (document.body.contains(printFrame)) {
            document.body.removeChild(printFrame);
            resolve({
              success: false,
              error: "Print timeout",
            });
          }
        }, 5000);
      } catch (error) {
        resolve({
          success: false,
          error: error instanceof Error ? error.message : "Print setup failed",
        });
      }
    });
  }

  /**
   * Configure T220MD printer settings
   */
  static configure(config: Partial<T220MDConfig>): void {
    this.config = { ...this.config, ...config };
    console.log("T220MD configuration updated:", this.config);
  }

  /**
   * Get current T220MD configuration
   */
  static getConfig(): T220MDConfig {
    return { ...this.config };
  }

  /**
   * Test T220MD printer connection
   */
  static async testPrint(): Promise<T220MDPrintResult> {
    const testReceiptData: ReceiptData = {
      store: {
        name: "TREASURED SCENTS",
        address: "Greenhouse Mall, Ngong Road, Kenya",
        phone: "+***********-933",
      },
      orderNumber: "T220MD-TEST-001",
      orderDate: new Date().toISOString(),
      items: [
        {
          id: "test-1",
          title: "Test Product for T220MD",
          variantTitle: "Test Variant",
          quantity: 1,
          price: "25.00",
          sku: "T220MD-TEST",
        },
      ],
      subtotal: 25.0,
      tax: 4.0,
      total: 29.0,
      paymentMethod: "Cash",
      paymentDetails: {
        transactionId: "T220MD-TXN-001",
      },
      loyaltyPoints: {
        earned: 3, // Test data: Points earned this transaction
        balance: 1503, // Test data: Total TS Points balance
        membershipId: "TS12345T220", // Test data: Customer membership ID
      },
      staff: {
        name: "Test Cashier",
        role: "Cashier",
      },
      customer: {
        name: "Test Customer",
        email: "<EMAIL>",
        phone: "+*********** 001",
      },
      salesAgent: {
        name: "Test Sales Agent",
        id: "TEST-SA-001",
      },
    };

    return await this.printReceipt(testReceiptData);
  }
}
