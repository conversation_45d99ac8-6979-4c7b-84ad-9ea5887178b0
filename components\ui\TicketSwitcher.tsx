/**
 * Modern TicketSwitcher Component
 *
 * Redesigned ticket switching interface with clean UI, better visual indicators,
 * and consistent design system implementation.
 */

import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useAppDispatch, useAppSelector } from "@/src/store";
import {
  deleteTicket,
  selectActiveTicket,
  selectActiveTicketId,
  selectAllTickets,
  setActiveTicket,
} from "@/src/store/slices/ticketSlice";
import { formatCurrency } from "@/src/utils/currencyUtils";
import {
  createTicketStyles,
  getTicketColors,
  getTicketStatusIndicator,
  TicketVariant,
} from "@/src/design-system/TicketDesignSystem";
import React, { useState } from "react";
import {
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { IconSymbol } from "./IconSymbol";
import { TicketOptionsModal } from "./TicketOptionsModal";

interface TicketSwitcherProps {
  onCheckout?: () => void;
  style?: ViewStyle;
  compactMode?: boolean;
}

export function TicketSwitcher({
  onCheckout,
  style,
  compactMode = false,
}: TicketSwitcherProps) {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { user } = useSession();

  const tickets = useAppSelector(selectAllTickets);
  const activeTicketId = useAppSelector(selectActiveTicketId);
  const activeTicket = useAppSelector(selectActiveTicket);

  const [showOptionsModal, setShowOptionsModal] = useState(false);
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);

  const activeTickets = tickets.filter((t) => t.status === "active");
  const hasActiveTicket = activeTicket && activeTicket.items.length > 0;

  const handleTicketSwitch = (ticketId: string) => {
    if (ticketId !== activeTicketId) {
      dispatch(setActiveTicket(ticketId));
    }
  };

  const handleTicketOptions = (ticketId: string) => {
    setSelectedTicketId(ticketId);
    setShowOptionsModal(true);
  };

  const handleDeleteTicket = (ticketId: string) => {
    dispatch(deleteTicket(ticketId));
    setShowOptionsModal(false);
    setSelectedTicketId(null);
  };

  const getTicketDisplayName = (ticket: any) => {
    return ticket.name || `Ticket ${ticket.id.slice(-4)}`;
  };

  const getTicketItemCount = (ticket: any) => {
    return (
      ticket.items?.reduce(
        (total: number, item: any) => total + item.quantity,
        0
      ) || 0
    );
  };

  const getTicketVariant = (ticket: any): TicketVariant => {
    if (ticket.id === activeTicketId) return "active";
    return ticket.status as TicketVariant;
  };

  const styles = createTicketStyles(theme);

  if (compactMode) {
    // Compact mode for products page - redesigned with new design system
    const variant = activeTicket ? getTicketVariant(activeTicket) : "default";
    const colors = getTicketColors(theme, variant);

    return (
      <View style={[{ marginBottom: theme.spacing.md }, style]}>
        <TouchableOpacity
          style={[
            styles.card,
            {
              backgroundColor: theme.colors.primary, // Use primary theme color (pink/coral)
              borderColor: theme.colors.primary,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              paddingHorizontal: theme.spacing.lg,
              paddingVertical: theme.spacing.lg,
            },
          ]}
          onPress={onCheckout}
          activeOpacity={0.8}
        >
          <View style={{ flex: 1, alignItems: "center" }}>
            {/* Center-aligned content */}
            <Text
              style={[
                styles.text.title,
                {
                  color: "white",
                  textAlign: "center",
                  fontSize: theme.typography.bodyMedium.fontSize,
                  fontWeight: "600",
                  marginBottom: theme.spacing.xs,
                },
              ]}
            >
              TICKET TOTAL
            </Text>

            <Text
              style={[
                styles.text.total,
                {
                  color: "white",
                  textAlign: "center",
                  fontSize: theme.typography.h1.fontSize,
                  fontWeight: "bold",
                  marginBottom: theme.spacing.sm,
                },
              ]}
            >
              {formatCurrency(activeTicket?.total || 0)}
            </Text>

            {/* Item count with accented background */}
            <View
              style={{
                backgroundColor: "rgba(255, 255, 255, 0.2)",
                paddingHorizontal: theme.spacing.md,
                paddingVertical: theme.spacing.xs,
                borderRadius: theme.borderRadius.sm,
              }}
            >
              <Text
                style={[
                  styles.text.subtitle,
                  {
                    color: "white",
                    textAlign: "center",
                    fontSize: theme.typography.body.fontSize,
                    fontWeight: "500",
                  },
                ]}
              >
                {activeTicket && activeTicket.items.length > 0
                  ? `${getTicketItemCount(activeTicket)} item${
                      getTicketItemCount(activeTicket) === 1 ? "" : "s"
                    } in ticket`
                  : "Tap to start"}
              </Text>
            </View>

            {/* Multiple tickets indicator */}
            {activeTickets.length > 1 && (
              <View
                style={{
                  backgroundColor: theme.colors.warning,
                  paddingHorizontal: theme.spacing.xs,
                  paddingVertical: 2,
                  borderRadius: theme.borderRadius.round,
                  marginTop: theme.spacing.xs,
                  minWidth: 20,
                  alignItems: "center",
                }}
              >
                <Text
                  style={[
                    theme.typography.small,
                    { color: theme.colors.background, fontWeight: "600" },
                  ]}
                >
                  {activeTickets.length} tickets
                </Text>
              </View>
            )}
          </View>

          {/* Simple CTA Arrow - Clean white arrow matching the design */}
          <IconSymbol name="arrow.right" size={24} color="white" />
        </TouchableOpacity>

        {/* Quick ticket switcher - redesigned */}
        {activeTickets.length > 1 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ maxHeight: 40, marginTop: theme.spacing.sm }}
            contentContainerStyle={{
              paddingHorizontal: theme.spacing.sm,
              gap: theme.spacing.sm,
            }}
          >
            {activeTickets.map((ticket) => {
              const isActive = ticket.id === activeTicketId;
              const ticketColors = getTicketColors(
                theme,
                isActive ? "active" : "default"
              );

              return (
                <TouchableOpacity
                  key={ticket.id}
                  style={[
                    styles.buttons.secondary,
                    {
                      backgroundColor: isActive
                        ? ticketColors.background
                        : theme.colors.surface,
                      borderColor: isActive
                        ? ticketColors.border
                        : theme.colors.border,
                      flexDirection: "row",
                      alignItems: "center",
                      gap: theme.spacing.xs,
                      paddingHorizontal: theme.spacing.md,
                      paddingVertical: theme.spacing.sm,
                    },
                  ]}
                  onPress={() => handleTicketSwitch(ticket.id)}
                >
                  <Text
                    style={[
                      theme.typography.small,
                      {
                        color: isActive ? ticketColors.text : theme.colors.text,
                        fontWeight: "500",
                      },
                    ]}
                  >
                    {getTicketDisplayName(ticket)}
                  </Text>
                  {ticket.items.length > 0 && (
                    <View
                      style={{
                        backgroundColor: isActive
                          ? ticketColors.accent + "20"
                          : theme.colors.primary + "20",
                        paddingHorizontal: 4,
                        paddingVertical: 1,
                        borderRadius: theme.borderRadius.sm,
                        minWidth: 16,
                        alignItems: "center",
                      }}
                    >
                      <Text
                        style={[
                          theme.typography.small,
                          {
                            color: isActive
                              ? ticketColors.accent
                              : theme.colors.primary,
                            fontWeight: "600",
                            fontSize: 10,
                          },
                        ]}
                      >
                        {getTicketItemCount(ticket)}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              );
            })}

            {/* Manual Ticket Creation - Temporarily Hidden */}
            {/* TODO: Implement advanced ticket creation features later */}
          </ScrollView>
        )}

        {/* Modals */}
        <TicketOptionsModal
          visible={showOptionsModal}
          onClose={() => setShowOptionsModal(false)}
          ticket={
            selectedTicketId
              ? tickets.find((t) => t.id === selectedTicketId) || null
              : null
          }
          onDeleteTicket={handleDeleteTicket}
        />
      </View>
    );
  }

  // Full mode for cart/dedicated ticket management - redesigned
  return (
    <View style={[styles.list.container, style]}>
      {/* Header */}
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          paddingHorizontal: theme.spacing.lg,
          paddingVertical: theme.spacing.md,
          borderBottomWidth: 1,
          borderBottomColor: theme.colors.border,
        }}
      >
        <Text style={[theme.typography.h2, { color: theme.colors.text }]}>
          Tickets ({activeTickets.length})
        </Text>
        {/* Manual Ticket Creation - Temporarily Hidden */}
        {/* TODO: Implement advanced ticket creation features later */}
      </View>

      <ScrollView
        style={{ flex: 1, padding: theme.spacing.lg }}
        showsVerticalScrollIndicator={false}
      >
        {activeTickets.map((ticket) => {
          const variant = getTicketVariant(ticket);
          const statusIndicator = getTicketStatusIndicator(theme, variant);
          const itemCount = getTicketItemCount(ticket);

          return (
            <TouchableOpacity
              key={ticket.id}
              style={styles.card}
              onPress={() => handleTicketSwitch(ticket.id)}
              onLongPress={() => handleTicketOptions(ticket.id)}
              activeOpacity={0.7}
            >
              <View style={styles.list.section}>
                {/* Header with title and status */}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                  }}
                >
                  <View style={{ flex: 1 }}>
                    <Text style={styles.text.title}>
                      {getTicketDisplayName(ticket)}
                    </Text>
                    <Text style={styles.text.subtitle}>
                      {itemCount} item{itemCount === 1 ? "" : "s"}
                    </Text>
                  </View>

                  {/* Status Indicator */}
                  <View style={statusIndicator.container}>
                    <View style={statusIndicator.dot} />
                    <Text style={statusIndicator.text}>
                      {variant.charAt(0).toUpperCase() + variant.slice(1)}
                    </Text>
                  </View>
                </View>

                {/* Total and timestamp */}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginTop: theme.spacing.md,
                  }}
                >
                  <Text style={styles.text.total}>
                    {formatCurrency(ticket.total || 0)}
                  </Text>
                  <Text style={styles.text.timestamp}>
                    {new Date(
                      ticket.updatedAt || ticket.createdAt
                    ).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </Text>
                </View>

                {/* Active indicator */}
                {ticket.id === activeTicketId && (
                  <View
                    style={{
                      position: "absolute",
                      left: 0,
                      top: 0,
                      bottom: 0,
                      width: 4,
                      backgroundColor: theme.colors.primary,
                      borderTopLeftRadius: theme.borderRadius.lg,
                      borderBottomLeftRadius: theme.borderRadius.lg,
                    }}
                  />
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* Modals */}
      <TicketOptionsModal
        visible={showOptionsModal}
        onClose={() => setShowOptionsModal(false)}
        ticket={
          selectedTicketId
            ? tickets.find((t) => t.id === selectedTicketId) || null
            : null
        }
        onDeleteTicket={handleDeleteTicket}
      />
    </View>
  );
}
