/**
 * User Slice
 *
 * Manages current user state in Redux for middleware and global access.
 * Bridges the gap between AuthContext and Redux middleware.
 */

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// Define User type locally to match AuthContext
interface User {
  id: string;
  username: string;
  name: string;
  email?: string;
  role: string;
  storeId: string;
  permissions: string[];
  commissionRate?: number;
  lastLogin?: string;
  has_pin?: boolean;
}

interface UserState {
  currentUser: User | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: UserState = {
  currentUser: null,
  isLoading: false,
  error: null,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setCurrentUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.error = null;
    },
    clearCurrentUser: (state) => {
      state.currentUser = null;
      state.error = null;
    },
    setUserLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setUserError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    clearUserError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setCurrentUser,
  clearCurrentUser,
  setUserLoading,
  setUserError,
  clearUserError,
} = userSlice.actions;

export default userSlice.reducer;

// Selectors
export const selectCurrentUser = (state: { user: UserState }) =>
  state.user.currentUser;
export const selectUserLoading = (state: { user: UserState }) =>
  state.user.isLoading;
export const selectUserError = (state: { user: UserState }) => state.user.error;
