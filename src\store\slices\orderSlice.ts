import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { getAPIClient } from '../../services/api/dukalink-client';
import { Order } from '../../types/shopify';

interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  isLoading: boolean;
  error: string | null;
  isCreatingOrder: boolean;
}

const initialState: OrderState = {
  orders: [],
  currentOrder: null,
  isLoading: false,
  error: null,
  isCreatingOrder: false,
};

// Async thunks
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async (
    { storeId, page = 1, limit = 20 }: {
      storeId: string;
      page?: number;
      limit?: number;
    },
    { rejectWithValue }
  ) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getOrders(storeId, {
        page,
        limit,
      });
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch orders');
      }
      
      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async (
    { storeId, orderData }: {
      storeId: string;
      orderData: Partial<Order>;
    },
    { rejectWithValue }
  ) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.createOrder(storeId, orderData);
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to create order');
      }
      
      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const getOrder = createAsyncThunk(
  'orders/getOrder',
  async (
    { storeId, orderId }: {
      storeId: string;
      orderId: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getOrder(storeId, orderId);
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch order');
      }
      
      return response.data!;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

const orderSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentOrder: (state, action: PayloadAction<Order | null>) => {
      state.currentOrder = action.payload;
    },
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
    },
    clearOrders: (state) => {
      state.orders = [];
    },
  },
  extraReducers: (builder) => {
    // Fetch Orders
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.orders = action.payload.data;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create Order
    builder
      .addCase(createOrder.pending, (state) => {
        state.isCreatingOrder = true;
        state.error = null;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.isCreatingOrder = false;
        state.orders.unshift(action.payload);
        state.currentOrder = action.payload;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.isCreatingOrder = false;
        state.error = action.payload as string;
      });

    // Get Order
    builder
      .addCase(getOrder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentOrder = action.payload;
      })
      .addCase(getOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { 
  clearError, 
  setCurrentOrder, 
  clearCurrentOrder, 
  clearOrders 
} = orderSlice.actions;

export default orderSlice.reducer;
