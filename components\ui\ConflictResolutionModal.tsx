/**
 * Conflict Resolution Modal
 *
 * Handles ticket synchronization conflicts with visual comparison
 * and resolution options for users.
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import { SyncConflict } from "@/src/services/TicketSyncService";
import { formatCurrency } from "@/src/utils/currencyUtils";
import React, { useState } from "react";
import {
  <PERSON>ert,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { IconSymbol } from "./IconSymbol";
import { ModernButton } from "./ModernButton";
import { ModernCard } from "./ModernCard";

interface ConflictResolutionModalProps {
  visible: boolean;
  conflicts: SyncConflict[];
  onResolve: (
    conflictId: string,
    resolution: "local" | "server" | "merge"
  ) => Promise<boolean>;
  onClose: () => void;
}

export const ConflictResolutionModal: React.FC<
  ConflictResolutionModalProps
> = ({ visible, conflicts, onResolve, onClose }) => {
  const theme = useTheme();
  const [currentConflictIndex, setCurrentConflictIndex] = useState(0);
  const [isResolving, setIsResolving] = useState(false);

  const styles = createStyles(theme);

  const currentConflict = conflicts[currentConflictIndex];

  const handleResolve = async (resolution: "local" | "server" | "merge") => {
    if (!currentConflict) return;

    setIsResolving(true);
    try {
      const success = await onResolve(currentConflict.ticketId, resolution);

      if (success) {
        // Move to next conflict or close if done
        if (currentConflictIndex < conflicts.length - 1) {
          setCurrentConflictIndex(currentConflictIndex + 1);
        } else {
          onClose();
        }
      } else {
        Alert.alert("Error", "Failed to resolve conflict. Please try again.");
      }
    } catch (error) {
      Alert.alert(
        "Error",
        "An unexpected error occurred while resolving the conflict."
      );
    } finally {
      setIsResolving(false);
    }
  };

  const handleSkip = () => {
    if (currentConflictIndex < conflicts.length - 1) {
      setCurrentConflictIndex(currentConflictIndex + 1);
    } else {
      onClose();
    }
  };

  const renderTicketComparison = () => {
    if (!currentConflict) return null;

    const { localTicket, serverTicket, conflictType } = currentConflict;

    return (
      <View style={styles.comparisonContainer}>
        <Text style={[styles.conflictTitle, { color: theme.colors.text }]}>
          Conflict Type: {conflictType.toUpperCase()}
        </Text>

        <View style={styles.ticketsRow}>
          {/* Local Version */}
          <ModernCard style={[styles.ticketCard, styles.localCard]}>
            <View style={styles.cardHeader}>
              <IconSymbol
                name="smartphone"
                size={20}
                color={theme.colors.primary}
              />
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                Local Version
              </Text>
            </View>

            <View style={styles.ticketDetails}>
              <Text style={[styles.ticketName, { color: theme.colors.text }]}>
                {localTicket.name}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Items: {localTicket.items.length}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Total: {formatCurrency(localTicket.total)}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Updated: {new Date(localTicket.updatedAt).toLocaleString()}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Status: {localTicket.status}
              </Text>
            </View>
          </ModernCard>

          {/* Server Version */}
          <ModernCard style={[styles.ticketCard, styles.serverCard]}>
            <View style={styles.cardHeader}>
              <IconSymbol name="cloud" size={20} color={theme.colors.success} />
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                Server Version
              </Text>
            </View>

            <View style={styles.ticketDetails}>
              <Text style={[styles.ticketName, { color: theme.colors.text }]}>
                {serverTicket.name}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Items: {serverTicket.items.length}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Total: {formatCurrency(serverTicket.total)}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Updated: {new Date(serverTicket.updatedAt).toLocaleString()}
              </Text>
              <Text
                style={[
                  styles.ticketMeta,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Status: {serverTicket.status}
              </Text>
            </View>
          </ModernCard>
        </View>

        {/* Detailed Differences */}
        {conflictType === "content" && (
          <View style={styles.differencesContainer}>
            <Text
              style={[styles.differencesTitle, { color: theme.colors.text }]}
            >
              Item Differences:
            </Text>

            <ScrollView
              style={styles.differencesList}
              showsVerticalScrollIndicator={false}
            >
              {renderItemDifferences(localTicket.items, serverTicket.items)}
            </ScrollView>
          </View>
        )}
      </View>
    );
  };

  const renderItemDifferences = (localItems: any[], serverItems: any[]) => {
    const differences: JSX.Element[] = [];

    // Find items that exist in local but not server
    localItems.forEach((localItem, index) => {
      const serverItem = serverItems.find(
        (si) => si.variantId === localItem.variantId
      );

      if (!serverItem) {
        differences.push(
          <View key={`local-${index}`} style={styles.differenceItem}>
            <Text
              style={[styles.differenceText, { color: theme.colors.primary }]}
            >
              + Local: {localItem.title} (Qty: {localItem.quantity})
            </Text>
          </View>
        );
      } else if (localItem.quantity !== serverItem.quantity) {
        differences.push(
          <View key={`diff-${index}`} style={styles.differenceItem}>
            <Text
              style={[styles.differenceText, { color: theme.colors.warning }]}
            >
              ≠ {localItem.title}: Local({localItem.quantity}) vs Server(
              {serverItem.quantity})
            </Text>
          </View>
        );
      }
    });

    // Find items that exist in server but not local
    serverItems.forEach((serverItem, index) => {
      const localItem = localItems.find(
        (li) => li.variantId === serverItem.variantId
      );

      if (!localItem) {
        differences.push(
          <View key={`server-${index}`} style={styles.differenceItem}>
            <Text
              style={[styles.differenceText, { color: theme.colors.success }]}
            >
              + Server: {serverItem.title} (Qty: {serverItem.quantity})
            </Text>
          </View>
        );
      }
    });

    return differences;
  };

  if (!visible || conflicts.length === 0) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        {/* Header */}
        <View
          style={[
            styles.header,
            { backgroundColor: theme.colors.backgroundSecondary },
          ]}
        >
          <View style={styles.headerContent}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Resolve Conflicts
            </Text>
            <Text
              style={[
                styles.headerSubtitle,
                { color: theme.colors.textSecondary },
              ]}
            >
              {currentConflictIndex + 1} of {conflicts.length}
            </Text>
          </View>

          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <IconSymbol name="xmark" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderTicketComparison()}
        </ScrollView>

        {/* Actions */}
        <View
          style={[
            styles.actions,
            { backgroundColor: theme.colors.backgroundSecondary },
          ]}
        >
          <Text style={[styles.actionsTitle, { color: theme.colors.text }]}>
            Choose Resolution:
          </Text>

          <View style={styles.actionButtons}>
            <ModernButton
              title="Keep Local"
              onPress={() => handleResolve("local")}
              loading={isResolving}
              disabled={isResolving}
              style={[
                styles.actionButton,
                { backgroundColor: theme.colors.primary },
              ]}
            />

            <ModernButton
              title="Use Server"
              onPress={() => handleResolve("server")}
              loading={isResolving}
              disabled={isResolving}
              style={[
                styles.actionButton,
                { backgroundColor: theme.colors.success },
              ]}
            />

            <ModernButton
              title="Smart Merge"
              onPress={() => handleResolve("merge")}
              loading={isResolving}
              disabled={isResolving}
              style={[
                styles.actionButton,
                { backgroundColor: theme.colors.warning },
              ]}
            />
          </View>

          <ModernButton
            title="Skip This Conflict"
            onPress={handleSkip}
            variant="outline"
            disabled={isResolving}
            style={styles.skipButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (theme: any) => {
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerContent: {
      flex: 1,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: "700",
      marginBottom: 4,
    },
    headerSubtitle: {
      fontSize: 14,
    },
    closeButton: {
      padding: theme.spacing.sm,
    },
    content: {
      flex: 1,
      padding: theme.spacing.lg,
    },
    comparisonContainer: {
      gap: theme.spacing.lg,
    },
    conflictTitle: {
      fontSize: 18,
      fontWeight: "600",
      textAlign: "center",
      marginBottom: theme.spacing.md,
    },
    ticketsRow: {
      flexDirection: "row",
      gap: theme.spacing.md,
    },
    ticketCard: {
      flex: 1,
      padding: theme.spacing.lg,
    },
    localCard: {
      borderColor: theme.colors.primary,
      borderWidth: 2,
    },
    serverCard: {
      borderColor: theme.colors.success,
      borderWidth: 2,
    },
    cardHeader: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.md,
    },
    cardTitle: {
      fontSize: 16,
      fontWeight: "600",
    },
    ticketDetails: {
      gap: theme.spacing.xs,
    },
    ticketName: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
    },
    ticketMeta: {
      fontSize: 14,
    },
    differencesContainer: {
      marginTop: theme.spacing.lg,
    },
    differencesTitle: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    differencesList: {
      maxHeight: 200,
    },
    differenceItem: {
      paddingVertical: theme.spacing.xs,
    },
    differenceText: {
      fontSize: 14,
      fontFamily: "monospace",
    },
    actions: {
      padding: theme.spacing.lg,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    actionsTitle: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
      textAlign: "center",
    },
    actionButtons: {
      flexDirection: "row",
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.md,
    },
    actionButton: {
      flex: 1,
    },
    skipButton: {
      marginTop: theme.spacing.sm,
    },
  });
};

export default ConflictResolutionModal;
