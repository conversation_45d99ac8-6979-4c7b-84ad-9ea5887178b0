/**
 * PIN Input Component
 *
 * Secure PIN input with visual feedback, auto-focus management,
 * and customizable styling for user switching authentication.
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  Vibration,
  View,
} from "react-native";

interface PinInputProps {
  length?: number;
  value: string;
  onChangeText: (pin: string) => void;
  onComplete?: (pin: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  disabled?: boolean;
  error?: boolean;
  errorMessage?: string;
  autoFocus?: boolean;
  showClearButton?: boolean;
  style?: any;
  testID?: string;
}

export const PinInput: React.FC<PinInputProps> = ({
  length = 4,
  value,
  onChangeText,
  onComplete,
  placeholder = "•",
  secureTextEntry = true,
  disabled = false,
  error = false,
  errorMessage,
  autoFocus = true,
  showClearButton = true,
  style,
  testID,
}) => {
  const theme = useTheme();
  const [focused, setFocused] = useState(false);
  const [showPin, setShowPin] = useState(!secureTextEntry);
  const inputRef = useRef<TextInput>(null);
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Auto-focus on mount
  useEffect(() => {
    if (autoFocus && !disabled) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [autoFocus, disabled]);

  // Handle completion
  useEffect(() => {
    if (value.length === length && onComplete) {
      onComplete(value);
    }
  }, [value, length, onComplete]);

  // Shake animation for errors
  useEffect(() => {
    if (error) {
      // Vibrate on error (if supported)
      if (Platform.OS === "ios" || Platform.OS === "android") {
        Vibration.vibrate(200);
      }

      // Shake animation
      Animated.sequence([
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: -10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [error, shakeAnimation]);

  const handleChangeText = (text: string) => {
    // Only allow digits and limit to specified length
    const numericText = text.replace(/[^0-9]/g, "").slice(0, length);
    onChangeText(numericText);
  };

  const handleClear = () => {
    onChangeText("");
    inputRef.current?.focus();
  };

  const toggleShowPin = () => {
    setShowPin(!showPin);
  };

  const renderPinDots = () => {
    const dots = [];
    for (let i = 0; i < length; i++) {
      const hasValue = i < value.length;
      const isActive = focused && i === value.length;

      dots.push(
        <View
          key={i}
          style={[
            styles.pinDot,
            {
              backgroundColor: hasValue
                ? theme.colors.primary
                : theme.colors.surface,
              borderColor: isActive
                ? theme.colors.primary
                : error
                ? theme.colors.error
                : theme.colors.border,
            },
            isActive && styles.activeDot,
            error && styles.errorDot,
          ]}
        >
          {hasValue && (
            <Text style={[styles.pinDigit, { color: theme.colors.onPrimary }]}>
              {showPin ? value[i] : placeholder}
            </Text>
          )}
        </View>
      );
    }
    return dots;
  };

  return (
    <Animated.View
      style={[
        styles.container,
        { transform: [{ translateX: shakeAnimation }] },
        style,
      ]}
      testID={testID}
    >
      {/* Hidden TextInput for actual input handling */}
      <TextInput
        ref={inputRef}
        style={styles.hiddenInput}
        value={value}
        onChangeText={handleChangeText}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        keyboardType="numeric"
        maxLength={length}
        secureTextEntry={false} // We handle display ourselves
        editable={!disabled}
        autoFocus={autoFocus}
        testID={`${testID}-input`}
      />

      {/* PIN Dots Display */}
      <View style={styles.pinContainer}>{renderPinDots()}</View>

      {/* Controls */}
      <View style={styles.controls}>
        {/* Toggle visibility button */}
        {secureTextEntry && (
          <TouchableOpacity
            style={[styles.controlButton, { borderColor: theme.colors.border }]}
            onPress={toggleShowPin}
            disabled={disabled}
            testID={`${testID}-toggle-visibility`}
          >
            <Ionicons
              name={showPin ? "eye-off" : "eye"}
              size={20}
              color={
                disabled ? theme.colors.disabled : theme.colors.textSecondary
              }
            />
          </TouchableOpacity>
        )}

        {/* Clear button */}
        {showClearButton && value.length > 0 && (
          <TouchableOpacity
            style={[styles.controlButton, { borderColor: theme.colors.border }]}
            onPress={handleClear}
            disabled={disabled}
            testID={`${testID}-clear`}
          >
            <Ionicons
              name="backspace"
              size={20}
              color={
                disabled ? theme.colors.disabled : theme.colors.textSecondary
              }
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Error message */}
      {error && errorMessage && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {errorMessage}
        </Text>
      )}

      {/* Helper text */}
      {!error && (
        <Text
          style={[styles.helperText, { color: theme.colors.textSecondary }]}
        >
          Enter {length}-digit PIN
        </Text>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    paddingVertical: 16,
  },
  hiddenInput: {
    position: "absolute",
    opacity: 0,
    height: 1,
    width: 1,
  },
  pinContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 12,
    marginBottom: 16,
  },
  pinDot: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    justifyContent: "center",
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  activeDot: {
    borderWidth: 3,
    elevation: 4,
    shadowOpacity: 0.2,
  },
  errorDot: {
    borderWidth: 3,
  },
  pinDigit: {
    fontSize: 20,
    fontWeight: "600",
  },
  controls: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 8,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 4,
    fontWeight: "500",
  },
  helperText: {
    fontSize: 12,
    textAlign: "center",
    marginTop: 4,
  },
});

export default PinInput;
