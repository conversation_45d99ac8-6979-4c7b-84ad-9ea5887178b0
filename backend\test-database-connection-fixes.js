#!/usr/bin/env node

/**
 * Comprehensive Database Connection Management Test
 *
 * This script tests all the services that were fixed to ensure:
 * 1. No "this.getConnection is not a function" errors
 * 2. All services use centralized DatabaseManager
 * 3. No connection pool exhaustion
 * 4. Proper singleton patterns
 */

require("dotenv").config();

async function testDatabaseConnectionFixes() {
  console.log("🧪 Testing Database Connection Management Fixes...\n");

  const results = {
    passed: 0,
    failed: 0,
    errors: [],
  };

  // Test 1: Enhanced Discount Service
  console.log("1️⃣ Testing Enhanced Discount Service...");
  try {
    const EnhancedDiscountService = require("./src/services/enhanced-discount-service");
    const service = EnhancedDiscountService.getInstance
      ? EnhancedDiscountService.getInstance()
      : EnhancedDiscountService;

    console.log("   - Service loaded:", !!service);
    console.log("   - DatabaseManager exists:", !!service.databaseManager);
    console.log(
      "   - executeQuery method exists:",
      typeof service.executeQuery === "function"
    );

    // Test a simple query
    const [rows] = await service.executeQuery("SELECT 1 as test");
    console.log("   - Database query works:", rows[0].test === 1);
    console.log("✅ Enhanced Discount Service: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Enhanced Discount Service: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Enhanced Discount Service: ${error.message}`);
  }

  // Test 2: Loyalty Service
  console.log("2️⃣ Testing Loyalty Service...");
  try {
    const LoyaltyService = require("./src/services/loyalty-service");
    const service = LoyaltyService.getInstance
      ? LoyaltyService.getInstance()
      : LoyaltyService;

    console.log("   - Service loaded:", !!service);
    console.log("   - DatabaseManager exists:", !!service.databaseManager);
    console.log(
      "   - executeQuery method exists:",
      typeof service.executeQuery === "function"
    );

    // Test a simple query
    const [rows] = await service.executeQuery("SELECT 1 as test");
    console.log("   - Database query works:", rows[0].test === 1);
    console.log("✅ Loyalty Service: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Loyalty Service: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Loyalty Service: ${error.message}`);
  }

  // Test 3: Auth Service
  console.log("3️⃣ Testing Auth Service...");
  try {
    const { authService } = require("./src/middleware/auth");

    console.log("   - Service loaded:", !!authService);
    console.log("   - DatabaseManager exists:", !!authService.databaseManager);
    console.log(
      "   - executeQuery method exists:",
      typeof authService.executeQuery === "function"
    );

    // Test a simple query
    const [rows] = await authService.executeQuery("SELECT 1 as test");
    console.log("   - Database query works:", rows[0].test === 1);
    console.log("✅ Auth Service: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Auth Service: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Auth Service: ${error.message}`);
  }

  // Test 4: Payment Transaction Service
  console.log("4️⃣ Testing Payment Transaction Service...");
  try {
    const PaymentTransactionService = require("./src/services/payment-transaction-service");
    const service = PaymentTransactionService.getInstance();

    console.log("   - Service loaded:", !!service);
    console.log("   - DatabaseManager exists:", !!service.databaseManager);
    console.log(
      "   - executeQuery method exists:",
      typeof service.executeQuery === "function"
    );
    console.log(
      "   - processPaymentMethod exists:",
      typeof service.processPaymentMethod === "function"
    );

    // Test a simple query
    const [rows] = await service.executeQuery("SELECT 1 as test");
    console.log("   - Database query works:", rows[0].test === 1);
    console.log("✅ Payment Transaction Service: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Payment Transaction Service: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Payment Transaction Service: ${error.message}`);
  }

  // Test 5: Fulfillment Service
  console.log("5️⃣ Testing Fulfillment Service...");
  try {
    const fulfillmentService = require("./src/services/fulfillment-service");

    console.log("   - Service loaded:", !!fulfillmentService);
    console.log(
      "   - DatabaseManager exists:",
      !!fulfillmentService.databaseManager
    );
    console.log(
      "   - calculateShippingFee exists:",
      typeof fulfillmentService.calculateShippingFee === "function"
    );

    console.log("✅ Fulfillment Service: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Fulfillment Service: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Fulfillment Service: ${error.message}`);
  }

  // Test 6: Sales Agent Service MySQL
  console.log("6️⃣ Testing Sales Agent Service MySQL...");
  try {
    const SalesAgentService = require("./src/services/sales-agent-service-mysql");

    console.log("   - Service loaded:", !!SalesAgentService);
    console.log(
      "   - DatabaseManager exists:",
      !!SalesAgentService.databaseManager
    );
    console.log(
      "   - getAllSalesAgents exists:",
      typeof SalesAgentService.getAllSalesAgents === "function"
    );

    console.log("✅ Sales Agent Service MySQL: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Sales Agent Service MySQL: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Sales Agent Service MySQL: ${error.message}`);
  }

  // Test 7: Commission Service MySQL
  console.log("7️⃣ Testing Commission Service MySQL...");
  try {
    const CommissionService = require("./src/services/commission-service-mysql");

    console.log("   - Service loaded:", !!CommissionService);
    console.log(
      "   - DatabaseManager exists:",
      !!CommissionService.databaseManager
    );
    console.log(
      "   - getConfiguration exists:",
      typeof CommissionService.getConfiguration === "function"
    );

    console.log("✅ Commission Service MySQL: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Commission Service MySQL: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Commission Service MySQL: ${error.message}`);
  }

  // Test 8: Terminal Management Services
  console.log("8️⃣ Testing Terminal Management Services...");
  try {
    const TerminalService = require("./src/services/terminal-management-service");
    const TerminalServiceMySQL = require("./src/services/terminal-management-service-mysql");

    console.log("   - Terminal Service loaded:", !!TerminalService);
    console.log("   - Terminal Service MySQL loaded:", !!TerminalServiceMySQL);
    console.log(
      "   - Both have DatabaseManager:",
      !!TerminalService.databaseManager &&
        !!TerminalServiceMySQL.databaseManager
    );

    console.log("✅ Terminal Management Services: PASSED\n");
    results.passed++;
  } catch (error) {
    console.error("❌ Terminal Management Services: FAILED -", error.message);
    results.failed++;
    results.errors.push(`Terminal Management Services: ${error.message}`);
  }

  // Final Results
  console.log("🎉 DATABASE CONNECTION FIXES TEST RESULTS:");
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(
    `📊 Success Rate: ${(
      (results.passed / (results.passed + results.failed)) *
      100
    ).toFixed(1)}%\n`
  );

  if (results.errors.length > 0) {
    console.log("❌ Errors encountered:");
    results.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  } else {
    console.log(
      "🎉 ALL TESTS PASSED! Database connection management fixes are working correctly."
    );
    console.log('✅ No "this.getConnection is not a function" errors');
    console.log("✅ All services use centralized DatabaseManager");
    console.log("✅ Connection pool exhaustion prevented");
    console.log("✅ Syntax errors fixed");
  }

  process.exit(results.failed > 0 ? 1 : 0);
}

// Run the test
testDatabaseConnectionFixes().catch((error) => {
  console.error("💥 Test suite failed:", error);
  process.exit(1);
});
