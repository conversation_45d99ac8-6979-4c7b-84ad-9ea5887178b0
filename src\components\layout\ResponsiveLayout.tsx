import { ThemedView } from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import { ResponsiveDimensions, ResponsiveUtils } from "@/src/utils/mobileUtils";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React, { ReactNode } from "react";
import { ScrollView, StyleSheet, View, ViewStyle } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

interface ResponsiveLayoutProps {
  children: ReactNode;
  variant?: "grid" | "list" | "stack" | "flex";
  columns?: number;
  spacing?: keyof typeof ResponsiveDimensions.spacing;
  padding?: keyof typeof ResponsiveDimensions.spacing;
  scrollable?: boolean;
  style?: ViewStyle;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  variant = "stack",
  columns,
  spacing = "md",
  padding = "md",
  scrollable = false,
  style,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const getLayoutStyle = (): ViewStyle => {
    const spacingValue = ResponsiveDimensions.spacing[spacing];
    const paddingValue = ResponsiveDimensions.spacing[padding];

    const baseStyle: ViewStyle = {
      padding: paddingValue,
    };

    switch (variant) {
      case "grid":
        return {
          ...baseStyle,
          flexDirection: "row",
          flexWrap: "wrap",
          justifyContent: "space-between",
          gap: spacingValue,
        };
      case "list":
        return {
          ...baseStyle,
          gap: spacingValue,
        };
      case "flex":
        return {
          ...baseStyle,
          flexDirection: "row",
          flexWrap: "wrap",
          gap: spacingValue,
        };
      case "stack":
      default:
        return {
          ...baseStyle,
          gap: spacingValue,
        };
    }
  };

  const getChildrenWithResponsiveProps = () => {
    if (variant === "grid") {
      const gridColumns = columns || ResponsiveDimensions.gridColumns;
      const itemWidth = ResponsiveUtils.getGridItemWidth(
        gridColumns,
        ResponsiveDimensions.spacing[spacing]
      );

      return React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            style: [
              child.props.style,
              {
                width: itemWidth,
                marginBottom: ResponsiveDimensions.spacing[spacing],
              },
            ],
            key: index,
          });
        }
        return child;
      });
    }

    return children;
  };

  const layoutStyle = [getLayoutStyle(), style];
  const responsiveChildren = getChildrenWithResponsiveProps();

  if (scrollable) {
    return (
      <ScrollView
        style={[
          styles.scrollContainer,
          { backgroundColor: theme.colors.background },
        ]}
        contentContainerStyle={layoutStyle}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {responsiveChildren}
      </ScrollView>
    );
  }

  return (
    <ThemedView variant="background" style={layoutStyle}>
      {responsiveChildren}
    </ThemedView>
  );
};

// Responsive Grid Component
interface ResponsiveGridProps {
  children: ReactNode;
  columns?: number;
  spacing?: keyof typeof ResponsiveDimensions.spacing;
  style?: ViewStyle;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns,
  spacing = "md",
  style,
}) => {
  return (
    <ResponsiveLayout
      variant="grid"
      columns={columns}
      spacing={spacing}
      style={style}
    >
      {children}
    </ResponsiveLayout>
  );
};

// Touch-Friendly Button Container
interface TouchFriendlyContainerProps {
  children: ReactNode;
  size?: "small" | "medium" | "large";
  style?: ViewStyle;
}

export const TouchFriendlyContainer: React.FC<TouchFriendlyContainerProps> = ({
  children,
  size = "medium",
  style,
}) => {
  const theme = useTheme();

  const getSizeStyle = (): ViewStyle => {
    switch (size) {
      case "small":
        return {
          minHeight: ResponsiveDimensions.touchTargetSmall,
          minWidth: ResponsiveDimensions.touchTargetSmall,
          padding: ResponsiveDimensions.spacing.sm,
        };
      case "large":
        return {
          minHeight: ResponsiveDimensions.touchTargetLarge,
          minWidth: ResponsiveDimensions.touchTargetLarge,
          padding: ResponsiveDimensions.spacing.lg,
        };
      case "medium":
      default:
        return {
          minHeight: ResponsiveDimensions.touchTargetMedium,
          minWidth: ResponsiveDimensions.touchTargetMedium,
          padding: ResponsiveDimensions.spacing.md,
        };
    }
  };

  return (
    <View style={[styles.touchContainer, getSizeStyle(), style]}>
      {children}
    </View>
  );
};

// Safe Area Container
interface SafeAreaContainerProps {
  children: ReactNode;
  edges?: ("top" | "bottom" | "left" | "right")[];
  style?: ViewStyle;
}

export const SafeAreaContainer: React.FC<SafeAreaContainerProps> = ({
  children,
  edges = ["top", "bottom"],
  style,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const getSafeAreaStyle = (): ViewStyle => {
    const safeStyle: ViewStyle = {};

    if (edges.includes("top")) {
      safeStyle.paddingTop = insets.top;
    }
    if (edges.includes("bottom")) {
      safeStyle.paddingBottom = insets.bottom;
    }
    if (edges.includes("left")) {
      safeStyle.paddingLeft = insets.left + ResponsiveDimensions.spacing.md;
    }
    if (edges.includes("right")) {
      safeStyle.paddingRight = insets.right + ResponsiveDimensions.spacing.md;
    }

    return safeStyle;
  };

  return (
    <ThemedView
      variant="background"
      style={[styles.safeContainer, getSafeAreaStyle(), style]}
    >
      {children}
    </ThemedView>
  );
};

// Responsive Modal Container
interface ResponsiveModalProps {
  children: ReactNode;
  size?: "small" | "medium" | "large";
  style?: ViewStyle;
}

export const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  children,
  size = "medium",
  style,
}) => {
  const theme = useTheme();
  const responsiveLayout = useResponsiveLayout();
  const getModalSize =
    responsiveLayout?.getModalSize || (() => ({ width: "90%", maxWidth: 400 }));

  const modalSize = getModalSize(size);

  return (
    <ThemedView
      variant="card"
      elevated={true}
      style={[
        styles.modalContainer,
        modalSize,
        {
          backgroundColor: theme.colors.surface,
          borderRadius: theme.borderRadius.lg,
          ...theme.shadows.lg,
        },
        style,
      ]}
    >
      {children}
    </ThemedView>
  );
};

// Responsive Text Input Container
interface ResponsiveInputContainerProps {
  children: ReactNode;
  style?: ViewStyle;
}

export const ResponsiveInputContainer: React.FC<
  ResponsiveInputContainerProps
> = ({ children, style }) => {
  const theme = useTheme();
  const responsiveLayout = useResponsiveLayout();
  const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;

  return (
    <View
      style={[
        styles.inputContainer,
        {
          height: 44 * spacingMultiplier,
          paddingHorizontal: theme.spacing.md * spacingMultiplier,
          borderRadius: theme.borderRadius.md,
          backgroundColor: theme.colors.input,
          borderWidth: 1,
          borderColor: theme.colors.inputBorder,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  touchContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  safeContainer: {
    flex: 1,
  },
  modalContainer: {
    alignSelf: "center",
    marginVertical: "auto",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
});
