import { getAPIClient } from "@/src/services/api/dukalink-client";
import AsyncStorage from "@react-native-async-storage/async-storage";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { useSession } from "./AuthContext";

interface Location {
  id: string;
  name: string;
  address: {
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    country?: string;
    zip?: string;
    phone?: string;
  };
  active: boolean;
  legacy?: boolean;
  localized_country_name?: string;
  localized_province_name?: string;
}

interface LocationContextType {
  currentLocation: Location | null;
  availableLocations: Location[];
  setCurrentLocation: (location: Location | null) => void;
  loadLocations: () => Promise<void>;
  isLocationSelected: boolean;
  isLoading: boolean;
  error: string | null;
}

const LocationContext = createContext<LocationContextType | undefined>(
  undefined
);

const STORAGE_KEY = "@dukalink_current_location";

interface LocationProviderProps {
  children: ReactNode;
}

export const LocationProvider: React.FC<LocationProviderProps> = ({
  children,
}) => {
  const { isPosAuthenticated } = useSession();
  const [currentLocation, setCurrentLocationState] = useState<Location | null>(
    null
  );
  const [availableLocations, setAvailableLocations] = useState<Location[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load current location from storage on app start
  useEffect(() => {
    loadCurrentLocation();
  }, []);

  // Load locations only when authenticated
  useEffect(() => {
    if (isPosAuthenticated) {
      loadLocations();
    }
  }, [isPosAuthenticated]);

  const loadCurrentLocation = async () => {
    try {
      const storedLocation = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedLocation) {
        setCurrentLocationState(JSON.parse(storedLocation));
      }
    } catch (error) {
      console.error("Failed to load current location:", error);
    }
  };

  const setCurrentLocation = async (location: Location | null) => {
    try {
      setCurrentLocationState(location);
      if (location) {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(location));
      } else {
        await AsyncStorage.removeItem(STORAGE_KEY);
      }
    } catch (error) {
      console.error("Failed to save current location:", error);
    }
  };

  const loadLocations = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const apiClient = getAPIClient();
      const response = await apiClient.getLocations();

      if (response.success && response.data) {
        const activeLocations = response.data.locations.filter(
          (loc: Location) => loc.active
        );
        setAvailableLocations(activeLocations);

        // If no current location is set and we have locations, don't auto-select
        // Let the user choose their location explicitly
      } else {
        setError(response.error || "Failed to load locations");
      }
    } catch (error) {
      console.error("Failed to load locations:", error);
      setError("Failed to load locations");
    } finally {
      setIsLoading(false);
    }
  };

  const value: LocationContextType = {
    currentLocation,
    availableLocations,
    setCurrentLocation,
    loadLocations,
    isLocationSelected: !!currentLocation,
    isLoading,
    error,
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
};

export const useLocation = (): LocationContextType => {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error("useLocation must be used within a LocationProvider");
  }
  return context;
};

export default LocationContext;
