/**
 * Fix Missing staff_permissions Table
 */

require("dotenv").config();
const mysql = require("mysql2/promise");

async function fixPermissionsTable() {
  let connection;
  
  try {
    console.log("🔗 Connecting to MySQL database...");
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "dukalink",
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || "dukalink_shopify_pos",
      charset: "utf8mb4",
    });

    console.log("✅ Connected to MySQL database");

    // Create staff_permissions table
    console.log("🔧 Creating staff_permissions table...");
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS staff_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        staff_id VARCHAR(255) NOT NULL,
        permission VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIG<PERSON> KEY (staff_id) REFERENCES pos_staff(id) ON DELETE CASCADE,
        UNIQUE KEY unique_staff_permission (staff_id, permission),
        INDEX idx_staff_permissions_staff (staff_id),
        INDEX idx_staff_permissions_permission (permission)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log("✅ staff_permissions table created");

    // Get all staff members
    const [staffRows] = await connection.execute("SELECT id, role FROM pos_staff WHERE is_active = 1");

    // Define permissions by role
    const rolePermissions = {
      'super_admin': [
        'manage_staff', 'manage_inventory', 'process_orders', 'view_reports', 
        'manage_discounts', 'manage_customers', 'manage_sales_agents', 
        'access_dashboard', 'manage_locations', 'manage_terminals'
      ],
      'manager': [
        'manage_inventory', 'process_orders', 'view_reports', 'manage_discounts',
        'manage_customers', 'access_dashboard', 'manage_sales_agents'
      ],
      'cashier': [
        'process_orders', 'manage_customers', 'access_dashboard'
      ]
    };

    // Insert permissions for each staff member
    console.log("🔐 Adding permissions for staff members...");
    for (const staff of staffRows) {
      const permissions = rolePermissions[staff.role] || rolePermissions['cashier'];
      
      for (const permission of permissions) {
        try {
          await connection.execute(
            "INSERT IGNORE INTO staff_permissions (staff_id, permission) VALUES (?, ?)",
            [staff.id, permission]
          );
        } catch (error) {
          // Ignore duplicate key errors
          if (error.code !== 'ER_DUP_ENTRY') {
            console.error(`Error adding permission ${permission} for staff ${staff.id}:`, error);
          }
        }
      }
      
      console.log(`✅ Added permissions for staff ${staff.id} (${staff.role})`);
    }

    // Verify permissions were added
    const [permCount] = await connection.execute("SELECT COUNT(*) as count FROM staff_permissions");
    console.log(`✅ Total permissions in database: ${permCount[0].count}`);

    console.log("\n✅ Permissions table fix completed successfully!");

  } catch (error) {
    console.error("❌ Fix failed:", error);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

fixPermissionsTable();
