/**
 * Responsive Debug Panel for Dukalink POS
 * Shows current responsive state and allows testing different breakpoints
 */

import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { ResponsiveText, ResponsiveHeading2, ResponsiveBody } from "@/src/components/typography/ResponsiveText";
import { ResponsiveContainer, ResponsiveGrid } from "@/src/components/layout/ResponsiveContainer";
import { ResponsivePadding, ResponsiveSpacer } from "@/src/components/layout/ResponsiveSpacing";

interface ResponsiveDebugPanelProps {
  visible?: boolean;
  onClose?: () => void;
}

export const ResponsiveDebugPanel: React.FC<ResponsiveDebugPanelProps> = ({
  visible = false,
  onClose,
}) => {
  const theme = useTheme();
  const {
    screenSize,
    isDesktop,
    isTablet,
    isMobile,
    spacingMultiplier,
    typographyScale,
    gridColumns,
    navigationHeight,
    getModalSize,
    getButtonSize,
  } = useResponsiveLayout();

  const [testModalSize, setTestModalSize] = useState<'small' | 'medium' | 'large'>('medium');
  const [testButtonSize, setTestButtonSize] = useState<'small' | 'medium' | 'large'>('medium');

  if (!visible) return null;

  const debugInfo = [
    { label: "Screen Size", value: screenSize },
    { label: "Is Desktop", value: isDesktop ? "Yes" : "No" },
    { label: "Is Tablet", value: isTablet ? "Yes" : "No" },
    { label: "Is Mobile", value: isMobile ? "Yes" : "No" },
    { label: "Spacing Multiplier", value: spacingMultiplier.toFixed(2) },
    { label: "Typography Scale", value: typographyScale.toFixed(2) },
    { label: "Grid Columns", value: gridColumns.toString() },
    { label: "Navigation Height", value: `${navigationHeight}px` },
  ];

  const testItems = Array.from({ length: 8 }, (_, i) => ({
    id: i,
    title: `Test Item ${i + 1}`,
    subtitle: `Subtitle for item ${i + 1}`,
  }));

  return (
    <View style={[styles.overlay, { backgroundColor: 'rgba(0,0,0,0.8)' }]}>
      <ResponsiveContainer maxWidth={800}>
        <ResponsivePadding size="lg">
          <ModernCard variant="elevated">
            <ResponsivePadding size="lg">
              <View style={styles.header}>
                <ResponsiveHeading2>Responsive Debug Panel</ResponsiveHeading2>
                {onClose && (
                  <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                    <Text style={[styles.closeText, { color: theme.colors.primary }]}>✕</Text>
                  </TouchableOpacity>
                )}
              </View>

              <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Current State */}
                <ResponsivePadding vertical="md">
                  <ResponsiveText variant="h3" weight="semibold">Current State</ResponsiveText>
                  <ResponsiveSpacer size="sm" />
                  {debugInfo.map((info, index) => (
                    <View key={index} style={styles.infoRow}>
                      <ResponsiveBody weight="medium">{info.label}:</ResponsiveBody>
                      <ResponsiveBody color="accent">{info.value}</ResponsiveBody>
                    </View>
                  ))}
                </ResponsivePadding>

                {/* Typography Test */}
                <ResponsivePadding vertical="md">
                  <ResponsiveText variant="h3" weight="semibold">Typography Test</ResponsiveText>
                  <ResponsiveSpacer size="sm" />
                  <ResponsiveText variant="h1">Heading 1 - Large Title</ResponsiveText>
                  <ResponsiveText variant="h2">Heading 2 - Section Title</ResponsiveText>
                  <ResponsiveText variant="h3">Heading 3 - Subsection</ResponsiveText>
                  <ResponsiveText variant="body">Body text - This is regular body text that should scale appropriately.</ResponsiveText>
                  <ResponsiveText variant="caption">Caption text - Small descriptive text</ResponsiveText>
                </ResponsivePadding>

                {/* Button Test */}
                <ResponsivePadding vertical="md">
                  <ResponsiveText variant="h3" weight="semibold">Button Test</ResponsiveText>
                  <ResponsiveSpacer size="sm" />
                  <View style={styles.buttonRow}>
                    <ModernButton title="Small" size="sm" onPress={() => setTestButtonSize('small')} />
                    <ModernButton title="Medium" size="md" onPress={() => setTestButtonSize('medium')} />
                    <ModernButton title="Large" size="lg" onPress={() => setTestButtonSize('large')} />
                  </View>
                  <ResponsiveSpacer size="sm" />
                  <ResponsiveBody>Current button size: {testButtonSize}</ResponsiveBody>
                </ResponsivePadding>

                {/* Modal Size Test */}
                <ResponsivePadding vertical="md">
                  <ResponsiveText variant="h3" weight="semibold">Modal Size Test</ResponsiveText>
                  <ResponsiveSpacer size="sm" />
                  <View style={styles.buttonRow}>
                    <ModernButton 
                      title="Small Modal" 
                      size="sm" 
                      variant="outline"
                      onPress={() => setTestModalSize('small')} 
                    />
                    <ModernButton 
                      title="Medium Modal" 
                      size="sm" 
                      variant="outline"
                      onPress={() => setTestModalSize('medium')} 
                    />
                    <ModernButton 
                      title="Large Modal" 
                      size="sm" 
                      variant="outline"
                      onPress={() => setTestModalSize('large')} 
                    />
                  </View>
                  <ResponsiveSpacer size="sm" />
                  <View style={[getModalSize(testModalSize), { backgroundColor: theme.colors.surface, padding: theme.spacing.md, borderRadius: theme.borderRadius.md }]}>
                    <ResponsiveBody>This is a {testModalSize} modal preview</ResponsiveBody>
                  </View>
                </ResponsivePadding>

                {/* Grid Test */}
                <ResponsivePadding vertical="md">
                  <ResponsiveText variant="h3" weight="semibold">Grid Test</ResponsiveText>
                  <ResponsiveSpacer size="sm" />
                  <ResponsiveGrid minItemWidth={150} spacing={theme.spacing.sm}>
                    {testItems.map((item) => (
                      <ModernCard key={item.id} variant="outlined">
                        <ResponsivePadding size="md">
                          <ResponsiveText variant="bodyLarge" weight="medium">{item.title}</ResponsiveText>
                          <ResponsiveText variant="caption">{item.subtitle}</ResponsiveText>
                        </ResponsivePadding>
                      </ModernCard>
                    ))}
                  </ResponsiveGrid>
                </ResponsivePadding>

                {/* Spacing Test */}
                <ResponsivePadding vertical="md">
                  <ResponsiveText variant="h3" weight="semibold">Spacing Test</ResponsiveText>
                  <ResponsiveSpacer size="sm" />
                  <View style={[styles.spacingDemo, { backgroundColor: theme.colors.surface }]}>
                    <ResponsiveBody>XS Spacing</ResponsiveBody>
                    <ResponsiveSpacer size="xs" />
                    <View style={[styles.spacingBar, { backgroundColor: theme.colors.primary }]} />
                  </View>
                  <View style={[styles.spacingDemo, { backgroundColor: theme.colors.surface }]}>
                    <ResponsiveBody>SM Spacing</ResponsiveBody>
                    <ResponsiveSpacer size="sm" />
                    <View style={[styles.spacingBar, { backgroundColor: theme.colors.primary }]} />
                  </View>
                  <View style={[styles.spacingDemo, { backgroundColor: theme.colors.surface }]}>
                    <ResponsiveBody>MD Spacing</ResponsiveBody>
                    <ResponsiveSpacer size="md" />
                    <View style={[styles.spacingBar, { backgroundColor: theme.colors.primary }]} />
                  </View>
                </ResponsivePadding>
              </ScrollView>
            </ResponsivePadding>
          </ModernCard>
        </ResponsivePadding>
      </ResponsiveContainer>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    maxHeight: 600,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
    flexWrap: 'wrap',
  },
  spacingDemo: {
    padding: 12,
    marginVertical: 4,
    borderRadius: 8,
  },
  spacingBar: {
    height: 4,
    borderRadius: 2,
  },
});
