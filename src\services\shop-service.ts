import { getAPIClient } from "./api/dukalink-client";

export interface ShopInfo {
  name: string;
  email: string;
  phone?: string;
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    country?: string;
    zip?: string;
    formatted?: string[];
  };
  domain: string;
  currencyCode: string;
}

class ShopService {
  private static cachedShopInfo: ShopInfo | null = null;
  private static cacheExpiry: number = 0;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get shop information from Shopify Admin API
   */
  static async getShopInfo(forceRefresh: boolean = false): Promise<ShopInfo> {
    // Clear cache if force refresh is requested
    if (forceRefresh) {
      this.clearCache();
    }

    // Return cached data if still valid and not forcing refresh
    if (!forceRefresh && this.cachedShopInfo && Date.now() < this.cacheExpiry) {
      return this.cachedShopInfo;
    }

    try {
      const apiClient = getAPIClient();

      // Use the existing backend endpoint that gets shop info from Shopify
      const response = await apiClient.getStoreInfo();

      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch shop information");
      }

      const store = response.data.store;

      // Transform the response to our ShopInfo interface
      const shopInfo: ShopInfo = {
        name: store.name || "TREASURED SCENTS",
        email: store.email || "<EMAIL>",
        phone: store.phone || "+254 111 443 993",
        address: store.address
          ? {
              address1: store.address.address1 || "Greenhouse Mall, Ngong Road",
              address2: store.address.address2 || undefined,
              city: store.address.city || "Kenya",
              province: store.address.province || undefined,
              country: store.address.country || "Kenya",
              zip: store.address.zip || undefined,
              formatted: [
                store.address.address1 || "Greenhouse Mall, Ngong Road",
                store.address.city || "Kenya",
                store.address.country || "Kenya",
              ].filter(Boolean) as string[],
            }
          : {
              address1: "Greenhouse Mall, Ngong Road",
              city: "Kenya",
              country: "Kenya",
              formatted: ["Greenhouse Mall, Ngong Road, Kenya"],
            },
        domain: store.domain || "",
        currencyCode: store.currency || "KES",
      };

      // Cache the result
      this.cachedShopInfo = shopInfo;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;

      return shopInfo;
    } catch (error: any) {
      console.error("❌ Error fetching shop info:", error);
      console.error("❌ Error details:", {
        message: error?.message,
        code: error?.code,
        response: error?.response?.data,
      });
      // Return fallback shop info if API fails
      return this.getFallbackShopInfo();
    }
  }

  /**
   * Get fallback shop information when API is unavailable
   */
  private static getFallbackShopInfo(): ShopInfo {
    return {
      name: "TREASURED SCENTS",
      email: "<EMAIL>",
      phone: "+254 111 443 993",
      address: {
        address1: "Greenhouse Mall, Ngong Road",
        city: "Kenya",
        country: "Kenya",
        formatted: ["Greenhouse Mall, Ngong Road, Kenya"],
      },
      domain: "treasuredscents.myshopify.com",
      currencyCode: "KES",
    };
  }

  /**
   * Clear cached shop info (useful for testing or when shop settings change)
   */
  static clearCache(): void {
    this.cachedShopInfo = null;
    this.cacheExpiry = 0;
  }

  /**
   * Get formatted store address for receipts
   */
  static async getFormattedStoreAddress(
    forceRefresh: boolean = false
  ): Promise<string> {
    // For receipts, use the specific Treasured Scents location
    return "Greenhouse Mall, Ngong Road, Kenya";
  }

  /**
   * Get store contact information for receipts
   */
  static async getStoreContactInfo(): Promise<{
    name: string;
    phone?: string;
    email: string;
  }> {
    const shopInfo = await this.getShopInfo();

    return {
      name: shopInfo.name,
      phone: shopInfo.phone,
      email: shopInfo.email,
    };
  }
}

export default ShopService;
