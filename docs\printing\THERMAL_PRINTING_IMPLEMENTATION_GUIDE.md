# Thermal Printing System - Implementation Guide

## Project Structure

Create the following directory structure in your React Native project:

```
src/
├── services/
│   ├── PrintService.js
│   └── EnhancedPrintService.js
├── utils/
│   ├── BluetoothPermissionHelper.js
│   ├── ReceiptGenerator.js
│   └── QRCodePrinter.js
├── screens/
│   └── settings/
│       ├── PrinterSetupScreen.js
│       ├── PrinterModelScreen.js
│       └── TestReceiptScreen.js
└── components/
    └── printing/
        ├── PrintButton.js
        └── PrinterStatus.js
```

## Step 1: Core PrintService Implementation

### Create `src/services/PrintService.js`

```javascript
import { NetPrinter, USBPrinter } from '@tumihub/react-native-thermal-receipt-printer';
import { LogBox, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BluetoothPermissionHelper from '../utils/BluetoothPermissionHelper';
import { BluetoothEscposPrinter, BluetoothManager } from 'react-native-bluetooth-escpos-printer';

// Suppress warnings
LogBox.ignoreLogs([
  '`new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method',
  '`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method',
]);

class PrintService {
  constructor() {
    this.isConnected = false;
    this.pairedDevices = [];
    this.connectedDevice = null;
    this.PRINTER_STORAGE_KEY = '@dukalink_printer_address';
    this.PRINTER_TYPE_KEY = '@dukalink_printer_type';
    this.PRINTER_MODEL_KEY = '@dukalink_printer_model';
    this.printerType = null; // 'ble', 'usb', or 'net'
    this.printerModel = null;
    this.printer = null;
  }

  /**
   * Initialize the print service
   */
  async init() {
    try {
      // Load saved printer configuration
      const savedPrinterAddress = await AsyncStorage.getItem(this.PRINTER_STORAGE_KEY);
      const savedPrinterType = await AsyncStorage.getItem(this.PRINTER_TYPE_KEY);
      const savedPrinterModel = await AsyncStorage.getItem(this.PRINTER_MODEL_KEY);

      if (savedPrinterType) {
        this.printerType = savedPrinterType;
        this.printerModel = savedPrinterModel || 'generic';

        // Initialize the appropriate printer type
        switch (this.printerType) {
          case 'ble':
            this.printer = BluetoothEscposPrinter;
            break;
          case 'usb':
            if (Platform.OS === 'android') {
              this.printer = USBPrinter;
              await USBPrinter.init();
            }
            break;
          case 'net':
            this.printer = NetPrinter;
            await NetPrinter.init();
            break;
        }

        // Try to connect to the last used printer
        if (savedPrinterAddress) {
          await this.connectToPrinter(savedPrinterAddress);
        }
      } else {
        // Default to BLE printer
        this.printerType = 'ble';
        this.printer = BluetoothEscposPrinter;
      }

      return true;
    } catch (error) {
      console.error('Error initializing printer service:', error);
      return false;
    }
  }

  /**
   * Set printer type and initialize
   */
  async setPrinterType(type) {
    try {
      this.printerType = type;
      await AsyncStorage.setItem(this.PRINTER_TYPE_KEY, type);

      switch (type) {
        case 'ble':
          this.printer = BluetoothEscposPrinter;
          break;
        case 'usb':
          this.printer = USBPrinter;
          await USBPrinter.init();
          break;
        case 'net':
          this.printer = NetPrinter;
          await NetPrinter.init();
          break;
      }

      return true;
    } catch (error) {
      console.error('Error setting printer type:', error);
      return false;
    }
  }

  /**
   * Request Bluetooth permissions
   */
  async requestBluetoothPermissions() {
    if (Platform.OS === 'android') {
      try {
        return await BluetoothPermissionHelper.requestBluetoothPermissions();
      } catch (error) {
        console.error('Error requesting Bluetooth permissions:', error);
        return false;
      }
    }
    return true;
  }

  /**
   * Scan for available devices
   */
  async scanDevices() {
    try {
      switch (this.printerType) {
        case 'ble':
          const devices = await BluetoothManager.scanDevices();
          this.pairedDevices = devices.found || [];
          return this.pairedDevices;
        case 'usb':
          if (Platform.OS === 'android') {
            const usbDevices = await USBPrinter.getDeviceList();
            return usbDevices || [];
          }
          return [];
        case 'net':
          // For network printers, return common IP ranges or manual entry option
          return [{ name: 'Manual Entry', address: 'manual' }];
        default:
          return [];
      }
    } catch (error) {
      console.error('Error scanning devices:', error);
      return [];
    }
  }

  /**
   * Connect to a printer
   */
  async connectToPrinter(address, port = null) {
    try {
      switch (this.printerType) {
        case 'ble':
          await BluetoothManager.connect(address);
          break;
        case 'usb':
          await USBPrinter.connectPrinter(address);
          break;
        case 'net':
          await NetPrinter.connectPrinter(address, port || 9100);
          break;
      }

      this.isConnected = true;
      this.connectedDevice = { address, port };
      
      // Save the connected printer
      await AsyncStorage.setItem(this.PRINTER_STORAGE_KEY, address);
      
      return true;
    } catch (error) {
      console.error('Error connecting to printer:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Print a receipt
   */
  async printReceipt(saleData) {
    if (!this.isConnected) {
      throw new Error('Printer not connected');
    }

    try {
      // Initialize printer
      await BluetoothEscposPrinter.printerInit();

      // Print header
      await BluetoothEscposPrinter.printerAlign(BluetoothEscposPrinter.ALIGN.CENTER);
      await BluetoothEscposPrinter.printText('Your Business Name\n', null);
      await BluetoothEscposPrinter.printText('------------------------\n', null);

      // Print receipt info
      const date = new Date();
      const dateStr = date.toLocaleDateString();
      const timeStr = date.toLocaleTimeString();
      
      await BluetoothEscposPrinter.printText('SALES RECEIPT\n', null);
      await BluetoothEscposPrinter.printText(`${dateStr} at ${timeStr}\n\n`, null);

      // Print customer info
      if (saleData.customer_name) {
        await BluetoothEscposPrinter.printerAlign(BluetoothEscposPrinter.ALIGN.LEFT);
        await BluetoothEscposPrinter.printText(`Customer: ${saleData.customer_name}\n`, null);
      }

      // Print items
      await BluetoothEscposPrinter.printText('\nItems:\n', null);
      await BluetoothEscposPrinter.printText('------------------------\n', null);

      for (const item of saleData.items || []) {
        const itemLine = `${item.name}\n`;
        const qtyPriceLine = `${item.quantity} x ${item.unit_price} = ${item.total_price}\n`;
        
        await BluetoothEscposPrinter.printText(itemLine, null);
        await BluetoothEscposPrinter.printText(qtyPriceLine, null);
      }

      // Print totals
      await BluetoothEscposPrinter.printText('------------------------\n', null);
      await BluetoothEscposPrinter.printText(`Subtotal: ${saleData.subtotal || 0}\n`, null);
      await BluetoothEscposPrinter.printText(`Tax: ${saleData.tax_amount || 0}\n`, null);
      await BluetoothEscposPrinter.printText(`Total: ${saleData.total_amount || 0}\n`, null);

      // Print payment info
      if (saleData.payment_method) {
        await BluetoothEscposPrinter.printText(`Payment: ${saleData.payment_method}\n`, null);
      }

      // Print footer
      await BluetoothEscposPrinter.printerAlign(BluetoothEscposPrinter.ALIGN.CENTER);
      await BluetoothEscposPrinter.printText('\nThank you for your business!\n', null);
      await BluetoothEscposPrinter.printText('\n\n\n', null);

    } catch (error) {
      console.error('Error printing receipt:', error);
      throw new Error(`Failed to print receipt: ${error.message}`);
    }
  }

  /**
   * Print test receipt
   */
  async printTestReceipt() {
    const testData = {
      customer_name: 'Test Customer',
      items: [
        { name: 'Test Item 1', quantity: 1, unit_price: 10.00, total_price: 10.00 },
        { name: 'Test Item 2', quantity: 2, unit_price: 5.00, total_price: 10.00 }
      ],
      subtotal: 20.00,
      tax_amount: 2.00,
      total_amount: 22.00,
      payment_method: 'Cash'
    };

    await this.printReceipt(testData);
  }

  /**
   * Disconnect from printer
   */
  async disconnect() {
    try {
      switch (this.printerType) {
        case 'ble':
          await BluetoothManager.disconnect();
          break;
        case 'usb':
          await USBPrinter.closeConn();
          break;
        case 'net':
          await NetPrinter.closeConn();
          break;
      }

      this.isConnected = false;
      this.connectedDevice = null;
    } catch (error) {
      console.error('Error disconnecting printer:', error);
    }
  }

  // Getters
  getPrinterType() { return this.printerType; }
  getConnectedDevice() { return this.connectedDevice; }
  isConnectedToPrinter() { return this.isConnected; }
}

// Export singleton instance
export default new PrintService();
```

## Step 2: Bluetooth Permission Helper

### Create `src/utils/BluetoothPermissionHelper.js`

```javascript
import { Platform, PermissionsAndroid, NativeModules } from 'react-native';

export const BluetoothPermissionHelper = {
  /**
   * Request Bluetooth permissions for Android 12+
   */
  requestBluetoothPermissions: async () => {
    if (Platform.OS !== 'android') {
      return true;
    }

    if (Platform.Version >= 31) {
      try {
        const bluetoothConnectGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          {
            title: 'Bluetooth Connect Permission',
            message: 'This app needs access to Bluetooth to connect to printers.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );

        const bluetoothScanGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          {
            title: 'Bluetooth Scan Permission',
            message: 'This app needs access to Bluetooth to scan for printers.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );

        return (
          bluetoothConnectGranted === PermissionsAndroid.RESULTS.GRANTED &&
          bluetoothScanGranted === PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (error) {
        console.warn('Error requesting Bluetooth permissions:', error);
        return false;
      }
    }

    // For older Android versions, permissions are granted at install time
    return true;
  },

  /**
   * Check if Bluetooth is enabled
   */
  isBluetoothEnabled: async () => {
    try {
      return await NativeModules.BluetoothManager.isBluetoothEnabled();
    } catch (error) {
      console.warn('Error checking Bluetooth state:', error);
      return false;
    }
  },

  /**
   * Enable Bluetooth
   */
  enableBluetooth: async () => {
    try {
      if (Platform.OS !== 'android') {
        return false;
      }

      if (Platform.Version >= 31) {
        const hasPermissions = await BluetoothPermissionHelper.requestBluetoothPermissions();
        if (!hasPermissions) {
          return false;
        }
      }

      await NativeModules.BluetoothManager.enableBluetooth();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return await BluetoothPermissionHelper.isBluetoothEnabled();
    } catch (error) {
      console.warn('Error enabling Bluetooth:', error);
      return false;
    }
  },
};

export default BluetoothPermissionHelper;
```

## Step 3: Enhanced Print Service

### Create `src/services/EnhancedPrintService.js`

```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import PrintService from './PrintService';
import { BluetoothManager, BluetoothEscposPrinter } from 'react-native-bluetooth-escpos-printer';

const PRINTER_STORAGE_KEY = '@dukalink_printer_address';

class EnhancedPrintService {
  constructor() {
    this.isConnected = false;
    this.printerAddress = null;
  }

  /**
   * Print receipt with enhanced error handling
   */
  async printReceipt(saleId) {
    try {
      // Ensure printer connection
      await this.ensurePrinterConnection();

      // Get receipt data
      const receiptData = await this.getReceiptData(saleId);

      // Execute print
      await this.executePrint(receiptData);

      return { success: true, saleId };
    } catch (error) {
      console.error('Print error:', error.message);
      return await this.handlePrintError(error, saleId);
    }
  }

  /**
   * Ensure printer connection
   */
  async ensurePrinterConnection() {
    // Check if already connected
    if (this.isConnected && this.printerAddress) {
      return;
    }

    // Try to get saved printer address
    this.printerAddress = await AsyncStorage.getItem(PRINTER_STORAGE_KEY);

    if (!this.printerAddress) {
      throw new Error('No printer configured. Please set up printer first.');
    }

    // Test connection
    try {
      await BluetoothManager.connect(this.printerAddress);
      this.isConnected = true;
    } catch (error) {
      throw new Error(`Failed to connect to printer: ${error.message}`);
    }
  }

  /**
   * Get receipt data for a sale
   */
  async getReceiptData(saleId) {
    // This would typically fetch from your API or local database
    // For demo purposes, return mock data
    return {
      id: saleId,
      date: new Date(),
      customer_name: 'Customer',
      items: [
        { name: 'Sample Item', quantity: 1, unit_price: 10.00, total_price: 10.00 }
      ],
      subtotal: 10.00,
      tax_amount: 1.00,
      total_amount: 11.00,
      payment_method: 'Cash'
    };
  }

  /**
   * Execute the actual printing
   */
  async executePrint(receiptData) {
    await BluetoothEscposPrinter.printerInit();

    // Print header
    await BluetoothEscposPrinter.printerAlign(BluetoothEscposPrinter.ALIGN.CENTER);
    await BluetoothEscposPrinter.printText('Your Business\n', null);
    await BluetoothEscposPrinter.printText('------------------------\n', null);

    // Print receipt content
    await BluetoothEscposPrinter.printText(`Receipt #${receiptData.id}\n`, null);
    await BluetoothEscposPrinter.printText(`${receiptData.date.toLocaleDateString()}\n\n`, null);

    // Print items
    for (const item of receiptData.items) {
      await BluetoothEscposPrinter.printText(`${item.name}\n`, null);
      await BluetoothEscposPrinter.printText(`${item.quantity} x ${item.unit_price} = ${item.total_price}\n`, null);
    }

    // Print total
    await BluetoothEscposPrinter.printText('------------------------\n', null);
    await BluetoothEscposPrinter.printText(`Total: ${receiptData.total_amount}\n`, null);
    await BluetoothEscposPrinter.printText('\n\n\n', null);
  }

  /**
   * Handle print errors
   */
  async handlePrintError(error, saleId) {
    // Store receipt for offline printing
    await this.storeOfflineReceipt(saleId);

    if (error.message.includes('No printer configured')) {
      return this.handlePrinterConfigurationError(saleId);
    }

    return { success: false, error: error.message };
  }

  /**
   * Store receipt data for offline printing
   */
  async storeOfflineReceipt(saleId) {
    try {
      const receiptData = await this.getReceiptData(saleId);
      await AsyncStorage.setItem(`offline_receipt_${saleId}`, JSON.stringify(receiptData));
    } catch (error) {
      console.error('Failed to store offline receipt:', error);
    }
  }

  /**
   * Handle printer configuration errors
   */
  handlePrinterConfigurationError(saleId) {
    return new Promise((resolve) => {
      Alert.alert(
        'Printer Setup Required',
        'No printer is configured. Would you like to set up a printer now?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => resolve({ success: false, error: 'No printer configured', cancelled: true })
          },
          {
            text: 'Setup Printer',
            onPress: () => {
              // Navigate to printer setup screen
              // NavigationService.navigate('PrinterSetup');
              resolve({ success: false, error: 'Redirected to printer setup', redirected: true });
            }
          }
        ]
      );
    });
  }
}

export default new EnhancedPrintService();
```

## Step 4: Print Button Component

### Create `src/components/printing/PrintButton.js`

```javascript
import React, { useState } from 'react';
import { Alert } from 'react-native';
import { Button } from 'react-native-paper';
import EnhancedPrintService from '../../services/EnhancedPrintService';

const PrintButton = ({ saleId, onPrintSuccess, onPrintError, disabled = false }) => {
  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrint = async () => {
    try {
      setIsPrinting(true);

      const result = await EnhancedPrintService.printReceipt(saleId);

      if (result.success) {
        Alert.alert('Success', 'Receipt printed successfully');
        onPrintSuccess?.(result);
      } else if (result.redirected) {
        // User was redirected to printer setup
        onPrintError?.(result);
      } else {
        Alert.alert('Print Error', result.error || 'Failed to print receipt');
        onPrintError?.(result);
      }
    } catch (error) {
      Alert.alert('Error', `Print failed: ${error.message}`);
      onPrintError?.({ success: false, error: error.message });
    } finally {
      setIsPrinting(false);
    }
  };

  return (
    <Button
      mode="contained"
      onPress={handlePrint}
      loading={isPrinting}
      disabled={disabled || isPrinting}
      icon="printer"
    >
      {isPrinting ? 'Printing...' : 'Print Receipt'}
    </Button>
  );
};

export default PrintButton;
```

## Step 5: Printer Setup Screen

### Create `src/screens/settings/PrinterSetupScreen.js`

```javascript
import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, FlatList } from 'react-native';
import { Button, Text, Card, List, ActivityIndicator } from 'react-native-paper';
import PrintService from '../../services/PrintService';

const PrinterSetupScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [devices, setDevices] = useState([]);
  const [connectedDevice, setConnectedDevice] = useState(null);
  const [printerType, setPrinterType] = useState('ble');

  useEffect(() => {
    initializePrinterService();
  }, []);

  const initializePrinterService = async () => {
    setLoading(true);
    try {
      await PrintService.init();
      const currentDevice = PrintService.getConnectedDevice();
      setConnectedDevice(currentDevice);
      setPrinterType(PrintService.getPrinterType() || 'ble');
    } catch (error) {
      console.error('Error initializing printer service:', error);
    } finally {
      setLoading(false);
    }
  };

  const scanDevices = async () => {
    setLoading(true);
    try {
      // Request permissions for Bluetooth
      if (printerType === 'ble') {
        const hasPermissions = await PrintService.requestBluetoothPermissions();
        if (!hasPermissions) {
          Alert.alert('Permissions Required', 'Bluetooth permissions are required to scan for printers.');
          setLoading(false);
          return;
        }
      }

      const foundDevices = await PrintService.scanDevices();
      setDevices(foundDevices);
    } catch (error) {
      Alert.alert('Error', `Failed to scan devices: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const connectToPrinter = async (device) => {
    setLoading(true);
    try {
      const connected = await PrintService.connectToPrinter(device.address);
      if (connected) {
        setConnectedDevice(device);
        Alert.alert('Success', 'Connected to printer successfully');
      } else {
        Alert.alert('Error', 'Failed to connect to printer');
      }
    } catch (error) {
      Alert.alert('Error', `Connection failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPrint = async () => {
    setLoading(true);
    try {
      await PrintService.printTestReceipt();
      Alert.alert('Success', 'Test receipt printed successfully');
    } catch (error) {
      Alert.alert('Error', `Test print failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderDevice = ({ item }) => (
    <List.Item
      title={item.name || 'Unknown Device'}
      description={item.address}
      left={props => <List.Icon {...props} icon="printer" />}
      onPress={() => connectToPrinter(item)}
    />
  );

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>Printer Setup</Text>

          {connectedDevice ? (
            <View style={styles.connectedContainer}>
              <Text style={styles.connectedText}>
                Connected to: {connectedDevice.name || connectedDevice.address}
              </Text>
              <Button mode="contained" onPress={testPrint} disabled={loading}>
                Test Print
              </Button>
            </View>
          ) : (
            <Text style={styles.noConnectionText}>No printer connected</Text>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.subtitle}>Available Devices</Text>
          <Button mode="outlined" onPress={scanDevices} disabled={loading} style={styles.scanButton}>
            {loading ? 'Scanning...' : 'Scan for Devices'}
          </Button>

          {loading && <ActivityIndicator style={styles.loader} />}

          <FlatList
            data={devices}
            renderItem={renderDevice}
            keyExtractor={(item, index) => `${item.address || index}`}
            style={styles.deviceList}
          />
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  card: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  connectedContainer: {
    alignItems: 'center',
  },
  connectedText: {
    marginBottom: 16,
    color: 'green',
  },
  noConnectionText: {
    textAlign: 'center',
    color: 'gray',
  },
  scanButton: {
    marginBottom: 16,
  },
  loader: {
    marginVertical: 16,
  },
  deviceList: {
    maxHeight: 300,
  },
});

export default PrinterSetupScreen;
```

## Step 6: Integration Example

### Using the Print System in Your App

```javascript
// In your main component (e.g., CartScreen, SalesScreen)
import React from 'react';
import { View } from 'react-native';
import PrintButton from '../components/printing/PrintButton';

const SalesScreen = () => {
  const handlePrintSuccess = (result) => {
    console.log('Print successful:', result);
    // Handle successful print (e.g., update UI, navigate)
  };

  const handlePrintError = (result) => {
    console.log('Print error:', result);
    // Handle print error (e.g., show retry option, navigate to setup)
    if (result.redirected) {
      // User was redirected to printer setup
      navigation.navigate('PrinterSetup');
    }
  };

  return (
    <View>
      {/* Your sales content */}

      <PrintButton
        saleId={123}
        onPrintSuccess={handlePrintSuccess}
        onPrintError={handlePrintError}
      />
    </View>
  );
};
```

## Step 7: App Integration

### Add to your main App.js or navigation

```javascript
// In your navigation stack
import PrinterSetupScreen from './src/screens/settings/PrinterSetupScreen';

// Add to your stack navigator
<Stack.Screen
  name="PrinterSetup"
  component={PrinterSetupScreen}
  options={{ title: 'Printer Setup' }}
/>
```

This implementation guide provides a complete foundation for integrating thermal printing into your React Native application. The modular design allows for easy customization and extension based on your specific requirements.
