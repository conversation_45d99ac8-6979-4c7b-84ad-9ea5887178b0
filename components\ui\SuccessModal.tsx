import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import React from "react";
import { Modal, ScrollView, StyleSheet, Text, View } from "react-native";

export interface SuccessAction {
  title: string;
  onPress: () => void;
  variant?: "primary" | "outline" | "ghost";
  icon?: string;
  dismissModal?: boolean; // Whether this action should dismiss the modal (default: true)
}

interface SuccessModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  message: string;
  actions?: SuccessAction[];
  showCloseButton?: boolean;
}

export function SuccessModal({
  visible,
  onClose,
  title,
  message,
  actions = [],
  showCloseButton = true,
}: SuccessModalProps) {
  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");
  const successColor = "#10B981"; // Green color for success

  const handleActionPress = (action: SuccessAction) => {
    action.onPress();
    // Only dismiss modal if dismissModal is not explicitly set to false
    if (action.dismissModal !== false) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}} // Disable Android back button
    >
      <View style={styles.overlay}>
        <View style={[styles.modal, { backgroundColor, borderColor }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <View
                style={[
                  styles.successIcon,
                  { backgroundColor: successColor + "20" },
                ]}
              >
                <IconSymbol
                  name="checkmark.circle.fill"
                  size={32}
                  color={successColor}
                />
              </View>
              <Text style={[styles.title, { color: textColor }]}>{title}</Text>
            </View>
            {/* Close button removed - users must use action buttons */}
          </View>

          {/* Message */}
          <View style={styles.messageSection}>
            <Text style={[styles.message, { color: textSecondary }]}>
              {message}
            </Text>
          </View>

          {/* Actions */}
          {actions.length > 0 && (
            <ScrollView
              style={styles.actionsSection}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
            >
              {actions.map((action, index) => (
                <ModernButton
                  key={index}
                  title={action.title}
                  onPress={() => handleActionPress(action)}
                  variant={action.variant || "outline"}
                  icon={
                    action.icon ? (
                      <IconSymbol name={action.icon} size={16} color="white" />
                    ) : undefined
                  }
                  style={[
                    styles.actionButton,
                    index === actions.length - 1 && styles.lastActionButton,
                  ]}
                />
              ))}
            </ScrollView>
          )}

          {/* Default close button if no actions */}
          {actions.length === 0 && (
            <View style={styles.defaultActionSection}>
              <ModernButton
                title="OK"
                onPress={onClose}
                variant="primary"
                style={styles.defaultActionButton}
              />
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modal: {
    width: "90%",
    maxWidth: 400,
    maxHeight: "80%",
    borderRadius: 16,
    borderWidth: 1,
    padding: Spacing.lg,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: Spacing.lg,
  },
  headerContent: {
    flex: 1,
    alignItems: "center",
  },
  successIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Spacing.md,
  },
  title: {
    ...Typography.h3,
    fontWeight: "600",
    textAlign: "center",
  },
  messageSection: {
    marginBottom: Spacing.lg,
  },
  message: {
    ...Typography.body,
    textAlign: "center",
    lineHeight: 22,
  },
  actionsSection: {
    maxHeight: 200,
  },
  actionButton: {
    width: "100%",
    marginBottom: Spacing.md,
  },
  lastActionButton: {
    marginBottom: 0,
  },
  defaultActionSection: {
    marginTop: Spacing.md,
  },
  defaultActionButton: {
    width: "100%",
  },
});
