/**
 * Currency utilities for Kenyan Shilling (KES) formatting
 * Centralized currency handling for the Dukalink POS system
 */

// Currency configuration for Kenya
export const CURRENCY_CONFIG = {
  code: "KES",
  symbol: "KSh",
  name: "Kenyan Shilling",
  locale: "en-KE",
  decimalPlaces: 2,
  thousandsSeparator: ",",
  decimalSeparator: ".",
  symbolPosition: "before", // 'before' or 'after'
  spaceAfterSymbol: true,
};

// Currency formatting options
export interface CurrencyFormatOptions {
  showSymbol?: boolean;
  showCode?: boolean;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  useGrouping?: boolean;
  compact?: boolean; // For large numbers (e.g., 1.2K, 1.5M)
}

/**
 * Format amount as Kenyan Shilling currency
 */
export const formatCurrency = (
  amount: number,
  options: CurrencyFormatOptions = {}
): string => {
  const {
    showSymbol = true,
    showCode = false,
    minimumFractionDigits = CURRENCY_CONFIG.decimalPlaces,
    maximumFractionDigits = CURRENCY_CONFIG.decimalPlaces,
    useGrouping = true,
    compact = false,
  } = options;

  // Handle invalid amounts
  if (typeof amount !== "number" || isNaN(amount)) {
    return showSymbol ? `${CURRENCY_CONFIG.symbol} 0.00` : "0.00";
  }

  // Format for compact display (K, M, B)
  if (compact && Math.abs(amount) >= 1000) {
    return formatCompactCurrency(amount, options);
  }

  // Format the number
  const formattedNumber = amount.toLocaleString(CURRENCY_CONFIG.locale, {
    minimumFractionDigits,
    maximumFractionDigits,
    useGrouping,
  });

  // Build the currency string
  let result = "";

  if (showSymbol) {
    result =
      CURRENCY_CONFIG.symbolPosition === "before"
        ? `${CURRENCY_CONFIG.symbol}${
            CURRENCY_CONFIG.spaceAfterSymbol ? " " : ""
          }${formattedNumber}`
        : `${formattedNumber}${CURRENCY_CONFIG.spaceAfterSymbol ? " " : ""}${
            CURRENCY_CONFIG.symbol
          }`;
  } else {
    result = formattedNumber;
  }

  if (showCode) {
    result += ` ${CURRENCY_CONFIG.code}`;
  }

  return result;
};

/**
 * Format currency in compact form (1.2K, 1.5M, etc.)
 */
export const formatCompactCurrency = (
  amount: number,
  options: CurrencyFormatOptions = {}
): string => {
  const { showSymbol = true } = options;

  const absAmount = Math.abs(amount);
  const sign = amount < 0 ? "-" : "";

  let value: number;
  let suffix: string;

  if (absAmount >= 1_000_000_000) {
    value = amount / 1_000_000_000;
    suffix = "B";
  } else if (absAmount >= 1_000_000) {
    value = amount / 1_000_000;
    suffix = "M";
  } else if (absAmount >= 1_000) {
    value = amount / 1_000;
    suffix = "K";
  } else {
    return formatCurrency(amount, { ...options, compact: false });
  }

  const formattedValue = value.toFixed(1).replace(/\.0$/, "");
  const symbol = showSymbol ? `${CURRENCY_CONFIG.symbol} ` : "";

  return `${sign}${symbol}${formattedValue}${suffix}`;
};

/**
 * Parse currency string back to number
 */
export const parseCurrency = (currencyString: string): number => {
  if (!currencyString || typeof currencyString !== "string") {
    return 0;
  }

  // Remove currency symbol, code, and spaces
  const cleanString = currencyString
    .replace(new RegExp(CURRENCY_CONFIG.symbol, "g"), "")
    .replace(new RegExp(CURRENCY_CONFIG.code, "g"), "")
    .replace(/[^\d.,\-]/g, "")
    .replace(/,/g, ""); // Remove thousands separators

  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Validate currency amount
 */
export const validateCurrencyAmount = (
  amount: number,
  options: {
    min?: number;
    max?: number;
    allowZero?: boolean;
    allowNegative?: boolean;
  } = {}
): { valid: boolean; error?: string } => {
  const {
    min = 0,
    max = 10_000_000, // 10M KES default max
    allowZero = false,
    allowNegative = false,
  } = options;

  if (typeof amount !== "number" || isNaN(amount)) {
    return { valid: false, error: "Invalid amount" };
  }

  if (!allowNegative && amount < 0) {
    return { valid: false, error: "Amount cannot be negative" };
  }

  if (!allowZero && amount === 0) {
    return { valid: false, error: "Amount must be greater than zero" };
  }

  if (amount < min) {
    return {
      valid: false,
      error: `Amount must be at least ${formatCurrency(min)}`,
    };
  }

  if (amount > max) {
    return {
      valid: false,
      error: `Amount cannot exceed ${formatCurrency(max)}`,
    };
  }

  return { valid: true };
};

/**
 * Calculate percentage of amount
 */
export const calculatePercentage = (
  amount: number,
  percentage: number
): number => {
  return (amount * percentage) / 100;
};

// Tax calculation function removed - POS system no longer calculates taxes

/**
 * Calculate discount amount
 */
export const calculateDiscount = (
  amount: number,
  discountPercentage: number
): { discountAmount: number; finalAmount: number } => {
  const discountAmount = calculatePercentage(amount, discountPercentage);
  const finalAmount = amount - discountAmount;

  return {
    discountAmount: Math.round(discountAmount * 100) / 100,
    finalAmount: Math.round(finalAmount * 100) / 100,
  };
};

/**
 * Round to nearest currency unit (e.g., nearest 5 cents)
 */
export const roundToCurrencyUnit = (
  amount: number,
  unit: number = 0.05 // Round to nearest 5 cents
): number => {
  return Math.round(amount / unit) * unit;
};

/**
 * Format currency for different contexts
 */
export const CurrencyFormatter = {
  // For product prices
  product: (amount: number) => formatCurrency(amount),

  // For cart totals
  total: (amount: number) =>
    formatCurrency(amount, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }),

  // For receipts
  receipt: (amount: number) =>
    formatCurrency(amount, {
      showSymbol: true,
      minimumFractionDigits: 2,
    }),

  // For compact display in lists
  compact: (amount: number) => formatCompactCurrency(amount),

  // For input placeholders
  placeholder: (amount: number = 0) =>
    formatCurrency(amount, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }),

  // For change calculations
  change: (amount: number) => formatCurrency(Math.abs(amount)),

  // Tax formatter removed - POS system no longer displays taxes

  // For discount display
  discount: (amount: number) =>
    formatCurrency(amount, {
      minimumFractionDigits: 2,
    }),
};

/**
 * Currency conversion utilities (for future multi-currency support)
 */
export const CurrencyConverter = {
  // Placeholder for future currency conversion
  convert: (
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    exchangeRate: number
  ): number => {
    if (fromCurrency === toCurrency) return amount;
    return amount * exchangeRate;
  },

  // Format with different currency
  formatAs: (amount: number, currencyCode: string, symbol: string): string => {
    const formattedNumber = amount.toLocaleString("en-KE", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    return `${symbol} ${formattedNumber}`;
  },
};

// Export default formatter for convenience
export default {
  format: formatCurrency,
  parse: parseCurrency,
  validate: validateCurrencyAmount,
  compact: formatCompactCurrency,
  ...CurrencyFormatter,
};
