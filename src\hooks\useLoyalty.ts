/**
 * <PERSON>yal<PERSON> Hooks
 * 
 * Custom hooks for loyalty system data management.
 * Follows existing patterns and provides easy migration path to React Query.
 */

import { useState, useEffect, useCallback } from "react";
import { useAppDispatch } from "../store";
import { loyaltyService } from "../services/loyalty-service";
import {
  CustomerLoyaltyData,
  LoyaltyTransaction,
  LoyaltyDiscountCalculation,
  PointsRedemptionRequest,
  PointsRedemptionResult,
  LoyaltyLeaderboardEntry,
} from "../types/shopify";
import { LoyaltyAnalytics } from "../services/loyalty-service";

// Customer Loyalty Hook
export const useCustomerLoyalty = (customerId: string | null) => {
  const [loyaltyData, setLoyaltyData] = useState<CustomerLoyaltyData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLoyaltyData = useCallback(async () => {
    if (!customerId) {
      setLoyaltyData(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const data = await loyaltyService.getCustomerLoyaltySummary(customerId);
      setLoyaltyData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch loyalty data");
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  const refreshLoyaltyData = useCallback(() => {
    fetchLoyaltyData();
  }, [fetchLoyaltyData]);

  useEffect(() => {
    fetchLoyaltyData();
  }, [fetchLoyaltyData]);

  return {
    loyaltyData,
    isLoading,
    error,
    refresh: refreshLoyaltyData,
  };
};

// Loyalty Transactions Hook
export const useLoyaltyTransactions = (
  customerId: string | null,
  options?: {
    limit?: number;
    offset?: number;
    type?: "earned" | "redeemed" | "expired" | "adjusted";
  }
) => {
  const [transactions, setTransactions] = useState<LoyaltyTransaction[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = useCallback(async () => {
    if (!customerId) {
      setTransactions([]);
      setTotalCount(0);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await loyaltyService.getCustomerLoyaltyTransactions(
        customerId,
        options
      );
      
      if (result) {
        setTransactions(result.transactions);
        setTotalCount(result.count);
      } else {
        setTransactions([]);
        setTotalCount(0);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch transactions");
      setTransactions([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  }, [customerId, options]);

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  return {
    transactions,
    totalCount,
    isLoading,
    error,
    refresh: fetchTransactions,
  };
};

// Loyalty Discounts Hook
export const useLoyaltyDiscounts = () => {
  const [discountCalculation, setDiscountCalculation] = useState<LoyaltyDiscountCalculation | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculateDiscounts = useCallback(async (customerId: string, orderTotal: number) => {
    setIsCalculating(true);
    setError(null);

    try {
      const calculation = await loyaltyService.calculateLoyaltyDiscounts(
        customerId,
        orderTotal
      );
      setDiscountCalculation(calculation);
      return calculation;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to calculate discounts";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsCalculating(false);
    }
  }, []);

  const clearDiscounts = useCallback(() => {
    setDiscountCalculation(null);
    setError(null);
  }, []);

  return {
    discountCalculation,
    isCalculating,
    error,
    calculateDiscounts,
    clearDiscounts,
  };
};

// Points Redemption Hook
export const usePointsRedemption = () => {
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const redeemPoints = useCallback(async (
    customerId: string,
    redemptionData: PointsRedemptionRequest
  ): Promise<PointsRedemptionResult | null> => {
    setIsRedeeming(true);
    setError(null);

    try {
      const result = await loyaltyService.redeemLoyaltyPoints(customerId, redemptionData);
      
      // Invalidate customer loyalty cache
      loyaltyService.invalidateCustomerLoyalty(customerId);
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to redeem points";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsRedeeming(false);
    }
  }, []);

  return {
    isRedeeming,
    error,
    redeemPoints,
  };
};

// Loyalty Leaderboard Hook
export const useLoyaltyLeaderboard = (options?: {
  limit?: number;
  tier?: "bronze" | "silver" | "gold" | "platinum";
  orderBy?: "points" | "purchases";
}) => {
  const [leaderboard, setLeaderboard] = useState<LoyaltyLeaderboardEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLeaderboard = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await loyaltyService.getLoyaltyLeaderboard(options);
      setLeaderboard(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch leaderboard");
      setLeaderboard([]);
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  useEffect(() => {
    fetchLeaderboard();
  }, [fetchLeaderboard]);

  return {
    leaderboard,
    isLoading,
    error,
    refresh: fetchLeaderboard,
  };
};

// Loyalty Analytics Hook
export const useLoyaltyAnalytics = (options?: {
  dateFrom?: string;
  dateTo?: string;
  tier?: "bronze" | "silver" | "gold" | "platinum";
}) => {
  const [analytics, setAnalytics] = useState<LoyaltyAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await loyaltyService.getLoyaltyAnalytics(options);
      setAnalytics(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch analytics");
      setAnalytics(null);
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  return {
    analytics,
    isLoading,
    error,
    refresh: fetchAnalytics,
  };
};
