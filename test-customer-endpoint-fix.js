#!/usr/bin/env node

/**
 * Customer Endpoint Fix Test Script
 *
 * This script verifies that the customer endpoint issue has been fixed
 * by checking that checkout.tsx uses the correct getStoreCustomers method.
 */

const fs = require("fs");
const path = require("path");

console.log("🔍 Testing Customer Endpoint Fix...\n");

const tests = [
  {
    name: "Checkout Screen Customer Loading Fix",
    file: "app/checkout.tsx",
    checks: [
      "getStoreCustomers({",
      "search: searchQuery",
      "limit: 50",
      "response.data.customers",
    ],
    shouldNotContain: ["getCustomers(searchQuery)", "response.data.data"],
  },
  {
    name: "Customer List Screen (Reference)",
    file: "app/customer-list.tsx",
    checks: ["getStoreCustomers", "fetchCustomers"],
  },
  {
    name: "Customer Slice (Reference)",
    file: "src/store/slices/customerSlice.ts",
    checks: ["getStoreCustomers({", "response.data?.customers"],
  },
];

let passedTests = 0;
let totalTests = 0;
let issues = [];

tests.forEach((test) => {
  console.log(`📋 Testing: ${test.name}`);

  const filePath = path.join(process.cwd(), test.file);

  if (!fs.existsSync(filePath)) {
    console.log(`   ❌ File not found: ${test.file}`);
    issues.push(`File not found: ${test.file}`);
    return;
  }

  const content = fs.readFileSync(filePath, "utf8");

  // Check for required content
  test.checks.forEach((check) => {
    totalTests++;
    if (content.includes(check)) {
      console.log(`   ✅ Found: ${check}`);
      passedTests++;
    } else {
      console.log(`   ❌ Missing: ${check}`);
      issues.push(`Missing in ${test.file}: ${check}`);
    }
  });

  // Check for content that should NOT be present
  if (test.shouldNotContain) {
    test.shouldNotContain.forEach((check) => {
      totalTests++;
      if (!content.includes(check)) {
        console.log(`   ✅ Correctly removed: ${check}`);
        passedTests++;
      } else {
        console.log(`   ❌ Still contains: ${check}`);
        issues.push(`Still contains in ${test.file}: ${check}`);
      }
    });
  }

  console.log("");
});

// Summary
console.log("📊 Test Summary:");
console.log(`   Passed: ${passedTests}/${totalTests}`);
console.log(
  `   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`
);

if (passedTests === totalTests) {
  console.log("\n🎉 All tests passed! Customer endpoint fix successful.");
  console.log("\n📱 What was fixed:");
  console.log(
    "   ❌ OLD: apiClient.getCustomers(searchQuery) - Wrong endpoint"
  );
  console.log(
    "   ✅ NEW: apiClient.getStoreCustomers({ search: searchQuery }) - Correct endpoint"
  );
  console.log("\n🔧 Technical Details:");
  console.log(
    "   • getCustomers() requires storeId parameter and uses /stores/{storeId}/customers"
  );
  console.log(
    "   • getStoreCustomers() uses /store/customers (correct for POS)"
  );
  console.log("   • Response structure: data.customers (not data.data)");
  console.log("\n📋 Next steps:");
  console.log("   1. Test customer selection in checkout flow");
  console.log("   2. Verify customers appear in the selection modal");
  console.log("   3. Confirm selected customers are properly passed to orders");
} else {
  console.log("\n⚠️  Some tests failed. Issues found:");
  issues.forEach((issue) => {
    console.log(`   • ${issue}`);
  });
  console.log("\n🔧 Troubleshooting:");
  console.log("   1. Check that checkout.tsx uses getStoreCustomers method");
  console.log("   2. Verify the response data structure is correct");
  console.log("   3. Ensure search parameters are properly formatted");
}

console.log("\n📚 API Endpoint Comparison:");
console.log("   🔴 WRONG: /stores/{storeId}/customers (getCustomers)");
console.log("   🟢 CORRECT: /store/customers (getStoreCustomers)");
console.log(
  "\n💡 The POS app should use /store/customers for direct Shopify data access."
);
