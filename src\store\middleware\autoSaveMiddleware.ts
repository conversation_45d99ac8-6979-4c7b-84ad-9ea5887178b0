/**
 * Auto-Save Middleware
 *
 * Enhanced middleware that automatically saves dirty tickets to the backend
 * with intelligent debouncing, offline support, and resume functionality.
 * Integrates with AutoSaveService for advanced features.
 */

import { Middleware } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { autoSaveDirtyTickets } from "../thunks/ticketThunks";
import { autoSaveService } from "@/src/services/AutoSaveService";

// Debounce configuration
const AUTO_SAVE_DELAY = 10000; // 10 seconds - increased to prevent immediate saves
const MAX_AUTO_SAVE_DELAY = 30000; // 30 seconds maximum delay
const NEW_TICKET_GRACE_PERIOD = 15000; // 15 seconds grace period for new tickets

// Track auto-save timers
let autoSaveTimer: NodeJS.Timeout | null = null;
let maxDelayTimer: NodeJS.Timeout | null = null;
let lastAutoSaveTime = 0;

// Actions that trigger auto-save
const AUTO_SAVE_TRIGGER_ACTIONS = [
  "tickets/addItemToTicketWithAutoCreate",
  "tickets/addItemToActiveTicket",
  "tickets/removeItemFromActiveTicket",
  "tickets/updateItemQuantityInActiveTicket",
  "tickets/applyItemDiscountInActiveTicket",
  "tickets/updateItemNotesInActiveTicket",
  "tickets/setActiveTicketCustomer",
  "tickets/setActiveTicketSalesperson",
  "tickets/addDiscountToActiveTicket",
  "tickets/removeDiscountFromActiveTicket",
  "tickets/setActiveTicketNote",
  "tickets/updateTicketName",
  "tickets/addItemToTicket",
  "tickets/removeItemFromTicket",
  "tickets/updateItemQuantity",
  "tickets/updateItemDiscount",
  "tickets/updateTicketCustomer",
  "tickets/updateTicketSalesperson",
  "tickets/updateTicketNote",
  "tickets/applyDiscount",
  "tickets/removeDiscount",
  "tickets/recalculateTotals",
];

// Actions that should force immediate save
const IMMEDIATE_SAVE_ACTIONS = [
  "tickets/setActiveTicket",
  "tickets/deleteTicket",
];

// Actions to ignore for auto-save
const IGNORE_ACTIONS = [
  "tickets/autoSaveTicket/pending",
  "tickets/autoSaveTicket/fulfilled",
  "tickets/autoSaveTicket/rejected",
  "tickets/batchAutoSaveTickets/pending",
  "tickets/batchAutoSaveTickets/fulfilled",
  "tickets/batchAutoSaveTickets/rejected",
  "tickets/saveTicket/pending",
  "tickets/saveTicket/fulfilled",
  "tickets/saveTicket/rejected",
  "tickets/autoSaveDirtyTickets/pending",
  "tickets/autoSaveDirtyTickets/fulfilled",
  "tickets/autoSaveDirtyTickets/rejected",
  "tickets/markTicketAsSaved",
  "tickets/createTicket", // Ignore ticket creation to prevent immediate auto-save
];

export const autoSaveMiddleware: Middleware<{}, RootState> =
  (store) => (next) => (action) => {
    // Skip ignored actions
    if (IGNORE_ACTIONS.some((pattern) => action.type.includes(pattern))) {
      return next(action);
    }

    // Get state before action
    const prevState = store.getState();
    const prevTickets = prevState.tickets.tickets;
    const prevActiveTicketId = prevState.tickets.activeTicketId;

    // Process the action first
    const result = next(action);

    // Get state after action
    const nextState = store.getState();
    const nextTickets = nextState.tickets.tickets;
    const nextActiveTicketId = nextState.tickets.activeTicketId;

    // Only proceed if auto-save is enabled
    if (!nextState.tickets.autoSaveEnabled) {
      return result;
    }

    // Check if this action should trigger auto-save
    const shouldAutoSave = AUTO_SAVE_TRIGGER_ACTIONS.includes(action.type);
    const shouldForceSave = IMMEDIATE_SAVE_ACTIONS.includes(action.type);

    if (shouldAutoSave || shouldForceSave) {
      // Find tickets that have changed
      const changedTicketIds = findChangedTickets(prevTickets, nextTickets);

      if (changedTicketIds.length > 0) {
        console.log(
          `🔄 Auto-save triggered by action: ${
            action.type
          }, changed tickets: ${changedTicketIds.join(", ")}`
        );
      }

      // Use new auto-save service for individual tickets
      changedTicketIds.forEach((ticketId) => {
        try {
          if (
            autoSaveService &&
            typeof autoSaveService.scheduleAutoSave === "function"
          ) {
            autoSaveService.scheduleAutoSave(ticketId, shouldForceSave);
          } else {
            // Fallback to legacy auto-save
            scheduleAutoSave(store);
          }
        } catch (error) {
          console.error(
            `Failed to schedule auto-save for ticket ${ticketId}:`,
            error
          );
          // Fallback to legacy auto-save
          scheduleAutoSave(store);
        }
      });

      // If no specific tickets changed, use legacy auto-save
      if (changedTicketIds.length === 0 && shouldAutoSave) {
        scheduleAutoSave(store);
      }
    }

    // Handle ticket switching
    if (action.type === "tickets/setActiveTicket") {
      if (prevActiveTicketId && prevActiveTicketId !== nextActiveTicketId) {
        // Force save the previously active ticket
        try {
          if (
            autoSaveService &&
            typeof autoSaveService.scheduleAutoSave === "function"
          ) {
            autoSaveService.scheduleAutoSave(prevActiveTicketId, true);
          }
        } catch (error) {
          console.error(
            `Failed to auto-save previous ticket ${prevActiveTicketId}:`,
            error
          );
        }
      }
    }

    // Handle auto-save enable/disable
    if (action.type === "tickets/setAutoSave") {
      if (!action.payload) {
        // Auto-save disabled, clear timers and service
        clearAutoSaveTimers();
        try {
          if (autoSaveService && typeof autoSaveService.clear === "function") {
            autoSaveService.clear();
          }
        } catch (error) {
          console.error("Failed to clear auto-save service:", error);
        }
      }
    }

    // Handle app state changes
    if (action.type === "app/setAppState") {
      if (action.payload === "background" || action.payload === "inactive") {
        // App going to background, force save all dirty tickets
        try {
          if (
            autoSaveService &&
            typeof autoSaveService.saveAllDirtyTickets === "function"
          ) {
            autoSaveService.saveAllDirtyTickets();
          } else {
            // Fallback to legacy auto-save
            performAutoSave(store);
          }
        } catch (error) {
          console.error(
            "Failed to save dirty tickets on app background:",
            error
          );
          // Fallback to legacy auto-save
          performAutoSave(store);
        }
      }
    }

    return result;
  };

// Helper function to find tickets that have changed
const findChangedTickets = (
  prevTickets: any[],
  nextTickets: any[]
): string[] => {
  const changedIds: string[] = [];

  // Check for new tickets
  nextTickets.forEach((ticket) => {
    const prevTicket = prevTickets.find((t) => t.id === ticket.id);

    if (!prevTicket) {
      // New ticket - apply grace period and item checks
      const ticketAge = Date.now() - new Date(ticket.createdAt).getTime();
      const hasItems = ticket.items && ticket.items.length > 0;
      const isInGracePeriod = ticketAge < NEW_TICKET_GRACE_PERIOD;

      // Only auto-save new tickets if:
      // 1. They have items AND
      // 2. They're past the grace period OR have multiple items (user is actively working)
      if (
        (ticket.isDirty || ticket.isLocal) &&
        hasItems &&
        (!isInGracePeriod || ticket.items.length > 1)
      ) {
        console.log(
          `🔄 Auto-save scheduled for new ticket ${
            ticket.id
          } (age: ${Math.round(ticketAge / 1000)}s, items: ${
            ticket.items.length
          })`
        );
        changedIds.push(ticket.id);
      } else if (hasItems && isInGracePeriod) {
        console.log(
          `⏳ Skipping auto-save for new ticket ${
            ticket.id
          } (in grace period: ${Math.round(
            (NEW_TICKET_GRACE_PERIOD - ticketAge) / 1000
          )}s remaining)`
        );
      }
      return;
    }

    // Skip if ticket is not dirty and not local (already saved)
    if (!ticket.isDirty && !ticket.isLocal) {
      return;
    }

    // Check if ticket has changed (simple comparison)
    if (ticket.isDirty && !prevTicket.isDirty) {
      changedIds.push(ticket.id);
      return;
    }

    // Check key fields for changes
    const keyFields = [
      "name",
      "items",
      "customer",
      "salesperson",
      "note",
      "discounts",
      "total",
    ];
    for (const field of keyFields) {
      if (JSON.stringify(prevTicket[field]) !== JSON.stringify(ticket[field])) {
        changedIds.push(ticket.id);
        break;
      }
    }
  });

  return changedIds;
};

const scheduleAutoSave = (store: any) => {
  const now = Date.now();

  // Clear existing timers
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer);
  }

  // Set up debounced auto-save
  autoSaveTimer = setTimeout(() => {
    performAutoSave(store);
  }, AUTO_SAVE_DELAY);

  // Set up maximum delay timer if not already set
  if (!maxDelayTimer && now - lastAutoSaveTime > 0) {
    maxDelayTimer = setTimeout(() => {
      performAutoSave(store);
    }, MAX_AUTO_SAVE_DELAY);
  }
};

const performAutoSave = async (store: any) => {
  try {
    const state = store.getState();
    const dirtyTickets = state.tickets.tickets.filter((t: any) => t.isDirty);

    if (dirtyTickets.length > 0) {
      console.log(`Auto-saving ${dirtyTickets.length} dirty tickets...`);
      await store.dispatch(autoSaveDirtyTickets());
      lastAutoSaveTime = Date.now();
    }
  } catch (error) {
    console.error("Auto-save failed:", error);
  } finally {
    clearAutoSaveTimers();
  }
};

const clearAutoSaveTimers = () => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = null;
  }

  if (maxDelayTimer) {
    clearTimeout(maxDelayTimer);
    maxDelayTimer = null;
  }
};

// Export utility functions for manual control
export const triggerManualAutoSave = (store: any) => {
  clearAutoSaveTimers();
  performAutoSave(store);
};

export const clearAutoSave = () => {
  clearAutoSaveTimers();
};
