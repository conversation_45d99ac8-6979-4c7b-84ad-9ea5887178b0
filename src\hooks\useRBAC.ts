import { useMemo } from "react";
import {
  canAccessF<PERSON>ure,
  canAccessScreen,
  canAssign<PERSON><PERSON>,
  getAssignableR<PERSON><PERSON>,
  getRoleLevel,
  getRolePermissions,
  hasAllPermissions,
  hasAnyPermission,
  hasAnyRole,
  hasPermission,
  hasRole,
  hasRoleLevel,
  UserRole,
} from "../config/rbac";
import { useSession } from "../contexts/AuthContext";

/**
 * useRBAC Hook
 *
 * A comprehensive hook that provides all RBAC functionality in a convenient API.
 * This hook integrates with the AuthContext and RBAC configuration to provide
 * easy access to permission and role checking functions.
 *
 * @returns Object with user info and RBAC checking functions
 */
export const useRBAC = () => {
  const { user, isPosAuthenticated } = useSession();

  // Memoize user data for performance
  const userData = useMemo(() => {
    if (!isPosAuthenticated || !user) {
      return {
        isAuthenticated: false,
        user: null,
        role: null,
        permissions: [],
        roleLevel: 0,
      };
    }

    return {
      isAuthenticated: true,
      user,
      role: user.role as UserRole,
      permissions: user.permissions || [],
      roleLevel: getRoleLevel(user.role as UserRole),
    };
  }, [user, isPosAuthenticated]);

  // Memoize permission checking functions
  const permissionCheckers = useMemo(() => {
    const userPermissions = userData.permissions;

    return {
      /**
       * Check if user has a specific permission
       */
      hasPermission: (permission: string): boolean => {
        if (!userData.isAuthenticated) return false;
        return hasPermission(userPermissions, permission);
      },

      /**
       * Check if user has any of the specified permissions
       */
      hasAnyPermission: (permissions: string[]): boolean => {
        if (!userData.isAuthenticated) return false;
        return hasAnyPermission(userPermissions, permissions);
      },

      /**
       * Check if user has all of the specified permissions
       */
      hasAllPermissions: (permissions: string[]): boolean => {
        if (!userData.isAuthenticated) return false;
        return hasAllPermissions(userPermissions, permissions);
      },

      /**
       * Get all permissions for the current user's role
       */
      getRolePermissions: (): string[] => {
        if (!userData.isAuthenticated || !userData.role) return [];
        return getRolePermissions(userData.role);
      },
    };
  }, [userData]);

  // Memoize role checking functions
  const roleCheckers = useMemo(() => {
    const userRole = userData.role;

    return {
      /**
       * Check if user has a specific role
       */
      hasRole: (role: UserRole): boolean => {
        if (!userData.isAuthenticated || !userRole) return false;
        return hasRole(userRole, role);
      },

      /**
       * Check if user has any of the specified roles
       */
      hasAnyRole: (roles: UserRole[]): boolean => {
        if (!userData.isAuthenticated || !userRole) return false;
        return hasAnyRole(userRole, roles);
      },

      /**
       * Check if user has minimum role level
       */
      hasMinRoleLevel: (minRole: UserRole): boolean => {
        if (!userData.isAuthenticated || !userRole) return false;
        return hasRoleLevel(userRole, minRole);
      },

      /**
       * Check if user is a cashier
       */
      isCashier: (): boolean => {
        return userData.role === "cashier";
      },

      /**
       * Check if user is a manager
       */
      isManager: (): boolean => {
        return userData.role === "manager";
      },

      /**
       * Check if user is a super admin
       */
      isSuperAdmin: (): boolean => {
        return userData.role === "super_admin";
      },

      /**
       * Check if user is manager or above
       */
      isManagerOrAbove: (): boolean => {
        if (!userData.isAuthenticated || !userRole) return false;
        return hasRoleLevel(userRole, "manager");
      },

      /**
       * Check if user is super admin or above
       */
      isSuperAdminOrAbove: (): boolean => {
        if (!userData.isAuthenticated || !userRole) return false;
        return hasRoleLevel(userRole, "super_admin");
      },
    };
  }, [userData]);

  // Memoize access checking functions
  const accessCheckers = useMemo(() => {
    const userRole = userData.role;
    const userPermissions = userData.permissions;

    return {
      /**
       * Check if user can access a specific screen
       */
      canAccessScreen: (screenName: string): boolean => {
        if (!userData.isAuthenticated || !userRole) return false;
        return canAccessScreen(userRole, userPermissions, screenName);
      },

      /**
       * Check if user can access a specific feature
       */
      canAccessFeature: (featureId: string): boolean => {
        if (!userData.isAuthenticated || !userRole) return false;
        return canAccessFeature(userRole, userPermissions, featureId);
      },
    };
  }, [userData]);

  // Memoize common permission combinations
  const commonPermissions = useMemo(
    () => ({
      // Staff Management
      canViewStaff: permissionCheckers.hasPermission("view_staff"),
      canManageStaff: permissionCheckers.hasPermission("manage_staff"),
      canViewStaffPerformance: permissionCheckers.hasPermission(
        "view_staff_performance"
      ),

      // Customer Management
      canReadCustomers: permissionCheckers.hasPermission("read_customers"),
      canManageCustomers: permissionCheckers.hasPermission("manage_customers"),

      // Order Management
      canCreateOrders: permissionCheckers.hasPermission("create_orders"),
      canViewOrders: permissionCheckers.hasPermission("view_orders"),
      canManageOrders: permissionCheckers.hasPermission("manage_orders"),

      // Product Management
      canReadProducts: permissionCheckers.hasPermission("read_products"),
      canManageProducts: permissionCheckers.hasPermission("manage_products"),

      // Inventory Management
      canViewInventory: permissionCheckers.hasPermission("view_inventory"),
      canManageInventory: permissionCheckers.hasPermission("manage_inventory"),

      // Analytics
      canViewAnalytics: permissionCheckers.hasPermission("view_analytics"),

      // Payment Processing
      canProcessPayments: permissionCheckers.hasPermission("process_payments"),

      // Discount Management
      canApplyDiscounts: permissionCheckers.hasPermission("apply_discounts"),
      canManageDiscounts: permissionCheckers.hasPermission("manage_discounts"),

      // System Administration
      canManageSystem: permissionCheckers.hasPermission("manage_system"),
      canManageRoles: permissionCheckers.hasPermission("manage_roles"),
      canManagePermissions:
        permissionCheckers.hasPermission("manage_permissions"),
      canViewAuditLogs: permissionCheckers.hasPermission("view_audit_logs"),
      canManageIntegrations: permissionCheckers.hasPermission(
        "manage_integrations"
      ),
    }),
    [permissionCheckers]
  );

  return {
    // User data
    ...userData,

    // Permission checking functions
    ...permissionCheckers,

    // Role checking functions
    ...roleCheckers,

    // Access checking functions
    ...accessCheckers,

    // Common permission combinations
    ...commonPermissions,

    // Role assignment functions
    getAssignableRoles: (): UserRole[] => {
      if (!userData.isAuthenticated || !userData.role) return [];
      return getAssignableRoles(userData.role);
    },

    canAssignRole: (targetRole: UserRole): boolean => {
      if (!userData.isAuthenticated || !userData.role) return false;
      return canAssignRole(userData.role, targetRole);
    },
  };
};

/**
 * usePermission Hook
 *
 * A simple hook for checking a single permission.
 *
 * @param permission - The permission to check
 * @returns boolean indicating if user has the permission
 */
export const usePermission = (permission: string): boolean => {
  const { hasPermission } = useRBAC();
  return hasPermission(permission);
};

/**
 * useRole Hook
 *
 * A simple hook for checking a single role.
 *
 * @param role - The role to check
 * @returns boolean indicating if user has the role
 */
export const useRole = (role: UserRole): boolean => {
  const { hasRole } = useRBAC();
  return hasRole(role);
};

/**
 * useScreenAccess Hook
 *
 * A simple hook for checking screen access.
 *
 * @param screenName - The screen name to check access for
 * @returns boolean indicating if user can access the screen
 */
export const useScreenAccess = (screenName: string): boolean => {
  const { canAccessScreen } = useRBAC();
  return canAccessScreen(screenName);
};

/**
 * useFeatureAccess Hook
 *
 * A simple hook for checking feature access.
 *
 * @param featureId - The feature ID to check access for
 * @returns boolean indicating if user can access the feature
 */
export const useFeatureAccess = (featureId: string): boolean => {
  const { canAccessFeature } = useRBAC();
  return canAccessFeature(featureId);
};
