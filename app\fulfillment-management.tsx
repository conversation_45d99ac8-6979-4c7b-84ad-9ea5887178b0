/**
 * Fulfillment Management Dashboard Screen
 *
 * Comprehensive fulfillment management interface for staff and managers.
 * Provides order fulfillment tracking, delivery management, shipping fee calculation,
 * and fulfillment analytics with proper RBAC implementation.
 */

import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernCard } from "@/components/ui/ModernCard";
import { PermissionGate } from "@/src/components/rbac";
import { ShippingRatesManager } from "@/src/components/fulfillment/ShippingRatesManager";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  useFulfillmentsByOrder,
  useShippingRates,
  useFulfillmentStats,
} from "@/src/hooks/useFulfillment";
import { useRBAC } from "@/src/hooks/useRBAC";
import { createStyleUtils } from "@/src/utils/themeUtils";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

interface FulfillmentStats {
  totalFulfillments: number;
  pendingFulfillments: number;
  shippedFulfillments: number;
  deliveredFulfillments: number;
  averageDeliveryTime: string;
  totalShippingFees: number;
}

export default function FulfillmentManagementScreen() {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const router = useRouter();
  const { hasPermission } = useRBAC();
  const { tab } = useLocalSearchParams<{ tab?: string }>();

  // Check permissions
  const canViewFulfillments = hasPermission("view_fulfillments");
  const canManageFulfillments = hasPermission("manage_fulfillments");
  const canManageShippingRates = hasPermission("manage_shipping_rates");
  const canViewReports = hasPermission("view_fulfillment_reports");

  // State management
  const [activeTab, setActiveTab] = useState<
    "overview" | "fulfillments" | "shipping" | "reports"
  >("overview");

  // Handle tab parameter from URL
  useEffect(() => {
    if (
      tab &&
      ["overview", "fulfillments", "shipping", "reports"].includes(tab)
    ) {
      setActiveTab(tab as "overview" | "fulfillments" | "shipping" | "reports");
    }
  }, [tab]);

  // React Query hooks
  const { data: shippingRates, isLoading: ratesLoading } = useShippingRates();
  const { data: fulfillmentStats, isLoading: statsLoading } =
    useFulfillmentStats();

  // Use real stats data from API with fallback
  const stats: FulfillmentStats = fulfillmentStats || {
    totalFulfillments: 0,
    pendingFulfillments: 0,
    shippedFulfillments: 0,
    deliveredFulfillments: 0,
    averageDeliveryTime: "0 days",
    totalShippingFees: 0,
  };

  // Create theme-aware styles
  const styles = createStyles(theme);

  if (!canViewFulfillments) {
    return (
      <ScreenWrapper title="Fulfillment Management" showBackButton>
        <ThemedView style={styles.container}>
          <View style={styles.errorContainer}>
            <IconSymbol
              name="exclamationmark.triangle"
              size={48}
              color={theme.colors.error}
            />
            <ThemedText variant="h3" style={utils.mt("md")}>
              Access Denied
            </ThemedText>
            <ThemedText variant="body" color="secondary" style={utils.mt("sm")}>
              You don't have permission to view fulfillment management.
            </ThemedText>
          </View>
        </ThemedView>
      </ScreenWrapper>
    );
  }

  // Dynamic tab styles using theme
  const getTabStyle = (isActive: boolean) => ({
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    backgroundColor: isActive ? theme.colors.primary : theme.colors.surface,
    alignItems: "center" as const,
    justifyContent: "center" as const,
    minWidth: 100,
  });

  const getTabTextStyle = (isActive: boolean) => ({
    fontSize: 14,
    color: isActive ? "#FFFFFF" : theme.colors.textSecondary,
    fontWeight: isActive ? ("600" as const) : ("500" as const),
  });

  const renderTabBar = () => (
    <View style={styles.tabBarContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabBar}
      >
        <TouchableOpacity
          style={getTabStyle(activeTab === "overview")}
          onPress={() => setActiveTab("overview")}
        >
          <ThemedText
            variant="bodyMedium"
            style={getTabTextStyle(activeTab === "overview")}
          >
            Overview
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={getTabStyle(activeTab === "fulfillments")}
          onPress={() => setActiveTab("fulfillments")}
        >
          <ThemedText
            variant="bodyMedium"
            style={getTabTextStyle(activeTab === "fulfillments")}
          >
            Fulfillments
          </ThemedText>
        </TouchableOpacity>

        {canManageShippingRates && (
          <TouchableOpacity
            style={getTabStyle(activeTab === "shipping")}
            onPress={() => setActiveTab("shipping")}
          >
            <ThemedText
              variant="bodyMedium"
              style={getTabTextStyle(activeTab === "shipping")}
            >
              Shipping Rates
            </ThemedText>
          </TouchableOpacity>
        )}

        {canViewReports && (
          <TouchableOpacity
            style={getTabStyle(activeTab === "reports")}
            onPress={() => setActiveTab("reports")}
          >
            <ThemedText
              variant="bodyMedium"
              style={getTabTextStyle(activeTab === "reports")}
            >
              Reports
            </ThemedText>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );

  const renderOverview = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Key Metrics */}
      <View style={styles.metricsGrid}>
        <ModernCard style={styles.metricCard}>
          <IconSymbol
            name="shippingbox.fill"
            size={24}
            color={theme.colors.primary}
          />
          <ThemedText variant="h2" style={styles.metricValue}>
            {stats.totalFulfillments}
          </ThemedText>
          <ThemedText variant="small" color="secondary">
            Total Fulfillments
          </ThemedText>
        </ModernCard>

        <ModernCard style={styles.metricCard}>
          <IconSymbol
            name="clock.fill"
            size={24}
            color={theme.colors.warning}
          />
          <ThemedText variant="h2" style={styles.metricValue}>
            {stats.pendingFulfillments}
          </ThemedText>
          <ThemedText variant="small" color="secondary">
            Pending
          </ThemedText>
        </ModernCard>

        <ModernCard style={styles.metricCard}>
          <IconSymbol
            name="truck.box.fill"
            size={24}
            color={theme.colors.info}
          />
          <ThemedText variant="h2" style={styles.metricValue}>
            {stats.shippedFulfillments}
          </ThemedText>
          <ThemedText variant="small" color="secondary">
            Shipped
          </ThemedText>
        </ModernCard>

        <ModernCard style={styles.metricCard}>
          <IconSymbol
            name="checkmark.circle.fill"
            size={24}
            color={theme.colors.success}
          />
          <ThemedText variant="h2" style={styles.metricValue}>
            {stats.deliveredFulfillments}
          </ThemedText>
          <ThemedText variant="small" color="secondary">
            Delivered
          </ThemedText>
        </ModernCard>
      </View>

      {/* Quick Actions */}
      <ModernCard style={utils.mt("lg")}>
        <ThemedText variant="h3" style={utils.mb("md")}>
          Quick Actions
        </ThemedText>
        <View style={styles.actionsGrid}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push("/fulfillment-details")}
          >
            <IconSymbol
              name="plus.circle.fill"
              size={20}
              color={theme.colors.primary}
            />
            <ThemedText variant="small" style={utils.ml("xs")}>
              Create Fulfillment
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setActiveTab("fulfillments")}
          >
            <IconSymbol
              name="list.bullet"
              size={20}
              color={theme.colors.primary}
            />
            <ThemedText variant="small" style={utils.ml("xs")}>
              View All
            </ThemedText>
          </TouchableOpacity>

          <PermissionGate requiredPermissions={["manage_shipping_rates"]}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setActiveTab("shipping")}
            >
              <IconSymbol name="gear" size={20} color={theme.colors.primary} />
              <ThemedText variant="small" style={utils.ml("xs")}>
                Manage Rates
              </ThemedText>
            </TouchableOpacity>
          </PermissionGate>
        </View>
      </ModernCard>

      {/* Performance Summary */}
      <ModernCard style={utils.mt("lg")}>
        <ThemedText variant="h3" style={utils.mb("md")}>
          Performance Summary
        </ThemedText>
        <View style={styles.summaryRow}>
          <ThemedText variant="body">Average Delivery Time:</ThemedText>
          <ThemedText variant="bodyMedium" color="primary">
            {stats.averageDeliveryTime}
          </ThemedText>
        </View>
        <View style={styles.summaryRow}>
          <ThemedText variant="body">Total Shipping Revenue:</ThemedText>
          <ThemedText variant="bodyMedium" color="success">
            KES {stats.totalShippingFees.toLocaleString()}
          </ThemedText>
        </View>
      </ModernCard>
    </ScrollView>
  );

  const renderFulfillments = () => {
    // Note: FulfillmentList will handle its own data fetching
    // This is a placeholder for when we have a general fulfillments endpoint
    return (
      <View style={styles.tabContent}>
        <ThemedText variant="body" style={styles.comingSoon}>
          General fulfillments list coming soon. Use order-specific fulfillments
          for now.
        </ThemedText>
      </View>
    );
  };

  const renderShippingRates = () => (
    <View style={styles.tabContent}>
      <ShippingRatesManager />
    </View>
  );

  const renderReports = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <ThemedText variant="body" style={styles.comingSoon}>
        Fulfillment Reports Coming Soon
      </ThemedText>
    </ScrollView>
  );

  return (
    <ScreenWrapper title="Fulfillment Management" showBackButton>
      <ThemedView style={styles.container}>
        {/* Tab Bar */}
        {renderTabBar()}

        {/* Tab Content */}
        {activeTab === "overview" && renderOverview()}
        {activeTab === "fulfillments" && renderFulfillments()}
        {activeTab === "shipping" && (
          <PermissionGate requiredPermissions={["manage_shipping_rates"]}>
            {renderShippingRates()}
          </PermissionGate>
        )}
        {activeTab === "reports" && (
          <PermissionGate requiredPermissions={["view_fulfillment_reports"]}>
            {renderReports()}
          </PermissionGate>
        )}
      </ThemedView>
    </ScreenWrapper>
  );
}

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    tabBarContainer: {
      backgroundColor: "transparent",
      paddingHorizontal: 16,
      paddingTop: 16,
      paddingBottom: 8,
    },
    tabBar: {
      flexDirection: "row",
      gap: 12,
      paddingHorizontal: 4,
    },
    tabContent: {
      flex: 1,
      padding: 16,
    },
    comingSoon: {
      textAlign: "center",
      fontStyle: "italic",
      padding: 32,
    },
    metricsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 12,
    },
    metricCard: {
      flex: 1,
      minWidth: 150,
      padding: 16,
      alignItems: "center",
      gap: 8,
    },
    metricValue: {
      fontSize: 24,
      fontWeight: "bold",
    },
    actionsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 12,
    },
    actionButton: {
      flexDirection: "row",
      alignItems: "center",
      padding: 12,
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    summaryRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 8,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    rateHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 8,
    },
    comingSoon: {
      textAlign: "center",
      padding: 32,
      fontStyle: "italic",
    },
  });
