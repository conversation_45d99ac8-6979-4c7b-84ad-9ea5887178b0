import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useScreenNavigation } from "@/src/hooks/useScreenNavigation";
import { useUnifiedCart } from "@/src/hooks/useUnifiedCart";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

// Import types and components
import { formatCurrency } from "@/src/utils/currencyUtils";
import { ConfirmationModal } from "@/components/ui/ConfirmationModal";

const CartScreen: React.FC = () => {
  const router = useRouter();
  const { isPosAuthenticated: isAuthenticated } = useSession();
  const cart = useUnifiedCart();
  const theme = useTheme();
  const styles = createStyles(theme);

  // Modal states for web-compatible alerts
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const [showInsufficientStock, setShowInsufficientStock] = useState(false);
  const [showEmptyCartError, setShowEmptyCartError] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string>("");
  const [stockErrorMessage, setStockErrorMessage] = useState("");

  // Use enhanced navigation hook
  useScreenNavigation({
    title: "Cart",
    forceTitle: true,
  });

  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const warningColor = useThemeColor({}, "warning");
  const errorColor = useThemeColor({}, "error");
  const errorLightColor = useThemeColor({}, "errorLight");
  const borderColor = useThemeColor({}, "border");
  const surfaceColor = useThemeColor({}, "surface");

  const handleUpdateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setSelectedItemId(id);
      setShowRemoveConfirm(true);
    } else {
      // Find the item to check stock
      const item = cart.items.find((item) => item.variantId === id);
      if (item && newQuantity > item.inventoryQuantity) {
        setStockErrorMessage(
          `Only ${item.inventoryQuantity} units available in stock.`
        );
        setShowInsufficientStock(true);
        return;
      }
      cart.updateQuantity(id, newQuantity);
    }
  };

  const handleRemoveItem = (id: string) => {
    setSelectedItemId(id);
    setShowRemoveConfirm(true);
  };

  const confirmRemoveItem = () => {
    if (selectedItemId) {
      cart.removeItem(selectedItemId);
      setSelectedItemId("");
    }
    setShowRemoveConfirm(false);
  };

  const handleClearCart = () => {
    setShowClearConfirm(true);
  };

  const confirmClearCart = () => {
    cart.clearCart();
    setShowClearConfirm(false);
  };

  const handleCheckout = () => {
    if (cart.items.length === 0) {
      setShowEmptyCartError(true);
      return;
    }
    // Navigate directly to checkout page
    router.push("/checkout");
  };

  const renderCartItem = ({ item }: { item: any }) => {
    const isLowStock = item.inventoryQuantity <= 5;
    const isOutOfStock = item.inventoryQuantity <= 0;
    const canIncrement = item.quantity < item.inventoryQuantity;

    return (
      <ModernCard style={styles.cartItem} variant="outlined">
        <View style={styles.itemContent}>
          {item.image && (
            <Image
              source={{ uri: item.image }}
              style={styles.itemImage}
              resizeMode="cover"
            />
          )}

          <View style={styles.itemDetails}>
            <Text
              style={[styles.itemTitle, { color: textColor }]}
              numberOfLines={2}
            >
              {item.title}
            </Text>

            {item.variant && item.variant !== "Default Title" && (
              <Text style={[styles.itemVariant, { color: textSecondary }]}>
                {item.variant}
              </Text>
            )}

            <Text style={[styles.itemPrice, { color: primaryColor }]}>
              KSh {(parseFloat(item.price) || 0).toFixed(2)}
            </Text>

            {item.sku && (
              <Text style={[styles.itemSku, { color: textSecondary }]}>
                SKU: {item.sku}
              </Text>
            )}

            {/* Stock indicator */}
            {isLowStock && !isOutOfStock && (
              <Text style={[styles.stockWarning, { color: warningColor }]}>
                Only {item.inventoryQuantity} left in stock
              </Text>
            )}
            {isOutOfStock && (
              <Text style={[styles.stockWarning, { color: errorColor }]}>
                Out of stock
              </Text>
            )}
          </View>

          <View style={styles.itemActions}>
            <View style={styles.quantityControls}>
              <TouchableOpacity
                style={[styles.quantityButton, { borderColor: borderColor }]}
                onPress={() =>
                  handleUpdateQuantity(item.variantId, item.quantity - 1)
                }
              >
                <IconSymbol name="minus" size={16} color={textColor} />
              </TouchableOpacity>

              <Text style={[styles.quantityText, { color: textColor }]}>
                {item.quantity}
              </Text>

              <TouchableOpacity
                style={[
                  styles.quantityButton,
                  {
                    borderColor: borderColor,
                    opacity: canIncrement ? 1 : 0.5,
                  },
                ]}
                onPress={() =>
                  handleUpdateQuantity(item.variantId, item.quantity + 1)
                }
                disabled={!canIncrement}
              >
                <IconSymbol
                  name="plus"
                  size={16}
                  color={canIncrement ? textColor : textSecondary}
                />
              </TouchableOpacity>
            </View>
            <Text style={[styles.itemTotal, { color: textColor }]}>
              {formatCurrency((parseFloat(item.price) || 0) * item.quantity)}
            </Text>
          </View>
        </View>

        {/* Remove button positioned at bottom-right */}
        <TouchableOpacity
          style={[styles.removeButton, { backgroundColor: errorLightColor }]}
          onPress={() => handleRemoveItem(item.variantId)}
        >
          <IconSymbol name="trash" size={20} color={errorColor} />
        </TouchableOpacity>
      </ModernCard>
    );
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Cart Header */}
      <View style={[styles.header, { backgroundColor: surfaceColor }]}>
        <View>
          <Text style={[styles.headerTitle, { color: textColor }]}>
            Your Cart
          </Text>
          <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
            {cart.itemCount} {cart.itemCount === 1 ? "item" : "items"}
          </Text>
        </View>

        {cart.items.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClearCart}
          >
            <Text style={[styles.clearButtonText, { color: errorColor }]}>
              Clear All
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Cart Items */}
      {cart.items.length === 0 ? (
        <View style={styles.emptyContainer}>
          <IconSymbol name="cart.fill" size={64} color={textSecondary} />
          <Text style={[styles.emptyTitle, { color: textColor }]}>
            Your cart is empty
          </Text>
          <Text style={[styles.emptySubtitle, { color: textSecondary }]}>
            Add some products to get started
          </Text>
          <ModernButton
            title="Browse Products"
            onPress={() => router.push("/(tabs)/products")}
            style={styles.browseButton}
          />
        </View>
      ) : (
        <>
          <FlatList
            data={cart.items}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.variantId}
            style={styles.cartList}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />

          {/* Cart Summary */}
          <View style={[styles.summary, { backgroundColor: surfaceColor }]}>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: textSecondary }]}>
                Subtotal ({cart.itemCount} items)
              </Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>
                {formatCurrency(cart.total)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.totalLabel, { color: textColor }]}>
                Total
              </Text>
              <Text style={[styles.totalValue, { color: primaryColor }]}>
                {formatCurrency(cart.total)}
              </Text>
            </View>

            <ModernButton
              title="Proceed to Checkout"
              onPress={handleCheckout}
              style={styles.checkoutButton}
              size="lg"
            />
          </View>
        </>
      )}

      {/* Web-Compatible Modals */}
      <ConfirmationModal
        visible={showClearConfirm}
        title="Clear Cart"
        message="Are you sure you want to remove all items from cart?"
        confirmText="Clear All"
        cancelText="Cancel"
        onConfirm={confirmClearCart}
        onCancel={() => setShowClearConfirm(false)}
        confirmStyle="destructive"
      />

      <ConfirmationModal
        visible={showRemoveConfirm}
        title="Remove Item"
        message="Are you sure you want to remove this item from cart?"
        confirmText="Remove"
        cancelText="Cancel"
        onConfirm={confirmRemoveItem}
        onCancel={() => setShowRemoveConfirm(false)}
        confirmStyle="destructive"
      />

      <ConfirmationModal
        visible={showInsufficientStock}
        title="Insufficient Stock"
        message={stockErrorMessage}
        confirmText="OK"
        onConfirm={() => setShowInsufficientStock(false)}
        showCancel={false}
      />

      <ConfirmationModal
        visible={showEmptyCartError}
        title="Error"
        message="Your cart is empty"
        confirmText="OK"
        onConfirm={() => setShowEmptyCartError(false)}
        showCancel={false}
      />
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      // Add layout stability constraints
      minHeight: 0,
      maxHeight: "100%",
      overflow: "hidden",
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
      marginBottom: 4,
    },
    headerSubtitle: {
      ...theme.typography.caption,
      opacity: 0.7,
    },
    clearButton: {
      padding: theme.spacing.sm,
    },
    clearButtonText: {
      ...theme.typography.bodyMedium,
    },
    cartList: {
      flex: 1,
      padding: theme.spacing.md,
      // Prevent list from expanding beyond container
      minHeight: 0,
      maxHeight: "100%",
    },
    listContent: {
      paddingBottom: theme.spacing.lg,
    },
    cartItem: {
      marginBottom: theme.spacing.md,
      position: "relative",
    },
    itemContent: {
      flexDirection: "row",
      alignItems: "flex-start",
      paddingBottom: theme.spacing.lg, // Add space for remove button
    },
    itemImage: {
      width: 60,
      height: 60,
      borderRadius: theme.borderRadius.sm,
      marginRight: theme.spacing.md,
    },
    itemDetails: {
      flex: 1,
      marginRight: theme.spacing.md,
    },
    itemTitle: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
    },
    itemVariant: {
      ...theme.typography.caption,
      opacity: 0.7,
      marginBottom: 4,
    },
    itemPrice: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
    },
    itemSku: {
      ...theme.typography.body,
    },
    stockWarning: {
      ...theme.typography.caption,
      fontWeight: "500",
      marginTop: 4,
    },
    itemActions: {
      alignItems: "center",
      minWidth: 80,
    },
    quantityControls: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    quantityButton: {
      width: 28,
      height: 28,
      borderWidth: 1,
      borderRadius: 14,
      alignItems: "center",
      justifyContent: "center",
    },
    quantityText: {
      ...theme.typography.bodyMedium,
      marginHorizontal: theme.spacing.md,
      minWidth: 20,
      textAlign: "center",
    },
    itemTotal: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.sm,
    },
    removeButton: {
      position: "absolute",
      bottom: theme.spacing.sm,
      right: theme.spacing.sm,
      width: 36,
      height: 36,
      borderRadius: 18,
      alignItems: "center",
      justifyContent: "center",
      elevation: 2,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
    },
    summary: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    summaryRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    summaryLabel: {
      ...theme.typography.body,
    },
    summaryValue: {
      ...theme.typography.bodyMedium,
    },
    totalLabel: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
    },
    totalValue: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.h2.fontSize,
      fontWeight: "700",
    },
    checkoutButton: {
      marginTop: theme.spacing.md,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    emptyTitle: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    emptySubtitle: {
      ...theme.typography.body,
      textAlign: "center",
      marginBottom: theme.spacing.lg,
    },
    browseButton: {
      marginTop: theme.spacing.md,
    },
  });

export default CartScreen;
