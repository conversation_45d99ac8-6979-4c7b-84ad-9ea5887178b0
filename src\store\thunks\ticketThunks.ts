/**
 * Ticket Async Thunks
 *
 * Handles asynchronous operations for ticket management including
 * saving, loading, syncing, and auto-save functionality.
 */

import { createAsyncThunk } from "@reduxjs/toolkit";
import { API_CONFIG } from "../../constants/Api";
import { RootState } from "../index";
import { Ticket } from "../slices/ticketSlice";
import { CrossPlatformStorage } from "../../utils/storage";

// API base URL - uses centralized configuration
const API_BASE_URL = API_CONFIG.baseURL;

// Types for API responses - using flexible types to handle nested response structures
interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// Helper function to get auth token from secure storage
const getAuthToken = async (): Promise<string | null> => {
  try {
    const token = await CrossPlatformStorage.getItemAsync("session_token");
    return token;
  } catch (error) {
    console.error("Failed to get auth token:", error);
    return null;
  }
};

// Helper function to make authenticated API calls
const makeAuthenticatedRequest = async (
  url: string,
  options: RequestInit = {},
  token: string
): Promise<Response> => {
  return fetch(url, {
    ...options,
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
      ...options.headers,
    },
  });
};

// Save ticket to backend
export const saveTicket = createAsyncThunk<
  Ticket,
  { ticket: Ticket },
  { state: RootState; rejectValue: string }
>("tickets/saveTicket", async ({ ticket }, { rejectWithValue }) => {
  try {
    const token = await getAuthToken();

    if (!token) {
      return rejectWithValue("Authentication required");
    }

    const url = ticket.isLocal
      ? `${API_BASE_URL}/tickets`
      : `${API_BASE_URL}/tickets/${ticket.id}`;

    const method = ticket.isLocal ? "POST" : "PUT";

    const response = await makeAuthenticatedRequest(
      url,
      {
        method,
        body: JSON.stringify({
          name: ticket.name,
          items: ticket.items,
          subtotal: ticket.subtotal,
          tax: ticket.tax,
          total: ticket.total,
          discounts: ticket.discounts,
          customer: ticket.customer,
          note: ticket.note,
          status: ticket.status,
          terminalId: ticket.terminalId,
          locationId: ticket.locationId,
        }),
      },
      token
    );

    if (!response.ok) {
      const errorData = await response.json();
      return rejectWithValue(errorData.error || "Failed to save ticket");
    }

    const result = await response.json();

    if (!result.success || !result.data) {
      return rejectWithValue(result.error || "Failed to save ticket");
    }

    // Extract the ticket from the nested response structure
    return result.data.ticket || result.data;
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : "Network error"
    );
  }
});

// Load tickets from backend
export const loadTickets = createAsyncThunk<
  Ticket[],
  void,
  { state: RootState; rejectValue: string }
>("tickets/loadTickets", async (_, { rejectWithValue }) => {
  try {
    const token = await getAuthToken();

    if (!token) {
      return rejectWithValue("Authentication required");
    }

    const response = await makeAuthenticatedRequest(
      `${API_BASE_URL}/tickets`,
      { method: "GET" },
      token
    );

    if (!response.ok) {
      const errorData = await response.json();
      return rejectWithValue(errorData.error || "Failed to load tickets");
    }

    const result = await response.json();

    if (!result.success || !result.data) {
      return rejectWithValue(result.error || "Failed to load tickets");
    }

    // Extract the tickets array from the nested response structure
    return result.data.tickets || result.data;
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : "Network error"
    );
  }
});

// Delete ticket from backend
export const deleteTicketFromBackend = createAsyncThunk<
  string,
  { ticketId: string },
  { state: RootState; rejectValue: string }
>(
  "tickets/deleteTicketFromBackend",
  async ({ ticketId }, { rejectWithValue }) => {
    try {
      const token = await getAuthToken();

      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const response = await makeAuthenticatedRequest(
        `${API_BASE_URL}/tickets/${ticketId}`,
        { method: "DELETE" },
        token
      );

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || "Failed to delete ticket");
      }

      return ticketId;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Network error"
      );
    }
  }
);

// Auto-save dirty tickets
export const autoSaveDirtyTickets = createAsyncThunk<
  Ticket[],
  void,
  { state: RootState; rejectValue: string }
>(
  "tickets/autoSaveDirtyTickets",
  async (_, { getState, dispatch, rejectWithValue }) => {
    try {
      const state = getState();
      const dirtyTickets = state.tickets.tickets.filter(
        (t: Ticket) => t.isDirty
      );

      if (dirtyTickets.length === 0) {
        return [];
      }

      const savePromises = dirtyTickets.map((ticket: Ticket) =>
        dispatch(saveTicket({ ticket })).unwrap()
      );

      const savedTickets = await Promise.all(savePromises);
      return savedTickets;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Auto-save failed"
      );
    }
  }
);

// Sync tickets with backend (load and merge)
export const syncTickets = createAsyncThunk<
  { serverTickets: Ticket[]; conflicts: Ticket[] },
  void,
  { state: RootState; rejectValue: string }
>("tickets/syncTickets", async (_, { getState, dispatch, rejectWithValue }) => {
  try {
    // First, auto-save any dirty local tickets
    await dispatch(autoSaveDirtyTickets()).unwrap();

    // Then load tickets from server
    const serverTickets = await dispatch(loadTickets()).unwrap();

    const state = getState();
    const localTickets = state.tickets.tickets;

    // Identify conflicts (tickets that exist both locally and on server with different timestamps)
    const conflicts: Ticket[] = [];

    serverTickets.forEach((serverTicket) => {
      const localTicket = localTickets.find(
        (t: Ticket) => t.id === serverTicket.id
      );
      if (localTicket && localTicket.updatedAt !== serverTicket.updatedAt) {
        conflicts.push(serverTicket);
      }
    });

    return { serverTickets, conflicts };
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : "Sync failed"
    );
  }
});

// Batch save multiple tickets
export const batchSaveTickets = createAsyncThunk<
  Ticket[],
  { tickets: Ticket[] },
  { state: RootState; rejectValue: string }
>(
  "tickets/batchSaveTickets",
  async ({ tickets }, { dispatch, rejectWithValue }) => {
    try {
      const savePromises = tickets.map((ticket) =>
        dispatch(saveTicket({ ticket })).unwrap()
      );

      const savedTickets = await Promise.all(savePromises);
      return savedTickets;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Batch save failed"
      );
    }
  }
);

// Load specific ticket by ID
export const loadTicketById = createAsyncThunk<
  Ticket,
  { ticketId: string },
  { state: RootState; rejectValue: string }
>("tickets/loadTicketById", async ({ ticketId }, { rejectWithValue }) => {
  try {
    const token = await getAuthToken();

    if (!token) {
      return rejectWithValue("Authentication required");
    }

    const response = await makeAuthenticatedRequest(
      `${API_BASE_URL}/tickets/${ticketId}`,
      { method: "GET" },
      token
    );

    if (!response.ok) {
      const errorData = await response.json();
      return rejectWithValue(errorData.error || "Failed to load ticket");
    }

    const result = await response.json();

    if (!result.success || !result.data) {
      return rejectWithValue(result.error || "Failed to load ticket");
    }

    // Extract the ticket from the nested response structure
    return result.data.ticket || result.data;
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : "Network error"
    );
  }
});

// Auto-save ticket
export const autoSaveTicket = createAsyncThunk<
  { ticketId: string; timestamp: string },
  { ticketId: string; ticketState: any },
  { state: RootState; rejectValue: string }
>(
  "tickets/autoSaveTicket",
  async ({ ticketId, ticketState }, { rejectWithValue }) => {
    try {
      const token = await getAuthToken();

      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const response = await makeAuthenticatedRequest(
        `${API_BASE_URL}/tickets/${ticketId}/auto-save`,
        {
          method: "POST",
          body: JSON.stringify(ticketState),
        },
        token
      );

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || "Failed to auto-save ticket");
      }

      const result = await response.json();

      if (!result.success || !result.data) {
        return rejectWithValue(result.error || "Failed to auto-save ticket");
      }

      return {
        ticketId,
        timestamp: result.data.timestamp,
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Auto-save failed"
      );
    }
  }
);

// Batch auto-save tickets
export const batchAutoSaveTickets = createAsyncThunk<
  { results: any[]; summary: any },
  { tickets: { ticketId: string; ticketState: any }[] },
  { state: RootState; rejectValue: string }
>("tickets/batchAutoSaveTickets", async ({ tickets }, { rejectWithValue }) => {
  try {
    const token = await getAuthToken();

    if (!token) {
      return rejectWithValue("Authentication required");
    }

    const response = await makeAuthenticatedRequest(
      `${API_BASE_URL}/tickets/batch-auto-save`,
      {
        method: "POST",
        body: JSON.stringify({ tickets }),
      },
      token
    );

    if (!response.ok) {
      const errorData = await response.json();
      return rejectWithValue(
        errorData.error || "Failed to batch auto-save tickets"
      );
    }

    const result = await response.json();

    if (!result.success || !result.data) {
      return rejectWithValue(
        result.error || "Failed to batch auto-save tickets"
      );
    }

    return result.data;
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : "Batch auto-save failed"
    );
  }
});

// Get recovery tickets
export const getRecoveryTickets = createAsyncThunk<
  Ticket[],
  void,
  { state: RootState; rejectValue: string }
>("tickets/getRecoveryTickets", async (_, { rejectWithValue }) => {
  try {
    const token = await getAuthToken();

    if (!token) {
      return rejectWithValue("Authentication required");
    }

    const response = await makeAuthenticatedRequest(
      `${API_BASE_URL}/tickets/recovery`,
      { method: "GET" },
      token
    );

    if (!response.ok) {
      const errorData = await response.json();
      return rejectWithValue(
        errorData.error || "Failed to get recovery tickets"
      );
    }

    const result = await response.json();

    if (!result.success || !result.data) {
      return rejectWithValue(result.error || "Failed to get recovery tickets");
    }

    // Extract the tickets array from the nested response structure
    return result.data.tickets || result.data;
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : "Recovery fetch failed"
    );
  }
});

// Get auto-save statistics
export const getAutoSaveStats = createAsyncThunk<
  { stats: any[]; summary: any },
  { days?: number },
  { state: RootState; rejectValue: string }
>("tickets/getAutoSaveStats", async ({ days = 7 }, { rejectWithValue }) => {
  try {
    const token = await getAuthToken();

    if (!token) {
      return rejectWithValue("Authentication required");
    }

    const response = await makeAuthenticatedRequest(
      `${API_BASE_URL}/tickets/auto-save-stats?days=${days}`,
      { method: "GET" },
      token
    );

    if (!response.ok) {
      const errorData = await response.json();
      return rejectWithValue(
        errorData.error || "Failed to get auto-save stats"
      );
    }

    const result = await response.json();

    if (!result.success || !result.data) {
      return rejectWithValue(result.error || "Failed to get auto-save stats");
    }

    return result.data;
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : "Stats fetch failed"
    );
  }
});

// Update ticket status
export const updateTicketStatus = createAsyncThunk<
  Ticket,
  { ticketId: string; status: Ticket["status"] },
  { state: RootState; rejectValue: string }
>(
  "tickets/updateTicketStatus",
  async ({ ticketId, status }, { rejectWithValue }) => {
    try {
      const token = await getAuthToken();

      if (!token) {
        return rejectWithValue("Authentication required");
      }

      const response = await makeAuthenticatedRequest(
        `${API_BASE_URL}/tickets/${ticketId}`,
        {
          method: "PUT",
          body: JSON.stringify({ status }),
        },
        token
      );

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(
          errorData.error || "Failed to update ticket status"
        );
      }

      const result = await response.json();

      if (!result.success || !result.data) {
        return rejectWithValue(
          result.error || "Failed to update ticket status"
        );
      }

      // Extract the ticket from the nested response structure
      return result.data.ticket || result.data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Network error"
      );
    }
  }
);
