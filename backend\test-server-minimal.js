#!/usr/bin/env node

/**
 * Minimal Server Test
 * 
 * This script tests the core server functionality with the centralized database manager
 * without loading problematic services that have syntax errors.
 */

const express = require("express");
const cors = require("cors");
const { databaseManager } = require("./src/config/database-manager");
require("dotenv").config();

const app = express();
const PORT = process.env.PORT || 3020;

// Basic middleware
app.use(cors());
app.use(express.json());

// Enhanced health check with database status
app.get("/health", async (req, res) => {
  try {
    // Test database connectivity
    let dbStatus = "OK";
    let dbStats = null;
    
    try {
      await databaseManager.executeQuery('SELECT 1 as test');
      dbStats = databaseManager.getStats();
      
      // Check if connection pool is healthy
      const utilizationRate = dbStats.poolConfig?.connectionLimit 
        ? (dbStats.connectionStats.activeConnections / dbStats.poolConfig.connectionLimit)
        : 0;
      
      if (utilizationRate > 0.9 || dbStats.connectionStats.queuedRequests > 10) {
        dbStatus = "WARNING";
      }
    } catch (error) {
      dbStatus = "ERROR";
      console.error('Health check database error:', error);
    }
    
    res.json({
      status: dbStatus === "ERROR" ? "ERROR" : "OK",
      timestamp: new Date().toISOString(),
      services: {
        database: {
          status: dbStatus,
          connectionPool: dbStats ? {
            active: dbStats.connectionStats.activeConnections,
            limit: dbStats.poolConfig?.connectionLimit || 0,
            queued: dbStats.connectionStats.queuedRequests,
            total: dbStats.connectionStats.totalConnections,
            totalQueries: dbStats.connectionStats.totalQueries,
            failedQueries: dbStats.connectionStats.failedQueries,
          } : null,
        },
      },
      monitoring: {
        endpoint: "/api/database/health",
        stats: "/api/database/stats", 
        leaks: "/api/database/leaks",
      },
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: "ERROR",
      timestamp: new Date().toISOString(),
      error: "Health check failed",
    });
  }
});

// Database monitoring routes (simplified)
app.get("/api/database/stats", async (req, res) => {
  try {
    const stats = databaseManager.getStats();
    
    res.json({
      success: true,
      data: {
        connectionStats: stats.connectionStats,
        poolConfig: stats.poolConfig,
        poolState: stats.poolState,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error getting database stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get database statistics',
    });
  }
});

app.get("/api/database/health", async (req, res) => {
  try {
    const stats = databaseManager.getStats();
    
    // Perform a test query to verify connectivity
    let connectionTest = { success: false, responseTime: null };
    const startTime = Date.now();
    
    try {
      await databaseManager.executeQuery('SELECT 1 as test');
      connectionTest = {
        success: true,
        responseTime: Date.now() - startTime,
      };
    } catch (testError) {
      connectionTest = {
        success: false,
        error: testError.message,
        responseTime: Date.now() - startTime,
      };
    }
    
    const healthReport = {
      overall: connectionTest.success ? 'healthy' : 'error',
      timestamp: new Date().toISOString(),
      connectionPool: {
        status: connectionTest.success ? 'healthy' : 'error',
        totalConnections: stats.connectionStats.totalConnections,
        activeConnections: stats.connectionStats.activeConnections,
        queuedRequests: stats.connectionStats.queuedRequests,
        connectionLimit: stats.poolConfig?.connectionLimit || 0,
        utilizationPercentage: stats.poolConfig?.connectionLimit 
          ? Math.round((stats.connectionStats.activeConnections / stats.poolConfig.connectionLimit) * 100)
          : 0,
      },
      performance: {
        totalQueries: stats.connectionStats.totalQueries,
        failedQueries: stats.connectionStats.failedQueries,
        successRate: stats.connectionStats.totalQueries > 0 
          ? Math.round(((stats.connectionStats.totalQueries - stats.connectionStats.failedQueries) / stats.connectionStats.totalQueries) * 100)
          : 100,
        lastHealthCheck: stats.connectionStats.lastHealthCheck,
      },
      connectivity: connectionTest,
    };
    
    res.json({
      success: true,
      data: healthReport,
    });
  } catch (error) {
    console.error('Error getting database health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get database health report',
    });
  }
});

// Test endpoint to verify database operations
app.get("/api/test/database", async (req, res) => {
  try {
    console.log('🧪 Testing database operations...');
    
    // Test 1: Simple query
    const [rows1] = await databaseManager.executeQuery('SELECT 1 as test, "Database working" as message');
    console.log('✅ Simple query test passed');
    
    // Test 2: Multiple concurrent queries
    const concurrentQueries = [];
    for (let i = 0; i < 3; i++) {
      concurrentQueries.push(
        databaseManager.executeQuery('SELECT ? as query_number', [i + 1])
      );
    }
    const results = await Promise.all(concurrentQueries);
    console.log('✅ Concurrent queries test passed');
    
    // Test 3: Transaction test
    const transactionResult = await databaseManager.executeTransaction(async (connection) => {
      await connection.execute('SELECT 1 as step1');
      await connection.execute('SELECT 2 as step2');
      return { success: true, message: 'Transaction completed' };
    });
    console.log('✅ Transaction test passed');
    
    // Get final stats
    const stats = databaseManager.getStats();
    
    res.json({
      success: true,
      message: "All database tests passed!",
      results: {
        simpleQuery: rows1[0],
        concurrentQueries: results.map(r => r[0][0]),
        transaction: transactionResult,
        connectionStats: stats.connectionStats,
      },
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    res.status(500).json({
      success: false,
      error: 'Database test failed',
      message: error.message,
    });
  }
});

// Initialize database and start server
async function startServer() {
  try {
    // Initialize centralized database manager
    console.log('🔌 Initializing database connection pool...');
    await databaseManager.initialize();
    console.log('✅ Database connection pool initialized');

    // Start the server
    app.listen(PORT, () => {
      console.log(`🚀 Minimal Test Server running on port ${PORT}`);
      console.log(`📱 Health check: http://localhost:${PORT}/health`);
      console.log(`🔍 Database stats: http://localhost:${PORT}/api/database/stats`);
      console.log(`🧪 Database test: http://localhost:${PORT}/api/test/database`);
      console.log('');
      console.log('🎯 Testing centralized database manager...');
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  await databaseManager.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  await databaseManager.shutdown();
  process.exit(0);
});

// Start the server
startServer();
