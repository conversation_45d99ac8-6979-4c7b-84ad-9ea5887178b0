/**
 * Professional Ticket Workflow Hook
 * 
 * Provides comprehensive ticket management functionality following
 * professional POS system patterns and best practices.
 */

import { useCallback, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/src/store';
import { useSession } from '@/src/contexts/AuthContext';
import {
  selectAllTickets,
  selectActiveTicket,
  selectActiveTicketId,
  selectTicketStats,
  createTicket,
  setActiveTicket,
  deleteTicket,
  updateTicketStatus,
  duplicateTicket,
} from '@/src/store/slices/ticketSlice';
import { clearCart } from '@/src/store/slices/cartSlice';

export interface TicketWorkflowOptions {
  maxTicketsPerUser?: number;
  maxTicketsPerTerminal?: number;
  autoSwitchToNewTicket?: boolean;
  confirmBeforeDelete?: boolean;
  allowDuplicateTickets?: boolean;
}

const DEFAULT_OPTIONS: TicketWorkflowOptions = {
  maxTicketsPerUser: 5,
  maxTicketsPerTerminal: 10,
  autoSwitchToNewTicket: true,
  confirmBeforeDelete: true,
  allowDuplicateTickets: true,
};

export function useProfessionalTicketWorkflow(options: TicketWorkflowOptions = {}) {
  const dispatch = useAppDispatch();
  const { user } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const config = { ...DEFAULT_OPTIONS, ...options };
  
  const allTickets = useAppSelector(selectAllTickets);
  const activeTicket = useAppSelector(selectActiveTicket);
  const activeTicketId = useAppSelector(selectActiveTicketId);
  const ticketStats = useAppSelector(selectTicketStats);

  // Filter tickets by current user
  const userTickets = allTickets.filter(ticket => ticket.staffId === user?.id);
  const activeUserTickets = userTickets.filter(ticket => ticket.status === 'active');
  const pausedUserTickets = userTickets.filter(ticket => ticket.status === 'paused');

  /**
   * Create a new ticket with professional naming and validation
   */
  const createNewTicket = useCallback(async (customName?: string) => {
    if (!user?.id) {
      setError('User not authenticated');
      return null;
    }

    // Check user ticket limit
    if (userTickets.length >= config.maxTicketsPerUser!) {
      setError(`Maximum ${config.maxTicketsPerUser} tickets per user`);
      return null;
    }

    // Check terminal ticket limit
    if (allTickets.length >= config.maxTicketsPerTerminal!) {
      setError(`Maximum ${config.maxTicketsPerTerminal} tickets per terminal`);
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const ticketName = customName || `Order ${new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      })}`;

      const result = dispatch(createTicket({
        name: ticketName,
        staffId: user.id,
        terminalId: (user as any).terminalId || 'terminal-1',
        locationId: (user as any).locationId || 'location-1',
      }));

      return result;
    } catch (err) {
      setError('Failed to create ticket');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user, userTickets.length, allTickets.length, config, dispatch]);

  /**
   * Switch to a different ticket with confirmation if current has items
   */
  const switchToTicket = useCallback(async (ticketId: string) => {
    const targetTicket = allTickets.find(t => t.id === ticketId);
    if (!targetTicket) {
      setError('Ticket not found');
      return false;
    }

    // Check if current ticket has unsaved items
    if (activeTicket?.items?.length > 0) {
      return new Promise<boolean>((resolve) => {
        Alert.alert(
          'Switch Ticket',
          'Current ticket has items. Switch anyway?',
          [
            { text: 'Cancel', style: 'cancel', onPress: () => resolve(false) },
            { 
              text: 'Switch', 
              onPress: () => {
                dispatch(setActiveTicket(ticketId));
                resolve(true);
              }
            },
          ]
        );
      });
    }

    dispatch(setActiveTicket(ticketId));
    return true;
  }, [activeTicket, allTickets, dispatch]);

  /**
   * Delete ticket with confirmation and validation
   */
  const deleteTicketSafely = useCallback(async (ticketId: string) => {
    const targetTicket = allTickets.find(t => t.id === ticketId);
    if (!targetTicket) {
      setError('Ticket not found');
      return false;
    }

    // Prevent deletion of tickets with items
    if (targetTicket.items?.length > 0) {
      setError('Cannot delete ticket with items');
      return false;
    }

    if (config.confirmBeforeDelete) {
      return new Promise<boolean>((resolve) => {
        Alert.alert(
          'Delete Ticket',
          `Delete "${targetTicket.name}"? This cannot be undone.`,
          [
            { text: 'Cancel', style: 'cancel', onPress: () => resolve(false) },
            { 
              text: 'Delete', 
              style: 'destructive',
              onPress: () => {
                dispatch(deleteTicket(ticketId));
                resolve(true);
              }
            },
          ]
        );
      });
    }

    dispatch(deleteTicket(ticketId));
    return true;
  }, [allTickets, config.confirmBeforeDelete, dispatch]);

  /**
   * Pause current ticket and optionally create new one
   */
  const pauseAndCreateNew = useCallback(async (createNew = true) => {
    if (activeTicketId) {
      dispatch(updateTicketStatus({ ticketId: activeTicketId, status: 'paused' }));
    }

    if (createNew) {
      return await createNewTicket();
    }

    return null;
  }, [activeTicketId, createNewTicket, dispatch]);

  /**
   * Duplicate existing ticket
   */
  const duplicateExistingTicket = useCallback(async (ticketId: string) => {
    if (!config.allowDuplicateTickets) {
      setError('Ticket duplication not allowed');
      return null;
    }

    const targetTicket = allTickets.find(t => t.id === ticketId);
    if (!targetTicket) {
      setError('Ticket not found');
      return null;
    }

    try {
      const result = dispatch(duplicateTicket(ticketId));
      return result;
    } catch (err) {
      setError('Failed to duplicate ticket');
      return null;
    }
  }, [allTickets, config.allowDuplicateTickets, dispatch]);

  /**
   * Get recommended actions based on current state
   */
  const getRecommendedActions = useCallback(() => {
    const actions = [];

    if (!activeTicket) {
      actions.push({
        type: 'create',
        label: 'Create First Ticket',
        description: 'Start by creating your first ticket',
        priority: 'high',
      });
    } else if (activeTicket.items?.length === 0) {
      actions.push({
        type: 'add_items',
        label: 'Add Items',
        description: 'Add products to your ticket',
        priority: 'medium',
      });
    } else {
      actions.push({
        type: 'checkout',
        label: 'Process Payment',
        description: 'Complete the sale',
        priority: 'high',
      });
    }

    if (activeUserTickets.length > 1) {
      actions.push({
        type: 'switch',
        label: 'Switch Tickets',
        description: 'Manage multiple customer orders',
        priority: 'low',
      });
    }

    return actions;
  }, [activeTicket, activeUserTickets.length]);

  // Clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return {
    // State
    allTickets,
    userTickets,
    activeTicket,
    activeTicketId,
    activeUserTickets,
    pausedUserTickets,
    ticketStats,
    isLoading,
    error,
    
    // Actions
    createNewTicket,
    switchToTicket,
    deleteTicketSafely,
    pauseAndCreateNew,
    duplicateExistingTicket,
    
    // Utilities
    getRecommendedActions,
    clearError: () => setError(null),
    
    // Configuration
    config,
  };
}
