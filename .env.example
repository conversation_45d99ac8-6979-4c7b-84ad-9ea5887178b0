# Environment Configuration Example
# Copy this file to .env.local and configure your settings

# API Environment Selection
# Options: 'local', 'development', 'production'
EXPO_PUBLIC_API_ENV=local

# Local Development API (when using local backend)
EXPO_PUBLIC_LOCAL_API_URL=http://localhost:3020/api

# Development Online API (when using online dev server)
EXPO_PUBLIC_DEV_API_URL=https://treasuredposdev.dukalink.com/api

# Production API (when using production server)
EXPO_PUBLIC_PROD_API_URL=https://shopify.dukalink.com/api

# Debug Settings
EXPO_PUBLIC_DEBUG_API=true
EXPO_PUBLIC_LOG_REQUESTS=true

# App Configuration
EXPO_PUBLIC_APP_NAME=Dukalink POS
EXPO_PUBLIC_APP_VERSION=1.0.0

# Feature Flags
EXPO_PUBLIC_ENABLE_LOYALTY=true
EXPO_PUBLIC_ENABLE_THERMAL_PRINTING=true
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=false
