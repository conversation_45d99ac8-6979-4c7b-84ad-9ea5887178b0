import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useCustomer } from "@/src/contexts/CustomerContext";
import { useLocation } from "@/src/contexts/LocationContext";
import { useSalesAgent } from "@/src/contexts/SalesAgentContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { CrossPlatformPrintService } from "@/src/services/CrossPlatformPrintService";
import EnhancedThermalPrintService from "@/src/services/EnhancedThermalPrintService";
import { PaymentService } from "@/src/services/payment-service";
import { ReceiptGenerator } from "@/src/components/receipt/ReceiptGenerator";
import { useAppDispatch, useAppSelector } from "@/src/store";
import {
  clearCart,
  selectCartItemCount,
  selectCartTotal,
} from "@/src/store/slices/cartSlice";
import { createCustomer } from "@/src/store/slices/customerSlice";
import { Customer, Order } from "@/src/types/shopify";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { useRouter } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

import CartInventoryValidator from "@/components/ui/CartInventoryValidator";
import { ConfirmationModal } from "@/components/ui/ConfirmationModal";
import { OrderCompletionModal } from "@/components/ui/OrderCompletionModal";
import {
  PreOrderPrinterCheckModal,
  PreOrderPrinterCheckResult,
} from "@/components/ui/PreOrderPrinterCheckModal";
import { FloatingActionButton } from "@/src/components/ui/FloatingActionButton";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useInventoryManagement } from "@/src/hooks/useInventoryManagement";
import SplitPaymentModal from "./split-payment-modal";

const CheckoutScreen: React.FC = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user, isPosAuthenticated: isAuthenticated } = useSession();
  const { selectedCustomer, setSelectedCustomer, clearSelectedCustomer } =
    useCustomer();
  const { selectedAgent, setSelectedAgent, clearSelectedAgent } =
    useSalesAgent();
  const { currentLocation } = useLocation();
  const { setCurrentTitle } = useNavigation();
  const cartItems = useAppSelector((state) => state.cart.items);
  const cartTotal = useAppSelector(selectCartTotal);
  const itemCount = useAppSelector(selectCartItemCount);

  // Inventory management hook
  const {
    handlePostOrderInventoryUpdate,
    validateCartInventory,
    hasOutOfStockItems,
    adjustCartForInventory,
  } = useInventoryManagement();

  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<
    "cash" | "card" | "mpesa_till"
  >("cash");
  const [transactionCode, setTransactionCode] = useState("");

  // Selection modal states
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showSalesAgentModal, setShowSalesAgentModal] = useState(false);

  // Customer selection state
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerSearchQuery, setCustomerSearchQuery] = useState("");
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false);

  // Sales agent selection state
  const [salesAgents, setSalesAgents] = useState<any[]>([]);
  const [agentSearchQuery, setAgentSearchQuery] = useState("");
  const [isLoadingSalesAgents, setIsLoadingSalesAgents] = useState(false);

  // Modal states
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [showOrderCompletionModal, setShowOrderCompletionModal] =
    useState(false);
  const [showPreOrderPrinterCheck, setShowPreOrderPrinterCheck] =
    useState(false);
  const [showSplitPaymentModal, setShowSplitPaymentModal] = useState(false);
  const [showCustomerCreationModal, setShowCustomerCreationModal] =
    useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
    orderNumber?: string;
    orderTotal?: number;
  }>({ title: "", message: "" });

  // Customer creation form state
  const [customerFormData, setCustomerFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
  });
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false);

  // Pre-order printer check state
  const [printerCheckResult, setPrinterCheckResult] =
    useState<PreOrderPrinterCheckResult | null>(null);
  const [pendingOrderAction, setPendingOrderAction] = useState<
    "cash" | "mpesa_till" | null
  >(null);

  // Store order data for receipt printing and completion flow
  const [lastOrderData, setLastOrderData] = useState<any>(null);
  const [completionOrderData, setCompletionOrderData] = useState<{
    orderData: any;
    orderNumber: string;
    orderTotal: number;
    paymentMethod: string;
    transactionId?: string;
    printingAlreadyAttempted?: boolean;
    printingWasSuccessful?: boolean;
  } | null>(null);

  // Handle completion of the entire order flow (from OrderCompletionModal)
  const handleOrderCompletion = async () => {
    console.log("🔄 Starting order completion with inventory update...");

    // Update inventory before clearing cart
    if (cartItems.length > 0) {
      const inventoryResult = await handlePostOrderInventoryUpdate(cartItems);
      if (inventoryResult.success) {
        console.log("✅ Inventory updated successfully after order completion");
      } else {
        console.error("❌ Inventory update failed:", inventoryResult.error);
        // Continue with order completion even if inventory update fails
      }
    }

    // Clear cart and selections only when the entire flow is complete
    dispatch(clearCart());
    clearSelectedCustomer();
    clearSelectedAgent();

    // Reset completion data
    setCompletionOrderData(null);
    setShowOrderCompletionModal(false);

    // Reset pre-order check state
    setPrinterCheckResult(null);
    setPendingOrderAction(null);

    // Clear order data
    setLastOrderData(null);

    console.log("✅ Order completion finished");
  };

  // Load customers for selection
  const loadCustomers = useCallback(async (searchQuery = "") => {
    setIsLoadingCustomers(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomers({
        search: searchQuery,
        limit: 50,
      });

      if (response.success && response.data) {
        setCustomers(response.data.customers || []);
      } else {
        console.error("Failed to load customers:", response.error);
        setCustomers([]);
      }
    } catch (error) {
      console.error("Error loading customers:", error);
      setCustomers([]);
    } finally {
      setIsLoadingCustomers(false);
    }
  }, []);

  // Load sales agents for selection
  const loadSalesAgents = useCallback(async (searchQuery = "") => {
    setIsLoadingSalesAgents(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getSalesAgents();

      if (response.success && response.data) {
        let agents = response.data.salesAgents || [];

        // Filter by search query if provided
        if (searchQuery.trim()) {
          agents = agents.filter(
            (agent: any) =>
              agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
              agent.email.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }

        setSalesAgents(agents);
      } else {
        console.error("Failed to load sales agents:", response.error);
        setSalesAgents([]);
      }
    } catch (error) {
      console.error("Error loading sales agents:", error);
      setSalesAgents([]);
    } finally {
      setIsLoadingSalesAgents(false);
    }
  }, []);

  // Handle customer selection
  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer({
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName || "",
      displayName:
        customer.displayName ||
        `${customer.firstName || ""} ${customer.lastName || ""}`.trim() ||
        "Unknown Customer",
      email: customer.email,
      phone: customer.phone,
    });
    setShowCustomerModal(false);
  };

  // Handle sales agent selection
  const handleSalesAgentSelect = (agent: any) => {
    setSelectedAgent({
      id: agent.id,
      name: agent.name,
      email: agent.email,
      territory: agent.territory || "",
      commissionRate: agent.commissionRate,
      active: agent.active,
    });
    setShowSalesAgentModal(false);
  };

  // Handle customer creation
  const handleCreateCustomer = async () => {
    if (
      !customerFormData.firstName.trim() ||
      !customerFormData.lastName.trim()
    ) {
      setModalData({
        title: "Validation Error",
        message: "First name and last name are required.",
      });
      setShowErrorModal(true);
      return;
    }

    setIsCreatingCustomer(true);
    try {
      const customerData = {
        firstName: customerFormData.firstName.trim(),
        lastName: customerFormData.lastName.trim(),
        email: customerFormData.email.trim() || undefined,
        phone: customerFormData.phone.trim() || undefined,
        note: "Created from POS checkout",
        tags: "pos,checkout",
      };

      const result = await dispatch(createCustomer(customerData)).unwrap();

      if (result) {
        // Auto-select the newly created customer
        setSelectedCustomer({
          id: result.id,
          firstName: result.firstName,
          lastName: result.lastName || "",
          displayName: `${result.firstName} ${result.lastName || ""}`.trim(),
          email: result.email,
          phone: result.phone,
        });

        // Refresh customer list
        await loadCustomers(customerSearchQuery);

        // Close creation modal and customer selection modal
        setShowCustomerCreationModal(false);
        setShowCustomerModal(false);

        // Reset form
        setCustomerFormData({
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
        });

        // Show success message
        setModalData({
          title: "Customer Created",
          message: `Customer "${result.firstName} ${result.lastName}" has been created and selected.`,
        });
        setShowSuccessModal(true);
      }
    } catch (error: any) {
      console.error("Customer creation error:", error);
      setModalData({
        title: "Customer Creation Failed",
        message:
          error.message || "Failed to create customer. Please try again.",
      });
      setShowErrorModal(true);
    } finally {
      setIsCreatingCustomer(false);
    }
  };

  // Load customers when modal opens
  useEffect(() => {
    if (showCustomerModal) {
      loadCustomers(customerSearchQuery);
    }
  }, [showCustomerModal, loadCustomers, customerSearchQuery]);

  // Load sales agents when modal opens
  useEffect(() => {
    if (showSalesAgentModal) {
      loadSalesAgents(agentSearchQuery);
    }
  }, [showSalesAgentModal, loadSalesAgents, agentSearchQuery]);

  // Set page title
  useEffect(() => {
    setCurrentTitle("Checkout");
  }, [setCurrentTitle]);

  // Handle success modal dismissal with proper cleanup
  const handleSuccessModalDismiss = async () => {
    console.log("🔄 Starting success modal dismissal with inventory update...");

    // Update inventory before clearing cart
    if (cartItems.length > 0) {
      const inventoryResult = await handlePostOrderInventoryUpdate(cartItems);
      if (inventoryResult.success) {
        console.log(
          "✅ Inventory updated successfully on success modal dismissal"
        );
      } else {
        console.error("❌ Inventory update failed:", inventoryResult.error);
        // Continue with dismissal even if inventory update fails
      }
    }

    // Clear cart and selections when user dismisses success modal
    dispatch(clearCart());
    clearSelectedCustomer();
    clearSelectedAgent();

    // Reset pre-order check state
    setPrinterCheckResult(null);
    setPendingOrderAction(null);

    // Clear order data
    setLastOrderData(null);

    // Close modal
    setShowSuccessModal(false);

    console.log("✅ Success modal dismissal finished");
  };

  // Handle pre-order printer check result
  const handlePreOrderPrinterCheckResult = (
    result: PreOrderPrinterCheckResult
  ) => {
    setPrinterCheckResult(result);
    setShowPreOrderPrinterCheck(false);

    if (result.proceed) {
      // User chose to proceed with or without printer
      // Now execute the pending order action
      if (pendingOrderAction === "mpesa_till") {
        executeActualMpesaTillPayment(result);
      } else if (pendingOrderAction === "cash") {
        executeActualOrderPlacement(result);
      }
    } else {
      // User cancelled or chose to setup printer
      // Reset pending action
      setPendingOrderAction(null);
    }
  };

  // Execute the actual M-Pesa Till payment after printer check
  const executeActualMpesaTillPayment = async (
    printerInfo: PreOrderPrinterCheckResult
  ) => {
    if (!selectedCustomer || !selectedAgent || !user || !currentLocation) {
      setModalData({
        title: "Missing Information",
        message:
          "Missing required information for order creation. Please ensure customer, sales agent, and location are selected.",
      });
      setShowErrorModal(true);
      return;
    }

    setIsProcessing(true);
    try {
      const staffInfo = {
        id: user.id,
        name: user.name,
        terminal: "Current Terminal",
      };

      const customerInfo = {
        name:
          selectedCustomer.displayName ||
          `${selectedCustomer.firstName} ${selectedCustomer.lastName}`.trim() ||
          "Walk-in Customer",
        phone: selectedCustomer.phone || "",
      };

      const additionalData = {
        tillNumber: "123456", // Default ABSA till number
        accountNumber: customerInfo.phone || "POS-PAYMENT",
        amountTendered: cartTotal,
        transactionCode: transactionCode.trim(),
      };

      // Process ABSA Till payment
      const paymentResult = await PaymentService.processPayment(
        {
          method: PaymentService.getPaymentMethodById("absa_till")!,
          amount: cartTotal,
          currency: "KES",
        },
        staffInfo,
        customerInfo,
        additionalData
      );

      if (paymentResult.success) {
        // Create order with payment information and printer info
        await createOrderWithPayment(paymentResult);
      } else {
        setModalData({
          title: "Payment Failed",
          message:
            paymentResult.error ||
            "M-Pesa Till payment processing failed. Please try again.",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("M-Pesa Till payment processing error:", error);
      setModalData({
        title: "Payment Error",
        message:
          "Payment processing failed. Please check your connection and try again.",
      });
      setShowErrorModal(true);
    } finally {
      setIsProcessing(false);
    }
  };

  // Execute the actual order placement after printer check
  const executeActualOrderPlacement = async (
    printerInfo: PreOrderPrinterCheckResult
  ) => {
    setIsProcessing(true);
    try {
      const apiClient = getAPIClient();

      const orderData: Partial<Order> = {
        lineItems: cartItems.map((item) => ({
          id: "", // Will be generated by API
          variantId: item.variantId,
          productId: item.productId,
          title: item.title,
          variantTitle: item.variantTitle,
          sku: item.sku,
          quantity: item.quantity,
          price: item.price,
          totalDiscount: "0.00",
          taxLines: [],
          customAttributes: [],
        })),
        subtotalPrice: cartTotal.toString(),
        totalTax: "0.00",
        totalPrice: cartTotal.toString(),
        currency: "KES",
        financialStatus: "pending",
        fulfillmentStatus: "null",
        discounts: [],
        customAttributes: [
          { key: "salesAgentId", value: selectedAgent?.id || "" },
          { key: "posStaffId", value: user?.id || "" },
          {
            key: "paymentMethod",
            value: paymentMethod,
          },
        ],
        note: `POS Order - Staff: ${user?.name || "Unknown"}${
          selectedAgent ? `, Agent: ${selectedAgent.name}` : ""
        }`,
        tags: ["pos", "mobile"],
        isSynced: false,
        salespersonId: selectedAgent?.id,
        salespersonName: selectedAgent?.name,
        commissionRate: selectedAgent?.commissionRate,
      };

      const response = await apiClient.createStoreOrder(orderData);

      if (response.success && response.data) {
        // Handle the response structure { order: Order }
        const order = response.data.order || response.data;
        const orderNumber = order.orderNumber || order.name || order.id;

        // Generate order data for completion flow
        const orderDataForCompletion = {
          id: order.id,
          orderNumber,
          totalPrice: cartTotal.toString(),
          createdAt: new Date().toISOString(),
          salespersonName: user?.name || "Unknown Staff",
          salespersonId: selectedAgent?.id,
          paymentMethod: paymentMethod,
          customer: selectedCustomer
            ? {
                firstName: selectedCustomer.firstName,
                lastName: selectedCustomer.lastName,
                email: selectedCustomer.email,
                phone: selectedCustomer.phone,
              }
            : undefined,
          lineItems: cartItems.map((item) => ({
            id: item.variantId,
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            sku: item.sku,
            variantId: item.variantId,
            productId: item.productId,
          })),
        };

        // Store order data for manual receipt printing
        setLastOrderData(orderDataForCompletion);

        // Handle printing based on printer availability using cross-platform service
        if (printerInfo.printerAvailable) {
          // Printer is available, attempt automatic printing with cross-platform service
          try {
            // First try cross-platform printing (automatically chooses best method for web/mobile)
            const receiptData = await ReceiptGenerator.generateReceiptData(
              orderDataForCompletion,
              true
            );
            const crossPlatformResult =
              await CrossPlatformPrintService.printReceipt(receiptData);

            if (crossPlatformResult.success) {
              // Cross-platform printing successful
              setPendingOrderAction(null);
              setPrinterCheckResult(null);

              setModalData({
                title: "Order Completed Successfully!",
                message: `Order #${orderNumber} has been created and receipt printed using ${
                  crossPlatformResult.method
                } on ${
                  crossPlatformResult.platform
                }.\n\nTotal: KSh ${cartTotal.toFixed(
                  2
                )}\nPayment: ${paymentMethod}`,
                orderNumber,
                orderTotal: cartTotal,
              });
              setShowSuccessModal(true);
            } else {
              // Cross-platform printing failed, fallback to thermal printing
              console.log(
                "Cross-platform printing failed, trying thermal fallback:",
                crossPlatformResult.error
              );

              const printResult =
                await EnhancedThermalPrintService.printReceipt(
                  orderDataForCompletion
                );

              if (printResult.success) {
                // Thermal printing successful
                setPendingOrderAction(null);
                setPrinterCheckResult(null);

                setModalData({
                  title: "Order Completed Successfully!",
                  message: `Order #${orderNumber} has been created and receipt printed (thermal fallback).\n\nTotal: KSh ${cartTotal.toFixed(
                    2
                  )}\nPayment: ${paymentMethod}`,
                  orderNumber,
                  orderTotal: cartTotal,
                });
                setShowSuccessModal(true);
              } else {
                // Both printing methods failed, show completion modal for retry options
                setCompletionOrderData({
                  orderData: orderDataForCompletion,
                  orderNumber,
                  orderTotal: cartTotal,
                  paymentMethod: paymentMethod,
                  transactionId: `ORDER-${orderNumber}`,
                  printingAlreadyAttempted: true,
                  printingWasSuccessful: false,
                });
                setShowOrderCompletionModal(true);
              }
            }
          } catch (error) {
            console.error("Automatic printing error:", error);
            console.log("Setting completion order data for error case");
            // Printing failed, show completion modal for retry options
            const completionData = {
              orderData: orderDataForCompletion,
              orderNumber,
              orderTotal: cartTotal,
              paymentMethod: paymentMethod,
              transactionId: `ORDER-${orderNumber}`,
              printingAlreadyAttempted: true,
              printingWasSuccessful: false,
            };
            console.log("Completion data:", completionData);
            setCompletionOrderData(completionData);
            setShowOrderCompletionModal(true);
            console.log("OrderCompletionModal should now be visible");
          }
        } else {
          // No printer available, show success modal but preserve data for receipt operations
          // Data will be cleared when user dismisses the success modal
          setPendingOrderAction(null);
          setPrinterCheckResult(null);

          setModalData({
            title: "Order Completed Successfully!",
            message: `Order #${orderNumber} has been created.\n\nTotal: KSh ${cartTotal.toFixed(
              2
            )}\nPayment: ${paymentMethod}\n\nReceipt can be printed later from Orders screen.`,
            orderNumber,
            orderTotal: cartTotal,
          });
          setShowSuccessModal(true);
        }
      } else {
        setModalData({
          title: "Error",
          message: response.error || "Failed to place order",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Failed to place order:", error);
      setModalData({
        title: "Error",
        message: "Failed to place order. Please try again.",
      });
      setShowErrorModal(true);
    } finally {
      setIsProcessing(false);
    }
  };

  // Theme system
  const theme = useTheme();
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = useThemeColor({}, "success");
  const borderColor = useThemeColor({}, "border");

  // Create styles using theme
  const styles = createStyles(theme);

  // Old handleManualReceiptPrint removed - functionality integrated into handlePrintReceiptFromSuccess

  // Enhanced action handlers that preserve data for receipt operations
  const handleViewOrdersFromSuccess = () => {
    router.replace("/(tabs)/orders");
    handleSuccessModalDismiss();
  };

  const handleNewSaleFromSuccess = () => {
    router.replace("/(tabs)/products");
    handleSuccessModalDismiss();
  };

  const handleViewReceiptFromSuccess = () => {
    if (!modalData.orderNumber) {
      setModalData({
        title: "Error",
        message: "Order number not available for receipt viewing.",
      });
      setShowErrorModal(true);
      return;
    }
    router.push(`/order-receipt?orderNumber=${modalData.orderNumber}`);
    handleSuccessModalDismiss();
  };

  const handlePrintReceiptFromSuccess = async () => {
    if (!lastOrderData) {
      setModalData({
        title: "No Order Data",
        message: "No order data available for printing.",
      });
      setShowErrorModal(true);
      return;
    }

    try {
      // First try cross-platform printing (automatically chooses best method for web/mobile)
      const receiptData = await ReceiptGenerator.generateReceiptData(
        lastOrderData,
        true
      );
      const crossPlatformResult = await CrossPlatformPrintService.printReceipt(
        receiptData
      );

      if (crossPlatformResult.success) {
        // Cross-platform printing successful
        setModalData({
          title: "Order Completed Successfully!",
          message: `${modalData.message}\n\nReceipt has been printed successfully using ${crossPlatformResult.method} on ${crossPlatformResult.platform}!`,
          orderNumber: modalData.orderNumber,
          orderTotal: modalData.orderTotal,
        });
        // Modal stays open - user can still view receipt or navigate
      } else {
        // Cross-platform printing failed, fallback to thermal printing
        console.log(
          "Cross-platform printing failed, trying thermal fallback:",
          crossPlatformResult.error
        );

        const printResult = await EnhancedThermalPrintService.printReceipt(
          lastOrderData
        );

        if (printResult.success) {
          // Thermal printing successful
          setModalData({
            title: "Order Completed Successfully!",
            message: `${modalData.message}\n\nReceipt has been printed successfully (thermal fallback)!`,
            orderNumber: modalData.orderNumber,
            orderTotal: modalData.orderTotal,
          });
          // Modal stays open - user can still view receipt or navigate
        } else {
          // Both printing methods failed
          setModalData({
            title: "Print Error",
            message: `Cross-platform printing failed: ${
              crossPlatformResult.error
            }\nThermal printing failed: ${
              printResult.error || "Unknown error"
            }\n\nPlease check your printer connection.`,
          });
          setShowErrorModal(true);
        }
      }
    } catch (error) {
      console.error("Receipt printing error:", error);
      setModalData({
        title: "Print Error",
        message:
          "Failed to print receipt. Please check your printer connection.",
      });
      setShowErrorModal(true);
    }
  };

  // Old thermal printing logic removed - now handled by pre-order printer check

  // Create order with payment information (shared by M-Pesa Till and future payment methods)
  const createOrderWithPayment = async (paymentResult: any) => {
    try {
      const apiClient = getAPIClient();

      // Prepare order data with payment information
      const orderData = {
        lineItems: cartItems.map((item) => ({
          variantId: item.variantId,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          title: item.title,
          sku: item.sku,
        })),
        customer: selectedCustomer!.email
          ? {
              id: selectedCustomer!.id,
              email: selectedCustomer!.email,
              firstName: selectedCustomer!.firstName,
              lastName: selectedCustomer!.lastName,
              phone: selectedCustomer!.phone,
            }
          : undefined,
        staffId: user!.id,
        salesAgentId: selectedAgent!.id,
        email:
          selectedCustomer!.email ||
          `${selectedCustomer!.firstName.toLowerCase()}.${selectedCustomer!.lastName.toLowerCase()}@pos.local`,
        phone: selectedCustomer!.phone,
        note: `POS Order by ${user!.name} (${user!.role}) | Sales Agent: ${
          selectedAgent!.name
        } | Payment: ${paymentResult.method.name} | Transaction: ${
          paymentResult.transactionId
        }`,
        tags: `POS,Dukalink,Staff:${user!.username},Agent:${
          selectedAgent!.name
        },Payment:${paymentResult.method.type},Location:${
          currentLocation!.name
        }`,
        billingAddress: {
          firstName: selectedCustomer!.firstName,
          lastName: selectedCustomer!.lastName,
          phone: selectedCustomer!.phone,
          address1: "POS Sale",
          city: currentLocation!.name || "Store Location",
          country: "Kenya",
          zip: "00100",
        },
        // Payment information
        paymentMethod: paymentResult.method.name,
        paymentTransactionId: paymentResult.transactionId,
        paymentTimestamp: paymentResult.timestamp,
        financialStatus: "paid", // Mark as paid since payment was processed
        // Location information
        locationId: currentLocation!.id,
        locationName: currentLocation!.name,
      };

      const response = await apiClient.createOrderWithDualAttribution(
        orderData
      );

      if (response.success) {
        // Order created successfully
        const orderNumber =
          response?.data?.order.name || response?.data?.order.id || "Unknown";

        // Generate order data for receipt
        const orderDataForReceipt = {
          id: response?.data?.order.id,
          orderNumber,
          totalPrice: cartTotal.toString(),
          createdAt: new Date().toISOString(),
          salespersonName: user!.name,
          salespersonId: selectedAgent!.id,
          paymentMethod: paymentResult.method.name,
          customer: selectedCustomer
            ? {
                firstName: selectedCustomer.firstName,
                lastName: selectedCustomer.lastName,
                email: selectedCustomer.email,
                phone: selectedCustomer.phone,
              }
            : undefined,
          lineItems: cartItems.map((item) => ({
            id: item.variantId || item.productId,
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            sku: item.sku,
            variantId: item.variantId,
            productId: item.productId,
          })),
        };

        // Store order data for manual receipt printing
        setLastOrderData(orderDataForReceipt);

        // Set up completion modal data and show it
        setCompletionOrderData({
          orderData: orderDataForReceipt,
          orderNumber,
          orderTotal: cartTotal,
          paymentMethod: paymentResult.method.name,
          transactionId: paymentResult.transactionId,
        });
        setShowOrderCompletionModal(true);
      } else {
        setModalData({
          title: "Order Creation Failed",
          message:
            response.error || "Failed to create order. Please try again.",
        });
        setShowErrorModal(true);
      }
    } catch (error: any) {
      console.error("Order creation error:", error);
      setModalData({
        title: "Order Creation Error",
        message:
          "Failed to create order. Please check your connection and try again.",
      });
      setShowErrorModal(true);
    }
  };

  // Handle order placement within the unified modal - using EXACT original logic
  const handlePlaceOrderInModal = async () => {
    try {
      const apiClient = getAPIClient();

      const orderData: Partial<Order> = {
        lineItems: cartItems.map((item) => ({
          id: "", // Will be generated by API
          variantId: item.variantId,
          productId: item.productId,
          title: item.title,
          variantTitle: item.variantTitle,
          sku: item.sku,
          quantity: item.quantity,
          price: item.price,
          totalDiscount: "0.00",
          taxLines: [],
          customAttributes: [],
        })),
        subtotalPrice: cartTotal.toString(),
        totalTax: "0.00",
        totalPrice: cartTotal.toString(),
        currency: "KES",
        financialStatus: "pending",
        fulfillmentStatus: "null",
        discounts: [],
        customAttributes: [
          { key: "salesAgentId", value: selectedAgent?.id || "" },
          { key: "posStaffId", value: user?.id || "" },
          {
            key: "paymentMethod",
            value: paymentMethod,
          },
        ],
        note: `POS Order - Staff: ${user?.name || "Unknown"}${
          selectedAgent ? `, Agent: ${selectedAgent.name}` : ""
        }`,
        tags: ["pos", "mobile"],
        isSynced: false,
        salespersonId: selectedAgent?.id,
        salespersonName: selectedAgent?.name,
        commissionRate: selectedAgent?.commissionRate,
      };

      const response = await apiClient.createStoreOrder(orderData);

      if (response.success && response.data) {
        // Handle the response structure { order: Order }
        const order = response.data.order || response.data;
        const orderNumber = order.orderNumber || order.name || order.id;

        // Generate order data for completion flow
        const orderDataForCompletion = {
          id: order.id,
          orderNumber,
          totalPrice: cartTotal.toString(),
          createdAt: new Date().toISOString(),
          salespersonName: user?.name || "Unknown Staff",
          salespersonId: selectedAgent?.id,
          paymentMethod: paymentMethod,
          customer: selectedCustomer
            ? {
                firstName: selectedCustomer.firstName,
                lastName: selectedCustomer.lastName,
                email: selectedCustomer.email,
                phone: selectedCustomer.phone,
              }
            : undefined,
          lineItems: cartItems.map((item) => ({
            id: item.variantId,
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            sku: item.sku,
            variantId: item.variantId,
            productId: item.productId,
          })),
        };

        // Store order data for manual receipt printing
        setLastOrderData(orderDataForCompletion);

        return {
          success: true,
          orderData: orderDataForCompletion,
          orderNumber,
        };
      } else {
        return {
          success: false,
          error: response.error || "Failed to place order",
        };
      }
    } catch (error) {
      console.error("Failed to place order:", error);
      return {
        success: false,
        error: "Failed to place order. Please try again.",
      };
    }
  };

  const handlePlaceOrder = async () => {
    if (cartItems.length === 0) {
      setModalData({
        title: "Error",
        message: "Your cart is empty",
      });
      setShowErrorModal(true);
      return;
    }

    if (!selectedCustomer) {
      setModalData({
        title: "Error",
        message: "Please select a customer",
      });
      setShowErrorModal(true);
      return;
    }

    // Validate inventory before placing order
    const inventoryValidation = validateCartInventory(cartItems);
    if (!inventoryValidation.isValid) {
      setModalData({
        title: "Insufficient Inventory",
        message: `Cannot place order due to insufficient stock:\n\n${inventoryValidation.errors.join(
          "\n"
        )}`,
      });
      setShowErrorModal(true);
      return;
    }

    // Check for potential overselling
    if (hasOutOfStockItems(cartItems)) {
      const adjustment = adjustCartForInventory(cartItems);
      if (adjustment.adjustmentsMade) {
        setModalData({
          title: "Cart Adjusted for Inventory",
          message: `Some items were adjusted due to insufficient stock:\n\n${adjustment.adjustments.join(
            "\n"
          )}\n\nPlease review your cart and try again.`,
        });
        setShowErrorModal(true);
        return;
      }
    }

    // For Mpesa Till, validate transaction code first
    if (paymentMethod === "mpesa_till") {
      if (!transactionCode.trim()) {
        setModalData({
          title: "Transaction Code Required",
          message:
            "Please enter the M-Pesa transaction code to proceed with the payment.",
        });
        setShowErrorModal(true);
        return;
      }
      // Set pending action and show printer check modal
      setPendingOrderAction("mpesa_till");
      setShowPreOrderPrinterCheck(true);
      return;
    }

    // For cash orders, set pending action and show printer check modal
    setPendingOrderAction("cash");
    setShowPreOrderPrinterCheck(true);
  };

  // Handle split payment completion
  const handleSplitPaymentComplete = async (splitResult: any) => {
    if (!splitResult.success) {
      setModalData({
        title: "Split Payment Failed",
        message: splitResult.error || "Split payment processing failed",
      });
      setShowErrorModal(true);
      return;
    }

    try {
      // Create order with split payment information
      const apiClient = getAPIClient();

      // Prepare order data with split payment information
      const orderData = {
        lineItems: cartItems.map((item) => ({
          variantId: item.variantId,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          title: item.title,
          sku: item.sku,
        })),
        customer: selectedCustomer!.email
          ? {
              id: selectedCustomer!.id,
              email: selectedCustomer!.email,
              firstName: selectedCustomer!.firstName,
              lastName: selectedCustomer!.lastName,
              phone: selectedCustomer!.phone,
            }
          : undefined,
        staffId: user!.id,
        salesAgentId: selectedAgent!.id,
        email:
          selectedCustomer!.email ||
          `${selectedCustomer!.firstName.toLowerCase()}.${selectedCustomer!.lastName.toLowerCase()}@pos.local`,
        phone: selectedCustomer!.phone,
        note: `POS Split Payment Order by ${user!.name} (${
          user!.role
        }) | Sales Agent: ${
          selectedAgent!.name
        } | ${PaymentService.generateSplitPaymentSummary(splitResult)}`,
        tags: `POS,Dukalink,Staff:${user!.username},Agent:${
          selectedAgent!.name
        },Payment:Split,Location:${currentLocation!.name}`,
        billingAddress: {
          firstName: selectedCustomer!.firstName,
          lastName: selectedCustomer!.lastName,
          phone: selectedCustomer!.phone,
          address1: "POS Sale",
          city: currentLocation!.name || "Store Location",
          country: "Kenya",
          zip: "00100",
        },
        // Split payment information
        paymentMethod: "Split Payment",
        paymentTransactionId: splitResult.combinedTransactionId,
        paymentTimestamp: splitResult.timestamp,
        splitPaymentData: splitResult, // Store complete split payment data
        financialStatus: "paid", // Mark as paid since all payments were processed
        // Location information
        locationId: currentLocation!.id,
        locationName: currentLocation!.name,
      };

      const response = await apiClient.createOrderWithDualAttribution(
        orderData
      );

      if (response.success) {
        // Order created successfully
        const orderNumber =
          response?.data?.order.name || response?.data?.order.id || "Unknown";

        // Generate order data for receipt
        const orderDataForReceipt = {
          id: response?.data?.order.id,
          orderNumber,
          totalPrice: cartTotal.toString(),
          createdAt: new Date().toISOString(),
          salespersonName: user!.name,
          salespersonId: selectedAgent!.id,
          paymentMethod: "Split Payment",
          splitPaymentData: splitResult, // Include split payment data for receipt
          customer: selectedCustomer
            ? {
                firstName: selectedCustomer.firstName,
                lastName: selectedCustomer.lastName,
                email: selectedCustomer.email,
                phone: selectedCustomer.phone,
              }
            : undefined,
          lineItems: cartItems.map((item) => ({
            id: item.variantId || item.productId,
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            sku: item.sku,
            variantId: item.variantId,
            productId: item.productId,
          })),
        };

        // Store order data for manual receipt printing
        setLastOrderData(orderDataForReceipt);

        // Update inventory immediately after successful order creation
        const inventoryResult = await handlePostOrderInventoryUpdate(cartItems);
        if (inventoryResult.success) {
          console.log(
            "✅ Inventory updated successfully after split payment order"
          );
        } else {
          console.error(
            "❌ Inventory update failed after split payment:",
            inventoryResult.error
          );
        }

        // Set up completion modal data and show it
        setCompletionOrderData({
          orderData: orderDataForReceipt,
          orderNumber,
          orderTotal: cartTotal,
          paymentMethod: "Split Payment",
          transactionId: splitResult.combinedTransactionId,
        });
        setShowOrderCompletionModal(true);
      } else {
        setModalData({
          title: "Order Creation Failed",
          message:
            response.error || "Failed to create order. Please try again.",
        });
        setShowErrorModal(true);
      }
    } catch (error: any) {
      console.error("Split payment order creation error:", error);
      setModalData({
        title: "Order Creation Error",
        message:
          "Failed to create order. Please check your connection and try again.",
      });
      setShowErrorModal(true);
    }
  };

  const renderCartItem = (item: any, index: number) => {
    const isLastItem = index === cartItems.length - 1;
    const itemTotal = (parseFloat(item.price) || 0) * item.quantity;

    return (
      <View
        key={`cart-item-${item.variantId || index}`}
        style={[
          styles.cartItem,
          // Only show border if not the last item and there are multiple items
          !isLastItem && cartItems.length > 1 && styles.cartItemWithBorder,
        ]}
      >
        {/* Top line: Item title only */}
        <Text
          style={[styles.itemTitle, { color: textColor }]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {item.title}
        </Text>

        {/* Bottom line: Quantity @ Price with dotted line to total */}
        <View style={styles.itemBottomRow}>
          <Text style={[styles.itemQuantityPrice, { color: textSecondary }]}>
            {item.quantity} @ KSh{" "}
            {(parseFloat(item.price) || 0).toLocaleString("en-KE", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </Text>

          {/* Dotted line */}
          <View style={styles.dottedLine}>
            <Text style={[styles.dots, { color: textSecondary }]}>-</Text>
          </View>

          <Text style={[styles.itemTotal, { color: primaryColor }]}>
            KSh{" "}
            {itemTotal.toLocaleString("en-KE", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </Text>
        </View>
      </View>
    );
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <ScreenWrapper title="Checkout" showBackButton>
      <ScrollView style={[styles.container, { backgroundColor }]}>
        {/* Order Summary */}
        <ModernCard style={styles.orderSummaryCard} variant="elevated">
          <View style={styles.orderSummaryHeader}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              Order Summary
            </Text>
            <View
              style={[styles.itemCountBadge, { backgroundColor: primaryColor }]}
            >
              <Text style={styles.itemCountText}>{itemCount}</Text>
            </View>
          </View>

          <View style={styles.cartItems}>
            {cartItems.map((item, index) => renderCartItem(item, index))}
          </View>

          <View
            style={[styles.totalRow, { borderTopColor: theme.colors.border }]}
          >
            <Text style={[styles.totalLabel, { color: textColor }]}>Total</Text>
            <Text style={[styles.totalAmount, { color: primaryColor }]}>
              {formatCurrency(cartTotal)}
            </Text>
          </View>
        </ModernCard>

        {/* Inventory Validation */}
        <CartInventoryValidator cartItems={cartItems} showWarnings={true} />

        {/* Customer Information */}
        <ModernCard style={styles.section} variant="elevated">
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              Customer
            </Text>
            <TouchableOpacity
              onPress={() => setShowCustomerModal(true)}
              style={styles.changeButton}
            >
              <Text style={[styles.changeButtonText, { color: primaryColor }]}>
                Change
              </Text>
            </TouchableOpacity>
          </View>

          {selectedCustomer ? (
            <View style={styles.customerInfo}>
              <View
                style={[
                  styles.customerAvatar,
                  { backgroundColor: primaryColor },
                ]}
              >
                <Text style={styles.customerAvatarText}>
                  {(
                    selectedCustomer.displayName ||
                    selectedCustomer.firstName ||
                    "U"
                  )
                    .charAt(0)
                    .toUpperCase()}
                </Text>
              </View>
              <View style={styles.customerDetails}>
                <Text style={[styles.customerName, { color: textColor }]}>
                  {selectedCustomer.displayName ||
                    `${selectedCustomer.firstName} ${selectedCustomer.lastName}`.trim() ||
                    "Unknown Customer"}
                </Text>
                {selectedCustomer.email && (
                  <Text
                    style={[styles.customerContact, { color: textSecondary }]}
                  >
                    {selectedCustomer.email}
                  </Text>
                )}
                {selectedCustomer.phone && (
                  <Text
                    style={[styles.customerContact, { color: textSecondary }]}
                  >
                    {selectedCustomer.phone}
                  </Text>
                )}
              </View>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCustomerModal(true)}
            >
              <IconSymbol name="plus.circle" size={24} color={primaryColor} />
              <Text style={[styles.selectButtonText, { color: primaryColor }]}>
                Select Customer
              </Text>
            </TouchableOpacity>
          )}
        </ModernCard>

        {/* Sales Agent Information */}
        <ModernCard style={styles.section} variant="elevated">
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              Sales Agent
            </Text>
            <TouchableOpacity
              onPress={() => setShowSalesAgentModal(true)}
              style={styles.changeButton}
            >
              <Text style={[styles.changeButtonText, { color: primaryColor }]}>
                {selectedAgent ? "Change" : "Select"}
              </Text>
            </TouchableOpacity>
          </View>

          {selectedAgent ? (
            <View style={styles.agentInfo}>
              <View
                style={[styles.agentAvatar, { backgroundColor: successColor }]}
              >
                <Text style={styles.agentAvatarText}>
                  {selectedAgent.name
                    .split(" ")
                    .map((n: string) => n.charAt(0))
                    .join("")
                    .slice(0, 2)}
                </Text>
              </View>
              <View style={styles.agentDetails}>
                <Text style={[styles.agentName, { color: textColor }]}>
                  {selectedAgent.name}
                </Text>
                <Text style={[styles.agentCommission, { color: successColor }]}>
                  {selectedAgent.commissionRate}% commission
                </Text>
                {selectedAgent.email && (
                  <Text style={[styles.agentContact, { color: textSecondary }]}>
                    {selectedAgent.email}
                  </Text>
                )}
              </View>
            </View>
          ) : (
            <View style={styles.noAgentInfo}>
              <Text style={[styles.noAgentText, { color: textSecondary }]}>
                No sales agent selected
              </Text>
              <Text style={[styles.noAgentSubtext, { color: textSecondary }]}>
                Commission will not be tracked for this sale
              </Text>
            </View>
          )}
        </ModernCard>

        {/* Payment Method */}
        <ModernCard style={styles.section} variant="elevated">
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Payment Method
          </Text>

          <View style={styles.paymentMethods}>
            {[
              { id: "cash", label: "Cash", icon: "banknote" },
              { id: "credit", label: "Credit", icon: "creditcard" },
              {
                id: "absa_till",
                label: "ABSA Till",
                icon: "phone",
              },
            ].map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentMethod,
                  { borderColor: borderColor },
                  paymentMethod === method.id && {
                    borderColor: primaryColor,
                    backgroundColor: `${primaryColor}10`,
                  },
                ]}
                onPress={() => setPaymentMethod(method.id as any)}
              >
                <IconSymbol
                  name={method.icon as any}
                  size={24}
                  color={
                    paymentMethod === method.id ? primaryColor : textSecondary
                  }
                />
                <Text
                  style={[
                    styles.paymentMethodText,
                    {
                      color:
                        paymentMethod === method.id ? primaryColor : textColor,
                    },
                  ]}
                >
                  {method.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Split Payment Option */}
          <TouchableOpacity
            style={[
              styles.splitPaymentButton,
              {
                borderColor: primaryColor,
                backgroundColor: `${primaryColor}08`,
              },
            ]}
            onPress={() => setShowSplitPaymentModal(true)}
            disabled={!selectedCustomer || cartItems.length === 0}
          >
            <IconSymbol
              name="creditcard.and.123"
              size={24}
              color={primaryColor}
            />
            <View style={styles.splitPaymentContent}>
              <Text style={[styles.splitPaymentTitle, { color: primaryColor }]}>
                Split Payment
              </Text>
              <Text
                style={[styles.splitPaymentSubtitle, { color: textSecondary }]}
              >
                Pay with multiple methods
              </Text>
            </View>
            <IconSymbol name="chevron.right" size={16} color={primaryColor} />
          </TouchableOpacity>

          {/* M-Pesa Till Transaction Code Input */}
          {paymentMethod === "mpesa_till" && (
            <View style={styles.transactionCodeSection}>
              <Text style={[styles.inputLabel, { color: textColor }]}>
                M-Pesa Transaction Code *
              </Text>
              <TextInput
                style={[
                  styles.transactionCodeInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={transactionCode}
                onChangeText={setTransactionCode}
                placeholder="Enter M-Pesa transaction code"
                placeholderTextColor={textSecondary}
                autoCapitalize="characters"
                selectTextOnFocus
              />
              <Text style={[styles.inputHint, { color: textSecondary }]}>
                Enter the transaction code you received from M-Pesa
              </Text>
            </View>
          )}
        </ModernCard>

        {/* Place Order Button */}
        <View style={styles.checkoutActions}>
          <ModernButton
            title={isProcessing ? "Processing..." : "Place Order"}
            onPress={handlePlaceOrder}
            loading={isProcessing}
            disabled={
              isProcessing ||
              cartItems.length === 0 ||
              !selectedCustomer ||
              (paymentMethod === "mpesa_till" && !transactionCode.trim())
            }
            size="lg"
            style={styles.placeOrderButton}
          />

          <Text style={[styles.disclaimer, { color: textSecondary }]}>
            By placing this order, you confirm that all information is correct
          </Text>
        </View>
      </ScrollView>

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        onClose={handleSuccessModalDismiss}
        title={modalData.title}
        message={`${modalData.message}\n\nTotal: KSh ${
          modalData.orderTotal?.toFixed(2) || "0.00"
        }`}
        actions={[
          {
            title: "Print Receipt",
            onPress: handlePrintReceiptFromSuccess,
            variant: "outline",
            icon: "printer",
            dismissModal: false, // Don't dismiss - let user see print result
          },
          {
            title: "View Orders",
            onPress: handleViewOrdersFromSuccess,
            variant: "outline",
            icon: "list.bullet",
            dismissModal: true, // Dismiss and navigate
          },
          {
            title: "New Sale",
            onPress: handleNewSaleFromSuccess,
            variant: "primary",
            icon: "plus.circle",
            dismissModal: true, // Dismiss and navigate
          },
          {
            title: "View Receipt",
            onPress: handleViewReceiptFromSuccess,
            variant: "outline",
            icon: "doc.text",
            dismissModal: true, // Dismiss and navigate
          },
        ]}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title={modalData.title}
        message={modalData.message}
        showRetryButton={modalData.message.includes("Failed to place order")}
        onRetry={() => handlePlaceOrder()}
      />

      {/* Confirmation Modal for Printer Setup */}
      <ConfirmationModal
        visible={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        title={modalData.title}
        message={modalData.message}
        icon="printer"
        actions={[
          {
            title: "Cancel",
            onPress: () => setShowConfirmationModal(false),
            variant: "outline",
          },
          {
            title: "Setup Printer",
            onPress: () => router.push("/thermal-printer-setup"),
            variant: "primary",
            icon: "gear",
          },
        ]}
      />

      {/* Order Completion Modal */}
      {completionOrderData && (
        <OrderCompletionModal
          visible={showOrderCompletionModal}
          onClose={() => setShowOrderCompletionModal(false)}
          onComplete={handleOrderCompletion}
          orderData={completionOrderData.orderData}
          orderNumber={completionOrderData.orderNumber}
          orderTotal={completionOrderData.orderTotal}
          paymentMethod={completionOrderData.paymentMethod}
          transactionId={completionOrderData.transactionId}
          printingAlreadyAttempted={
            completionOrderData.printingAlreadyAttempted
          }
          printingWasSuccessful={completionOrderData.printingWasSuccessful}
        />
      )}

      {/* Pre-Order Printer Check Modal */}
      <PreOrderPrinterCheckModal
        visible={showPreOrderPrinterCheck}
        onClose={() => setShowPreOrderPrinterCheck(false)}
        orderTotal={cartTotal}
        paymentMethod={paymentMethod === "mpesa_till" ? "M-Pesa Till" : "Cash"}
        onPlaceOrder={handlePlaceOrderInModal}
        onOrderComplete={handleOrderCompletion}
      />

      {/* Customer Selection Modal */}
      <Modal
        visible={showCustomerModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCustomerModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: borderColor }]}
          >
            <Text style={[styles.modalTitle, { color: textColor }]}>
              Select Customer
            </Text>
            <TouchableOpacity
              onPress={() => setShowCustomerModal(false)}
              style={styles.modalCloseButton}
            >
              <IconSymbol name="xmark" size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          <View style={styles.modalSearchContainer}>
            <TextInput
              style={[
                styles.modalSearchInput,
                {
                  borderColor: borderColor,
                  color: textColor,
                  backgroundColor: theme.colors.surface,
                },
              ]}
              value={customerSearchQuery}
              onChangeText={(text) => {
                setCustomerSearchQuery(text);
                loadCustomers(text);
              }}
              placeholder="Search customers..."
              placeholderTextColor={textSecondary}
              autoFocus
            />
          </View>

          {isLoadingCustomers ? (
            <View style={styles.modalLoadingContainer}>
              <ActivityIndicator size="large" color={primaryColor} />
              <Text style={[styles.modalLoadingText, { color: textSecondary }]}>
                Loading customers...
              </Text>
            </View>
          ) : (
            <FlatList
              data={customers}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.modalListItem,
                    { borderBottomColor: borderColor },
                  ]}
                  onPress={() => handleCustomerSelect(item)}
                >
                  <View
                    style={[
                      styles.modalItemAvatar,
                      { backgroundColor: primaryColor },
                    ]}
                  >
                    <Text style={styles.modalItemAvatarText}>
                      {(item.firstName || item.displayName || "U")
                        .charAt(0)
                        .toUpperCase()}
                    </Text>
                  </View>
                  <View style={styles.modalItemInfo}>
                    <Text style={[styles.modalItemName, { color: textColor }]}>
                      {item.displayName ||
                        `${item.firstName || ""} ${
                          item.lastName || ""
                        }`.trim() ||
                        "Unknown Customer"}
                    </Text>
                    {item.email && (
                      <Text
                        style={[
                          styles.modalItemContact,
                          { color: textSecondary },
                        ]}
                      >
                        {item.email}
                      </Text>
                    )}
                    {item.phone && (
                      <Text
                        style={[
                          styles.modalItemContact,
                          { color: textSecondary },
                        ]}
                      >
                        {item.phone}
                      </Text>
                    )}
                  </View>
                  <IconSymbol
                    name="chevron.right"
                    size={16}
                    color={textSecondary}
                  />
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.modalListContainer}
              showsVerticalScrollIndicator={false}
            />
          )}

          {/* Floating Action Button for Customer Creation */}
          <FloatingActionButton
            iconName="person.badge.plus"
            onPress={() => setShowCustomerCreationModal(true)}
            accessibilityLabel="Create new customer"
            bottom={theme.spacing.xl}
            right={theme.spacing.xl}
          />
        </View>
      </Modal>

      {/* Sales Agent Selection Modal */}
      <Modal
        visible={showSalesAgentModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSalesAgentModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: borderColor }]}
          >
            <Text style={[styles.modalTitle, { color: textColor }]}>
              Select Sales Agent
            </Text>
            <TouchableOpacity
              onPress={() => setShowSalesAgentModal(false)}
              style={styles.modalCloseButton}
            >
              <IconSymbol name="xmark" size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          <View style={styles.modalSearchContainer}>
            <TextInput
              style={[
                styles.modalSearchInput,
                {
                  borderColor: borderColor,
                  color: textColor,
                  backgroundColor: theme.colors.surface,
                },
              ]}
              value={agentSearchQuery}
              onChangeText={(text) => {
                setAgentSearchQuery(text);
                loadSalesAgents(text);
              }}
              placeholder="Search sales agents..."
              placeholderTextColor={textSecondary}
              autoFocus
            />
          </View>

          {isLoadingSalesAgents ? (
            <View style={styles.modalLoadingContainer}>
              <ActivityIndicator size="large" color={primaryColor} />
              <Text style={[styles.modalLoadingText, { color: textSecondary }]}>
                Loading sales agents...
              </Text>
            </View>
          ) : (
            <FlatList
              data={salesAgents}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.modalListItem,
                    { borderBottomColor: borderColor },
                  ]}
                  onPress={() => handleSalesAgentSelect(item)}
                >
                  <View
                    style={[
                      styles.modalItemAvatar,
                      { backgroundColor: successColor },
                    ]}
                  >
                    <Text style={styles.modalItemAvatarText}>
                      {item.name
                        .split(" ")
                        .map((n: string) => n.charAt(0))
                        .join("")
                        .slice(0, 2)}
                    </Text>
                  </View>
                  <View style={styles.modalItemInfo}>
                    <Text style={[styles.modalItemName, { color: textColor }]}>
                      {item.name}
                    </Text>
                    <Text
                      style={[
                        styles.modalItemCommission,
                        { color: successColor },
                      ]}
                    >
                      {item.commissionRate}% commission
                    </Text>
                    {item.email && (
                      <Text
                        style={[
                          styles.modalItemContact,
                          { color: textSecondary },
                        ]}
                      >
                        {item.email}
                      </Text>
                    )}
                  </View>
                  <IconSymbol
                    name="chevron.right"
                    size={16}
                    color={textSecondary}
                  />
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.modalListContainer}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </Modal>

      {/* Split Payment Modal */}
      <SplitPaymentModal
        visible={showSplitPaymentModal}
        onClose={() => setShowSplitPaymentModal(false)}
        totalAmount={cartTotal}
        onPaymentComplete={handleSplitPaymentComplete}
        staffInfo={{
          id: user?.id || "",
          name: user?.name || "",
          terminal: "Current Terminal",
        }}
        customerInfo={
          selectedCustomer
            ? {
                name:
                  selectedCustomer.displayName ||
                  `${selectedCustomer.firstName} ${selectedCustomer.lastName}`.trim() ||
                  "Walk-in Customer",
                phone: selectedCustomer.phone || "",
              }
            : undefined
        }
      />

      {/* Customer Creation Modal */}
      <Modal
        visible={showCustomerCreationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCustomerCreationModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: borderColor }]}
          >
            <Text style={[styles.modalTitle, { color: textColor }]}>
              Create New Customer
            </Text>
            <TouchableOpacity
              onPress={() => setShowCustomerCreationModal(false)}
              style={styles.modalCloseButton}
            >
              <IconSymbol name="xmark" size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.customerFormContainer}>
            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                First Name *
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.firstName}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, firstName: text })
                }
                placeholder="Enter first name"
                placeholderTextColor={textSecondary}
                autoFocus
              />
            </View>

            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                Last Name *
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.lastName}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, lastName: text })
                }
                placeholder="Enter last name"
                placeholderTextColor={textSecondary}
              />
            </View>

            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                Email
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.email}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, email: text })
                }
                placeholder="Enter email address"
                placeholderTextColor={textSecondary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                Phone
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.phone}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, phone: text })
                }
                placeholder="Enter phone number"
                placeholderTextColor={textSecondary}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.customerFormActions}>
              <TouchableOpacity
                style={[
                  styles.customerFormButton,
                  styles.customerFormCancelButton,
                  { borderColor: borderColor },
                ]}
                onPress={() => setShowCustomerCreationModal(false)}
              >
                <Text
                  style={[styles.customerFormButtonText, { color: textColor }]}
                >
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.customerFormButton,
                  styles.customerFormCreateButton,
                  { backgroundColor: primaryColor },
                ]}
                onPress={handleCreateCustomer}
                disabled={isCreatingCustomer}
              >
                {isCreatingCustomer ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.customerFormCreateButtonText}>
                    Create
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </ScreenWrapper>
  );
};

// Create styles function that uses theme
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    section: {
      margin: theme.spacing.md,
    },
    orderSummaryCard: {
      margin: theme.spacing.md,
      marginBottom: theme.spacing.lg,
    },
    orderSummaryHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    itemCountBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.round,
      minWidth: 28,
      height: 28,
      alignItems: "center",
      justifyContent: "center",
    },
    itemCountText: {
      ...theme.typography.caption,
      color: "#FFFFFF",
      fontWeight: "600",
      fontSize: 12,
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
      marginBottom: theme.spacing.lg,
    },
    sectionSubtitle: {
      ...theme.typography.caption,
      marginBottom: theme.spacing.md,
    },
    changeButton: {
      padding: theme.spacing.sm,
    },
    changeButtonText: {
      ...theme.typography.bodyMedium,
    },
    cartItems: {
      marginBottom: theme.spacing.md,
    },
    cartItem: {
      paddingVertical: theme.spacing.md,
    },
    cartItemWithBorder: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    itemTitle: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.xs,
      fontWeight: "500",
    },
    itemBottomRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    itemQuantityPrice: {
      ...theme.typography.caption,
      flex: 0,
      minWidth: 120,
    },
    dottedLine: {
      overflow: "hidden",
    },
    dots: {
      ...theme.typography.caption,
      letterSpacing: 1,
      opacity: 0.5,
    },
    itemTotal: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      textAlign: "right",
      minWidth: 80,
    },
    totalRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
    },
    totalLabel: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
    },
    totalAmount: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.h2.fontSize,
      fontWeight: "600",
    },
    customerInfo: {
      flexDirection: "row",
      alignItems: "center",
    },
    customerAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.md,
    },
    customerAvatarText: {
      ...theme.typography.bodyMedium,
      color: "#FFFFFF",
    },
    customerDetails: {
      flex: 1,
    },
    customerName: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
    },
    customerContact: {
      ...theme.typography.caption,
      marginBottom: 2,
    },
    selectButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.lg,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderStyle: "dashed",
      borderRadius: theme.borderRadius.sm,
      gap: theme.spacing.sm,
    },
    selectButtonText: {
      ...theme.typography.bodyMedium,
    },
    agentInfo: {
      flexDirection: "row",
      alignItems: "center",
    },
    agentAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.md,
    },
    agentAvatarText: {
      ...theme.typography.bodyMedium,
      color: "#FFFFFF",
    },
    agentDetails: {
      flex: 1,
    },
    agentName: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
    },
    agentCommission: {
      ...theme.typography.caption,
      marginBottom: 2,
    },
    agentContact: {
      ...theme.typography.caption,
    },
    noAgentInfo: {
      padding: theme.spacing.lg,
      alignItems: "center",
    },
    noAgentText: {
      ...theme.typography.body,
      marginBottom: 4,
    },
    noAgentSubtext: {
      ...theme.typography.caption,
      textAlign: "center",
    },
    paymentMethods: {
      flexDirection: "row",
      gap: theme.spacing.md,
    },
    paymentMethod: {
      flex: 1,
      alignItems: "center",
      padding: theme.spacing.md,
      borderWidth: 2,
      borderRadius: theme.borderRadius.sm,
      gap: theme.spacing.sm,
    },
    paymentMethodText: {
      ...theme.typography.caption,
    },
    checkoutActions: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    placeOrderButton: {
      marginBottom: theme.spacing.md,
    },
    disclaimer: {
      ...theme.typography.small,
      textAlign: "center",
      lineHeight: 18,
    },
    transactionCodeSection: {
      marginTop: theme.spacing.md,
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    inputLabel: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.sm,
      fontWeight: "500",
    },
    transactionCodeInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.md,
      fontSize: 16,
      marginBottom: theme.spacing.sm,
    },
    inputHint: {
      ...theme.typography.caption,
      fontStyle: "italic",
    },
    splitPaymentButton: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.md,
      marginTop: theme.spacing.md,
      borderWidth: 2,
      borderRadius: theme.borderRadius.sm,
      borderStyle: "dashed",
    },
    splitPaymentContent: {
      flex: 1,
      marginLeft: theme.spacing.md,
    },
    splitPaymentTitle: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      marginBottom: 2,
    },
    splitPaymentSubtitle: {
      ...theme.typography.caption,
    },
    // Modal styles
    modalContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: theme.spacing.lg,
      borderBottomWidth: 1,
    },
    modalTitle: {
      ...theme.typography.h3,
      fontWeight: "600",
    },
    modalCloseButton: {
      padding: theme.spacing.sm,
    },
    modalSearchContainer: {
      padding: theme.spacing.lg,
      paddingBottom: theme.spacing.md,
    },
    modalSearchInput: {
      ...theme.typography.body,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
    },
    modalLoadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    modalLoadingText: {
      ...theme.typography.body,
      marginTop: theme.spacing.md,
    },
    modalListContainer: {
      paddingHorizontal: theme.spacing.lg,
    },
    modalListItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: theme.spacing.lg,
      borderBottomWidth: 1,
    },
    modalItemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.md,
    },
    modalItemAvatarText: {
      ...theme.typography.bodyMedium,
      color: "#FFFFFF",
      fontWeight: "600",
    },
    modalItemInfo: {
      flex: 1,
    },
    modalItemName: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
      fontWeight: "500",
    },
    modalItemContact: {
      ...theme.typography.caption,
      marginBottom: 2,
    },
    modalItemCommission: {
      ...theme.typography.caption,
      marginBottom: 2,
      fontWeight: "600",
    },
    // Customer creation form styles
    customerFormContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },
    customerFormField: {
      marginBottom: theme.spacing.lg,
    },
    customerFormLabel: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
    },
    customerFormInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.md,
      fontSize: 16,
    },
    customerFormActions: {
      flexDirection: "row",
      gap: theme.spacing.md,
      marginTop: theme.spacing.xl,
      paddingBottom: theme.spacing.xl,
    },
    customerFormButton: {
      flex: 1,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 48,
    },
    customerFormCancelButton: {
      borderWidth: 1,
    },
    customerFormCreateButton: {
      // backgroundColor set dynamically
    },
    customerFormButtonText: {
      ...theme.typography.body,
      fontWeight: "500",
    },
    customerFormCreateButtonText: {
      ...theme.typography.body,
      fontWeight: "600",
      color: "#FFFFFF",
    },
  });

export default CheckoutScreen;
