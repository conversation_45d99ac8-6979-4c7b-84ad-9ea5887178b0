import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import EnhancedThermalPrintService from "@/src/services/EnhancedThermalPrintService";
import ThermalPrintService from "@/src/services/ThermalPrintService";
import { UnifiedReceiptManager } from "@/src/services/UnifiedReceiptManager";
import { BluetoothPermissionHelper } from "@/src/utils/BluetoothPermissionHelper";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";

export interface PreOrderPrinterCheckResult {
  proceed: boolean;
  printerAvailable: boolean;
  userChoice: "setup" | "proceed_without" | "cancel";
}

interface PreOrderPrinterCheckModalProps {
  visible: boolean;
  onClose: () => void;
  onResult?: (result: PreOrderPrinterCheckResult) => void; // Made optional since we handle everything inline
  orderTotal: number;
  paymentMethod: string;
  orderData?: any; // Optional order data for printing
  onOrderComplete?: (orderData: any) => void; // Callback when order is completed
  onPlaceOrder?: () => Promise<any>; // Function to place the order
}

type ModalStep =
  | "checking"
  | "printer_available"
  | "printer_setup_required"
  | "order_processing"
  | "printer_scanning"
  | "printer_device_list"
  | "printer_connecting"
  | "printer_testing"
  | "printer_setup_success"
  | "printer_setup_error"
  | "order_success"
  | "order_error"
  | "printing_receipt"
  | "print_success"
  | "print_error";

export function PreOrderPrinterCheckModal({
  visible,
  onClose,
  onResult,
  orderTotal,
  paymentMethod,
  orderData: propOrderData,
  onOrderComplete,
  onPlaceOrder,
}: PreOrderPrinterCheckModalProps) {
  const router = useRouter();
  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");
  const successColor = "#10B981";
  const warningColor = "#F59E0B";

  const [currentStep, setCurrentStep] = useState<ModalStep>("checking");
  const [printerAvailable, setPrinterAvailable] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Printer setup state
  const [availableDevices, setAvailableDevices] = useState<any[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<any>(null);
  const [connectedPrinter, setConnectedPrinter] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>("");

  // Store order data internally when order is placed
  const [internalOrderData, setInternalOrderData] = useState<any>(null);
  // Use the order data from props or internal state
  const orderData = propOrderData || internalOrderData;
  const [successMessage, setSuccessMessage] = useState<string>(
    "Your order has been completed successfully!"
  );

  // ORDER STATE MANAGEMENT - Prevent duplicate orders and false sales
  const [orderPlaced, setOrderPlaced] = useState(false);
  const [orderPlacementInProgress, setOrderPlacementInProgress] =
    useState(false);
  const [placedOrderId, setPlacedOrderId] = useState<string | null>(null);

  useEffect(() => {
    if (visible) {
      initializePrinterAndCheck();
    }
    // REMOVED: resetOrderState() on modal close to prevent clearing cart during navigation
    // Order state should only be reset when explicitly closing the modal or completing the order
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // Reset order state to prevent issues on next modal open
  const resetOrderState = () => {
    console.log("🔄 Resetting order state");
    setOrderPlaced(false);
    setOrderPlacementInProgress(false);
    setPlacedOrderId(null);
    setInternalOrderData(null);
    setErrorMessage("");
    setSuccessMessage("Your order has been completed successfully!");
  };

  // Platform-aware printer initialization
  const initializePrinterAndCheck = async () => {
    setCurrentStep("checking");
    setIsProcessing(true);

    try {
      console.log("Initializing printer service for platform:", Platform.OS);

      if (Platform.OS === "web") {
        // Web platform: Use CrossPlatformPrintService
        console.log("Web platform detected - using CrossPlatformPrintService");

        // Initialize cross-platform print service
        const initialized = await CrossPlatformPrintService.init();
        console.log("CrossPlatformPrintService initialized:", initialized);

        // Check printer status
        const printerStatus =
          await CrossPlatformPrintService.getPrinterStatus();
        console.log("Web printer status:", printerStatus);

        if (printerStatus.available) {
          setPrinterAvailable(true);
          setCurrentStep("printer_available");

          // AUTOMATIC PRINTING: If we have onPlaceOrder, proceed automatically
          if (onPlaceOrder) {
            setTimeout(() => {
              handleProceedWithPrinter();
            }, 1500);
          }
          return;
        } else {
          // Web printing is always available (browser print dialog)
          console.log("Web printing available via browser dialog");
          setPrinterAvailable(true);
          setCurrentStep("printer_available");

          if (onPlaceOrder) {
            setTimeout(() => {
              handleProceedWithPrinter();
            }, 1500);
          }
          return;
        }
      } else {
        // Mobile platform: Use ThermalPrintService
        console.log("Mobile platform detected - using ThermalPrintService");

        // Step 1: Initialize ThermalPrintService (same as ThermalPrinterSetup.tsx)
        await ThermalPrintService.init();

        // Step 2: Check if there's already a connected device
        const connectedDevice = ThermalPrintService.getConnectedDevice();
        console.log("Connected device from init:", connectedDevice);

        if (connectedDevice) {
          // Step 3: Verify the connection is still valid
          const isVerified =
            await ThermalPrintService.verifyPrinterConnection();
          console.log("Connection verified:", isVerified);

          if (isVerified) {
            // Step 4: Check if it's the P502A printer
            const isP502A = await ThermalPrintService.isConnectedToP502A();
            console.log("Is P502A connected:", isP502A);

            if (isP502A) {
              setPrinterAvailable(true);
              setCurrentStep("printer_available");

              // AUTOMATIC PRINTING: If we have onPlaceOrder, proceed automatically
              if (onPlaceOrder) {
                setTimeout(() => {
                  handleProceedWithPrinter();
                }, 1500);
              }
              return;
            }
          }
        }

        // If we get here, no verified connection - show setup required
        console.log("No verified printer connection found");
        setPrinterAvailable(false);
        setCurrentStep("printer_setup_required");
      }
    } catch (error) {
      console.error("Error initializing printer:", error);

      if (Platform.OS === "web") {
        // On web, always allow proceeding (browser print dialog available)
        console.log("Web platform - allowing proceed despite error");
        setPrinterAvailable(true);
        setCurrentStep("printer_available");

        if (onPlaceOrder) {
          setTimeout(() => {
            handleProceedWithPrinter();
          }, 1500);
        }
      } else {
        // On mobile, show setup required
        setPrinterAvailable(false);
        setCurrentStep("printer_setup_required");
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleProceedWithPrinter = async () => {
    // If we have onPlaceOrder, handle the complete flow inline
    if (onPlaceOrder) {
      await handlePlaceOrderAndPrint();
    } else if (onResult) {
      // Legacy behavior for backward compatibility
      onResult({
        proceed: true,
        printerAvailable: true,
        userChoice: "proceed_without", // Will print automatically
      });
    }
  };

  const handlePlaceOrderAndPrint = async () => {
    if (!onPlaceOrder) return;

    // PREVENT DUPLICATE ORDERS - Check if order is already placed or in progress
    if (orderPlaced || orderPlacementInProgress) {
      console.log(
        "Order already placed or in progress, skipping duplicate order placement"
      );
      console.log("Order state:", {
        orderPlaced,
        orderPlacementInProgress,
        placedOrderId,
      });

      // If order is already placed, just handle printing using existing method
      if (orderPlaced && orderData) {
        console.log(
          "Order already placed, checking if we should print receipt..."
        );
        if (Platform.OS === "web") {
          // Web platform - always proceed to print (browser dialog available)
          console.log("Web platform - proceeding to print with browser dialog");
          await handlePrintReceipt();
        } else {
          // Mobile platform - check thermal printer connection
          const isCurrentlyConnected =
            ThermalPrintService.isConnectedToPrinter();
          const isP502AConnected =
            await ThermalPrintService.isConnectedToP502A();

          if (isCurrentlyConnected && isP502AConnected) {
            console.log(
              "Printer available, calling handlePrintReceipt() for existing order"
            );
            await handlePrintReceipt();
          } else {
            console.log(
              "No printer, going to order success for existing order"
            );
            setCurrentStep("order_success");
            setSuccessMessage(
              "Order completed successfully! Receipt can be printed later from Orders."
            );
          }
        }
        return;
      }

      // If order is in progress, just wait
      return;
    }

    console.log("Starting order placement...");
    setOrderPlacementInProgress(true);
    setCurrentStep("order_processing");
    setIsProcessing(true);

    try {
      // Place the order - THIS SHOULD ONLY HAPPEN ONCE
      console.log("Calling onPlaceOrder() - PLACING ACTUAL ORDER");
      const orderResult = await onPlaceOrder();

      if (orderResult && orderResult.success) {
        // MARK ORDER AS SUCCESSFULLY PLACED - Prevent duplicate orders
        setOrderPlaced(true);
        setOrderPlacementInProgress(false);
        setPlacedOrderId(
          orderResult.orderData?.id ||
            orderResult.orderData?.order_id ||
            `order_${Date.now()}`
        );

        // Store order data for printing
        setInternalOrderData(orderResult.orderData);

        console.log("✅ ORDER SUCCESSFULLY PLACED:", {
          orderId: orderResult.orderData?.id || orderResult.orderData?.order_id,
          orderTotal:
            orderResult.orderData?.total || orderResult.orderData?.total_price,
          timestamp: new Date().toISOString(),
        });

        // AUTOMATIC PRINTING: Platform-aware printer check and printing
        if (Platform.OS === "web") {
          // Web platform - always proceed to print (browser dialog available)
          console.log(
            "✅ Web platform - automatically printing with browser dialog..."
          );
          await handlePrintReceiptWithData(orderResult.orderData);
        } else {
          // Mobile platform - check thermal printer connection
          const isCurrentlyConnected =
            ThermalPrintService.isConnectedToPrinter();
          const isP502AConnected =
            await ThermalPrintService.isConnectedToP502A();

          if (isCurrentlyConnected && isP502AConnected) {
            // Printer is connected - automatically print using the fresh order data
            console.log(
              "✅ Printer detected, automatically printing with fresh order data..."
            );
            await handlePrintReceiptWithData(orderResult.orderData);
          } else {
            // No printer - go to order success with manual print option
            console.log("ℹ️ No printer detected, going to order success");
            setCurrentStep("order_success");
            setSuccessMessage(
              "Order completed successfully! Receipt can be printed later from Orders."
            );
          }
        }
      } else {
        // Order failed - Reset order placement state
        console.log("❌ ORDER PLACEMENT FAILED:", orderResult?.error);
        setOrderPlacementInProgress(false);
        setOrderPlaced(false);
        setPlacedOrderId(null);
        setErrorMessage(orderResult?.error || "Failed to place order");
        setCurrentStep("order_error");
      }
    } catch (error) {
      // Order placement error - Reset order placement state
      console.error("❌ ORDER PLACEMENT ERROR:", error);
      setOrderPlacementInProgress(false);
      setOrderPlaced(false);
      setPlacedOrderId(null);
      setErrorMessage("Failed to place order. Please try again.");
      setCurrentStep("order_error");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSetupPrinter = () => {
    console.log(
      "Setup Printer button clicked, starting enhanced setup with initialization"
    );

    if (Platform.OS === "web") {
      // On web, redirect to printer setup page or show web printer info
      console.log("Web platform - redirecting to web printer setup");
      router.push("/printer-setup");
      handleExplicitClose();
    } else {
      // On mobile, start thermal printer setup
      setCurrentStep("printer_scanning");
      startEnhancedPrinterSetup();
    }
  };

  const startEnhancedPrinterSetup = async () => {
    if (Platform.OS === "web") {
      // Web platform doesn't need thermal printer setup
      console.log("Web platform - skipping thermal printer setup");
      return;
    }

    setCurrentStep("printer_scanning");
    setIsProcessing(true);
    setErrorMessage("");

    try {
      console.log(
        "🔄 Step 1: Trying to reinitialize printer service (printer might be back online)..."
      );

      // STEP 1: Try to reinitialize - printer might be back online now
      await ThermalPrintService.init();

      // Check if there's a connected device after reinitialization
      const connectedDevice = ThermalPrintService.getConnectedDevice();
      console.log("Connected device after reinit:", connectedDevice);

      if (connectedDevice) {
        // Verify the connection is still valid
        const isVerified = await ThermalPrintService.verifyPrinterConnection();
        console.log("Connection verified after reinit:", isVerified);

        if (isVerified) {
          // Check if it's the P502A printer
          const isP502A = await ThermalPrintService.isConnectedToP502A();
          console.log("Is P502A connected after reinit:", isP502A);

          if (isP502A) {
            console.log("✅ Printer reconnected successfully during setup!");
            setPrinterAvailable(true);
            setCurrentStep("printer_setup_success");
            setConnectedPrinter({
              name: connectedDevice.name,
              address: connectedDevice.address,
              isConnected: true,
            });
            return;
          }
        }
      }

      console.log(
        "🔍 Step 2: Reinitialization didn't find printer, falling back to device scanning..."
      );

      // STEP 2: If reinitialization didn't work, fall back to scanning
      await scanForThermalPrinters();
    } catch (error) {
      console.error("Enhanced printer setup error:", error);
      setErrorMessage(
        error instanceof Error ? error.message : "Failed to setup printer"
      );
      setCurrentStep("printer_setup_error");
    } finally {
      setIsProcessing(false);
    }
  };

  const scanForThermalPrinters = async (): Promise<void> => {
    if (Platform.OS === "web") {
      // Web platform doesn't support Bluetooth scanning
      console.log("Web platform - skipping Bluetooth scanning");
      setErrorMessage(
        "Bluetooth scanning is not available on web. Please use the web printer setup page."
      );
      setCurrentStep("printer_setup_error");
      return;
    }

    try {
      // Check and request Bluetooth permissions
      const permissionResult =
        await BluetoothPermissionHelper.ensureBluetoothReady();
      if (!permissionResult.granted) {
        throw new Error(
          permissionResult.error ||
            "Bluetooth permissions are required to scan for printers."
        );
      }

      // Check if Bluetooth is enabled
      const bluetoothEnabled =
        await BluetoothPermissionHelper.isBluetoothEnabled();
      if (!bluetoothEnabled) {
        const enabled =
          await BluetoothPermissionHelper.showBluetoothEnableDialog();
        if (!enabled) {
          throw new Error("Please enable Bluetooth to scan for printers.");
        }
      }

      console.log(
        "🔍 Scanning for ALL Bluetooth devices (like ThermalPrinterSetup.tsx showAllDevices)..."
      );

      // Use scanAllDevices to get ALL Bluetooth devices (same as ThermalPrinterSetup.tsx showAllDevices)
      const foundDevices = await ThermalPrintService.scanAllDevices();

      // Convert to our interface format and prioritize paired devices (ALL devices, not just printers)
      const allBluetoothDevices = foundDevices.map((device: any) => ({
        id: device.address || device.id || `device_${Date.now()}`,
        name: device.name || "Unknown Device",
        address: device.address || "",
        isConnected: device.isConnected || false,
        paired: device.paired || false,
      }));

      // Sort devices: paired devices first, then by name (same as ThermalPrinterSetup.tsx)
      const sortedDevices = allBluetoothDevices.sort((a, b) => {
        // Paired devices first
        if (a.paired && !b.paired) return -1;
        if (!a.paired && b.paired) return 1;
        // Then sort by name
        return a.name.localeCompare(b.name);
      });

      console.log(
        `📱 Found ${sortedDevices.length} Bluetooth devices:`,
        sortedDevices.map(
          (d) =>
            `${d.name} (${d.address}) - ${d.paired ? "Paired" : "Discoverable"}`
        )
      );

      setAvailableDevices(sortedDevices);

      if (sortedDevices.length > 0) {
        setCurrentStep("printer_device_list");
      } else {
        // ENHANCED ERROR: Better guidance for users (same as ThermalPrinterSetup.tsx)
        const pairedDevicesCount = sortedDevices.filter((d) => d.paired).length;
        const errorMsg =
          pairedDevicesCount > 0
            ? "Found paired devices but they appear to be offline. Make sure your printer is turned on and try again."
            : "No Bluetooth devices found. Please:\n1. Turn on your printer\n2. Pair it in Android Bluetooth settings first\n3. Try scanning again";

        setErrorMessage(errorMsg);
        setCurrentStep("printer_setup_error");
      }
    } catch (error) {
      console.error("❌ Thermal printer scan error:", error);

      // FALLBACK: Try to get just paired devices if scanning fails
      console.log(
        "🔄 Scan failed, trying to get paired devices as fallback..."
      );
      try {
        await getPairedDevicesAsFallback();
      } catch (fallbackError) {
        console.error("❌ Fallback also failed:", fallbackError);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to scan for printers. Please check Bluetooth is enabled and try again.";
        setErrorMessage(errorMessage);
        setCurrentStep("printer_setup_error");
      }
    }
  };

  // FALLBACK: Get paired devices when scanning fails
  const getPairedDevicesAsFallback = async (): Promise<void> => {
    try {
      console.log(
        "🔄 Getting ALL paired Bluetooth devices as fallback (like settings page)..."
      );

      // Use scanAllDevices to get ALL Bluetooth devices (same as ThermalPrinterSetup.tsx showAllDevices)
      const allDevices = await ThermalPrintService.scanAllDevices();

      // Filter for paired devices only (includes phones, audio devices, printers, etc.)
      const pairedDevices = allDevices.filter((device: any) => device.paired);

      // Convert to our interface format - keep ALL paired devices, not just printers
      const allPairedDevices = pairedDevices.map((device: any) => ({
        id: device.address || device.id || `device_${Date.now()}`,
        name: device.name || "Unknown Device",
        address: device.address || "",
        isConnected: device.isConnected || false,
        paired: true, // These are all paired devices
      }));

      console.log(
        `📱 Fallback found ${allPairedDevices.length} paired Bluetooth devices:`,
        allPairedDevices.map((d) => `${d.name} (${d.address})`)
      );

      setAvailableDevices(allPairedDevices);

      if (allPairedDevices.length > 0) {
        setCurrentStep("printer_device_list");
      } else {
        setErrorMessage(
          "No paired Bluetooth devices found. Please pair your thermal printer in Android Bluetooth settings first, then try again."
        );
        setCurrentStep("printer_setup_error");
      }
    } catch (error) {
      throw error; // Re-throw to be handled by caller
    }
  };

  const connectToPrinter = async (device: any) => {
    setSelectedDevice(device);
    setCurrentStep("printer_connecting");
    setIsProcessing(true);

    try {
      // Actually connect to the printer using ThermalPrintService
      const connectionSuccess = await ThermalPrintService.connectToPrinter(
        device.address,
        device.port
      );

      if (connectionSuccess) {
        const connectedDevice = { ...device, isConnected: true };
        setConnectedPrinter(connectedDevice);

        // Proceed to testing
        await testPrinterConnection(connectedDevice);
      } else {
        throw new Error("Failed to establish connection");
      }
    } catch (error) {
      console.error("Printer connection error:", error);
      setErrorMessage(`Failed to connect to ${device.name}. Please try again.`);
      setCurrentStep("printer_setup_error");
    } finally {
      setIsProcessing(false);
    }
  };

  const testPrinterConnection = async (_device: any) => {
    setCurrentStep("printer_testing");
    setIsProcessing(true);

    try {
      // Test printer with ThermalPrintService test print
      const testResult = await ThermalPrintService.printTestReceipt();

      if (testResult.success) {
        // Also initialize EnhancedThermalPrintService so it knows about the connection
        try {
          await EnhancedThermalPrintService.testThermalPrint();
          console.log("EnhancedThermalPrintService initialized successfully");
        } catch (error) {
          console.log(
            "EnhancedThermalPrintService initialization note:",
            error
          );
          // Don't fail the setup if this doesn't work - the main connection is established
        }

        setCurrentStep("printer_setup_success");
        setPrinterAvailable(true);
      } else {
        setErrorMessage(
          testResult.error ||
            "Printer test failed. Please check the connection."
        );
        setCurrentStep("printer_setup_error");
      }
    } catch (error) {
      console.error("Printer test error:", error);
      setErrorMessage("Failed to test printer connection. Please try again.");
      setCurrentStep("printer_setup_error");
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePrinterSetupSuccess = () => {
    // Printer setup was successful, go back to printer available state
    setCurrentStep("printer_available");
    // Auto-proceed after showing success
    setTimeout(() => {
      handleProceedWithPrinter();
    }, 1500);
  };

  const handleRetryPrinterSetup = () => {
    // Use enhanced setup that tries initialization first
    startEnhancedPrinterSetup();
  };

  const handleBackToSetupRequired = () => {
    setCurrentStep("printer_setup_required");
    setErrorMessage("");
  };

  // SHARED PRINTING LOGIC: Can be called with specific order data or use state
  const handlePrintReceiptWithData = async (dataToUse?: any) => {
    const printData = dataToUse || orderData;

    // SAFETY CHECK: Only print receipts for successfully placed orders
    if (!printData) {
      console.log("❌ No order data available for printing");
      setErrorMessage("No order data available for printing");
      setCurrentStep("print_error");
      return;
    }

    if (!orderPlaced && !dataToUse) {
      console.log(
        "⚠️ Attempted to print receipt for order that hasn't been placed yet"
      );
      setErrorMessage("Cannot print receipt - order has not been placed yet");
      setCurrentStep("print_error");
      return;
    }

    // CRITICAL FIX: Extract loyalty completion data from order result
    let loyaltyCompletionData = undefined;
    if (printData?.loyaltyResult?.success) {
      loyaltyCompletionData = {
        pointsEarned: printData.loyaltyResult.pointsAdded || 0,
        newBalance: printData.loyaltyResult.newBalance || 0,
        membershipId: `TS${
          printData.customer?.id?.toString().slice(-8) || "GUEST"
        }`,
        tierStatus: {
          current: printData.loyaltyResult.newTier || "bronze",
          changed: printData.loyaltyResult.tierChanged || false,
          previous: printData.loyaltyResult.tierChanged
            ? printData.loyaltyResult.previousTier
            : undefined,
        },
      };
      console.log(
        "🔍 CRITICAL FIX: Extracted loyalty completion data for receipt:",
        loyaltyCompletionData
      );
      console.log(
        "🔍 Backend loyaltyResult in order data:",
        printData.loyaltyResult
      );
    }

    const orderId = printData?.id || printData?.order_id || placedOrderId;
    console.log("🖨️ Print receipt requested for order:", orderId);
    console.log("📄 Order data:", {
      id: printData?.id,
      order_id: printData?.order_id,
      total: printData?.total || printData?.total_price,
      hasLineItems: !!printData?.line_items?.length,
    });

    setCurrentStep("printing_receipt");
    setIsProcessing(true);

    try {
      // Use UnifiedReceiptManager for consistent printing across all platforms
      console.log("🖨️ Using UnifiedReceiptManager for receipt printing");

      const printResult = await UnifiedReceiptManager.generateReceipt(
        printData,
        {
          format: "thermal",
          autoPrint: true,
          printerType: Platform.OS === "web" ? "web" : "thermal",
        }
      );

      if (printResult.success && printResult.printed) {
        setCurrentStep("print_success");
        console.log("✅ Unified printing successful for order:", orderId);
      } else {
        setErrorMessage(printResult.error || "Failed to print receipt");
        setCurrentStep("print_error");
        console.log("❌ Unified printing failed:", printResult.error);
      }
    } catch (error) {
      console.error("❌ Print receipt error:", error);
      setErrorMessage("Failed to print receipt. Please try again.");
      setCurrentStep("print_error");
    } finally {
      setIsProcessing(false);
    }
  };

  // MANUAL PRINT: Uses state data (for "Print Receipt" button)
  const handlePrintReceipt = async () => {
    await handlePrintReceiptWithData();
  };

  const handleProceedWithoutPrinter = async () => {
    // If we have onPlaceOrder, handle the complete flow inline
    if (onPlaceOrder) {
      await handlePlaceOrderAndPrint();
    } else if (onResult) {
      // Legacy behavior for backward compatibility
      onResult({
        proceed: true,
        printerAvailable: false,
        userChoice: "proceed_without",
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "checking":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Checking Printer Status
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Please wait while we check for connected thermal printers...
            </Text>
          </View>
        );

      case "printer_available":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: successColor + "20" },
              ]}
            >
              <IconSymbol
                name="checkmark.circle.fill"
                size={32}
                color={successColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Printer Connected
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Thermal printer is ready. Your receipt will be printed
              automatically after order placement.
            </Text>
            <View style={styles.orderSummary}>
              <Text style={[styles.summaryText, { color: textSecondary }]}>
                Order Total: KSh {orderTotal.toFixed(2)}
              </Text>
              <Text style={[styles.summaryText, { color: textSecondary }]}>
                Payment: {paymentMethod}
              </Text>
            </View>
          </View>
        );

      case "printer_setup_required":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: warningColor + "20" },
              ]}
            >
              <IconSymbol name="printer" size={32} color={warningColor} />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              No Printer Connected
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              No thermal printer is currently connected. You can set up a
              printer now or proceed without printing. Receipts can be printed
              later from the Orders screen.
            </Text>

            <View style={styles.orderSummary}>
              <Text style={[styles.summaryText, { color: textSecondary }]}>
                Order Total: KSh {orderTotal.toFixed(2)}
              </Text>
              <Text style={[styles.summaryText, { color: textSecondary }]}>
                Payment: {paymentMethod}
              </Text>
            </View>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Setup Printer"
                onPress={handleSetupPrinter}
                variant="primary"
                icon={<IconSymbol name="gear" size={16} color="white" />}
                style={styles.actionButton}
              />

              <ModernButton
                title="Proceed Without Printer"
                onPress={handleProceedWithoutPrinter}
                variant="outline"
                icon={
                  <IconSymbol
                    name="arrow.right"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "order_processing":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Processing Order
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Please wait while we process your order...
            </Text>
          </View>
        );

      case "printer_scanning":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Scanning for Printers
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Looking for thermal printers nearby. Make sure your printer is
              turned on and in pairing mode.
            </Text>
          </View>
        );

      case "printer_device_list":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: primaryColor + "20" },
              ]}
            >
              <IconSymbol name="printer" size={32} color={primaryColor} />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Select Printer
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Found {availableDevices.length} thermal printer
              {availableDevices.length !== 1 ? "s" : ""}. Select the one you
              want to connect.
            </Text>

            <ScrollView
              style={styles.deviceListContainer}
              showsVerticalScrollIndicator={true}
              nestedScrollEnabled={true}
            >
              <View style={styles.deviceList}>
                {availableDevices.map((item) => (
                  <View
                    key={item.id}
                    style={[styles.deviceItem, { borderColor }]}
                  >
                    <View style={styles.deviceInfo}>
                      <View
                        style={[
                          styles.deviceIcon,
                          { backgroundColor: primaryColor + "20" },
                        ]}
                      >
                        <IconSymbol
                          name={item.paired ? "gear" : "wifi"}
                          size={24}
                          color={primaryColor}
                        />
                      </View>
                      <View style={styles.deviceDetails}>
                        <Text style={[styles.deviceName, { color: textColor }]}>
                          {item.name}
                        </Text>
                        <Text
                          style={[
                            styles.deviceAddress,
                            { color: textSecondary },
                          ]}
                        >
                          {item.address}{" "}
                          {item.paired ? "• Paired" : "• Discoverable"}
                        </Text>
                      </View>
                    </View>
                    <ModernButton
                      title=""
                      onPress={() => connectToPrinter(item)}
                      variant="outline"
                      style={styles.connectButton}
                      disabled={isProcessing}
                      size="sm"
                      icon={
                        <IconSymbol
                          name="chevron.right"
                          size={16}
                          color={primaryColor}
                        />
                      }
                    />
                  </View>
                ))}
              </View>
            </ScrollView>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Scan Again"
                onPress={handleRetryPrinterSetup}
                variant="outline"
                icon={
                  <IconSymbol
                    name="arrow.clockwise"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
                disabled={isProcessing}
              />

              <ModernButton
                title="Proceed Without Printing"
                onPress={handleProceedWithoutPrinter}
                variant="outline"
                icon={
                  <IconSymbol
                    name="arrow.right"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
                disabled={isProcessing}
              />

              <ModernButton
                title="Back"
                onPress={handleBackToSetupRequired}
                variant="ghost"
                icon={
                  <IconSymbol
                    name="arrow.left"
                    size={16}
                    color={textSecondary}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "printer_connecting":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Connecting to Printer
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Connecting to {selectedDevice?.name}...
            </Text>
          </View>
        );

      case "printer_testing":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Testing Connection
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Printing test receipt to verify connection...
            </Text>
          </View>
        );

      case "printer_setup_success":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: successColor + "20" },
              ]}
            >
              <IconSymbol
                name="checkmark.circle.fill"
                size={32}
                color={successColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Printer Connected Successfully!
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {connectedPrinter?.name} is now connected and ready for printing.
              Test receipt should have been printed.
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Done"
                onPress={handlePrinterSetupSuccess}
                variant="primary"
                icon={<IconSymbol name="checkmark" size={16} color="white" />}
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "printer_setup_error":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: "#EF4444" + "20" },
              ]}
            >
              <IconSymbol
                name="exclamationmark.triangle"
                size={32}
                color="#EF4444"
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Setup Failed
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {errorMessage}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Try Again"
                onPress={handleRetryPrinterSetup}
                variant="primary"
                icon={
                  <IconSymbol name="arrow.clockwise" size={16} color="white" />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="Proceed Without Printing"
                onPress={handleProceedWithoutPrinter}
                variant="outline"
                icon={
                  <IconSymbol
                    name="arrow.right"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="Back"
                onPress={handleBackToSetupRequired}
                variant="ghost"
                icon={
                  <IconSymbol
                    name="arrow.left"
                    size={16}
                    color={textSecondary}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "order_success":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: successColor + "20" },
              ]}
            >
              <IconSymbol
                name="checkmark.circle.fill"
                size={32}
                color={successColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Order Completed Successfully!
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {successMessage}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Print Receipt"
                onPress={handlePrintReceipt}
                variant="outline"
                icon={
                  <IconSymbol name="printer" size={16} color={primaryColor} />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="Go to Dashboard"
                onPress={() => {
                  // Call onOrderComplete to properly clear data and close modal
                  if (onOrderComplete) {
                    onOrderComplete(null);
                  }
                  handleExplicitClose();
                  // Navigate to dashboard (tabs root)
                  router.replace("/(tabs)");
                }}
                variant="outline"
                icon={
                  <IconSymbol name="house" size={16} color={primaryColor} />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="New Sale"
                onPress={() => {
                  // Call onOrderComplete to properly clear data and close modal
                  if (onOrderComplete) {
                    onOrderComplete(null);
                  }
                  handleExplicitClose();
                  // Navigate to products page
                  router.replace("/(tabs)/products");
                }}
                variant="primary"
                icon={<IconSymbol name="plus.circle" size={16} color="white" />}
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "order_error":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: "#EF4444" + "20" },
              ]}
            >
              <IconSymbol
                name="exclamationmark.triangle"
                size={32}
                color="#EF4444"
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Order Failed
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {errorMessage}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Try Again"
                onPress={() => {
                  if (onResult) {
                    onResult({
                      proceed: true,
                      printerAvailable: printerAvailable,
                      userChoice: "proceed_without",
                    });
                  }
                  handleExplicitClose();
                }}
                variant="primary"
                icon={
                  <IconSymbol name="arrow.clockwise" size={16} color="white" />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="Cancel"
                onPress={() => {
                  if (onResult) {
                    onResult({
                      proceed: false,
                      printerAvailable: false,
                      userChoice: "cancel",
                    });
                  }
                  handleExplicitClose();
                }}
                variant="outline"
                icon={
                  <IconSymbol name="xmark" size={16} color={primaryColor} />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "printing_receipt":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Printing Receipt
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Please wait while we print your receipt...
            </Text>
          </View>
        );

      case "print_success":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: successColor + "20" },
              ]}
            >
              <IconSymbol
                name="checkmark.circle.fill"
                size={32}
                color={successColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Receipt Printed Successfully!
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Your receipt has been printed successfully.
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Go to Dashboard"
                onPress={() => {
                  // Call onOrderComplete to properly clear data and close modal
                  if (onOrderComplete) {
                    onOrderComplete(null);
                  }
                  handleExplicitClose();
                  // Navigate to dashboard (tabs root)
                  router.replace("/(tabs)");
                }}
                variant="outline"
                icon={
                  <IconSymbol name="house" size={16} color={primaryColor} />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="New Sale"
                onPress={() => {
                  // Call onOrderComplete to properly clear data and close modal
                  if (onOrderComplete) {
                    onOrderComplete(null);
                  }
                  handleExplicitClose();
                  // Navigate to products page
                  router.replace("/(tabs)/products");
                }}
                variant="primary"
                icon={<IconSymbol name="plus.circle" size={16} color="white" />}
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "print_error":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: "#EF4444" + "20" },
              ]}
            >
              <IconSymbol
                name="exclamationmark.triangle"
                size={32}
                color="#EF4444"
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Print Failed
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {errorMessage}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Try Again"
                onPress={handlePrintReceipt}
                variant="primary"
                icon={
                  <IconSymbol name="arrow.clockwise" size={16} color="white" />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="Skip Printing"
                onPress={() => {
                  // Don't close modal - transition to order success state
                  setCurrentStep("order_success");
                  setSuccessMessage(
                    "Order completed successfully! Receipt can be printed later from Orders."
                  );
                }}
                variant="outline"
                icon={
                  <IconSymbol
                    name="arrow.right"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  // Handle explicit modal close (only when user explicitly closes, not during navigation)
  const handleExplicitClose = () => {
    resetOrderState();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}} // Disable Android back button
    >
      <View style={styles.overlay}>
        <View style={[styles.modal, { backgroundColor, borderColor }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.headerTitle, { color: textColor }]}>
              Order Preparation
            </Text>
            {/* Close button removed - users must use action buttons */}
          </View>

          {renderStepContent()}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: Spacing.lg,
  },
  modal: {
    width: "100%",
    maxWidth: 400,
    borderRadius: 16,
    borderWidth: 1,
    overflow: "hidden",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  headerTitle: {
    fontSize: Typography.h3.fontSize,
    fontWeight: "600",
  },
  stepContainer: {
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Spacing.md,
  },
  stepTitle: {
    fontSize: Typography.h3.fontSize,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: Spacing.sm,
  },
  stepMessage: {
    fontSize: Typography.body.fontSize,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: Spacing.lg,
  },
  orderSummary: {
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    borderRadius: 8,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
    width: "100%",
  },
  summaryText: {
    fontSize: Typography.body.fontSize,
    textAlign: "center",
    marginBottom: Spacing.xs,
  },
  actionsContainer: {
    width: "100%",
    gap: Spacing.sm,
  },
  actionButton: {
    marginBottom: 0,
  },
  deviceListContainer: {
    maxHeight: 300, // Limit height to make it scrollable
    width: "100%",
    marginBottom: Spacing.lg,
  },
  deviceList: {
    width: "100%",
  },
  deviceItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: Spacing.md,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: Spacing.sm,
  },
  deviceInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  deviceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  deviceDetails: {
    flex: 1,
  },
  deviceName: {
    fontSize: Typography.body.fontSize,
    fontWeight: "600",
    marginBottom: 2,
  },
  deviceAddress: {
    fontSize: Typography.caption.fontSize,
  },
  connectButton: {
    minWidth: 40,
  },
});
