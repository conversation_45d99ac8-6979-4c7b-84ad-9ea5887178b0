/**
 * Commission Service - MySQL Implementation
 * Migrated from in-memory object to MySQL database
 * Maintains 100% API compatibility with existing implementation
 */

require("dotenv").config();
const { databaseManager } = require("../config/database-manager");

class CommissionService {
  constructor() {
    if (CommissionService.instance) {
      return CommissionService.instance;
    }

    this.databaseManager = databaseManager;

    CommissionService.instance = this;
  }

  static getInstance() {
    if (!CommissionService.instance) {
      CommissionService.instance = new CommissionService();
    }
    return CommissionService.instance;
  }

  // Get commission configuration (alias for compatibility)
  async getDiscountConfiguration() {
    return await this.getConfiguration();
  }

  // Get commission configuration
  async getConfiguration() {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        "SELECT config_value FROM commission_config WHERE config_key = ? AND is_active = TRUE",
        ["main_config"]
      );

      if (rows.length === 0) {
        // Return default configuration if not found
        return {
          success: true,
          configuration: {
            staff_discount_rate: 2,
            agent_discount_rate: 3,
            loyalty_multiplier: 1.1,
            min_order_amount: 50,
            max_discount_percentage: 12,
            commission_threshold: 1,
            enabled: true,
          },
        };
      }

      const configValue = rows[0].config_value;
      const configuration =
        typeof configValue === "string" ? JSON.parse(configValue) : configValue;

      return {
        success: true,
        configuration: configuration,
      };
    } catch (error) {
      console.error("Get commission configuration error:", error);
      return {
        success: false,
        error: "Failed to fetch commission configuration",
      };
    }
  }

  // Update commission configuration (alias for compatibility)
  async updateDiscountConfiguration(newConfig) {
    return await this.updateConfiguration(newConfig);
  }

  // Update commission configuration
  async updateConfiguration(newConfig) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          // Update main configuration
          await connection.execute(
            "UPDATE commission_config SET config_value = ?, updated_at = NOW() WHERE config_key = ?",
            [JSON.stringify(newConfig), "main_config"]
          );

          // Update individual configuration items
          const configItems = [
            {
              key: "staff_discount_rate",
              value: newConfig.staff_discount_rate,
            },
            {
              key: "agent_discount_rate",
              value: newConfig.agent_discount_rate,
            },
            { key: "loyalty_multiplier", value: newConfig.loyalty_multiplier },
            { key: "min_order_amount", value: newConfig.min_order_amount },
            {
              key: "max_discount_percentage",
              value: newConfig.max_discount_percentage,
            },
            {
              key: "commission_threshold",
              value: newConfig.commission_threshold,
            },
            { key: "system_enabled", value: newConfig.enabled },
          ];

          for (const item of configItems) {
            await connection.execute(
              `UPDATE commission_config
             SET config_value = ?, updated_at = NOW()
             WHERE config_key = ?`,
              [JSON.stringify(item.value), item.key]
            );
          }

          return {
            success: true,
            configuration: newConfig,
            message: "Commission configuration updated successfully",
          };
        }
      );
    } catch (error) {
      console.error("Update commission configuration error:", error);
      return {
        success: false,
        error: "Failed to update commission configuration",
      };
    }
  }

  // Calculate commission discount
  async calculateDiscount(cartData, staffId, salesAgentId, customerId = null) {
    try {
      // Get configuration
      const configResult = await this.getConfiguration();
      if (!configResult.success) {
        return configResult;
      }

      const config = configResult.configuration;

      // Check if system is enabled
      if (!config.enabled) {
        return {
          success: true,
          discount: {
            totalDiscount: 0,
            discountPercentage: 0,
            staffDiscount: 0,
            agentDiscount: 0,
            loyaltyBonus: 0,
            breakdown: {
              staff: { rate: 0, amount: 0 },
              agent: { rate: 0, amount: 0 },
              loyalty: { multiplier: 1, bonus: 0 },
            },
          },
          message: "Commission system is disabled",
        };
      }

      const subtotal = parseFloat(cartData.subtotal || 0);

      // Check minimum order amount
      if (subtotal < config.min_order_amount) {
        return {
          success: true,
          discount: {
            totalDiscount: 0,
            discountPercentage: 0,
            staffDiscount: 0,
            agentDiscount: 0,
            loyaltyBonus: 0,
            breakdown: {
              staff: { rate: 0, amount: 0 },
              agent: { rate: 0, amount: 0 },
              loyalty: { multiplier: 1, bonus: 0 },
            },
          },
          message: `Order amount below minimum threshold of KSh ${config.min_order_amount}`,
        };
      }

      // Get staff commission rate
      let staffCommissionRate = config.staff_discount_rate;
      if (staffId) {
        try {
          const [staffRows] = await this.databaseManager.executeQuery(
            "SELECT commission_rate FROM pos_staff WHERE id = ?",
            [staffId]
          );
          if (staffRows.length > 0 && staffRows[0].commission_rate > 0) {
            staffCommissionRate = parseFloat(staffRows[0].commission_rate);
          }
        } catch (staffError) {
          // Use default rate if staff lookup fails
          console.warn(
            "Staff lookup failed, using default rate:",
            staffError.message
          );
        }
      }

      // Get sales agent commission rate
      let agentCommissionRate = config.agent_discount_rate;
      if (salesAgentId) {
        try {
          const [agentRows] = await this.databaseManager.executeQuery(
            "SELECT commission_rate FROM sales_agents WHERE id = ?",
            [salesAgentId]
          );
          if (agentRows.length > 0 && agentRows[0].commission_rate > 0) {
            agentCommissionRate = parseFloat(agentRows[0].commission_rate);
          }
        } catch (agentError) {
          // Use default rate if agent lookup fails
          console.warn(
            "Agent lookup failed, using default rate:",
            agentError.message
          );
        }
      }

      // Calculate base discounts
      const staffDiscount = (subtotal * staffCommissionRate) / 100;
      const agentDiscount = salesAgentId
        ? (subtotal * agentCommissionRate) / 100
        : 0;

      // Calculate loyalty bonus (simplified - in production, get from customer data)
      const loyaltyMultiplier = config.loyalty_multiplier || 1.0;
      const loyaltyBonus =
        (staffDiscount + agentDiscount) * (loyaltyMultiplier - 1);

      // Calculate total discount
      const totalDiscount = staffDiscount + agentDiscount + loyaltyBonus;
      const discountPercentage = (totalDiscount / subtotal) * 100;

      // Apply maximum discount cap
      const cappedDiscountPercentage = Math.min(
        discountPercentage,
        config.max_discount_percentage
      );
      const cappedTotalDiscount = (subtotal * cappedDiscountPercentage) / 100;

      return {
        success: true,
        discount: {
          totalDiscount: parseFloat(cappedTotalDiscount.toFixed(2)),
          discountPercentage: parseFloat(cappedDiscountPercentage.toFixed(2)),
          rate: parseFloat(cappedDiscountPercentage.toFixed(2)), // For route compatibility
          amount: parseFloat(cappedTotalDiscount.toFixed(2)), // For test compatibility
          staffDiscount: parseFloat(staffDiscount.toFixed(2)),
          agentDiscount: parseFloat(agentDiscount.toFixed(2)),
          loyaltyBonus: parseFloat(loyaltyBonus.toFixed(2)),
          breakdown: {
            staff: {
              rate: staffCommissionRate,
              amount: parseFloat(staffDiscount.toFixed(2)),
            },
            agent: {
              rate: agentCommissionRate,
              amount: parseFloat(agentDiscount.toFixed(2)),
            },
            loyalty: {
              multiplier: loyaltyMultiplier,
              bonus: parseFloat(loyaltyBonus.toFixed(2)),
            },
          },
          subtotal: subtotal,
          finalAmount: parseFloat((subtotal - cappedTotalDiscount).toFixed(2)),
        },
        reason: "Commission discount calculated successfully",
        message: "Commission discount calculated from MySQL database",
      };
    } catch (error) {
      console.error("Calculate commission discount error:", error);
      return {
        success: false,
        error: "Failed to calculate commission discount",
      };
    }
  }

  // Get commission rules
  async getCommissionRules() {
    try {
      const [rows] = await this.databaseManager.executeQuery(
        `SELECT rule_name, rule_type, conditions, discount_rate, multiplier, 
                min_amount, max_amount, priority
         FROM commission_rules 
         WHERE is_active = TRUE 
         ORDER BY priority ASC`
      );

      const rules = rows.map((row) => ({
        name: row.rule_name,
        type: row.rule_type,
        conditions:
          typeof row.conditions === "string"
            ? JSON.parse(row.conditions)
            : row.conditions,
        discountRate: parseFloat(row.discount_rate || 0),
        multiplier: parseFloat(row.multiplier || 1),
        minAmount: parseFloat(row.min_amount || 0),
        maxAmount: row.max_amount ? parseFloat(row.max_amount) : null,
        priority: row.priority,
      }));

      return {
        success: true,
        rules: rules,
      };
    } catch (error) {
      console.error("Get commission rules error:", error);
      return {
        success: false,
        error: "Failed to fetch commission rules",
      };
    }
  }

  // Test calculation with sample data
  async testCalculation() {
    try {
      const testData = {
        cartData: { subtotal: "150.00" },
        staffId: "pos-001",
        salesAgentId: "agent-001",
      };

      const result = await this.calculateDiscount(
        testData.cartData,
        testData.staffId,
        testData.salesAgentId
      );

      return {
        success: true,
        testData: testData,
        result: result,
        message: "Test calculation completed from MySQL database",
      };
    } catch (error) {
      console.error("Test calculation error:", error);
      return {
        success: false,
        error: "Failed to run test calculation",
      };
    }
  }

  // Calculate commission discount (alias for compatibility)
  async calculateCommissionDiscount(
    cartData,
    staffId,
    salesAgentId,
    customerId = null
  ) {
    return await this.calculateDiscount(
      cartData,
      staffId,
      salesAgentId,
      customerId
    );
  }

  // Create commission discount (placeholder for compatibility)
  async createCommissionDiscount(discountData, staffId, salesAgentId) {
    try {
      const result = await this.calculateDiscount(
        discountData.cartData,
        staffId,
        salesAgentId,
        discountData.customerId
      );

      if (result.success) {
        return {
          success: true,
          discount: result.discount,
          calculation: result.discount,
          message: "Commission discount created successfully",
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error("Create commission discount error:", error);
      return {
        success: false,
        error: "Failed to create commission discount",
      };
    }
  }

  // Get customer loyalty tier (simplified implementation)
  async getCustomerLoyaltyTier(customerId) {
    try {
      // Simplified logic - in production, this would check customer purchase history
      const customerIdNum = parseInt(customerId) || 0;

      if (customerIdNum % 4 === 0) return "platinum";
      if (customerIdNum % 3 === 0) return "gold";
      if (customerIdNum % 2 === 0) return "silver";
      return "bronze";
    } catch (error) {
      console.error("Get customer loyalty tier error:", error);
      return "bronze";
    }
  }

  // Get discount analytics (placeholder for compatibility)
  async getDiscountAnalytics(dateRange = {}) {
    try {
      // Placeholder implementation - in production, this would query actual discount usage
      return {
        success: true,
        analytics: {
          totalDiscounts: 0,
          totalSavings: 0,
          averageDiscount: 0,
          topStaff: [],
          topAgents: [],
          dateRange: dateRange,
          message: "Analytics from MySQL database (placeholder implementation)",
        },
      };
    } catch (error) {
      console.error("Get discount analytics error:", error);
      return {
        success: false,
        error: "Failed to fetch discount analytics",
      };
    }
  }
}

module.exports = new CommissionService();
