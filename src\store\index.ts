import { configureStore } from "@reduxjs/toolkit";
import { TypedUseSelectorHook, useDispatch, useSelector } from "react-redux";

// Import slices
import authSlice from "./slices/authSlice";
import cartCompatibilitySlice from "./slices/cartCompatibilitySlice";
import cartSlice from "./slices/cartSlice";
import customerSlice from "./slices/customerSlice";
import orderSlice from "./slices/orderSlice";
import productSlice from "./slices/productSlice";
import storeSlice from "./slices/storeSlice";
import syncSlice from "./slices/syncSlice";
import ticketSlice from "./slices/ticketSlice";
import userSlice from "./slices/userSlice";

// Import middleware
import { autoSaveMiddleware } from "./middleware/autoSaveMiddleware";
import { ticketAutoCreationMiddleware } from "./middleware/ticketAutoCreationMiddleware";
import { ticketCompletionMiddleware } from "./middleware/ticketCompletionMiddleware";

export const store = configureStore({
  reducer: {
    auth: authSlice,
    store: storeSlice,
    products: productSlice,
    cart: cartSlice,
    customers: customerSlice,
    orders: orderSlice,
    sync: syncSlice,
    tickets: ticketSlice,
    cartCompatibility: cartCompatibilitySlice,
    user: userSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }).concat(
      autoSaveMiddleware,
      ticketAutoCreationMiddleware,
      ticketCompletionMiddleware
    ),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
