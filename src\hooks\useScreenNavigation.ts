import { useNavigation } from "@/src/contexts/NavigationContext";
import { useFocusEffect } from "@react-navigation/native";
import { useCallback } from "react";

interface UseScreenNavigationOptions {
  title?: string;
  forceTitle?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
}

/**
 * Enhanced hook for screen navigation management
 * Provides automatic title management and focus/blur handling
 */
export function useScreenNavigation(options: UseScreenNavigationOptions = {}) {
  const { title, forceTitle = false, onFocus, onBlur } = options;
  const {
    setCurrentTitle,
    currentTitle,
    canGoBack,
    isInCheckoutFlow,
    currentRoute,
  } = useNavigation();

  // Handle focus/blur events with React Navigation
  useFocusEffect(
    useCallback(() => {
      // Screen is focused - set title if provided
      if (title) {
        setCurrentTitle(title);
      }
      onFocus?.();

      return () => {
        // Screen is blurred
        onBlur?.();
      };
    }, [title, setCurrentTitle, onFocus, onBlur])
  );

  return {
    currentTitle,
    canGoBack,
    isInCheckoutFlow,
    currentRoute,
    setTitle: setCurrentTitle,
  };
}

/**
 * Simple hook for setting screen title
 * Use this for screens that just need to set a title
 */
export function useScreenTitle(title: string, forceTitle = false) {
  return useScreenNavigation({ title, forceTitle });
}

/**
 * Hook for screens that need focus/blur handling
 * Useful for screens that need to refresh data when focused
 */
export function useScreenFocus(onFocus?: () => void, onBlur?: () => void) {
  return useScreenNavigation({ onFocus, onBlur });
}
