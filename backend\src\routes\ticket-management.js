/**
 * Ticket Management API Routes
 *
 * Provides RESTful API endpoints for ticket operations:
 * - CRUD operations for tickets
 * - Ticket item management
 * - Discount application
 * - Audit trail access
 */

const express = require("express");
const router = express.Router();
const ticketService = require("../services/ticket-management-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const { v4: uuidv4 } = require("uuid");

// Get all tickets for current user
router.get("/tickets", authenticateToken, async (req, res) => {
  try {
    const {
      status = "active",
      limit = 50,
      offset = 0,
      includeExpired = false,
    } = req.query;

    const options = {
      status,
      limit: parseInt(limit),
      offset: parseInt(offset),
      includeExpired: includeExpired === "true",
    };

    const result = await ticketService.getTicketsByStaff(req.user.id, options);

    if (result.success) {
      res.json({
        success: true,
        data: {
          tickets: result.tickets,
          pagination: {
            limit: options.limit,
            offset: options.offset,
            hasMore: result.tickets.length === options.limit,
          },
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get tickets error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch tickets",
    });
  }
});

// Get tickets by customer ID
router.get(
  "/customers/:customerId/tickets",
  authenticateToken,
  async (req, res) => {
    try {
      const { customerId } = req.params;
      const {
        status = "active,paused",
        limit = 10,
        includeExpired = false,
      } = req.query;

      const options = {
        status,
        limit: parseInt(limit),
        includeExpired: includeExpired === "true",
      };

      const result = await ticketService.getTicketsByCustomer(
        customerId,
        options
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            tickets: result.tickets,
            count: result.tickets.length,
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Get customer tickets error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  }
);

// Get all tickets for any staff (requires permission)
router.get(
  "/tickets/all",
  authenticateToken,
  requirePermission("view_all_tickets"),
  async (req, res) => {
    try {
      const {
        staffId,
        status = "active",
        limit = 50,
        offset = 0,
        includeExpired = false,
      } = req.query;

      if (!staffId) {
        return res.status(400).json({
          success: false,
          error: "Staff ID is required",
        });
      }

      const options = {
        status,
        limit: parseInt(limit),
        offset: parseInt(offset),
        includeExpired: includeExpired === "true",
      };

      const result = await ticketService.getTicketsByStaff(staffId, options);

      if (result.success) {
        res.json({
          success: true,
          data: {
            tickets: result.tickets,
            staffId,
            pagination: {
              limit: options.limit,
              offset: options.offset,
              hasMore: result.tickets.length === options.limit,
            },
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Get all tickets error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch tickets",
      });
    }
  }
);

// Get specific ticket by ID
router.get("/tickets/:ticketId", authenticateToken, async (req, res) => {
  try {
    const { ticketId } = req.params;
    const ticket = await ticketService.getTicketById(ticketId);

    if (!ticket) {
      return res.status(404).json({
        success: false,
        error: "Ticket not found",
      });
    }

    // Check if user owns the ticket or has permission to view all tickets
    if (
      ticket.staff_id !== req.user.id &&
      !req.user.permissions.includes("view_all_tickets")
    ) {
      return res.status(403).json({
        success: false,
        error: "Permission denied: Cannot view this ticket",
      });
    }

    res.json({
      success: true,
      data: { ticket },
    });
  } catch (error) {
    console.error("Get ticket error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch ticket",
    });
  }
});

// Create new ticket
router.post("/tickets", authenticateToken, async (req, res) => {
  try {
    const {
      name = "New Ticket",
      customerId,
      salesAgentId,
      note,
      expiresAt,
    } = req.body;

    const ticketData = {
      name: name.trim(),
      terminalId: req.user.terminalId,
      locationId: req.user.locationId,
      customerId,
      salesAgentId,
      note,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
    };

    const result = await ticketService.createTicket(req.user.id, ticketData);

    if (result.success) {
      res.status(201).json({
        success: true,
        data: {
          ticket: result.ticket,
          message: "Ticket created successfully",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Create ticket error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to create ticket",
    });
  }
});

// Update ticket
router.put("/tickets/:ticketId", authenticateToken, async (req, res) => {
  try {
    const { ticketId } = req.params;
    const updateData = req.body;

    // Get ticket to check ownership
    const ticket = await ticketService.getTicketById(ticketId);
    if (!ticket) {
      return res.status(404).json({
        success: false,
        error: "Ticket not found",
      });
    }

    // Check if user owns the ticket or has permission to manage all tickets
    if (
      ticket.staff_id !== req.user.id &&
      !req.user.permissions.includes("manage_tickets")
    ) {
      return res.status(403).json({
        success: false,
        error: "Permission denied: Cannot update this ticket",
      });
    }

    const result = await ticketService.updateTicket(
      ticketId,
      req.user.id,
      updateData
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          ticket: result.ticket,
          message: "Ticket updated successfully",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Update ticket error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to update ticket",
    });
  }
});

// Delete ticket (cancel)
router.delete("/tickets/:ticketId", authenticateToken, async (req, res) => {
  try {
    const { ticketId } = req.params;

    // Get ticket to check ownership
    const ticket = await ticketService.getTicketById(ticketId);
    if (!ticket) {
      return res.status(404).json({
        success: false,
        error: "Ticket not found",
      });
    }

    // Check if user owns the ticket or has permission to manage all tickets
    if (
      ticket.staff_id !== req.user.id &&
      !req.user.permissions.includes("manage_tickets")
    ) {
      return res.status(403).json({
        success: false,
        error: "Permission denied: Cannot delete this ticket",
      });
    }

    const result = await ticketService.deleteTicket(ticketId, req.user.id);

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Delete ticket error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to delete ticket",
    });
  }
});

// Duplicate ticket
router.post(
  "/tickets/:ticketId/duplicate",
  authenticateToken,
  async (req, res) => {
    try {
      const { ticketId } = req.params;
      const { name } = req.body;

      // Get ticket to check ownership
      const ticket = await ticketService.getTicketById(ticketId);
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: "Ticket not found",
        });
      }

      // Check if user owns the ticket or has permission to manage all tickets
      if (
        ticket.staff_id !== req.user.id &&
        !req.user.permissions.includes("manage_tickets")
      ) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot duplicate this ticket",
        });
      }

      const result = await ticketService.duplicateTicket(
        ticketId,
        req.user.id,
        name
      );

      if (result.success) {
        res.status(201).json({
          success: true,
          data: {
            ticket: result.ticket,
            message: "Ticket duplicated successfully",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Duplicate ticket error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to duplicate ticket",
      });
    }
  }
);

// Add item to ticket
router.post("/tickets/:ticketId/items", authenticateToken, async (req, res) => {
  try {
    const { ticketId } = req.params;
    const itemData = req.body;

    // Validate required fields
    const { variantId, productId, title, price, quantity = 1 } = itemData;
    if (!variantId || !productId || !title || !price) {
      return res.status(400).json({
        success: false,
        error: "variantId, productId, title, and price are required",
      });
    }

    // Get ticket to check ownership
    const ticket = await ticketService.getTicketById(ticketId);
    if (!ticket) {
      return res.status(404).json({
        success: false,
        error: "Ticket not found",
      });
    }

    // Check if user owns the ticket or has permission to manage all tickets
    if (
      ticket.staff_id !== req.user.id &&
      !req.user.permissions.includes("manage_tickets")
    ) {
      return res.status(403).json({
        success: false,
        error: "Permission denied: Cannot modify this ticket",
      });
    }

    const result = await ticketService.addItemToTicket(
      ticketId,
      req.user.id,
      itemData
    );

    if (result.success) {
      res.status(201).json({
        success: true,
        data: {
          item: result.item,
          message: "Item added to ticket successfully",
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Add item to ticket error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to add item to ticket",
    });
  }
});

// Update ticket item
router.put(
  "/tickets/:ticketId/items/:itemId",
  authenticateToken,
  async (req, res) => {
    try {
      const { ticketId, itemId } = req.params;
      const updateData = req.body;

      // Get ticket to check ownership
      const ticket = await ticketService.getTicketById(ticketId);
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: "Ticket not found",
        });
      }

      // Check if user owns the ticket or has permission to manage all tickets
      if (
        ticket.staff_id !== req.user.id &&
        !req.user.permissions.includes("manage_tickets")
      ) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot modify this ticket",
        });
      }

      const result = await ticketService.updateTicketItem(
        itemId,
        req.user.id,
        updateData
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            item: result.item,
            message: "Item updated successfully",
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Update ticket item error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update ticket item",
      });
    }
  }
);

// Remove item from ticket
router.delete(
  "/tickets/:ticketId/items/:itemId",
  authenticateToken,
  async (req, res) => {
    try {
      const { ticketId, itemId } = req.params;

      // Get ticket to check ownership
      const ticket = await ticketService.getTicketById(ticketId);
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: "Ticket not found",
        });
      }

      // Check if user owns the ticket or has permission to manage all tickets
      if (
        ticket.staff_id !== req.user.id &&
        !req.user.permissions.includes("manage_tickets")
      ) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot modify this ticket",
        });
      }

      const result = await ticketService.removeItemFromTicket(
        itemId,
        req.user.id
      );

      if (result.success) {
        res.json({
          success: true,
          message: result.message,
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Remove item from ticket error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to remove item from ticket",
      });
    }
  }
);

// Auto-save ticket endpoint
router.post(
  "/tickets/:ticketId/auto-save",
  authenticateToken,
  async (req, res) => {
    try {
      const { ticketId } = req.params;
      const ticketState = req.body;

      // Validate ticket ownership
      const ticket = await ticketService.getTicketById(ticketId);
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: "Ticket not found",
        });
      }

      if (ticket.staff_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          error: "Permission denied: Cannot auto-save this ticket",
        });
      }

      const result = await ticketService.autoSaveTicket(
        ticketId,
        req.user.id,
        ticketState
      );

      if (result.success) {
        res.json({
          success: true,
          data: {
            message: result.message,
            timestamp: result.timestamp,
          },
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Auto-save ticket error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to auto-save ticket",
      });
    }
  }
);

// Batch auto-save tickets endpoint
router.post("/tickets/batch-auto-save", authenticateToken, async (req, res) => {
  try {
    const { tickets } = req.body;

    if (!Array.isArray(tickets) || tickets.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Invalid tickets data",
      });
    }

    // Validate all tickets belong to the user
    for (const { ticketId } of tickets) {
      const ticket = await ticketService.getTicketById(ticketId);
      if (!ticket || ticket.staff_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          error: `Permission denied for ticket ${ticketId}`,
        });
      }
    }

    const result = await ticketService.batchAutoSaveTickets(
      req.user.id,
      tickets
    );

    res.json({
      success: result.success,
      data: {
        results: result.results,
        summary: result.summary,
      },
      error: result.error,
    });
  } catch (error) {
    console.error("Batch auto-save error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to batch auto-save tickets",
    });
  }
});

// Get recovery tickets endpoint
router.get("/tickets/recovery", authenticateToken, async (req, res) => {
  try {
    const result = await ticketService.getRecoveryTickets(req.user.id);

    if (result.success) {
      res.json({
        success: true,
        data: {
          tickets: result.tickets,
          count: result.tickets.length,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get recovery tickets error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get recovery tickets",
    });
  }
});

// Get auto-save statistics endpoint
router.get("/tickets/auto-save-stats", authenticateToken, async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const result = await ticketService.getAutoSaveStats(
      req.user.id,
      parseInt(days)
    );

    if (result.success) {
      res.json({
        success: true,
        data: {
          stats: result.stats,
          summary: result.summary,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Get auto-save stats error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get auto-save statistics",
    });
  }
});

// Admin endpoints for cleanup operations
router.post(
  "/admin/cleanup/expired-tickets",
  authenticateToken,
  requirePermission("admin_cleanup"),
  async (req, res) => {
    try {
      const result = await ticketService.cleanupExpiredTickets();

      res.json({
        success: result.success,
        data: {
          message: result.message,
          cleanedCount: result.cleanedCount,
          cleanedTickets: result.cleanedTickets,
        },
        error: result.error,
      });
    } catch (error) {
      console.error("Cleanup expired tickets error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to cleanup expired tickets",
      });
    }
  }
);

router.post(
  "/admin/cleanup/old-completed-tickets",
  authenticateToken,
  requirePermission("admin_cleanup"),
  async (req, res) => {
    try {
      const { daysOld = 30 } = req.body;
      const result = await ticketService.cleanupOldCompletedTickets(
        parseInt(daysOld)
      );

      res.json({
        success: result.success,
        data: {
          message: result.message,
          archivedCount: result.archivedCount,
        },
        error: result.error,
      });
    } catch (error) {
      console.error("Cleanup old completed tickets error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to cleanup old completed tickets",
      });
    }
  }
);

module.exports = router;
