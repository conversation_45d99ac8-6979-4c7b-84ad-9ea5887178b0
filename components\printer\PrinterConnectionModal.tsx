import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ModernButton } from '@/components/ui/ModernButton';
import { useThemeColor } from '@/hooks/useThemeColor';
import { ThermalPrintService } from '@/src/services/ThermalPrintService';
import { BluetoothPermissionHelper } from '@/src/utils/BluetoothPermissionHelper';
import { Spacing, Typography } from '@/constants/Design';

interface PrinterDevice {
  id: string;
  name: string;
  address: string;
  connected: boolean;
}

interface PrinterConnectionModalProps {
  visible: boolean;
  onClose: () => void;
  onPrinterConnected?: (device: PrinterDevice) => void;
}

export function PrinterConnectionModal({
  visible,
  onClose,
  onPrinterConnected,
}: PrinterConnectionModalProps) {
  const [isScanning, setIsScanning] = useState(false);
  const [devices, setDevices] = useState<PrinterDevice[]>([]);
  const [connectedDevice, setConnectedDevice] = useState<PrinterDevice | null>(null);
  const [isConnecting, setIsConnecting] = useState<string | null>(null);

  const backgroundColor = useThemeColor({}, 'surface');
  const textColor = useThemeColor({}, 'text');
  const textSecondary = useThemeColor({}, 'textSecondary');
  const primaryColor = useThemeColor({}, 'primary');
  const borderColor = useThemeColor({}, 'border');

  useEffect(() => {
    if (visible) {
      checkCurrentConnection();
    }
  }, [visible]);

  const checkCurrentConnection = async () => {
    try {
      const isConnected = await ThermalPrintService.isConnected();
      if (isConnected) {
        // Get connected device info if available
        const deviceInfo = await ThermalPrintService.getConnectedDevice();
        if (deviceInfo) {
          setConnectedDevice(deviceInfo);
        }
      }
    } catch (error) {
      console.log('No printer currently connected');
    }
  };

  const scanForPrinters = async () => {
    try {
      setIsScanning(true);
      setDevices([]);

      // Check permissions first
      const hasPermissions = await BluetoothPermissionHelper.requestPermissions();
      if (!hasPermissions) {
        Alert.alert('Permission Required', 'Bluetooth permissions are required to scan for printers.');
        return;
      }

      // Scan for thermal printers
      const discoveredDevices = await ThermalPrintService.scanForPrinters();
      
      // Filter to show only thermal printers (basic filtering by name)
      const thermalPrinters = discoveredDevices.filter((device: any) => 
        device.name && (
          device.name.toLowerCase().includes('thermal') ||
          device.name.toLowerCase().includes('printer') ||
          device.name.toLowerCase().includes('pos') ||
          device.name.toLowerCase().includes('receipt')
        )
      );

      setDevices(thermalPrinters);
    } catch (error) {
      console.error('Error scanning for printers:', error);
      Alert.alert('Scan Error', 'Failed to scan for printers. Please try again.');
    } finally {
      setIsScanning(false);
    }
  };

  const connectToPrinter = async (device: PrinterDevice) => {
    try {
      setIsConnecting(device.id);
      
      const success = await ThermalPrintService.connectToPrinter(device.address);
      
      if (success) {
        setConnectedDevice(device);
        onPrinterConnected?.(device);
        Alert.alert('Success', `Connected to ${device.name}`);
      } else {
        Alert.alert('Connection Failed', `Could not connect to ${device.name}`);
      }
    } catch (error) {
      console.error('Error connecting to printer:', error);
      Alert.alert('Connection Error', 'Failed to connect to printer.');
    } finally {
      setIsConnecting(null);
    }
  };

  const testPrinter = async () => {
    if (!connectedDevice) return;

    try {
      await ThermalPrintService.printTestReceipt();
      Alert.alert('Test Successful', 'Test receipt printed successfully!');
    } catch (error) {
      console.error('Test print failed:', error);
      Alert.alert('Test Failed', 'Could not print test receipt.');
    }
  };

  const disconnectPrinter = async () => {
    try {
      await ThermalPrintService.disconnect();
      setConnectedDevice(null);
      Alert.alert('Disconnected', 'Printer disconnected successfully.');
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  };

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.overlay}>
        <View style={[styles.modal, { backgroundColor, borderColor }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: textColor }]}>
              Printer Connection
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <IconSymbol name="xmark" size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          {/* Current Connection Status */}
          {connectedDevice && (
            <View style={[styles.connectedSection, { borderColor }]}>
              <View style={styles.connectedInfo}>
                <IconSymbol name="printer.fill" size={24} color={primaryColor} />
                <View style={styles.deviceInfo}>
                  <Text style={[styles.deviceName, { color: textColor }]}>
                    {connectedDevice.name}
                  </Text>
                  <Text style={[styles.deviceStatus, { color: primaryColor }]}>
                    Connected
                  </Text>
                </View>
              </View>
              <View style={styles.connectedActions}>
                <ModernButton
                  title="Test Print"
                  onPress={testPrinter}
                  variant="outline"
                  size="sm"
                  style={styles.actionButton}
                />
                <ModernButton
                  title="Disconnect"
                  onPress={disconnectPrinter}
                  variant="outline"
                  size="sm"
                  style={styles.actionButton}
                />
              </View>
            </View>
          )}

          {/* Scan Section */}
          <View style={styles.scanSection}>
            <ModernButton
              title={isScanning ? "Scanning..." : "Scan for Printers"}
              onPress={scanForPrinters}
              disabled={isScanning}
              icon={isScanning ? undefined : <IconSymbol name="magnifyingglass" size={16} color="white" />}
              style={styles.scanButton}
            />
          </View>

          {/* Devices List */}
          <View style={styles.devicesList}>
            {isScanning && (
              <View style={styles.scanningIndicator}>
                <ActivityIndicator size="small" color={primaryColor} />
                <Text style={[styles.scanningText, { color: textSecondary }]}>
                  Scanning for thermal printers...
                </Text>
              </View>
            )}

            {devices.map((device) => (
              <TouchableOpacity
                key={device.id}
                style={[styles.deviceItem, { borderColor }]}
                onPress={() => connectToPrinter(device)}
                disabled={!!isConnecting}
              >
                <View style={styles.deviceItemContent}>
                  <IconSymbol name="printer" size={20} color={textSecondary} />
                  <View style={styles.deviceInfo}>
                    <Text style={[styles.deviceName, { color: textColor }]}>
                      {device.name}
                    </Text>
                    <Text style={[styles.deviceAddress, { color: textSecondary }]}>
                      {device.address}
                    </Text>
                  </View>
                  {isConnecting === device.id ? (
                    <ActivityIndicator size="small" color={primaryColor} />
                  ) : (
                    <IconSymbol name="chevron.right" size={16} color={textSecondary} />
                  )}
                </View>
              </TouchableOpacity>
            ))}

            {!isScanning && devices.length === 0 && (
              <View style={styles.emptyState}>
                <IconSymbol name="printer" size={48} color={textSecondary} />
                <Text style={[styles.emptyText, { color: textSecondary }]}>
                  No thermal printers found
                </Text>
                <Text style={[styles.emptySubtext, { color: textSecondary }]}>
                  Make sure your printer is on and in pairing mode
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    borderWidth: 1,
    padding: Spacing.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  title: {
    ...Typography.h3,
    fontWeight: '600',
  },
  closeButton: {
    padding: Spacing.sm,
  },
  connectedSection: {
    borderWidth: 1,
    borderRadius: 8,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
  },
  connectedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  deviceInfo: {
    flex: 1,
    marginLeft: Spacing.md,
  },
  deviceName: {
    ...Typography.bodyMedium,
    fontWeight: '600',
  },
  deviceStatus: {
    ...Typography.caption,
    fontWeight: '500',
  },
  deviceAddress: {
    ...Typography.caption,
  },
  connectedActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  scanSection: {
    marginBottom: Spacing.lg,
  },
  scanButton: {
    width: '100%',
  },
  devicesList: {
    maxHeight: 300,
  },
  scanningIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.lg,
  },
  scanningText: {
    ...Typography.body,
    marginLeft: Spacing.md,
  },
  deviceItem: {
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: Spacing.sm,
  },
  deviceItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
  },
  emptyState: {
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyText: {
    ...Typography.bodyMedium,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  emptySubtext: {
    ...Typography.caption,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
});
