/**
 * Test Environment Variables
 * Quick script to verify .env file is being read correctly
 */

require('dotenv').config();

console.log('🔍 Testing Environment Variables...\n');

console.log('Database Configuration:');
console.log(`DB_HOST: ${process.env.DB_HOST}`);
console.log(`DB_USER: ${process.env.DB_USER}`);
console.log(`DB_PASSWORD: ${process.env.DB_PASSWORD ? '***HIDDEN***' : 'NOT SET'}`);
console.log(`DB_NAME: ${process.env.DB_NAME}`);
console.log(`DB_PORT: ${process.env.DB_PORT}`);

console.log('\nMySQL Root Configuration:');
console.log(`MYSQL_ROOT_PASSWORD: ${process.env.MYSQL_ROOT_PASSWORD ? '***HIDDEN***' : 'NOT SET'}`);

console.log('\nOther Configuration:');
console.log(`JWT_SECRET: ${process.env.JWT_SECRET ? '***HIDDEN***' : 'NOT SET'}`);
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`PORT: ${process.env.PORT}`);

// Test if all required variables are set
const requiredVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'MYSQL_ROOT_PASSWORD'];
const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length === 0) {
  console.log('\n✅ All required environment variables are set!');
} else {
  console.log('\n❌ Missing environment variables:');
  missingVars.forEach(varName => console.log(`   - ${varName}`));
}

console.log('\n🎯 Ready to run setup with these credentials!');
