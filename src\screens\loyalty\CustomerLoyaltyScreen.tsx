/**
 * Customer Loyalty Management Screen
 * Comprehensive loyalty management interface for viewing customer loyalty data,
 * transaction history, and managing points redemption
 */

import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import {
  ThemedView,
  ThemedText,
} from "@/src/components/themed/ThemedComponents";
import { ModernButton } from "@/src/components/ui/ModernButton";
import { IconSymbol } from "@/src/components/ui/IconSymbol";
import {
  CustomerLoyaltyCard,
  CustomerLoyaltyData,
} from "@/src/components/loyalty/CustomerLoyaltyCard";
import { TierProgressIndicator } from "@/src/components/loyalty/TierProgressIndicator";
import {
  LoyaltyTransactionsList,
  LoyaltyTransaction,
} from "@/src/components/loyalty/LoyaltyTransactionsList";
import { PointsRedemptionModal } from "@/src/components/loyalty/PointsRedemptionModal";
import { formatCurrency } from "@/src/utils/currencyUtils";

interface CustomerLoyaltyScreenProps {
  customerId: string;
  customerName?: string;
  onBack?: () => void;
  orderTotal?: number; // For redemption context
}

export const CustomerLoyaltyScreen: React.FC<CustomerLoyaltyScreenProps> = ({
  customerId,
  customerName,
  onBack,
  orderTotal,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  // State management
  const [loyaltyData, setLoyaltyData] = useState<CustomerLoyaltyData | null>(
    null
  );
  const [transactions, setTransactions] = useState<LoyaltyTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMoreTransactions, setHasMoreTransactions] = useState(true);
  const [showRedemptionModal, setShowRedemptionModal] = useState(false);
  const [activeTab, setActiveTab] = useState<"overview" | "transactions">(
    "overview"
  );

  // Pagination
  const [transactionOffset, setTransactionOffset] = useState(0);
  const transactionLimit = 20;

  // Load customer loyalty data
  const loadLoyaltyData = useCallback(
    async (showLoader = true) => {
      try {
        if (showLoader) setLoading(true);

        // Simulate API call - replace with actual API integration
        const response = await fetch(
          `/api/loyalty/customers/${customerId}?includeShopifyData=true`
        );
        const result = await response.json();

        if (result.success) {
          setLoyaltyData(result.data);
        } else {
          Alert.alert("Error", "Failed to load loyalty data");
        }
      } catch (error) {
        console.error("Load loyalty data error:", error);
        Alert.alert("Error", "Failed to load loyalty data");
      } finally {
        if (showLoader) setLoading(false);
      }
    },
    [customerId]
  );

  // Load transaction history
  const loadTransactions = useCallback(
    async (offset = 0, showLoader = true) => {
      try {
        if (showLoader) setLoadingMore(true);

        const response = await fetch(
          `/api/loyalty/customers/${customerId}/transactions?limit=${transactionLimit}&offset=${offset}`
        );
        const result = await response.json();

        if (result.success) {
          const newTransactions = result.data.transactions;

          if (offset === 0) {
            setTransactions(newTransactions);
          } else {
            setTransactions((prev) => [...prev, ...newTransactions]);
          }

          setHasMoreTransactions(newTransactions.length === transactionLimit);
          setTransactionOffset(offset + newTransactions.length);
        } else {
          Alert.alert("Error", "Failed to load transaction history");
        }
      } catch (error) {
        console.error("Load transactions error:", error);
        Alert.alert("Error", "Failed to load transaction history");
      } finally {
        if (showLoader) setLoadingMore(false);
      }
    },
    [customerId, transactionLimit]
  );

  // Initial data load
  useEffect(() => {
    loadLoyaltyData();
    loadTransactions();
  }, [loadLoyaltyData, loadTransactions]);

  // Refresh handler
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([loadLoyaltyData(false), loadTransactions(0, false)]);
    setRefreshing(false);
  }, [loadLoyaltyData, loadTransactions]);

  // Load more transactions
  const handleLoadMoreTransactions = useCallback(() => {
    if (!loadingMore && hasMoreTransactions) {
      loadTransactions(transactionOffset);
    }
  }, [loadTransactions, transactionOffset, loadingMore, hasMoreTransactions]);

  // Handle points redemption
  const handlePointsRedemption = useCallback(
    async (pointsToRedeem: number) => {
      try {
        const response = await fetch(
          `/api/loyalty/customers/${customerId}/points/redeem`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              pointsToRedeem,
              orderId: null, // Will be set during actual order processing
              ticketId: null, // Will be set if redeeming during ticket creation
            }),
          }
        );

        const result = await response.json();

        if (result.success) {
          Alert.alert(
            "Points Redeemed Successfully",
            `${pointsToRedeem.toLocaleString()} points redeemed for ${formatCurrency(
              result.data.discountAmount
            )} discount`
          );

          // Refresh data
          await handleRefresh();
        } else {
          throw new Error(result.error);
        }
      } catch (error) {
        console.error("Points redemption error:", error);
        throw error;
      }
    },
    [customerId, handleRefresh]
  );

  // Tab navigation
  const renderTabBar = () => (
    <View style={styles.tabBar}>
      <TouchableOpacity
        style={[
          styles.tabButton,
          activeTab === "overview" && styles.activeTabButton,
          { borderBottomColor: theme.colors.primary },
        ]}
        onPress={() => setActiveTab("overview")}
      >
        <ThemedText
          variant="body"
          style={[
            styles.tabText,
            {
              color:
                activeTab === "overview"
                  ? theme.colors.primary
                  : theme.colors.textSecondary,
            },
          ]}
        >
          Overview
        </ThemedText>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.tabButton,
          activeTab === "transactions" && styles.activeTabButton,
          { borderBottomColor: theme.colors.primary },
        ]}
        onPress={() => setActiveTab("transactions")}
      >
        <ThemedText
          variant="body"
          style={[
            styles.tabText,
            {
              color:
                activeTab === "transactions"
                  ? theme.colors.primary
                  : theme.colors.textSecondary,
            },
          ]}
        >
          Transactions
        </ThemedText>
      </TouchableOpacity>
    </View>
  );

  // Overview tab content
  const renderOverviewTab = () => (
    <ScrollView
      style={styles.tabContent}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor={theme.colors.primary}
        />
      }
    >
      {loyaltyData && (
        <>
          {/* Loyalty Card */}
          <CustomerLoyaltyCard
            loyaltyData={loyaltyData}
            showProgress={true}
            compact={false}
          />

          {/* Tier Progress */}
          <TierProgressIndicator
            currentTier={loyaltyData.tier}
            currentSpending={loyaltyData.totalPurchases}
            nextTierThreshold={loyaltyData.progressToNextTier?.requiredAmount}
            compact={false}
            showLabels={true}
            animated={true}
          />

          {/* Quick Actions */}
          <ThemedView
            variant="card"
            style={[styles.actionsCard, utils.p("lg")]}
          >
            <ThemedText variant="h3" style={utils.mb("md")}>
              Quick Actions
            </ThemedText>

            <View style={styles.actionButtons}>
              {orderTotal && (
                <ModernButton
                  title="Redeem Points"
                  variant="primary"
                  onPress={() => setShowRedemptionModal(true)}
                  style={styles.actionButton}
                  leftIcon="star.fill"
                />
              )}

              <ModernButton
                title="View History"
                variant="outline"
                onPress={() => setActiveTab("transactions")}
                style={styles.actionButton}
                leftIcon="clock"
              />
            </View>
          </ThemedView>

          {/* Recent Transactions Preview */}
          <ThemedView variant="card" style={[styles.recentCard, utils.p("lg")]}>
            <View style={styles.recentHeader}>
              <ThemedText variant="h3">Recent Activity</ThemedText>
              <TouchableOpacity onPress={() => setActiveTab("transactions")}>
                <ThemedText variant="small" color="primary">
                  View All
                </ThemedText>
              </TouchableOpacity>
            </View>

            {transactions.slice(0, 3).map((transaction) => (
              <View key={transaction.id} style={styles.recentTransaction}>
                <View style={styles.recentTransactionInfo}>
                  <ThemedText variant="body">
                    {transaction.description}
                  </ThemedText>
                  <ThemedText variant="small" color="secondary">
                    {new Date(transaction.createdAt).toLocaleDateString()}
                  </ThemedText>
                </View>
                <ThemedText
                  variant="body"
                  style={{
                    color:
                      transaction.pointsAmount > 0
                        ? theme.colors.success
                        : theme.colors.warning,
                  }}
                >
                  {transaction.pointsAmount > 0 ? "+" : ""}
                  {transaction.pointsAmount.toLocaleString()}
                </ThemedText>
              </View>
            ))}
          </ThemedView>
        </>
      )}
    </ScrollView>
  );

  // Transactions tab content
  const renderTransactionsTab = () => (
    <LoyaltyTransactionsList
      transactions={transactions}
      loading={loadingMore}
      onRefresh={handleRefresh}
      onLoadMore={handleLoadMoreTransactions}
      hasMore={hasMoreTransactions}
      showFilters={true}
    />
  );

  if (loading) {
    return (
      <ThemedView variant="background" style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <ThemedText variant="body" color="secondary" style={utils.mt("md")}>
          Loading loyalty data...
        </ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView variant="background" style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        {onBack && (
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <IconSymbol
              name="chevron.left"
              size={24}
              color={theme.colors.text}
            />
          </TouchableOpacity>
        )}
        <View style={styles.headerContent}>
          <ThemedText variant="h2">Loyalty Details</ThemedText>
          {customerName && (
            <ThemedText variant="small" color="secondary">
              {customerName}
            </ThemedText>
          )}
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            onPress={handleRefresh}
            style={styles.refreshButton}
          >
            <IconSymbol
              name="arrow.clockwise"
              size={20}
              color={theme.colors.text}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Tab Content */}
      {activeTab === "overview" ? renderOverviewTab() : renderTransactionsTab()}

      {/* Points Redemption Modal */}
      {loyaltyData && (
        <PointsRedemptionModal
          visible={showRedemptionModal}
          onClose={() => setShowRedemptionModal(false)}
          onRedeem={handlePointsRedemption}
          customerLoyalty={loyaltyData}
          orderTotal={orderTotal || 0}
        />
      )}
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerContent: {
    flex: 1,
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  refreshButton: {
    padding: 8,
  },
  tabBar: {
    flexDirection: "row",
    paddingHorizontal: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTabButton: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontWeight: "600",
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  actionsCard: {
    marginVertical: 8,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  recentCard: {
    marginVertical: 8,
  },
  recentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  recentTransaction: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
  },
  recentTransactionInfo: {
    flex: 1,
  },
});
