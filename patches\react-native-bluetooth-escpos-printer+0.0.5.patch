diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build.gradle b/node_modules/react-native-bluetooth-escpos-printer/android/build.gradle
index d86e4f5..fd25b92 100644
--- a/node_modules/react-native-bluetooth-escpos-printer/android/build.gradle
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build.gradle
@@ -1,7 +1,7 @@
 buildscript {
     repositories {
-        jcenter { url "http://jcenter.bintray.com/" }
-        maven {url "http://repo.spring.io/plugins-release/"}
+        jcenter { url "https://jcenter.bintray.com/" }
+        maven {url "https://repo.spring.io/plugins-release/"}
         mavenCentral()
         maven {
             // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
@@ -13,19 +13,19 @@ buildscript {
     }
 
     dependencies {
-        classpath 'com.android.tools.build:gradle:3.1.4'
+        classpath 'com.android.tools.build:gradle:8.0.0'
     }
 }
 
 apply plugin: 'com.android.library'
 
 android {
-    compileSdkVersion 27
-    buildToolsVersion "27.0.3"
+    compileSdkVersion 34
+    buildToolsVersion "34.0.0"
 
     defaultConfig {
-        minSdkVersion 16
-        targetSdkVersion 24
+        minSdkVersion 24
+        targetSdkVersion 34
         versionCode 1
         versionName "1.0"
     }
@@ -40,8 +40,8 @@ android {
 }
 
 repositories {
-    jcenter { url "http://jcenter.bintray.com/" }
-    maven {url "http://repo.spring.io/plugins-release/"}
+    jcenter { url "https://jcenter.bintray.com/" }
+    maven {url "https://repo.spring.io/plugins-release/"}
     mavenCentral()
     maven {
         // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
@@ -53,8 +53,8 @@ repositories {
 }
 
 dependencies {
-    compile fileTree(dir: 'libs', include: ['*.jar'])
+    implementation fileTree(dir: 'libs', include: ['*.jar'])
     implementation 'com.facebook.react:react-native:+'  // From node_modules
-    implementation group: 'com.android.support', name: 'support-v4', version: '27.0.0'
-    implementation "com.google.zxing:core:3.3.0"
+    implementation 'androidx.core:core:1.9.0'
+    implementation "com.google.zxing:core:3.5.1"
 }
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/generated/source/buildConfig/debug/cn/jystudio/bluetooth/BuildConfig.java b/node_modules/react-native-bluetooth-escpos-printer/android/build/generated/source/buildConfig/debug/cn/jystudio/bluetooth/BuildConfig.java
new file mode 100644
index 0000000..81aa281
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/generated/source/buildConfig/debug/cn/jystudio/bluetooth/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package cn.jystudio.bluetooth;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "cn.jystudio.bluetooth";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..432e640
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,12 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="cn.jystudio.bluetooth" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
+    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..eaf30ae
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "cn.jystudio.bluetooth",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..08a59d7
Binary files /dev/null and b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..dc0bf43
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Sun Apr 20 18:25:15 EAT 2025
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..1f67d93
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..3782f20
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,20 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="cn.jystudio.bluetooth" >
+4
+5    <uses-sdk android:minSdkVersion="24" />
+6
+7    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+7-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:4:5-74
+7-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:4:22-71
+8    <uses-permission android:name="android.permission.BLUETOOTH" />
+8-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:5:5-68
+8-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:5:22-65
+9    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
+9-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:6:5-80
+9-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:6:22-78
+10    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
+10-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:7:5-78
+10-->/home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:7:22-76
+11
+12</manifest>
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..432e640
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,12 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="cn.jystudio.bluetooth" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
+    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..abf5209
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1 @@
+cn.jystudio.bluetooth
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-bluetooth-escpos-printer/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..efe6c32
--- /dev/null
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,33 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:2:1-9:12
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:2:1-9:12
+	package
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:3:11-42
+		INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:2:11-69
+uses-permission#android.permission.BLUETOOTH_ADMIN
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:4:5-74
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:4:22-71
+uses-permission#android.permission.BLUETOOTH
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:5:5-68
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:5:22-65
+uses-permission#android.permission.ACCESS_COARSE_LOCATION
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:6:5-80
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:6:22-78
+uses-permission#android.permission.ACCESS_FINE_LOCATION
+ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:7:5-78
+	android:name
+		ADDED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml:7:22-76
+uses-sdk
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml
+INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /home/<USER>/Desktop/PROJECTS/dukalinkCombined/DukalinkNative/node_modules/react-native-bluetooth-escpos-printer/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-bluetooth-escpos-printer/android/src/main/java/cn/jystudio/bluetooth/RNBluetoothManagerModule.java b/node_modules/react-native-bluetooth-escpos-printer/android/src/main/java/cn/jystudio/bluetooth/RNBluetoothManagerModule.java
index dd5d33e..bcdf209 100644
--- a/node_modules/react-native-bluetooth-escpos-printer/android/src/main/java/cn/jystudio/bluetooth/RNBluetoothManagerModule.java
+++ b/node_modules/react-native-bluetooth-escpos-printer/android/src/main/java/cn/jystudio/bluetooth/RNBluetoothManagerModule.java
@@ -10,8 +10,8 @@ import android.content.Intent;
 import android.content.IntentFilter;
 import android.content.pm.PackageManager;
 import android.os.Bundle;
-import android.support.v4.app.ActivityCompat;
-import android.support.v4.content.ContextCompat;
+import androidx.core.app.ActivityCompat;
+import androidx.core.content.ContextCompat;
 import android.util.Log;
 import android.widget.Toast;
 import com.facebook.react.bridge.*;
