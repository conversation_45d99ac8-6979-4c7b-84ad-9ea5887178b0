import { Alert } from "react-native";
import EnhancedThermalPrintService, {
  ReceiptData,
} from "../services/EnhancedThermalPrintService";
import ThermalPrintService from "../services/ThermalPrintService";
import BluetoothPermissionHelper from "./BluetoothPermissionHelper";

export interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  duration?: number;
}

export interface TestSuite {
  suiteName: string;
  results: TestResult[];
  passed: number;
  failed: number;
  totalDuration: number;
}

export class ThermalPrintingTestUtils {
  private static testResults: TestSuite[] = [];

  /**
   * Run comprehensive thermal printing tests
   */
  static async runAllTests(): Promise<TestSuite[]> {
    console.log("🧪 Starting Thermal Printing Test Suite...");

    this.testResults = [];

    // Run test suites
    await this.runPermissionTests();
    await this.runServiceTests();
    await this.runPrintingTests();
    await this.runIntegrationTests();

    // Display results
    this.displayTestResults();

    return this.testResults;
  }

  /**
   * Test Bluetooth permissions functionality
   */
  private static async runPermissionTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: "Bluetooth Permissions",
      results: [],
      passed: 0,
      failed: 0,
      totalDuration: 0,
    };

    // Test 1: Check permissions
    await this.runTest(
      "Check Bluetooth Permissions",
      async () => {
        const result =
          await BluetoothPermissionHelper.checkBluetoothPermissions();
        if (typeof result.granted !== "boolean") {
          throw new Error("Permission check should return boolean");
        }
        return true;
      },
      suite
    );

    // Test 2: Bluetooth state
    await this.runTest(
      "Check Bluetooth State",
      async () => {
        const isEnabled = await BluetoothPermissionHelper.isBluetoothEnabled();
        if (typeof isEnabled !== "boolean") {
          throw new Error("Bluetooth state check should return boolean");
        }
        return true;
      },
      suite
    );

    // Test 3: Get Bluetooth state info
    await this.runTest(
      "Get Bluetooth State Info",
      async () => {
        const state = await BluetoothPermissionHelper.getBluetoothState();
        if (
          !state ||
          typeof state.enabled !== "boolean" ||
          typeof state.available !== "boolean"
        ) {
          throw new Error(
            "Bluetooth state info should have enabled and available properties"
          );
        }
        return true;
      },
      suite
    );

    this.testResults.push(suite);
  }

  /**
   * Test thermal print services
   */
  private static async runServiceTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: "Thermal Print Services",
      results: [],
      passed: 0,
      failed: 0,
      totalDuration: 0,
    };

    // Test 1: Service initialization
    await this.runTest(
      "Initialize Thermal Print Service",
      async () => {
        const result = await ThermalPrintService.init();
        if (typeof result !== "boolean") {
          throw new Error("Service initialization should return boolean");
        }
        return true;
      },
      suite
    );

    // Test 2: Set printer type
    await this.runTest(
      "Set Printer Type",
      async () => {
        const result = await ThermalPrintService.setPrinterType("ble");
        if (typeof result !== "boolean") {
          throw new Error("Set printer type should return boolean");
        }
        return true;
      },
      suite
    );

    // Test 3: Get printer status
    await this.runTest(
      "Get Printer Status",
      async () => {
        const status = EnhancedThermalPrintService.getThermalPrinterStatus();
        if (!status || typeof status.isConnected !== "boolean") {
          throw new Error("Printer status should have isConnected property");
        }
        return true;
      },
      suite
    );

    // Test 4: Check printer availability
    await this.runTest(
      "Check Printer Availability",
      async () => {
        const available =
          await EnhancedThermalPrintService.isThermalPrinterAvailable();
        if (typeof available !== "boolean") {
          throw new Error("Printer availability check should return boolean");
        }
        return true;
      },
      suite
    );

    this.testResults.push(suite);
  }

  /**
   * Test printing functionality
   */
  private static async runPrintingTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: "Printing Functionality",
      results: [],
      passed: 0,
      failed: 0,
      totalDuration: 0,
    };

    // Test 1: Generate test receipt data
    await this.runTest(
      "Generate Test Receipt Data",
      async () => {
        const testData = this.generateTestReceiptData();
        if (
          !testData ||
          !testData.orderNumber ||
          !testData.items ||
          testData.items.length === 0
        ) {
          throw new Error("Test receipt data should be properly formatted");
        }
        return true;
      },
      suite
    );

    // Test 2: Validate receipt formatting
    await this.runTest(
      "Validate Receipt Formatting",
      async () => {
        const testData = this.generateTestReceiptData();

        // Check required fields
        const requiredFields = [
          "orderNumber",
          "customer",
          "staff",
          "items",
          "total",
          "store",
        ];
        for (const field of requiredFields) {
          if (!testData[field as keyof ReceiptData]) {
            throw new Error(`Receipt data missing required field: ${field}`);
          }
        }

        // Check KES currency formatting
        if (testData.total <= 0) {
          throw new Error("Receipt total should be greater than 0");
        }

        return true;
      },
      suite
    );

    // Test 3: Test offline receipt storage
    await this.runTest(
      "Test Offline Receipt Storage",
      async () => {
        const offlineReceipts =
          await EnhancedThermalPrintService.getOfflineReceipts();
        if (!Array.isArray(offlineReceipts)) {
          throw new Error("Offline receipts should return an array");
        }
        return true;
      },
      suite
    );

    this.testResults.push(suite);
  }

  /**
   * Test integration with existing systems
   */
  private static async runIntegrationTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: "Integration Tests",
      results: [],
      passed: 0,
      failed: 0,
      totalDuration: 0,
    };

    // Test 1: Receipt generator integration
    await this.runTest(
      "Receipt Generator Integration",
      async () => {
        // Import ReceiptGenerator dynamically to avoid circular dependencies
        const { ReceiptGenerator } = await import(
          "../components/receipt/ReceiptGenerator"
        );

        if (typeof ReceiptGenerator.isThermalPrintingAvailable !== "function") {
          throw new Error(
            "ReceiptGenerator should have isThermalPrintingAvailable method"
          );
        }

        if (typeof ReceiptGenerator.printThermalReceipt !== "function") {
          throw new Error(
            "ReceiptGenerator should have printThermalReceipt method"
          );
        }

        return true;
      },
      suite
    );

    // Test 2: Test order data compatibility
    await this.runTest(
      "Order Data Compatibility",
      async () => {
        const testOrderData = this.generateTestOrderData();

        // Check order data structure
        const requiredFields = ["id", "totalPrice", "lineItems"];
        for (const field of requiredFields) {
          if (!testOrderData[field as keyof typeof testOrderData]) {
            throw new Error(`Order data missing required field: ${field}`);
          }
        }

        return true;
      },
      suite
    );

    this.testResults.push(suite);
  }

  /**
   * Run individual test with timing and error handling
   */
  private static async runTest(
    testName: string,
    testFunction: () => Promise<boolean>,
    suite: TestSuite
  ): Promise<void> {
    const startTime = Date.now();

    try {
      console.log(`  🔍 Running: ${testName}`);
      const result = await testFunction();
      const duration = Date.now() - startTime;

      suite.results.push({
        testName,
        passed: result,
        duration,
      });

      if (result) {
        suite.passed++;
        console.log(`  ✅ Passed: ${testName} (${duration}ms)`);
      } else {
        suite.failed++;
        console.log(`  ❌ Failed: ${testName} (${duration}ms)`);
      }

      suite.totalDuration += duration;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";

      suite.results.push({
        testName,
        passed: false,
        error: errorMessage,
        duration,
      });

      suite.failed++;
      suite.totalDuration += duration;

      console.log(`  ❌ Failed: ${testName} (${duration}ms) - ${errorMessage}`);
    }
  }

  /**
   * Display comprehensive test results
   */
  private static displayTestResults(): void {
    console.log("\n📊 Test Results Summary:");
    console.log("========================");

    let totalPassed = 0;
    let totalFailed = 0;
    let totalDuration = 0;

    for (const suite of this.testResults) {
      console.log(`\n📋 ${suite.suiteName}:`);
      console.log(`   Passed: ${suite.passed}`);
      console.log(`   Failed: ${suite.failed}`);
      console.log(`   Duration: ${suite.totalDuration}ms`);

      totalPassed += suite.passed;
      totalFailed += suite.failed;
      totalDuration += suite.totalDuration;

      // Show failed tests
      const failedTests = suite.results.filter((r) => !r.passed);
      if (failedTests.length > 0) {
        console.log(`   Failed Tests:`);
        for (const test of failedTests) {
          console.log(`     - ${test.testName}: ${test.error}`);
        }
      }
    }

    console.log("\n🎯 Overall Results:");
    console.log(`   Total Passed: ${totalPassed}`);
    console.log(`   Total Failed: ${totalFailed}`);
    console.log(`   Total Duration: ${totalDuration}ms`);
    console.log(
      `   Success Rate: ${(
        (totalPassed / (totalPassed + totalFailed)) *
        100
      ).toFixed(1)}%`
    );

    // Show alert with results
    Alert.alert(
      "Test Results",
      `Passed: ${totalPassed}\nFailed: ${totalFailed}\nSuccess Rate: ${(
        (totalPassed / (totalPassed + totalFailed)) *
        100
      ).toFixed(1)}%`,
      [{ text: "OK" }]
    );
  }

  /**
   * Generate test receipt data
   */
  static generateTestReceiptData(): ReceiptData {
    return {
      orderNumber: "TEST-" + Date.now(),
      orderDate: new Date().toISOString(),
      customer: {
        name: "Test Customer",
        email: "<EMAIL>",
        phone: "+254700000000",
      },
      staff: {
        name: "Test Staff",
        role: "Cashier",
      },
      salesAgent: {
        name: "Test Sales Agent",
        territory: "Nairobi",
      },
      items: [
        {
          id: "1",
          title: "Test Product 1",
          quantity: 2,
          price: "150.00",
          sku: "TEST-001",
          variantTitle: "Medium",
        },
        {
          id: "2",
          title: "Test Product 2 with Very Long Name That Should Wrap Properly",
          quantity: 1,
          price: "300.00",
          sku: "TEST-002",
        },
        {
          id: "3",
          title: "Test Product 3",
          quantity: 3,
          price: "50.00",
          sku: "TEST-003",
          variantTitle: "Small",
        },
      ],
      subtotal: 600.0,
      tax: 0, // No tax calculations in POS system
      total: 600.0,
      paymentMethod: "Cash",
      paymentDetails: {
        transactionId: "TXN-TEST-" + Date.now(),
      },
      store: {
        name: "Test Store",
        address: "Test Address, Nairobi, Kenya",
        phone: "+254700000000",
      },
    };
  }

  /**
   * Generate test order data
   */
  static generateTestOrderData() {
    return {
      id: "order-test-" + Date.now(),
      orderNumber: "TEST-" + Date.now(),
      totalPrice: "696.00",
      createdAt: new Date().toISOString(),
      customer: {
        firstName: "Test",
        lastName: "Customer",
        email: "<EMAIL>",
        phone: "+254700000000",
      },
      salespersonName: "Test Staff",
      salespersonId: "staff-1",
      paymentMethod: "Cash",
      lineItems: [
        {
          id: "1",
          title: "Test Product 1",
          quantity: 2,
          price: "150.00",
          sku: "TEST-001",
          variantTitle: "Medium",
        },
        {
          id: "2",
          title: "Test Product 2",
          quantity: 1,
          price: "300.00",
          sku: "TEST-002",
        },
      ],
    };
  }

  /**
   * Quick thermal printer connectivity test
   */
  static async quickConnectivityTest(): Promise<boolean> {
    try {
      console.log("🔍 Running Quick Connectivity Test...");

      // Check permissions
      const permissions =
        await BluetoothPermissionHelper.checkBluetoothPermissions();
      if (!permissions.granted) {
        console.log("❌ Bluetooth permissions not granted");
        return false;
      }

      // Check Bluetooth state
      const bluetoothEnabled =
        await BluetoothPermissionHelper.isBluetoothEnabled();
      if (!bluetoothEnabled) {
        console.log("❌ Bluetooth not enabled");
        return false;
      }

      // Check service initialization
      const serviceInit = await ThermalPrintService.init();
      if (!serviceInit) {
        console.log("❌ Service initialization failed");
        return false;
      }

      console.log("✅ Quick connectivity test passed");
      return true;
    } catch (error) {
      console.log("❌ Quick connectivity test failed:", error);
      return false;
    }
  }
}

export default ThermalPrintingTestUtils;
