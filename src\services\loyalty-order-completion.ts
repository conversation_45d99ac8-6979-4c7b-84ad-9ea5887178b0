/**
 * Loyalty Order Completion Service
 *
 * Handles loyalty points addition, tier progression checks, and cache invalidation
 * during the order completion flow.
 */

import { loyaltyService } from "./loyalty-service";
import { getAPIClient } from "./api/dukalink-client";
import { useQueryClient } from "@tanstack/react-query";
import { getQueryClientInstance } from "@/src/providers/QueryClientProvider";
import { getInvalidationKeys } from "@/src/lib/queryKeys";

export interface OrderCompletionData {
  orderId: string;
  orderNumber: string;
  customerId: string;
  orderTotal: number;
  salesAgentId?: string;
  staffId: string;
  lineItems: Array<{
    id: string;
    title: string;
    quantity: number;
    price: number;
    sku?: string;
    variantId?: string;
    productId?: string;
  }>;
  paymentMethod: string;
  transactionId: string;
  loyaltyDiscount?: {
    type: "tier" | "points";
    amount: number;
    metadata?: any;
  };
}

export interface LoyaltyCompletionResult {
  success: boolean;
  pointsAdded: number;
  newBalance: number;
  tierChanged: boolean;
  newTier?: string;
  transactionId: string;
  error?: string;
}

class LoyaltyOrderCompletionService {
  private apiClient = getAPIClient();

  /**
   * Process loyalty points addition for completed order
   */
  async processLoyaltyCompletion(
    orderData: OrderCompletionData
  ): Promise<LoyaltyCompletionResult> {
    const startTime = Date.now();
    console.log("🔄 Starting loyalty completion processing:", {
      orderId: orderData.orderId,
      customerId: orderData.customerId,
      orderTotal: orderData.orderTotal,
      staffId: orderData.staffId,
      salesAgentId: orderData.salesAgentId,
      loyaltyDiscount: orderData.loyaltyDiscount,
      timestamp: new Date().toISOString(),
    });

    try {
      // Enhanced input validation
      if (!orderData.customerId) {
        console.error("❌ Loyalty processing failed: Missing customer ID", {
          orderData: { ...orderData, customerId: "[MISSING]" },
        });
        return {
          success: false,
          pointsAdded: 0,
          newBalance: 0,
          tierChanged: false,
          transactionId: "",
          error: "Customer ID is required for loyalty processing",
        };
      }

      if (!orderData.orderId) {
        console.error("❌ Loyalty processing failed: Missing order ID", {
          customerId: orderData.customerId,
          orderTotal: orderData.orderTotal,
        });
        return {
          success: false,
          pointsAdded: 0,
          newBalance: 0,
          tierChanged: false,
          transactionId: "",
          error: "Order ID is required for loyalty processing",
        };
      }

      // Check eligibility before processing
      if (!this.isEligibleForLoyalty(orderData)) {
        console.log("⚠️ Customer not eligible for loyalty points:", {
          customerId: orderData.customerId,
          orderTotal: orderData.orderTotal,
          eligibilityReason: "Failed eligibility check",
        });
        return {
          success: false,
          pointsAdded: 0,
          newBalance: 0,
          tierChanged: false,
          transactionId: "",
          error: "Customer not eligible for loyalty points",
        };
      }

      // Calculate the order total for loyalty points (excluding any loyalty discounts)
      const loyaltyEligibleTotal = orderData.loyaltyDiscount
        ? orderData.orderTotal + orderData.loyaltyDiscount.amount
        : orderData.orderTotal;

      console.log("💰 Calculated loyalty eligible total:", {
        originalTotal: orderData.orderTotal,
        loyaltyDiscount: orderData.loyaltyDiscount?.amount || 0,
        eligibleTotal: loyaltyEligibleTotal,
      });

      // Add loyalty points
      const pointsResult = await loyaltyService.addLoyaltyPoints(
        orderData.customerId,
        {
          orderTotal: loyaltyEligibleTotal,
          orderId: orderData.orderId,
          salesAgentId: orderData.salesAgentId,
        }
      );

      if (!pointsResult) {
        throw new Error("Failed to add loyalty points");
      }

      console.log("✅ Loyalty points added successfully:", {
        pointsAdded: pointsResult.pointsAdded,
        newBalance: pointsResult.newBalance,
        tierChanged: pointsResult.tierChanged,
        newTier: pointsResult.newTier,
      });

      // Update Shopify customer metafields with new loyalty data
      await this.updateShopifyLoyaltyMetafields(orderData.customerId, {
        loyaltyPoints: pointsResult.newBalance,
        loyaltyTier: pointsResult.newTier || "bronze",
        lastOrderId: orderData.orderId,
        lastOrderTotal: loyaltyEligibleTotal,
        lastPointsAdded: pointsResult.pointsAdded,
      });

      // Invalidate relevant caches
      await this.invalidateLoyaltyCaches(orderData.customerId);

      const processingTime = Date.now() - startTime;
      const successResult = {
        success: true,
        pointsAdded: pointsResult.pointsAdded,
        newBalance: pointsResult.newBalance,
        tierChanged: pointsResult.tierChanged,
        newTier: pointsResult.newTier,
        transactionId: pointsResult.transactionId,
      };

      console.log("🎉 Loyalty completion processing successful:", {
        orderId: orderData.orderId,
        customerId: orderData.customerId,
        pointsAdded: pointsResult.pointsAdded,
        newBalance: pointsResult.newBalance,
        tierChanged: pointsResult.tierChanged,
        newTier: pointsResult.newTier,
        processingTime: `${processingTime}ms`,
        timestamp: new Date().toISOString(),
      });

      return successResult;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error("❌ Loyalty completion processing failed:", {
        orderId: orderData.orderId,
        customerId: orderData.customerId,
        orderTotal: orderData.orderTotal,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
        processingTime: `${processingTime}ms`,
        timestamp: new Date().toISOString(),
      });

      return {
        success: false,
        pointsAdded: 0,
        newBalance: 0,
        tierChanged: false,
        transactionId: "",
        error:
          error instanceof Error
            ? error.message
            : "Unknown error occurred during loyalty processing",
      };
    }
  }

  /**
   * Update Shopify customer metafields with loyalty information
   */
  private async updateShopifyLoyaltyMetafields(
    customerId: string,
    loyaltyData: {
      loyaltyPoints: number;
      loyaltyTier: string;
      lastOrderId: string;
      lastOrderTotal: number;
      lastPointsAdded: number;
    }
  ): Promise<void> {
    try {
      console.log(
        "🔄 Updating Shopify loyalty metafields for customer:",
        customerId
      );

      const metafields = [
        {
          namespace: "loyalty",
          key: "points_balance",
          value: loyaltyData.loyaltyPoints.toString(),
          type: "number_integer",
        },
        {
          namespace: "loyalty",
          key: "tier",
          value: loyaltyData.loyaltyTier,
          type: "single_line_text_field",
        },
        {
          namespace: "loyalty",
          key: "last_order_id",
          value: loyaltyData.lastOrderId,
          type: "single_line_text_field",
        },
        {
          namespace: "loyalty",
          key: "last_order_total",
          value: loyaltyData.lastOrderTotal.toString(),
          type: "number_decimal",
        },
        {
          namespace: "loyalty",
          key: "last_points_added",
          value: loyaltyData.lastPointsAdded.toString(),
          type: "number_integer",
        },
        {
          namespace: "loyalty",
          key: "last_updated",
          value: new Date().toISOString(),
          type: "date_time",
        },
      ];

      // Update metafields via API
      for (const metafield of metafields) {
        try {
          await this.apiClient.request(
            `/shopify/customers/${customerId}/metafields`,
            {
              method: "POST",
              data: metafield,
            }
          );
        } catch (error) {
          console.warn(
            `⚠️ Failed to update metafield ${metafield.key}:`,
            error
          );
          // Continue with other metafields even if one fails
        }
      }

      console.log("✅ Shopify loyalty metafields updated successfully");
    } catch (error) {
      console.error("❌ Error updating Shopify loyalty metafields:", error);
      // Don't throw error as this is not critical for order completion
    }
  }

  /**
   * Invalidate loyalty-related caches for real-time updates
   */
  private async invalidateLoyaltyCaches(customerId: string): Promise<void> {
    try {
      console.log("🔄 Invalidating loyalty caches for customer:", customerId);

      // Get query client instance and invalidate caches directly
      const queryClient = getQueryClientInstance();
      const invalidationKeys = getInvalidationKeys.onLoyaltyUpdate(customerId);

      invalidationKeys.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      // Also invalidate customer data to refresh loyalty information
      const customerInvalidationKeys =
        getInvalidationKeys.onCustomerUpdate(customerId);
      customerInvalidationKeys.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      console.log(
        "✅ Loyalty cache invalidation completed for customer:",
        customerId
      );
    } catch (error) {
      console.error("❌ Error invalidating loyalty caches:", error);
      // Don't throw error as this is not critical for order completion
    }
  }

  /**
   * Check if customer is eligible for loyalty points
   */
  isEligibleForLoyalty(orderData: OrderCompletionData): boolean {
    // Basic eligibility checks
    if (!orderData.customerId || orderData.orderTotal <= 0) {
      return false;
    }

    // Could add more sophisticated eligibility rules here
    // e.g., minimum order amount, customer status, product categories, etc.

    return true;
  }

  /**
   * Calculate tier progression information
   */
  async getTierProgressionInfo(customerId: string): Promise<{
    currentTier: string;
    nextTier?: string;
    progressToNext?: number;
    benefitsUnlocked?: string[];
  } | null> {
    try {
      const loyaltyData = await loyaltyService.getCustomerLoyaltySummary(
        customerId
      );

      if (!loyaltyData) {
        return null;
      }

      return {
        currentTier: loyaltyData.loyaltyTier,
        nextTier: loyaltyData.progressToNextTier?.nextTier,
        progressToNext: loyaltyData.progressToNextTier?.purchaseProgress,
        benefitsUnlocked: this.getTierBenefits(loyaltyData.loyaltyTier),
      };
    } catch (error) {
      console.error("Error getting tier progression info:", error);
      return null;
    }
  }

  /**
   * Get benefits for a specific tier
   */
  private getTierBenefits(tier: string): string[] {
    const benefits: Record<string, string[]> = {
      bronze: ["1x points per KSh", "Basic customer support"],
      silver: [
        "1.2x points per KSh",
        "Priority customer support",
        "5% tier discount",
      ],
      gold: [
        "1.5x points per KSh",
        "Premium customer support",
        "10% tier discount",
        "Early access to sales",
      ],
      platinum: [
        "2x points per KSh",
        "VIP customer support",
        "15% tier discount",
        "Exclusive products",
        "Free shipping",
      ],
    };

    return benefits[tier] || benefits.bronze;
  }

  /**
   * Create a summary of the loyalty completion for display
   */
  createCompletionSummary(
    orderData: OrderCompletionData,
    loyaltyResult: LoyaltyCompletionResult
  ): {
    pointsEarned: number;
    newBalance: number;
    tierStatus: {
      current: string;
      changed: boolean;
      previous?: string;
    };
    orderTotal: number;
    loyaltyDiscount?: {
      type: string;
      amount: number;
    };
  } {
    return {
      pointsEarned: loyaltyResult.pointsAdded,
      newBalance: loyaltyResult.newBalance,
      tierStatus: {
        current: loyaltyResult.newTier || "bronze",
        changed: loyaltyResult.tierChanged,
        previous: loyaltyResult.tierChanged ? undefined : loyaltyResult.newTier,
      },
      orderTotal: orderData.orderTotal,
      loyaltyDiscount: orderData.loyaltyDiscount
        ? {
            type: orderData.loyaltyDiscount.type,
            amount: orderData.loyaltyDiscount.amount,
          }
        : undefined,
    };
  }
}

// Export singleton instance
export const loyaltyOrderCompletionService =
  new LoyaltyOrderCompletionService();

// Export hook for cache invalidation in React components
export const useLoyaltyCacheInvalidation = () => {
  const queryClient = useQueryClient();

  const invalidateLoyaltyCaches = (customerId: string) => {
    const invalidationKeys = getInvalidationKeys.onLoyaltyUpdate(customerId);
    invalidationKeys.forEach((key) => {
      queryClient.invalidateQueries({ queryKey: key });
    });

    console.log("✅ Loyalty caches invalidated for customer:", customerId);
  };

  return { invalidateLoyaltyCaches };
};

// Export function for cache invalidation in non-React contexts
export const invalidateLoyaltyCaches = (customerId: string) => {
  const queryClient = getQueryClientInstance();
  const invalidationKeys = getInvalidationKeys.onLoyaltyUpdate(customerId);

  invalidationKeys.forEach((key) => {
    queryClient.invalidateQueries({ queryKey: key });
  });

  console.log("✅ Loyalty caches invalidated for customer:", customerId);
};
