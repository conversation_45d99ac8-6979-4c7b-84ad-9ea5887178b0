import { useSession } from "@/src/contexts/AuthContext";
import { useCustomer } from "@/src/contexts/CustomerContext";
import { useLocation } from "@/src/contexts/LocationContext";
import { useSalesAgent } from "@/src/contexts/SalesAgentContext";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import PaymentService from "@/src/services/payment-service";
import { ticketLifecycleService } from "@/src/services/TicketLifecycleService";
import { useAppDispatch, useAppSelector } from "@/src/store";
import {
  clearCart,
  selectCartItems,
  selectCartTotal,
} from "@/src/store/slices/cartSlice";
import { selectActiveTicket } from "@/src/store/slices/ticketSlice";
import { PaymentMethod } from "@/src/types/payment";
import { Ionicons } from "@expo/vector-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import EnhancedThermalPrintService from "../src/services/EnhancedThermalPrintService";

const PaymentProcessingScreen: React.FC = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { user } = useSession();
  const dispatch = useAppDispatch();

  // Cart and context data
  const cartItems = useAppSelector(selectCartItems);
  const cartTotal = useAppSelector(selectCartTotal);
  const { selectedCustomer, clearSelectedCustomer } = useCustomer();
  const { selectedAgent, clearSelectedAgent } = useSalesAgent();
  const { currentLocation } = useLocation();

  const methodId = params.methodId as string;
  const total = parseFloat(params.total as string) || 0;
  const customerName = (params.customerName as string) || "Walk-in Customer";

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
    null
  );
  const [amountTendered, setAmountTendered] = useState("");
  const [transactionCode, setTransactionCode] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [change, setChange] = useState(0);
  const [validationError, setValidationError] = useState("");

  useEffect(() => {
    // Only proceed if we have a valid methodId
    if (!methodId) {
      console.warn("PaymentProcessingScreen: No methodId provided");
      return;
    }

    try {
      const method = PaymentService.getPaymentMethodById(methodId);
      if (!method) {
        console.warn(
          `PaymentProcessingScreen: Payment method not found for ID: ${methodId}`
        );
        setPaymentMethod(null);
        return;
      }

      setPaymentMethod(method);

      // For cash payments, pre-fill with exact amount
      if (method.type === "cash") {
        setAmountTendered(total.toString());
      }
    } catch (error) {
      console.error(
        "PaymentProcessingScreen: Error loading payment method:",
        error
      );
      setPaymentMethod(null);
    }
  }, [methodId, total]);

  useEffect(() => {
    if (
      (paymentMethod?.type === "cash" ||
        paymentMethod?.type === "mpesa_till") &&
      amountTendered
    ) {
      const tendered = parseFloat(amountTendered) || 0;
      const validation = PaymentService.validateCashTender(total, tendered);

      if (validation.valid) {
        setChange(validation.change || 0);
        setValidationError("");
      } else {
        setChange(0);
        setValidationError(validation.error || "");
      }
    }
  }, [amountTendered, total, paymentMethod]);

  const handleQuickAmount = (amount: number) => {
    setAmountTendered(amount.toString());
  };

  // Helper function to handle thermal printing after order creation
  const handleThermalPrinting = async (orderData: any) => {
    try {
      // Check if thermal printer is available
      const isAvailable =
        await EnhancedThermalPrintService.isThermalPrinterAvailable();

      if (isAvailable) {
        console.log(
          "Thermal printer available, attempting to print receipt..."
        );

        // Attempt thermal printing
        const printResult = await EnhancedThermalPrintService.printReceipt(
          orderData
        );

        if (printResult.success) {
          console.log("Thermal receipt printed successfully");
        } else {
          console.warn("Thermal printing failed:", printResult.error);
          // Don't show error to user as this is automatic printing
        }
      } else {
        console.log("No thermal printer available, showing setup dialog...");

        // Show dialog to setup printer
        Alert.alert(
          "Print Receipt",
          "No thermal printer connected. Would you like to set up a printer for automatic receipt printing?",
          [
            {
              text: "Cancel",
              style: "cancel",
            },
            {
              text: "Setup Printer",
              onPress: () => {
                router.push("/thermal-printer-setup");
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error("Error handling thermal printing:", error);
      // Don't show error to user for automatic printing
    }
  };

  // Generate order data for receipt from cart and payment information
  const generateOrderDataForReceipt = (
    orderNumber: string,
    paymentResult: any
  ) => {
    return {
      id: `order_${Date.now()}`,
      orderNumber,
      totalPrice: total.toString(),
      createdAt: new Date().toISOString(),
      salespersonName: user?.name || "Unknown Staff",
      salespersonId: selectedAgent?.id,
      paymentMethod: paymentResult.method.name,
      paymentTransactionId: paymentResult.transactionId,
      customer: selectedCustomer
        ? {
            firstName: selectedCustomer.firstName,
            lastName: selectedCustomer.lastName,
            email: selectedCustomer.email,
            phone: selectedCustomer.phone,
          }
        : undefined,
      lineItems: cartItems.map((item) => ({
        id: item.variantId || item.productId,
        title: item.title,
        quantity: item.quantity,
        price: item.price,
        sku: item.sku,
        variantId: item.variantId,
        productId: item.productId,
      })),
    };
  };

  const createOrderWithPayment = async (paymentResult: any) => {
    try {
      if (!selectedCustomer || !selectedAgent || !user || !currentLocation) {
        Alert.alert("Error", "Missing required information for order creation");
        return;
      }

      // Get active ticket for completion
      const activeTicket = useAppSelector(selectActiveTicket);

      const apiClient = getAPIClient();

      // Prepare order data with payment information
      const orderData = {
        lineItems: cartItems.map((item) => ({
          variantId: item.variantId,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          title: item.title,
          sku: item.sku,
        })),
        customer: selectedCustomer.email
          ? {
              id: selectedCustomer.id,
              email: selectedCustomer.email,
              firstName: selectedCustomer.firstName,
              lastName: selectedCustomer.lastName,
              phone: selectedCustomer.phone,
            }
          : undefined,
        staffId: user.id,
        salesAgentId: selectedAgent.id,
        email:
          selectedCustomer.email ||
          `${selectedCustomer.firstName.toLowerCase()}.${selectedCustomer.lastName.toLowerCase()}@pos.local`,
        phone: selectedCustomer.phone,
        note: `POS Order by ${user.name} (${user.role}) | Sales Agent: ${selectedAgent.name} | Payment: ${paymentResult.method.name} | Transaction: ${paymentResult.transactionId}`,
        tags: `POS,Dukalink,Staff:${user.username},Agent:${selectedAgent.name},Payment:${paymentResult.method.type},Location:${currentLocation.name}`,
        billingAddress: {
          firstName: selectedCustomer.firstName,
          lastName: selectedCustomer.lastName,
          phone: selectedCustomer.phone,
          address1: "POS Sale",
          city: currentLocation.name || "Store Location",
          country: "Kenya",
          zip: "00100",
        },
        // Payment information
        paymentMethod: paymentResult.method.name,
        paymentTransactionId: paymentResult.transactionId,
        paymentTimestamp: paymentResult.timestamp,
        financialStatus: "paid", // Mark as paid since payment was processed
        // Location information
        locationId: currentLocation.id,
        locationName: currentLocation.name,
      };

      const response = await apiClient.createOrderWithDualAttribution(
        orderData
      );

      if (response.success) {
        // Order created successfully
        const orderNumber =
          response?.data?.order.name || response?.data?.order.id || "Unknown";

        // Complete the ticket after successful order creation
        if (activeTicket) {
          const completionResult =
            await ticketLifecycleService.completeTicketAfterPayment(
              activeTicket.id,
              {
                success: true,
                transactionId: paymentResult.transactionId,
                method: paymentResult.method.name,
                amount: paymentResult.amount,
                timestamp: new Date().toISOString(),
              },
              user.id
            );

          if (completionResult.success) {
            console.log(
              `✅ Ticket ${activeTicket.id} completed after successful payment`
            );
          } else {
            console.error(
              `❌ Failed to complete ticket: ${completionResult.error}`
            );
          }
        }

        // Generate order data for receipt
        const orderData = generateOrderDataForReceipt(
          orderNumber,
          paymentResult
        );

        // Attempt automatic thermal printing
        await handleThermalPrinting(orderData);

        Alert.alert(
          "Order Created Successfully!",
          `Order #${orderNumber} has been created and paid.\n\nTotal: ${PaymentService.formatCurrency(
            total
          )}\nPayment: ${paymentResult.method.name}${
            (paymentResult.method.type === "cash" ||
              paymentResult.method.type === "credit" ||
              paymentResult.method.type === "absa_till") &&
            change > 0
              ? `\nChange: ${PaymentService.formatCurrency(change)}`
              : ""
          }\nTransaction: ${paymentResult.transactionId}`,
          [
            {
              text: "Print Receipt",
              onPress: async () => {
                try {
                  const printResult =
                    await EnhancedThermalPrintService.printReceipt(orderData);
                  if (printResult.success) {
                    Alert.alert("Success", "Receipt printed successfully!");
                  } else {
                    Alert.alert(
                      "Print Error",
                      printResult.error || "Failed to print receipt"
                    );
                  }
                } catch (error) {
                  console.error("Receipt printing error:", error);
                  Alert.alert("Error", "Failed to print receipt");
                }
              },
            },
            {
              text: "New Sale",
              onPress: () => {
                // Clear cart and selections
                dispatch(clearCart());
                clearSelectedCustomer();
                clearSelectedAgent();

                // Navigate back to products
                router.replace("/(tabs)/products");
              },
            },
          ]
        );
      } else {
        Alert.alert(
          "Order Creation Failed",
          response.error || "Failed to create order"
        );
      }
    } catch (error: any) {
      console.error("Order creation error:", error);
      Alert.alert("Error", "Failed to create order. Please try again.");
    }
  };

  const getQuickAmounts = () => {
    const amounts = [];
    const roundedTotal = Math.ceil(total / 50) * 50; // Round up to nearest 50

    amounts.push(total); // Exact amount
    amounts.push(roundedTotal); // Rounded amount
    amounts.push(roundedTotal + 50); // +50
    amounts.push(roundedTotal + 100); // +100

    if (total >= 500) {
      amounts.push(Math.ceil(total / 100) * 100); // Round to nearest 100
    }

    return [...new Set(amounts)].sort((a, b) => a - b);
  };

  const handleProcessPayment = async () => {
    if (!paymentMethod || !user) {
      Alert.alert("Error", "Missing payment information");
      return;
    }

    if (
      paymentMethod?.type === "cash" ||
      paymentMethod?.type === "mpesa_till"
    ) {
      const tendered = parseFloat(amountTendered) || 0;
      const validation = PaymentService.validateCashTender(total, tendered);

      if (!validation.valid) {
        Alert.alert("Invalid Amount", validation.error);
        return;
      }

      // Transaction code for Mpesa Till is now optional
      // Payment can proceed without transaction code for manual entry later
    }

    setIsProcessing(true);

    try {
      const staffInfo = {
        id: user.id,
        name: user.name,
        terminal: "Current Terminal", // Will be populated from context
      };

      const customerInfo = {
        name: customerName,
        phone: selectedCustomer?.phone || "",
      };

      const additionalData =
        paymentMethod?.type === "cash"
          ? { amountTendered: parseFloat(amountTendered) }
          : paymentMethod?.type === "credit"
          ? { amountTendered: parseFloat(amountTendered) }
          : paymentMethod?.type === "absa_till"
          ? {
              tillNumber: "123456", // Default ABSA till number
              accountNumber: customerInfo.phone || "POS-PAYMENT",
              amountTendered: parseFloat(amountTendered),
              transactionCode: transactionCode.trim(),
            }
          : {};

      const result = await PaymentService.processPayment(
        {
          method: paymentMethod,
          amount: total,
          currency: "KES",
        },
        staffInfo,
        customerInfo,
        additionalData
      );

      if (result.success) {
        // For cash and Mpesa Till, create order immediately (both are processed instantly)
        await createOrderWithPayment(result);
      } else {
        Alert.alert(
          "Payment Failed",
          result.error || "Payment processing failed"
        );
      }
    } catch (error) {
      console.error("Payment processing error:", error);
      Alert.alert("Error", "Payment processing failed");
    } finally {
      setIsProcessing(false);
    }
  };

  // Early return if no methodId or invalid state
  if (!methodId) {
    return (
      <View style={styles.container}>
        <Text>Error: No payment method specified</Text>
      </View>
    );
  }

  if (!paymentMethod) {
    return (
      <View style={styles.container}>
        <Text>Loading payment method...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#1a1a1a" />
        </TouchableOpacity>
        <Text style={styles.title}>
          {paymentMethod?.name || "Payment"} Payment
        </Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        <View style={styles.paymentSummary}>
          <View style={styles.methodDisplay}>
            <Text style={styles.methodIcon}>{paymentMethod?.icon || "💳"}</Text>
            <Text style={styles.methodName}>
              {paymentMethod?.name || "Payment Method"}
            </Text>
          </View>

          <View style={styles.amountDisplay}>
            <Text style={styles.amountLabel}>Amount Due</Text>
            <Text style={styles.amountValue}>
              {PaymentService.formatCurrency(total)}
            </Text>
          </View>
        </View>

        {paymentMethod?.type === "cash" && (
          <View style={styles.cashPaymentSection}>
            <Text style={styles.sectionTitle}>Cash Payment</Text>

            <View style={styles.inputSection}>
              <Text style={styles.inputLabel}>Amount Tendered</Text>
              <TextInput
                style={[
                  styles.amountInput,
                  validationError && styles.amountInputError,
                ]}
                value={amountTendered}
                onChangeText={setAmountTendered}
                placeholder="0.00"
                keyboardType="numeric"
                selectTextOnFocus
              />
              {validationError ? (
                <Text style={styles.errorText}>{validationError}</Text>
              ) : null}
            </View>

            <View style={styles.quickAmounts}>
              <Text style={styles.quickAmountsLabel}>Quick Amounts:</Text>
              <View style={styles.quickAmountsGrid}>
                {getQuickAmounts().map((amount) => (
                  <TouchableOpacity
                    key={amount}
                    style={[
                      styles.quickAmountButton,
                      parseFloat(amountTendered) === amount &&
                        styles.quickAmountButtonSelected,
                    ]}
                    onPress={() => handleQuickAmount(amount)}
                  >
                    <Text
                      style={[
                        styles.quickAmountText,
                        parseFloat(amountTendered) === amount &&
                          styles.quickAmountTextSelected,
                      ]}
                    >
                      {PaymentService.formatCurrency(amount)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {change > 0 && (
              <View style={styles.changeDisplay}>
                <Text style={styles.changeLabel}>Change Due:</Text>
                <Text style={styles.changeAmount}>
                  {PaymentService.formatCurrency(change)}
                </Text>
              </View>
            )}
          </View>
        )}

        {paymentMethod?.type === "mpesa_till" && (
          <View style={styles.mpesaTillPaymentSection}>
            <Text style={styles.sectionTitle}>Mpesa Till Payment</Text>

            <View style={styles.mpesaTillInfo}>
              <View style={styles.mpesaTillInfoRow}>
                <Text style={styles.mpesaTillLabel}>Till Number:</Text>
                <Text style={styles.mpesaTillValue}>123456</Text>
              </View>

              <View style={styles.mpesaTillInfoRow}>
                <Text style={styles.mpesaTillLabel}>Account Number:</Text>
                <Text style={styles.mpesaTillValue}>
                  {selectedCustomer?.phone || "POS-PAYMENT"}
                </Text>
              </View>

              <View style={styles.mpesaTillInfoRow}>
                <Text style={styles.mpesaTillLabel}>Amount:</Text>
                <Text style={styles.mpesaTillValue}>
                  {PaymentService.formatCurrency(total)}
                </Text>
              </View>
            </View>

            <View style={styles.inputSection}>
              <Text style={styles.inputLabel}>Amount Paid</Text>
              <TextInput
                style={[
                  styles.amountInput,
                  validationError && styles.amountInputError,
                ]}
                value={amountTendered}
                onChangeText={setAmountTendered}
                placeholder="0.00"
                keyboardType="numeric"
                selectTextOnFocus
              />
              {validationError ? (
                <Text style={styles.errorText}>{validationError}</Text>
              ) : null}
            </View>

            <View style={styles.inputSection}>
              <Text style={styles.inputLabel}>Transaction Code (Optional)</Text>
              <TextInput
                style={styles.amountInput}
                value={transactionCode}
                onChangeText={setTransactionCode}
                placeholder="Enter Mpesa transaction code (optional)"
                autoCapitalize="characters"
                selectTextOnFocus
              />
              <Text style={styles.inputHint}>
                Transaction code can be entered now or later if needed
              </Text>
            </View>

            {change > 0 && (
              <View style={styles.changeDisplay}>
                <Text style={styles.changeLabel}>Change Due:</Text>
                <Text style={styles.changeAmount}>
                  {PaymentService.formatCurrency(change)}
                </Text>
              </View>
            )}

            <View style={styles.mpesaTillInstructions}>
              <Text style={styles.instructionsTitle}>Payment Process:</Text>
              <Text style={styles.instructionText}>
                1. Customer pays via Mpesa Till using the details above
              </Text>
              <Text style={styles.instructionText}>
                2. Enter the amount paid above
              </Text>
              <Text style={styles.instructionText}>
                3. Tap &quot;Process Payment&quot; to complete the transaction
              </Text>
            </View>
          </View>
        )}

        <View style={styles.customerInfo}>
          <Text style={styles.customerLabel}>Customer:</Text>
          <Text style={styles.customerName}>{customerName}</Text>
        </View>
      </View>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => router.back()}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.processButton,
            (validationError ||
              isProcessing ||
              (paymentMethod?.type === "mpesa_till" &&
                (!amountTendered || parseFloat(amountTendered) <= 0))) &&
              styles.processButtonDisabled,
          ]}
          onPress={handleProcessPayment}
          disabled={
            !!validationError ||
            isProcessing ||
            (paymentMethod?.type === "mpesa_till" &&
              (!amountTendered || parseFloat(amountTendered) <= 0))
          }
        >
          <Text style={styles.processButtonText}>
            {isProcessing ? "Processing..." : "Process Payment"}
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e8ed",
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  paymentSummary: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#e1e8ed",
  },
  methodDisplay: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  methodIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  methodName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  amountDisplay: {
    alignItems: "center",
  },
  amountLabel: {
    fontSize: 16,
    color: "#666",
    marginBottom: 4,
  },
  amountValue: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#2ecc71",
  },
  cashPaymentSection: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#e1e8ed",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    color: "#666",
    marginBottom: 8,
  },
  amountInput: {
    borderWidth: 1,
    borderColor: "#e1e8ed",
    borderRadius: 8,
    padding: 16,
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    backgroundColor: "#f8f9fa",
  },
  amountInputError: {
    borderColor: "#e74c3c",
  },
  errorText: {
    color: "#e74c3c",
    fontSize: 14,
    marginTop: 4,
    textAlign: "center",
  },
  quickAmounts: {
    marginBottom: 20,
  },
  quickAmountsLabel: {
    fontSize: 16,
    color: "#666",
    marginBottom: 12,
  },
  quickAmountsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  quickAmountButton: {
    backgroundColor: "#f8f9fa",
    borderWidth: 1,
    borderColor: "#e1e8ed",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  quickAmountButtonSelected: {
    backgroundColor: "#2ecc71",
    borderColor: "#2ecc71",
  },
  quickAmountText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  quickAmountTextSelected: {
    color: "#fff",
  },
  changeDisplay: {
    backgroundColor: "#f0f9f4",
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  changeLabel: {
    fontSize: 18,
    color: "#2ecc71",
    fontWeight: "600",
  },
  changeAmount: {
    fontSize: 24,
    color: "#2ecc71",
    fontWeight: "bold",
  },
  customerInfo: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#e1e8ed",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  customerLabel: {
    fontSize: 16,
    color: "#666",
  },
  customerName: {
    fontSize: 16,
    color: "#1a1a1a",
    fontWeight: "600",
  },
  footer: {
    flexDirection: "row",
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e1e8ed",
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#e74c3c",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
  },
  cancelButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  processButton: {
    flex: 2,
    backgroundColor: "#2ecc71",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
  },
  processButtonDisabled: {
    backgroundColor: "#bdc3c7",
  },
  processButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  // Mpesa Till specific styles
  mpesaTillPaymentSection: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#e1e8ed",
  },
  mpesaTillInfo: {
    marginBottom: 16,
  },
  mpesaTillInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#f5f5f5",
  },
  mpesaTillLabel: {
    fontSize: 16,
    color: "#666",
    fontWeight: "500",
  },
  mpesaTillValue: {
    fontSize: 16,
    color: "#1a1a1a",
    fontWeight: "600",
  },
  warningBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff3cd",
    borderColor: "#ffeaa7",
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 14,
    color: "#856404",
    marginLeft: 8,
    flex: 1,
  },
  mpesaTillInstructions: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 16,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
    lineHeight: 20,
  },
  inputHint: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    fontStyle: "italic",
  },
});

export default PaymentProcessingScreen;
