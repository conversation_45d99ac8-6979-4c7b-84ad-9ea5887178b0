/**
 * Resume Modal Component
 *
 * Displays recovery options when resumable tickets are available.
 * Provides user-friendly interface for session restoration with
 * detailed information about recoverable tickets.
 */

import { useTheme } from "@/src/contexts/ThemeContext";
import { ResumeResult } from "@/src/services/ResumeService";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  Alert,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface ResumeModalProps {
  visible: boolean;
  resumeResult: ResumeResult | null;
  onResume: (ticketIds: string[]) => void;
  onDismiss: () => void;
  onClearAll: () => void;
}

export const ResumeModal: React.FC<ResumeModalProps> = ({
  visible,
  resumeResult,
  onResume,
  onDismiss,
  onClearAll,
}) => {
  const { theme } = useTheme();
  const [selectedTickets, setSelectedTickets] = useState<Set<string>>(
    new Set()
  );
  const [isLoading, setIsLoading] = useState(false);

  const recoveredTickets = resumeResult?.recoveredTickets || [];

  const handleTicketToggle = (ticketId: string) => {
    const newSelected = new Set(selectedTickets);
    if (newSelected.has(ticketId)) {
      newSelected.delete(ticketId);
    } else {
      newSelected.add(ticketId);
    }
    setSelectedTickets(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedTickets.size === recoveredTickets.length) {
      setSelectedTickets(new Set());
    } else {
      setSelectedTickets(new Set(recoveredTickets.map((t) => t.id)));
    }
  };

  const handleResume = async () => {
    if (selectedTickets.size === 0) {
      Alert.alert(
        "No Selection",
        "Please select at least one ticket to resume."
      );
      return;
    }

    setIsLoading(true);
    try {
      await onResume(Array.from(selectedTickets));
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearAll = () => {
    Alert.alert(
      "Clear All Recovery Data",
      "This will permanently remove all recoverable tickets. Are you sure?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear All",
          style: "destructive",
          onPress: onClearAll,
        },
      ]
    );
  };

  const formatLastActivity = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 60) {
      return `${diffMins} minutes ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hours ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (!visible || !resumeResult || recoveredTickets.length === 0) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onDismiss}
    >
      <View
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        {/* Header */}
        <View
          style={[styles.header, { borderBottomColor: theme.colors.border }]}
        >
          <View style={styles.headerLeft}>
            <Ionicons
              name="refresh-circle"
              size={24}
              color={theme.colors.primary}
            />
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Resume Session
            </Text>
          </View>
          <TouchableOpacity onPress={onDismiss} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Info Section */}
          <View
            style={[
              styles.infoSection,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <Ionicons
              name="information-circle"
              size={20}
              color={theme.colors.primary}
            />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>
              We found {recoveredTickets.length} ticket
              {recoveredTickets.length !== 1 ? "s" : ""}
              from your previous session. Select which ones you'd like to
              resume.
            </Text>
          </View>

          {/* Select All Button */}
          <TouchableOpacity
            style={[
              styles.selectAllButton,
              { borderColor: theme.colors.border },
            ]}
            onPress={handleSelectAll}
          >
            <Ionicons
              name={
                selectedTickets.size === recoveredTickets.length
                  ? "checkbox"
                  : "square-outline"
              }
              size={20}
              color={theme.colors.primary}
            />
            <Text style={[styles.selectAllText, { color: theme.colors.text }]}>
              {selectedTickets.size === recoveredTickets.length
                ? "Deselect All"
                : "Select All"}
            </Text>
          </TouchableOpacity>

          {/* Tickets List */}
          <View style={styles.ticketsList}>
            {recoveredTickets.map((ticket) => (
              <TouchableOpacity
                key={ticket.id}
                style={[
                  styles.ticketCard,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: selectedTickets.has(ticket.id)
                      ? theme.colors.primary
                      : theme.colors.border,
                    borderWidth: selectedTickets.has(ticket.id) ? 2 : 1,
                  },
                ]}
                onPress={() => handleTicketToggle(ticket.id)}
              >
                <View style={styles.ticketHeader}>
                  <View style={styles.ticketInfo}>
                    <Text
                      style={[styles.ticketName, { color: theme.colors.text }]}
                    >
                      {ticket.name}
                    </Text>
                    <Text
                      style={[
                        styles.ticketMeta,
                        { color: theme.colors.textSecondary },
                      ]}
                    >
                      {ticket.items?.length || 0} items • Last activity:{" "}
                      {formatLastActivity(ticket.last_activity)}
                    </Text>
                  </View>
                  <View style={styles.ticketActions}>
                    <Text
                      style={[
                        styles.ticketTotal,
                        { color: theme.colors.primary },
                      ]}
                    >
                      {formatCurrency(ticket.total || 0)}
                    </Text>
                    <Ionicons
                      name={
                        selectedTickets.has(ticket.id)
                          ? "checkbox"
                          : "square-outline"
                      }
                      size={20}
                      color={
                        selectedTickets.has(ticket.id)
                          ? theme.colors.primary
                          : theme.colors.textSecondary
                      }
                    />
                  </View>
                </View>

                {/* Ticket Items Preview */}
                {ticket.items && ticket.items.length > 0 && (
                  <View style={styles.itemsPreview}>
                    {ticket.items
                      .slice(0, 3)
                      .map((item: any, index: number) => (
                        <Text
                          key={index}
                          style={[
                            styles.itemText,
                            { color: theme.colors.textSecondary },
                          ]}
                          numberOfLines={1}
                        >
                          {item.quantity}x {item.title}
                        </Text>
                      ))}
                    {ticket.items.length > 3 && (
                      <Text
                        style={[
                          styles.moreItems,
                          { color: theme.colors.textSecondary },
                        ]}
                      >
                        +{ticket.items.length - 3} more items
                      </Text>
                    )}
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
          <TouchableOpacity
            style={[styles.clearButton, { borderColor: theme.colors.border }]}
            onPress={handleClearAll}
          >
            <Text
              style={[
                styles.clearButtonText,
                { color: theme.colors.textSecondary },
              ]}
            >
              Clear All
            </Text>
          </TouchableOpacity>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[
                styles.dismissButton,
                { backgroundColor: theme.colors.surface },
              ]}
              onPress={onDismiss}
            >
              <Text
                style={[styles.dismissButtonText, { color: theme.colors.text }]}
              >
                Skip
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.resumeButton,
                {
                  backgroundColor:
                    selectedTickets.size > 0
                      ? theme.colors.primary
                      : theme.colors.disabled,
                },
              ]}
              onPress={handleResume}
              disabled={selectedTickets.size === 0 || isLoading}
            >
              <Text
                style={[
                  styles.resumeButtonText,
                  {
                    color:
                      selectedTickets.size > 0
                        ? theme.colors.onPrimary
                        : theme.colors.textSecondary,
                  },
                ]}
              >
                {isLoading ? "Resuming..." : `Resume (${selectedTickets.size})`}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  infoSection: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    padding: 16,
    borderRadius: 8,
    marginVertical: 16,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  selectAllButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
  },
  selectAllText: {
    fontSize: 16,
    fontWeight: "500",
  },
  ticketsList: {
    gap: 12,
    paddingBottom: 20,
  },
  ticketCard: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  ticketHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  ticketInfo: {
    flex: 1,
    marginRight: 12,
  },
  ticketName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  ticketMeta: {
    fontSize: 12,
  },
  ticketActions: {
    alignItems: "flex-end",
    gap: 8,
  },
  ticketTotal: {
    fontSize: 16,
    fontWeight: "600",
  },
  itemsPreview: {
    gap: 2,
  },
  itemText: {
    fontSize: 12,
  },
  moreItems: {
    fontSize: 12,
    fontStyle: "italic",
  },
  footer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  clearButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 6,
  },
  clearButtonText: {
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
  },
  dismissButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  dismissButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  resumeButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  resumeButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ResumeModal;
