/**
 * ResponsiveFlatGrid Component
 * A FlatList-like component that renders items in a responsive grid layout
 * Adapts to different screen sizes and breakpoints
 */

import React, { useMemo } from "react";
import {
  FlatList,
  View,
  ViewStyle,
  ListRenderItem,
  RefreshControl,
  ActivityIndicator,
} from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";
import { useTheme } from "@/src/contexts/ThemeContext";
import { GRID_COLUMNS } from "@/src/constants/ResponsiveConstants";

interface ResponsiveFlatGridProps<T> {
  data: T[];
  renderItem: ListRenderItem<T>;
  keyExtractor: (item: T, index: number) => string;
  minItemWidth?: number;
  spacing?: number;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
  refreshControl?: React.ReactElement<RefreshControl>;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListEmptyComponent?: React.ComponentType<any> | React.ReactElement | null;
  numColumns?: number; // Override automatic column calculation
}

export function ResponsiveFlatGrid<T>({
  data,
  renderItem,
  keyExtractor,
  minItemWidth = 300,
  spacing = 16,
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
  refreshControl,
  onEndReached,
  onEndReachedThreshold,
  ListFooterComponent,
  ListHeaderComponent,
  ListEmptyComponent,
  numColumns,
}: ResponsiveFlatGridProps<T>) {
  const responsiveLayout = useResponsiveLayout();
  const theme = useTheme();

  // Calculate responsive columns based on screen width and breakpoints
  const calculatedColumns = useMemo(() => {
    if (numColumns) return numColumns;

    // Use responsive grid columns from constants as base
    const baseColumns =
      responsiveLayout?.gridColumns ||
      GRID_COLUMNS[responsiveLayout?.screenSize || "mobile"];

    // For products, we want more columns on larger screens
    const productColumns =
      responsiveLayout?.getResponsiveValue({
        mobile: 1,
        tablet: 2,
        desktop: 3,
        large: 4,
      }) || baseColumns;

    // Verify the columns fit with minItemWidth
    const screenWidth = responsiveLayout?.width || 375;
    const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
    const adjustedSpacing = spacing * spacingMultiplier;

    // Calculate how many items can actually fit with better spacing calculation
    const containerPadding = adjustedSpacing; // Container padding
    const availableWidth = screenWidth - containerPadding * 2; // Account for container padding

    // For each column, we need minItemWidth + spacing between items
    const spacingBetweenItems = adjustedSpacing * (productColumns - 1);
    const totalWidthNeeded =
      minItemWidth * productColumns + spacingBetweenItems;

    let finalColumns = productColumns;
    if (totalWidthNeeded > availableWidth && productColumns > 1) {
      // Try with fewer columns
      finalColumns = Math.floor(
        availableWidth / (minItemWidth + adjustedSpacing)
      );
      finalColumns = Math.max(1, finalColumns);
    }

    // Use the smaller of responsive columns or what actually fits
    return Math.max(1, Math.min(productColumns, finalColumns));
  }, [responsiveLayout, minItemWidth, spacing, numColumns]);

  // Calculate item width based on columns
  const itemWidth = useMemo(() => {
    const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
    const adjustedSpacing = spacing * spacingMultiplier;
    const screenWidth = responsiveLayout?.width || 375;

    // More precise width calculation to prevent overflow
    const containerPadding = adjustedSpacing; // Container left/right padding
    const spacingBetweenItems = adjustedSpacing * (calculatedColumns - 1);
    const availableWidth =
      screenWidth - containerPadding * 2 - spacingBetweenItems;
    const calculatedWidth = availableWidth / calculatedColumns;

    // Ensure we don't exceed the calculated width to prevent overflow
    const safeWidth = Math.floor(calculatedWidth);
    return Math.max(minItemWidth, safeWidth);
  }, [
    calculatedColumns,
    responsiveLayout?.width,
    responsiveLayout?.spacingMultiplier,
    spacing,
    minItemWidth,
  ]);

  // Render item with responsive wrapper
  const renderGridItem: ListRenderItem<T> = ({ item, index }) => {
    const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
    const adjustedSpacing = spacing * spacingMultiplier;

    return (
      <View
        style={{
          width: itemWidth,
          marginHorizontal: adjustedSpacing / 2,
          marginBottom: adjustedSpacing,
        }}
      >
        {renderItem({ item, index })}
      </View>
    );
  };

  // Create grid data (flatten for FlatList)
  const gridData = useMemo(() => {
    return data;
  }, [data]);

  const containerStyle = useMemo(() => {
    const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
    const adjustedSpacing = spacing * spacingMultiplier;

    return [
      {
        paddingHorizontal: adjustedSpacing,
        paddingTop: adjustedSpacing / 2,
      },
      contentContainerStyle,
    ];
  }, [responsiveLayout?.spacingMultiplier, spacing, contentContainerStyle]);

  return (
    <FlatList
      data={gridData}
      renderItem={renderGridItem}
      keyExtractor={keyExtractor}
      numColumns={calculatedColumns}
      key={calculatedColumns} // Force re-render when columns change
      style={style}
      contentContainerStyle={containerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      refreshControl={refreshControl}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      ListFooterComponent={ListFooterComponent}
      ListHeaderComponent={ListHeaderComponent}
      ListEmptyComponent={ListEmptyComponent}
      columnWrapperStyle={
        calculatedColumns > 1
          ? {
              justifyContent: "space-between",
              paddingHorizontal: 0, // Remove extra padding to prevent overflow
            }
          : undefined
      }
    />
  );
}

// Export default for easier imports
export default ResponsiveFlatGrid;
