/**
 * Shopify Metafields Routes
 * Handles loyalty points, customer tiers, and discount tracking metafields
 * Follows patterns from store-api.js
 */

const express = require("express");
const shopifyMetafieldsService = require("../services/shopify-metafields-service");
const { authenticateToken } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");
const router = express.Router();

// Setup loyalty metafield definitions
router.post("/setup", authenticateToken, async (req, res) => {
  try {
    console.log("🏗️ Setting up Shopify metafield definitions for loyalty system...");
    
    const result = await shopifyMetafieldsService.setupLoyaltyMetafields();

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          setup: result.results,
          summary: {
            customerFields: result.results.customer.length,
            orderFields: result.results.order.length,
            errors: result.results.errors.length
          }
        },
        result.message
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Metafields setup error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to setup metafield definitions"
    );
  }
});

// Verify metafield setup
router.get("/verify", authenticateToken, async (req, res) => {
  try {
    const result = await shopifyMetafieldsService.verifyMetafieldSetup();

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.verification,
        result.verification.isComplete 
          ? "All metafield definitions are properly configured"
          : "Some metafield definitions are missing"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Metafields verification error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to verify metafield setup"
    );
  }
});

// Get metafield definitions
router.get("/definitions/:ownerType", authenticateToken, async (req, res) => {
  try {
    const { ownerType } = req.params;
    const { namespace } = req.query;

    if (!["customer", "order", "product"].includes(ownerType)) {
      return ResponseFormatter.validationError(res, {
        ownerType: "Must be one of: customer, order, product"
      });
    }

    const result = await shopifyMetafieldsService.getMetafieldDefinitions(
      ownerType,
      namespace
    );

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          ownerType: ownerType,
          namespace: namespace || "all",
          definitions: result.definitions,
          count: result.definitions.length
        },
        `Found ${result.definitions.length} metafield definitions`
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Get metafield definitions error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get metafield definitions"
    );
  }
});

// Set customer loyalty data
router.put("/customer/:customerId/loyalty", authenticateToken, async (req, res) => {
  try {
    const { customerId } = req.params;
    const loyaltyData = req.body;

    // Validate customer ID format
    if (!customerId.startsWith("gid://shopify/Customer/")) {
      return ResponseFormatter.validationError(res, {
        customerId: "Must be a valid Shopify customer ID"
      });
    }

    // Validate loyalty data
    if (loyaltyData.tier && !["bronze", "silver", "gold", "platinum"].includes(loyaltyData.tier)) {
      return ResponseFormatter.validationError(res, {
        tier: "Must be one of: bronze, silver, gold, platinum"
      });
    }

    if (loyaltyData.points !== undefined && (loyaltyData.points < 0 || !Number.isInteger(loyaltyData.points))) {
      return ResponseFormatter.validationError(res, {
        points: "Must be a non-negative integer"
      });
    }

    const result = await shopifyMetafieldsService.setCustomerLoyaltyData(
      customerId,
      loyaltyData
    );

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          customerId: customerId,
          metafieldsUpdated: result.metafieldsUpdated,
          customer: result.customer
        },
        `Updated ${result.metafieldsUpdated} loyalty metafields for customer`
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Set customer loyalty data error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to update customer loyalty data"
    );
  }
});

// Get customer loyalty data
router.get("/customer/:customerId/loyalty", authenticateToken, async (req, res) => {
  try {
    const { customerId } = req.params;

    // Validate customer ID format
    if (!customerId.startsWith("gid://shopify/Customer/")) {
      return ResponseFormatter.validationError(res, {
        customerId: "Must be a valid Shopify customer ID"
      });
    }

    const result = await shopifyMetafieldsService.getCustomerLoyaltyData(customerId);

    if (result.success) {
      return ResponseFormatter.success(
        res,
        result.loyaltyData,
        "Customer loyalty data retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Get customer loyalty data error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get customer loyalty data"
    );
  }
});

// Set order metafields
router.put("/order/:orderId/metadata", authenticateToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const orderData = req.body;

    // Validate order ID format
    if (!orderId.startsWith("gid://shopify/Order/")) {
      return ResponseFormatter.validationError(res, {
        orderId: "Must be a valid Shopify order ID"
      });
    }

    // Validate order data
    if (orderData.pointsEarned !== undefined && (orderData.pointsEarned < 0 || !Number.isInteger(orderData.pointsEarned))) {
      return ResponseFormatter.validationError(res, {
        pointsEarned: "Must be a non-negative integer"
      });
    }

    if (orderData.pointsRedeemed !== undefined && (orderData.pointsRedeemed < 0 || !Number.isInteger(orderData.pointsRedeemed))) {
      return ResponseFormatter.validationError(res, {
        pointsRedeemed: "Must be a non-negative integer"
      });
    }

    const result = await shopifyMetafieldsService.setOrderMetafields(
      orderId,
      orderData
    );

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          orderId: orderId,
          metafieldsUpdated: result.metafieldsUpdated,
          order: result.order
        },
        `Updated ${result.metafieldsUpdated} metafields for order`
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Set order metafields error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to update order metafields"
    );
  }
});

// Bulk update customer loyalty data
router.post("/customers/loyalty/bulk", authenticateToken, async (req, res) => {
  try {
    const { customers } = req.body;

    if (!Array.isArray(customers) || customers.length === 0) {
      return ResponseFormatter.validationError(res, {
        customers: "Must be a non-empty array of customer loyalty data"
      });
    }

    const results = {
      successful: [],
      failed: [],
      total: customers.length
    };

    for (const customerData of customers) {
      if (!customerData.customerId || !customerData.loyaltyData) {
        results.failed.push({
          customerId: customerData.customerId || "unknown",
          error: "Missing customerId or loyaltyData"
        });
        continue;
      }

      const result = await shopifyMetafieldsService.setCustomerLoyaltyData(
        customerData.customerId,
        customerData.loyaltyData
      );

      if (result.success) {
        results.successful.push({
          customerId: customerData.customerId,
          metafieldsUpdated: result.metafieldsUpdated
        });
      } else {
        results.failed.push({
          customerId: customerData.customerId,
          error: result.error
        });
      }
    }

    return ResponseFormatter.success(
      res,
      results,
      `Bulk update completed. ${results.successful.length} successful, ${results.failed.length} failed`
    );
  } catch (error) {
    console.error("Bulk update customer loyalty data error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to bulk update customer loyalty data"
    );
  }
});

// Health check for metafields service
router.get("/health", async (req, res) => {
  try {
    // Test basic connectivity by getting a simple metafield definition query
    const result = await shopifyMetafieldsService.getMetafieldDefinitions("customer", "dukalink_loyalty");
    
    return ResponseFormatter.success(
      res,
      {
        status: "healthy",
        metafieldsService: result.success ? "connected" : "error",
        timestamp: new Date().toISOString()
      },
      "Metafields service health check completed"
    );
  } catch (error) {
    console.error("Metafields health check error:", error);
    return ResponseFormatter.error(
      res,
      "Metafields service health check failed",
      503
    );
  }
});

module.exports = router;
