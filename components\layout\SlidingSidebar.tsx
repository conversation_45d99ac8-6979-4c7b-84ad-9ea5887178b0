import { SafeAreaWrapper } from "@/components/layout/SafeAreaWrapper";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useAppSelector } from "@/src/store";
import { selectCartItemCount } from "@/src/store/slices/cartSlice";

import { useRouter } from "expo-router";
import React, { useEffect, useRef } from "react";
import {
  Animated,
  Dimensions,
  Modal,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
} from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

interface SidebarItem {
  title: string;
  icon: string;
  route: string;
  badge?: string | number;
}

interface SlidingSidebarProps {
  visible: boolean;
  onClose: () => void;
}

// Get screen dimensions with fallback
const getScreenDimensions = () => {
  try {
    return Dimensions.get("window");
  } catch (error) {
    console.warn("Failed to get screen dimensions, using fallback", error);
    return { width: 375, height: 667 }; // iPhone SE fallback
  }
};

const { width: SCREEN_WIDTH } = getScreenDimensions();

export function SlidingSidebar({ visible, onClose }: SlidingSidebarProps) {
  const router = useRouter();
  const { currentTitle } = useNavigation();
  const { user, signOut } = useSession();
  const { canManageStaff, canManageSystem, hasPermission } = useRBAC();
  const responsiveLayout = useResponsiveLayout();
  const spacingMultiplier = responsiveLayout?.spacingMultiplier || 1;
  const isDesktop = responsiveLayout?.isDesktop || false;

  // Cart item count for badge
  const cartItemCount = useAppSelector(selectCartItemCount);

  // Responsive sidebar width
  const SIDEBAR_WIDTH = isDesktop
    ? Math.min(350, SCREEN_WIDTH * 0.3) // Wider on desktop, max 30% of screen
    : Math.min(300, SCREEN_WIDTH * 0.85); // Mobile sizing

  // Animation values
  const slideAnim = useRef(new Animated.Value(-SIDEBAR_WIDTH)).current;
  const backdropAnim = useRef(new Animated.Value(0)).current;

  // Theme colors
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");

  // Check if we're on the dashboard based on the current title
  const isDashboard =
    currentTitle === "Dashboard" || currentTitle === "Dukalink POS Dashboard";

  // Base sidebar items (always visible)
  const baseSidebarItems: SidebarItem[] = [
    {
      title: "Dashboard",
      icon: "house.fill",
      route: "/(tabs)",
    },
    {
      title: "Products",
      icon: "cube.fill",
      route: "/(tabs)/products",
    },
    {
      title: "Cart",
      icon: "cart.fill",
      route: "/(tabs)/cart",
      badge: cartItemCount > 0 ? cartItemCount : undefined,
    },
    {
      title: "Orders",
      icon: "doc.text.fill",
      route: "/(tabs)/orders",
    },
  ];

  // Management items (conditional based on permissions)
  const managementItems: SidebarItem[] = [];

  if (canManageStaff) {
    managementItems.push({
      title: "Sales Agents",
      icon: "person.2.fill",
      route: "/sales-agent-list",
    });
  }

  // Add loyalty management for users with loyalty permissions
  if (hasPermission("view_loyalty") || hasPermission("manage_loyalty")) {
    managementItems.push({
      title: "Loyalty Management",
      icon: "star.fill",
      route: "/loyalty-management",
    });
  }

  // Add discount management for users with discount permissions
  if (hasPermission("apply_discounts") || hasPermission("manage_discounts")) {
    managementItems.push({
      title: "Discount Management",
      icon: "tag.fill",
      route: "/discount-management",
    });
  }

  if (canManageSystem) {
    managementItems.push({
      title: "Settings",
      icon: "gear.fill",
      route: "/settings",
    });
  }

  // Dashboard-specific sidebar items (minimal)
  const dashboardSidebarItems: SidebarItem[] = [
    {
      title: "Settings",
      icon: "gear.fill",
      route: "/settings",
    },
  ];

  // Combine sidebar items based on current page
  const sidebarItems = isDashboard
    ? dashboardSidebarItems
    : [...baseSidebarItems, ...managementItems];

  // Animation effects
  useEffect(() => {
    if (visible) {
      // Slide in from left
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Slide out to left
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -SIDEBAR_WIDTH,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, slideAnim, backdropAnim]);

  const handleNavigation = (route: string) => {
    // Animate out first, then navigate
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -SIDEBAR_WIDTH,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(backdropAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
      router.push(route as any);
    });
  };

  const handleLogout = async () => {
    // Animate out first, then logout
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -SIDEBAR_WIDTH,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(backdropAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(async () => {
      onClose();
      await signOut();
      router.replace("/pos-login");
    });
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.overlay}>
        {/* Animated Backdrop */}
        <Animated.View
          style={[
            styles.backdrop,
            {
              opacity: backdropAnim,
            },
          ]}
        >
          <Pressable style={StyleSheet.absoluteFill} onPress={onClose} />
        </Animated.View>

        {/* Animated Sidebar */}
        <Animated.View
          style={[
            styles.sidebar,
            {
              backgroundColor: surfaceColor,
              transform: [{ translateX: slideAnim }],
              width: SIDEBAR_WIDTH,
              maxWidth: SCREEN_WIDTH - 50,
            },
          ]}
        >
          <SafeAreaWrapper style={styles.sidebarContent}>
            {/* Green Header Section with User Info */}
            <View
              style={[styles.headerSection, { backgroundColor: primaryColor }]}
            >
              <View style={styles.headerContent}>
                <View style={styles.avatarContainer}>
                  <Text style={styles.avatarText}>
                    {user?.name
                      ?.split(" ")
                      .map((n) => n.charAt(0))
                      .join("")
                      .toUpperCase() || "MM"}
                  </Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userTitle} numberOfLines={1}>
                    {user?.role === "super_admin"
                      ? "Main Store Manager"
                      : user?.role === "manager"
                      ? "Store Manager"
                      : user?.role === "staff"
                      ? "Store Staff"
                      : "Staff Member"}
                  </Text>
                  <Text style={styles.userSubtitle} numberOfLines={1}>
                    {user?.username || "branch_manager"}
                  </Text>
                </View>
              </View>
            </View>

            {/* Store Name Section */}
            <View
              style={[styles.storeSection, { backgroundColor: primaryColor }]}
            >
              <View style={styles.storeContainer}>
                <IconSymbol name="house.fill" size={16} color="white" />
                <Text style={styles.storeName}>Treasured Scents</Text>
              </View>
            </View>

            {/* Main Navigation */}
            <View style={styles.mainNavigation}>
              {sidebarItems.map((item, index) => {
                const isActive =
                  currentTitle === item.title ||
                  (item.title === "Dashboard" &&
                    (currentTitle === "Dashboard" ||
                      currentTitle === "Dukalink POS Dashboard"));

                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.navItem,
                      isActive && [
                        styles.activeNavItem,
                        { backgroundColor: surfaceColor },
                      ],
                    ]}
                    onPress={() => handleNavigation(item.route)}
                    activeOpacity={0.7}
                  >
                    <View style={styles.navButton}>
                      <IconSymbol
                        name={item.icon as any}
                        size={20}
                        color={isActive ? primaryColor : textSecondary}
                      />
                      <Text
                        style={[
                          styles.navText,
                          { color: isActive ? primaryColor : textSecondary },
                        ]}
                      >
                        {item.title}
                      </Text>
                      {item.badge && (
                        <View
                          style={[
                            styles.badge,
                            { backgroundColor: primaryColor },
                          ]}
                        >
                          <Text style={styles.badgeText}>
                            {item.badge.toString()}
                          </Text>
                        </View>
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>

            {/* Bottom Section */}
            <View style={styles.bottomSection}>
              {/* Profile */}
              <TouchableOpacity
                style={styles.bottomNavItem}
                onPress={() => handleNavigation("/profile")}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name="person.fill"
                  size={20}
                  color={textSecondary}
                />
                <Text style={[styles.bottomNavText, { color: textSecondary }]}>
                  Profile
                </Text>
              </TouchableOpacity>

              {/* Support */}
              <TouchableOpacity
                style={styles.bottomNavItem}
                onPress={() => handleNavigation("/support")}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name="questionmark.circle"
                  size={20}
                  color={textSecondary}
                />
                <Text style={[styles.bottomNavText, { color: textSecondary }]}>
                  Support
                </Text>
              </TouchableOpacity>

              {/* Sign Out */}
              <TouchableOpacity
                style={styles.bottomNavItem}
                onPress={handleLogout}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name="arrow.right.square"
                  size={20}
                  color={textSecondary}
                />
                <Text style={[styles.bottomNavText, { color: textSecondary }]}>
                  Sign Out
                </Text>
              </TouchableOpacity>
            </View>
          </SafeAreaWrapper>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "flex-start", // Changed from flex-end to flex-start
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  sidebar: {
    height: "100%",
    elevation: 25,
    shadowColor: "#000",
    shadowOffset: {
      width: 2, // Changed from -2 to 2 for left-side shadow
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 5,
  },
  sidebarContent: {
    flex: 1,
  },
  // Green header section
  headerSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    paddingTop: Spacing.xl,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: Spacing.md,
  },
  avatarText: {
    ...Typography.body,
    fontWeight: "700",
    color: "#FFFFFF",
    fontSize: 18,
  },
  userInfo: {
    flex: 1,
  },
  userTitle: {
    ...Typography.body,
    fontWeight: "600",
    color: "#FFFFFF",
    fontSize: 16,
    marginBottom: 2,
  },
  userSubtitle: {
    ...Typography.caption,
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 14,
  },
  // Store section
  storeSection: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
  storeContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: 8,
  },
  storeName: {
    ...Typography.body,
    color: "#FFFFFF",
    fontWeight: "500",
    marginLeft: Spacing.sm,
  },
  // Main navigation section
  mainNavigation: {
    flex: 1,
    paddingHorizontal: Spacing.md,
    paddingTop: Spacing.md,
  },
  navItem: {
    marginBottom: Spacing.xs,
  },
  activeNavItem: {
    borderRadius: 8,
    marginHorizontal: Spacing.xs,
  },
  navButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderRadius: 8,
    minHeight: 48,
  },
  navText: {
    ...Typography.body,
    marginLeft: Spacing.md,
    flex: 1,
    fontWeight: "500",
  },
  badge: {
    paddingHorizontal: Spacing.xs,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: "center",
  },
  badgeText: {
    ...Typography.caption,
    color: "#FFFFFF",
    fontWeight: "600",
  },
  // Bottom section
  bottomSection: {
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
    marginTop: Spacing.md,
  },
  bottomNavItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderRadius: 8,
    marginBottom: Spacing.xs,
  },
  bottomNavText: {
    ...Typography.body,
    marginLeft: Spacing.md,
    fontWeight: "500",
  },
  userSwitchingContainer: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.sm,
  },
  userSwitchingButton: {
    width: "100%",
    justifyContent: "flex-start",
  },
});
