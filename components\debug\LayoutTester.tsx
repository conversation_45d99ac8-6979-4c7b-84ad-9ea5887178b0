import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAppStateLayout, useDynamicDimensions } from '@/src/hooks/useAppStateLayout';

/**
 * Layout Tester Component
 * Use this component to test safe area and layout behavior
 * Especially useful for testing app switching scenarios
 */
export const LayoutTester: React.FC = () => {
  const insets = useSafeAreaInsets();
  const { dimensions, safeAreaInsets, appState, isActive } = useAppStateLayout();
  const dynamicDimensions = useDynamicDimensions();

  const [testResults, setTestResults] = React.useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const runLayoutTest = () => {
    addTestResult('Layout test started');
    addTestResult(`Screen: ${dimensions.width}x${dimensions.height}`);
    addTestResult(`Safe Area: T:${insets.top} B:${insets.bottom} L:${insets.left} R:${insets.right}`);
    addTestResult(`App State: ${appState} (Active: ${isActive})`);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      {/* Status Bar Test Area */}
      <View style={[styles.statusBarTest, { paddingTop: insets.top }]}>
        <Text style={styles.testText}>Status Bar Safe Area (Top: {insets.top}px)</Text>
      </View>

      {/* Main Content */}
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Layout Information</Text>
          <Text style={styles.infoText}>Screen Dimensions: {dimensions.width} x {dimensions.height}</Text>
          <Text style={styles.infoText}>Orientation: {dimensions.isLandscape ? 'Landscape' : 'Portrait'}</Text>
          <Text style={styles.infoText}>App State: {appState}</Text>
          <Text style={styles.infoText}>Is Active: {isActive ? 'Yes' : 'No'}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Safe Area Insets</Text>
          <Text style={styles.infoText}>Top: {insets.top}px</Text>
          <Text style={styles.infoText}>Bottom: {insets.bottom}px</Text>
          <Text style={styles.infoText}>Left: {insets.left}px</Text>
          <Text style={styles.infoText}>Right: {insets.right}px</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dynamic Dimensions</Text>
          <Text style={styles.infoText}>Is Tablet: {dynamicDimensions.isTablet ? 'Yes' : 'No'}</Text>
          <Text style={styles.infoText}>Is Small Screen: {dynamicDimensions.isSmallScreen ? 'Yes' : 'No'}</Text>
          <Text style={styles.infoText}>Is Large Screen: {dynamicDimensions.isLargeScreen ? 'Yes' : 'No'}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Testing Instructions</Text>
          <Text style={styles.instructionText}>
            1. Tap "Run Layout Test" to capture current layout info{'\n'}
            2. Switch to another app (Settings, Messages, etc.){'\n'}
            3. Return to this app{'\n'}
            4. Tap "Run Layout Test" again{'\n'}
            5. Compare the results - they should be identical{'\n'}
            6. Check that this text is not hidden behind status bar or navigation bar
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.testButton} onPress={runLayoutTest}>
            <Text style={styles.buttonText}>Run Layout Test</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.clearButton} onPress={clearResults}>
            <Text style={styles.buttonText}>Clear Results</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Results</Text>
          {testResults.length === 0 ? (
            <Text style={styles.infoText}>No test results yet. Tap "Run Layout Test" to start.</Text>
          ) : (
            testResults.map((result, index) => (
              <Text key={index} style={styles.resultText}>{result}</Text>
            ))
          )}
        </View>
      </ScrollView>

      {/* Navigation Bar Test Area */}
      <View style={[styles.navigationBarTest, { paddingBottom: insets.bottom }]}>
        <Text style={styles.testText}>Navigation Bar Safe Area (Bottom: {insets.bottom}px)</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusBarTest: {
    backgroundColor: '#ff6b6b',
    paddingVertical: 8,
    alignItems: 'center',
  },
  navigationBarTest: {
    backgroundColor: '#4ecdc4',
    paddingVertical: 8,
    alignItems: 'center',
  },
  testText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#666',
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  testButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButton: {
    flex: 1,
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  resultText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
    color: '#333',
    backgroundColor: '#f8f8f8',
    padding: 4,
    borderRadius: 4,
  },
});
