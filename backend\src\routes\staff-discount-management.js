/**
 * Staff Discount Management API Routes
 * Handles discount rule creation, permission management, and usage tracking
 * Follows patterns from staff-management.js
 */

const express = require("express");
const router = express.Router();
const enhancedDiscountService = require("../services/enhanced-discount-service");
const { authenticateToken, requirePermission } = require("../middleware/auth");
const ResponseFormatter = require("../utils/response-formatter");

// Get all discount rules with staff permissions
router.get("/rules", authenticateToken, async (req, res) => {
  try {
    const { includeInactive = false, staffId = null } = req.query;
    const effectiveStaffId = staffId || req.user.id;

    // Check if user can view other staff's permissions
    if (
      staffId &&
      staffId !== req.user.id &&
      !req.user.permissions.includes("manage_discounts")
    ) {
      return ResponseFormatter.error(
        res,
        "Permission denied: Cannot view other staff member's discount permissions",
        403
      );
    }

    const result = await enhancedDiscountService.getCustomDiscountRules(
      effectiveStaffId,
      null, // customerId
      0 // orderTotal
    );

    if (result.success) {
      let discountRules = result.discounts;

      // Filter out inactive rules unless specifically requested
      if (!includeInactive) {
        discountRules = discountRules.filter((rule) => rule.isActive !== false);
      }

      return ResponseFormatter.success(
        res,
        {
          discountRules: discountRules,
          staffId: effectiveStaffId,
          totalRules: discountRules.length,
        },
        "Discount rules retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Get discount rules error:", error);
    return ResponseFormatter.serverError(res, "Failed to get discount rules");
  }
});

// Create new discount rule (requires manage_discounts permission)
router.post(
  "/rules",
  authenticateToken,
  requirePermission("manage_discounts"),
  async (req, res) => {
    try {
      const ruleData = req.body;

      // Validate required fields
      if (!ruleData.name || !ruleData.discountType || !ruleData.discountValue) {
        return ResponseFormatter.validationError(res, {
          name: !ruleData.name ? "Discount rule name is required" : null,
          discountType: !ruleData.discountType
            ? "Discount type is required"
            : null,
          discountValue: !ruleData.discountValue
            ? "Discount value is required"
            : null,
        });
      }

      // Validate discount type
      const validTypes = [
        "percentage",
        "fixed_amount",
        "bogo",
        "loyalty_points",
      ];
      if (!validTypes.includes(ruleData.discountType)) {
        return ResponseFormatter.validationError(res, {
          discountType: `Invalid discount type. Must be one of: ${validTypes.join(
            ", "
          )}`,
        });
      }

      // Validate discount value
      const discountValue = parseFloat(ruleData.discountValue);
      if (isNaN(discountValue) || discountValue <= 0) {
        return ResponseFormatter.validationError(res, {
          discountValue: "Discount value must be a positive number",
        });
      }

      // Validate percentage discount
      if (ruleData.discountType === "percentage" && discountValue > 100) {
        return ResponseFormatter.validationError(res, {
          discountValue: "Percentage discount cannot exceed 100%",
        });
      }

      // Validate customer eligibility
      if (
        ruleData.customerEligibility &&
        !["all", "loyalty_tier", "specific_customers"].includes(
          ruleData.customerEligibility
        )
      ) {
        return ResponseFormatter.validationError(res, {
          customerEligibility:
            "Invalid customer eligibility. Must be: all, loyalty_tier, or specific_customers",
        });
      }

      // Validate loyalty tier if required
      if (
        ruleData.customerEligibility === "loyalty_tier" &&
        !ruleData.loyaltyTierRequired
      ) {
        return ResponseFormatter.validationError(res, {
          loyaltyTierRequired:
            "Loyalty tier is required when customer eligibility is set to loyalty_tier",
        });
      }

      if (
        ruleData.loyaltyTierRequired &&
        !["bronze", "silver", "gold", "platinum"].includes(
          ruleData.loyaltyTierRequired
        )
      ) {
        return ResponseFormatter.validationError(res, {
          loyaltyTierRequired:
            "Invalid loyalty tier. Must be: bronze, silver, gold, or platinum",
        });
      }

      // Validate date range
      if (ruleData.validFrom && ruleData.validTo) {
        const fromDate = new Date(ruleData.validFrom);
        const toDate = new Date(ruleData.validTo);

        if (fromDate >= toDate) {
          return ResponseFormatter.validationError(res, {
            validTo: "Valid to date must be after valid from date",
          });
        }
      }

      const result = await enhancedDiscountService.createDiscountRule(
        ruleData,
        req.user.id
      );

      if (result.success) {
        return ResponseFormatter.created(
          res,
          {
            ruleId: result.ruleId,
            message: result.message,
          },
          "Discount rule created successfully"
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Create discount rule error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to create discount rule"
      );
    }
  }
);

// Grant staff permission for discount rule
router.post(
  "/rules/:ruleId/permissions",
  authenticateToken,
  requirePermission("manage_discounts"),
  async (req, res) => {
    try {
      const { ruleId } = req.params;
      const {
        staffId,
        permissionType,
        maxDiscountPercentage,
        maxDiscountAmount,
      } = req.body;

      // Validate required fields
      if (!staffId || !permissionType) {
        return ResponseFormatter.validationError(res, {
          staffId: !staffId ? "Staff ID is required" : null,
          permissionType: !permissionType
            ? "Permission type is required"
            : null,
        });
      }

      // Validate permission type
      const validPermissions = ["apply", "modify", "view"];
      if (!validPermissions.includes(permissionType)) {
        return ResponseFormatter.validationError(res, {
          permissionType: `Invalid permission type. Must be one of: ${validPermissions.join(
            ", "
          )}`,
        });
      }

      // Validate limits if provided
      if (maxDiscountPercentage !== undefined) {
        const percentage = parseFloat(maxDiscountPercentage);
        if (isNaN(percentage) || percentage < 0 || percentage > 100) {
          return ResponseFormatter.validationError(res, {
            maxDiscountPercentage:
              "Maximum discount percentage must be between 0 and 100",
          });
        }
      }

      if (maxDiscountAmount !== undefined) {
        const amount = parseFloat(maxDiscountAmount);
        if (isNaN(amount) || amount < 0) {
          return ResponseFormatter.validationError(res, {
            maxDiscountAmount:
              "Maximum discount amount must be a positive number",
          });
        }
      }

      const limits = {};
      if (maxDiscountPercentage !== undefined)
        limits.maxDiscountPercentage = parseFloat(maxDiscountPercentage);
      if (maxDiscountAmount !== undefined)
        limits.maxDiscountAmount = parseFloat(maxDiscountAmount);

      const result = await enhancedDiscountService.grantStaffPermission(
        staffId,
        ruleId,
        permissionType,
        req.user.id, // grantedBy
        limits
      );

      if (result.success) {
        return ResponseFormatter.success(
          res,
          {
            staffId: staffId,
            ruleId: ruleId,
            permissionType: permissionType,
            limits: limits,
          },
          result.message
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Grant staff permission error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to grant staff permission"
      );
    }
  }
);

// Get applicable discounts for a cart
router.post("/applicable", authenticateToken, async (req, res) => {
  try {
    const { cartData, customerId, salesAgentId } = req.body;

    // Validate required fields
    if (!cartData || !cartData.subtotal) {
      return ResponseFormatter.validationError(res, {
        cartData: "Cart data with subtotal is required",
      });
    }

    const subtotal = parseFloat(cartData.subtotal);
    if (isNaN(subtotal) || subtotal <= 0) {
      return ResponseFormatter.validationError(res, {
        subtotal: "Cart subtotal must be a positive number",
      });
    }

    const result = await enhancedDiscountService.getApplicableDiscounts(
      cartData,
      req.user.id, // staffId
      customerId,
      salesAgentId
    );

    if (result.success) {
      return ResponseFormatter.success(
        res,
        {
          discounts: result.discounts,
          totalPossibleSavings: result.totalPossibleSavings,
          cartSubtotal: subtotal,
        },
        "Applicable discounts retrieved successfully"
      );
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Get applicable discounts error:", error);
    return ResponseFormatter.serverError(
      res,
      "Failed to get applicable discounts"
    );
  }
});

// Apply discount to cart
router.post("/apply", authenticateToken, async (req, res) => {
  try {
    const { discountData, cartData, customerId, salesAgentId } = req.body;

    // Validate required fields
    if (!discountData || !cartData) {
      return ResponseFormatter.validationError(res, {
        discountData: !discountData ? "Discount data is required" : null,
        cartData: !cartData ? "Cart data is required" : null,
      });
    }

    if (!discountData.discountId || !discountData.discountType) {
      return ResponseFormatter.validationError(res, {
        discountId: !discountData.discountId ? "Discount ID is required" : null,
        discountType: !discountData.discountType
          ? "Discount type is required"
          : null,
      });
    }

    const result = await enhancedDiscountService.applyDiscount(
      discountData,
      cartData,
      req.user.id, // staffId
      customerId,
      salesAgentId
    );

    if (result.success) {
      return ResponseFormatter.success(res, result.discount, result.message);
    } else {
      return ResponseFormatter.error(res, result.error, 400);
    }
  } catch (error) {
    console.error("Apply discount error:", error);
    return ResponseFormatter.serverError(res, "Failed to apply discount");
  }
});

// Get discount analytics (requires permission)
router.get(
  "/analytics",
  authenticateToken,
  requirePermission("view_analytics"),
  async (req, res) => {
    try {
      const { startDate, endDate, staffId } = req.query;

      // Check if user can view other staff's analytics
      if (
        staffId &&
        staffId !== req.user.id &&
        !req.user.permissions.includes("view_all_analytics")
      ) {
        return ResponseFormatter.error(
          res,
          "Permission denied: Cannot view other staff member's discount analytics",
          403
        );
      }

      const dateRange = {};
      if (startDate) dateRange.startDate = startDate;
      if (endDate) dateRange.endDate = endDate;

      const result = await enhancedDiscountService.getDiscountAnalytics(
        dateRange,
        staffId
      );

      if (result.success) {
        return ResponseFormatter.success(
          res,
          result.analytics,
          "Discount analytics retrieved successfully"
        );
      } else {
        return ResponseFormatter.error(res, result.error, 400);
      }
    } catch (error) {
      console.error("Get discount analytics error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to get discount analytics"
      );
    }
  }
);

// Get staff discount permissions
router.get(
  "/staff/:staffId/permissions",
  authenticateToken,
  async (req, res) => {
    try {
      const { staffId } = req.params;

      // Check if user can view other staff's permissions
      if (
        staffId !== req.user.id &&
        !req.user.permissions.includes("manage_discounts")
      ) {
        return ResponseFormatter.error(
          res,
          "Permission denied: Cannot view other staff member's discount permissions",
          403
        );
      }

      // Query staff discount permissions from database
      const [rows] = await enhancedDiscountService.pool.execute(
        `SELECT
        sdp.id,
        sdp.permission_type,
        sdp.max_discount_percentage,
        sdp.max_discount_amount,
        sdp.created_at,
        dr.id as rule_id,
        dr.name as rule_name,
        dr.description as rule_description,
        dr.discount_type,
        dr.discount_value,
        dr.is_active,
        ps.name as granted_by_name
       FROM staff_discount_permissions sdp
       INNER JOIN discount_rules dr ON sdp.discount_rule_id = dr.id
       LEFT JOIN pos_staff ps ON sdp.granted_by = ps.id
       WHERE sdp.staff_id = ?
       ORDER BY sdp.created_at DESC`,
        [staffId]
      );

      const permissions = rows.map((row) => ({
        id: row.id,
        permissionType: row.permission_type,
        maxDiscountPercentage: row.max_discount_percentage,
        maxDiscountAmount: row.max_discount_amount
          ? parseFloat(row.max_discount_amount)
          : null,
        createdAt: row.created_at,
        rule: {
          id: row.rule_id,
          name: row.rule_name,
          description: row.rule_description,
          discountType: row.discount_type,
          discountValue: parseFloat(row.discount_value),
          isActive: row.is_active,
        },
        grantedByName: row.granted_by_name,
      }));

      return ResponseFormatter.success(
        res,
        {
          staffId: staffId,
          permissions: permissions,
          totalPermissions: permissions.length,
        },
        "Staff discount permissions retrieved successfully"
      );
    } catch (error) {
      console.error("Get staff permissions error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to get staff discount permissions"
      );
    }
  }
);

// Update discount rule (requires manage_discounts permission)
router.put(
  "/rules/:ruleId",
  authenticateToken,
  requirePermission("manage_discounts"),
  async (req, res) => {
    try {
      const { ruleId } = req.params;
      const updateData = req.body;

      // Validate that at least one field is being updated
      const allowedFields = [
        "name",
        "description",
        "discountValue",
        "minPurchaseAmount",
        "maxDiscountAmount",
        "customerEligibility",
        "loyaltyTierRequired",
        "maxUsesPerCustomer",
        "maxUsesTotal",
        "validFrom",
        "validTo",
        "isActive",
      ];

      const fieldsToUpdate = Object.keys(updateData).filter((field) =>
        allowedFields.includes(field)
      );

      if (fieldsToUpdate.length === 0) {
        return ResponseFormatter.validationError(res, {
          updateData: `No valid fields to update. Allowed fields: ${allowedFields.join(
            ", "
          )}`,
        });
      }

      // Build dynamic update query
      const setClause = fieldsToUpdate
        .map((field) => {
          const dbField = field.replace(/([A-Z])/g, "_$1").toLowerCase();
          return `${dbField} = ?`;
        })
        .join(", ");

      const values = fieldsToUpdate.map((field) => updateData[field]);
      values.push(ruleId);

      const updateQuery = `
      UPDATE discount_rules
      SET ${setClause}, updated_at = NOW()
      WHERE id = ?
    `;

      const [result] = await enhancedDiscountService.pool.execute(
        updateQuery,
        values
      );

      if (result.affectedRows === 0) {
        return ResponseFormatter.error(res, "Discount rule not found", 404);
      }

      // Get updated rule
      const [updatedRows] = await enhancedDiscountService.pool.execute(
        `SELECT * FROM discount_rules WHERE id = ?`,
        [ruleId]
      );

      return ResponseFormatter.success(
        res,
        {
          ruleId: ruleId,
          updatedFields: fieldsToUpdate,
          rule: updatedRows[0],
        },
        "Discount rule updated successfully"
      );
    } catch (error) {
      console.error("Update discount rule error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to update discount rule"
      );
    }
  }
);

// Delete discount rule (requires manage_discounts permission)
router.delete(
  "/rules/:ruleId",
  authenticateToken,
  requirePermission("manage_discounts"),
  async (req, res) => {
    try {
      const { ruleId } = req.params;
      const { forceDelete = false } = req.query;

      // Check if rule has been used
      const [usageRows] = await enhancedDiscountService.pool.execute(
        `SELECT COUNT(*) as usage_count FROM discount_usage_log WHERE discount_rule_id = ?`,
        [ruleId]
      );

      const hasBeenUsed = usageRows[0].usage_count > 0;

      if (hasBeenUsed && !forceDelete) {
        return ResponseFormatter.error(
          res,
          "Cannot delete discount rule that has been used. Use forceDelete=true to override.",
          400
        );
      }

      // Begin transaction
      await enhancedDiscountService.pool.beginTransaction();

      try {
        // Delete staff permissions first
        await enhancedDiscountService.pool.execute(
          `DELETE FROM staff_discount_permissions WHERE discount_rule_id = ?`,
          [ruleId]
        );

        // Delete usage logs if force delete
        if (forceDelete) {
          await enhancedDiscountService.pool.execute(
            `DELETE FROM discount_usage_log WHERE discount_rule_id = ?`,
            [ruleId]
          );
        }

        // Delete the rule
        const [result] = await enhancedDiscountService.pool.execute(
          `DELETE FROM discount_rules WHERE id = ?`,
          [ruleId]
        );

        if (result.affectedRows === 0) {
          await enhancedDiscountService.pool.rollback();
          return ResponseFormatter.error(res, "Discount rule not found", 404);
        }

        await enhancedDiscountService.pool.commit();

        return ResponseFormatter.success(
          res,
          {
            ruleId: ruleId,
            forceDelete: forceDelete,
            hadUsageHistory: hasBeenUsed,
          },
          "Discount rule deleted successfully"
        );
      } catch (error) {
        await enhancedDiscountService.pool.rollback();
        throw error;
      }
    } catch (error) {
      console.error("Delete discount rule error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to delete discount rule"
      );
    }
  }
);

// Revoke staff permission for discount rule
router.delete(
  "/rules/:ruleId/permissions/:staffId",
  authenticateToken,
  requirePermission("manage_discounts"),
  async (req, res) => {
    try {
      const { ruleId, staffId } = req.params;

      const [result] = await enhancedDiscountService.pool.execute(
        `DELETE FROM staff_discount_permissions
       WHERE discount_rule_id = ? AND staff_id = ?`,
        [ruleId, staffId]
      );

      if (result.affectedRows === 0) {
        return ResponseFormatter.error(res, "Staff permission not found", 404);
      }

      return ResponseFormatter.success(
        res,
        {
          ruleId: ruleId,
          staffId: staffId,
        },
        "Staff permission revoked successfully"
      );
    } catch (error) {
      console.error("Revoke staff permission error:", error);
      return ResponseFormatter.serverError(
        res,
        "Failed to revoke staff permission"
      );
    }
  }
);

// Health check for discount management service
router.get("/health", async (req, res) => {
  try {
    // Test database connectivity
    const [rows] = await enhancedDiscountService.pool.execute(
      "SELECT 1 as test"
    );

    return ResponseFormatter.success(
      res,
      {
        status: "healthy",
        database: "connected",
        discountService: "operational",
        timestamp: new Date().toISOString(),
      },
      "Discount management service health check completed"
    );
  } catch (error) {
    console.error("Discount management health check error:", error);
    return ResponseFormatter.error(
      res,
      "Discount management service health check failed",
      503
    );
  }
});

module.exports = router;
