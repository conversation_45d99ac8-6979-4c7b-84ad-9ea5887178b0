/**
 * Ticket Design System
 *
 * Comprehensive design system specifically for ticket/cart management components
 * Ensures consistency across all ticket-related UI elements
 */

import { Theme } from "@/src/contexts/ThemeContext";
import { ViewStyle, TextStyle } from "react-native";

// =====================================================
// TICKET COMPONENT VARIANTS
// =====================================================

export type TicketVariant =
  | "default"
  | "active"
  | "paused"
  | "completed"
  | "cancelled"
  | "expired"
  | "archived"
  | "error";
export type TicketSize = "compact" | "default" | "expanded";
export type TicketPriority = "low" | "normal" | "high" | "urgent";

// =====================================================
// TICKET COLORS & STATES
// =====================================================

export const getTicketColors = (theme: Theme, variant: TicketVariant) => {
  const colors = {
    default: {
      background: theme.colors.card,
      border: theme.colors.cardBorder,
      text: theme.colors.text,
      accent: theme.colors.textSecondary,
    },
    active: {
      background: theme.colors.primary + "10",
      border: theme.colors.primary,
      text: theme.colors.text,
      accent: theme.colors.primary,
    },
    paused: {
      background: theme.colors.warning + "10",
      border: theme.colors.warning,
      text: theme.colors.text,
      accent: theme.colors.warning,
    },
    completed: {
      background: theme.colors.success + "10",
      border: theme.colors.success,
      text: theme.colors.textSecondary,
      accent: theme.colors.success,
    },
    error: {
      background: theme.colors.error + "10",
      border: theme.colors.error,
      text: theme.colors.text,
      accent: theme.colors.error,
    },
  };

  return colors[variant];
};

// =====================================================
// TICKET CARD STYLES
// =====================================================

export const getTicketCardStyle = (
  theme: Theme,
  variant: TicketVariant = "default",
  size: TicketSize = "default",
  isSelected: boolean = false
): ViewStyle => {
  const colors = getTicketColors(theme, variant);

  const sizeStyles = {
    compact: {
      padding: theme.spacing.md,
      minHeight: 80,
    },
    default: {
      padding: theme.spacing.lg,
      minHeight: 100,
    },
    expanded: {
      padding: theme.spacing.xl,
      minHeight: 120,
    },
  };

  return {
    backgroundColor: colors.background,
    borderRadius: theme.borderRadius.lg,
    borderWidth: isSelected ? 2 : 1,
    borderColor: isSelected ? colors.accent : colors.border,
    marginBottom: theme.spacing.md,
    ...sizeStyles[size],
    ...theme.shadows.sm,
  };
};

// =====================================================
// TICKET TEXT STYLES
// =====================================================

export const getTicketTextStyles = (
  theme: Theme,
  variant: TicketVariant = "default"
) => {
  const colors = getTicketColors(theme, variant);

  return {
    title: {
      ...theme.typography.h3,
      color: colors.text,
      marginBottom: theme.spacing.xs,
    } as TextStyle,

    subtitle: {
      ...theme.typography.caption,
      color: colors.accent,
      marginBottom: theme.spacing.sm,
    } as TextStyle,

    total: {
      ...theme.typography.h2,
      color: colors.accent,
      fontWeight: "600",
    } as TextStyle,

    itemCount: {
      ...theme.typography.small,
      color: colors.text,
      opacity: 0.7,
    } as TextStyle,

    status: {
      ...theme.typography.overline,
      color: colors.accent,
      fontWeight: "600",
    } as TextStyle,

    timestamp: {
      ...theme.typography.small,
      color: theme.colors.textSecondary,
    } as TextStyle,
  };
};

// =====================================================
// TICKET BUTTON STYLES
// =====================================================

export const getTicketButtonStyle = (
  theme: Theme,
  variant: "primary" | "secondary" | "ghost" = "primary",
  size: "small" | "medium" | "large" = "medium"
): ViewStyle => {
  const sizeStyles = {
    small: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.sm,
    },
    medium: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
    },
    large: {
      paddingHorizontal: theme.spacing.xl,
      paddingVertical: theme.spacing.lg,
      borderRadius: theme.borderRadius.lg,
    },
  };

  const variantStyles = {
    primary: {
      backgroundColor: theme.colors.primary,
      borderWidth: 0,
    },
    secondary: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    ghost: {
      backgroundColor: "transparent",
      borderWidth: 0,
    },
  };

  return {
    alignItems: "center",
    justifyContent: "center",
    ...sizeStyles[size],
    ...variantStyles[variant],
    ...theme.shadows.xs,
  };
};

// =====================================================
// TICKET MODAL STYLES
// =====================================================

export const getTicketModalStyles = (theme: Theme) => ({
  overlay: {
    flex: 1,
    backgroundColor: theme.colors.overlay,
    justifyContent: "center",
    alignItems: "center",
    padding: theme.spacing.lg,
  } as ViewStyle,

  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.xl,
    width: "100%",
    maxWidth: 400,
    maxHeight: "80%",
    ...theme.shadows.lg,
  } as ViewStyle,

  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  } as ViewStyle,

  title: {
    ...theme.typography.h2,
    color: theme.colors.text,
  } as TextStyle,

  content: {
    flex: 1,
  } as ViewStyle,

  footer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: theme.spacing.md,
    marginTop: theme.spacing.lg,
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  } as ViewStyle,
});

// =====================================================
// TICKET STATUS INDICATORS
// =====================================================

export const getTicketStatusIndicator = (
  theme: Theme,
  variant: TicketVariant
) => {
  const colors = getTicketColors(theme, variant);

  return {
    container: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.round,
      backgroundColor: colors.accent + "20",
    } as ViewStyle,

    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.accent,
      marginRight: theme.spacing.xs,
    } as ViewStyle,

    text: {
      ...theme.typography.small,
      color: colors.accent,
      fontWeight: "600",
    } as TextStyle,
  };
};

// =====================================================
// TICKET LIST STYLES
// =====================================================

export const getTicketListStyles = (theme: Theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  } as ViewStyle,

  section: {
    marginBottom: theme.spacing.xl,
  } as ViewStyle,

  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
  } as ViewStyle,

  sectionTitle: {
    ...theme.typography.h3,
    color: theme.colors.text,
  } as TextStyle,

  sectionCount: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.round,
  } as TextStyle,

  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: theme.spacing.xxl,
  } as ViewStyle,

  emptyText: {
    ...theme.typography.body,
    color: theme.colors.textSecondary,
    textAlign: "center",
    marginTop: theme.spacing.md,
  } as TextStyle,
});

// =====================================================
// ANIMATION CONFIGURATIONS
// =====================================================

export const TicketAnimations = {
  cardPress: {
    scale: 0.98,
    duration: 150,
  },

  cardSwipe: {
    translateX: 100,
    duration: 300,
  },

  modalSlide: {
    translateY: 50,
    opacity: 0,
    duration: 300,
  },

  statusChange: {
    scale: 1.05,
    duration: 200,
  },
};

// =====================================================
// TICKET FORM STYLES
// =====================================================

export const getTicketFormStyles = (theme: Theme) => ({
  container: {
    gap: theme.spacing.lg,
  } as ViewStyle,

  fieldGroup: {
    gap: theme.spacing.md,
  } as ViewStyle,

  label: {
    ...theme.typography.label,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  } as TextStyle,

  input: {
    backgroundColor: theme.colors.input,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    ...theme.typography.body,
    color: theme.colors.text,
  } as ViewStyle,

  inputFocused: {
    borderColor: theme.colors.primary,
    ...theme.shadows.sm,
  } as ViewStyle,

  inputError: {
    borderColor: theme.colors.error,
  } as ViewStyle,

  errorText: {
    ...theme.typography.small,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  } as TextStyle,

  helpText: {
    ...theme.typography.small,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  } as TextStyle,
});

// =====================================================
// TICKET STATS STYLES
// =====================================================

export const getTicketStatsStyles = (theme: Theme) => ({
  container: {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.sm,
  } as ViewStyle,

  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  } as ViewStyle,

  statItem: {
    alignItems: "center",
    flex: 1,
  } as ViewStyle,

  statNumber: {
    ...theme.typography.h1,
    color: theme.colors.primary,
    fontWeight: "700",
  } as TextStyle,

  statLabel: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    textAlign: "center",
    marginTop: theme.spacing.xs,
  } as TextStyle,

  divider: {
    width: 1,
    height: 40,
    backgroundColor: theme.colors.border,
    marginHorizontal: theme.spacing.md,
  } as ViewStyle,
});

// =====================================================
// TICKET ACTION STYLES
// =====================================================

export const getTicketActionStyles = (theme: Theme) => ({
  container: {
    flexDirection: "row",
    gap: theme.spacing.sm,
    marginTop: theme.spacing.md,
  } as ViewStyle,

  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.sm,
  } as ViewStyle,

  primaryAction: {
    backgroundColor: theme.colors.primary,
  } as ViewStyle,

  secondaryAction: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  } as ViewStyle,

  dangerAction: {
    backgroundColor: theme.colors.error,
  } as ViewStyle,

  actionText: {
    ...theme.typography.button,
    color: theme.colors.primaryForeground,
  } as TextStyle,

  secondaryActionText: {
    ...theme.typography.button,
    color: theme.colors.text,
  } as TextStyle,
});

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

export const createTicketStyles = (theme: Theme) => ({
  // Card styles
  card: getTicketCardStyle(theme),
  activeCard: getTicketCardStyle(theme, "active"),
  pausedCard: getTicketCardStyle(theme, "paused"),
  completedCard: getTicketCardStyle(theme, "completed"),

  // Text styles
  text: getTicketTextStyles(theme),

  // Button styles
  buttons: {
    primary: getTicketButtonStyle(theme, "primary"),
    secondary: getTicketButtonStyle(theme, "secondary"),
    ghost: getTicketButtonStyle(theme, "ghost"),
  },

  // Modal styles
  modal: getTicketModalStyles(theme),

  // Form styles
  form: getTicketFormStyles(theme),

  // Stats styles
  stats: getTicketStatsStyles(theme),

  // Action styles
  actions: getTicketActionStyles(theme),

  // List styles
  list: getTicketListStyles(theme),
});

// =====================================================
// THEME VALIDATION
// =====================================================

export const validateTicketTheme = (theme: Theme): boolean => {
  const requiredColors = [
    "primary",
    "surface",
    "text",
    "textSecondary",
    "border",
    "success",
    "warning",
    "error",
    "card",
    "background",
  ];

  return requiredColors.every(
    (color) =>
      color in theme.colors && theme.colors[color as keyof typeof theme.colors]
  );
};
