/**
 * Fulfillment Manager Component
 *
 * Comprehensive fulfillment management interface for POS staff.
 * Demonstrates integration with TanStack React Query hooks and
 * provides all core fulfillment functionality.
 */

import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  useFulfillmentsByOrder,
  useCreateFulfillment,
  useUpdateDeliveryDetails,
  useUpdateFulfillmentStatus,
  useCalculateShippingFee,
  useShippingRates,
} from "../../hooks/useFulfillment";
import {
  FulfillmentData,
  UpdateDeliveryDetailsData,
} from "../../services/fulfillmentApiService";
import { formatCurrency } from "../../utils/currencyUtils";
import { theme } from "../../theme";

interface FulfillmentManagerProps {
  orderId: string;
  orderData?: any;
  onFulfillmentCreated?: (fulfillment: any) => void;
  onClose?: () => void;
}

export const FulfillmentManager: React.FC<FulfillmentManagerProps> = ({
  orderId,
  orderData,
  onFulfillmentCreated,
  onClose,
}) => {
  const [selectedDeliveryMethod, setSelectedDeliveryMethod] =
    useState<string>("standard");
  const [deliveryAddress, setDeliveryAddress] = useState<string>("");
  const [deliveryPhone, setDeliveryPhone] = useState<string>("");
  const [deliveryInstructions, setDeliveryInstructions] = useState<string>("");
  const [calculatedShippingFee, setCalculatedShippingFee] = useState<number>(0);

  // React Query hooks
  const { data: fulfillments, isLoading: fulfillmentsLoading } =
    useFulfillmentsByOrder(orderId);
  const { data: shippingRates, isLoading: ratesLoading } = useShippingRates();
  const createFulfillmentMutation = useCreateFulfillment();
  const updateDeliveryMutation = useUpdateDeliveryDetails();
  const updateStatusMutation = useUpdateFulfillmentStatus();
  const calculateShippingMutation = useCalculateShippingFee();

  const handleCalculateShipping = async () => {
    try {
      const result = await calculateShippingMutation.mutateAsync({
        deliveryMethod: selectedDeliveryMethod,
        distanceKm: 10, // This would come from address calculation
        weightKg: 1, // This would come from order items
      });
      setCalculatedShippingFee(result.shippingFee);
    } catch (error) {
      console.error("Shipping calculation failed:", error);
    }
  };

  const handleCreateFulfillment = async () => {
    if (!deliveryAddress.trim()) {
      Alert.alert("Error", "Please enter a delivery address");
      return;
    }

    const fulfillmentData: FulfillmentData = {
      orderId,
      shopifyOrderId: orderData?.shopifyOrderId,
      deliveryAddress: {
        street: deliveryAddress,
        city: "Nairobi", // Default city
        country: "Kenya",
      },
      deliveryContactName: orderData?.customer?.name || "",
      deliveryContactPhone: deliveryPhone || orderData?.customer?.phone,
      deliveryInstructions,
      deliveryMethod: selectedDeliveryMethod,
      shippingFee: calculatedShippingFee,
      shippingFeeCurrency: "KES",
    };

    try {
      const result = await createFulfillmentMutation.mutateAsync(
        fulfillmentData
      );
      onFulfillmentCreated?.(result);
      Alert.alert("Success", "Fulfillment created successfully");
    } catch (error) {
      console.error("Create fulfillment failed:", error);
    }
  };

  const handleUpdateDeliveryDetails = async (fulfillmentId: string) => {
    const updateData: UpdateDeliveryDetailsData = {
      deliveryAddress: {
        street: deliveryAddress,
        city: "Nairobi",
        country: "Kenya",
      },
      deliveryContactPhone: deliveryPhone,
      deliveryInstructions,
      deliveryMethod: selectedDeliveryMethod,
      shippingFee: calculatedShippingFee,
    };

    try {
      await updateDeliveryMutation.mutateAsync({
        fulfillmentId,
        updateData,
      });
      Alert.alert("Success", "Delivery details updated successfully");
    } catch (error) {
      console.error("Update delivery details failed:", error);
    }
  };

  const handleUpdateStatus = async (
    fulfillmentId: string,
    newStatus: string
  ) => {
    try {
      await updateStatusMutation.mutateAsync({
        fulfillmentId,
        status: newStatus,
      });
      Alert.alert("Success", `Status updated to ${newStatus}`);
    } catch (error) {
      console.error("Update status failed:", error);
    }
  };

  if (fulfillmentsLoading || ratesLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading fulfillment data...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Fulfillment Management</Text>
        <Text style={styles.subtitle}>Order: {orderId}</Text>
      </View>

      {/* Delivery Method Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Delivery Method</Text>
        <View style={styles.methodGrid}>
          {shippingRates?.map((rate) => (
            <TouchableOpacity
              key={rate.id}
              style={[
                styles.methodCard,
                selectedDeliveryMethod === rate.deliveryMethod &&
                  styles.selectedMethod,
              ]}
              onPress={() => setSelectedDeliveryMethod(rate.deliveryMethod)}
            >
              <Text style={styles.methodName}>
                 {rate.deliveryMethod?.toUpperCase() || "STANDARD"}
              </Text>
              <Text style={styles.methodFee}>
                Base: {formatCurrency(rate.baseFee)}
              </Text>
              <Text style={styles.methodDescription}>{rate.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Shipping Fee Calculation */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Shipping Fee</Text>
        <TouchableOpacity
          style={styles.calculateButton}
          onPress={handleCalculateShipping}
          disabled={calculateShippingMutation.isPending}
        >
          <Text style={styles.calculateButtonText}>
            {calculateShippingMutation.isPending
              ? "Calculating..."
              : "Calculate Shipping Fee"}
          </Text>
        </TouchableOpacity>
        {calculatedShippingFee > 0 && (
          <Text style={styles.calculatedFee}>
            Calculated Fee: {formatCurrency(calculatedShippingFee)}
          </Text>
        )}
      </View>

      {/* Existing Fulfillments */}
      {fulfillments && fulfillments.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Existing Fulfillments</Text>
          {fulfillments.map((fulfillment) => (
            <View key={fulfillment.id} style={styles.fulfillmentCard}>
              <View style={styles.fulfillmentHeader}>
                <Text style={styles.fulfillmentId}>
                  #{fulfillment.id.slice(-8)}
                </Text>
                <Text
                  style={[
                    styles.fulfillmentStatus,
                    { color: getStatusColor(fulfillment.fulfillmentStatus) },
                  ]}
                >
                  {fulfillment.fulfillmentStatus.toUpperCase()}
                </Text>
              </View>
              <Text style={styles.fulfillmentMethod}>
                Method: {fulfillment.deliveryMethod}
              </Text>
              <Text style={styles.fulfillmentFee}>
                Fee: {formatCurrency(fulfillment.shippingFee)}
              </Text>
              {fulfillment.trackingNumber && (
                <Text style={styles.trackingNumber}>
                  Tracking: {fulfillment.trackingNumber}
                </Text>
              )}

              <View style={styles.fulfillmentActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleUpdateDeliveryDetails(fulfillment.id)}
                >
                  <Text style={styles.actionButtonText}>Update Details</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, styles.statusButton]}
                  onPress={() => handleUpdateStatus(fulfillment.id, "shipped")}
                >
                  <Text style={styles.actionButtonText}>Mark Shipped</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      )}

      {/* Create New Fulfillment */}
      {(!fulfillments || fulfillments.length === 0) && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Create Fulfillment</Text>
          <TouchableOpacity
            style={styles.createButton}
            onPress={handleCreateFulfillment}
            disabled={createFulfillmentMutation.isPending}
          >
            <Text style={styles.createButtonText}>
              {createFulfillmentMutation.isPending
                ? "Creating..."
                : "Create Fulfillment"}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );
};

const getStatusColor = (status: string): string => {
  switch (status) {
    case "pending":
      return theme.colors.warning;
    case "processing":
      return theme.colors.info;
    case "shipped":
      return theme.colors.primary;
    case "delivered":
      return theme.colors.success;
    case "cancelled":
      return theme.colors.error;
    default:
      return theme.colors.text;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: theme.colors.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: "center",
    marginTop: 50,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: theme.colors.text,
    marginBottom: 12,
  },
  methodGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  methodCard: {
    flex: 1,
    minWidth: 150,
    padding: 12,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedMethod: {
    borderColor: theme.colors.primary,
  },
  methodName: {
    fontSize: 14,
    fontWeight: "600",
    color: theme.colors.text,
    marginBottom: 4,
  },
  methodFee: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 11,
    color: theme.colors.textSecondary,
  },
  calculateButton: {
    backgroundColor: theme.colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  calculateButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  calculatedFee: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.success,
    textAlign: "center",
    marginTop: 12,
  },
  fulfillmentCard: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  fulfillmentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  fulfillmentId: {
    fontSize: 14,
    fontWeight: "600",
    color: theme.colors.text,
  },
  fulfillmentStatus: {
    fontSize: 12,
    fontWeight: "bold",
  },
  fulfillmentMethod: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  fulfillmentFee: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  trackingNumber: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  fulfillmentActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    padding: 8,
    borderRadius: 6,
    alignItems: "center",
  },
  statusButton: {
    backgroundColor: theme.colors.success,
  },
  actionButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  createButton: {
    backgroundColor: theme.colors.success,
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  createButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});
