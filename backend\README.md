# 🏪 **DUKALINK POS BACKEND**

Backend API server for Dukalink POS with Shopify integration and MySQL database migration capabilities.

---

## **📁 PROJECT STRUCTURE**

```
backend/
├── src/                          # Source code
│   ├── middleware/               # Authentication & middleware
│   ├── routes/                   # API routes
│   ├── services/                 # Business logic services
│   └── server.js                 # Main server file
├── migrations/                   # Database migration files
│   ├── 001_sales_agents_schema.sql
│   ├── 002_pos_staff_schema.sql
│   └── setup_database.sql
├── testing/                      # Testing scripts
│   └── test_current_system.js
├── .env.example                  # Environment variables template
├── package.json                  # Dependencies & scripts
└── TESTING_SETUP_GUIDE.md       # Complete testing guide
```

---

## **🚀 QUICK START**

### **One-Command Setup (Recommended)**
```bash
cd backend
npm install
npm run setup
```

This will automatically:
- ✅ Create database and user
- ✅ Run all migrations
- ✅ Seed initial data
- ✅ Verify setup

### **Manual Setup**
```bash
# 1. Install dependencies
cd backend
npm install

# 2. Setup environment
cp .env.example .env
# Edit .env with your database credentials

# 3. Setup database and run migrations
npm run setup

# 4. Start development server
npm run dev
```

📋 **For detailed setup instructions, see [SETUP_GUIDE.md](./SETUP_GUIDE.md)**

---

## **📋 AVAILABLE SCRIPTS**

### **Setup Commands**
| Script | Description |
|--------|-------------|
| `npm run setup` | **Complete one-command setup** |
| `npm run setup:complete` | Same as above (alias) |

### **Development Commands**
| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with nodemon |
| `npm start` | Start production server |
| `npm test` | Run current system tests |

### **Database Commands**
| Script | Description |
|--------|-------------|
| `npm run db:setup` | Setup MySQL database and user |
| `npm run db:create-schema` | Create database schema |
| `npm run db:migrate-staff` | Migrate staff authentication |
| `npm run db:migrate-agents` | Migrate sales agents |
| `npm run db:migrate-terminals` | Migrate terminal management |

---

## **🔧 ENVIRONMENT VARIABLES**

Required environment variables (see `.env.example`):

```bash
# Database
DB_HOST=localhost
DB_USER=dukalink
DB_PASSWORD=your_password
DB_NAME=dukalink_pos

# JWT
JWT_SECRET=your_secure_secret

# Shopify
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
```

---

## **🧪 TESTING**

### **Before Migration Testing**
```bash
# Test current in-memory system
npm test
```

**Expected Output:**
```
✅ Passed: 6
❌ Failed: 0
📈 Success Rate: 100.0%
🎉 All tests passed! System is ready for migration.
```

### **Manual API Testing**
```bash
# Test staff login
curl -X POST http://localhost:3020/api/pos/login \
  -H "Content-Type: application/json" \
  -d '{"username":"cashier1","password":"password123"}'

# Test sales agents
curl -X GET http://localhost:3020/api/sales-agents \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## **🗄️ DATABASE MIGRATION**

### **Current Status: IN-MEMORY STORAGE**
- Sales agents stored in Map()
- Staff authentication in hardcoded arrays
- Commission calculations in memory
- Terminal management in Map()

### **Migration Plan**
1. **Test current system** ✅ (Run `npm test`)
2. **Setup MySQL database** (Run `npm run db:setup`)
3. **Create schema** (Run `npm run db:schema`)
4. **Run migration scripts** (Coming soon)
5. **Switch to MySQL services** (Coming soon)
6. **Verify functionality** (Coming soon)

### **⚠️ IMPORTANT**
**DO NOT RUN MIGRATION** until all current system tests pass!

---

## **📊 API ENDPOINTS**

### **Authentication**
- `POST /api/pos/login` - Staff login
- `GET /api/pos/verify` - Verify JWT token

### **Sales Agents**
- `GET /api/sales-agents` - Get all sales agents
- `GET /api/sales-agents/:id` - Get sales agent by ID
- `POST /api/sales-agents` - Create new sales agent
- `PUT /api/sales-agents/:id` - Update sales agent

### **Staff Management**
- `GET /api/staff` - Get all staff members
- `POST /api/staff/create-order` - Create order with staff attribution

### **Commission & Discounts**
- `GET /api/commission-discounts/config` - Get discount configuration
- `POST /api/commission-discounts/calculate` - Calculate commission discount

---

## **🔍 TROUBLESHOOTING**

### **Server Won't Start**
```bash
# Check port availability
lsof -i :3020

# Check environment variables
cat .env

# Install dependencies
npm install
```

### **Database Connection Issues**
```bash
# Test MySQL connection
mysql -u dukalink -p -e "SELECT 1;"

# Check MySQL service
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS
```

### **API Tests Failing**
```bash
# Check server logs
tail -f logs/dukalink-pos.log

# Test health endpoint
curl http://localhost:3020/api/health
```

---

## **📚 DOCUMENTATION**

- **[Testing Setup Guide](TESTING_SETUP_GUIDE.md)** - Complete testing instructions
- **[Environment Variables](.env.example)** - Configuration template
- **[Migration Scripts](migrations/)** - Database migration files

---

## **🤝 DEVELOPMENT WORKFLOW**

1. **Make changes** to source code
2. **Test locally** with `npm test`
3. **Verify APIs** work correctly
4. **Check database** if using MySQL
5. **Commit changes** when tests pass

---

## **📞 SUPPORT**

For issues or questions:
1. Check the troubleshooting section above
2. Review the testing guide
3. Check server logs for errors
4. Verify environment configuration

---

## **🎯 NEXT STEPS**

1. **✅ Test current system** - Run `npm test`
2. **🔄 Setup database** - Run `npm run db:setup`
3. **🧪 Run migration** - Coming soon
4. **🚀 Deploy to production** - After migration testing
