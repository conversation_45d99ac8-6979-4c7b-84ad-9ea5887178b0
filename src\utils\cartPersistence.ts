/**
 * Cart Persistence Utility
 *
 * Provides localStorage-based persistence for cart data to survive
 * React re-renders and Redux state transitions during ticket ID changes.
 */

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Customer,
  Salesperson,
  OrderDiscount,
} from "../types/shopify";

interface PersistedCartData {
  items: CartItem[];
  customer?: Customer;
  salesperson?: Salesperson | null;
  discounts: OrderDiscount[];
  note?: string;
  timestamp: number;
  ticketId?: string;
}

const CART_STORAGE_KEY = "dukalink_active_cart";
const STORAGE_EXPIRY_HOURS = 24; // Cart data expires after 24 hours

/**
 * Save cart data to localStorage
 */
export const saveCartToStorage = (
  cartData: Omit<PersistedCartData, "timestamp">
) => {
  try {
    const dataToSave: PersistedCartData = {
      ...cartData,
      timestamp: Date.now(),
    };

    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(dataToSave));
    console.log(
      `💾 Cart saved to localStorage: ${cartData.items.length} items`
    );
  } catch (error) {
    console.error("Failed to save cart to localStorage:", error);
  }
};

/**
 * Load cart data from localStorage
 */
export const loadCartFromStorage = (): PersistedCartData | null => {
  try {
    const stored = localStorage.getItem(CART_STORAGE_KEY);
    if (!stored) return null;

    const data: PersistedCartData = JSON.parse(stored);

    // Check if data is expired
    const ageHours = (Date.now() - data.timestamp) / (1000 * 60 * 60);
    if (ageHours > STORAGE_EXPIRY_HOURS) {
      console.log("📅 Stored cart data expired, clearing...");
      clearCartFromStorage();
      return null;
    }

    console.log(
      `📂 Cart loaded from localStorage: ${
        data.items.length
      } items (age: ${Math.round(ageHours * 60)}min)`
    );
    return data;
  } catch (error) {
    console.error("Failed to load cart from localStorage:", error);
    return null;
  }
};

/**
 * Clear cart data from localStorage
 */
export const clearCartFromStorage = () => {
  try {
    const hadData = localStorage.getItem(CART_STORAGE_KEY) !== null;
    localStorage.removeItem(CART_STORAGE_KEY);
    console.log(
      `🗑️ Cart cleared from localStorage ${
        hadData ? "(had data)" : "(was empty)"
      }`
    );
  } catch (error) {
    console.error("Failed to clear cart from localStorage:", error);
  }
};

/**
 * Check if stored cart data exists and is valid
 */
export const hasValidStoredCart = (): boolean => {
  const stored = loadCartFromStorage();
  return stored !== null && stored.items.length > 0;
};

/**
 * Update only items in stored cart (for quick updates)
 * ENHANCED: Clear entire localStorage when cart becomes empty for consistency
 */
export const updateStoredCartItems = (items: CartItem[]) => {
  // If no items, clear everything from localStorage (consistent with sale completion)
  if (items.length === 0) {
    clearCartFromStorage();
    console.log(
      "🗑️ Cart emptied - cleared entire localStorage for consistency"
    );
    return;
  }

  // Otherwise, update items while preserving other cart data
  const existing = loadCartFromStorage();
  if (existing) {
    saveCartToStorage({
      ...existing,
      items,
    });
  } else {
    saveCartToStorage({
      items,
      discounts: [],
    });
  }
};

/**
 * Recovery utility - detects if cart should be restored
 */
export const shouldRecoverCart = (
  currentItems: CartItem[],
  ticketId?: string
): boolean => {
  // If current cart is empty but we have stored data
  if (currentItems.length === 0) {
    const stored = loadCartFromStorage();
    if (stored && stored.items.length > 0) {
      // If ticket ID changed, definitely recover
      if (ticketId && stored.ticketId && ticketId !== stored.ticketId) {
        console.log(
          `🔄 Ticket ID changed (${stored.ticketId} → ${ticketId}), should recover cart`
        );
        return true;
      }

      // If cart was recently active (within 5 minutes), recover
      const ageMinutes = (Date.now() - stored.timestamp) / (1000 * 60);
      if (ageMinutes < 5) {
        console.log(
          `⚡ Recent cart activity detected (${Math.round(
            ageMinutes
          )}min ago), should recover cart`
        );
        return true;
      }
    }
  }

  return false;
};

/**
 * Get storage statistics for debugging
 */
export const getStorageStats = () => {
  const stored = loadCartFromStorage();
  return {
    hasData: !!stored,
    itemCount: stored?.items.length || 0,
    ageMinutes: stored
      ? Math.round((Date.now() - stored.timestamp) / (1000 * 60))
      : 0,
    ticketId: stored?.ticketId,
  };
};

/**
 * Debug utilities - expose to global window for testing
 */
if (typeof window !== "undefined") {
  (window as any).cartDebug = {
    getStorageStats,
    loadCartFromStorage,
    clearCartFromStorage,
    hasValidStoredCart,
  };
}
