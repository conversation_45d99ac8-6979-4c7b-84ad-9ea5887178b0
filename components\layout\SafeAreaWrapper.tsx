import { useThemeColor } from "@/hooks/useThemeColor";
import React from "react";
import { StatusBar, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { ResponsiveContainer } from "@/src/components/layout/ResponsiveContainer";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

interface SafeAreaWrapperProps {
  children: React.ReactNode;
  edges?: ("top" | "bottom" | "left" | "right")[];
  style?: any;
  useResponsiveContainer?: boolean;
  maxWidth?: boolean;
  centered?: boolean;
}

/**
 * SafeAreaWrapper component that properly handles safe areas
 * with stable layout to prevent expansion/snapping issues
 */
export function SafeAreaWrapper({
  children,
  edges = ["top", "bottom"],
  style,
  useResponsiveContainer = true,
  maxWidth = true,
  centered = true,
}: SafeAreaWrapperProps) {
  const backgroundColor = useThemeColor({}, "background");
  const responsiveLayout = useResponsiveLayout();
  const isDesktop = responsiveLayout?.isDesktop || false;

  // Remove the force refresh that causes layout instability
  // The SafeAreaProvider at root level handles app state changes properly

  const content =
    useResponsiveContainer && Platform.OS === "web" ? (
      <ResponsiveContainer
        maxWidth={maxWidth}
        centered={centered}
        padding={false}
        style={{ flex: 1 }}
      >
        {children}
      </ResponsiveContainer>
    ) : (
      children
    );

  return (
    <SafeAreaView
      style={[
        {
          flex: 1,
          backgroundColor,
          // Add layout stability constraints
          minHeight: 0, // Prevent flex expansion beyond container
          maxHeight: "100%", // Constrain to viewport
          // Add web-specific styling
          ...(Platform.OS === "web" &&
            isDesktop && {
              minWidth: 320, // Minimum width for desktop
            }),
        },
        style,
      ]}
      edges={edges}
    >
      <StatusBar barStyle="light-content" backgroundColor={backgroundColor} />
      {content}
    </SafeAreaView>
  );
}
