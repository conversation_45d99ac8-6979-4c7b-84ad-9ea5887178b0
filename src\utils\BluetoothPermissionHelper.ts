import { Al<PERSON>, PermissionsAndroid, Platform } from "react-native";

export interface BluetoothPermissionResult {
  granted: boolean;
  error?: string;
  shouldShowRationale?: boolean;
}

export interface BluetoothState {
  enabled: boolean;
  available: boolean;
}

export const BluetoothPermissionHelper = {
  /**
   * Request Bluetooth permissions for Android 12+ compatibility
   */
  requestBluetoothPermissions: async (): Promise<BluetoothPermissionResult> => {
    if (Platform.OS !== "android") {
      return { granted: true };
    }

    try {
      // Check Android version for appropriate permissions
      if (Platform.Version >= 31) {
        // Android 12+ requires new Bluetooth permissions
        const bluetoothConnectGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          {
            title: "Bluetooth Connect Permission",
            message:
              "This app needs access to Bluetooth to connect to thermal printers for receipt printing.",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK",
          }
        );

        const bluetoothScanGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          {
            title: "Bluetooth Scan Permission",
            message:
              "This app needs access to Bluetooth to scan for thermal printers.",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK",
          }
        );

        // Also request location permission as it's required for Bluetooth scanning
        const locationGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: "Location Permission",
            message:
              "This app needs location access to scan for Bluetooth thermal printers.",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK",
          }
        );

        const allGranted =
          bluetoothConnectGranted === PermissionsAndroid.RESULTS.GRANTED &&
          bluetoothScanGranted === PermissionsAndroid.RESULTS.GRANTED &&
          locationGranted === PermissionsAndroid.RESULTS.GRANTED;

        if (!allGranted) {
          return {
            granted: false,
            error:
              "Bluetooth permissions are required for thermal printer functionality",
          };
        }

        return { granted: true };
      } else {
        // For older Android versions, check if legacy permissions are available
        const hasBluetoothPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH
        );
        const hasBluetoothAdminPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN
        );
        const hasLocationPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );

        if (!hasLocationPermission) {
          const locationGranted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: "Location Permission",
              message:
                "This app needs location access to scan for Bluetooth thermal printers.",
              buttonNeutral: "Ask Me Later",
              buttonNegative: "Cancel",
              buttonPositive: "OK",
            }
          );

          if (locationGranted !== PermissionsAndroid.RESULTS.GRANTED) {
            return {
              granted: false,
              error: "Location permission is required for Bluetooth scanning",
            };
          }
        }

        // Legacy Bluetooth permissions are granted at install time
        return {
          granted: hasBluetoothPermission && hasBluetoothAdminPermission,
          error:
            !hasBluetoothPermission || !hasBluetoothAdminPermission
              ? "Bluetooth permissions not available"
              : undefined,
        };
      }
    } catch (error) {
      console.warn("Error requesting Bluetooth permissions:", error);
      return {
        granted: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to request Bluetooth permissions",
      };
    }
  },

  /**
   * Check if Bluetooth permissions are granted
   */
  checkBluetoothPermissions: async (): Promise<BluetoothPermissionResult> => {
    if (Platform.OS !== "android") {
      return { granted: true };
    }

    try {
      if (Platform.Version >= 31) {
        // Check Android 12+ permissions
        const bluetoothConnectGranted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
        );
        const bluetoothScanGranted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN
        );
        const locationGranted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );

        const allGranted =
          bluetoothConnectGranted && bluetoothScanGranted && locationGranted;

        return {
          granted: allGranted,
          error: !allGranted
            ? "Some Bluetooth permissions are not granted"
            : undefined,
        };
      } else {
        // Check legacy permissions
        const hasBluetoothPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH
        );
        const hasBluetoothAdminPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN
        );
        const hasLocationPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );

        const allGranted =
          hasBluetoothPermission &&
          hasBluetoothAdminPermission &&
          hasLocationPermission;

        return {
          granted: allGranted,
          error: !allGranted
            ? "Some Bluetooth permissions are not granted"
            : undefined,
        };
      }
    } catch (error) {
      console.warn("Error checking Bluetooth permissions:", error);
      return {
        granted: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to check Bluetooth permissions",
      };
    }
  },

  /**
   * Check if Bluetooth is enabled
   */
  isBluetoothEnabled: async (): Promise<boolean> => {
    try {
      if (Platform.OS !== "android") {
        // For iOS, assume Bluetooth is available if permissions are granted
        return true;
      }

      // Try to use the BluetoothManager from the thermal printing library
      const {
        BluetoothManager,
      } = require("react-native-bluetooth-escpos-printer");

      if (BluetoothManager && BluetoothManager.isBluetoothEnabled) {
        return await BluetoothManager.isBluetoothEnabled();
      }

      // Fallback: assume enabled if we can't check
      return true;
    } catch (error) {
      console.warn("Error checking Bluetooth state:", error);
      return false;
    }
  },

  /**
   * Enable Bluetooth (Android only)
   */
  enableBluetooth: async (): Promise<boolean> => {
    try {
      if (Platform.OS !== "android") {
        return false;
      }

      // First check permissions
      const permissionResult =
        await BluetoothPermissionHelper.requestBluetoothPermissions();
      if (!permissionResult.granted) {
        return false;
      }

      // Try to enable Bluetooth using the thermal printing library
      const {
        BluetoothManager,
      } = require("react-native-bluetooth-escpos-printer");

      if (BluetoothManager && BluetoothManager.enableBluetooth) {
        await BluetoothManager.enableBluetooth();

        // Wait a moment for Bluetooth to initialize
        await new Promise((resolve) => setTimeout(resolve, 1000));

        return await BluetoothPermissionHelper.isBluetoothEnabled();
      }

      return false;
    } catch (error) {
      console.warn("Error enabling Bluetooth:", error);
      return false;
    }
  },

  /**
   * Get Bluetooth state information
   */
  getBluetoothState: async (): Promise<BluetoothState> => {
    try {
      const enabled = await BluetoothPermissionHelper.isBluetoothEnabled();
      const permissionResult =
        await BluetoothPermissionHelper.checkBluetoothPermissions();

      return {
        enabled,
        available: permissionResult.granted,
      };
    } catch (error) {
      console.warn("Error getting Bluetooth state:", error);
      return {
        enabled: false,
        available: false,
      };
    }
  },

  /**
   * Show permission rationale dialog
   */
  showPermissionRationale: (): Promise<boolean> => {
    return new Promise((resolve) => {
      Alert.alert(
        "Bluetooth Permissions Required",
        "This app needs Bluetooth permissions to connect to thermal printers for receipt printing. Please grant the permissions to use thermal printing features.",
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () => resolve(false),
          },
          {
            text: "Grant Permissions",
            onPress: async () => {
              const result =
                await BluetoothPermissionHelper.requestBluetoothPermissions();
              resolve(result.granted);
            },
          },
        ]
      );
    });
  },

  /**
   * Show Bluetooth enable dialog
   */
  showBluetoothEnableDialog: (): Promise<boolean> => {
    return new Promise((resolve) => {
      Alert.alert(
        "Enable Bluetooth",
        "Bluetooth is required for thermal printer connectivity. Would you like to enable Bluetooth now?",
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () => resolve(false),
          },
          {
            text: "Enable",
            onPress: async () => {
              const enabled = await BluetoothPermissionHelper.enableBluetooth();
              resolve(enabled);
            },
          },
        ]
      );
    });
  },

  /**
   * Comprehensive Bluetooth setup check
   */
  ensureBluetoothReady: async (): Promise<BluetoothPermissionResult> => {
    try {
      // Step 1: Check permissions
      let permissionResult =
        await BluetoothPermissionHelper.checkBluetoothPermissions();

      if (!permissionResult.granted) {
        // Request permissions if not granted
        permissionResult =
          await BluetoothPermissionHelper.requestBluetoothPermissions();

        if (!permissionResult.granted) {
          return permissionResult;
        }
      }

      // Step 2: Check if Bluetooth is enabled
      const isEnabled = await BluetoothPermissionHelper.isBluetoothEnabled();

      if (!isEnabled) {
        // Try to enable Bluetooth
        const enabled = await BluetoothPermissionHelper.enableBluetooth();

        if (!enabled) {
          return {
            granted: false,
            error:
              "Bluetooth is not enabled and could not be enabled automatically",
          };
        }
      }

      return { granted: true };
    } catch (error) {
      console.error("Error ensuring Bluetooth ready:", error);
      return {
        granted: false,
        error:
          error instanceof Error ? error.message : "Failed to setup Bluetooth",
      };
    }
  },
};

export default BluetoothPermissionHelper;
