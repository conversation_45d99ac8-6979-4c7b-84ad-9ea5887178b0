import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  StyleSheet,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { PAYMENT_METHODS } from "@/src/types/payment";
import PaymentService from "@/src/services/payment-service";

interface SplitPaymentModalProps {
  visible: boolean;
  onClose: () => void;
  totalAmount: number;
  onPaymentComplete: (splitResult: any) => void;
  staffInfo: { id: string; name: string; terminal: string };
  customerInfo?: { name: string; phone?: string };
}

interface SplitPaymentMethod {
  id: string;
  paymentMethod: any;
  amount: string;
  amountTendered: string;
  transactionCode: string;
}

const SplitPaymentModal: React.FC<SplitPaymentModalProps> = ({
  visible,
  onClose,
  totalAmount,
  onPaymentComplete,
  staffInfo,
  customerInfo,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [splitPayments, setSplitPayments] = useState<SplitPaymentMethod[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const steps = ["Add Payments", "Review & Confirm", "Processing"];

  useEffect(() => {
    if (visible) {
      // Reset state when modal opens
      setCurrentStep(0);
      setSplitPayments([]);
      setIsProcessing(false);
    }
  }, [visible]);

  const addPaymentMethod = () => {
    // Find the first enabled payment method, default to cash
    const defaultMethod =
      PAYMENT_METHODS.find((m) => m.enabled) || PAYMENT_METHODS[0];

    const newPayment: SplitPaymentMethod = {
      id: Date.now().toString(),
      paymentMethod: defaultMethod,
      amount: "",
      amountTendered: "",
      transactionCode: "",
    };
    setSplitPayments([...splitPayments, newPayment]);
  };

  const removePaymentMethod = (id: string) => {
    setSplitPayments(splitPayments.filter((p) => p.id !== id));
  };

  const updatePaymentMethod = (id: string, field: string, value: any) => {
    setSplitPayments(
      splitPayments.map((p) => (p.id === id ? { ...p, [field]: value } : p))
    );
  };

  const getTotalPaymentAmount = () => {
    return splitPayments.reduce((sum, payment) => {
      return sum + (parseFloat(payment.amount) || 0);
    }, 0);
  };

  const getRemainingAmount = () => {
    return totalAmount - getTotalPaymentAmount();
  };

  const validatePayments = () => {
    if (splitPayments.length === 0) {
      Alert.alert("Error", "Please add at least one payment method");
      return false;
    }

    const totalPaymentAmount = getTotalPaymentAmount();
    if (Math.abs(totalPaymentAmount - totalAmount) > 0.01) {
      Alert.alert(
        "Error",
        `Payment total (${PaymentService.formatCurrency(
          totalPaymentAmount
        )}) must equal order total (${PaymentService.formatCurrency(
          totalAmount
        )})`
      );
      return false;
    }

    // Validate individual payments
    for (let i = 0; i < splitPayments.length; i++) {
      const payment = splitPayments[i];
      const amount = parseFloat(payment.amount);

      if (!amount || amount <= 0) {
        Alert.alert("Error", `Payment ${i + 1}: Amount must be greater than 0`);
        return false;
      }

      if (
        payment.paymentMethod?.type === "cash" ||
        payment.paymentMethod?.type === "credit"
      ) {
        const amountTendered = parseFloat(payment.amountTendered) || amount;
        if (amountTendered < amount) {
          Alert.alert(
            "Error",
            `Payment ${i + 1}: Insufficient ${
              payment.paymentMethod?.name?.toLowerCase() || "payment"
            } tendered`
          );
          return false;
        }
      }

      if (payment.paymentMethod?.type === "absa_till") {
        // ABSA Till transaction code is now optional
        // Payment can proceed without transaction code for manual entry later
      }
    }

    return true;
  };

  const processSplitPayment = async () => {
    if (!validatePayments()) return;

    setIsProcessing(true);
    setCurrentStep(2);

    try {
      const splitRequest = {
        totalAmount,
        payments: splitPayments.map((p) => ({
          paymentMethod: p.paymentMethod,
          amount: parseFloat(p.amount),
          amountTendered: parseFloat(p.amountTendered) || parseFloat(p.amount),
          additionalData:
            p.paymentMethod?.type === "absa_till"
              ? {
                  tillNumber: "123456",
                  accountNumber: customerInfo?.phone || "POS-PAYMENT",
                  transactionCode: p.transactionCode,
                }
              : undefined,
        })),
        currency: "KES",
      };

      const result = await PaymentService.processSplitPayment(
        splitRequest,
        staffInfo,
        customerInfo
      );

      if (result.success) {
        onPaymentComplete(result);
        onClose();
      } else {
        Alert.alert(
          "Payment Failed",
          result.error || "Split payment processing failed"
        );
        setCurrentStep(1); // Go back to review step
      }
    } catch (error) {
      console.error("Split payment error:", error);
      Alert.alert("Error", "Failed to process split payment");
      setCurrentStep(1);
    } finally {
      setIsProcessing(false);
    }
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {steps.map((step, index) => (
        <View key={index} style={styles.stepItem}>
          <View
            style={[
              styles.stepCircle,
              index <= currentStep && styles.stepCircleActive,
            ]}
          >
            <Text
              style={[
                styles.stepNumber,
                index <= currentStep && styles.stepNumberActive,
              ]}
            >
              {index + 1}
            </Text>
          </View>
          <Text style={styles.stepLabel}>{step}</Text>
        </View>
      ))}
    </View>
  );

  const renderPaymentMethodSelector = (payment: SplitPaymentMethod) => (
    <View style={styles.paymentMethodSelector}>
      <Text style={styles.selectorLabel}>Payment Method:</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {PAYMENT_METHODS.filter((m) => m.enabled).map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.methodOption,
              payment.paymentMethod?.id === method.id &&
                styles.methodOptionSelected,
            ]}
            onPress={() =>
              updatePaymentMethod(payment.id, "paymentMethod", method)
            }
          >
            <Text style={styles.methodIcon}>{method.icon}</Text>
            <Text style={styles.methodName}>{method.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderPaymentInputs = (payment: SplitPaymentMethod, index: number) => (
    <View key={payment.id} style={styles.paymentCard}>
      <View style={styles.paymentHeader}>
        <Text style={styles.paymentTitle}>Payment {index + 1}</Text>
        <TouchableOpacity
          onPress={() => removePaymentMethod(payment.id)}
          style={styles.removeButton}
        >
          <Ionicons name="close-circle" size={24} color="#e74c3c" />
        </TouchableOpacity>
      </View>

      {renderPaymentMethodSelector(payment)}

      <View style={styles.inputRow}>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Amount *</Text>
          <TextInput
            style={styles.input}
            value={payment.amount}
            onChangeText={(value) =>
              updatePaymentMethod(payment.id, "amount", value)
            }
            placeholder="0.00"
            keyboardType="numeric"
          />
        </View>

        {(payment.paymentMethod?.type === "cash" ||
          payment.paymentMethod?.type === "credit") && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Amount Tendered</Text>
            <TextInput
              style={styles.input}
              value={payment.amountTendered}
              onChangeText={(value) =>
                updatePaymentMethod(payment.id, "amountTendered", value)
              }
              placeholder={payment.amount || "0.00"}
              keyboardType="numeric"
            />
          </View>
        )}
      </View>

      {payment.paymentMethod?.type === "absa_till" && (
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Transaction Code (Optional)</Text>
          <TextInput
            style={styles.input}
            value={payment.transactionCode}
            onChangeText={(value) =>
              updatePaymentMethod(payment.id, "transactionCode", value)
            }
            placeholder="Enter ABSA Till transaction code (optional)"
            autoCapitalize="characters"
          />
        </View>
      )}

      {(payment.paymentMethod?.type === "cash" ||
        payment.paymentMethod?.type === "credit") &&
        payment.amount &&
        payment.amountTendered && (
          <View style={styles.changeInfo}>
            <Text style={styles.changeLabel}>
              Change:{" "}
              {PaymentService.formatCurrency(
                Math.max(
                  0,
                  parseFloat(payment.amountTendered) -
                    parseFloat(payment.amount)
                )
              )}
            </Text>
          </View>
        )}
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Split Payment</Text>
          <View style={styles.placeholder} />
        </View>

        {renderStepIndicator()}

        <View style={styles.amountSummary}>
          <Text style={styles.totalAmount}>
            Total: {PaymentService.formatCurrency(totalAmount)}
          </Text>
          <Text style={styles.remainingAmount}>
            Remaining: {PaymentService.formatCurrency(getRemainingAmount())}
          </Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {currentStep === 0 && (
            <View>
              {splitPayments.map((payment, index) =>
                renderPaymentInputs(payment, index)
              )}

              <TouchableOpacity
                style={styles.addButton}
                onPress={addPaymentMethod}
              >
                <Ionicons name="add-circle" size={24} color="#3498db" />
                <Text style={styles.addButtonText}>Add Payment Method</Text>
              </TouchableOpacity>
            </View>
          )}

          {currentStep === 1 && (
            <View style={styles.reviewSection}>
              <Text style={styles.reviewTitle}>Review Split Payment</Text>
              {splitPayments.map((payment, index) => (
                <View key={payment.id} style={styles.reviewItem}>
                  <Text style={styles.reviewMethod}>
                    {payment.paymentMethod?.name || "Payment Method"}
                  </Text>
                  <Text style={styles.reviewAmount}>
                    {PaymentService.formatCurrency(parseFloat(payment.amount))}
                  </Text>
                </View>
              ))}
              <View style={styles.reviewTotal}>
                <Text style={styles.reviewTotalText}>
                  Total:{" "}
                  {PaymentService.formatCurrency(getTotalPaymentAmount())}
                </Text>
              </View>
            </View>
          )}

          {currentStep === 2 && (
            <View style={styles.processingSection}>
              <Text style={styles.processingText}>
                Processing split payment...
              </Text>
            </View>
          )}
        </ScrollView>

        <View style={styles.footer}>
          {currentStep === 0 && (
            <TouchableOpacity
              style={[
                styles.nextButton,
                splitPayments.length === 0 && styles.buttonDisabled,
              ]}
              onPress={() => {
                if (validatePayments()) {
                  setCurrentStep(1);
                }
              }}
              disabled={splitPayments.length === 0}
            >
              <Text style={styles.buttonText}>Review Payment</Text>
            </TouchableOpacity>
          )}

          {currentStep === 1 && (
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, styles.backButton]}
                onPress={() => setCurrentStep(0)}
              >
                <Text style={styles.buttonText}>Back</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.processButton]}
                onPress={processSplitPayment}
                disabled={isProcessing}
              >
                <Text style={styles.buttonText}>Process Payment</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e8ed",
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  placeholder: {
    width: 40,
  },
  stepIndicator: {
    flexDirection: "row",
    justifyContent: "space-around",
    padding: 16,
    backgroundColor: "#fff",
    marginBottom: 8,
  },
  stepItem: {
    alignItems: "center",
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#e1e8ed",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 4,
  },
  stepCircleActive: {
    backgroundColor: "#3498db",
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
  },
  stepNumberActive: {
    color: "#fff",
  },
  stepLabel: {
    fontSize: 12,
    color: "#666",
  },
  amountSummary: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#fff",
    marginBottom: 8,
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  remainingAmount: {
    fontSize: 16,
    fontWeight: "600",
    color: "#e74c3c",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  paymentCard: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  paymentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  paymentTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  removeButton: {
    padding: 4,
  },
  paymentMethodSelector: {
    marginBottom: 16,
  },
  selectorLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
    marginBottom: 8,
  },
  methodOption: {
    alignItems: "center",
    padding: 12,
    marginRight: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e1e8ed",
    minWidth: 80,
  },
  methodOptionSelected: {
    borderColor: "#3498db",
    backgroundColor: "#e3f2fd",
  },
  methodIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  methodName: {
    fontSize: 12,
    color: "#333",
    textAlign: "center",
  },
  inputRow: {
    flexDirection: "row",
    gap: 12,
  },
  inputGroup: {
    flex: 1,
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
    marginBottom: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: "#e1e8ed",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  changeInfo: {
    backgroundColor: "#f8f9fa",
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
  },
  changeLabel: {
    fontSize: 14,
    color: "#27ae60",
    fontWeight: "500",
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    borderWidth: 2,
    borderColor: "#3498db",
    borderStyle: "dashed",
  },
  addButtonText: {
    fontSize: 16,
    color: "#3498db",
    fontWeight: "500",
    marginLeft: 8,
  },
  reviewSection: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
  },
  reviewTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
  },
  reviewItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  reviewMethod: {
    fontSize: 16,
    color: "#333",
  },
  reviewAmount: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  reviewTotal: {
    paddingTop: 12,
    marginTop: 8,
    borderTopWidth: 2,
    borderTopColor: "#e1e8ed",
  },
  reviewTotalText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    textAlign: "right",
  },
  processingSection: {
    alignItems: "center",
    padding: 32,
  },
  processingText: {
    fontSize: 16,
    color: "#666",
  },
  footer: {
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e1e8ed",
  },
  buttonRow: {
    flexDirection: "row",
    gap: 12,
  },
  button: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  nextButton: {
    backgroundColor: "#3498db",
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  backButton: {
    backgroundColor: "#95a5a6",
  },
  processButton: {
    backgroundColor: "#27ae60",
  },
  buttonDisabled: {
    backgroundColor: "#bdc3c7",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
});

export default SplitPaymentModal;
