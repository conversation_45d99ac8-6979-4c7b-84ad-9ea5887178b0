import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, Platform, Alert } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { ModernButton } from "@/src/components/ui/ModernButton";
import { ModernInput } from "@/src/components/ui/ModernInput";
import {
  T220MDPrintService,
  T220MDConfig,
} from "@/src/services/T220MDPrintService";

interface T220MDSetupProps {
  onSetupComplete?: (config: T220MDConfig) => void;
  onClose?: () => void;
}

export const T220MDSetup: React.FC<T220MDSetupProps> = ({
  onSetupComplete,
  onClose,
}) => {
  const { theme } = useTheme();
  const [config, setConfig] = useState<Partial<T220MDConfig>>({
    printerIP: "",
    printerPort: 9100,
    printServerURL: "http://localhost:3001",
    websocketURL: "",
    paperWidth: 80,
    charactersPerLine: 48,
    encoding: "utf-8",
    cutPaper: true,
    openDrawer: false,
  });

  const [connectionMethod, setConnectionMethod] = useState<
    "web-serial" | "network" | "cloud" | "browser"
  >("web-serial");
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    method?: string;
    error?: string;
  }>({ connected: false });

  useEffect(() => {
    if (Platform.OS === "web") {
      // Initialize T220MD service
      T220MDPrintService.init(config);
    }
  }, []);

  const handleConfigChange = (key: keyof T220MDConfig, value: any) => {
    setConfig((prev) => ({ ...prev, [key]: value }));
  };

  const testConnection = async () => {
    if (Platform.OS !== "web") {
      Alert.alert("Error", "T220MD printing is only available on web platform");
      return;
    }

    setIsConnecting(true);
    setConnectionStatus({ connected: false });

    try {
      // Configure the service with current settings
      T220MDPrintService.configure(config as T220MDConfig);

      // Test print
      const result = await T220MDPrintService.testPrint();

      if (result.success) {
        setConnectionStatus({
          connected: true,
          method: result.method,
        });

        if (Platform.OS === "web") {
          Alert.alert(
            "Success!",
            `T220MD printer connected successfully via ${result.method}!\nTest receipt should be printing now.`
          );
        }
      } else {
        setConnectionStatus({
          connected: false,
          error: result.error,
        });

        if (Platform.OS === "web") {
          Alert.alert("Connection Failed", result.error || "Unknown error");
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Connection test failed";
      setConnectionStatus({
        connected: false,
        error: errorMessage,
      });

      if (Platform.OS === "web") {
        Alert.alert("Error", errorMessage);
      }
    } finally {
      setIsConnecting(false);
    }
  };

  const handleSaveConfig = () => {
    if (connectionStatus.connected) {
      onSetupComplete?.(config as T220MDConfig);
      onClose?.();
    } else {
      if (Platform.OS === "web") {
        Alert.alert(
          "Test Connection First",
          "Please test the connection before saving the configuration."
        );
      }
    }
  };

  if (Platform.OS !== "web") {
    return (
      <View
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        <Text style={[styles.title, { color: theme.colors.text }]}>
          T220MD Thermal Printer
        </Text>
        <Text
          style={[styles.description, { color: theme.colors.textSecondary }]}
        >
          T220MD thermal printing is only available on web platform. Please use
          the web version of the app to configure your T220MD printer.
        </Text>
        {onClose && (
          <ModernButton
            title="Close"
            onPress={onClose}
            variant="outline"
            style={styles.button}
          />
        )}
      </View>
    );
  }

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <Text style={[styles.title, { color: theme.colors.text }]}>
        T220MD Thermal Printer Setup
      </Text>

      <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
        Configure your T220MD thermal printer (80mm paper width, ESC/POS
        compatible)
      </Text>

      {/* Connection Method Selection */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Connection Method
        </Text>

        <View style={styles.methodButtons}>
          <ModernButton
            title="USB Direct (Recommended)"
            onPress={() => setConnectionMethod("web-serial")}
            variant={connectionMethod === "web-serial" ? "primary" : "outline"}
            style={styles.methodButton}
          />
          <ModernButton
            title="Network (IP)"
            onPress={() => setConnectionMethod("network")}
            variant={connectionMethod === "network" ? "primary" : "outline"}
            style={styles.methodButton}
          />
          <ModernButton
            title="Cloud Print"
            onPress={() => setConnectionMethod("cloud")}
            variant={connectionMethod === "cloud" ? "primary" : "outline"}
            style={styles.methodButton}
          />
          <ModernButton
            title="Browser Print"
            onPress={() => setConnectionMethod("browser")}
            variant={connectionMethod === "browser" ? "primary" : "outline"}
            style={styles.methodButton}
          />
        </View>
      </View>

      {/* USB Configuration */}
      {connectionMethod === "web-serial" && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            USB Direct Connection (Web Serial API)
          </Text>

          <Text
            style={[styles.helpText, { color: theme.colors.textSecondary }]}
          >
            ✅ No server installation required{"\n"}✅ Direct USB communication
            with T220MD{"\n"}✅ Works in Chrome, Edge, Opera browsers{"\n"}✅
            Requires HTTPS (works on localhost){"\n\n"}
            Setup:{"\n"}
            1. Connect T220MD printer via USB{"\n"}
            2. Install printer drivers if required{"\n"}
            3. Click "Test Connection" below{"\n"}
            4. Grant permission to access serial devices{"\n"}
            5. Select your T220MD from the list
          </Text>

          <Text
            style={[
              styles.helpText,
              { color: theme.colors.primary, fontWeight: "600" },
            ]}
          >
            This is the recommended method - no additional software needed!
          </Text>
        </View>
      )}

      {/* Network Configuration */}
      {connectionMethod === "network" && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Network Configuration
          </Text>

          <ModernInput
            label="Printer IP Address"
            value={config.printerIP || ""}
            onChangeText={(value) => handleConfigChange("printerIP", value)}
            placeholder="*************"
            style={styles.input}
          />

          <ModernInput
            label="Printer Port"
            value={config.printerPort?.toString() || ""}
            onChangeText={(value) =>
              handleConfigChange("printerPort", parseInt(value) || 9100)
            }
            placeholder="9100"
            keyboardType="numeric"
            style={styles.input}
          />

          <Text
            style={[styles.helpText, { color: theme.colors.textSecondary }]}
          >
            Connect T220MD to your network and enter its IP address. Default
            ESC/POS port is 9100.
          </Text>
        </View>
      )}

      {/* WebSocket Configuration */}
      {connectionMethod === "cloud" && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Cloud Print Configuration
          </Text>

          <ModernInput
            label="Cloud Print Service"
            value={config.cloudPrintService || ""}
            onChangeText={(value) =>
              handleConfigChange("cloudPrintService", value)
            }
            placeholder="printnode or ezeep"
            style={styles.input}
          />

          <ModernInput
            label="API Key"
            value={config.cloudApiKey || ""}
            onChangeText={(value) => handleConfigChange("cloudApiKey", value)}
            placeholder="Your cloud print API key"
            style={styles.input}
          />

          <ModernInput
            label="Printer ID"
            value={config.cloudPrinterId || ""}
            onChangeText={(value) =>
              handleConfigChange("cloudPrinterId", value)
            }
            placeholder="Your T220MD printer ID"
            style={styles.input}
          />

          <Text
            style={[styles.helpText, { color: theme.colors.textSecondary }]}
          >
            Cloud printing allows remote printing from anywhere with internet.
            {"\n"}
            Supported services: PrintNode, ezeep{"\n"}
            Requires subscription to cloud print service.
          </Text>
        </View>
      )}

      {/* Browser Print Configuration */}
      {connectionMethod === "browser" && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Browser Print (Universal Fallback)
          </Text>

          <Text
            style={[styles.helpText, { color: theme.colors.textSecondary }]}
          >
            ✅ Works with any printer connected to computer{"\n"}✅ Universal
            browser compatibility{"\n"}✅ No configuration required{"\n"}✅ Uses
            standard browser print dialog{"\n\n"}
            Features:{"\n"}• Thermal-optimized formatting for 80mm receipts
            {"\n"}• Automatic paper size detection{"\n"}• Print preview
            available{"\n"}• Works with T220MD and other thermal printers
          </Text>

          <Text
            style={[
              styles.helpText,
              { color: theme.colors.primary, fontWeight: "600" },
            ]}
          >
            Best compatibility - works everywhere!
          </Text>
        </View>
      )}

      {/* Printer Settings */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Printer Settings
        </Text>

        <View style={styles.row}>
          <ModernButton
            title={`Cut Paper: ${config.cutPaper ? "ON" : "OFF"}`}
            onPress={() => handleConfigChange("cutPaper", !config.cutPaper)}
            variant={config.cutPaper ? "primary" : "outline"}
            style={styles.toggleButton}
          />

          <ModernButton
            title={`Open Drawer: ${config.openDrawer ? "ON" : "OFF"}`}
            onPress={() => handleConfigChange("openDrawer", !config.openDrawer)}
            variant={config.openDrawer ? "primary" : "outline"}
            style={styles.toggleButton}
          />
        </View>
      </View>

      {/* Connection Status */}
      {connectionStatus.connected && (
        <View style={[styles.statusContainer, styles.successStatus]}>
          <Text style={styles.statusText}>
            ✅ T220MD Connected via {connectionStatus.method}
          </Text>
        </View>
      )}

      {connectionStatus.error && (
        <View style={[styles.statusContainer, styles.errorStatus]}>
          <Text style={styles.statusText}>
            ❌ Connection Failed: {connectionStatus.error}
          </Text>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <ModernButton
          title={isConnecting ? "Testing..." : "Test Connection"}
          onPress={testConnection}
          variant="primary"
          disabled={isConnecting}
          style={styles.button}
        />

        <ModernButton
          title="Save Configuration"
          onPress={handleSaveConfig}
          variant="primary"
          disabled={!connectionStatus.connected}
          style={styles.button}
        />

        {onClose && (
          <ModernButton
            title="Cancel"
            onPress={onClose}
            variant="outline"
            style={styles.button}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 22,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  methodButtons: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  methodButton: {
    flex: 1,
    minWidth: 100,
  },
  input: {
    marginBottom: 12,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 8,
  },
  row: {
    flexDirection: "row",
    gap: 12,
  },
  toggleButton: {
    flex: 1,
  },
  statusContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  successStatus: {
    backgroundColor: "#d4edda",
  },
  errorStatus: {
    backgroundColor: "#f8d7da",
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
    color: "#000",
  },
  actionButtons: {
    gap: 12,
    marginTop: 16,
  },
  button: {
    marginBottom: 8,
  },
});

export default T220MDSetup;
