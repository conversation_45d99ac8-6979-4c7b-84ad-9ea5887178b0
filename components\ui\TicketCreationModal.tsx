/**
 * Modern TicketCreationModal Component
 *
 * Redesigned modal for creating new tickets with streamlined UX,
 * better validation feedback, and design system integration.
 */

import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  Animated,
} from "react-native";
import { IconSymbol } from "./IconSymbol";
import { ModernButton } from "./ModernButton";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  createTicketStyles,
  getTicketModalStyles,
} from "@/src/design-system/TicketDesignSystem";

interface TicketCreationModalProps {
  visible: boolean;
  onClose: () => void;
  onCreateTicket: (name: string) => void;
  maxTickets: number;
  currentTicketCount: number;
}

const TICKET_NAME_TEMPLATES = [
  { name: "Customer Order", icon: "person.fill", color: "primary" },
  { name: "Walk-in Sale", icon: "storefront.fill", color: "success" },
  { name: "Phone Order", icon: "phone.fill", color: "warning" },
  { name: "Special Request", icon: "star.fill", color: "error" },
  { name: "Bulk Order", icon: "shippingbox.fill", color: "primary" },
  { name: "VIP Customer", icon: "crown.fill", color: "warning" },
  {
    name: "Return/Exchange",
    icon: "arrow.triangle.2.circlepath",
    color: "error",
  },
  { name: "Layaway", icon: "clock.fill", color: "warning" },
];

const QUICK_ACTIONS = [
  { title: "Quick Start", subtitle: "Auto-generated name", action: "quick" },
  { title: "Custom Name", subtitle: "Enter your own name", action: "custom" },
  {
    title: "From Template",
    subtitle: "Choose from presets",
    action: "template",
  },
];

export function TicketCreationModal({
  visible,
  onClose,
  onCreateTicket,
  maxTickets,
  currentTicketCount,
}: TicketCreationModalProps) {
  const theme = useTheme();
  const [ticketName, setTicketName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [currentStep, setCurrentStep] = useState<
    "action" | "custom" | "template"
  >("action");
  const [showValidation, setShowValidation] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const styles = createTicketStyles(theme);
  const modalStyles = getTicketModalStyles(theme);

  const canCreateTicket = currentTicketCount < maxTickets;
  const isNameValid = ticketName.trim().length >= 2;

  useEffect(() => {
    if (visible) {
      // Reset form when modal opens
      setTicketName("");
      setIsCreating(false);
      setCurrentStep("action");
      setShowValidation(false);
    }
  }, [visible]);

  const handleCreateTicket = async (name?: string) => {
    const finalName = name || ticketName.trim();

    if (!finalName || finalName.length < 2) {
      setShowValidation(true);
      Alert.alert(
        "Invalid Name",
        "Ticket name must be at least 2 characters long."
      );
      return;
    }

    if (!canCreateTicket) {
      Alert.alert(
        "Ticket Limit Reached",
        `You can only have ${maxTickets} active tickets at a time. Please complete or delete an existing ticket first.`
      );
      return;
    }

    setIsCreating(true);

    try {
      await onCreateTicket(finalName);
      onClose();
    } catch (error) {
      Alert.alert("Error", "Failed to create ticket. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  const handleActionSelect = (action: string) => {
    switch (action) {
      case "quick":
        handleQuickCreate();
        break;
      case "custom":
        setCurrentStep("custom");
        setTimeout(() => inputRef.current?.focus(), 300);
        break;
      case "template":
        setCurrentStep("template");
        break;
    }
  };

  const handleTemplateSelect = (template: {
    name: string;
    icon: string;
    color: string;
  }) => {
    setTicketName(template.name);
    setCurrentStep("custom");
    setTimeout(() => inputRef.current?.focus(), 300);
  };

  const handleClose = () => {
    if (!isCreating) {
      onClose();
    }
  };

  const generateDefaultName = () => {
    const now = new Date();
    const timeString = now.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    return `Ticket ${timeString}`;
  };

  const handleQuickCreate = () => {
    const defaultName = generateDefaultName();
    if (canCreateTicket) {
      handleCreateTicket(defaultName);
    }
  };

  const handleBack = () => {
    if (currentStep === "custom" || currentStep === "template") {
      setCurrentStep("action");
      setTicketName("");
      setShowValidation(false);
    }
  };

  const renderActionStep = () => (
    <View style={styles.form.container}>
      <Text
        style={[
          theme.typography.h2,
          {
            color: theme.colors.text,
            textAlign: "center",
            marginBottom: theme.spacing.xl,
          },
        ]}
      >
        Create New Ticket
      </Text>

      {!canCreateTicket && (
        <View
          style={{
            backgroundColor: theme.colors.error + "10",
            borderColor: theme.colors.error,
            borderWidth: 1,
            borderRadius: theme.borderRadius.md,
            padding: theme.spacing.md,
            marginBottom: theme.spacing.lg,
            flexDirection: "row",
            alignItems: "center",
            gap: theme.spacing.sm,
          }}
        >
          <IconSymbol
            name="exclamationmark.triangle"
            size={20}
            color={theme.colors.error}
          />
          <Text
            style={[
              theme.typography.body,
              { color: theme.colors.error, flex: 1 },
            ]}
          >
            Maximum of {maxTickets} tickets allowed ({currentTicketCount}/
            {maxTickets})
          </Text>
        </View>
      )}

      <View style={{ gap: theme.spacing.md }}>
        {QUICK_ACTIONS.map((action, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.buttons.secondary,
              {
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                padding: theme.spacing.lg,
                opacity:
                  !canCreateTicket && action.action !== "quick" ? 0.5 : 1,
              },
            ]}
            onPress={() => handleActionSelect(action.action)}
            disabled={!canCreateTicket && action.action !== "quick"}
          >
            <View>
              <Text style={[styles.text.title, { color: theme.colors.text }]}>
                {action.title}
              </Text>
              <Text
                style={[
                  styles.text.subtitle,
                  { color: theme.colors.textSecondary },
                ]}
              >
                {action.subtitle}
              </Text>
            </View>
            <IconSymbol
              name="chevron.right"
              size={20}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderCustomStep = () => (
    <View style={styles.form.container}>
      <View style={styles.form.fieldGroup}>
        <Text style={styles.form.label}>Ticket Name</Text>
        <TextInput
          ref={inputRef}
          style={[
            styles.form.input,
            showValidation && !isNameValid ? styles.form.inputError : {},
          ]}
          value={ticketName}
          onChangeText={(text) => {
            setTicketName(text);
            setShowValidation(false);
          }}
          placeholder="Enter ticket name..."
          placeholderTextColor={theme.colors.textSecondary}
          maxLength={50}
          editable={!isCreating}
          autoCapitalize="words"
          returnKeyType="done"
          onSubmitEditing={() => handleCreateTicket()}
        />
        {showValidation && !isNameValid && (
          <Text style={styles.form.errorText}>
            Ticket name must be at least 2 characters long
          </Text>
        )}
        <Text style={styles.form.helpText}>
          {ticketName.length}/50 characters
        </Text>
      </View>
    </View>
  );

  const renderTemplateStep = () => (
    <View style={styles.form.container}>
      <Text style={[styles.form.label, { marginBottom: theme.spacing.lg }]}>
        Choose Template
      </Text>
      <View style={{ gap: theme.spacing.md }}>
        {TICKET_NAME_TEMPLATES.map((template, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.buttons.secondary,
              {
                flexDirection: "row",
                alignItems: "center",
                padding: theme.spacing.lg,
                gap: theme.spacing.md,
              },
            ]}
            onPress={() => handleTemplateSelect(template)}
            disabled={isCreating}
          >
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: theme.borderRadius.round,
                backgroundColor:
                  theme.colors[template.color as keyof typeof theme.colors] +
                  "20",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <IconSymbol
                name={template.icon}
                size={20}
                color={
                  theme.colors[template.color as keyof typeof theme.colors]
                }
              />
            </View>
            <Text
              style={[styles.text.title, { color: theme.colors.text, flex: 1 }]}
            >
              {template.name}
            </Text>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={modalStyles.overlay}>
        <View style={modalStyles.container}>
          {/* Header */}
          <View style={modalStyles.header}>
            {currentStep !== "action" && (
              <TouchableOpacity
                onPress={handleBack}
                disabled={isCreating}
                style={{ padding: theme.spacing.sm }}
              >
                <IconSymbol
                  name="chevron.left"
                  size={24}
                  color={theme.colors.text}
                />
              </TouchableOpacity>
            )}
            <View style={{ flex: 1 }} />
            <TouchableOpacity
              onPress={handleClose}
              disabled={isCreating}
              style={{
                padding: theme.spacing.sm,
                opacity: isCreating ? 0.5 : 1,
              }}
            >
              <IconSymbol
                name="xmark"
                size={24}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={modalStyles.content}>
            {currentStep === "action" && renderActionStep()}
            {currentStep === "custom" && renderCustomStep()}
            {currentStep === "template" && renderTemplateStep()}
          </View>

          {/* Footer */}
          {currentStep === "custom" && (
            <View style={modalStyles.footer}>
              <ModernButton
                title="Cancel"
                onPress={handleBack}
                variant="outline"
                style={{ flex: 1 }}
                disabled={isCreating}
              />
              <ModernButton
                title={isCreating ? "Creating..." : "Create Ticket"}
                onPress={() => handleCreateTicket()}
                style={{ flex: 1 }}
                disabled={!isNameValid || !canCreateTicket || isCreating}
                loading={isCreating}
              />
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}
