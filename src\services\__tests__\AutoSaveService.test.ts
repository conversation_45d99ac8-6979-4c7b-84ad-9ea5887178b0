/**
 * AutoSaveService Tests
 * 
 * Comprehensive tests for the fixed auto-save system including:
 * - Local ticket handling
 * - Existing ticket updates
 * - Offline queue processing
 * - Error recovery scenarios
 * - Retry logic with exponential backoff
 */

import { AutoSaveService, AutoSaveErrorType } from '../AutoSaveService';
import { store } from '@/src/store';
import { createTicket } from '@/src/store/slices/ticketSlice';
import { saveTicket, autoSaveTicket } from '@/src/store/thunks/ticketThunks';

// Mock the store and thunks
jest.mock('@/src/store', () => ({
  store: {
    getState: jest.fn(),
    dispatch: jest.fn(),
  },
}));

jest.mock('@/src/store/thunks/ticketThunks', () => ({
  saveTicket: jest.fn(),
  autoSaveTicket: jest.fn(),
  batchAutoSaveTickets: jest.fn(),
}));

describe('AutoSaveService', () => {
  let autoSaveService: AutoSaveService;
  let mockStore: any;
  let mockDispatch: jest.Mock;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup mock store
    mockDispatch = jest.fn();
    mockStore = {
      getState: jest.fn(),
      dispatch: mockDispatch,
    };
    (store as any) = mockStore;

    // Create service instance
    autoSaveService = new AutoSaveService({
      debounceMs: 100,
      maxRetries: 2,
      retryDelayMs: 50,
      exponentialBackoffMultiplier: 2,
      maxRetryDelayMs: 1000,
    });
  });

  describe('Local Ticket Handling', () => {
    it('should use saveTicket for local tickets', async () => {
      const localTicket = {
        id: 'ticket_123',
        name: 'Test Ticket',
        staffId: 'staff_1',
        isLocal: true,
        items: [],
        subtotal: 0,
        tax: 0,
        total: 0,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockStore.getState.mockReturnValue({
        tickets: {
          tickets: [localTicket],
        },
      });

      mockDispatch.mockResolvedValue({
        unwrap: () => Promise.resolve(localTicket),
      });

      // Schedule auto-save for local ticket
      autoSaveService.scheduleAutoSave('ticket_123', true);

      // Wait for debounce
      await new Promise(resolve => setTimeout(resolve, 150));

      // Verify saveTicket was called for local ticket
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: expect.stringContaining('saveTicket'),
        })
      );
    });

    it('should use autoSaveTicket for existing tickets', async () => {
      const existingTicket = {
        id: 'ticket_456',
        name: 'Existing Ticket',
        staffId: 'staff_1',
        isLocal: false,
        items: [],
        subtotal: 0,
        tax: 0,
        total: 0,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockStore.getState.mockReturnValue({
        tickets: {
          tickets: [existingTicket],
        },
      });

      mockDispatch.mockResolvedValue({
        unwrap: () => Promise.resolve({ ticketId: 'ticket_456', timestamp: new Date().toISOString() }),
      });

      // Schedule auto-save for existing ticket
      autoSaveService.scheduleAutoSave('ticket_456', true);

      // Wait for debounce
      await new Promise(resolve => setTimeout(resolve, 150));

      // Verify autoSaveTicket was called for existing ticket
      expect(mockDispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: expect.stringContaining('autoSaveTicket'),
        })
      );
    });
  });

  describe('Error Handling and Retry Logic', () => {
    it('should categorize errors correctly', () => {
      const service = autoSaveService as any;

      expect(service.categorizeError('Ticket not found')).toBe(AutoSaveErrorType.TICKET_NOT_FOUND);
      expect(service.categorizeError('Authentication required')).toBe(AutoSaveErrorType.AUTHENTICATION_ERROR);
      expect(service.categorizeError('Permission denied')).toBe(AutoSaveErrorType.PERMISSION_ERROR);
      expect(service.categorizeError('Validation failed')).toBe(AutoSaveErrorType.VALIDATION_ERROR);
      expect(service.categorizeError('Network error')).toBe(AutoSaveErrorType.NETWORK_ERROR);
      expect(service.categorizeError('Unknown issue')).toBe(AutoSaveErrorType.UNKNOWN_ERROR);
    });

    it('should calculate retry delay with exponential backoff', () => {
      const service = autoSaveService as any;

      expect(service.calculateRetryDelay(0)).toBe(50);  // Base delay
      expect(service.calculateRetryDelay(1)).toBe(100); // 50 * 2^1
      expect(service.calculateRetryDelay(2)).toBe(200); // 50 * 2^2
      expect(service.calculateRetryDelay(10)).toBe(1000); // Capped at maxRetryDelayMs
    });
  });

  describe('Ticket Validation', () => {
    it('should validate ticket state correctly', () => {
      const service = autoSaveService as any;

      const validTicket = {
        id: 'ticket_123',
        staffId: 'staff_1',
        name: 'Valid Ticket',
        status: 'active',
        subtotal: 10.50,
        tax: 1.05,
        total: 11.55,
        items: [
          {
            id: 'item_1',
            variantId: 'variant_1',
            title: 'Test Item',
            quantity: 2,
            price: 5.25,
          },
        ],
        discounts: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const result = service.validateTicketState(validTicket);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid ticket state', () => {
      const service = autoSaveService as any;

      const invalidTicket = {
        // Missing required fields
        name: '',
        status: 'invalid_status',
        subtotal: -10,
        items: [
          {
            // Missing required item fields
            quantity: 0,
            price: -5,
          },
        ],
      };

      const result = service.validateTicketState(invalidTicket);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Statistics Tracking', () => {
    it('should track save statistics correctly', () => {
      const service = autoSaveService as any;

      // Track successful save
      service.updateSaveStats(true, 100, 1);
      
      let stats = autoSaveService.getStats();
      expect(stats.totalSaves).toBe(1);
      expect(stats.successfulSaves).toBe(1);
      expect(stats.failedSaves).toBe(0);

      // Track failed save with error type
      service.updateSaveStats(false, 200, 1, AutoSaveErrorType.NETWORK_ERROR, 'ticket_123', 1);
      
      stats = autoSaveService.getStats();
      expect(stats.totalSaves).toBe(2);
      expect(stats.successfulSaves).toBe(1);
      expect(stats.failedSaves).toBe(1);
      expect(stats.errorsByType[AutoSaveErrorType.NETWORK_ERROR]).toBe(1);
      expect(stats.lastError?.ticketId).toBe('ticket_123');
    });
  });
});
