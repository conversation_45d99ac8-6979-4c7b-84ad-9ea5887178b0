import { useSharedValue, useAnimatedStyle, withTiming, withSpring, withSequence, Easing } from 'react-native-reanimated';
import { useEffect, useCallback } from 'react';

// Animation timing constants
const TIMING = {
  fast: 150,
  normal: 300,
  slow: 500,
  verySlow: 800,
};

// Easing functions
const EASING = {
  easeOut: Easing.out(Easing.quad),
  easeIn: Easing.in(Easing.quad),
  easeInOut: Easing.inOut(Easing.quad),
  spring: Easing.out(Easing.back(1.2)),
};

// Fade animation hook
export const useFadeAnimation = (initialValue = 0, autoStart = true) => {
  const opacity = useSharedValue(initialValue);

  const fadeIn = useCallback((duration = TIMING.normal) => {
    opacity.value = withTiming(1, { duration, easing: EASING.easeOut });
  }, [opacity]);

  const fadeOut = useCallback((duration = TIMING.normal) => {
    opacity.value = withTiming(0, { duration, easing: EASING.easeOut });
  }, [opacity]);

  const fadeToggle = useCallback((duration = TIMING.normal) => {
    opacity.value = withTiming(opacity.value === 0 ? 1 : 0, { duration, easing: EASING.easeOut });
  }, [opacity]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  useEffect(() => {
    if (autoStart && initialValue === 0) {
      fadeIn();
    }
  }, [autoStart, initialValue, fadeIn]);

  return {
    opacity,
    fadeIn,
    fadeOut,
    fadeToggle,
    animatedStyle,
  };
};

// Scale animation hook
export const useScaleAnimation = (initialScale = 1, autoStart = false) => {
  const scale = useSharedValue(initialScale);

  const scaleIn = useCallback((targetScale = 1, duration = TIMING.normal) => {
    scale.value = withTiming(targetScale, { duration, easing: EASING.spring });
  }, [scale]);

  const scaleOut = useCallback((targetScale = 0, duration = TIMING.normal) => {
    scale.value = withTiming(targetScale, { duration, easing: EASING.easeOut });
  }, [scale]);

  const scaleBounce = useCallback((targetScale = 1, duration = TIMING.fast) => {
    scale.value = withSequence(
      withTiming(targetScale * 1.1, { duration: duration / 2, easing: EASING.easeOut }),
      withTiming(targetScale, { duration: duration / 2, easing: EASING.easeOut })
    );
  }, [scale]);

  const scalePress = useCallback(() => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 50 }),
      withSpring(1, { damping: 15, stiffness: 300 })
    );
  }, [scale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  useEffect(() => {
    if (autoStart && initialScale !== 1) {
      scaleIn();
    }
  }, [autoStart, initialScale, scaleIn]);

  return {
    scale,
    scaleIn,
    scaleOut,
    scaleBounce,
    scalePress,
    animatedStyle,
  };
};

// Slide animation hook
export const useSlideAnimation = (
  direction: 'left' | 'right' | 'up' | 'down' = 'up',
  distance = 20,
  autoStart = true
) => {
  const translateX = useSharedValue(direction === 'left' ? -distance : direction === 'right' ? distance : 0);
  const translateY = useSharedValue(direction === 'up' ? distance : direction === 'down' ? -distance : 0);

  const slideIn = useCallback((duration = TIMING.normal) => {
    translateX.value = withTiming(0, { duration, easing: EASING.easeOut });
    translateY.value = withTiming(0, { duration, easing: EASING.easeOut });
  }, [translateX, translateY]);

  const slideOut = useCallback((duration = TIMING.normal) => {
    const targetX = direction === 'left' ? -distance : direction === 'right' ? distance : 0;
    const targetY = direction === 'up' ? distance : direction === 'down' ? -distance : 0;
    
    translateX.value = withTiming(targetX, { duration, easing: EASING.easeIn });
    translateY.value = withTiming(targetY, { duration, easing: EASING.easeIn });
  }, [translateX, translateY, direction, distance]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  useEffect(() => {
    if (autoStart) {
      slideIn();
    }
  }, [autoStart, slideIn]);

  return {
    translateX,
    translateY,
    slideIn,
    slideOut,
    animatedStyle,
  };
};

// Combined entrance animation hook
export const useEntranceAnimation = (
  type: 'fadeIn' | 'slideIn' | 'scaleIn' | 'fadeSlide' = 'fadeIn',
  delay = 0,
  autoStart = true
) => {
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const translateY = useSharedValue(20);

  const animate = useCallback((duration = TIMING.normal) => {
    const config = { duration, easing: EASING.easeOut };

    switch (type) {
      case 'fadeIn':
        opacity.value = withTiming(1, config);
        break;
      case 'slideIn':
        opacity.value = withTiming(1, config);
        translateY.value = withTiming(0, config);
        break;
      case 'scaleIn':
        opacity.value = withTiming(1, config);
        scale.value = withTiming(1, { ...config, easing: EASING.spring });
        break;
      case 'fadeSlide':
        opacity.value = withTiming(1, config);
        translateY.value = withTiming(0, config);
        scale.value = withTiming(1, config);
        break;
    }
  }, [opacity, scale, translateY, type]);

  const animatedStyle = useAnimatedStyle(() => {
    const baseStyle: any = { opacity: opacity.value };

    if (type === 'slideIn' || type === 'fadeSlide') {
      baseStyle.transform = [{ translateY: translateY.value }];
    }

    if (type === 'scaleIn' || type === 'fadeSlide') {
      baseStyle.transform = baseStyle.transform || [];
      baseStyle.transform.push({ scale: scale.value });
    }

    return baseStyle;
  });

  useEffect(() => {
    if (autoStart) {
      const timer = setTimeout(() => {
        animate();
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [autoStart, delay, animate]);

  return {
    opacity,
    scale,
    translateY,
    animate,
    animatedStyle,
  };
};

// Loading animation hook
export const useLoadingAnimation = () => {
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);

  const startLoading = useCallback(() => {
    rotation.value = withSequence(
      withTiming(360, { duration: 1000, easing: Easing.linear }),
      withTiming(720, { duration: 1000, easing: Easing.linear }),
      withTiming(1080, { duration: 1000, easing: Easing.linear })
    );
    
    scale.value = withSequence(
      withTiming(1.1, { duration: 500 }),
      withTiming(1, { duration: 500 })
    );
  }, [rotation, scale]);

  const stopLoading = useCallback(() => {
    rotation.value = withTiming(0, { duration: 300 });
    scale.value = withTiming(1, { duration: 300 });
  }, [rotation, scale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${rotation.value}deg` },
      { scale: scale.value },
    ],
  }));

  return {
    startLoading,
    stopLoading,
    animatedStyle,
  };
};

// Button press animation hook
export const useButtonPressAnimation = () => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const pressIn = useCallback(() => {
    scale.value = withTiming(0.95, { duration: 100 });
    opacity.value = withTiming(0.8, { duration: 100 });
  }, [scale, opacity]);

  const pressOut = useCallback(() => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 });
    opacity.value = withTiming(1, { duration: 150 });
  }, [scale, opacity]);

  const press = useCallback(() => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 50 }),
      withSpring(1, { damping: 15, stiffness: 300 })
    );
  }, [scale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return {
    pressIn,
    pressOut,
    press,
    animatedStyle,
  };
};

// Card hover animation hook (for touch interactions)
export const useCardHoverAnimation = () => {
  const scale = useSharedValue(1);
  const elevation = useSharedValue(2);

  const hoverIn = useCallback(() => {
    scale.value = withTiming(1.02, { duration: 200, easing: EASING.easeOut });
    elevation.value = withTiming(8, { duration: 200 });
  }, [scale, elevation]);

  const hoverOut = useCallback(() => {
    scale.value = withTiming(1, { duration: 200, easing: EASING.easeOut });
    elevation.value = withTiming(2, { duration: 200 });
  }, [scale, elevation]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    elevation: elevation.value,
  }));

  return {
    hoverIn,
    hoverOut,
    animatedStyle,
  };
};

// Stagger animation hook
export const useStaggerAnimation = (itemCount: number, staggerDelay = 100) => {
  const animations = Array.from({ length: itemCount }, () => ({
    opacity: useSharedValue(0),
    translateY: useSharedValue(20),
  }));

  const startStagger = useCallback(() => {
    animations.forEach((animation, index) => {
      const delay = index * staggerDelay;
      
      setTimeout(() => {
        animation.opacity.value = withTiming(1, { duration: TIMING.normal, easing: EASING.easeOut });
        animation.translateY.value = withTiming(0, { duration: TIMING.normal, easing: EASING.easeOut });
      }, delay);
    });
  }, [animations, staggerDelay]);

  const getAnimatedStyle = useCallback((index: number) => {
    return useAnimatedStyle(() => ({
      opacity: animations[index]?.opacity.value || 0,
      transform: [{ translateY: animations[index]?.translateY.value || 20 }],
    }));
  }, [animations]);

  return {
    startStagger,
    getAnimatedStyle,
  };
};
