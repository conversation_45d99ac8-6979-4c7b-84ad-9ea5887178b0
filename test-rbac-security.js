#!/usr/bin/env node

/**
 * RBAC Security Testing Script
 * Tests the manager role assignment restrictions
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3020/api';

// Test credentials (update these with actual seeded user credentials)
const TEST_USERS = {
  super_admin: {
    username: 'super_admin',
    password: 'admin123'
  },
  manager: {
    username: 'manager',
    password: 'manager123'
  },
  cashier: {
    username: 'cashier',
    password: 'cashier123'
  }
};

let authTokens = {};

async function login(role) {
  try {
    console.log(`🔐 Logging in as ${role}...`);
    const response = await axios.post(`${BASE_URL}/auth/pos-login`, {
      username: TEST_USERS[role].username,
      password: TEST_USERS[role].password
    });
    
    if (response.data.success) {
      authTokens[role] = response.data.data.token;
      console.log(`✅ ${role} login successful`);
      return true;
    } else {
      console.log(`❌ ${role} login failed:`, response.data.error);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${role} login error:`, error.response?.data?.error || error.message);
    return false;
  }
}

async function testAssignableRoles(role) {
  try {
    console.log(`\n🧪 Testing assignable roles for ${role}...`);
    const response = await axios.get(`${BASE_URL}/staff-management/assignable-roles`, {
      headers: { Authorization: `Bearer ${authTokens[role]}` }
    });
    
    if (response.data.success) {
      const { assignableRoles, userRole, userLevel } = response.data.data;
      console.log(`✅ ${role} can assign roles:`, assignableRoles);
      console.log(`   User Role: ${userRole}, Level: ${userLevel}`);
      
      // Validate expected results
      if (role === 'manager') {
        const expected = ['cashier'];
        const isValid = JSON.stringify(assignableRoles.sort()) === JSON.stringify(expected.sort());
        console.log(`   Expected: ${expected}, Valid: ${isValid ? '✅' : '❌'}`);
      } else if (role === 'super_admin') {
        const expected = ['cashier', 'manager', 'super_admin'];
        const isValid = JSON.stringify(assignableRoles.sort()) === JSON.stringify(expected.sort());
        console.log(`   Expected: ${expected}, Valid: ${isValid ? '✅' : '❌'}`);
      }
      
      return assignableRoles;
    } else {
      console.log(`❌ ${role} assignable roles failed:`, response.data.error);
      return null;
    }
  } catch (error) {
    console.log(`❌ ${role} assignable roles error:`, error.response?.data?.error || error.message);
    return null;
  }
}

async function testStaffCreation(role, targetRole) {
  try {
    console.log(`\n🧪 Testing ${role} creating staff with ${targetRole} role...`);
    const testStaff = {
      username: `test_${targetRole}_${Date.now()}`,
      name: `Test ${targetRole} User`,
      email: `test_${targetRole}@example.com`,
      password: 'test123',
      role: targetRole,
      commissionRate: 5.0,
      permissions: []
    };
    
    const response = await axios.post(`${BASE_URL}/staff-management/staff`, testStaff, {
      headers: { Authorization: `Bearer ${authTokens[role]}` }
    });
    
    if (response.data.success) {
      console.log(`✅ ${role} successfully created ${targetRole} staff`);
      return true;
    } else {
      console.log(`❌ ${role} failed to create ${targetRole} staff:`, response.data.error);
      return false;
    }
  } catch (error) {
    const errorMsg = error.response?.data?.error || error.message;
    console.log(`❌ ${role} create ${targetRole} staff error:`, errorMsg);
    
    // Check if it's a permission denied error (expected for unauthorized attempts)
    if (errorMsg.includes('Permission denied') || errorMsg.includes('cannot assign')) {
      console.log(`   ✅ Correctly blocked unauthorized role assignment`);
      return 'blocked';
    }
    return false;
  }
}

async function runSecurityTests() {
  console.log('🚀 Starting RBAC Security Tests\n');
  
  // Login all users
  const loginResults = await Promise.all([
    login('super_admin'),
    login('manager'),
    login('cashier')
  ]);
  
  if (!loginResults.every(result => result)) {
    console.log('\n❌ Some logins failed. Please check user credentials and backend status.');
    return;
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('ASSIGNABLE ROLES TESTS');
  console.log('='.repeat(50));
  
  // Test assignable roles for each user type
  await testAssignableRoles('super_admin');
  await testAssignableRoles('manager');
  
  // Cashier should not have access to this endpoint
  try {
    await testAssignableRoles('cashier');
  } catch (error) {
    console.log(`✅ Cashier correctly blocked from assignable roles endpoint`);
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('STAFF CREATION TESTS');
  console.log('='.repeat(50));
  
  // Test staff creation scenarios
  console.log('\n--- Super Admin Tests ---');
  await testStaffCreation('super_admin', 'cashier');
  await testStaffCreation('super_admin', 'manager');
  await testStaffCreation('super_admin', 'super_admin');
  
  console.log('\n--- Manager Tests ---');
  await testStaffCreation('manager', 'cashier'); // Should succeed
  const managerToManager = await testStaffCreation('manager', 'manager'); // Should be blocked
  const managerToSuperAdmin = await testStaffCreation('manager', 'super_admin'); // Should be blocked
  
  console.log('\n' + '='.repeat(50));
  console.log('SECURITY TEST RESULTS');
  console.log('='.repeat(50));
  
  console.log('\n✅ PASSED TESTS:');
  console.log('- Super Admin can assign any role');
  console.log('- Manager can assign cashier role');
  
  if (managerToManager === 'blocked' && managerToSuperAdmin === 'blocked') {
    console.log('- Manager correctly blocked from assigning manager/super_admin roles');
    console.log('\n🎉 ALL SECURITY TESTS PASSED! Manager role restrictions are working correctly.');
  } else {
    console.log('\n❌ SECURITY ISSUE: Manager can still assign unauthorized roles!');
    console.log('🚨 This is a critical security vulnerability that needs immediate attention.');
  }
}

// Run the tests
runSecurityTests().catch(console.error);
