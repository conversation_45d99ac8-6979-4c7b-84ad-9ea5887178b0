import React, { useEffect } from 'react';
import { View, ViewProps } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
  interpolate,
  Easing,
  Layout,
  FadeIn,
  FadeOut,
  SlideInLeft,
  SlideInRight,
  SlideInUp,
  SlideInDown,
  SlideOutLeft,
  SlideOutRight,
  SlideOutUp,
  SlideOutDown,
  ZoomIn,
  ZoomOut,
} from 'react-native-reanimated';

// Screen transition wrapper
interface ScreenTransitionProps extends ViewProps {
  children: React.ReactNode;
  transitionType?: 'fade' | 'slide' | 'scale' | 'none';
  direction?: 'left' | 'right' | 'up' | 'down';
  duration?: number;
}

export const ScreenTransition: React.FC<ScreenTransitionProps> = ({
  children,
  transitionType = 'fade',
  direction = 'right',
  duration = 300,
  style,
  ...props
}) => {
  const getEnteringAnimation = () => {
    switch (transitionType) {
      case 'slide':
        switch (direction) {
          case 'left':
            return SlideInLeft.duration(duration);
          case 'right':
            return SlideInRight.duration(duration);
          case 'up':
            return SlideInUp.duration(duration);
          case 'down':
            return SlideInDown.duration(duration);
          default:
            return SlideInRight.duration(duration);
        }
      case 'scale':
        return ZoomIn.duration(duration);
      case 'fade':
        return FadeIn.duration(duration);
      default:
        return undefined;
    }
  };

  const getExitingAnimation = () => {
    switch (transitionType) {
      case 'slide':
        switch (direction) {
          case 'left':
            return SlideOutRight.duration(duration);
          case 'right':
            return SlideOutLeft.duration(duration);
          case 'up':
            return SlideOutDown.duration(duration);
          case 'down':
            return SlideOutUp.duration(duration);
          default:
            return SlideOutLeft.duration(duration);
        }
      case 'scale':
        return ZoomOut.duration(duration);
      case 'fade':
        return FadeOut.duration(duration);
      default:
        return undefined;
    }
  };

  if (transitionType === 'none') {
    return (
      <View style={style} {...props}>
        {children}
      </View>
    );
  }

  return (
    <Animated.View
      entering={getEnteringAnimation()}
      exiting={getExitingAnimation()}
      layout={Layout.springify()}
      style={style}
      {...props}
    >
      {children}
    </Animated.View>
  );
};

// Modal transition component
interface ModalTransitionProps extends ViewProps {
  visible: boolean;
  children: React.ReactNode;
  animationType?: 'slide' | 'fade' | 'scale';
  onAnimationComplete?: () => void;
}

export const ModalTransition: React.FC<ModalTransitionProps> = ({
  visible,
  children,
  animationType = 'scale',
  onAnimationComplete,
  style,
  ...props
}) => {
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const translateY = useSharedValue(50);

  useEffect(() => {
    if (visible) {
      const config = { duration: 300, easing: Easing.out(Easing.cubic) };
      
      switch (animationType) {
        case 'fade':
          opacity.value = withTiming(1, config, onAnimationComplete);
          break;
        case 'scale':
          opacity.value = withTiming(1, config);
          scale.value = withSpring(1, { damping: 15, stiffness: 300 }, onAnimationComplete);
          break;
        case 'slide':
          opacity.value = withTiming(1, config);
          translateY.value = withTiming(0, config, onAnimationComplete);
          break;
      }
    } else {
      const config = { duration: 200, easing: Easing.in(Easing.cubic) };
      
      switch (animationType) {
        case 'fade':
          opacity.value = withTiming(0, config, onAnimationComplete);
          break;
        case 'scale':
          opacity.value = withTiming(0, config);
          scale.value = withTiming(0.8, config, onAnimationComplete);
          break;
        case 'slide':
          opacity.value = withTiming(0, config);
          translateY.value = withTiming(50, config, onAnimationComplete);
          break;
      }
    }
  }, [visible, animationType, onAnimationComplete]);

  const animatedStyle = useAnimatedStyle(() => {
    const baseStyle: any = { opacity: opacity.value };

    if (animationType === 'scale') {
      baseStyle.transform = [{ scale: scale.value }];
    } else if (animationType === 'slide') {
      baseStyle.transform = [{ translateY: translateY.value }];
    }

    return baseStyle;
  });

  if (!visible && opacity.value === 0) {
    return null;
  }

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

// List item animation component
interface ListItemTransitionProps extends ViewProps {
  index: number;
  staggerDelay?: number;
  animationType?: 'fade' | 'slide' | 'scale';
  children: React.ReactNode;
}

export const ListItemTransition: React.FC<ListItemTransitionProps> = ({
  index,
  staggerDelay = 50,
  animationType = 'slide',
  children,
  style,
  ...props
}) => {
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(20);
  const scale = useSharedValue(0.9);

  useEffect(() => {
    const delay = index * staggerDelay;
    const config = { duration: 300, easing: Easing.out(Easing.cubic) };

    setTimeout(() => {
      opacity.value = withTiming(1, config);
      
      if (animationType === 'slide' || animationType === 'scale') {
        translateY.value = withTiming(0, config);
      }
      
      if (animationType === 'scale') {
        scale.value = withTiming(1, { ...config, easing: Easing.out(Easing.back(1.2)) });
      }
    }, delay);
  }, [index, staggerDelay, animationType]);

  const animatedStyle = useAnimatedStyle(() => {
    const baseStyle: any = { opacity: opacity.value };

    if (animationType === 'slide') {
      baseStyle.transform = [{ translateY: translateY.value }];
    } else if (animationType === 'scale') {
      baseStyle.transform = [
        { translateY: translateY.value },
        { scale: scale.value },
      ];
    }

    return baseStyle;
  });

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

// Tab transition component
interface TabTransitionProps extends ViewProps {
  activeIndex: number;
  index: number;
  children: React.ReactNode;
}

export const TabTransition: React.FC<TabTransitionProps> = ({
  activeIndex,
  index,
  children,
  style,
  ...props
}) => {
  const opacity = useSharedValue(activeIndex === index ? 1 : 0);
  const translateX = useSharedValue(activeIndex === index ? 0 : 20);

  useEffect(() => {
    const isActive = activeIndex === index;
    const config = { duration: 250, easing: Easing.out(Easing.cubic) };

    opacity.value = withTiming(isActive ? 1 : 0, config);
    translateX.value = withTiming(isActive ? 0 : 20, config);
  }, [activeIndex, index]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ translateX: translateX.value }],
  }));

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

// Progress animation component
interface ProgressTransitionProps extends ViewProps {
  progress: number;
  duration?: number;
  children?: React.ReactNode;
}

export const ProgressTransition: React.FC<ProgressTransitionProps> = ({
  progress,
  duration = 500,
  children,
  style,
  ...props
}) => {
  const animatedProgress = useSharedValue(0);

  useEffect(() => {
    animatedProgress.value = withTiming(progress, {
      duration,
      easing: Easing.out(Easing.cubic),
    });
  }, [progress, duration]);

  const animatedStyle = useAnimatedStyle(() => {
    const width = interpolate(animatedProgress.value, [0, 1], [0, 100]);
    
    return {
      width: `${width}%`,
    };
  });

  return (
    <View style={style} {...props}>
      <Animated.View style={[{ height: '100%', backgroundColor: 'currentColor' }, animatedStyle]} />
      {children}
    </View>
  );
};

// Notification slide-in component
interface NotificationTransitionProps extends ViewProps {
  visible: boolean;
  position?: 'top' | 'bottom';
  children: React.ReactNode;
}

export const NotificationTransition: React.FC<NotificationTransitionProps> = ({
  visible,
  position = 'top',
  children,
  style,
  ...props
}) => {
  const translateY = useSharedValue(position === 'top' ? -100 : 100);
  const opacity = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      const config = { duration: 300, easing: Easing.out(Easing.back(1.2)) };
      translateY.value = withTiming(0, config);
      opacity.value = withTiming(1, { duration: 200 });
    } else {
      const config = { duration: 200, easing: Easing.in(Easing.cubic) };
      translateY.value = withTiming(position === 'top' ? -100 : 100, config);
      opacity.value = withTiming(0, config);
    }
  }, [visible, position]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};
