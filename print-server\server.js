/**
 * T220MD Print Server for Web-to-USB Printing
 * This Node.js server enables web browsers to print to USB-connected T220MD printers
 *
 * Installation:
 * 1. cd print-server
 * 2. npm install
 * 3. Connect T220MD printer via USB
 * 4. npm start
 * 5. Configure web app to use: http://localhost:3001
 */

const express = require("express");
const cors = require("cors");
const {
  ThermalPrinter,
  PrinterTypes,
  CharacterSet,
  BreakLine,
} = require("node-thermal-printer");
const { SerialPort } = require("serialport");

const app = express();
const PORT = 3001;

// Enable CORS for web app
app.use(
  cors({
    origin: [
      "http://localhost:8082",
      "https://shopify.dukalink.com",
      "https://treasuredposdev.dukalink.com",
    ],
    credentials: true,
  })
);

app.use(express.json({ limit: "10mb" }));
app.use(express.raw({ type: "application/octet-stream", limit: "10mb" }));

// T220MD Printer configuration
let printer = null;
let printerPort = null;

/**
 * Initialize T220MD printer connection
 */
async function initializePrinter() {
  try {
    // List available serial ports
    const ports = await SerialPort.list();
    console.log("Available serial ports:");
    ports.forEach((port) => {
      console.log(`  ${port.path} - ${port.manufacturer || "Unknown"}`);
    });

    // Find T220MD or thermal printer port (adjust as needed)
    const thermalPorts = ports.filter(
      (port) =>
        port.manufacturer?.toLowerCase().includes("prolific") ||
        port.manufacturer?.toLowerCase().includes("ftdi") ||
        port.manufacturer?.toLowerCase().includes("ch340") ||
        port.path.includes("USB") ||
        port.path.includes("ttyUSB") ||
        port.path.includes("COM")
    );

    if (thermalPorts.length === 0) {
      console.log("No thermal printer ports found. Using network fallback.");
      return false;
    }

    const selectedPort = thermalPorts[0].path;
    console.log(`Attempting to connect to T220MD on port: ${selectedPort}`);

    // Initialize thermal printer
    printer = new ThermalPrinter({
      type: PrinterTypes.EPSON, // T220MD is ESC/POS compatible
      interface: `serial:${selectedPort}`,
      characterSet: CharacterSet.PC858_MULTILINGUAL,
      width: 48, // 80mm paper = ~48 characters
      options: {
        timeout: 5000,
        baudRate: 9600, // Standard for T220MD
        dataBits: 8,
        parity: "none",
        stopBits: 1,
        flowControl: false,
      },
    });

    // Test connection
    const isConnected = await printer.isPrinterConnected();
    if (isConnected) {
      console.log("✅ T220MD printer connected successfully!");
      return true;
    } else {
      console.log("❌ Failed to connect to T220MD printer");
      return false;
    }
  } catch (error) {
    console.error("Error initializing T220MD printer:", error);
    return false;
  }
}

/**
 * Print receipt to T220MD
 */
app.post("/print", async (req, res) => {
  try {
    console.log("Received print request for T220MD");

    if (!printer) {
      const initialized = await initializePrinter();
      if (!initialized) {
        return res.status(500).json({
          success: false,
          error: "T220MD printer not available",
        });
      }
    }

    // Handle different request formats
    let receiptData;
    if (req.body.commands) {
      // Raw ESC/POS commands
      const commands = new Uint8Array(req.body.commands);
      await printer.raw(commands);
    } else if (req.body.receiptData) {
      // Structured receipt data
      receiptData = req.body.receiptData;
      await printStructuredReceipt(receiptData);
    } else {
      // Assume it's receipt data
      receiptData = req.body;
      await printStructuredReceipt(receiptData);
    }

    // Execute print
    await printer.execute();
    console.log("✅ T220MD print completed successfully");

    res.json({
      success: true,
      message: "Printed to T220MD successfully",
      printer: "T220MD",
      method: "USB",
    });
  } catch (error) {
    console.error("T220MD print error:", error);
    res.status(500).json({
      success: false,
      error: error.message || "T220MD print failed",
    });
  }
});

/**
 * Print structured receipt data to T220MD
 */
async function printStructuredReceipt(receiptData) {
  if (!printer) throw new Error("Printer not initialized");

  // Clear any previous content
  printer.clear();

  // Store header (centered, bold, large)
  printer.alignCenter();
  printer.setTextSize(1, 1); // Double size
  printer.bold(true);
  printer.println(receiptData.store?.name || "STORE NAME");
  printer.bold(false);
  printer.setTextNormal();

  // Store details
  if (receiptData.store?.address) {
    printer.println(receiptData.store.address);
  }
  if (receiptData.store?.phone) {
    printer.println(receiptData.store.phone);
  }

  // Separator
  printer.drawLine();

  // Receipt header
  printer.alignLeft();
  printer.bold(true);
  printer.println("SALES RECEIPT");
  printer.bold(false);

  // Receipt details
  printer.println(`Receipt: ${receiptData.orderNumber || "N/A"}`);

  const date = new Date(receiptData.orderDate || Date.now());
  printer.println(
    `Date: ${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
  );

  if (receiptData.customer?.name) {
    printer.println(`Customer: ${receiptData.customer.name}`);
  }

  if (receiptData.salesAgent?.name) {
    printer.println(`Sales Agent: ${receiptData.salesAgent.name}`);
  }

  printer.println("".padEnd(48, "-"));

  // Items
  if (receiptData.items && receiptData.items.length > 0) {
    receiptData.items.forEach((item) => {
      printer.println(item.title || "Unknown Item");

      if (item.variantTitle && item.variantTitle !== "Default Title") {
        printer.println(`  ${item.variantTitle}`);
      }

      const qty = (item.quantity || 1).toString();
      const price = `KSh ${parseFloat(item.price || 0).toFixed(2)}`;
      const total = `KSh ${(
        parseFloat(item.price || 0) * (item.quantity || 1)
      ).toFixed(2)}`;

      const line = `${qty} x ${price}`.padEnd(30) + total.padStart(18);
      printer.println(line);
    });
  }

  // Total section
  printer.drawLine();
  printer.bold(true);
  printer.setTextSize(0, 1); // Double height
  printer.println(`TOTAL: KSh ${(receiptData.total || 0).toFixed(2)}`);
  printer.setTextNormal();
  printer.bold(false);

  // Payment method
  printer.println(`Payment: ${receiptData.paymentMethod || "Cash"}`);

  printer.println("".padEnd(48, "-"));

  // Footer
  printer.alignCenter();
  printer.println("Thank you for your business!");
  printer.println("Powered by Dukalink POS");

  // Feed and cut paper
  printer.newLine();
  printer.newLine();
  printer.cut();

  // Open cash drawer if configured
  if (receiptData.openDrawer) {
    printer.openCashDrawer();
  }
}

/**
 * Get printer status
 */
app.get("/status", async (req, res) => {
  try {
    let status = {
      connected: false,
      printer: "T220MD",
      port: null,
      error: null,
    };

    if (printer) {
      status.connected = await printer.isPrinterConnected();
      status.port = printer.interface;
    } else {
      const initialized = await initializePrinter();
      status.connected = initialized;
    }

    res.json(status);
  } catch (error) {
    res.json({
      connected: false,
      printer: "T220MD",
      port: null,
      error: error.message,
    });
  }
});

/**
 * Test print
 */
app.post("/test", async (req, res) => {
  try {
    const testReceipt = {
      store: {
        name: "T220MD TEST PRINT",
        address: "123 Test Street, Test City",
        phone: "+254 700 000 000",
      },
      orderNumber: "TEST-001",
      orderDate: new Date().toISOString(),
      items: [
        {
          title: "Test Product",
          variantTitle: "Test Variant",
          quantity: 1,
          price: "10.00",
        },
      ],
      total: 10.0,
      paymentMethod: "Cash",
      customer: {
        name: "Test Customer",
      },
    };

    await printStructuredReceipt(testReceipt);
    await printer.execute();

    res.json({
      success: true,
      message: "T220MD test print completed",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

/**
 * List available printers/ports
 */
app.get("/printers", async (req, res) => {
  try {
    const ports = await SerialPort.list();
    res.json({
      ports: ports.map((port) => ({
        path: port.path,
        manufacturer: port.manufacturer,
        serialNumber: port.serialNumber,
        vendorId: port.vendorId,
        productId: port.productId,
      })),
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
    });
  }
});

// Health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    service: "T220MD Print Server",
    timestamp: new Date().toISOString(),
  });
});

// Start server
app.listen(PORT, async () => {
  console.log(`🖨️  T220MD Print Server running on http://localhost:${PORT}`);
  console.log("📋 Available endpoints:");
  console.log("  POST /print     - Print receipt");
  console.log("  POST /test      - Test print");
  console.log("  GET  /status    - Printer status");
  console.log("  GET  /printers  - List available printers");
  console.log("  GET  /health    - Health check");

  // Initialize printer on startup
  console.log("\n🔌 Initializing T220MD printer...");
  await initializePrinter();
});

// Graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Shutting down T220MD Print Server...");
  if (printer) {
    try {
      await printer.disconnect();
    } catch (error) {
      console.error("Error disconnecting printer:", error);
    }
  }
  process.exit(0);
});

module.exports = app;
