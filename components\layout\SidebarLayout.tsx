import { IconSymbol } from "@/components/ui/IconSymbol";
import { Layout, Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useCheckoutFlow } from "@/src/contexts/CheckoutFlowContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useRouter } from "expo-router";
import React from "react";
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface SidebarLayoutProps {
  children: React.ReactNode;
  title: string;
  showBackButton?: boolean;
}

interface SidebarItem {
  title: string;
  icon: string;
  route: string;
  badge?: string | number;
}

export function SidebarLayout({
  children,
  title,
  showBackButton = true,
}: SidebarLayoutProps) {
  const router = useRouter();
  const { user, signOut } = useSession();
  const { endCheckoutFlow } = useCheckoutFlow();
  const { canManageStaff, hasPermission } = useRBAC();
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");

  // Base sidebar items (always visible)
  const baseSidebarItems: SidebarItem[] = [
    { title: "Dashboard", icon: "house.fill", route: "/(tabs)" },
    { title: "Products", icon: "bag.fill", route: "/(tabs)/products" },
    { title: "Cart", icon: "cart.fill", route: "/(tabs)/cart" },
    { title: "Orders", icon: "list.bullet", route: "/(tabs)/orders" },
    { title: "Customers", icon: "person.2.fill", route: "/customer-list" },
  ];

  // Management items (conditional based on permissions)
  const managementItems: SidebarItem[] = [];

  if (canManageStaff) {
    managementItems.push({
      title: "Sales Agents",
      icon: "person.badge.plus",
      route: "/sales-agent-list",
    });
  }

  // Add loyalty management for users with loyalty permissions
  if (hasPermission("view_loyalty") || hasPermission("manage_loyalty")) {
    managementItems.push({
      title: "Loyalty Management",
      icon: "star.fill",
      route: "/loyalty-management",
    });
  }

  // Add discount management for users with discount permissions
  if (hasPermission("apply_discounts") || hasPermission("manage_discounts")) {
    managementItems.push({
      title: "Discount Management",
      icon: "tag.fill",
      route: "/discount-management",
    });
  }

  // Add fulfillment management for users with fulfillment permissions
  if (hasPermission("view_fulfillments")) {
    managementItems.push({
      title: "Fulfillment Management",
      icon: "shippingbox.fill",
      route: "/fulfillment-management",
    });
  }

  // Combine all sidebar items
  const sidebarItems: SidebarItem[] = [...baseSidebarItems, ...managementItems];

  const handleNavigation = (route: string) => {
    // Clear checkout flow when navigating to management pages
    if (
      route === "/customer-list" ||
      route === "/sales-agent-list" ||
      route === "/loyalty-management" ||
      route === "/discount-management" ||
      route === "/fulfillment-management"
    ) {
      endCheckoutFlow();
    }
    router.push(route as any);
  };

  const handleLogout = async () => {
    await signOut();
    router.replace("/pos-login");
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <StatusBar barStyle="light-content" backgroundColor={backgroundColor} />

      <View style={styles.layout}>
        {/* Sidebar */}
        <View
          style={[
            styles.sidebar,
            { backgroundColor: surfaceColor, borderColor },
          ]}
        >
          {/* User Profile */}
          <View style={[styles.userProfile, { borderColor }]}>
            <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
              <Text style={styles.avatarText}>
                {user?.name?.charAt(0).toUpperCase() || "U"}
              </Text>
            </View>
            <View style={styles.userInfo}>
              <Text
                style={[styles.userName, { color: textColor }]}
                numberOfLines={1}
              >
                {user?.name || "User"}
              </Text>
              <Text
                style={[styles.userRole, { color: textSecondary }]}
                numberOfLines={1}
              >
                {user?.role || "Staff"}
              </Text>
            </View>
          </View>

          {/* Navigation */}
          <ScrollView
            style={styles.navigation}
            showsVerticalScrollIndicator={false}
          >
            {sidebarItems.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={styles.navItem}
                onPress={() => handleNavigation(item.route)}
                activeOpacity={0.7}
              >
                <IconSymbol
                  name={item.icon as any}
                  size={20}
                  color={textSecondary}
                />
                <Text style={[styles.navText, { color: textSecondary }]}>
                  {item.title}
                </Text>
                {item.badge && (
                  <View
                    style={[styles.badge, { backgroundColor: primaryColor }]}
                  >
                    <Text style={styles.badgeText}>{item.badge}</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Logout Button */}
          <TouchableOpacity
            style={[styles.logoutButton, { borderColor }]}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <IconSymbol
              name="arrow.right.square"
              size={20}
              color={textSecondary}
            />
            <Text style={[styles.logoutText, { color: textSecondary }]}>
              Logout
            </Text>
          </TouchableOpacity>
        </View>

        {/* Main Content */}
        <View style={styles.main}>
          {/* Header */}
          <View style={[styles.header, { borderColor }]}>
            {showBackButton && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
                activeOpacity={0.7}
              >
                <IconSymbol name="chevron.left" size={24} color={textColor} />
              </TouchableOpacity>
            )}
            <Text style={[styles.headerTitle, { color: textColor }]}>
              {title}
            </Text>
          </View>

          {/* Content */}
          <View style={styles.content}>{children}</View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  layout: {
    flex: 1,
    flexDirection: "row",
  },
  sidebar: {
    width: Layout.sidebarWidth,
    borderRightWidth: 1,
    paddingVertical: Spacing.lg,
  },
  userProfile: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
    marginBottom: Spacing.lg,
    borderBottomWidth: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: Spacing.md,
  },
  avatarText: {
    ...Typography.bodyMedium,
    color: "#1A1A1A",
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    ...Typography.bodyMedium,
    marginBottom: 2,
  },
  userRole: {
    ...Typography.caption,
  },
  navigation: {
    flex: 1,
    paddingHorizontal: Spacing.md,
  },
  navItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderRadius: 8,
    marginBottom: 4,
  },
  navText: {
    ...Typography.body,
    marginLeft: Spacing.md,
    flex: 1,
  },
  badge: {
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: "center",
  },
  badgeText: {
    ...Typography.small,
    color: "#1A1A1A",
    fontWeight: "600",
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    marginHorizontal: Spacing.md,
    borderTopWidth: 1,
    marginTop: Spacing.lg,
    paddingTop: Spacing.lg,
  },
  logoutText: {
    ...Typography.body,
    marginLeft: Spacing.md,
  },
  main: {
    flex: 1,
  },
  header: {
    height: Layout.headerHeight,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: Spacing.md,
    padding: Spacing.sm,
  },
  headerTitle: {
    ...Typography.h3,
    flex: 1,
  },
  content: {
    flex: 1,
  },
});
