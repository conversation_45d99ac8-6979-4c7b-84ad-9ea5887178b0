#!/usr/bin/env node

/**
 * Database Connection Refactoring <PERSON>ript
 * 
 * This script automatically refactors all services to use the centralized DatabaseManager
 * instead of individual connection pools.
 */

const fs = require('fs');
const path = require('path');

// Services to refactor
const servicesToRefactor = [
  'backend/src/services/ticket-management-service.js',
  'backend/src/services/staff-service-mysql.js',
  'backend/src/services/multi-user-session-service.js',
  'backend/src/services/terminal-management-service.js',
  'backend/src/services/terminal-management-service-mysql.js',
  'backend/src/services/sales-agent-service.js',
  'backend/src/services/sales-agent-service-mysql.js',
  'backend/src/services/commission-service-mysql.js',
  'backend/src/services/commission-discount-service.js',
  'backend/src/services/fulfillment-service.js',
  'backend/src/middleware/auth-mysql.js',
  'backend/src/routes/pos-user-switching.js',
];

function refactorService(filePath) {
  console.log(`\n🔧 Refactoring ${filePath}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 1. Replace mysql import with database manager import
  if (content.includes('const mysql = require("mysql2/promise")')) {
    content = content.replace(
      'const mysql = require("mysql2/promise")',
      'const { databaseManager } = require("../config/database-manager")'
    );
    modified = true;
    console.log('  ✅ Updated mysql import');
  }
  
  if (content.includes("const mysql = require('mysql2/promise')")) {
    content = content.replace(
      "const mysql = require('mysql2/promise')",
      "const { databaseManager } = require('../config/database-manager')"
    );
    modified = true;
    console.log('  ✅ Updated mysql import (single quotes)');
  }
  
  // 2. Replace connection pool creation with database manager reference
  const poolCreationRegex = /this\.pool = mysql\.createPool\(\{[\s\S]*?\}\);/g;
  if (poolCreationRegex.test(content)) {
    content = content.replace(poolCreationRegex, 'this.databaseManager = databaseManager;');
    modified = true;
    console.log('  ✅ Replaced connection pool creation');
  }
  
  // 3. Replace direct pool creation (for routes)
  const directPoolRegex = /const pool = mysql\.createPool\(\{[\s\S]*?\}\);/g;
  if (directPoolRegex.test(content)) {
    content = content.replace(directPoolRegex, 'const { databaseManager } = require("../config/database-manager");');
    modified = true;
    console.log('  ✅ Replaced direct pool creation');
  }
  
  // 4. Replace this.pool.execute calls
  content = content.replace(/this\.pool\.execute\(/g, 'this.databaseManager.executeQuery(');
  if (content.includes('this.databaseManager.executeQuery(')) {
    modified = true;
    console.log('  ✅ Replaced this.pool.execute calls');
  }
  
  // 5. Replace pool.execute calls (for routes)
  content = content.replace(/pool\.execute\(/g, 'databaseManager.executeQuery(');
  if (content.includes('databaseManager.executeQuery(')) {
    modified = true;
    console.log('  ✅ Replaced pool.execute calls');
  }
  
  // 6. Replace this.pool.getConnection calls
  content = content.replace(/this\.pool\.getConnection\(\)/g, 'this.databaseManager.getConnection()');
  if (content.includes('this.databaseManager.getConnection()')) {
    modified = true;
    console.log('  ✅ Replaced this.pool.getConnection calls');
  }
  
  // 7. Replace pool.getConnection calls (for routes)
  content = content.replace(/pool\.getConnection\(\)/g, 'databaseManager.getConnection()');
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Successfully refactored ${filePath}`);
  } else {
    console.log(`  ℹ️ No changes needed for ${filePath}`);
  }
}

function main() {
  console.log('🚀 Starting database connection refactoring...\n');
  
  servicesToRefactor.forEach(refactorService);
  
  console.log('\n✅ Database connection refactoring completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Test the refactored services');
  console.log('2. Update any remaining manual connection management');
  console.log('3. Restart the server to use the centralized database manager');
}

if (require.main === module) {
  main();
}

module.exports = { refactorService };
