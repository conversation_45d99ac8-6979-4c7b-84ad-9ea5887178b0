/**
 * Fulfillment React Query Hooks
 *
 * TanStack React Query hooks for fulfillment management.
 * Provides optimistic updates, cache invalidation, and error handling
 * for all fulfillment-related operations.
 */

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  fulfillmentApiService,
  FulfillmentData,
  FulfillmentResponse,
  ShippingCalculationData,
  ShippingRate,
  UpdateDeliveryDetailsData,
} from "../services/fulfillmentApiService";

// Query Keys
export const fulfillmentKeys = {
  all: ["fulfillments"] as const,
  lists: () => [...fulfillmentKeys.all, "list"] as const,
  list: (filters: string) => [...fulfillmentKeys.lists(), { filters }] as const,
  details: () => [...fulfillmentKeys.all, "detail"] as const,
  detail: (id: string) => [...fulfillmentKeys.details(), id] as const,
  orderFulfillments: (orderId: string) =>
    [...fulfillmentKeys.all, "order", orderId] as const,
  shippingRates: () => ["shipping", "rates"] as const,
  shippingCalculation: () => ["shipping", "calculation"] as const,
  stats: () => [...fulfillmentKeys.all, "stats"] as const,
};

/**
 * Hook to get fulfillment by ID
 */
export function useFulfillment(fulfillmentId: string) {
  return useQuery({
    queryKey: fulfillmentKeys.detail(fulfillmentId),
    queryFn: () => fulfillmentApiService.getFulfillmentById(fulfillmentId),
    enabled: !!fulfillmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get fulfillments for an order
 */
export function useFulfillmentsByOrder(orderId: string) {
  return useQuery({
    queryKey: fulfillmentKeys.orderFulfillments(orderId),
    queryFn: () => fulfillmentApiService.getFulfillmentsByOrderId(orderId),
    enabled: !!orderId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to get shipping rates
 */
export function useShippingRates() {
  return useQuery({
    queryKey: fulfillmentKeys.shippingRates(),
    queryFn: () => fulfillmentApiService.getShippingRates(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get fulfillment statistics
 */
export function useFulfillmentStats() {
  return useQuery({
    queryKey: fulfillmentKeys.stats(),
    queryFn: () => fulfillmentApiService.getFulfillmentStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to create a new fulfillment
 */
export function useCreateFulfillment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (fulfillmentData: FulfillmentData) =>
      fulfillmentApiService.createFulfillment(fulfillmentData),
    onSuccess: (data, variables) => {
      // Invalidate and refetch order fulfillments
      queryClient.invalidateQueries({
        queryKey: fulfillmentKeys.orderFulfillments(variables.orderId),
      });

      // Add the new fulfillment to the cache
      queryClient.setQueryData(fulfillmentKeys.detail(data.id), data);
    },
    onError: (error: any) => {
      // Error handling will be done by the component using this hook
      console.error("Failed to create fulfillment:", error);
    },
  });
}

/**
 * Hook to update delivery details
 */
export function useUpdateDeliveryDetails() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      fulfillmentId,
      updateData,
    }: {
      fulfillmentId: string;
      updateData: UpdateDeliveryDetailsData;
    }) =>
      fulfillmentApiService.updateDeliveryDetails(fulfillmentId, updateData),
    onSuccess: (_, variables) => {
      // Invalidate fulfillment details
      queryClient.invalidateQueries({
        queryKey: fulfillmentKeys.detail(variables.fulfillmentId),
      });

      // Invalidate order fulfillments if we have the order ID
      const fulfillmentData = queryClient.getQueryData<FulfillmentResponse>(
        fulfillmentKeys.detail(variables.fulfillmentId)
      );
      if (fulfillmentData) {
        queryClient.invalidateQueries({
          queryKey: fulfillmentKeys.orderFulfillments(fulfillmentData.orderId),
        });
      }

      // Success handling will be done by the component using this hook
    },
    onError: (error: any) => {
      // Error handling will be done by the component using this hook
      console.error("Failed to update delivery details:", error);
    },
  });
}

/**
 * Hook to update fulfillment status
 */
export function useUpdateFulfillmentStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      fulfillmentId,
      status,
    }: {
      fulfillmentId: string;
      status: string;
    }) => fulfillmentApiService.updateFulfillmentStatus(fulfillmentId, status),
    onSuccess: (_, variables) => {
      // Invalidate fulfillment details
      queryClient.invalidateQueries({
        queryKey: fulfillmentKeys.detail(variables.fulfillmentId),
      });

      // Invalidate order fulfillments
      const fulfillmentData = queryClient.getQueryData<FulfillmentResponse>(
        fulfillmentKeys.detail(variables.fulfillmentId)
      );
      if (fulfillmentData) {
        queryClient.invalidateQueries({
          queryKey: fulfillmentKeys.orderFulfillments(fulfillmentData.orderId),
        });
      }

      // Invalidate stats
      queryClient.invalidateQueries({
        queryKey: fulfillmentKeys.stats(),
      });

      // Success handling will be done by the component using this hook
    },
    onError: (error: any) => {
      // Error handling will be done by the component using this hook
      console.error("Failed to update fulfillment status:", error);
    },
  });
}

/**
 * Hook to calculate shipping fee
 */
export function useCalculateShippingFee() {
  return useMutation({
    mutationFn: (shippingData: ShippingCalculationData) =>
      fulfillmentApiService.calculateShippingFee(shippingData),
    onError: (error: any) => {
      // Error handling will be done by the component using this hook
      console.error("Failed to calculate shipping fee:", error);
    },
  });
}

/**
 * Hook to create or update shipping rate
 */
export function useUpsertShippingRate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (rateData: Partial<ShippingRate>) =>
      fulfillmentApiService.upsertShippingRate(rateData),
    onSuccess: () => {
      // Invalidate shipping rates
      queryClient.invalidateQueries({
        queryKey: fulfillmentKeys.shippingRates(),
      });

      // Success handling will be done by the component using this hook
    },
    onError: (error: any) => {
      // Error handling will be done by the component using this hook
      console.error("Failed to save shipping rate:", error);
    },
  });
}

/**
 * Hook to update shipping rate
 */
export function useUpdateShippingRate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      rateId,
      rateData,
    }: {
      rateId: string;
      rateData: Partial<ShippingRate>;
    }) => fulfillmentApiService.updateShippingRate(rateId, rateData),
    onSuccess: () => {
      // Invalidate shipping rates
      queryClient.invalidateQueries({
        queryKey: fulfillmentKeys.shippingRates(),
      });

      // Success handling will be done by the component using this hook
    },
    onError: (error: any) => {
      // Error handling will be done by the component using this hook
      console.error("Failed to update shipping rate:", error);
    },
  });
}
