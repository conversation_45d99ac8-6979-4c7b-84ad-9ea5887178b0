import { Spacing, Typography } from "@/constants/Design";
import {
  getButtonStyle,
  getCardStyle,
  getInputStyle,
  getTextStyle,
  useTheme,
} from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React from "react";
import {
  ScrollView,
  ScrollViewProps,
  Text,
  TextInput,
  TextInputProps,
  TextProps,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewProps,
  ViewStyle,
  Platform,
} from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

// Themed View Component
interface ThemedViewProps extends ViewProps {
  variant?: "default" | "card" | "surface" | "background";
  elevated?: boolean;
}

export const ThemedView: React.FC<ThemedViewProps> = ({
  style,
  variant = "default",
  elevated = false,
  ...props
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const getVariantStyle = (): ViewStyle => {
    switch (variant) {
      case "card":
        return getCardStyle(theme, elevated);
      case "surface":
        return utils.bgSurface();
      case "background":
        return { backgroundColor: theme.colors.background };
      default:
        return {};
    }
  };

  return <View style={[getVariantStyle(), style]} {...props} />;
};

// Themed Text Component
interface ThemedTextProps extends TextProps {
  variant?: keyof typeof Typography;
  color?:
    | "primary"
    | "secondary"
    | "muted"
    | "accent"
    | "success"
    | "warning"
    | "error";
}

export const ThemedText: React.FC<ThemedTextProps> = ({
  style,
  variant = "body",
  color = "primary",
  ...props
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const getColorStyle = (): TextStyle => {
    switch (color) {
      case "primary":
        return utils.textPrimary();
      case "secondary":
        return utils.textSecondary();
      case "muted":
        return utils.textMuted();
      case "accent":
        return utils.textAccent();
      case "success":
        return utils.textSuccess();
      case "warning":
        return utils.textWarning();
      case "error":
        return utils.textError();
      default:
        return utils.textPrimary();
    }
  };

  return (
    <Text
      style={[getTextStyle(theme, variant), getColorStyle(), style]}
      {...props}
    />
  );
};

// Themed Button Component
interface ThemedButtonProps extends TouchableOpacityProps {
  variant?: "primary" | "secondary" | "outline";
  size?: "small" | "medium" | "large";
  title: string;
  textColor?: string;
}

export const ThemedButton: React.FC<ThemedButtonProps> = ({
  style,
  variant = "primary",
  size = "medium",
  title,
  textColor,
  ...props
}) => {
  const theme = useTheme();

  const buttonStyle = getButtonStyle(theme, variant, size);

  const getTextColor = (): string => {
    if (textColor) return textColor;

    switch (variant) {
      case "primary":
        return theme.colors.primaryForeground;
      case "secondary":
        return theme.colors.secondaryForeground;
      case "outline":
        return theme.colors.text;
      default:
        return theme.colors.primaryForeground;
    }
  };

  return (
    <TouchableOpacity style={[buttonStyle, style]} {...props}>
      <ThemedText
        variant={
          size === "small"
            ? "buttonSmall"
            : size === "large"
            ? "buttonLarge"
            : "button"
        }
        style={{ color: getTextColor() }}
      >
        {title}
      </ThemedText>
    </TouchableOpacity>
  );
};

// Themed TextInput Component
interface ThemedTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  focused?: boolean;
}

export const ThemedTextInput: React.FC<ThemedTextInputProps> = ({
  style,
  label,
  error,
  focused = false,
  ...props
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);
  const { spacingMultiplier, typographyScale } = useResponsiveLayout();

  const inputStyle = getInputStyle(theme, focused);

  // Apply responsive styling
  const responsiveInputStyle = {
    ...inputStyle,
    paddingHorizontal: theme.spacing.md * spacingMultiplier,
    paddingVertical: theme.spacing.md * spacingMultiplier * 0.8,
    fontSize: theme.typography.body.fontSize * typographyScale,
    minHeight: 44 * spacingMultiplier,
  };

  return (
    <View>
      {label && (
        <ThemedText variant="label" color="secondary" style={utils.mb("sm")}>
          {label}
        </ThemedText>
      )}
      <TextInput
        style={[responsiveInputStyle, style]}
        placeholderTextColor={theme.colors.placeholder}
        {...props}
      />
      {error && (
        <ThemedText variant="small" color="error" style={utils.mt("xs")}>
          {error}
        </ThemedText>
      )}
    </View>
  );
};

// Themed Card Component
interface ThemedCardProps extends ViewProps {
  title?: string;
  subtitle?: string;
  elevated?: boolean;
  padding?: keyof typeof Spacing;
}

export const ThemedCard: React.FC<ThemedCardProps> = ({
  style,
  title,
  subtitle,
  elevated = true,
  padding = "lg",
  children,
  ...props
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const cardStyle = getCardStyle(theme, elevated);

  return (
    <View style={[cardStyle, utils.p(padding), style]} {...props}>
      {title && (
        <ThemedText
          variant="h3"
          style={subtitle ? utils.mb("xs") : utils.mb("md")}
        >
          {title}
        </ThemedText>
      )}
      {subtitle && (
        <ThemedText variant="caption" color="secondary" style={utils.mb("md")}>
          {subtitle}
        </ThemedText>
      )}
      {children}
    </View>
  );
};

// Themed ScrollView Component
interface ThemedScrollViewProps extends ScrollViewProps {
  variant?: "default" | "background";
}

export const ThemedScrollView: React.FC<ThemedScrollViewProps> = ({
  style,
  variant = "default",
  ...props
}) => {
  const theme = useTheme();

  const getBackgroundColor = () => {
    switch (variant) {
      case "background":
        return theme.colors.background;
      default:
        return undefined;
    }
  };

  return (
    <ScrollView
      style={[{ backgroundColor: getBackgroundColor() }, style]}
      {...props}
    />
  );
};

// Themed Divider Component
interface ThemedDividerProps {
  style?: ViewStyle;
  color?: string;
  thickness?: number;
  margin?: keyof typeof Spacing;
}

export const ThemedDivider: React.FC<ThemedDividerProps> = ({
  style,
  color,
  thickness = 1,
  margin = "md",
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  return (
    <View
      style={[
        {
          height: thickness,
          backgroundColor: color || theme.colors.divider,
        },
        utils.my(margin),
        style,
      ]}
    />
  );
};

// Themed Badge Component
interface ThemedBadgeProps extends ViewProps {
  text: string;
  variant?: "primary" | "secondary" | "success" | "warning" | "error";
  size?: "small" | "medium";
}

export const ThemedBadge: React.FC<ThemedBadgeProps> = ({
  style,
  text,
  variant = "primary",
  size = "medium",
  ...props
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const getBackgroundColor = () => {
    switch (variant) {
      case "primary":
        return theme.colors.primary;
      case "secondary":
        return theme.colors.secondary;
      case "success":
        return theme.colors.success;
      case "warning":
        return theme.colors.warning;
      case "error":
        return theme.colors.error;
      default:
        return theme.colors.primary;
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case "primary":
        return theme.colors.primaryForeground;
      case "secondary":
        return theme.colors.secondaryForeground;
      default:
        return theme.colors.textInverse;
    }
  };

  const sizeStyle =
    size === "small"
      ? { ...utils.px("sm"), ...utils.py("xs") }
      : { ...utils.px("md"), ...utils.py("sm") };

  return (
    <View
      style={[
        {
          backgroundColor: getBackgroundColor(),
          borderRadius: theme.borderRadius.round,
          alignItems: "center",
          justifyContent: "center",
        },
        sizeStyle,
        style,
      ]}
      {...props}
    >
      <ThemedText
        variant={size === "small" ? "small" : "caption"}
        style={{ color: getTextColor() }}
      >
        {text}
      </ThemedText>
    </View>
  );
};
