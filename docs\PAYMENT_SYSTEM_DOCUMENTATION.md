# Enhanced Payment System Documentation

## Overview

The Enhanced Payment System provides comprehensive multi-payment method support with split payment functionality, real-time status tracking, and seamless Shopify integration for the Dukalink POS application.

## Features

- **Multi-Payment Methods**: Cash, M-Pesa, ABSA Till, Card, Credit
- **Split Payments**: Multiple payment methods per transaction
- **Real-time Processing**: Live status updates and balance tracking
- **Shopify Integration**: Automatic order creation with payment metadata
- **Comprehensive Audit**: Full transaction logging and reporting
- **Error Handling**: Robust error management with recovery suggestions

## Architecture

### Backend Components

1. **PaymentTransactionService** - Core payment processing logic
2. **MpesaIntegrationService** - M-Pesa STK Push and validation
3. **ShopifyPaymentIntegrationService** - Shopify order and payment creation
4. **Payment Processing API** - RESTful endpoints for payment operations

### Frontend Components

1. **PaymentFlowManager** - Unified payment modal with step navigation
2. **EnhancedPaymentService** - API integration service
3. **PaymentErrorHandler** - Comprehensive error handling

### Database Schema

- `payment_transactions` - Main transaction records
- `payment_methods_used` - Individual payment method details
- `credit_payments` - Credit payment tracking
- `payment_audit_log` - Audit trail and logging

## API Endpoints

### Transaction Management

#### POST /api/payments/initiate
Initiate a new payment transaction.

**Request:**
```json
{
  "totalAmount": 1500.00,
  "currency": "KES",
  "customerId": "customer-uuid",
  "paymentMethods": [
    {
      "methodType": "cash",
      "methodName": "Cash Payment",
      "amount": 800.00
    },
    {
      "methodType": "mpesa",
      "methodName": "M-Pesa Payment",
      "amount": 700.00
    }
  ],
  "metadata": {
    "orderData": {},
    "customerName": "John Doe"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "transactionId": "txn-uuid",
    "status": "pending",
    "totalAmount": 1500.00,
    "isSplitPayment": true,
    "remainingAmount": 1500.00,
    "paymentMethods": 2
  }
}
```

#### POST /api/payments/{transactionId}/methods
Add a payment method to an existing transaction.

**Request:**
```json
{
  "methodType": "mpesa",
  "methodName": "M-Pesa Payment",
  "amount": 700.00,
  "metadata": {}
}
```

#### POST /api/payments/methods/{methodId}/process
Process a specific payment method.

**Request (M-Pesa STK Push):**
```json
{
  "paymentMethod": "stk_push",
  "phoneNumber": "************",
  "accountReference": "ORDER_123",
  "transactionDesc": "Payment for order"
}
```

**Request (M-Pesa Manual Code):**
```json
{
  "paymentMethod": "manual_code",
  "transactionCode": "QGH1234567",
  "phoneNumber": "************"
}
```

**Request (ABSA Till):**
```json
{
  "transactionCode": "ABSA1234567",
  "tillNumber": "123456",
  "customerPhone": "************"
}
```

**Request (Card Payment):**
```json
{
  "confirmed": true,
  "cardType": "visa",
  "lastFourDigits": "1234",
  "authorizationCode": "AUTH123456"
}
```

**Request (Credit Payment):**
```json
{
  "customerName": "John Doe",
  "customerPhone": "************",
  "creditLimit": 50000.00,
  "dueDate": "2024-02-01",
  "paymentTerms": "Net 30"
}
```

### Status and Balance

#### GET /api/payments/{transactionId}/status
Get transaction status and payment method details.

#### GET /api/payments/{transactionId}/balance
Get remaining balance for split payments.

#### GET /api/payments/{transactionId}/split-summary
Get comprehensive split payment summary.

### Validation

#### POST /api/payments/{transactionId}/validate-split
Validate split payment amounts before processing.

### Shopify Integration

#### POST /api/payments/{transactionId}/complete-with-shopify
Complete transaction and create Shopify order.

**Request:**
```json
{
  "orderData": {
    "line_items": [...],
    "customer": {...},
    "shipping_address": {...}
  }
}
```

## Payment Method Implementation

### Cash Payments

Cash payments require amount tendered and calculate change automatically.

```javascript
const processingData = {
  amountTendered: 1200.00,
  change: 200.00
};
```

### M-Pesa Payments

M-Pesa supports both STK Push and manual transaction code validation.

**STK Push Flow:**
1. Initiate STK Push with phone number
2. Customer receives payment prompt
3. Check payment status
4. Complete or fallback to manual code

**Manual Code Flow:**
1. Customer completes M-Pesa transaction
2. Enter transaction code from SMS
3. Validate code with M-Pesa API
4. Complete payment

### ABSA Till Payments

ABSA Till payments use transaction codes from customer receipts.

```javascript
const processingData = {
  transactionCode: "ABSA1234567",
  tillNumber: "123456",
  customerPhone: "************"
};
```

### Card Payments

Card payments use manual confirmation workflow.

```javascript
const processingData = {
  confirmed: true,
  cardType: "visa",
  lastFourDigits: "1234",
  authorizationCode: "AUTH123456",
  customerName: "John Doe"
};
```

### Credit Payments

Credit payments link to customer profiles with credit limits.

```javascript
const processingData = {
  customerName: "John Doe",
  customerPhone: "************",
  creditLimit: 50000.00,
  dueDate: "2024-02-01",
  paymentTerms: "Net 30",
  notes: "Regular customer"
};
```

## Split Payment Workflow

1. **Initiate Transaction** - Create transaction with multiple payment methods
2. **Add Payment Methods** - Add individual payment methods to transaction
3. **Process Methods** - Process each payment method individually
4. **Validate Balance** - Ensure total payments equal transaction amount
5. **Complete Transaction** - Mark transaction as completed when fully paid
6. **Create Shopify Order** - Generate order with payment metadata

## Error Handling

The system provides comprehensive error handling with user-friendly messages and recovery suggestions.

### Common Error Codes

- `NETWORK_ERROR` - Connection issues
- `INVALID_AMOUNT` - Invalid payment amounts
- `MPESA_STK_FAILED` - M-Pesa payment failures
- `ABSA_INVALID_CODE` - Invalid ABSA transaction codes
- `CREDIT_LIMIT_EXCEEDED` - Credit limit violations
- `AMOUNT_MISMATCH` - Split payment amount mismatches

### Error Response Format

```json
{
  "success": false,
  "error": "User-friendly error message",
  "code": "ERROR_CODE",
  "suggestions": [
    "Try again in a few moments",
    "Check your internet connection",
    "Contact support if problem persists"
  ]
}
```

## Database Schema

### payment_transactions
- `id` - Unique transaction identifier
- `total_amount` - Total transaction amount
- `payment_status` - Current transaction status
- `is_split_payment` - Boolean flag for split payments
- `remaining_amount` - Remaining unpaid amount
- `shopify_order_id` - Associated Shopify order

### payment_methods_used
- `id` - Unique payment method identifier
- `transaction_id` - Parent transaction reference
- `method_type` - Payment method type
- `amount` - Payment amount
- `status` - Payment method status
- `metadata` - Method-specific data

### credit_payments
- `id` - Unique credit payment identifier
- `customer_name` - Customer name
- `outstanding_balance` - Amount owed
- `due_date` - Payment due date
- `status` - Credit payment status

## Testing

### Unit Tests
Run payment system unit tests:
```bash
npm test backend/tests/payment-system.test.js
```

### Integration Tests
Test complete payment workflows:
```bash
npm run test:integration
```

### Test Scenarios
- Single payment methods (Cash, M-Pesa, ABSA Till, Card, Credit)
- Split payment combinations
- Error handling and recovery
- Shopify integration
- Balance calculations
- Status transitions

## Deployment

### Database Migration
Run the migration script to create payment tables:
```bash
mysql -u username -p database_name < backend/migrations/001_create_payment_system_tables.sql
```

### Environment Variables
Required environment variables:
```bash
# M-Pesa Configuration
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_short_code
MPESA_PASSKEY=your_passkey
MPESA_CALLBACK_URL=your_callback_url

# Shopify Configuration
SHOPIFY_SHOP_DOMAIN=your_shop.myshopify.com
SHOPIFY_ACCESS_TOKEN=your_access_token
SHOPIFY_API_VERSION=2024-01

# Database Configuration
DB_HOST=localhost
DB_USER=username
DB_PASSWORD=password
DB_NAME=database_name
```

### Production Checklist
- [ ] Database migration completed
- [ ] Environment variables configured
- [ ] M-Pesa credentials validated
- [ ] Shopify integration tested
- [ ] Error handling verified
- [ ] Audit logging enabled
- [ ] Performance monitoring setup

## Troubleshooting

### Common Issues

1. **M-Pesa STK Push Failures**
   - Verify phone number format
   - Check M-Pesa credentials
   - Ensure callback URL is accessible

2. **ABSA Till Validation Errors**
   - Verify transaction code format
   - Check ABSA API connectivity
   - Validate till number

3. **Split Payment Amount Mismatches**
   - Verify payment method amounts sum to total
   - Check for rounding errors
   - Validate currency consistency

4. **Shopify Integration Issues**
   - Verify Shopify credentials
   - Check API rate limits
   - Validate order data format

### Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: https://docs.dukalink.com
- GitHub Issues: https://github.com/dukalink/payment-system/issues
