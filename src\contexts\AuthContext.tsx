import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  type PropsWithChildren,
} from "react";
import { useStorageState } from "../hooks/useStorageState";
import { getAPIClient } from "../services/api/dukalink-client";
import { ShopifyStore } from "../types/shopify";

interface User {
  id: string;
  username: string;
  name: string;
  email?: string;
  role: string;
  storeId: string;
  permissions: string[];
  commissionRate?: number;
  lastLogin?: string;
  has_pin?: boolean; // Added PIN status property
}

interface AuthContextType {
  // Session state
  session: string | null;
  isLoading: boolean;

  // Shopify OAuth state
  isShopifyAuthenticated: boolean;
  connectedStore: ShopifyStore | null;

  // POS Authentication state
  isPosAuthenticated: boolean;
  user: User | null;

  // Methods
  signIn: (token: string) => void;
  signOut: () => void;
  setShopifyAuth: (token: string, store: ShopifyStore) => void;
  setPosAuth: (token: string, user: User) => void;
  clearShopifyAuth: () => void;
  clearPosAuth: () => void;

  // Verification methods
  verifyShopifySession: () => Promise<boolean>;
  verifyPosSession: () => Promise<boolean>;

  // Permission checking methods
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType>({
  session: null,
  isLoading: false,
  isShopifyAuthenticated: false,
  connectedStore: null,
  isPosAuthenticated: false,
  user: null,
  signIn: () => null,
  signOut: () => null,
  setShopifyAuth: () => null,
  setPosAuth: () => null,
  clearShopifyAuth: () => null,
  clearPosAuth: () => null,
  verifyShopifySession: async () => false,
  verifyPosSession: async () => false,
  hasPermission: () => false,
  hasRole: () => false,
  hasAnyPermission: () => false,
});

export function useSession() {
  const value = useContext(AuthContext);
  if (!value) {
    throw new Error("useSession must be wrapped in a <SessionProvider />");
  }
  return value;
}

export function SessionProvider({ children }: PropsWithChildren) {
  const [[isLoading, session], setSession] = useStorageState("session_token");
  const [[, shopifyData], setShopifyData] = useStorageState("shopify_auth");
  const [[, posData], setPosData] = useStorageState("pos_auth");

  const [isShopifyAuthenticated, setIsShopifyAuthenticated] = useState(false);
  const [connectedStore, setConnectedStore] = useState<ShopifyStore | null>(
    null
  );
  const [isPosAuthenticated, setIsPosAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);

  // Initialize authentication state from stored data and verify sessions
  useEffect(() => {
    const initializeAuth = async () => {
      if (!isLoading && session) {
        // Parse stored Shopify data
        if (shopifyData) {
          try {
            const parsed = JSON.parse(shopifyData);
            setConnectedStore(parsed.store);
            setIsShopifyAuthenticated(true);
            // Verify Shopify session in background
            verifyShopifySession();
          } catch (e) {
            console.error("Failed to parse Shopify data:", e);
            setShopifyData(null);
          }
        }

        // Parse stored POS data
        if (posData) {
          try {
            const parsed = JSON.parse(posData);
            setUser(parsed.user);
            setIsPosAuthenticated(true);
            // Verify POS session in background
            verifyPosSession();
          } catch (e) {
            console.error("Failed to parse POS data:", e);
            setPosData(null);
          }
        }
      }
    };

    initializeAuth();
  }, [isLoading, session, shopifyData, posData]);

  const signIn = (token: string) => {
    setSession(token);
  };

  const signOut = () => {
    setSession(null);
    clearShopifyAuth();
    clearPosAuth();
  };

  const setShopifyAuth = (token: string, store: ShopifyStore) => {
    setSession(token);
    setShopifyData(JSON.stringify({ store }));
    setConnectedStore(store);
    setIsShopifyAuthenticated(true);
  };

  const setPosAuth = (token: string, user: User) => {
    setSession(token);
    setPosData(JSON.stringify({ user }));
    setUser(user);
    setIsPosAuthenticated(true);
  };

  const clearShopifyAuth = () => {
    setShopifyData(null);
    setConnectedStore(null);
    setIsShopifyAuthenticated(false);
  };

  const clearPosAuth = () => {
    setPosData(null);
    setUser(null);
    setIsPosAuthenticated(false);
  };

  const verifyShopifySession = async (): Promise<boolean> => {
    if (!session) return false;

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.verifySession();

      if (response.success && response.data) {
        setConnectedStore(response.data.store);
        setIsShopifyAuthenticated(true);
        return true;
      } else {
        clearShopifyAuth();
        return false;
      }
    } catch (error) {
      console.error("Shopify session verification failed:", error);
      clearShopifyAuth();
      return false;
    }
  };

  const verifyPosSession = async (): Promise<boolean> => {
    if (!session) return false;

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.posVerify();

      if (response.success && response.data) {
        setUser(response.data.user);
        setIsPosAuthenticated(true);
        return true;
      } else {
        clearPosAuth();
        return false;
      }
    } catch (error) {
      console.error("POS session verification failed:", error);
      clearPosAuth();
      return false;
    }
  };

  // Permission checking methods
  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    return user?.role === role;
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return (
      permissions.some((permission) =>
        user?.permissions?.includes(permission)
      ) || false
    );
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        isLoading,
        isShopifyAuthenticated,
        connectedStore,
        isPosAuthenticated,
        user,
        signIn,
        signOut,
        setShopifyAuth,
        setPosAuth,
        clearShopifyAuth,
        clearPosAuth,
        verifyShopifySession,
        verifyPosSession,
        hasPermission,
        hasRole,
        hasAnyPermission,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
