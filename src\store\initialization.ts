/**
 * Store Initialization
 * 
 * Handles initial setup and configuration of the Redux store,
 * including enabling ticket mode and creating default tickets.
 */

import { store } from './index';
import { enableTicketMode } from './slices/cartCompatibilitySlice';
import { createTicket } from './slices/ticketSlice';

export const initializeStore = () => {
  // Enable ticket mode by default
  store.dispatch(enableTicketMode());
  
  // Create a default ticket if none exist
  const state = store.getState();
  if (state.tickets.tickets.length === 0) {
    store.dispatch(createTicket({
      name: 'Welcome Ticket',
      staffId: 'default-staff', // Will be updated when user logs in
      terminalId: 'terminal-1',
      locationId: 'location-1',
    }));
  }
};

export const initializeUserSession = (userId: string, terminalId?: string, locationId?: string) => {
  // Create a new ticket for the user session
  store.dispatch(createTicket({
    name: `Session ${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`,
    staffId: userId,
    terminalId: terminalId || 'terminal-1',
    locationId: locationId || 'location-1',
  }));
};
