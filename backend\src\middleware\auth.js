/**
 * POS Staff Authentication Middleware - MySQL Implementation
 * Migrated from hardcoded arrays to MySQL database
 * Maintains 100% API compatibility with existing implementation
 */

require("dotenv").config();
const jwt = require("jsonwebtoken");
const { databaseManager } = require("../config/database-manager");
const crypto = require("crypto");
const bcrypt = require("bcrypt");

class AuthService {
  constructor() {
    if (AuthService.instance) {
      return AuthService.instance;
    }

    // Use centralized database manager
    this.databaseManager = databaseManager;

    console.log(
      "✅ Auth Service initialized with centralized database manager"
    );

    AuthService.instance = this;
  }

  static getInstance() {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Execute query using centralized database manager
   */
  async executeQuery(query, params = []) {
    return await this.databaseManager.executeQuery(query, params);
  }

  // Get staff member with permissions
  async getStaffWithPermissions(staffId) {
    try {
      const query = `
        SELECT
          s.id,
          s.username,
          s.name,
          s.email,
          s.role,
          s.store_id,
          s.commission_rate,
          s.is_active,
          s.last_login,
          s.failed_login_attempts,
          s.locked_until,
          CASE WHEN s.pin IS NOT NULL THEN 1 ELSE 0 END as has_pin,
          GROUP_CONCAT(sp.permission) as permissions
        FROM pos_staff s
        LEFT JOIN staff_permissions sp ON s.id = sp.staff_id
        WHERE s.id = ? AND s.is_active = TRUE
        GROUP BY s.id
      `;

      const [rows] = await this.databaseManager.executeQuery(query, [staffId]);

      if (rows.length === 0) {
        return null;
      }

      const staff = rows[0];
      const permissions = staff.permissions ? staff.permissions.split(",") : [];

      return {
        id: staff.id,
        username: staff.username,
        name: staff.name,
        email: staff.email,
        role: staff.role,
        storeId: staff.store_id,
        commissionRate: parseFloat(staff.commission_rate),
        isActive: Boolean(staff.is_active),
        lastLogin: staff.last_login,
        failedLoginAttempts: staff.failed_login_attempts,
        lockedUntil: staff.locked_until,
        hasPin: Boolean(staff.has_pin), // Add PIN status
        permissions: permissions,
      };
    } catch (error) {
      console.error("Get staff with permissions error:", error);
      return null;
    }
  }

  // Get staff by username for login
  async getStaffByUsername(username) {
    try {
      const query = `
        SELECT
          s.id,
          s.username,
          s.password_hash,
          s.name,
          s.email,
          s.role,
          s.store_id,
          s.commission_rate,
          s.is_active,
          s.failed_login_attempts,
          s.locked_until,
          CASE WHEN s.pin IS NOT NULL THEN 1 ELSE 0 END as has_pin,
          GROUP_CONCAT(sp.permission) as permissions
        FROM pos_staff s
        LEFT JOIN staff_permissions sp ON s.id = sp.staff_id
        WHERE s.username = ?
        GROUP BY s.id
      `;

      const [rows] = await this.databaseManager.executeQuery(query, [username]);

      if (rows.length === 0) {
        return null;
      }

      const staff = rows[0];
      const permissions = staff.permissions ? staff.permissions.split(",") : [];

      return {
        id: staff.id,
        username: staff.username,
        passwordHash: staff.password_hash,
        name: staff.name,
        email: staff.email,
        role: staff.role,
        storeId: staff.store_id,
        commissionRate: parseFloat(staff.commission_rate),
        isActive: Boolean(staff.is_active),
        failedLoginAttempts: staff.failed_login_attempts,
        lockedUntil: staff.locked_until,
        hasPin: Boolean(staff.has_pin), // Add PIN status
        permissions: permissions,
      };
    } catch (error) {
      console.error("Get staff by username error:", error);
      return null;
    }
  }

  // Validate session token
  async validateSession(tokenHash) {
    try {
      const query = `
        SELECT s.staff_id, s.expires_at, s.terminal_id, s.location_id
        FROM pos_sessions s
        WHERE s.token_hash = ? AND s.expires_at > NOW()
      `;

      const [rows] = await this.databaseManager.executeQuery(query, [
        tokenHash,
      ]);

      if (rows.length === 0) {
        return null;
      }

      // Update last activity
      await this.databaseManager.executeQuery(
        "UPDATE pos_sessions SET last_activity = NOW() WHERE token_hash = ?",
        [tokenHash]
      );

      return rows[0];
    } catch (error) {
      console.error("Validate session error:", error);
      return null;
    }
  }

  // Create session
  async createSession(staffId, tokenHash, sessionData = {}) {
    try {
      const sessionId = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + 8 * 60 * 60 * 1000); // 8 hours

      await this.databaseManager.executeQuery(
        `INSERT INTO pos_sessions 
         (id, staff_id, token_hash, terminal_id, location_id, ip_address, user_agent, expires_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          sessionId,
          staffId,
          tokenHash,
          sessionData.terminalId || null,
          sessionData.locationId || null,
          sessionData.ipAddress || null,
          sessionData.userAgent || null,
          expiresAt,
        ]
      );

      return sessionId;
    } catch (error) {
      console.error("Create session error:", error);
      return null;
    }
  }

  // Remove session
  async removeSession(tokenHash) {
    try {
      await this.databaseManager.executeQuery(
        "DELETE FROM pos_sessions WHERE token_hash = ?",
        [tokenHash]
      );
    } catch (error) {
      console.error("Remove session error:", error);
    }
  }

  // Update login attempt
  async updateLoginAttempt(staffId, success = false) {
    try {
      if (success) {
        await this.databaseManager.executeQuery(
          "UPDATE pos_staff SET last_login = NOW(), failed_login_attempts = 0, locked_until = NULL WHERE id = ?",
          [staffId]
        );
      } else {
        await this.databaseManager.executeQuery(
          `UPDATE pos_staff 
           SET failed_login_attempts = failed_login_attempts + 1,
               locked_until = CASE 
                 WHEN failed_login_attempts >= 4 THEN DATE_ADD(NOW(), INTERVAL 15 MINUTE)
                 ELSE locked_until
               END
           WHERE id = ?`,
          [staffId]
        );
      }
    } catch (error) {
      console.error("Update login attempt error:", error);
    }
  }
}

const authService = new AuthService();

// Middleware to authenticate JWT tokens
const authenticateToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({
        success: false,
        error: "No token provided",
      });
    }

    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "your-secret-key"
    );

    // Create token hash for session lookup
    const tokenHash = crypto.createHash("sha256").update(token).digest("hex");

    // Validate session
    const session = await authService.validateSession(tokenHash);
    if (!session) {
      return res.status(401).json({
        success: false,
        error: "Invalid or expired session",
      });
    }

    // Get user with latest info and permissions
    const user = await authService.getStaffWithPermissions(decoded.userId);
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: "User not found or inactive",
      });
    }

    // Check if account is locked
    if (user.lockedUntil && new Date() < new Date(user.lockedUntil)) {
      return res.status(423).json({
        success: false,
        error: "Account temporarily locked due to failed login attempts",
      });
    }

    // Add user info to request (maintain compatibility)
    req.user = {
      id: user.id,
      username: user.username,
      name: user.name,
      role: user.role,
      storeId: user.storeId,
      permissions: user.permissions,
      terminalId: session.terminal_id,
      locationId: session.location_id,
    };

    next();
  } catch (error) {
    console.error("Token verification error:", error);
    res.status(401).json({
      success: false,
      error: "Invalid token",
    });
  }
};

// Middleware to check specific permissions (unchanged)
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: "Authentication required",
      });
    }

    if (!req.user.permissions.includes(permission)) {
      return res.status(403).json({
        success: false,
        error: `Permission '${permission}' required`,
      });
    }

    next();
  };
};

// Middleware to check role (unchanged)
const requireRole = (role) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: "Authentication required",
      });
    }

    if (req.user.role !== role) {
      return res.status(403).json({
        success: false,
        error: `Role '${role}' required`,
      });
    }

    next();
  };
};

module.exports = {
  authenticateToken,
  requirePermission,
  requireRole,
  authService,
};
