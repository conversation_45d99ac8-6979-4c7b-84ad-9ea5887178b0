/**
 * Product Query Hooks
 * 
 * TanStack React Query hooks for product-related API operations.
 * Provides caching, background updates, and optimistic updates.
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { getAPIClient } from '@/src/services/api/dukalink-client';
import { queryKeys } from '@/src/lib/queryKeys';
import { Product } from '@/src/types/shopify';

// Types for query parameters
interface ProductListParams {
  page?: number;
  limit?: number;
  search?: string;
  cursor?: string;
}

interface ProductSearchParams {
  query: string;
  limit?: number;
}

// Hook to fetch products with pagination
export const useProducts = (params: ProductListParams = {}) => {
  return useQuery({
    queryKey: queryKeys.products.list(params),
    queryFn: async () => {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreProducts(params);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch products');
      }
      
      return {
        products: response.data?.products || [],
        pagination: response.data?.pagination || {
          page: 1,
          limit: params.limit || 20,
          hasNext: false,
          hasPrev: false,
          total: 0,
        },
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });
};

// Hook for infinite scrolling products
export const useInfiniteProducts = (params: Omit<ProductListParams, 'page' | 'cursor'> = {}) => {
  return useInfiniteQuery({
    queryKey: queryKeys.products.list({ ...params, infinite: true }),
    queryFn: async ({ pageParam }) => {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreProducts({
        ...params,
        cursor: pageParam,
      });
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch products');
      }
      
      return {
        products: response.data?.products || [],
        pagination: response.data?.pagination,
        nextCursor: response.data?.pagination?.endCursor,
      };
    },
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 30,
  });
};

// Hook to fetch a single product
export const useProduct = (productId: string | null) => {
  return useQuery({
    queryKey: queryKeys.products.detail(productId || ''),
    queryFn: async () => {
      if (!productId) throw new Error('Product ID is required');
      
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreProduct(productId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch product');
      }
      
      return response.data?.product;
    },
    enabled: !!productId,
    staleTime: 1000 * 60 * 10, // 10 minutes for individual products
    gcTime: 1000 * 60 * 60, // 1 hour
  });
};

// Hook to search products
export const useProductSearch = (params: ProductSearchParams) => {
  return useQuery({
    queryKey: queryKeys.products.search(params.query),
    queryFn: async () => {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreProducts({
        search: params.query,
        limit: params.limit || 50,
      });
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to search products');
      }
      
      return response.data?.products || [];
    },
    enabled: !!params.query && params.query.length > 0,
    staleTime: 1000 * 60 * 2, // 2 minutes for search results
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
};

// Hook to get product inventory
export const useProductInventory = (productId: string | null) => {
  return useQuery({
    queryKey: queryKeys.products.inventory(productId || ''),
    queryFn: async () => {
      if (!productId) throw new Error('Product ID is required');
      
      const apiClient = getAPIClient();
      const response = await apiClient.getProductInventory(productId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch product inventory');
      }
      
      return response.data;
    },
    enabled: !!productId,
    staleTime: 1000 * 30, // 30 seconds for inventory (more frequent updates)
    gcTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: 1000 * 60, // Refetch every minute for real-time inventory
  });
};

// Mutation hook to update product inventory
export const useUpdateProductInventory = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ productId, quantity }: { productId: string; quantity: number }) => {
      const apiClient = getAPIClient();
      const response = await apiClient.updateProductInventory(productId, quantity);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to update product inventory');
      }
      
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch product inventory
      queryClient.invalidateQueries({
        queryKey: queryKeys.products.inventory(variables.productId),
      });
      
      // Invalidate product details
      queryClient.invalidateQueries({
        queryKey: queryKeys.products.detail(variables.productId),
      });
      
      // Invalidate product lists
      queryClient.invalidateQueries({
        queryKey: queryKeys.products.lists(),
      });
    },
  });
};

// Hook to prefetch products for better UX
export const usePrefetchProducts = () => {
  const queryClient = useQueryClient();
  
  const prefetchProducts = (params: ProductListParams = {}) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.products.list(params),
      queryFn: async () => {
        const apiClient = getAPIClient();
        const response = await apiClient.getStoreProducts(params);
        
        if (!response.success) {
          throw new Error(response.error || 'Failed to fetch products');
        }
        
        return {
          products: response.data?.products || [],
          pagination: response.data?.pagination,
        };
      },
      staleTime: 1000 * 60 * 5,
    });
  };
  
  const prefetchProduct = (productId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.products.detail(productId),
      queryFn: async () => {
        const apiClient = getAPIClient();
        const response = await apiClient.getStoreProduct(productId);
        
        if (!response.success) {
          throw new Error(response.error || 'Failed to fetch product');
        }
        
        return response.data?.product;
      },
      staleTime: 1000 * 60 * 10,
    });
  };
  
  return { prefetchProducts, prefetchProduct };
};
