/**
 * Test MySQL Authentication System
 */

require('dotenv').config();
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function testMySQLAuth() {
  let connection = null;
  
  try {
    console.log('🔧 Testing MySQL Authentication...');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'dukalink',
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || 'dukalink_pos',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to MySQL database');
    
    // Test getting staff by username
    const [rows] = await connection.execute(
      'SELECT * FROM pos_staff WHERE username = ?',
      ['admin1']
    );
    
    if (rows.length === 0) {
      console.log('❌ Admin user not found in database');
      return;
    }
    
    const staff = rows[0];
    console.log('✅ Found admin user:', {
      id: staff.id,
      username: staff.username,
      name: staff.name,
      role: staff.role,
      is_active: staff.is_active
    });
    
    // Test password verification
    const passwordValid = await bcrypt.compare('admin123', staff.password_hash);
    console.log('✅ Password verification:', passwordValid ? 'VALID' : 'INVALID');
    
    // Test getting permissions
    const [permissionRows] = await connection.execute(
      'SELECT permission FROM staff_permissions WHERE staff_id = ?',
      [staff.id]
    );
    
    const permissions = permissionRows.map(row => row.permission);
    console.log('✅ Admin permissions:', permissions.length, 'permissions');
    console.log('   Sample permissions:', permissions.slice(0, 5));
    
    console.log('\n🎉 MySQL Authentication test completed successfully!');
    
  } catch (error) {
    console.error('❌ MySQL Authentication test failed:', error.message);
    console.error('   Stack:', error.stack);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run test
testMySQLAuth();
