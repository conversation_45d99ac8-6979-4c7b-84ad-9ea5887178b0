/**
 * TanStack React Query Provider Setup
 *
 * Provides QueryClient configuration optimized for React Native POS application
 * with proper error handling, network awareness, and cache management.
 */

import React, { ReactNode } from "react";
import {
  QueryClient,
  QueryClientProvider as TanStackQueryClientProvider,
} from "@tanstack/react-query";
import { onlineManager } from "@tanstack/react-query";
import NetInfo from "@react-native-community/netinfo";

interface QueryClientProviderProps {
  children: ReactNode;
}

// Create QueryClient with optimized configuration for React Native POS
const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Cache data for 5 minutes by default
        staleTime: 1000 * 60 * 5,
        // Keep data in cache for 30 minutes
        gcTime: 1000 * 60 * 30,
        // Retry failed requests 2 times
        retry: 2,
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch on window focus for fresh data
        refetchOnWindowFocus: true,
        // Refetch when coming back online
        refetchOnReconnect: true,
        // Don't refetch on mount if data is fresh
        refetchOnMount: true,
        // Network mode for offline scenarios
        networkMode: "online",
      },
      mutations: {
        // Retry mutations once on failure
        retry: 1,
        // Network mode for offline scenarios
        networkMode: "online",
        // Mutation retry delay
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
      },
    },
    // Custom logger for development
    logger: {
      log: (...args) => {
        if (__DEV__) {
          console.log("[React Query]", ...args);
        }
      },
      warn: (...args) => {
        if (__DEV__) {
          console.warn("[React Query]", ...args);
        }
      },
      error: (...args) => {
        console.error("[React Query]", ...args);
      },
    },
  });
};

// Singleton QueryClient instance
let queryClient: QueryClient | undefined;

const getQueryClient = () => {
  if (!queryClient) {
    queryClient = createQueryClient();
  }
  return queryClient;
};

// Setup network state listener for React Native
const setupNetworkListener = () => {
  onlineManager.setEventListener((setOnline) => {
    return NetInfo.addEventListener((state) => {
      setOnline(!!state.isConnected);
    });
  });
};

export const QueryClientProvider: React.FC<QueryClientProviderProps> = ({
  children,
}) => {
  const client = getQueryClient();

  // Setup network listener on mount
  React.useEffect(() => {
    setupNetworkListener();
  }, []);

  return (
    <TanStackQueryClientProvider client={client}>
      {children}
    </TanStackQueryClientProvider>
  );
};

// Export the QueryClient instance for use in services
export const getQueryClientInstance = () => getQueryClient();

// Export query client for direct access when needed
export { queryClient };
