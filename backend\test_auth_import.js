/**
 * Test Auth Import
 */

try {
  console.log('🔧 Testing auth imports...');
  
  // Test middleware import
  const { authService } = require('./src/middleware/auth');
  console.log('✅ Auth middleware imported successfully');
  
  // Test auth service
  console.log('✅ Auth service available:', typeof authService);
  
  // Test route import
  const posAuthRoute = require('./src/routes/pos-auth');
  console.log('✅ POS auth route imported successfully');
  
  console.log('🎉 All imports successful!');
  
} catch (error) {
  console.error('❌ Import failed:', error.message);
  console.error('   Stack:', error.stack);
}
