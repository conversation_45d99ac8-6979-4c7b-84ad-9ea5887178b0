/**
 * Complete MySQL Authentication System Test
 */

require('dotenv').config();
const { authService } = require('./src/middleware/auth-mysql');
const bcrypt = require('bcrypt');

async function testMySQLAuthComplete() {
  try {
    console.log('🔧 Testing Complete MySQL Authentication System...\n');
    
    // Test 1: Get staff by username
    console.log('📋 Test 1: Get Staff by Username');
    const staff = await authService.getStaffByUsername('admin1');
    
    if (staff) {
      console.log('✅ PASSED: Get Staff by Username');
      console.log(`   Found: ${staff.name} (${staff.username})`);
      console.log(`   Role: ${staff.role}`);
      console.log(`   Permissions: ${staff.permissions.length} permissions`);
      console.log(`   Active: ${staff.isActive}`);
    } else {
      console.log('❌ FAILED: Get Staff by Username - Staff not found');
      return;
    }
    
    console.log('');
    
    // Test 2: Password verification
    console.log('📋 Test 2: Password Verification');
    const passwordValid = await bcrypt.compare('admin123', staff.passwordHash);
    
    if (passwordValid) {
      console.log('✅ PASSED: Password Verification');
      console.log('   Password matches hash');
    } else {
      console.log('❌ FAILED: Password Verification');
      console.log('   Password does not match hash');
    }
    
    console.log('');
    
    // Test 3: Get staff with permissions
    console.log('📋 Test 3: Get Staff with Permissions');
    const staffWithPerms = await authService.getStaffWithPermissions(staff.id);
    
    if (staffWithPerms) {
      console.log('✅ PASSED: Get Staff with Permissions');
      console.log(`   Staff: ${staffWithPerms.name}`);
      console.log(`   Permissions: ${staffWithPerms.permissions.join(', ')}`);
    } else {
      console.log('❌ FAILED: Get Staff with Permissions');
    }
    
    console.log('');
    
    // Test 4: Create session
    console.log('📋 Test 4: Create Session');
    const tokenHash = 'test-token-hash-' + Date.now();
    const sessionId = await authService.createSession(staff.id, tokenHash, {
      terminalId: 'terminal-001',
      locationId: 'location-001',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Agent'
    });
    
    if (sessionId) {
      console.log('✅ PASSED: Create Session');
      console.log(`   Session ID: ${sessionId}`);
    } else {
      console.log('❌ FAILED: Create Session');
    }
    
    console.log('');
    
    // Test 5: Validate session
    console.log('📋 Test 5: Validate Session');
    const session = await authService.validateSession(tokenHash);
    
    if (session) {
      console.log('✅ PASSED: Validate Session');
      console.log(`   Staff ID: ${session.staff_id}`);
      console.log(`   Terminal: ${session.terminal_id}`);
    } else {
      console.log('❌ FAILED: Validate Session');
    }
    
    console.log('');
    
    // Test 6: Update login attempt (success)
    console.log('📋 Test 6: Update Login Attempt (Success)');
    await authService.updateLoginAttempt(staff.id, true);
    console.log('✅ PASSED: Update Login Attempt (Success)');
    
    console.log('');
    
    // Test 7: Remove session
    console.log('📋 Test 7: Remove Session');
    await authService.removeSession(tokenHash);
    console.log('✅ PASSED: Remove Session');
    
    console.log('');
    
    // Test 8: Validate removed session (should fail)
    console.log('📋 Test 8: Validate Removed Session');
    const removedSession = await authService.validateSession(tokenHash);
    
    if (!removedSession) {
      console.log('✅ PASSED: Validate Removed Session (correctly failed)');
    } else {
      console.log('❌ FAILED: Validate Removed Session (should have failed)');
    }
    
    console.log('\n🎉 MySQL Authentication System testing completed!');
    console.log('✅ All core authentication functions are working correctly.');
    console.log('🚀 Ready to switch to MySQL authentication!');
    
  } catch (error) {
    console.error('❌ MySQL Authentication test failed:', error.message);
    console.error('   Stack:', error.stack);
  }
}

// Run test
testMySQLAuthComplete();
