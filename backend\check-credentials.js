/**
 * Check Seeded Credentials
 * Verify what usernames and passwords were actually seeded
 */

require('dotenv').config();
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function checkCredentials() {
  let connection;
  
  try {
    console.log('🔍 Checking seeded credentials...\n');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'dukalink',
      password: process.env.DB_PASSWORD || 'dukalink_secure_password_2024',
      database: process.env.DB_NAME || 'dukalink_pos',
      charset: 'utf8mb4'
    });

    console.log(`✅ Connected to database: ${process.env.DB_NAME}`);
    
    // Get all staff users
    const [staff] = await connection.execute(`
      SELECT id, username, password_hash, name, email, role, is_active 
      FROM pos_staff 
      ORDER BY role DESC
    `);
    
    console.log('\n👥 Seeded Staff Users:');
    console.log('='.repeat(80));
    
    for (const user of staff) {
      console.log(`Username: ${user.username}`);
      console.log(`Name: ${user.name}`);
      console.log(`Email: ${user.email}`);
      console.log(`Role: ${user.role}`);
      console.log(`Active: ${user.is_active ? 'Yes' : 'No'}`);
      console.log(`Password Hash: ${user.password_hash.substring(0, 20)}...`);
      
      // Test the passwords we think should work
      const testPasswords = ['admin123', 'manager123', 'password123'];
      
      console.log('Testing passwords:');
      for (const testPassword of testPasswords) {
        try {
          const isValid = await bcrypt.compare(testPassword, user.password_hash);
          if (isValid) {
            console.log(`  ✅ Password "${testPassword}" works for ${user.username}`);
          } else {
            console.log(`  ❌ Password "${testPassword}" does NOT work for ${user.username}`);
          }
        } catch (error) {
          console.log(`  ⚠️  Error testing password "${testPassword}": ${error.message}`);
        }
      }
      
      console.log('-'.repeat(80));
    }
    
    // Get permissions for each user
    console.log('\n🔐 User Permissions:');
    const [permissions] = await connection.execute(`
      SELECT s.username, s.role, GROUP_CONCAT(sp.permission) as permissions
      FROM pos_staff s
      LEFT JOIN staff_permissions sp ON s.id = sp.staff_id
      GROUP BY s.id, s.username, s.role
      ORDER BY s.role DESC
    `);
    
    permissions.forEach(user => {
      console.log(`${user.username} (${user.role}): ${user.permissions || 'No permissions'}`);
    });
    
  } catch (error) {
    console.error('❌ Credential check failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Disconnected from database');
    }
  }
}

checkCredentials();
