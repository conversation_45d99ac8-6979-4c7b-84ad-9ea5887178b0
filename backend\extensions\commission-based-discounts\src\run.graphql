query Input {
  cart {
    lines {
      id
      quantity
      cost {
        amountPerQuantity {
          amount
        }
        subtotalAmount {
          amount
        }
      }
      merchandise {
        __typename
        ... on ProductVariant {
          id
          product {
            id
            title
            tags
            metafield(namespace: "dukalink", key: "commission_eligible") {
              value
            }
          }
        }
      }
    }
    cost {
      subtotalAmount {
        amount
      }
      totalAmount {
        amount
      }
    }
    buyerIdentity {
      customer {
        id
        tags
        metafield(namespace: "dukalink", key: "sales_agent_id") {
          value
        }
        loyaltyTier: metafield(namespace: "dukalink_loyalty", key: "tier_level") {
          value
        }
        loyaltyPoints: metafield(namespace: "dukalink_loyalty", key: "points_balance") {
          value
        }
        totalPurchases: metafield(namespace: "dukalink_loyalty", key: "total_purchases") {
          value
        }
        totalOrders: metafield(namespace: "dukalink_loyalty", key: "total_orders") {
          value
        }
        lastPurchase: metafield(namespace: "dukalink_loyalty", key: "last_purchase_date") {
          value
        }
        # Legacy support for existing metafield
        legacyLoyaltyTier: metafield(namespace: "dukalink", key: "loyalty_tier") {
          value
        }
      }
    }
    attribute(key: "staff_id") {
      value
    }
    attribute(key: "sales_agent_id") {
      value
    }
  }
  discountNode {
    metafield(namespace: "$app:commission-discounts", key: "function-configuration") {
      jsonValue
    }
  }
}
