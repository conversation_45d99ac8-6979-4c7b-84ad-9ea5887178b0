import { useFocusEffect, useNavigationState } from "@react-navigation/native";
import { usePathname, useRouter, useSegments } from "expo-router";
import React, {
  createContext,
  PropsWithChildren,
  useCallback,
  useContext,
  useState,
} from "react";

interface NavigationState {
  currentRoute: string;
  currentTitle: string;
  canGoBack: boolean;
  isInCheckoutFlow: boolean;
  routeParams: Record<string, any>;
}

interface NavigationContextType {
  sidebarVisible: boolean;
  openSidebar: () => void;
  closeSidebar: () => void;
  toggleSidebar: () => void;
  currentTitle: string;
  setCurrentTitle: (title: string) => void;
  navigationState: NavigationState;
  canGoBack: boolean;
  isInCheckoutFlow: boolean;
  currentRoute: string;
}

// Route configuration types
interface RouteConfig {
  title: string;
  showBackButton?: boolean;
  isMainScreen?: boolean;
  isCheckoutFlow?: boolean;
  hideHeader?: boolean;
}

// Route configuration for automatic title detection and navigation behavior
const ROUTE_CONFIG: Record<string, RouteConfig> = {
  // Main tab screens - show menu button
  "(tabs)": { title: "Dashboard", showBackButton: false, isMainScreen: true },
  "(tabs)/index": {
    title: "Dashboard",
    showBackButton: false,
    isMainScreen: true,
  },
  "(tabs)/products": {
    title: "Products",
    showBackButton: false,
    isMainScreen: true,
  },
  "(tabs)/cart": { title: "Cart", showBackButton: false, isMainScreen: true },
  "(tabs)/orders": {
    title: "Orders",
    showBackButton: false,
    isMainScreen: true,
  },

  // Detail/form screens - show back button
  "customer-list": {
    title: "Customer Management",
    showBackButton: true,
    isCheckoutFlow: false,
  },
  "customer-details": {
    title: "Customer Details",
    showBackButton: true,
    isCheckoutFlow: false,
  },
  "sales-agent-list": {
    title: "Sales Agent Management",
    showBackButton: true,
    isCheckoutFlow: false,
  },
  "sales-agent-details": {
    title: "Sales Agent Details",
    showBackButton: true,
    isCheckoutFlow: false,
  },
  "location-selection": {
    title: "Select Location",
    showBackButton: true,
    isCheckoutFlow: true,
  },
  "payment-method-selection": {
    title: "Payment Method",
    showBackButton: true,
    isCheckoutFlow: true,
  },
  checkout: { title: "Checkout", showBackButton: true, isCheckoutFlow: false },
  "payment-processing": {
    title: "Processing Payment",
    showBackButton: false,
    isCheckoutFlow: true,
  },
  "order-receipt": {
    title: "Receipt",
    showBackButton: true,
    isCheckoutFlow: false,
  },
  settings: { title: "Settings", showBackButton: true, isMainScreen: false },
  "staff-list": {
    title: "Staff Management",
    showBackButton: true,
    isMainScreen: false,
  },
  "sales-agent-create": {
    title: "Create Sales Agent",
    showBackButton: true,
    isMainScreen: false,
  },
  "customer-create": {
    title: "Add Customer",
    showBackButton: true,
    isMainScreen: false,
  },

  // Auth screens - no header
  auth: { title: "Authentication", showBackButton: false, hideHeader: true },
  "pos-login": { title: "POS Login", showBackButton: false, hideHeader: true },
};

const NavigationContext = createContext<NavigationContextType>({
  sidebarVisible: false,
  openSidebar: () => {},
  closeSidebar: () => {},
  toggleSidebar: () => {},
  currentTitle: "",
  setCurrentTitle: () => {},
  navigationState: {
    currentRoute: "",
    currentTitle: "",
    canGoBack: false,
    isInCheckoutFlow: false,
    routeParams: {},
  },
  canGoBack: false,
  isInCheckoutFlow: false,
  currentRoute: "",
});

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error("useNavigation must be used within a NavigationProvider");
  }
  return context;
}

export function NavigationProvider({ children }: PropsWithChildren) {
  const segments = useSegments();
  const pathname = usePathname();
  const router = useRouter();

  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [currentTitle, setCurrentTitle] = useState("Dashboard");
  const [routeTitles, setRouteTitles] = useState<Record<string, string>>({});
  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentRoute: "",
    currentTitle: "",
    canGoBack: false,
    isInCheckoutFlow: false,
    routeParams: {},
  });

  // Use React Navigation's useNavigationState hook for better state tracking
  const navState = useNavigationState((state) => state);

  const openSidebar = () => setSidebarVisible(true);
  const closeSidebar = () => setSidebarVisible(false);
  const toggleSidebar = () => setSidebarVisible(!sidebarVisible);

  // Determine route key from segments and pathname
  const getCurrentRouteKey = useCallback((): string => {
    // Handle tab routes
    if (segments[0] === "(tabs)") {
      if (segments.length === 1) return "(tabs)";
      return `(tabs)/${segments[1]}`;
    }

    // For non-tab routes, try exact match first, then fall back to first segment
    const fullPath = segments.join("/");
    const firstSegment = segments[0];

    // Check if full path exists in config, then first segment, then use full path
    if (ROUTE_CONFIG[fullPath]) {
      return fullPath;
    } else if (ROUTE_CONFIG[firstSegment]) {
      return firstSegment;
    }
    return fullPath;
  }, [segments]);

  // Enhanced setCurrentTitle that stores titles per route
  const setCurrentTitleEnhanced = useCallback(
    (title: string) => {
      const currentRouteKey = getCurrentRouteKey();
      setRouteTitles((prev) => ({ ...prev, [currentRouteKey]: title }));
      setCurrentTitle(title);
    },
    [getCurrentRouteKey]
  );

  // Use useFocusEffect instead of useEffect for better route change detection
  useFocusEffect(
    useCallback(() => {
      const routeKey = getCurrentRouteKey();
      const routeConfig: RouteConfig | undefined = ROUTE_CONFIG[routeKey];

      // Improved canGoBack logic using React Navigation state
      let canGoBack: boolean;
      if (routeKey === "checkout") {
        // Special handling for checkout - always show back button to return to products
        canGoBack = true;
      } else if (navState && navState.routes.length > 1) {
        // Use navigation state to determine if we can go back
        canGoBack = navState.index > 0;
      } else if (!routeConfig) {
        // For unknown routes, determine based on route pattern
        const isTabRoute = routeKey.startsWith("(tabs)");
        const isAuthRoute =
          routeKey.includes("auth") || routeKey.includes("login");
        canGoBack = !isTabRoute && !isAuthRoute;
      } else {
        // Use explicit showBackButton if available, otherwise use isMainScreen logic
        canGoBack =
          routeConfig.showBackButton !== undefined
            ? routeConfig.showBackButton
            : !routeConfig.isMainScreen;
      }

      // Determine if in checkout flow
      const isInCheckoutFlow = routeConfig?.isCheckoutFlow ?? false;

      // Use route-specific manual title if set, otherwise use route config title
      const routeSpecificTitle = routeTitles[routeKey];
      const autoTitle = routeConfig?.title ?? "Dashboard";
      const finalTitle = routeSpecificTitle || autoTitle;

      const newNavigationState: NavigationState = {
        currentRoute: routeKey,
        currentTitle: finalTitle,
        canGoBack,
        isInCheckoutFlow,
        routeParams: {},
      };

      setNavigationState(newNavigationState);
      setCurrentTitle(finalTitle);

      console.log("Navigation state updated (improved):", {
        routeKey,
        segments,
        pathname,
        finalTitle,
        canGoBack,
        isInCheckoutFlow,
        navStateIndex: navState?.index,
        navStateRoutesLength: navState?.routes.length,
        routeConfig: routeConfig ? "found" : "not found",
      });

      return () => {
        // Cleanup function called when screen loses focus
        console.log("Screen lost focus:", routeKey);
      };
    }, [segments, pathname, navState, routeTitles, getCurrentRouteKey])
  );

  return (
    <NavigationContext.Provider
      value={{
        sidebarVisible,
        openSidebar,
        closeSidebar,
        toggleSidebar,
        currentTitle,
        setCurrentTitle: setCurrentTitleEnhanced,
        navigationState,
        canGoBack: navigationState.canGoBack,
        isInCheckoutFlow: navigationState.isInCheckoutFlow,
        currentRoute: navigationState.currentRoute,
      }}
    >
      {children}
    </NavigationContext.Provider>
  );
}
