import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  Alert,
  StyleSheet,
  ActivityIndicator,
  Modal,
  ScrollView,
} from "react-native";
import { useRouter, useFocusEffect } from "expo-router";
import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ModernCard } from "@/components/ui/ModernCard";
import { ModernButton } from "@/components/ui/ModernButton";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { FloatingActionButton } from "@/src/components/ui/FloatingActionButton";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useSession } from "@/src/contexts/AuthContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { useAppDispatch, useAppSelector } from "@/src/store";
import { fetchCustomers } from "@/src/store/slices/customerSlice";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { Customer } from "@/src/types/shopify";
import { Colors } from "@/constants/Colors";
import { loyaltyService } from "@/src/services/loyalty-service";
import { CustomerLoyaltyData } from "@/src/components/loyalty/CustomerLoyaltyCard";
import {
  getEnhancedLoyaltyData,
  getLoyaltyDataSource,
  extractLoyaltyPointsFromNote,
  getEffectiveLoyaltyPoints,
} from "@/src/utils/loyaltyUtils";

interface NewCustomerData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export default function CustomerListScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { setCurrentTitle } = useNavigation();
  const { isPosAuthenticated } = useSession();
  const { canManageCustomers } = useRBAC();

  const { customers, isLoading } = useAppSelector((state) => state.customers);

  const [searchQuery, setSearchQuery] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [loyaltyData, setLoyaltyData] = useState<
    Record<string, CustomerLoyaltyData>
  >({});
  const [loadingLoyalty, setLoadingLoyalty] = useState(false);
  const [newCustomer, setNewCustomer] = useState<NewCustomerData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
  });

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");

  useEffect(() => {
    setCurrentTitle("Customer Management");
  }, [setCurrentTitle]);

  const loadCustomers = useCallback(() => {
    if (isPosAuthenticated) {
      dispatch(
        fetchCustomers({
          search: searchQuery,
          includeLoyalty: true, // Always include loyalty data for enhanced display
        })
      );
    }
  }, [dispatch, searchQuery, isPosAuthenticated]);

  // Extract loyalty data from customers (now included in API response)
  const extractLoyaltyData = useCallback(() => {
    console.log("🔍 Extracting loyalty data from customers:", customers.length);
    const loyaltyMap: Record<string, CustomerLoyaltyData> = {};

    customers.forEach((customer, index) => {
      const enhancedLoyaltyData = getEnhancedLoyaltyData(customer);
      const loyaltySource = getLoyaltyDataSource(customer);

      console.log(
        `   Customer ${index + 1}: ${customer.displayName || customer.email}`,
        {
          id: customer.id,
          hasLoyaltyData: !!customer.loyaltyData,
          loyaltyData: customer.loyaltyData,
          enhancedLoyaltyData,
          loyaltySource,
          notePoints: extractLoyaltyPointsFromNote(customer.note),
        }
      );

      if (enhancedLoyaltyData) {
        loyaltyMap[customer.id] = enhancedLoyaltyData;
      }
    });

    console.log(
      "📊 Loyalty map created:",
      Object.keys(loyaltyMap).length,
      "entries"
    );
    setLoyaltyData(loyaltyMap);
  }, [customers]);

  useEffect(() => {
    loadCustomers();
  }, [loadCustomers]);

  // Extract loyalty data when customers change
  useEffect(() => {
    if (customers.length > 0 && !isLoading) {
      extractLoyaltyData();
    }
  }, [customers, isLoading, extractLoyaltyData]);

  // Refresh customers when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (isPosAuthenticated) {
        loadCustomers();
      }
    }, [loadCustomers, isPosAuthenticated])
  );

  const handleCustomerPress = (customer: Customer) => {
    router.push({
      pathname: "/customer-details",
      params: { customerId: customer.id },
    });
  };

  const handleCreateCustomer = async () => {
    if (!newCustomer.firstName.trim() || !newCustomer.lastName.trim()) {
      Alert.alert("Error", "Please enter first and last name");
      return;
    }

    setIsCreating(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.createStoreCustomer(newCustomer);

      if (response.success && response.data) {
        setShowAddModal(false);
        setNewCustomer({ firstName: "", lastName: "", email: "", phone: "" });
        Alert.alert("Success", "Customer created successfully");
        loadCustomers(); // Refresh the list
      } else {
        Alert.alert("Error", response.error || "Failed to create customer");
      }
    } catch (error) {
      console.error("Failed to create customer:", error);
      Alert.alert("Error", "Failed to create customer");
    } finally {
      setIsCreating(false);
    }
  };

  const filteredCustomers = customers.filter(
    (customer) =>
      (customer.displayName || "")
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      (customer.email &&
        customer.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (customer.phone && customer.phone.includes(searchQuery))
  );

  // Helper function to get tier badge color
  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case "bronze":
        return "#CD7F32";
      case "silver":
        return "#C0C0C0";
      case "gold":
        return "#FFD700";
      case "platinum":
        return "#E5E4E2";
      default:
        return Colors.light.textSecondary;
    }
  };

  // Helper function to render loyalty tier badge
  const renderLoyaltyBadge = (customerId: string) => {
    const loyalty = loyaltyData[customerId];
    console.log(`🏷️ Rendering loyalty badge for ${customerId}:`, loyalty);
    if (!loyalty) return null;

    return (
      <View
        style={[
          styles.loyaltyBadge,
          { backgroundColor: getTierBadgeColor(loyalty.tier) },
        ]}
      >
        <IconSymbol name="star.fill" size={12} color="#FFFFFF" />
        <Text style={styles.loyaltyBadgeText}>
          {loyalty.tier.charAt(0).toUpperCase() + loyalty.tier.slice(1)}
        </Text>
      </View>
    );
  };

  // Helper function to render loyalty points
  const renderLoyaltyPoints = (customerId: string) => {
    const loyalty = loyaltyData[customerId];
    console.log(`⭐ Rendering loyalty points for ${customerId}:`, loyalty);
    if (!loyalty) return null;

    return (
      <View style={styles.loyaltyPoints}>
        <IconSymbol name="star" size={14} color={Colors.light.primary} />
        <Text
          style={[styles.loyaltyPointsText, { color: Colors.light.primary }]}
        >
          {(loyalty.loyaltyPoints || 0).toLocaleString()} pts
        </Text>
      </View>
    );
  };

  const renderCustomer = ({ item: customer }: { item: Customer }) => (
    <TouchableOpacity onPress={() => handleCustomerPress(customer)}>
      <ModernCard style={styles.customerCard} variant="outlined">
        <View style={styles.customerInfo}>
          <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
            <Text style={styles.avatarText}>
              {(customer.firstName || "").charAt(0)}
              {(customer.lastName || "").charAt(0)}
            </Text>
          </View>

          <View style={styles.customerDetails}>
            <View style={styles.customerNameRow}>
              <Text style={[styles.customerName, { color: textColor }]}>
                {customer.displayName}
              </Text>
              {renderLoyaltyBadge(customer.id)}
            </View>

            {customer.email && (
              <View style={styles.contactRow}>
                <IconSymbol name="envelope" size={14} color={textSecondary} />
                <Text style={[styles.contactText, { color: textSecondary }]}>
                  {customer.email}
                </Text>
              </View>
            )}

            {customer.phone && (
              <View style={styles.contactRow}>
                <IconSymbol name="phone" size={14} color={textSecondary} />
                <Text style={[styles.contactText, { color: textSecondary }]}>
                  {customer.phone}
                </Text>
              </View>
            )}

            <View style={styles.statsRow}>
              <Text style={[styles.statsText, { color: textSecondary }]}>
                {customer.ordersCount || 0} orders • KSh{" "}
                {parseFloat(customer.totalSpent || "0").toFixed(2)} spent
              </Text>
              {renderLoyaltyPoints(customer.id)}
            </View>
          </View>

          <View style={styles.chevronContainer}>
            <IconSymbol name="chevron.right" size={20} color={textSecondary} />
          </View>
        </View>
      </ModernCard>
    </TouchableOpacity>
  );

  if (!isPosAuthenticated) {
    return null;
  }

  return (
    <ScreenWrapper title="Customer Management" showBackButton>
      <View style={[styles.container, { backgroundColor }]}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <TextInput
              style={[
                styles.searchInput,
                {
                  backgroundColor: surfaceColor,
                  borderColor: Colors.light.border,
                  color: textColor,
                },
              ]}
              placeholder="Search customers..."
              placeholderTextColor={textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <IconSymbol
              name="magnifyingglass"
              size={20}
              color={textSecondary}
              style={styles.searchIcon}
            />
          </View>
        </View>

        {/* Customers List */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.loadingText, { color: textColor }]}>
              Loading customers...
            </Text>
          </View>
        ) : filteredCustomers.length === 0 ? (
          <View style={styles.emptyContainer}>
            <IconSymbol name="person.2" size={64} color={textSecondary} />
            <Text style={[styles.emptyTitle, { color: textColor }]}>
              {searchQuery ? "No customers found" : "No customers yet"}
            </Text>
            <Text style={[styles.emptySubtitle, { color: textSecondary }]}>
              {searchQuery
                ? "Try a different search term"
                : "Add your first customer to get started"}
            </Text>
            {!searchQuery && canManageCustomers && (
              <ModernButton
                title="Add First Customer"
                onPress={() => setShowAddModal(true)}
                style={styles.addFirstButton}
              />
            )}
          </View>
        ) : (
          <FlatList
            data={filteredCustomers}
            renderItem={renderCustomer}
            keyExtractor={(item) => item.id}
            style={styles.customersList}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />
        )}

        {/* Floating Action Button */}
        {canManageCustomers && (
          <FloatingActionButton
            onPress={() => setShowAddModal(true)}
            icon="plus"
            style={styles.fab}
          />
        )}

        {/* Add Customer Modal */}
        <Modal
          visible={showAddModal}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowAddModal(false)}
        >
          <View style={[styles.modalContainer, { backgroundColor }]}>
            <View
              style={[styles.modalHeader, { backgroundColor: surfaceColor }]}
            >
              <TouchableOpacity
                onPress={() => setShowAddModal(false)}
                style={styles.modalCloseButton}
              >
                <IconSymbol name="xmark" size={24} color={textColor} />
              </TouchableOpacity>
              <Text style={[styles.modalTitle, { color: textColor }]}>
                Add New Customer
              </Text>
              <View style={styles.modalCloseButton} />
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: textColor }]}>
                  First Name *
                </Text>
                <TextInput
                  style={[
                    styles.modalInput,
                    {
                      backgroundColor: surfaceColor,
                      borderColor: Colors.light.border,
                      color: textColor,
                    },
                  ]}
                  value={newCustomer.firstName}
                  onChangeText={(text) =>
                    setNewCustomer((prev) => ({ ...prev, firstName: text }))
                  }
                  placeholder="Enter first name"
                  placeholderTextColor={textSecondary}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: textColor }]}>
                  Last Name *
                </Text>
                <TextInput
                  style={[
                    styles.modalInput,
                    {
                      backgroundColor: surfaceColor,
                      borderColor: Colors.light.border,
                      color: textColor,
                    },
                  ]}
                  value={newCustomer.lastName}
                  onChangeText={(text) =>
                    setNewCustomer((prev) => ({ ...prev, lastName: text }))
                  }
                  placeholder="Enter last name"
                  placeholderTextColor={textSecondary}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: textColor }]}>
                  Email
                </Text>
                <TextInput
                  style={[
                    styles.modalInput,
                    {
                      backgroundColor: surfaceColor,
                      borderColor: Colors.light.border,
                      color: textColor,
                    },
                  ]}
                  value={newCustomer.email}
                  onChangeText={(text) =>
                    setNewCustomer((prev) => ({ ...prev, email: text }))
                  }
                  placeholder="Enter email address"
                  placeholderTextColor={textSecondary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: textColor }]}>
                  Phone
                </Text>
                <TextInput
                  style={[
                    styles.modalInput,
                    {
                      backgroundColor: surfaceColor,
                      borderColor: Colors.light.border,
                      color: textColor,
                    },
                  ]}
                  value={newCustomer.phone}
                  onChangeText={(text) =>
                    setNewCustomer((prev) => ({ ...prev, phone: text }))
                  }
                  placeholder="Enter phone number"
                  placeholderTextColor={textSecondary}
                  keyboardType="phone-pad"
                />
              </View>

              <ModernButton
                title={isCreating ? "Creating..." : "Create Customer"}
                onPress={handleCreateCustomer}
                loading={isCreating}
                disabled={isCreating}
                style={styles.createButton}
              />
            </ScrollView>
          </View>
        </Modal>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    padding: 16,
  },
  searchInputContainer: {
    position: "relative",
  },
  searchInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingRight: 40,
    fontSize: 16,
  },
  searchIcon: {
    position: "absolute",
    right: 16,
    top: "50%",
    marginTop: -10,
  },
  customersList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  listContent: {
    paddingBottom: 100, // Space for FAB
  },
  customerCard: {
    marginBottom: 12,
    padding: 16,
  },
  customerInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  avatarText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  customerDetails: {
    flex: 1,
  },
  customerNameRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  customerName: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  contactRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    marginBottom: 2,
  },
  contactText: {
    fontSize: 13,
  },
  statsRow: {
    marginTop: 4,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  statsText: {
    fontSize: 12,
    flex: 1,
  },
  loyaltyBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    gap: 4,
  },
  loyaltyBadgeText: {
    fontSize: 10,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  loyaltyPoints: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  loyaltyPointsText: {
    fontSize: 12,
    fontWeight: "500",
  },
  chevronContainer: {
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  addFirstButton: {
    marginTop: 16,
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  modalInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  createButton: {
    marginTop: 24,
  },
});
