/**
 * API Configuration Constants
 * Centralized configuration for all API endpoints and settings
 */

import { Platform } from "react-native";

// Production API Configuration
export const PRODUCTION_API = {
  baseURL: "https://shopify.dukalink.com/api",
  timeout: 30000,
  retryAttempts: 3,
};

// Local Development API Configuration
export const DEVELOPMENT_API = {
  baseURL:
    Platform.OS === "web"
      ? "http://localhost:3020/api"
      : "http://*************:3020/api",
  timeout: 30000,
  retryAttempts: 3,
};

// Development Online API Configuration
export const DEVELOPMENT_ONLINE_API = {
  baseURL: "https://shopifydev.dukalink.com/api",
  timeout: 30000,
  retryAttempts: 3,
};

// Environment Types
export type ApiEnvironment = "local" | "development" | "production";

// Environment Configuration Map
export const API_ENVIRONMENTS = {
  local: DEVELOPMENT_API,
  development: DEVELOPMENT_ONLINE_API,
  production: PRODUCTION_API,
} as const;

/**
 * API Environment Selection
 *
 * You can control which API environment to use by:
 * 1. Setting EXPO_PUBLIC_API_ENV environment variable
 * 2. Modifying FORCE_API_ENV constant below
 * 3. Default fallback based on NODE_ENV
 *
 * Priority: FORCE_API_ENV > EXPO_PUBLIC_API_ENV > NODE_ENV fallback
 */

// Force a specific environment (useful for testing)
// Set to null to use environment variables or default logic
const FORCE_API_ENV: ApiEnvironment | null = null;

// Get API environment from various sources
const getApiEnvironment = (): ApiEnvironment => {
  // 1. Check if forced environment is set
  if (FORCE_API_ENV) {
    console.log(`🔧 Using FORCED API environment: ${FORCE_API_ENV}`);
    return FORCE_API_ENV;
  }

  // 2. Check environment variable
  const envApiEnv = process.env.EXPO_PUBLIC_API_ENV as ApiEnvironment;
  if (envApiEnv && API_ENVIRONMENTS[envApiEnv]) {
    console.log(
      `🌍 Using API environment from EXPO_PUBLIC_API_ENV: ${envApiEnv}`
    );
    return envApiEnv;
  }

  // 3. Default fallback based on NODE_ENV
  const defaultEnv: ApiEnvironment =
    process.env.NODE_ENV === "development"
      ? "development"
      : process.env.NODE_ENV === "production"
      ? "production"
      : "local";
  console.log(
    `📱 Using default API environment based on NODE_ENV: ${defaultEnv}`
  );
  return defaultEnv;
};

// Get the current API environment
const CURRENT_API_ENV = getApiEnvironment();

console.log("Current API Environment:", CURRENT_API_ENV);

// API Configuration Selection
export const API_CONFIG = {
  // Select API configuration based on environment
  ...API_ENVIRONMENTS[CURRENT_API_ENV],

  // Additional configuration
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },

  // Environment metadata
  environment: CURRENT_API_ENV,
  environmentName: {
    local: "Local Development",
    development: "Development Online",
    production: "Production",
  }[CURRENT_API_ENV],
};

// Environment-specific settings
export const ENV_CONFIG = {
  isDevelopment: __DEV__,
  isProduction: !__DEV__,
  platform: Platform.OS,
  currentEnvironment: CURRENT_API_ENV,
  currentBaseURL: API_CONFIG.baseURL,
};

// Utility Functions
export const API_UTILS = {
  /**
   * Get current API environment info
   */
  getCurrentEnvironment: () => ({
    environment: CURRENT_API_ENV,
    name: API_CONFIG.environmentName,
    baseURL: API_CONFIG.baseURL,
    isLocal: CURRENT_API_ENV === "local",
    isDevelopment: CURRENT_API_ENV === "development",
    isProduction: CURRENT_API_ENV === "production",
  }),

  /**
   * Get all available environments
   */
  getAllEnvironments: () =>
    Object.keys(API_ENVIRONMENTS).map((env) => ({
      key: env as ApiEnvironment,
      name: {
        local: "Local Development",
        development: "Development Online",
        production: "Production",
      }[env as ApiEnvironment],
      baseURL: API_ENVIRONMENTS[env as ApiEnvironment].baseURL,
    })),

  /**
   * Check if current environment is secure (HTTPS)
   */
  isSecureEnvironment: () => API_CONFIG.baseURL.startsWith("https://"),

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig: (env: ApiEnvironment) => API_ENVIRONMENTS[env],

  /**
   * Log current environment info (useful for debugging)
   */
  logEnvironmentInfo: () => {
    const info = API_UTILS.getCurrentEnvironment();
    console.log("🌐 API Environment Info:", {
      environment: info.environment,
      name: info.name,
      baseURL: info.baseURL,
      isSecure: API_UTILS.isSecureEnvironment(),
      platform: Platform.OS,
    });
  },
};

// API Endpoints
export const ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: "/pos/login",
    VERIFY: "/pos/verify",
    LOGOUT: "/pos/logout",
    REFRESH: "/auth/refresh",
  },

  // Staff Management
  STAFF: {
    LIST: "/staff",
    CREATE: "/staff",
    UPDATE: (id: string) => `/staff/${id}`,
    DELETE: (id: string) => `/staff/${id}`,
    DETAILS: (id: string) => `/staff/${id}`,
  },

  // Sales Agents
  SALES_AGENTS: {
    LIST: "/sales-agents",
    CREATE: "/sales-agents",
    UPDATE: (id: string) => `/sales-agents/${id}`,
    DELETE: (id: string) => `/sales-agents/${id}`,
    DETAILS: (id: string) => `/sales-agents/${id}`,
  },

  // Customers
  CUSTOMERS: {
    LIST: "/customers",
    CREATE: "/customers",
    UPDATE: (id: string) => `/customers/${id}`,
    DELETE: (id: string) => `/customers/${id}`,
    DETAILS: (id: string) => `/customers/${id}`,
    SEARCH: "/customers/search",
  },

  // Products
  PRODUCTS: {
    LIST: "/products",
    DETAILS: (id: string) => `/products/${id}`,
    SEARCH: "/products/search",
    INVENTORY: (id: string) => `/products/${id}/inventory`,
  },

  // Orders
  ORDERS: {
    LIST: "/orders",
    CREATE: "/orders",
    DETAILS: (id: string) => `/orders/${id}`,
    UPDATE: (id: string) => `/orders/${id}`,
    CANCEL: (id: string) => `/orders/${id}/cancel`,
  },

  // Store Information
  STORE: {
    INFO: "/store/info",
    SETTINGS: "/store/settings",
  },

  // Health Check
  HEALTH: "/health",
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR:
    "Network connection failed. Please check your internet connection.",
  UNAUTHORIZED: "Session expired. Please login again.",
  FORBIDDEN: "You don't have permission to perform this action.",
  NOT_FOUND: "The requested resource was not found.",
  SERVER_ERROR: "Server error occurred. Please try again later.",
  TIMEOUT: "Request timed out. Please try again.",
  UNKNOWN: "An unexpected error occurred.",
} as const;

// Request Configuration
export const REQUEST_CONFIG = {
  DEFAULT_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

export default API_CONFIG;
