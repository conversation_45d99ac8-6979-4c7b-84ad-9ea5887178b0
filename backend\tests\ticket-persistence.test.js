/**
 * Ticket Persistence Backend Tests
 * 
 * Comprehensive tests for ticket auto-save, recovery, and cleanup functionality.
 */

const request = require('supertest');
const app = require('../src/app');
const ticketService = require('../src/services/ticket-management-service');
const cleanupService = require('../src/services/ticket-cleanup-service');

describe('Ticket Persistence Backend', () => {
  let authToken;
  let testUserId;
  let testTicketId;

  beforeAll(async () => {
    // Setup test user and authentication
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpassword123'
      });

    authToken = loginResponse.body.data.token;
    testUserId = loginResponse.body.data.user.id;
  });

  beforeEach(async () => {
    // Create a test ticket for each test
    const ticketResponse = await request(app)
      .post('/api/tickets')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Test Ticket',
        note: 'Test ticket for persistence testing'
      });

    testTicketId = ticketResponse.body.data.ticket.id;
  });

  afterEach(async () => {
    // Cleanup test ticket
    if (testTicketId) {
      await request(app)
        .delete(`/api/tickets/${testTicketId}`)
        .set('Authorization', `Bearer ${authToken}`);
    }
  });

  describe('Auto-Save Functionality', () => {
    test('should auto-save ticket with complete state', async () => {
      const ticketState = {
        name: 'Updated Test Ticket',
        items: [
          {
            variantId: 'variant-123',
            productId: 'product-123',
            title: 'Test Product',
            variantTitle: 'Default',
            sku: 'TEST-SKU',
            price: '29.99',
            quantity: 2,
            inventoryQuantity: 100
          }
        ],
        customer: null,
        salesperson: null,
        note: 'Auto-saved ticket',
        discounts: [],
        subtotal: 59.98,
        tax: 0,
        total: 59.98,
        status: 'active'
      };

      const response = await request(app)
        .post(`/api/tickets/${testTicketId}/auto-save`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(ticketState);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toContain('auto-saved successfully');
      expect(response.body.data.timestamp).toBeDefined();

      // Verify ticket was updated
      const ticketResponse = await request(app)
        .get(`/api/tickets/${testTicketId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(ticketResponse.body.data.ticket.name).toBe('Updated Test Ticket');
      expect(ticketResponse.body.data.ticket.items).toHaveLength(1);
      expect(ticketResponse.body.data.ticket.total).toBe(59.98);
    });

    test('should handle auto-save with empty items', async () => {
      const ticketState = {
        name: 'Empty Ticket',
        items: [],
        customer: null,
        salesperson: null,
        note: 'Empty ticket test',
        discounts: [],
        subtotal: 0,
        tax: 0,
        total: 0,
        status: 'active'
      };

      const response = await request(app)
        .post(`/api/tickets/${testTicketId}/auto-save`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(ticketState);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('should reject auto-save for non-existent ticket', async () => {
      const ticketState = {
        name: 'Test',
        items: [],
        total: 0
      };

      const response = await request(app)
        .post('/api/tickets/non-existent-id/auto-save')
        .set('Authorization', `Bearer ${authToken}`)
        .send(ticketState);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });

    test('should reject auto-save for unauthorized ticket', async () => {
      // Create ticket with different user
      const otherUserResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'otherpassword123'
        });

      const otherToken = otherUserResponse.body.data.token;

      const ticketState = {
        name: 'Unauthorized Test',
        items: [],
        total: 0
      };

      const response = await request(app)
        .post(`/api/tickets/${testTicketId}/auto-save`)
        .set('Authorization', `Bearer ${otherToken}`)
        .send(ticketState);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Permission denied');
    });
  });

  describe('Batch Auto-Save Functionality', () => {
    test('should batch auto-save multiple tickets', async () => {
      // Create additional test tickets
      const ticket2Response = await request(app)
        .post('/api/tickets')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ name: 'Test Ticket 2' });

      const ticket2Id = ticket2Response.body.data.ticket.id;

      const ticketsData = [
        {
          ticketId: testTicketId,
          ticketState: {
            name: 'Batch Updated 1',
            items: [],
            total: 10.00
          }
        },
        {
          ticketId: ticket2Id,
          ticketState: {
            name: 'Batch Updated 2',
            items: [],
            total: 20.00
          }
        }
      ];

      const response = await request(app)
        .post('/api/tickets/batch-auto-save')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ tickets: ticketsData });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.summary.total).toBe(2);
      expect(response.body.data.summary.successful).toBe(2);
      expect(response.body.data.summary.failed).toBe(0);

      // Cleanup
      await request(app)
        .delete(`/api/tickets/${ticket2Id}`)
        .set('Authorization', `Bearer ${authToken}`);
    });

    test('should handle partial batch auto-save failures', async () => {
      const ticketsData = [
        {
          ticketId: testTicketId,
          ticketState: {
            name: 'Valid Update',
            items: [],
            total: 10.00
          }
        },
        {
          ticketId: 'non-existent-id',
          ticketState: {
            name: 'Invalid Update',
            items: [],
            total: 20.00
          }
        }
      ];

      const response = await request(app)
        .post('/api/tickets/batch-auto-save')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ tickets: ticketsData });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(false);
      expect(response.body.data.summary.total).toBe(2);
      expect(response.body.data.summary.successful).toBe(1);
      expect(response.body.data.summary.failed).toBe(1);
    });
  });

  describe('Recovery Functionality', () => {
    test('should get recovery tickets for user', async () => {
      // Add some items to make it recoverable
      await request(app)
        .post(`/api/tickets/${testTicketId}/items`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          variantId: 'variant-123',
          productId: 'product-123',
          title: 'Recovery Test Product',
          price: '15.99',
          quantity: 1
        });

      const response = await request(app)
        .get('/api/tickets/recovery')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.tickets).toBeDefined();
      expect(response.body.data.count).toBeGreaterThanOrEqual(1);

      const recoveryTicket = response.body.data.tickets.find(t => t.id === testTicketId);
      expect(recoveryTicket).toBeDefined();
      expect(recoveryTicket.can_recover).toBe(true);
      expect(recoveryTicket.item_count).toBeGreaterThan(0);
    });

    test('should not return empty tickets in recovery', async () => {
      const response = await request(app)
        .get('/api/tickets/recovery')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Empty tickets should not be in recovery list
      const emptyTickets = response.body.data.tickets.filter(t => t.item_count === 0);
      expect(emptyTickets).toHaveLength(0);
    });
  });

  describe('Auto-Save Statistics', () => {
    test('should get auto-save statistics', async () => {
      // Perform some auto-saves first
      await request(app)
        .post(`/api/tickets/${testTicketId}/auto-save`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Stats Test',
          items: [],
          total: 0
        });

      const response = await request(app)
        .get('/api/tickets/auto-save-stats?days=7')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.stats).toBeDefined();
      expect(response.body.data.summary).toBeDefined();
      expect(response.body.data.summary.totalAutoSaves).toBeGreaterThanOrEqual(1);
    });

    test('should filter stats by days parameter', async () => {
      const response = await request(app)
        .get('/api/tickets/auto-save-stats?days=1')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('Cleanup Service Integration', () => {
    test('should cleanup expired tickets', async () => {
      // This would require admin permissions
      // For now, test the service directly
      const result = await ticketService.cleanupExpiredTickets();
      
      expect(result.success).toBe(true);
      expect(result.cleanedCount).toBeGreaterThanOrEqual(0);
      expect(result.message).toBeDefined();
    });

    test('should get cleanup service status', async () => {
      const status = cleanupService.getStatus();
      
      expect(status.isRunning).toBeDefined();
      expect(status.activeJobs).toBeDefined();
      expect(status.stats).toBeDefined();
      expect(status.configuration).toBeDefined();
    });

    test('should handle manual cleanup', async () => {
      const result = await cleanupService.runManualCleanup();
      
      expect(result.success).toBeDefined();
      expect(result.results).toBeDefined();
      expect(result.summary).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle database connection errors gracefully', async () => {
      // This would require mocking database failures
      // For now, test with invalid data
      const response = await request(app)
        .post(`/api/tickets/${testTicketId}/auto-save`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          // Invalid data structure
          invalidField: 'invalid'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should handle authentication errors', async () => {
      const response = await request(app)
        .post(`/api/tickets/${testTicketId}/auto-save`)
        .set('Authorization', 'Bearer invalid-token')
        .send({
          name: 'Test',
          items: [],
          total: 0
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });
});
