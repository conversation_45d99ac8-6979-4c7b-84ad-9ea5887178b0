/**
 * Test User Switching Step by Step
 */

const axios = require("axios");

async function testUserSwitching() {
  try {
    console.log("🔐 Logging in...");
    const loginResponse = await axios.post("http://localhost:3020/api/pos/login", {
      username: "admin1",
      password: "admin123"
    });

    if (!loginResponse.data.success) {
      throw new Error("Login failed");
    }

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log("✅ Login successful");

    // Test 1: Get available staff
    console.log("👥 Testing get available staff...");
    const staffResponse = await axios.get("http://localhost:3020/api/pos/user-switching/available-staff", { headers });
    
    if (staffResponse.data.success) {
      console.log(`✅ Found ${staffResponse.data.data.staff.length} staff members`);
      console.log("Staff:", staffResponse.data.data.staff.map(s => `${s.username} (${s.name})`));
    } else {
      console.log("❌ Get staff failed:", staffResponse.data.error);
      return;
    }

    // Test 2: Initialize PIN for first staff member
    const targetStaff = staffResponse.data.data.staff[0];
    if (!targetStaff) {
      console.log("❌ No staff available for testing");
      return;
    }

    console.log(`🔑 Testing PIN initialization for ${targetStaff.username}...`);
    const pinResponse = await axios.post("http://localhost:3020/api/pos/user-switching/initialize-pin", {
      staffId: targetStaff.id,
      pin: "1234",
      confirmPin: "1234"
    }, { headers });

    if (pinResponse.data.success) {
      console.log("✅ PIN initialization successful");
    } else {
      console.log("❌ PIN initialization failed:", pinResponse.data.error);
      return;
    }

    // Test 3: Validate PIN
    console.log("🔍 Testing PIN validation...");
    const validateResponse = await axios.post("http://localhost:3020/api/pos/user-switching/validate-pin", {
      staffId: targetStaff.id,
      pin: "1234"
    }, { headers });

    if (validateResponse.data.success && validateResponse.data.data.valid) {
      console.log("✅ PIN validation successful");
    } else {
      console.log("❌ PIN validation failed:", validateResponse.data.error);
      return;
    }

    console.log("🎉 User switching basic tests completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error.response?.data?.error || error.message);
  }
}

testUserSwitching();
