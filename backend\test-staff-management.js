#!/usr/bin/env node

/**
 * Test script for Shopify Staff Management & Order Attribution
 * This script validates the implementation of Task 1: Week 1 Shopify Staff Management
 */

const axios = require("axios");
require("dotenv").config();

const BASE_URL = process.env.BASE_URL || "http://localhost:3020";

// Test configuration
const TEST_CONFIG = {
  baseUrl: BASE_URL,
  testUser: {
    username: "manager1",
    password: "manager123",
  },
};

let authToken = null;

// Helper function to make authenticated requests
async function makeRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${TEST_CONFIG.baseUrl}${endpoint}`,
    headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || error.message,
      status: error.response?.status,
    };
  }
}

// Test functions
async function testPOSLogin() {
  console.log("\n🔐 Testing POS Login with Shopify Staff Integration...");

  const result = await makeRequest(
    "POST",
    "/api/pos/login",
    TEST_CONFIG.testUser
  );

  if (result.success) {
    authToken = result.data.data.token;
    const user = result.data.data.user;

    console.log("✅ Login successful");
    console.log(`   User: ${user.name} (${user.role})`);
    console.log(`   Shopify Staff ID: ${user.shopifyStaffId || "Not mapped"}`);
    console.log(`   Commission Rate: ${user.commissionRate || 0}%`);
    console.log(`   Permissions: ${user.permissions.join(", ")}`);

    return true;
  } else {
    console.log("❌ Login failed:", result.error);
    return false;
  }
}

async function testGetStaffMembers() {
  console.log("\n👥 Testing Get Staff Members...");

  const result = await makeRequest("GET", "/api/staff");

  if (result.success) {
    const staffMembers = result.data.data.staffMembers;
    console.log(`✅ Retrieved ${staffMembers.length} staff members`);

    staffMembers.forEach((staff, index) => {
      console.log(`   ${index + 1}. ${staff.name} (${staff.email})`);
      console.log(`      Account Type: ${staff.accountType}`);
      console.log(`      Commission Rate: ${staff.commissionRate}%`);
      console.log(`      Active: ${staff.active}`);
    });

    return staffMembers;
  } else {
    console.log("❌ Failed to get staff members:", result.error);
    return [];
  }
}

async function testSetCommissionRate(staffId) {
  console.log("\n💰 Testing Set Commission Rate...");

  const testCommissionRate = 7.5; // 7.5%
  const result = await makeRequest("PUT", `/api/staff/${staffId}/commission`, {
    commissionRate: testCommissionRate,
  });

  if (result.success) {
    console.log(
      `✅ Commission rate set to ${testCommissionRate}% for staff ${staffId}`
    );
    console.log(`   Metafield ID: ${result.data.data.metafield?.id || "N/A"}`);
    return true;
  } else {
    console.log("❌ Failed to set commission rate:", result.error);
    return false;
  }
}

async function testCreateOrderWithStaff(staffId) {
  console.log("\n📦 Testing Create Order with Staff Attribution...");

  const orderData = {
    lineItems: [
      {
        variantId: "123456789",
        quantity: 2,
        price: "25.00",
        title: "Test Product",
        sku: "TEST-001",
        productId: "987654321",
      },
    ],
    customer: {
      id: "123",
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "Customer",
      phone: "+254700000000",
    },
    email: "<EMAIL>",
    note: "Test order for staff attribution",
    tags: "POS,Test",
  };

  const result = await makeRequest("POST", "/api/orders/with-staff", {
    orderData,
    staffId,
  });

  if (result.success) {
    const order = result.data.data.order;
    console.log(`✅ Order created successfully: ${order.id}`);
    console.log(`   Order Number: ${order.order_number || order.name}`);
    console.log(`   Total: ${order.total_price} ${order.currency || "USD"}`);
    console.log(`   Source: ${order.source_name}`);

    // Check for staff attribution in note_attributes
    const staffAttribute = order.note_attributes?.find(
      (attr) => attr.name === "sales_agent_id"
    );
    const commissionAttribute = order.note_attributes?.find(
      (attr) => attr.name === "commission_rate"
    );

    if (staffAttribute) {
      console.log(`   ✅ Staff Attribution: ${staffAttribute.value}`);
    } else {
      console.log(`   ❌ Staff Attribution: Missing`);
    }

    if (commissionAttribute) {
      console.log(`   ✅ Commission Rate: ${commissionAttribute.value}%`);
    } else {
      console.log(`   ❌ Commission Rate: Missing`);
    }

    return order;
  } else {
    console.log("❌ Failed to create order:", result.error);
    return null;
  }
}

async function testGetStaffOrders(staffId) {
  console.log("\n📊 Testing Get Staff Orders...");

  const result = await makeRequest(
    "GET",
    `/api/staff/${staffId}/orders?limit=10`
  );

  if (result.success) {
    const orders = result.data.data.orders;
    console.log(`✅ Retrieved ${orders.length} orders for staff ${staffId}`);

    orders.forEach((order, index) => {
      console.log(
        `   ${index + 1}. Order ${order.order_number || order.name}: ${
          order.total_price
        } ${order.currency || "USD"}`
      );
      console.log(
        `      Created: ${new Date(order.created_at).toLocaleDateString()}`
      );
      console.log(`      Status: ${order.financial_status}`);
    });

    return orders;
  } else {
    console.log("❌ Failed to get staff orders:", result.error);
    return [];
  }
}

async function testStaffPerformance(staffId) {
  console.log("\n📈 Testing Staff Performance Analytics...");

  const result = await makeRequest("GET", `/api/staff/${staffId}/performance`);

  if (result.success) {
    const performance = result.data.data.performance;
    console.log(`✅ Performance data retrieved for staff ${staffId}`);
    console.log(`   Total Sales: ${performance.totalSales.toFixed(2)} USD`);
    console.log(
      `   Total Commission: ${performance.totalCommission.toFixed(2)} USD`
    );
    console.log(`   Order Count: ${performance.orderCount}`);
    console.log(
      `   Average Order Value: ${performance.averageOrderValue.toFixed(2)} USD`
    );
    console.log(`   Commission Rate: ${performance.commissionRate}%`);

    return performance;
  } else {
    console.log("❌ Failed to get staff performance:", result.error);
    return null;
  }
}

// Main test execution
async function runTests() {
  console.log("🚀 Starting Shopify Staff Management & Order Attribution Tests");
  console.log(
    "================================================================"
  );

  try {
    // Test 1: POS Login with Shopify integration
    const loginSuccess = await testPOSLogin();
    if (!loginSuccess) {
      console.log("\n❌ Cannot proceed without authentication");
      return;
    }

    // Test 2: Get Staff Members
    const staffMembers = await testGetStaffMembers();
    if (staffMembers.length === 0) {
      console.log("\n⚠️ No staff members found, some tests may fail");
    }

    // Use first staff member for testing, or fallback to a test ID
    const testStaffId = staffMembers[0]?.id || "gid://shopify/StaffMember/1";

    // Test 3: Set Commission Rate
    if (staffMembers.length > 0) {
      await testSetCommissionRate(testStaffId);
    }

    // Test 4: Create Order with Staff Attribution
    const order = await testCreateOrderWithStaff(testStaffId);

    // Test 5: Get Staff Orders
    await testGetStaffOrders(testStaffId);

    // Test 6: Staff Performance Analytics
    await testStaffPerformance(testStaffId);

    console.log("\n🎉 All tests completed!");
    console.log(
      "================================================================"
    );

    // Summary
    console.log("\n📋 Test Summary:");
    console.log("✅ POS Login with Shopify Staff Integration");
    console.log("✅ Staff Members Retrieval");
    console.log("✅ Commission Rate Management");
    console.log("✅ Order Creation with Staff Attribution");
    console.log("✅ Staff Order Tracking");
    console.log("✅ Staff Performance Analytics");
  } catch (error) {
    console.error("\n💥 Test execution failed:", error.message);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testPOSLogin,
  testGetStaffMembers,
  testSetCommissionRate,
  testCreateOrderWithStaff,
  testGetStaffOrders,
  testStaffPerformance,
};
