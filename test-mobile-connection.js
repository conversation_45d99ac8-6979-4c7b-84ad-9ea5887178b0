// Test mobile connection to backend
const axios = require("axios");

async function testMobileConnection() {
  console.log("🧪 Testing Mobile Connection to Backend...\n");

  const testURLs = [
    "http://localhost:3020/health",
    "http://***********:3020/health",
  ];

  for (const url of testURLs) {
    try {
      console.log(`Testing: ${url}`);
      const response = await axios.get(url, { timeout: 5000 });
      console.log(`✅ SUCCESS: ${response.data.message}`);
      console.log(`   Status: ${response.status}`);
      console.log(
        `   Response time: ${response.headers["x-response-time"] || "N/A"}`
      );
    } catch (error) {
      console.log(`❌ FAILED: ${error.message}`);
      if (error.code) {
        console.log(`   Error code: ${error.code}`);
      }
    }
    console.log("");
  }

  // Test API endpoint
  try {
    console.log("Testing API endpoint...");
    const response = await axios.post(
      "http://***********:3020/api/auth/shopify/url",
      {
        shopDomain: "test-store.myshopify.com",
      },
      { timeout: 5000 }
    );
    console.log("✅ API endpoint working:", response.data.success);
  } catch (error) {
    console.log("❌ API endpoint failed:", error.message);
  }
}

testMobileConnection();
