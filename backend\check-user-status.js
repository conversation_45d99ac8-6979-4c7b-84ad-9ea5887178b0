require('dotenv').config();
const mysql = require('mysql2/promise');

async function checkUserStatus() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4'
    });
    
    console.log('🔍 Checking user active status...\n');
    
    const [users] = await connection.execute(`
      SELECT id, username, name, role, is_active, failed_login_attempts, locked_until
      FROM pos_staff 
      ORDER BY role DESC
    `);
    
    console.log('👥 User Status:');
    console.log('='.repeat(80));
    
    users.forEach(user => {
      console.log(`Username: ${user.username}`);
      console.log(`Name: ${user.name}`);
      console.log(`Role: ${user.role}`);
      console.log(`Active: ${user.is_active ? 'YES' : 'NO'}`);
      console.log(`Failed Attempts: ${user.failed_login_attempts || 0}`);
      console.log(`Locked Until: ${user.locked_until || 'Not locked'}`);
      console.log('-'.repeat(40));
    });
    
    // Check if any users need to be activated
    const inactiveUsers = users.filter(user => !user.is_active);
    if (inactiveUsers.length > 0) {
      console.log('\n⚠️  Found inactive users. Activating them...');
      
      for (const user of inactiveUsers) {
        await connection.execute(
          'UPDATE pos_staff SET is_active = true WHERE id = ?',
          [user.id]
        );
        console.log(`✅ Activated user: ${user.username}`);
      }
    } else {
      console.log('\n✅ All users are active');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkUserStatus();
