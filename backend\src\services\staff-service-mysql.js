/**
 * MySQL-based Staff Management Service
 *
 * Handles all staff CRUD operations using MySQL database
 * Replaces the in-memory staff database in staff-attribution-service
 */

const { databaseManager } = require("../config/database-manager");
const bcrypt = require("bcrypt");
const { v4: uuidv4 } = require("uuid");

class StaffServiceMySQL {
  constructor() {
    if (StaffServiceMySQL.instance) {
      return StaffServiceMySQL.instance;
    }

    this.databaseManager = databaseManager;

    StaffServiceMySQL.instance = this;
  }

  static getInstance() {
    if (!StaffServiceMySQL.instance) {
      StaffServiceMySQL.instance = new StaffServiceMySQL();
    }
    return StaffServiceMySQL.instance;
  }

  // Get all staff members with their permissions
  async getAllStaff() {
    try {
      const query = `
        SELECT 
          s.id,
          s.username,
          s.name,
          s.email,
          s.role,
          s.commission_rate,
          s.is_active,
          s.last_login,
          s.created_at,
          s.updated_at,
          GROUP_CONCAT(sp.permission) as permissions
        FROM pos_staff s
        LEFT JOIN staff_permissions sp ON s.id = sp.staff_id
        WHERE s.is_active = TRUE
        GROUP BY s.id
        ORDER BY s.name
      `;

      const [rows] = await this.databaseManager.executeQuery(query);

      const staffMembers = rows.map((row) => ({
        id: row.id,
        username: row.username,
        name: row.name,
        email: row.email,
        role: row.role,
        commissionRate: parseFloat(row.commission_rate),
        isActive: Boolean(row.is_active),
        lastLogin: row.last_login,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        permissions: row.permissions ? row.permissions.split(",") : [],
      }));

      return {
        success: true,
        staffMembers,
      };
    } catch (error) {
      console.error("Get all staff error:", error);
      return {
        success: false,
        error: "Failed to fetch staff members",
      };
    }
  }

  // Get staff member by ID with permissions
  async getStaffById(staffId) {
    try {
      const query = `
        SELECT 
          s.id,
          s.username,
          s.name,
          s.email,
          s.role,
          s.commission_rate,
          s.is_active,
          s.last_login,
          s.created_at,
          s.updated_at,
          GROUP_CONCAT(sp.permission) as permissions
        FROM pos_staff s
        LEFT JOIN staff_permissions sp ON s.id = sp.staff_id
        WHERE s.id = ?
        GROUP BY s.id
      `;

      const [rows] = await this.databaseManager.executeQuery(query, [staffId]);

      if (rows.length === 0) {
        return {
          success: false,
          error: "Staff member not found",
        };
      }

      const row = rows[0];
      const staffMember = {
        id: row.id,
        username: row.username,
        name: row.name,
        email: row.email,
        role: row.role,
        commissionRate: parseFloat(row.commission_rate),
        isActive: Boolean(row.is_active),
        lastLogin: row.last_login,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        permissions: row.permissions ? row.permissions.split(",") : [],
      };

      return {
        success: true,
        staffMember,
      };
    } catch (error) {
      console.error("Get staff by ID error:", error);
      return {
        success: false,
        error: "Failed to fetch staff member",
      };
    }
  }

  // Create new staff member
  async createStaff(staffData) {
    const connection = await this.databaseManager.getConnection();

    try {
      await connection.beginTransaction();

      const {
        username,
        name,
        email,
        password,
        role,
        commissionRate,
        permissions,
      } = staffData;

      // Check if username already exists
      const [existingRows] = await connection.execute(
        "SELECT id FROM pos_staff WHERE username = ?",
        [username]
      );

      if (existingRows.length > 0) {
        await connection.rollback();
        return {
          success: false,
          error: "Username already exists",
        };
      }

      // Check if email already exists (if provided)
      if (email) {
        const [emailRows] = await connection.execute(
          "SELECT id FROM pos_staff WHERE email = ?",
          [email]
        );

        if (emailRows.length > 0) {
          await connection.rollback();
          return {
            success: false,
            error: "Email already exists",
          };
        }
      }

      // Generate staff ID
      const [countRows] = await connection.execute(
        "SELECT COUNT(*) as count FROM pos_staff"
      );
      const staffId = `pos-${String(countRows[0].count + 1).padStart(3, "0")}`;

      // Hash password
      const passwordHash = await bcrypt.hash(password, 10);

      // Insert staff member
      const insertQuery = `
        INSERT INTO pos_staff (
          id, username, password_hash, name, email, role, 
          store_id, commission_rate, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await connection.execute(insertQuery, [
        staffId,
        username,
        passwordHash,
        name,
        email,
        role,
        "default-store", // Default store ID
        commissionRate,
        true,
      ]);

      // Insert permissions
      if (permissions && permissions.length > 0) {
        const permissionInserts = permissions.map((permission) => [
          staffId,
          permission,
        ]);
        const permissionQuery =
          "INSERT INTO staff_permissions (staff_id, permission) VALUES ?";

        await connection.query(permissionQuery, [permissionInserts]);
      }

      await connection.commit();

      // Get the created staff member
      const result = await this.getStaffById(staffId);

      return {
        success: true,
        staffMember: result.staffMember,
      };
    } catch (error) {
      await connection.rollback();
      console.error("Create staff error:", error);
      return {
        success: false,
        error: "Failed to create staff member",
      };
    } finally {
      connection.release();
    }
  }

  // Update staff commission rate
  async setStaffCommissionRate(staffId, commissionRate) {
    try {
      // Validate commission rate
      if (
        typeof commissionRate !== "number" ||
        commissionRate < 0 ||
        commissionRate > 100
      ) {
        return {
          success: false,
          error: "Commission rate must be a number between 0 and 100",
        };
      }

      const updateQuery = `
        UPDATE pos_staff 
        SET commission_rate = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `;

      const [result] = await this.databaseManager.executeQuery(updateQuery, [
        commissionRate,
        staffId,
      ]);

      if (result.affectedRows === 0) {
        return {
          success: false,
          error: "Staff member not found",
        };
      }

      // Get updated staff member
      const staffResult = await this.getStaffById(staffId);

      return {
        success: true,
        staffMember: staffResult.staffMember,
      };
    } catch (error) {
      console.error("Set commission rate error:", error);
      return {
        success: false,
        error: "Failed to set commission rate",
      };
    }
  }

  // Update staff member
  async updateStaff(staffId, updateData) {
    try {
      const allowedFields = [
        "name",
        "email",
        "role",
        "commission_rate",
        "is_active",
      ];
      const updates = [];
      const values = [];

      // Build dynamic update query
      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key) && value !== undefined) {
          updates.push(`${key} = ?`);
          values.push(value);
        }
      }

      if (updates.length === 0) {
        return {
          success: false,
          error: "No valid fields to update",
        };
      }

      updates.push("updated_at = CURRENT_TIMESTAMP");
      values.push(staffId);

      const updateQuery = `UPDATE pos_staff SET ${updates.join(
        ", "
      )} WHERE id = ?`;
      const [result] = await this.databaseManager.executeQuery(
        updateQuery,
        values
      );

      if (result.affectedRows === 0) {
        return {
          success: false,
          error: "Staff member not found",
        };
      }

      // Get updated staff member
      const staffResult = await this.getStaffById(staffId);

      return {
        success: true,
        staffMember: staffResult.staffMember,
      };
    } catch (error) {
      console.error("Update staff error:", error);
      return {
        success: false,
        error: "Failed to update staff member",
      };
    }
  }

  // Update staff permissions
  async updateStaffPermissions(staffId, permissions) {
    const connection = await this.databaseManager.getConnection();

    try {
      await connection.beginTransaction();

      // Delete existing permissions
      await connection.execute(
        "DELETE FROM staff_permissions WHERE staff_id = ?",
        [staffId]
      );

      // Insert new permissions
      if (permissions && permissions.length > 0) {
        const permissionInserts = permissions.map((permission) => [
          staffId,
          permission,
        ]);
        const permissionQuery =
          "INSERT INTO staff_permissions (staff_id, permission) VALUES ?";

        await connection.query(permissionQuery, [permissionInserts]);
      }

      await connection.commit();

      // Get updated staff member
      const staffResult = await this.getStaffById(staffId);

      return {
        success: true,
        staffMember: staffResult.staffMember,
      };
    } catch (error) {
      await connection.rollback();
      console.error("Update staff permissions error:", error);
      return {
        success: false,
        error: "Failed to update staff permissions",
      };
    } finally {
      connection.release();
    }
  }
}

module.exports = new StaffServiceMySQL();
