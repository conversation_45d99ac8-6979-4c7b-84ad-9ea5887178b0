# 🔧 **API Configuration Guide**

This guide explains how to configure and switch between different API environments in the Dukalink POS application.

---

## **🌍 Available Environments**

### **1. Local Development** (`local`)
- **URL**: `http://localhost:3020/api` (web) / `http://*************:3020/api` (mobile)
- **Use Case**: Local backend development
- **Requirements**: Backend server running locally

### **2. Development Online** (`development`)
- **URL**: `https://treasuredposdev.dukalink.com/api`
- **Use Case**: Online development server for testing
- **Requirements**: Internet connection

### **3. Production** (`production`)
- **URL**: `https://shopify.dukalink.com/api`
- **Use Case**: Live production environment
- **Requirements**: Production access

---

## **🔧 Configuration Methods**

### **Method 1: Using NPM Scripts (Recommended)**

```bash
# Check current environment
npm run api:status

# Switch to local development
npm run api:local

# Switch to development online
npm run api:dev

# Switch to production
npm run api:prod
```

### **Method 2: Using Environment Variables**

Create a `.env.local` file in the project root:

```env
# API Environment Selection
EXPO_PUBLIC_API_ENV=local

# Debug Settings
EXPO_PUBLIC_DEBUG_API=true
EXPO_PUBLIC_LOG_REQUESTS=true
```

### **Method 3: Direct Script Usage**

```bash
# Show current status
node scripts/switch-api-env.js status

# Switch environments
node scripts/switch-api-env.js local
node scripts/switch-api-env.js development
node scripts/switch-api-env.js production
```

### **Method 4: Code-Level Configuration**

In `src/constants/Api.ts`, you can force a specific environment:

```typescript
// Force a specific environment (useful for testing)
const FORCE_API_ENV: ApiEnvironment | null = 'development';
```

---

## **📱 Environment Detection Priority**

The system uses the following priority order:

1. **FORCE_API_ENV** (hardcoded in Api.ts)
2. **EXPO_PUBLIC_API_ENV** (environment variable)
3. **NODE_ENV fallback** (development → local, production → production)

---

## **⚙️ Configuration Files**

### **Main Configuration Files**
1. `src/constants/Api.ts` - API endpoints and configuration
2. `src/config/api.ts` - Legacy API configuration
3. `src/config/environment.ts` - Environment-specific settings
4. `src/services/api/dukalink-client.ts` - API client implementation

### **Key Configuration Points**

#### **API Base URL**
```typescript
// Production (Current)
const baseURL = "https://shopify.dukalink.com/api";

// Local Development
const baseURL = Platform.OS === "web"
  ? "http://localhost:3020/api"
  : "http://***********:3020/api";
```

#### **Timeout Settings**
```typescript
export const API_CONFIG = {
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
};
```

---

## **🔧 How to Switch to Local Development**

### **Step 1: Update API Configuration**

Edit `src/constants/Api.ts`:

```typescript
// Change this line:
export const API_CONFIG = {
  ...PRODUCTION_API, // Current

// To this:
export const API_CONFIG = {
  ...DEVELOPMENT_API, // For local development
```

### **Step 2: Update IP Address (Mobile Only)**

If testing on mobile device, update the IP address in `src/constants/Api.ts`:

```typescript
export const DEVELOPMENT_API = {
  baseURL: Platform.OS === "web"
    ? "http://localhost:3020/api"
    : "http://YOUR_COMPUTER_IP:3020/api", // Replace with your IP
  // ...
};
```

### **Step 3: Start Local Backend**

```bash
cd backend
npm run dev
```

### **Step 4: Restart React Native App**

```bash
# Clear cache and restart
npx expo start --clear
```

---

## **🧪 Testing API Configuration**

### **Check Current Configuration**

Add this to any component to see current API config:

```typescript
import { API_CONFIG } from "@/src/constants/Api";

console.log("Current API Base URL:", API_CONFIG.baseURL);
```

### **Test API Connection**

```typescript
import { getAPIClient } from "@/src/services/api/dukalink-client";

const testConnection = async () => {
  const client = getAPIClient();
  try {
    const response = await client.request("/health");
    console.log("API Connection:", response.success ? "✅ Success" : "❌ Failed");
  } catch (error) {
    console.log("API Connection: ❌ Failed", error.message);
  }
};
```

---

## **📱 Platform-Specific Considerations**

### **Web Development**
- Use `http://localhost:3020/api` for local development
- CORS must be configured on backend
- Browser dev tools available for debugging

### **Mobile Development (iOS/Android)**
- Use your computer's IP address (e.g., `http://***********:3020/api`)
- Ensure mobile device is on same network
- Use `ipconfig` (Windows) or `ifconfig` (Mac/Linux) to find IP

### **Production Builds**
- Always use production API (`https://shopify.dukalink.com/api`)
- Ensure HTTPS is properly configured
- Test on real devices before release

---

## **🔍 Troubleshooting**

### **Common Issues**

#### **1. Connection Refused**
```
Error: Network Error / Connection Refused
```
**Solution**: Check if backend server is running and accessible

#### **2. CORS Errors (Web)**
```
Error: CORS policy blocked
```
**Solution**: Configure CORS on backend to allow your domain

#### **3. Timeout Errors**
```
Error: Request timeout
```
**Solution**: Increase timeout or check network connection

#### **4. 404 Not Found**
```
Error: 404 - Endpoint not found
```
**Solution**: Verify API endpoint exists and is correct

### **Debug Steps**

1. **Check API URL**: Verify the base URL is correct
2. **Test Backend**: Use curl or Postman to test backend directly
3. **Check Network**: Ensure device can reach the API server
4. **Review Logs**: Check both app and backend logs
5. **Clear Cache**: Clear React Native cache and restart

---

## **📋 Quick Reference**

### **Switch to Production API**
```typescript
// src/constants/Api.ts
export const API_CONFIG = {
  ...PRODUCTION_API,
  // ...
};
```

### **Switch to Local Development API**
```typescript
// src/constants/Api.ts
export const API_CONFIG = {
  ...DEVELOPMENT_API,
  // ...
};
```

### **Test API Connection**
```bash
# Test production API
curl https://shopify.dukalink.com/api/health

# Test local API
curl http://localhost:3020/api/health
```

---

## **🎯 Best Practices**

1. **Use Environment Variables**: Consider using environment variables for different builds
2. **Centralize Configuration**: Keep all API configuration in one place
3. **Document Changes**: Always document when switching environments
4. **Test Thoroughly**: Test on all platforms when changing API configuration
5. **Use Feature Flags**: Implement feature flags for environment-specific features

---

**Happy coding! 🚀**
