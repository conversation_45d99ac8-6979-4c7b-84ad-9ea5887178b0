/**
 * Session Context Indicator Component
 *
 * Displays current user information and switching status in the header,
 * with quick access to user switching functionality.
 */

import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useUserSwitching } from "@/src/contexts/UserSwitchingContext";
import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import { Alert, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { UserSwitchingModal } from "./UserSwitchingModal";

interface SessionContextIndicatorProps {
  showSwitchButton?: boolean;
  compact?: boolean;
  style?: any;
}

export const SessionContextIndicator: React.FC<
  SessionContextIndicatorProps
> = ({ showSwitchButton = true, compact = false, style }) => {
  const theme = useTheme();
  const { user, hasPermission } = useSession();
  const { sessionContext, switchBack, isSwitching } = useUserSwitching();
  const [showSwitchModal, setShowSwitchModal] = useState(false);

  // Check if user can switch users
  const canSwitchUsers =
    hasPermission("switch_users") ||
    user?.role === "super_admin" ||
    user?.role === "manager";

  const handleQuickSwitchBack = async () => {
    if (!sessionContext?.hasActiveSwitch) return;

    Alert.alert(
      "Switch Back",
      `Switch back to ${sessionContext.primaryUser.name}?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Switch Back",
          onPress: async () => {
            const success = await switchBack();
            if (success) {
              Alert.alert(
                "Switched Back",
                `Successfully switched back to ${sessionContext.primaryUser.name}`,
                [{ text: "OK" }]
              );
            }
          },
        },
      ]
    );
  };

  const handleSwitchUser = () => {
    if (!canSwitchUsers) {
      Alert.alert(
        "Permission Denied",
        "You do not have permission to switch users.",
        [{ text: "OK" }]
      );
      return;
    }

    setShowSwitchModal(true);
  };

  const renderUserInfo = () => {
    const currentUser = sessionContext?.currentUser || user;
    const isActiveSwitch = sessionContext?.hasActiveSwitch || false;

    if (!currentUser) return null;

    return (
      <View style={styles.userInfo}>
        <View style={styles.userDetails}>
          <Text
            style={[
              styles.userName,
              { color: theme.colors.text },
              compact && styles.userNameCompact,
            ]}
            numberOfLines={1}
          >
            {currentUser.name}
          </Text>

          {!compact && (
            <Text
              style={[styles.userRole, { color: theme.colors.textSecondary }]}
              numberOfLines={1}
            >
              {currentUser.role}
              {currentUser.commissionRate &&
                ` • ${currentUser.commissionRate}%`}
            </Text>
          )}
        </View>

        {isActiveSwitch && (
          <View
            style={[
              styles.switchIndicator,
              { backgroundColor: theme.colors.warning },
            ]}
          >
            <Ionicons
              name="swap-horizontal"
              size={12}
              color={theme.colors.textInverse}
            />
            {!compact && (
              <Text
                style={[styles.switchText, { color: theme.colors.textInverse }]}
              >
                Switched
              </Text>
            )}
          </View>
        )}
      </View>
    );
  };

  const renderActionButtons = () => {
    if (!showSwitchButton) return null;

    const hasActiveSwitch = sessionContext?.hasActiveSwitch || false;

    return (
      <View style={styles.actions}>
        {hasActiveSwitch && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.switchBackButton,
              { borderColor: theme.colors.primary },
            ]}
            onPress={handleQuickSwitchBack}
            disabled={isSwitching}
          >
            <Ionicons
              name="arrow-back"
              size={16}
              color={theme.colors.primary}
            />
            {!compact && (
              <Text
                style={[
                  styles.actionButtonText,
                  { color: theme.colors.primary },
                ]}
              >
                Back
              </Text>
            )}
          </TouchableOpacity>
        )}

        {canSwitchUsers && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.switchUserButton,
              { backgroundColor: theme.colors.primary },
            ]}
            onPress={handleSwitchUser}
            disabled={isSwitching}
          >
            <Ionicons
              name="people"
              size={16}
              color={theme.colors.primaryForeground}
            />
            {!compact && (
              <Text
                style={[
                  styles.actionButtonText,
                  { color: theme.colors.primaryForeground },
                ]}
              >
                Switch
              </Text>
            )}
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <>
      <View style={[styles.container, style]}>
        {renderUserInfo()}
        {renderActionButtons()}
      </View>

      <UserSwitchingModal
        visible={showSwitchModal}
        onClose={() => setShowSwitchModal(false)}
        onSwitchComplete={(success) => {
          if (success) {
            setShowSwitchModal(false);
          }
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingVertical: 8,
    minHeight: 48,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
    marginRight: 8,
  },
  userName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  userNameCompact: {
    fontSize: 14,
    marginBottom: 0,
  },
  userRole: {
    fontSize: 12,
  },
  switchIndicator: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 6,
    gap: 3,
  },
  switchText: {
    fontSize: 10,
    fontWeight: "600",
  },
  actions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  switchBackButton: {
    borderWidth: 1,
  },
  switchUserButton: {
    // Background color set inline
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: "600",
  },
});

export default SessionContextIndicator;
