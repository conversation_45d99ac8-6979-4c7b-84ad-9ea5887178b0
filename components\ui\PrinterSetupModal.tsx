import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import EnhancedThermalPrintService from "@/src/services/EnhancedThermalPrintService";
import ThermalPrintService from "@/src/services/ThermalPrintService";
import { BluetoothPermissionHelper } from "@/src/utils/BluetoothPermissionHelper";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";

export interface PrinterSetupResult {
  success: boolean;
  printerConnected: boolean;
  printerName?: string;
  error?: string;
  cancelled: boolean;
}

interface BluetoothDevice {
  id: string;
  name: string;
  address: string;
  isConnected?: boolean;
  paired?: boolean;
}

interface PrinterSetupModalProps {
  visible: boolean;
  onClose: () => void;
  onResult: (result: PrinterSetupResult) => void;
  title?: string;
  subtitle?: string;
  context?: "checkout" | "order_completion" | "settings" | "general";
}

type SetupStep =
  | "scanning"
  | "device_list"
  | "connecting"
  | "testing"
  | "success"
  | "error";

export function PrinterSetupModal({
  visible,
  onClose,
  onResult,
  title = "Printer Setup",
  subtitle = "Connect a thermal printer for automatic receipt printing",
  context = "general",
}: PrinterSetupModalProps) {
  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");
  const successColor = "#10B981";
  const errorColor = "#EF4444";

  const [currentStep, setCurrentStep] = useState<SetupStep>("scanning");
  const [isProcessing, setIsProcessing] = useState(false);
  const [availableDevices, setAvailableDevices] = useState<BluetoothDevice[]>(
    []
  );
  const [selectedDevice, setSelectedDevice] = useState<BluetoothDevice | null>(
    null
  );
  const [connectedPrinter, setConnectedPrinter] =
    useState<BluetoothDevice | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>("");

  useEffect(() => {
    console.log("PrinterSetupModal visibility changed:", visible);
    if (visible) {
      console.log("Starting printer setup...");
      startPrinterSetup();
    } else {
      resetSetupState();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const resetSetupState = () => {
    setCurrentStep("scanning");
    setIsProcessing(false);
    setAvailableDevices([]);
    setSelectedDevice(null);
    setConnectedPrinter(null);
    setErrorMessage("");
  };

  const startPrinterSetup = async () => {
    setCurrentStep("scanning");
    setIsProcessing(true);
    setErrorMessage("");

    try {
      await scanForThermalPrinters();
    } catch (error) {
      console.error("Printer setup error:", error);
      setErrorMessage(
        error instanceof Error ? error.message : "Failed to scan for printers"
      );
      setCurrentStep("error");
    } finally {
      setIsProcessing(false);
    }
  };

  const scanForThermalPrinters = async (): Promise<void> => {
    try {
      // Check and request Bluetooth permissions
      const permissionResult =
        await BluetoothPermissionHelper.ensureBluetoothReady();
      if (!permissionResult.granted) {
        throw new Error(
          permissionResult.error ||
            "Bluetooth permissions are required to scan for printers."
        );
      }

      // Check if Bluetooth is enabled
      const bluetoothEnabled =
        await BluetoothPermissionHelper.isBluetoothEnabled();
      if (!bluetoothEnabled) {
        const enabled =
          await BluetoothPermissionHelper.showBluetoothEnableDialog();
        if (!enabled) {
          throw new Error("Please enable Bluetooth to scan for printers.");
        }
      }

      // Use ThermalPrintService to scan for thermal printer devices (filtered)
      const foundDevices = await ThermalPrintService.scanDevices();

      // Convert to our interface format
      const thermalPrinters: BluetoothDevice[] = foundDevices.map(
        (device: any) => ({
          id: device.address || device.id || `device_${Date.now()}`,
          name: device.name || "Unknown Printer",
          address: device.address || "",
          isConnected: device.isConnected || false,
          paired: device.paired || false,
        })
      );

      setAvailableDevices(thermalPrinters);

      if (thermalPrinters.length > 0) {
        setCurrentStep("device_list");
      } else {
        setErrorMessage(
          "No thermal printers found. Make sure your printer is turned on, in pairing mode, and try pairing it in Android Bluetooth settings first."
        );
        setCurrentStep("error");
      }
    } catch (error) {
      console.error("Bluetooth scan error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to scan for printers";
      setErrorMessage(errorMessage);
      setCurrentStep("error");
    }
  };

  const connectToPrinter = async (device: BluetoothDevice) => {
    setSelectedDevice(device);
    setCurrentStep("connecting");
    setIsProcessing(true);

    try {
      // Simulate printer connection
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Mock connection success
      const connectedDevice = { ...device, isConnected: true };
      setConnectedPrinter(connectedDevice);

      // Proceed to testing
      await testPrinterConnection(connectedDevice);
    } catch (error) {
      console.error("Printer connection error:", error);
      setErrorMessage(`Failed to connect to ${device.name}. Please try again.`);
      setCurrentStep("error");
    } finally {
      setIsProcessing(false);
    }
  };

  const testPrinterConnection = async (device: BluetoothDevice) => {
    setCurrentStep("testing");
    setIsProcessing(true);

    try {
      // Test printer with sample receipt - using printReceipt with test data
      const testOrderData = {
        id: "test",
        orderNumber: "TEST-001",
        totalPrice: "0.00",
        createdAt: new Date().toISOString(),
        salespersonName: "Test User",
        paymentMethod: "Test",
        lineItems: [
          {
            id: "test-item",
            title: "Test Print",
            quantity: 1,
            price: "0.00",
            sku: "TEST",
            variantId: "test",
            productId: "test",
          },
        ],
      };

      const testResult = await EnhancedThermalPrintService.printReceipt(
        testOrderData
      );

      if (testResult.success) {
        // Save printer configuration
        await savePrinterConfiguration(device);
        setCurrentStep("success");
      } else {
        setErrorMessage(
          testResult.error ||
            "Printer test failed. Please check the connection."
        );
        setCurrentStep("error");
      }
    } catch (error) {
      console.error("Printer test error:", error);
      setErrorMessage("Failed to test printer connection. Please try again.");
      setCurrentStep("error");
    } finally {
      setIsProcessing(false);
    }
  };

  const savePrinterConfiguration = async (device: BluetoothDevice) => {
    try {
      // Save printer configuration to persistent storage
      console.log("Saving printer configuration:", device);
    } catch (error) {
      console.error("Failed to save printer configuration:", error);
    }
  };

  const handleSuccess = () => {
    onResult({
      success: true,
      printerConnected: true,
      printerName: connectedPrinter?.name,
      cancelled: false,
    });
    onClose();
  };

  const handleRetry = () => {
    startPrinterSetup();
  };

  const handleProceedWithoutPrinting = () => {
    onResult({
      success: true,
      printerConnected: false,
      cancelled: false,
    });
    onClose();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "scanning":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Scanning for Printers
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Looking for thermal printers nearby. Make sure your printer is
              turned on and in pairing mode.
            </Text>
          </View>
        );

      case "device_list":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: primaryColor + "20" },
              ]}
            >
              <IconSymbol name="printer" size={32} color={primaryColor} />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Select Printer
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Found {availableDevices.length} thermal printer
              {availableDevices.length !== 1 ? "s" : ""}. Select the one you
              want to connect.
            </Text>

            <View style={styles.deviceList}>
              {availableDevices.map((item) => (
                <View
                  key={item.id}
                  style={[styles.deviceItem, { borderColor }]}
                >
                  <View style={styles.deviceInfo}>
                    <View
                      style={[
                        styles.deviceIcon,
                        { backgroundColor: primaryColor + "20" },
                      ]}
                    >
                      <IconSymbol
                        name="printer"
                        size={24}
                        color={primaryColor}
                      />
                    </View>
                    <View style={styles.deviceDetails}>
                      <Text style={[styles.deviceName, { color: textColor }]}>
                        {item.name}
                      </Text>
                      <Text
                        style={[styles.deviceAddress, { color: textSecondary }]}
                      >
                        {item.address}
                      </Text>
                    </View>
                  </View>
                  <ModernButton
                    title="Connect"
                    onPress={() => connectToPrinter(item)}
                    variant="outline"
                    style={styles.connectButton}
                    disabled={isProcessing}
                  />
                </View>
              ))}
            </View>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Scan Again"
                onPress={handleRetry}
                variant="outline"
                icon={
                  <IconSymbol
                    name="arrow.clockwise"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
                disabled={isProcessing}
              />

              <ModernButton
                title="Proceed Without Printing"
                onPress={handleProceedWithoutPrinting}
                variant="ghost"
                icon={
                  <IconSymbol
                    name="arrow.right"
                    size={16}
                    color={textSecondary}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "connecting":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Connecting to Printer
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Connecting to {selectedDevice?.name}...
            </Text>
          </View>
        );

      case "testing":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Testing Connection
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Printing test receipt to verify connection...
            </Text>
          </View>
        );

      case "success":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: successColor + "20" },
              ]}
            >
              <IconSymbol
                name="checkmark.circle.fill"
                size={32}
                color={successColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Printer Connected Successfully!
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {connectedPrinter?.name} is now connected and ready for printing.
              Test receipt should have been printed.
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Done"
                onPress={handleSuccess}
                variant="primary"
                icon={<IconSymbol name="checkmark" size={16} color="white" />}
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "error":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: errorColor + "20" },
              ]}
            >
              <IconSymbol
                name="exclamationmark.triangle"
                size={32}
                color={errorColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Setup Failed
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {errorMessage}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Try Again"
                onPress={handleRetry}
                variant="primary"
                icon={
                  <IconSymbol name="arrow.clockwise" size={16} color="white" />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="Proceed Without Printing"
                onPress={handleProceedWithoutPrinting}
                variant="outline"
                icon={
                  <IconSymbol
                    name="arrow.right"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}} // Disable Android back button
      statusBarTranslucent
      presentationStyle="overFullScreen"
    >
      <View style={styles.overlay}>
        <View style={[styles.modal, { backgroundColor, borderColor }]}>
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.header}>
              <Text style={[styles.headerTitle, { color: textColor }]}>
                {title}
              </Text>
              <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
                {subtitle}
              </Text>
            </View>

            {/* Step Content */}
            {renderStepContent()}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "center",
    alignItems: "center",
    padding: Spacing.lg,
    zIndex: 9999,
    elevation: 9999,
  },
  modal: {
    width: "100%",
    maxWidth: 400,
    maxHeight: "90%",
    borderRadius: 16,
    borderWidth: 1,
    overflow: "hidden",
    zIndex: 10000,
    elevation: 10000,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: Spacing.lg,
  },
  header: {
    marginBottom: Spacing.lg,
    alignItems: "center",
  },
  headerTitle: {
    fontSize: Typography.h2.fontSize,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: Spacing.xs,
  },
  headerSubtitle: {
    fontSize: Typography.body.fontSize,
    textAlign: "center",
    lineHeight: 20,
  },
  stepContainer: {
    alignItems: "center",
    minHeight: 200,
    justifyContent: "center",
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Spacing.md,
  },
  stepTitle: {
    fontSize: Typography.h3.fontSize,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: Spacing.sm,
  },
  stepMessage: {
    fontSize: Typography.body.fontSize,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: Spacing.lg,
  },
  deviceList: {
    width: "100%",
    marginBottom: Spacing.lg,
  },
  deviceItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: Spacing.md,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: Spacing.sm,
  },
  deviceInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  deviceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  deviceDetails: {
    flex: 1,
  },
  deviceName: {
    fontSize: Typography.body.fontSize,
    fontWeight: "600",
    marginBottom: 2,
  },
  deviceAddress: {
    fontSize: Typography.caption.fontSize,
  },
  connectButton: {
    minWidth: 80,
  },
  actionsContainer: {
    width: "100%",
    gap: Spacing.sm,
  },
  actionButton: {
    marginBottom: 0,
  },
});
