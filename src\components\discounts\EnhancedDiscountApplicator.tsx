/**
 * Enhanced Discount Applicator Component
 * Extends the existing DiscountApplicator with loyalty-based discounts,
 * staff discount permissions, and advanced discount rules
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
  TextInput,
} from "react-native";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import {
  ThemedView,
  ThemedText,
} from "@/src/components/themed/ThemedComponents";
import { ModernButton } from "@/src/components/ui/ModernButton";
import { IconSymbol } from "@/src/components/ui/IconSymbol";
import { formatCurrency } from "@/src/utils/currencyUtils";

// Enhanced discount types
export interface EnhancedDiscount {
  id?: string;
  type:
    | "percentage"
    | "fixed_amount"
    | "loyalty_points"
    | "staff_rule"
    | "automatic";
  amount: number;
  description?: string;
  source: "manual" | "loyalty" | "staff_rule" | "automatic" | "commission";
  ruleId?: string;
  staffId?: string;
  loyaltyTier?: string;
  pointsRedeemed?: number;
  maxAmount?: number;
  restrictions?: {
    minOrderAmount?: number;
    maxDiscountAmount?: number;
    customerEligibility?: string[];
    productTypes?: string[];
  };
}

export interface AvailableDiscount {
  id: string;
  name: string;
  type: "percentage" | "fixed_amount" | "loyalty_points";
  value: number;
  description: string;
  source: "staff_rule" | "loyalty" | "automatic";
  eligibility: {
    canApply: boolean;
    reason?: string;
    maxAmount?: number;
    pointsRequired?: number;
  };
  tier?: string;
  icon?: string;
  color?: string;
}

interface EnhancedDiscountApplicatorProps {
  currentDiscount?: EnhancedDiscount;
  itemPrice: number;
  itemQuantity: number;
  customerId?: string;
  customerLoyalty?: {
    tier: string;
    points: number;
  };
  staffPermissions?: {
    canApplyManualDiscounts: boolean;
    canApplyStaffRules: boolean;
    maxDiscountPercentage?: number;
    maxDiscountAmount?: number;
    availableRules: string[];
  };
  onApplyDiscount: (discount: EnhancedDiscount | null) => void;
  onLoadAvailableDiscounts?: () => Promise<AvailableDiscount[]>;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  showAdvanced?: boolean;
}

export const EnhancedDiscountApplicator: React.FC<
  EnhancedDiscountApplicatorProps
> = ({
  currentDiscount,
  itemPrice,
  itemQuantity,
  customerId,
  customerLoyalty,
  staffPermissions,
  onApplyDiscount,
  onLoadAvailableDiscounts,
  disabled = false,
  size = "medium",
  showAdvanced = true,
}) => {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  // Create theme-aware styles
  const styles = createStyles(theme);

  // State management
  const [modalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "manual" | "available" | "loyalty"
  >("available");
  const [availableDiscounts, setAvailableDiscounts] = useState<
    AvailableDiscount[]
  >([]);
  const [loading, setLoading] = useState(false);

  // Manual discount state
  const [manualType, setManualType] = useState<"percentage" | "fixed_amount">(
    "percentage"
  );
  const [manualAmount, setManualAmount] = useState("");
  const [manualDescription, setManualDescription] = useState("");

  const lineTotal = itemPrice * itemQuantity;
  const hasDiscount = currentDiscount && currentDiscount.amount > 0;

  // Load available discounts when modal opens
  useEffect(() => {
    if (modalVisible && onLoadAvailableDiscounts) {
      loadAvailableDiscounts();
    }
  }, [modalVisible]);

  const loadAvailableDiscounts = async () => {
    if (!onLoadAvailableDiscounts) return;

    try {
      setLoading(true);
      const discounts = await onLoadAvailableDiscounts();
      setAvailableDiscounts(discounts);
    } catch (error) {
      console.error("Load available discounts error:", error);
      Alert.alert("Error", "Failed to load available discounts");
    } finally {
      setLoading(false);
    }
  };

  // Calculate discount amount
  const calculateDiscountAmount = (discount: EnhancedDiscount): number => {
    switch (discount.type) {
      case "percentage":
        const percentageAmount = (lineTotal * discount.amount) / 100;
        return discount.maxAmount
          ? Math.min(percentageAmount, discount.maxAmount)
          : percentageAmount;

      case "fixed_amount":
        return Math.min(discount.amount, lineTotal);

      case "loyalty_points":
        // Points to currency conversion (e.g., 100 points = 1 KSh)
        const pointsValue = (discount.pointsRedeemed || 0) * 0.01;
        return Math.min(pointsValue, lineTotal);

      default:
        return Math.min(discount.amount, lineTotal);
    }
  };

  // Get discount display information
  const getDiscountDisplay = () => {
    if (!hasDiscount) return { text: "", color: theme.colors.textSecondary };

    const discount = currentDiscount!;
    let text = "";
    let color = theme.colors.success;

    switch (discount.type) {
      case "percentage":
        text = `-${discount.amount}%`;
        break;
      case "fixed_amount":
        text = `-${formatCurrency(discount.amount)}`;
        break;
      case "loyalty_points":
        text = `${discount.pointsRedeemed} pts`;
        color = theme.colors.warning;
        break;
      case "staff_rule":
        text = `-${discount.amount}%`;
        color = theme.colors.primary;
        break;
      case "automatic":
        text = `Auto`;
        color = theme.colors.info;
        break;
    }

    return { text, color };
  };

  // Handle manual discount application
  const handleApplyManualDiscount = () => {
    if (!staffPermissions?.canApplyManualDiscounts) {
      Alert.alert(
        "Permission Denied",
        "You do not have permission to apply manual discounts"
      );
      return;
    }

    const amount = parseFloat(manualAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert("Invalid Discount", "Please enter a valid discount amount");
      return;
    }

    // Validate against staff permissions
    if (manualType === "percentage") {
      if (
        staffPermissions.maxDiscountPercentage &&
        amount > staffPermissions.maxDiscountPercentage
      ) {
        Alert.alert(
          "Permission Denied",
          `Maximum allowed discount is ${staffPermissions.maxDiscountPercentage}%`
        );
        return;
      }
      if (amount > 100) {
        Alert.alert(
          "Invalid Discount",
          "Percentage discount cannot exceed 100%"
        );
        return;
      }
    } else {
      if (
        staffPermissions.maxDiscountAmount &&
        amount > staffPermissions.maxDiscountAmount
      ) {
        Alert.alert(
          "Permission Denied",
          `Maximum allowed discount is ${formatCurrency(
            staffPermissions.maxDiscountAmount
          )}`
        );
        return;
      }
      if (amount > lineTotal) {
        Alert.alert(
          "Invalid Discount",
          `Fixed amount discount cannot exceed line total of ${formatCurrency(
            lineTotal
          )}`
        );
        return;
      }
    }

    const discount: EnhancedDiscount = {
      type: manualType,
      amount: amount,
      description: manualDescription.trim() || "Manual discount",
      source: "manual",
    };

    onApplyDiscount(discount);
    setModalVisible(false);
  };

  // Handle available discount selection
  const handleSelectAvailableDiscount = (
    availableDiscount: AvailableDiscount
  ) => {
    if (!availableDiscount.eligibility.canApply) {
      Alert.alert(
        "Cannot Apply Discount",
        availableDiscount.eligibility.reason || "Discount not available"
      );
      return;
    }

    const discount: EnhancedDiscount = {
      id: availableDiscount.id,
      type: availableDiscount.type,
      amount: availableDiscount.value,
      description: availableDiscount.description,
      source: availableDiscount.source,
      ruleId: availableDiscount.id,
      loyaltyTier: availableDiscount.tier,
      pointsRedeemed: availableDiscount.eligibility.pointsRequired,
      maxAmount: availableDiscount.eligibility.maxAmount,
    };

    onApplyDiscount(discount);
    setModalVisible(false);
  };

  // Render discount button
  const renderDiscountButton = () => {
    const display = getDiscountDisplay();
    const buttonSize = size === "small" ? 28 : size === "large" ? 40 : 32;

    return (
      <TouchableOpacity
        style={[
          styles.discountButton,
          {
            width: buttonSize,
            height: buttonSize,
            backgroundColor: hasDiscount
              ? `${display.color}20`
              : theme.colors.surface,
            borderColor: hasDiscount ? display.color : theme.colors.border,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
        onPress={() => setModalVisible(true)}
        disabled={disabled}
      >
        <IconSymbol
          name={hasDiscount ? "tag.fill" : "tag"}
          size={size === "small" ? 14 : size === "large" ? 20 : 16}
          color={hasDiscount ? display.color : theme.colors.textSecondary}
        />
        {hasDiscount && display.text && (
          <Text
            style={[
              styles.discountText,
              {
                color: display.color,
                fontSize: size === "small" ? 10 : size === "large" ? 14 : 12,
              },
            ]}
          >
            {display.text}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  // Render tab bar
  const renderTabBar = () => {
    const tabs = [
      { key: "available", label: "Available", icon: "star.fill" },
      ...(staffPermissions?.canApplyManualDiscounts
        ? [{ key: "manual" as const, label: "Manual", icon: "pencil" }]
        : []),
      ...(customerLoyalty
        ? [{ key: "loyalty" as const, label: "Loyalty", icon: "crown.fill" }]
        : []),
    ];

    return (
      <View style={styles.tabBar}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              activeTab === tab.key && styles.activeTabButton,
              { borderBottomColor: theme.colors.primary },
            ]}
            onPress={() => setActiveTab(tab.key)}
          >
            <IconSymbol
              name={tab.icon}
              size={16}
              color={
                activeTab === tab.key
                  ? theme.colors.primary
                  : theme.colors.textSecondary
              }
            />
            <ThemedText
              variant="small"
              style={[
                styles.tabText,
                {
                  color:
                    activeTab === tab.key
                      ? theme.colors.primary
                      : theme.colors.textSecondary,
                },
              ]}
            >
              {tab.label}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render available discounts tab
  const renderAvailableDiscountsTab = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <ThemedText variant="body" color="secondary" style={utils.mt("md")}>
            Loading available discounts...
          </ThemedText>
        </View>
      );
    }

    if (availableDiscounts.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <IconSymbol name="tag" size={48} color={theme.colors.textSecondary} />
          <ThemedText variant="h3" color="secondary" style={utils.mt("md")}>
            No Discounts Available
          </ThemedText>
          <ThemedText
            variant="body"
            color="muted"
            style={[utils.mt("sm"), styles.emptyText]}
          >
            No applicable discounts found for this item and customer.
          </ThemedText>
        </View>
      );
    }

    return (
      <View style={styles.discountsList}>
        {availableDiscounts.map((discount) => (
          <TouchableOpacity
            key={discount.id}
            style={[
              styles.discountCard,
              {
                backgroundColor: discount.eligibility.canApply
                  ? theme.colors.surface
                  : theme.colors.surface + "50",
                borderColor: discount.color || theme.colors.border,
                opacity: discount.eligibility.canApply ? 1 : 0.6,
              },
            ]}
            onPress={() => handleSelectAvailableDiscount(discount)}
            disabled={!discount.eligibility.canApply}
          >
            <View style={styles.discountCardHeader}>
              <View style={styles.discountCardIcon}>
                <IconSymbol
                  name={discount.icon || "tag.fill"}
                  size={20}
                  color={discount.color || theme.colors.primary}
                />
              </View>
              <View style={styles.discountCardInfo}>
                <ThemedText variant="body" style={styles.discountCardTitle}>
                  {discount.name}
                </ThemedText>
                <ThemedText variant="small" color="secondary">
                  {discount.description}
                </ThemedText>
              </View>
              <View style={styles.discountCardValue}>
                <ThemedText variant="h3" color="primary">
                  {discount.type === "percentage"
                    ? `${discount.value}%`
                    : discount.type === "fixed_amount"
                    ? formatCurrency(discount.value)
                    : `${discount.value} pts`}
                </ThemedText>
                {discount.source === "loyalty" && (
                  <ThemedText variant="caption" color="secondary">
                    {discount.tier} tier
                  </ThemedText>
                )}
              </View>
            </View>

            {!discount.eligibility.canApply && discount.eligibility.reason && (
              <View style={styles.discountCardFooter}>
                <IconSymbol
                  name="exclamationmark.triangle"
                  size={14}
                  color={theme.colors.warning}
                />
                <ThemedText
                  variant="small"
                  color="warning"
                  style={styles.reasonText}
                >
                  {discount.eligibility.reason}
                </ThemedText>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render manual discount tab
  const renderManualDiscountTab = () => {
    return (
      <View style={styles.manualDiscountContainer}>
        {/* Discount Type Selection */}
        <View style={styles.typeSelection}>
          <ThemedText variant="h3" style={utils.mb("md")}>
            Discount Type
          </ThemedText>
          <View style={styles.typeButtons}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor:
                    manualType === "percentage"
                      ? theme.colors.primary
                      : theme.colors.surface,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={() => setManualType("percentage")}
            >
              <ThemedText
                variant="body"
                style={{
                  color:
                    manualType === "percentage"
                      ? theme.colors.primaryForeground
                      : theme.colors.text,
                }}
              >
                Percentage (%)
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor:
                    manualType === "fixed_amount"
                      ? theme.colors.primary
                      : theme.colors.surface,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={() => setManualType("fixed_amount")}
            >
              <ThemedText
                variant="body"
                style={{
                  color:
                    manualType === "fixed_amount"
                      ? theme.colors.primaryForeground
                      : theme.colors.text,
                }}
              >
                Fixed Amount
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>

        {/* Amount Input */}
        <View style={styles.inputSection}>
          <ThemedText variant="body" style={utils.mb("sm")}>
            {manualType === "percentage" ? "Percentage" : "Amount"}
          </ThemedText>
          <TextInput
            style={[
              styles.amountInput,
              {
                color: theme.colors.text,
                borderColor: theme.colors.border,
                backgroundColor: theme.colors.surface,
              },
            ]}
            value={manualAmount}
            onChangeText={setManualAmount}
            placeholder={manualType === "percentage" ? "10" : "5.00"}
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            maxLength={manualType === "percentage" ? 3 : 10}
          />
        </View>

        {/* Description Input */}
        <View style={styles.inputSection}>
          <ThemedText variant="body" style={utils.mb("sm")}>
            Description (Optional)
          </ThemedText>
          <TextInput
            style={[
              styles.descriptionInput,
              {
                color: theme.colors.text,
                borderColor: theme.colors.border,
                backgroundColor: theme.colors.surface,
              },
            ]}
            value={manualDescription}
            onChangeText={setManualDescription}
            placeholder="Staff discount, promotion, etc."
            placeholderTextColor={theme.colors.textSecondary}
            maxLength={100}
          />
        </View>

        {/* Staff Permissions Info */}
        {staffPermissions && (
          <ThemedView
            variant="card"
            style={[styles.permissionsCard, utils.p("md")]}
          >
            <ThemedText
              variant="small"
              color="secondary"
              style={utils.mb("sm")}
            >
              Your Discount Limits:
            </ThemedText>
            {staffPermissions.maxDiscountPercentage && (
              <ThemedText variant="caption" color="muted">
                • Max percentage: {staffPermissions.maxDiscountPercentage}%
              </ThemedText>
            )}
            {staffPermissions.maxDiscountAmount && (
              <ThemedText variant="caption" color="muted">
                • Max amount:{" "}
                {formatCurrency(staffPermissions.maxDiscountAmount)}
              </ThemedText>
            )}
          </ThemedView>
        )}

        {/* Apply Button */}
        <ModernButton
          title="Apply Manual Discount"
          onPress={handleApplyManualDiscount}
          style={styles.applyButton}
          disabled={!manualAmount}
        />
      </View>
    );
  };

  // Render loyalty discount tab
  const renderLoyaltyDiscountTab = () => {
    if (!customerLoyalty) {
      return (
        <View style={styles.emptyContainer}>
          <IconSymbol
            name="crown"
            size={48}
            color={theme.colors.textSecondary}
          />
          <ThemedText variant="h3" color="secondary" style={utils.mt("md")}>
            No Loyalty Data
          </ThemedText>
          <ThemedText
            variant="body"
            color="muted"
            style={[utils.mt("sm"), styles.emptyText]}
          >
            Customer loyalty information is not available.
          </ThemedText>
        </View>
      );
    }

    const loyaltyDiscounts = availableDiscounts.filter(
      (d) => d.source === "loyalty"
    );

    return (
      <View style={styles.loyaltyContainer}>
        {/* Customer Loyalty Status */}
        <ThemedView
          variant="card"
          style={[styles.loyaltyStatusCard, utils.p("lg")]}
        >
          <View style={styles.loyaltyHeader}>
            <View style={styles.tierBadge}>
              <IconSymbol
                name="crown.fill"
                size={20}
                color={theme.colors.warning}
              />
              <ThemedText variant="h3" style={{ color: theme.colors.warning }}>
                {customerLoyalty.tier.charAt(0).toUpperCase() +
                  customerLoyalty.tier.slice(1)}{" "}
                Member
              </ThemedText>
            </View>
            <View style={styles.pointsDisplay}>
              <ThemedText variant="h2" color="primary">
                {(customerLoyalty.points || 0).toLocaleString()}
              </ThemedText>
              <ThemedText variant="small" color="secondary">
                Available Points
              </ThemedText>
            </View>
          </View>
        </ThemedView>

        {/* Loyalty-based Discounts */}
        {loyaltyDiscounts.length > 0 ? (
          <View style={styles.loyaltyDiscountsList}>
            <ThemedText variant="h3" style={utils.mb("md")}>
              Tier Benefits
            </ThemedText>
            {loyaltyDiscounts.map((discount) => (
              <TouchableOpacity
                key={discount.id}
                style={[
                  styles.loyaltyDiscountCard,
                  {
                    backgroundColor: discount.eligibility.canApply
                      ? theme.colors.surface
                      : theme.colors.surface + "50",
                    borderColor: theme.colors.warning,
                    opacity: discount.eligibility.canApply ? 1 : 0.6,
                  },
                ]}
                onPress={() => handleSelectAvailableDiscount(discount)}
                disabled={!discount.eligibility.canApply}
              >
                <View style={styles.loyaltyDiscountHeader}>
                  <IconSymbol
                    name="star.fill"
                    size={16}
                    color={theme.colors.warning}
                  />
                  <ThemedText
                    variant="body"
                    style={styles.loyaltyDiscountTitle}
                  >
                    {discount.name}
                  </ThemedText>
                  <ThemedText variant="h3" color="warning">
                    {discount.type === "percentage"
                      ? `${discount.value}%`
                      : formatCurrency(discount.value)}
                  </ThemedText>
                </View>
                <ThemedText variant="small" color="secondary">
                  {discount.description}
                </ThemedText>
                {!discount.eligibility.canApply &&
                  discount.eligibility.reason && (
                    <ThemedText
                      variant="small"
                      color="warning"
                      style={utils.mt("sm")}
                    >
                      {discount.eligibility.reason}
                    </ThemedText>
                  )}
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.noLoyaltyDiscounts}>
            <ThemedText variant="body" color="secondary">
              No tier-based discounts available for this item.
            </ThemedText>
          </View>
        )}

        {/* Points Redemption Info */}
        <ThemedView
          variant="card"
          style={[styles.pointsInfoCard, utils.p("md")]}
        >
          <ThemedText variant="small" color="secondary" style={utils.mb("sm")}>
            Points Redemption:
          </ThemedText>
          <ThemedText variant="caption" color="muted">
            • 100 points = {formatCurrency(1)}
          </ThemedText>
          <ThemedText variant="caption" color="muted">
            • Minimum redemption: 100 points
          </ThemedText>
          <ThemedText variant="caption" color="muted">
            • Maximum per order: 5,000 points
          </ThemedText>
        </ThemedView>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderDiscountButton()}

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <ThemedView variant="background" style={styles.modalContainer}>
          {/* Header */}
          <View
            style={[
              styles.modalHeader,
              { borderBottomColor: theme.colors.border },
            ]}
          >
            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              style={styles.closeButton}
            >
              <IconSymbol name="xmark" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <ThemedText variant="h2">Apply Discount</ThemedText>
            <View style={styles.placeholder} />
          </View>

          {/* Line Total Display */}
          <ThemedView
            variant="card"
            style={[styles.totalCard, utils.p("md"), utils.m("md")]}
          >
            <View style={styles.totalRow}>
              <ThemedText variant="body" color="secondary">
                Line Total
              </ThemedText>
              <ThemedText variant="h3">{formatCurrency(lineTotal)}</ThemedText>
            </View>
            {hasDiscount && (
              <View style={styles.totalRow}>
                <ThemedText variant="body" color="secondary">
                  After Discount
                </ThemedText>
                <ThemedText variant="h3" color="success">
                  {formatCurrency(
                    lineTotal - calculateDiscountAmount(currentDiscount!)
                  )}
                </ThemedText>
              </View>
            )}
          </ThemedView>

          {/* Tab Bar */}
          {showAdvanced && renderTabBar()}

          {/* Tab Content */}
          <ScrollView
            style={styles.tabContent}
            showsVerticalScrollIndicator={false}
          >
            {activeTab === "available" && renderAvailableDiscountsTab()}
            {activeTab === "manual" && renderManualDiscountTab()}
            {activeTab === "loyalty" && renderLoyaltyDiscountTab()}
          </ScrollView>
        </ThemedView>
      </Modal>
    </View>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      alignItems: "center",
    },
    discountButton: {
      borderRadius: 6,
      borderWidth: 1,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row",
      paddingHorizontal: 4,
    },
    discountText: {
      marginLeft: 4,
      fontWeight: "600",
    },
    modalContainer: {
      flex: 1,
    },
    modalHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
    },
    closeButton: {
      padding: 8,
    },
    placeholder: {
      width: 40,
    },
    totalCard: {
      marginHorizontal: 16,
      marginVertical: 8,
    },
    totalRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 4,
    },
    tabBar: {
      flexDirection: "row",
      paddingHorizontal: 16,
    },
    tabButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      borderBottomWidth: 2,
      borderBottomColor: "transparent",
      gap: 6,
    },
    activeTabButton: {
      borderBottomWidth: 2,
    },
    tabText: {
      fontWeight: "600",
    },
    tabContent: {
      flex: 1,
      padding: 16,
    },
    loadingContainer: {
      alignItems: "center",
      paddingVertical: 48,
    },
    emptyContainer: {
      alignItems: "center",
      paddingVertical: 48,
    },
    emptyText: {
      textAlign: "center",
      maxWidth: 280,
    },
    discountsList: {
      gap: 12,
    },
    discountCard: {
      borderRadius: 12,
      borderWidth: 1,
      padding: 16,
    },
    discountCardHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    discountCardIcon: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
      backgroundColor: theme.colors.surfaceSecondary,
    },
    discountCardInfo: {
      flex: 1,
    },
    discountCardTitle: {
      fontWeight: "600",
    },
    discountCardValue: {
      alignItems: "flex-end",
    },
    discountCardFooter: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 8,
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.borderLight,
    },
    reasonText: {
      marginLeft: 6,
      flex: 1,
    },
    manualDiscountContainer: {
      gap: 20,
    },
    typeSelection: {
      marginBottom: 16,
    },
    typeButtons: {
      flexDirection: "row",
      gap: 12,
    },
    typeButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
      alignItems: "center",
    },
    inputSection: {
      marginBottom: 16,
    },
    amountInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
    },
    descriptionInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 14,
      minHeight: 44,
    },
    permissionsCard: {
      marginBottom: 16,
    },
    applyButton: {
      marginTop: 8,
    },
    loyaltyContainer: {
      gap: 16,
    },
    loyaltyStatusCard: {
      marginBottom: 8,
    },
    loyaltyHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    tierBadge: {
      flexDirection: "row",
      alignItems: "center",
      gap: 8,
    },
    pointsDisplay: {
      alignItems: "flex-end",
    },
    loyaltyDiscountsList: {
      gap: 12,
    },
    loyaltyDiscountCard: {
      borderRadius: 8,
      borderWidth: 1,
      padding: 12,
    },
    loyaltyDiscountHeader: {
      flexDirection: "row",
      alignItems: "center",
      gap: 8,
      marginBottom: 4,
    },
    loyaltyDiscountTitle: {
      flex: 1,
      fontWeight: "600",
    },
    noLoyaltyDiscounts: {
      alignItems: "center",
      paddingVertical: 24,
    },
    pointsInfoCard: {
      marginTop: 8,
    },
  });
