import {
  AnimatedButton,
  FadeInView,
} from "@/src/components/animated/AnimatedComponents";
import {
  ThemedText,
  ThemedView,
} from "@/src/components/themed/ThemedComponents";
import { useTheme } from "@/src/contexts/ThemeContext";
import { createStyleUtils } from "@/src/utils/themeUtils";
import React from "react";
import { Image, StyleSheet, ViewStyle } from "react-native";
import { IconSymbol } from "./IconSymbol";

interface CartItem {
  variantId: string;
  productId: string;
  title: string;
  variantTitle?: string;
  price: string;
  quantity: number;
  image?: string;
  sku?: string;
  inventoryQuantity?: number;
}

interface CartItemCardProps {
  item: CartItem;
  onUpdateQuantity: (variantId: string, quantity: number) => void;
  onRemove: (variantId: string) => void;
  style?: ViewStyle;
  index?: number;
}

export function CartItemCard({
  item,
  onUpdateQuantity,
  onRemove,
  style,
  index = 0,
}: CartItemCardProps) {
  const theme = useTheme();
  const utils = createStyleUtils(theme);

  const handleIncrement = () => {
    onUpdateQuantity(item.variantId, item.quantity + 1);
  };

  const handleDecrement = () => {
    if (item.quantity > 1) {
      onUpdateQuantity(item.variantId, item.quantity - 1);
    }
  };

  const handleRemove = () => {
    onRemove(item.variantId);
  };

  const totalPrice = (parseFloat(item.price) * item.quantity).toFixed(2);

  return (
    <FadeInView delay={index * 50}>
      <ThemedView
        variant="card"
        elevated={true}
        style={[styles.cartItem, style]}
      >
        <ThemedView style={styles.itemContent}>
          {/* Product Image */}
          {item.image && (
            <ThemedView style={styles.imageContainer}>
              <Image
                source={{ uri: item.image }}
                style={[
                  styles.itemImage,
                  { borderRadius: theme.borderRadius.sm },
                ]}
                resizeMode="cover"
              />
            </ThemedView>
          )}

          {/* Item Details */}
          <ThemedView style={styles.itemDetails}>
            <ThemedText
              variant="bodyMedium"
              style={[styles.itemTitle, utils.mb("xs")]}
              numberOfLines={2}
            >
              {item.title}
            </ThemedText>

            {item.variantTitle && item.variantTitle !== "Default Title" && (
              <ThemedText
                variant="caption"
                color="secondary"
                style={utils.mb("xs")}
              >
                {item.variantTitle}
              </ThemedText>
            )}

            <ThemedText variant="h4" color="accent" style={utils.mb("xs")}>
              KES {parseFloat(item.price).toFixed(2)}
            </ThemedText>

            {item.sku && (
              <ThemedText
                variant="small"
                color="secondary"
                style={utils.mb("sm")}
              >
                SKU: {item.sku}
              </ThemedText>
            )}

            {/* Quantity Controls */}
            <ThemedView style={styles.quantityContainer}>
              <AnimatedButton
                style={[
                  styles.quantityButton,
                  { backgroundColor: theme.colors.surface },
                ]}
                onPress={handleDecrement}
                disabled={item.quantity <= 1}
                scaleOnPress={true}
              >
                <IconSymbol
                  name="minus"
                  size={16}
                  color={
                    item.quantity <= 1
                      ? theme.colors.textMuted
                      : theme.colors.text
                  }
                />
              </AnimatedButton>

              <ThemedView style={styles.quantityDisplay}>
                <ThemedText variant="bodyMedium" style={styles.quantityText}>
                  {item.quantity}
                </ThemedText>
              </ThemedView>

              <AnimatedButton
                style={[
                  styles.quantityButton,
                  { backgroundColor: theme.colors.surface },
                ]}
                onPress={handleIncrement}
                scaleOnPress={true}
              >
                <IconSymbol name="plus" size={16} color={theme.colors.text} />
              </AnimatedButton>
            </ThemedView>
          </ThemedView>

          {/* Actions */}
          <ThemedView style={styles.itemActions}>
            <ThemedText
              variant="bodyMedium"
              color="accent"
              style={[styles.totalPrice, utils.mb("sm")]}
            >
              KES {totalPrice}
            </ThemedText>

            <AnimatedButton
              style={[
                styles.removeButton,
                { backgroundColor: theme.colors.errorLight },
              ]}
              onPress={handleRemove}
              scaleOnPress={true}
            >
              <IconSymbol name="trash" size={16} color={theme.colors.error} />
            </AnimatedButton>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </FadeInView>
  );
}

const styles = StyleSheet.create({
  cartItem: {
    marginBottom: 12,
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  imageContainer: {
    marginRight: 12,
  },
  itemImage: {
    width: 60,
    height: 60,
  },
  itemDetails: {
    flex: 1,
    marginRight: 12,
  },
  itemTitle: {
    lineHeight: 20,
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
  },
  quantityDisplay: {
    marginHorizontal: 16,
    minWidth: 40,
    alignItems: "center",
  },
  quantityText: {
    fontWeight: "600",
  },
  itemActions: {
    alignItems: "flex-end",
    justifyContent: "space-between",
    minHeight: 80,
  },
  totalPrice: {
    fontWeight: "600",
    textAlign: "right",
  },
  removeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
});
