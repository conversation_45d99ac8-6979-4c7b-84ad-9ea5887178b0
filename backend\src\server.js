const express = require("express");
const cors = require("cors");
const { v4: uuidv4 } = require("uuid");
const { databaseManager } = require("./config/database-manager");
const ticketCleanupService = require("./services/ticket-cleanup-service");
require("dotenv").config();

const app = express();
const PORT = process.env.PORT || 3020;

// CORS Configuration with debugging
const allowedOrigins = [
  "http://localhost:3000",
  "http://localhost:3001", // Next.js dev server
  "http://localhost:8081", // Expo web dev server
  "http://localhost:8082", // Alternative Expo ports
  "http://localhost:8083",
  "http://localhost:8084",
  "http://localhost:8085",
  "http://localhost:19006", // Expo web default port
  "exp://*************:8081",
  "exp://localhost:8081",
  "https://treasuredpos.dukalink.com",
  "https://treasuredposdev.dukalink.com",
  // Add common development ports for web
  "http://127.0.0.1:8081",
  "http://127.0.0.1:19006",
];

console.log("🔧 CORS allowed origins:", allowedOrigins);

// Middleware
app.use(
  cors({
    origin: function (origin, callback) {
      console.log("🌐 CORS request from origin:", origin);
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);

      if (allowedOrigins.indexOf(origin) !== -1) {
        console.log("✅ CORS origin allowed:", origin);
        callback(null, true);
      } else {
        console.log("❌ CORS origin blocked:", origin);
        callback(new Error("Not allowed by CORS"));
      }
    },
    credentials: true,
  })
);
app.use(express.json());

// In-memory storage for demo (replace with database later)
const stores = new Map();
const sessions = new Map();

// Shopify configuration
const SHOPIFY_CONFIG = {
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecret: process.env.SHOPIFY_API_SECRET,
  scopes:
    "read_products,write_orders,read_customers,write_customers,read_inventory,write_inventory",
  hostName: process.env.SHOPIFY_APP_URL || "http://localhost:3020",
};

// Helper function to generate Shopify OAuth URL
function generateShopifyAuthURL(shop) {
  const state = uuidv4();
  const redirectUri = `${SHOPIFY_CONFIG.hostName}/api/auth/shopify/callback`;

  const params = new URLSearchParams({
    client_id: SHOPIFY_CONFIG.apiKey,
    scope: SHOPIFY_CONFIG.scopes,
    redirect_uri: redirectUri,
    state: state,
    "grant_options[]": "per-user",
  });

  sessions.set(state, { shop, timestamp: Date.now() });

  return `https://${shop}/admin/oauth/authorize?${params.toString()}`;
}

// Routes

// POS Authentication routes
app.use("/api/pos", require("./routes/pos-auth"));

// Shopify OAuth routes (for initial store setup)
app.use("/api/shopify", require("./routes/shopify-oauth"));

// Store API routes (direct Shopify integration)
app.use("/api/store", require("./routes/store-api"));

// Shopify metafields routes (loyalty and discount tracking)
app.use("/api/shopify/metafields", require("./routes/shopify-metafields"));

// Staff management routes
app.use("/api", require("./routes/staff-management"));

// Sales agent management routes
app.use("/api", require("./routes/sales-agent-management"));

// Commission-based discount routes
app.use("/api", require("./routes/commission-discounts"));

// Customer loyalty routes
app.use("/api/loyalty", require("./routes/customer-loyalty"));

// Staff discount management routes
app.use("/api/discounts", require("./routes/staff-discount-management"));

// Shopify Functions management routes
app.use("/api/shopify-functions", require("./routes/shopify-functions"));

// Loyalty synchronization routes
app.use("/api/loyalty-sync", require("./routes/loyalty-sync"));

// Shopify webhooks routes
app.use("/api/webhooks", require("./routes/shopify-webhooks"));

// Inventory management routes
app.use("/api", require("./routes/inventory-management"));

// Location management routes
app.use("/api/locations", require("./routes/locations"));

// Ticket management routes
app.use("/api", require("./routes/ticket-management"));

// User switching routes (PIN-based multi-user sessions)
app.use("/api/pos/user-switching", require("./routes/pos-user-switching"));

// Payment processing routes
app.use("/api/payments", require("./routes/payment-processing"));

// Fulfillment management routes
app.use("/api/fulfillment", require("./routes/fulfillment-management"));

// Database monitoring routes
app.use("/api/database", require("./routes/database-monitoring"));

// Health check
app.get("/health", async (req, res) => {
  try {
    // Test database connectivity
    let dbStatus = "OK";
    let dbStats = null;

    try {
      await databaseManager.executeQuery("SELECT 1 as test");
      dbStats = databaseManager.getStats();

      // Check if connection pool is healthy
      const utilizationRate = dbStats.poolConfig?.connectionLimit
        ? dbStats.connectionStats.activeConnections /
          dbStats.poolConfig.connectionLimit
        : 0;

      if (
        utilizationRate > 0.9 ||
        dbStats.connectionStats.queuedRequests > 10
      ) {
        dbStatus = "WARNING";
      }
    } catch (error) {
      dbStatus = "ERROR";
      console.error("Health check database error:", error);
    }

    res.json({
      status: dbStatus === "ERROR" ? "ERROR" : "OK",
      timestamp: new Date().toISOString(),
      services: {
        auth: "MySQL RBAC authentication active",
        commission: "MySQL commission system active",
        terminals: "MySQL terminal management active",
        database: {
          status: dbStatus,
          connectionPool: dbStats
            ? {
                active: dbStats.connectionStats.activeConnections,
                limit: dbStats.poolConfig?.connectionLimit || 0,
                queued: dbStats.connectionStats.queuedRequests,
              }
            : null,
        },
      },
      monitoring: {
        endpoint: "/api/database/health",
        stats: "/api/database/stats",
        leaks: "/api/database/leaks",
      },
    });
  } catch (error) {
    console.error("Health check error:", error);
    res.status(500).json({
      status: "ERROR",
      timestamp: new Date().toISOString(),
      error: "Health check failed",
    });
  }
});

// Get Shopify OAuth URL
app.post("/api/auth/shopify/url", (req, res) => {
  try {
    const { shopDomain } = req.body;

    if (!shopDomain) {
      return res.status(400).json({
        success: false,
        error: "Shop domain is required",
      });
    }

    // Clean and validate shop domain
    const cleanDomain = shopDomain
      .replace(/^https?:\/\//, "")
      .replace(/\/$/, "");

    if (!cleanDomain.endsWith(".myshopify.com")) {
      return res.status(400).json({
        success: false,
        error: "Invalid Shopify domain",
      });
    }

    const authUrl = generateShopifyAuthURL(cleanDomain);

    res.json({
      success: true,
      data: { authUrl },
    });
  } catch (error) {
    console.error("Error generating auth URL:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate auth URL",
    });
  }
});

// Handle Shopify OAuth callback
app.get("/api/auth/shopify/callback", async (req, res) => {
  try {
    const { code, state, shop } = req.query;

    if (!code || !state) {
      return res.status(400).json({
        success: false,
        error: "Missing authorization code or state",
      });
    }

    // Verify state
    const sessionData = sessions.get(state);
    if (!sessionData) {
      return res.status(400).json({
        success: false,
        error: "Invalid state parameter",
      });
    }

    // Exchange code for access token
    const tokenResponse = await fetch(
      `https://${shop}/admin/oauth/access_token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          client_id: SHOPIFY_CONFIG.apiKey,
          client_secret: SHOPIFY_CONFIG.apiSecret,
          code: code,
        }),
      }
    );

    if (!tokenResponse.ok) {
      throw new Error("Failed to exchange code for token");
    }

    const tokenData = await tokenResponse.json();

    // Store shop data
    const storeId = uuidv4();
    const sessionToken = uuidv4();

    stores.set(storeId, {
      id: storeId,
      domain: shop,
      accessToken: tokenData.access_token,
      scope: tokenData.scope,
      createdAt: new Date().toISOString(),
    });

    sessions.set(sessionToken, {
      storeId: storeId,
      createdAt: Date.now(),
    });

    // Clean up OAuth state
    sessions.delete(state);

    // Redirect to mobile app with success
    res.redirect(
      `dukalink://auth?success=true&token=${sessionToken}&storeId=${storeId}`
    );
  } catch (error) {
    console.error("OAuth callback error:", error);
    res.redirect(
      `dukalink://auth?success=false&error=${encodeURIComponent(error.message)}`
    );
  }
});

// Exchange auth code (for mobile app)
app.post("/api/auth/shopify/exchange", async (req, res) => {
  try {
    const { code, state } = req.body;

    // Verify state and get shop
    const sessionData = sessions.get(state);
    if (!sessionData) {
      return res.status(400).json({
        success: false,
        error: "Invalid state parameter",
      });
    }

    const shop = sessionData.shop;

    // Exchange code for access token
    const tokenResponse = await fetch(
      `https://${shop}/admin/oauth/access_token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          client_id: SHOPIFY_CONFIG.apiKey,
          client_secret: SHOPIFY_CONFIG.apiSecret,
          code: code,
        }),
      }
    );

    if (!tokenResponse.ok) {
      throw new Error("Failed to exchange code for token");
    }

    const tokenData = await tokenResponse.json();

    // Store shop data
    const storeId = uuidv4();
    const sessionToken = uuidv4();

    stores.set(storeId, {
      id: storeId,
      domain: shop,
      name: shop.replace(".myshopify.com", ""),
      accessToken: tokenData.access_token,
      scope: tokenData.scope,
      createdAt: new Date().toISOString(),
    });

    sessions.set(sessionToken, {
      storeId: storeId,
      createdAt: Date.now(),
    });

    // Clean up OAuth state
    sessions.delete(state);

    res.json({
      success: true,
      data: {
        sessionToken: sessionToken,
        store: {
          id: storeId,
          domain: shop,
          name: shop.replace(".myshopify.com", ""),
          isConnected: true,
        },
      },
    });
  } catch (error) {
    console.error("Token exchange error:", error);
    res.status(500).json({
      success: false,
      error: error.message || "Failed to exchange token",
    });
  }
});

// Verify session
app.get("/api/auth/verify", (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({
        success: false,
        error: "No valid authorization token",
      });
    }

    const token = authHeader.substring(7);
    const session = sessions.get(token);

    if (!session) {
      return res.status(401).json({
        success: false,
        error: "Invalid session token",
      });
    }

    const store = stores.get(session.storeId);
    if (!store) {
      return res.status(404).json({
        success: false,
        error: "Store not found",
      });
    }

    res.json({
      success: true,
      data: {
        store: {
          id: store.id,
          domain: store.domain,
          name: store.name || store.domain.replace(".myshopify.com", ""),
          isConnected: true,
        },
      },
    });
  } catch (error) {
    console.error("Session verification error:", error);
    res.status(500).json({
      success: false,
      error: "Session verification failed",
    });
  }
});

// Middleware to authenticate requests
function authenticateRequest(req, res, next) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({
      success: false,
      error: "No valid authorization token",
    });
  }

  const token = authHeader.substring(7);
  const session = sessions.get(token);

  if (!session) {
    return res.status(401).json({
      success: false,
      error: "Invalid session token",
    });
  }

  const store = stores.get(session.storeId);
  if (!store) {
    return res.status(404).json({
      success: false,
      error: "Store not found",
    });
  }

  req.store = store;
  req.session = session;
  next();
}

// Get products
app.get(
  "/api/stores/:storeId/products",
  authenticateRequest,
  async (req, res) => {
    try {
      const { store } = req;
      const { page = 1, limit = 50, search = "" } = req.query;

      // GraphQL query for products
      const query = `
      query getProducts($first: Int!, $query: String) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              title
              handle
              description
              productType
              vendor
              tags
              images(first: 5) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    price
                    sku
                    barcode
                    inventoryQuantity
                    weight
                    weightUnit
                  }
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            hasPreviousPage
          }
        }
      }
    `;

      const variables = {
        first: parseInt(limit),
        query: search || null,
      };

      const response = await fetch(
        `https://${store.domain}/admin/api/2023-10/graphql.json`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": store.accessToken,
          },
          body: JSON.stringify({ query, variables }),
        }
      );

      if (!response.ok) {
        throw new Error(`Shopify API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
      }

      // Transform data for mobile app
      const products = data.data.products.edges.map((edge) => ({
        id: edge.node.id,
        title: edge.node.title,
        handle: edge.node.handle,
        description: edge.node.description,
        productType: edge.node.productType,
        vendor: edge.node.vendor,
        tags: edge.node.tags,
        images: edge.node.images.edges.map((img) => ({
          id: img.node.id,
          url: img.node.url,
          altText: img.node.altText,
        })),
        variants: edge.node.variants.edges.map((variant) => ({
          id: variant.node.id,
          title: variant.node.title,
          price: variant.node.price,
          sku: variant.node.sku,
          barcode: variant.node.barcode,
          inventoryQuantity: variant.node.inventoryQuantity,
          weight: variant.node.weight,
          weightUnit: variant.node.weightUnit,
        })),
      }));

      res.json({
        success: true,
        data: {
          products,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            hasNext: data.data.products.pageInfo.hasNextPage,
            hasPrev: data.data.products.pageInfo.hasPreviousPage,
          },
        },
      });
    } catch (error) {
      console.error("Error fetching products:", error);
      res.status(500).json({
        success: false,
        error: error.message || "Failed to fetch products",
      });
    }
  }
);

// Create order
app.post(
  "/api/stores/:storeId/orders",
  authenticateRequest,
  async (req, res) => {
    try {
      const { store } = req;
      const { lineItems, customer, note, salespersonId, salespersonName } =
        req.body;

      if (!lineItems || lineItems.length === 0) {
        return res.status(400).json({
          success: false,
          error: "Line items are required",
        });
      }

      // GraphQL mutation for creating order
      const mutation = `
      mutation orderCreate($order: OrderInput!) {
        orderCreate(order: $order) {
          order {
            id
            name
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            subtotalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            totalTaxSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            lineItems(first: 50) {
              edges {
                node {
                  id
                  title
                  quantity
                  variant {
                    id
                    title
                    price
                  }
                }
              }
            }
            customer {
              id
              firstName
              lastName
              email
            }
            customAttributes {
              key
              value
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

      // Prepare order input
      const orderInput = {
        lineItems: lineItems.map((item) => ({
          variantId: item.variantId,
          quantity: item.quantity,
        })),
        customAttributes: [],
      };

      // Add customer if provided
      if (customer) {
        if (customer.id) {
          orderInput.customerId = customer.id;
        } else {
          orderInput.customer = {
            firstName: customer.firstName,
            lastName: customer.lastName,
            email: customer.email,
            phone: customer.phone,
          };
        }
      }

      // Add salesperson info as custom attributes
      if (salespersonId) {
        orderInput.customAttributes.push({
          key: "salesperson_id",
          value: salespersonId,
        });
      }
      if (salespersonName) {
        orderInput.customAttributes.push({
          key: "salesperson_name",
          value: salespersonName,
        });
      }

      // Add note if provided
      if (note) {
        orderInput.note = note;
      }

      const variables = { order: orderInput };

      const response = await fetch(
        `https://${store.domain}/admin/api/2023-10/graphql.json`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": store.accessToken,
          },
          body: JSON.stringify({ query: mutation, variables }),
        }
      );

      if (!response.ok) {
        throw new Error(`Shopify API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
      }

      if (data.data.orderCreate.userErrors.length > 0) {
        throw new Error(
          `Order creation errors: ${JSON.stringify(
            data.data.orderCreate.userErrors
          )}`
        );
      }

      const order = data.data.orderCreate.order;

      // Transform order data for mobile app
      const transformedOrder = {
        id: order.id,
        name: order.name,
        totalPrice: order.totalPriceSet.shopMoney.amount,
        subtotalPrice: order.subtotalPriceSet.shopMoney.amount,
        totalTax: order.totalTaxSet.shopMoney.amount,
        currency: order.totalPriceSet.shopMoney.currencyCode,
        lineItems: order.lineItems.edges.map((edge) => ({
          id: edge.node.id,
          title: edge.node.title,
          quantity: edge.node.quantity,
          variant: {
            id: edge.node.variant.id,
            title: edge.node.variant.title,
            price: edge.node.variant.price,
          },
        })),
        customer: order.customer
          ? {
              id: order.customer.id,
              firstName: order.customer.firstName,
              lastName: order.customer.lastName,
              email: order.customer.email,
            }
          : null,
        customAttributes: order.customAttributes || [],
      };

      res.json({
        success: true,
        data: { order: transformedOrder },
      });
    } catch (error) {
      console.error("Error creating order:", error);
      res.status(500).json({
        success: false,
        error: error.message || "Failed to create order",
      });
    }
  }
);

// Get single order by ID
app.get(
  "/api/stores/:storeId/orders/:orderId",
  require("./middleware/auth").authenticateToken,
  async (req, res) => {
    try {
      const { orderId } = req.params;

      // Use the existing shopify service
      const result = await require("./services/shopify-service").getOrderById(
        orderId
      );

      if (result.success) {
        res.json({
          success: true,
          data: result.order,
        });
      } else {
        res.status(404).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      console.error("Error fetching order:", error);
      res.status(500).json({
        success: false,
        error: error.message || "Failed to fetch order",
      });
    }
  }
);

// Initialize database and start server
async function startServer() {
  try {
    // Initialize centralized database manager
    console.log("🔌 Initializing database connection pool...");
    await databaseManager.initialize();
    console.log("✅ Database connection pool initialized");

    // Start the server
    app.listen(PORT, () => {
      console.log(`🚀 Dukalink POS Backend running on port ${PORT}`);
      console.log(`📱 Health check: http://localhost:${PORT}/health`);
      console.log(
        `🔐 Auth endpoint: http://localhost:${PORT}/api/auth/shopify/url`
      );

      // Start ticket cleanup service
      try {
        ticketCleanupService.start();
        console.log(`🧹 Ticket cleanup service started`);
      } catch (error) {
        console.error(`❌ Failed to start ticket cleanup service:`, error);
      }
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Received SIGINT, shutting down gracefully...");
  await databaseManager.shutdown();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
  await databaseManager.shutdown();
  process.exit(0);
});

// Start the server
startServer();

module.exports = app;
