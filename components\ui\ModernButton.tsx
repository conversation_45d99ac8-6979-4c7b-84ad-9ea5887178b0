import { useTheme } from "@/src/contexts/ThemeContext";
import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  ViewStyle,
  Platform,
} from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

interface ModernButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
}

export function ModernButton({
  title,
  onPress,
  variant = "primary",
  size = "md",
  loading = false,
  disabled = false,
  style,
  textStyle,
  icon,
}: ModernButtonProps) {
  const theme = useTheme();
  const { getButtonSize, spacingMultiplier } = useResponsiveLayout();

  const getButtonStyle = () => {
    const responsiveButtonSize = getButtonSize(size);

    const baseStyle = {
      borderRadius: theme.borderRadius.md,
      flexDirection: "row" as const,
      alignItems: "center" as const,
      justifyContent: "center" as const,
      gap: theme.spacing.sm * spacingMultiplier,
      paddingHorizontal: responsiveButtonSize.paddingHorizontal,
      height: responsiveButtonSize.height,
    };

    const variantStyle = {
      primary: {
        backgroundColor: theme.colors.primary,
      },
      secondary: {
        backgroundColor: theme.colors.secondary,
      },
      outline: {
        backgroundColor: "transparent",
        borderWidth: 1,
        borderColor: theme.colors.primary,
      },
      ghost: {
        backgroundColor: "transparent",
      },
    };

    return [baseStyle, variantStyle[variant]];
  };

  const getTextColor = (): string => {
    switch (variant) {
      case "primary":
        return theme.colors.primaryForeground;
      case "secondary":
        return theme.colors.secondaryForeground;
      case "outline":
        return theme.colors.text;
      case "ghost":
        return theme.colors.primary;
      default:
        return theme.colors.primaryForeground;
    }
  };

  return (
    <TouchableOpacity
      style={[
        getButtonStyle(),
        (disabled || loading) && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={getTextColor()} size="small" />
      ) : (
        <>
          {icon}
          <Text
            style={[
              {
                color: getTextColor(),
                fontSize: getButtonSize(size).fontSize,
                fontFamily: theme.typography.button.fontFamily,
              },
              textStyle,
            ]}
          >
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  disabled: {
    opacity: 0.6,
  },
});
