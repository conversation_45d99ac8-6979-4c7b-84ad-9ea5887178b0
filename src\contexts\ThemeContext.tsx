import { Colors } from "@/constants/Colors";
import { BorderRadius, Shadows, Spacing, Typography } from "@/constants/Design";
import { useColorScheme } from "@/hooks/useColorScheme";
import AsyncStorage from "@react-native-async-storage/async-storage";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

// Theme mode type
export type ThemeMode = "light" | "dark" | "system";

// Enhanced theme interface
export interface Theme {
  colors: typeof Colors.light;
  typography: typeof Typography;
  spacing: typeof Spacing;
  borderRadius: typeof BorderRadius;
  shadows: typeof Shadows;
  isDark: boolean;
  mode: ThemeMode;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
}

// Create theme context
const ThemeContext = createContext<Theme | undefined>(undefined);

// Theme provider props
interface ThemeProviderProps {
  children: ReactNode;
}

const THEME_STORAGE_KEY = "@dukalink_theme_mode";

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeModeState] = useState<ThemeMode>("dark"); // Default to dark mode
  const [isLoaded, setIsLoaded] = useState(false);

  // Load theme preference from storage
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedMode = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedMode && ["light", "dark", "system"].includes(savedMode)) {
          setThemeModeState(savedMode as ThemeMode);
        }
      } catch (error) {
        console.warn("Failed to load theme preference:", error);
      } finally {
        setIsLoaded(true);
      }
    };

    loadThemePreference();
  }, []);

  // Save theme preference to storage
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
      setThemeModeState(mode);
    } catch (error) {
      console.warn("Failed to save theme preference:", error);
      setThemeModeState(mode); // Still update state even if storage fails
    }
  };

  // Toggle between light and dark modes
  const toggleTheme = () => {
    const newMode = themeMode === "dark" ? "light" : "dark";
    setThemeMode(newMode);
  };

  // Determine actual theme based on mode
  const getActualTheme = (): boolean => {
    switch (themeMode) {
      case "light":
        return false;
      case "dark":
        return true;
      case "system":
        return systemColorScheme === "dark";
      default:
        return true; // Default to dark
    }
  };

  const isDark = getActualTheme();

  const theme: Theme = {
    colors: isDark ? Colors.dark : Colors.light,
    typography: Typography,
    spacing: Spacing,
    borderRadius: BorderRadius,
    shadows: Shadows,
    isDark,
    mode: themeMode,
    toggleTheme,
    setThemeMode,
  };

  // Don't render until theme is loaded
  if (!isLoaded) {
    return null;
  }

  return (
    <ThemeContext.Provider value={theme}>{children}</ThemeContext.Provider>
  );
};

// Hook to use theme
export const useTheme = (): Theme => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    // Provide a fallback theme instead of throwing an error
    console.warn(
      "useTheme called outside of ThemeProvider, using fallback theme"
    );
    return {
      colors: Colors.dark, // Default to dark theme
      typography: Typography,
      spacing: Spacing,
      borderRadius: BorderRadius,
      shadows: Shadows,
      isDark: true,
      mode: "dark" as ThemeMode,
      toggleTheme: () => {},
      setThemeMode: () => {},
    };
  }
  return context;
};

// Utility hooks for specific theme properties
export const useColors = () => {
  const theme = useTheme();
  return theme.colors;
};

export const useTypography = () => {
  const theme = useTheme();
  return theme.typography;
};

export const useSpacing = () => {
  const theme = useTheme();
  return theme.spacing;
};

export const useBorderRadius = () => {
  const theme = useTheme();
  return theme.borderRadius;
};

export const useShadows = () => {
  const theme = useTheme();
  return theme.shadows;
};

// Utility function to create themed styles
export const createThemedStyles = <T extends Record<string, any>>(
  styleFactory: (theme: Theme) => T
) => {
  return (theme: Theme): T => styleFactory(theme);
};

// Common style utilities
export const getTextStyle = (
  theme: Theme,
  variant: keyof typeof Typography
) => {
  return {
    ...theme.typography[variant],
    color: theme.colors.text,
  };
};

export const getButtonStyle = (
  theme: Theme,
  variant: "primary" | "secondary" | "outline" = "primary",
  size: "small" | "medium" | "large" = "medium"
) => {
  const baseStyle = {
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    alignItems: "center" as const,
    justifyContent: "center" as const,
    ...theme.shadows.sm,
  };

  const sizeStyles = {
    small: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      ...theme.typography.buttonSmall,
    },
    medium: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      ...theme.typography.button,
    },
    large: {
      paddingHorizontal: theme.spacing.xl,
      paddingVertical: theme.spacing.lg,
      ...theme.typography.buttonLarge,
    },
  };

  const variantStyles = {
    primary: {
      backgroundColor: theme.colors.primary,
    },
    secondary: {
      backgroundColor: theme.colors.secondary,
    },
    outline: {
      backgroundColor: "transparent",
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
  };

  return {
    ...baseStyle,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };
};

export const getCardStyle = (theme: Theme, elevated: boolean = true) => {
  return {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    borderWidth: 1,
    borderColor: theme.colors.cardBorder,
    ...(elevated ? theme.shadows.md : {}),
  };
};

export const getInputStyle = (theme: Theme, focused: boolean = false) => {
  return {
    backgroundColor: theme.colors.input,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderWidth: 1,
    borderColor: focused ? theme.colors.inputFocus : theme.colors.inputBorder,
    ...theme.typography.body,
    color: theme.colors.text,
  };
};

// Animation timing constants
export const AnimationTiming = {
  fast: 200,
  normal: 300,
  slow: 500,
  verySlow: 800,
};

// Animation easing curves
export const AnimationEasing = {
  easeInOut: "ease-in-out" as const,
  easeIn: "ease-in" as const,
  easeOut: "ease-out" as const,
  linear: "linear" as const,
};

// Common animation configurations
export const AnimationConfig = {
  fadeIn: {
    duration: AnimationTiming.normal,
    easing: AnimationEasing.easeOut,
  },
  slideIn: {
    duration: AnimationTiming.normal,
    easing: AnimationEasing.easeInOut,
  },
  scale: {
    duration: AnimationTiming.fast,
    easing: AnimationEasing.easeInOut,
  },
  bounce: {
    duration: AnimationTiming.slow,
    easing: AnimationEasing.easeOut,
  },
};
