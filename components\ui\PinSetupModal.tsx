/**
 * PIN Setup Modal Component
 *
 * Administrative interface for setting up PINs for staff members.
 * Only accessible to users with appropriate permissions.
 */

import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import {
  useUserSwitching,
  type AvailableStaff,
} from "@/src/contexts/UserSwitchingContext";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { ModernButton } from "./ModernButton";
import { PinInput } from "./PinInput";

interface PinSetupModalProps {
  visible: boolean;
  onClose: () => void;
  staffMember?: AvailableStaff | null;
}

export const PinSetupModal: React.FC<PinSetupModalProps> = ({
  visible,
  onClose,
  staffMember,
}) => {
  const theme = useTheme();
  const { hasPermission, user } = useSession();
  const {
    availableStaff,
    isLoading,
    error,
    initializePin,
    clearError,
    loadAvailableStaff,
  } = useUserSwitching();

  const [selectedStaff, setSelectedStaff] = useState<AvailableStaff | null>(
    null
  );
  const [pin, setPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [pinError, setPinError] = useState("");
  const [confirmPinError, setConfirmPinError] = useState("");
  const [step, setStep] = useState<"select" | "setup">("select");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if user has permission to set PINs
  const canSetPins =
    hasPermission("manage_staff") || user?.role === "super_admin";

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      if (staffMember) {
        setSelectedStaff(staffMember);
        setStep("setup");
      } else {
        setSelectedStaff(null);
        setStep("select");
      }
      setPin("");
      setConfirmPin("");
      setPinError("");
      setConfirmPinError("");
      clearError();
      loadAvailableStaff();
    }
  }, [visible, staffMember]);

  // Validate PIN match
  useEffect(() => {
    if (confirmPin.length > 0 && pin.length === confirmPin.length) {
      if (pin !== confirmPin) {
        setConfirmPinError("PINs do not match");
      } else {
        setConfirmPinError("");
      }
    } else {
      setConfirmPinError("");
    }
  }, [pin, confirmPin]);

  // Clear PIN errors when PINs change
  useEffect(() => {
    if (pinError) setPinError("");
  }, [pin]);

  const handleStaffSelect = (staff: AvailableStaff) => {
    setSelectedStaff(staff);
    setStep("setup");
  };

  const handleSetupPin = async () => {
    if (!selectedStaff) return;

    // Validate PIN
    if (pin.length < 4) {
      setPinError("PIN must be at least 4 digits");
      return;
    }

    if (pin !== confirmPin) {
      setConfirmPinError("PINs do not match");
      return;
    }

    setIsSubmitting(true);
    try {
      const success = await initializePin(selectedStaff.id, pin, confirmPin);

      if (success) {
        Alert.alert(
          "PIN Set Successfully",
          `PIN has been set for ${selectedStaff.name}. They can now be selected for user switching.`,
          [
            {
              text: "OK",
              onPress: () => {
                onClose();
              },
            },
          ]
        );
      } else {
        setPinError(error || "Failed to set PIN. Please try again.");
      }
    } catch (err) {
      setPinError("Failed to set PIN. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    if (step === "setup" && !staffMember) {
      setStep("select");
      setSelectedStaff(null);
      setPin("");
      setConfirmPin("");
      setPinError("");
      setConfirmPinError("");
    } else {
      onClose();
    }
  };

  const renderStaffSelection = () => {
    return (
      <ScrollView style={styles.staffList} showsVerticalScrollIndicator={false}>
        {availableStaff.map((staff) => (
          <TouchableOpacity
            key={staff.id}
            style={[
              styles.staffItem,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
              },
            ]}
            onPress={() => handleStaffSelect(staff)}
          >
            <View style={styles.staffInfo}>
              <View style={styles.staffHeader}>
                <Text style={[styles.staffName, { color: theme.colors.text }]}>
                  {staff.name}
                </Text>
                <View
                  style={[
                    styles.pinStatusBadge,
                    {
                      backgroundColor: staff.has_pin
                        ? theme.colors.success
                        : theme.colors.warning,
                    },
                  ]}
                >
                  <Ionicons
                    name={staff.has_pin ? "checkmark" : "time"}
                    size={12}
                    color={
                      staff.has_pin
                        ? theme.colors.textInverse
                        : theme.colors.textInverse
                    }
                  />
                  <Text
                    style={[
                      styles.pinStatusText,
                      {
                        color: staff.has_pin
                          ? theme.colors.textInverse
                          : theme.colors.textInverse,
                      },
                    ]}
                  >
                    {staff.has_pin ? "PIN Set" : "No PIN"}
                  </Text>
                </View>
              </View>
              <Text
                style={[
                  styles.staffDetails,
                  { color: theme.colors.textSecondary },
                ]}
              >
                {staff.role} • {staff.username}
              </Text>
            </View>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        ))}

        {availableStaff.length === 0 && !isLoading && (
          <View style={styles.emptyState}>
            <Ionicons
              name="people"
              size={48}
              color={theme.colors.textSecondary}
            />
            <Text
              style={[
                styles.emptyStateText,
                { color: theme.colors.textSecondary },
              ]}
            >
              No staff members found
            </Text>
          </View>
        )}
      </ScrollView>
    );
  };

  const renderPinSetup = () => {
    if (!selectedStaff) return null;

    const canSubmit = pin.length >= 4 && pin === confirmPin && !isSubmitting;

    return (
      <View style={styles.pinSetupContainer}>
        <View style={styles.staffInfoHeader}>
          <Text style={[styles.setupPrompt, { color: theme.colors.text }]}>
            Set PIN for
          </Text>
          <Text
            style={[styles.setupStaffName, { color: theme.colors.primary }]}
          >
            {selectedStaff.name}
          </Text>
          <Text
            style={[
              styles.setupStaffRole,
              { color: theme.colors.textSecondary },
            ]}
          >
            {selectedStaff.role} • {selectedStaff.username}
          </Text>

          {selectedStaff.has_pin && (
            <View
              style={[
                styles.warningBanner,
                { backgroundColor: theme.colors.warningLight },
              ]}
            >
              <Ionicons name="warning" size={16} color={theme.colors.warning} />
              <Text
                style={[styles.warningText, { color: theme.colors.warning }]}
              >
                This will replace the existing PIN
              </Text>
            </View>
          )}
        </View>

        <View style={styles.pinInputs}>
          <View style={styles.pinInputSection}>
            <Text style={[styles.pinLabel, { color: theme.colors.text }]}>
              Enter New PIN
            </Text>
            <PinInput
              length={4}
              value={pin}
              onChangeText={setPin}
              error={!!pinError}
              errorMessage={pinError}
              disabled={isSubmitting}
              autoFocus={true}
              testID="pin-setup-input"
            />
          </View>

          <View style={styles.pinInputSection}>
            <Text style={[styles.pinLabel, { color: theme.colors.text }]}>
              Confirm PIN
            </Text>
            <PinInput
              length={4}
              value={confirmPin}
              onChangeText={setConfirmPin}
              error={!!confirmPinError}
              errorMessage={confirmPinError}
              disabled={isSubmitting}
              autoFocus={false}
              testID="pin-confirm-input"
            />
          </View>
        </View>

        <View style={styles.setupActions}>
          <ModernButton
            title={isSubmitting ? "Setting PIN..." : "Set PIN"}
            onPress={handleSetupPin}
            disabled={!canSubmit}
            loading={isSubmitting}
            style={styles.setupButton}
          />
        </View>

        <View style={styles.pinGuidelines}>
          <Text style={[styles.guidelinesTitle, { color: theme.colors.text }]}>
            PIN Guidelines:
          </Text>
          <Text
            style={[
              styles.guidelinesText,
              { color: theme.colors.textSecondary },
            ]}
          >
            • Use 4-6 digits{"\n"}• Avoid obvious patterns (1234, 0000){"\n"}•
            Keep it secure and memorable{"\n"}• Staff member will use this to
            switch users
          </Text>
        </View>
      </View>
    );
  };

  if (!canSetPins) {
    return (
      <Modal visible={visible} transparent animationType="fade">
        <View style={styles.permissionDeniedContainer}>
          <View
            style={[
              styles.permissionDeniedModal,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <Ionicons name="lock-closed" size={48} color={theme.colors.error} />
            <Text
              style={[
                styles.permissionDeniedTitle,
                { color: theme.colors.text },
              ]}
            >
              Permission Denied
            </Text>
            <Text
              style={[
                styles.permissionDeniedText,
                { color: theme.colors.textSecondary },
              ]}
            >
              You do not have permission to set user PINs.
            </Text>
            <ModernButton
              title="OK"
              onPress={onClose}
              style={styles.permissionDeniedButton}
            />
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View
        style={[styles.container, { backgroundColor: theme.colors.background }]}
      >
        {/* Header */}
        <View
          style={[styles.header, { borderBottomColor: theme.colors.border }]}
        >
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="chevron-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>

          <View style={styles.headerTitle}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              {step === "select" ? "PIN Setup" : "Set PIN"}
            </Text>
            <Text
              style={[styles.subtitle, { color: theme.colors.textSecondary }]}
            >
              {step === "select"
                ? "Select staff member to set PIN"
                : "Create secure PIN for user switching"}
            </Text>
          </View>

          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text
                style={[
                  styles.loadingText,
                  { color: theme.colors.textSecondary },
                ]}
              >
                Loading staff...
              </Text>
            </View>
          ) : step === "select" ? (
            renderStaffSelection()
          ) : (
            renderPinSetup()
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    alignItems: "center",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
  },
  closeButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  staffList: {
    flex: 1,
    paddingTop: 16,
  },
  staffItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  staffInfo: {
    flex: 1,
  },
  staffHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  staffName: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  pinStatusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    gap: 2,
  },
  pinStatusText: {
    fontSize: 10,
    fontWeight: "600",
  },
  staffDetails: {
    fontSize: 14,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    marginTop: 12,
    textAlign: "center",
  },
  pinSetupContainer: {
    flex: 1,
    paddingTop: 20,
  },
  staffInfoHeader: {
    alignItems: "center",
    marginBottom: 32,
  },
  setupPrompt: {
    fontSize: 16,
    marginBottom: 8,
  },
  setupStaffName: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  setupStaffRole: {
    fontSize: 16,
    marginBottom: 16,
  },
  warningBanner: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  warningText: {
    fontSize: 14,
    fontWeight: "500",
  },
  pinInputs: {
    gap: 24,
    marginBottom: 32,
  },
  pinInputSection: {
    alignItems: "center",
  },
  pinLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  setupActions: {
    marginBottom: 32,
  },
  setupButton: {
    marginHorizontal: 20,
  },
  pinGuidelines: {
    paddingHorizontal: 20,
  },
  guidelinesTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  guidelinesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    gap: 12,
  },
  loadingText: {
    fontSize: 14,
  },
  permissionDeniedContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  permissionDeniedModal: {
    padding: 24,
    borderRadius: 16,
    alignItems: "center",
    minWidth: 280,
  },
  permissionDeniedTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  permissionDeniedText: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 20,
  },
  permissionDeniedButton: {
    minWidth: 100,
  },
});

export default PinSetupModal;
