/**
 * Add 'collect_payments' permission to existing staff members
 * 
 * This script adds the 'collect_payments' permission to all staff members
 * who already have 'process_orders' permission, since payment collection
 * is a natural extension of order processing.
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function addCollectPaymentsPermission() {
  let connection;
  
  try {
    console.log('🔧 Connecting to database...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'dukalink_pos',
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database');

    // Get all staff members who have 'process_orders' permission
    console.log('🔍 Finding staff members with process_orders permission...');
    const [staffWithProcessOrders] = await connection.execute(`
      SELECT DISTINCT staff_id 
      FROM staff_permissions 
      WHERE permission = 'process_orders'
    `);

    console.log(`Found ${staffWithProcessOrders.length} staff members with process_orders permission`);

    if (staffWithProcessOrders.length === 0) {
      console.log('ℹ️ No staff members found with process_orders permission');
      return;
    }

    // Check if any staff already have collect_payments permission
    const [existingCollectPayments] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM staff_permissions 
      WHERE permission = 'collect_payments'
    `);

    console.log(`${existingCollectPayments[0].count} staff members already have collect_payments permission`);

    // Add collect_payments permission to staff who have process_orders but not collect_payments
    let addedCount = 0;
    
    for (const staff of staffWithProcessOrders) {
      try {
        // Check if this staff member already has collect_payments permission
        const [existing] = await connection.execute(`
          SELECT COUNT(*) as count 
          FROM staff_permissions 
          WHERE staff_id = ? AND permission = 'collect_payments'
        `, [staff.staff_id]);

        if (existing[0].count === 0) {
          // Add the permission
          await connection.execute(`
            INSERT INTO staff_permissions (staff_id, permission) 
            VALUES (?, 'collect_payments')
          `, [staff.staff_id]);
          
          addedCount++;
          console.log(`✅ Added collect_payments permission to staff: ${staff.staff_id}`);
        } else {
          console.log(`ℹ️ Staff ${staff.staff_id} already has collect_payments permission`);
        }
      } catch (error) {
        console.error(`❌ Error adding permission for staff ${staff.staff_id}:`, error.message);
      }
    }

    console.log(`🎉 Successfully added collect_payments permission to ${addedCount} staff members`);

    // Verify the results
    const [finalCount] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM staff_permissions 
      WHERE permission = 'collect_payments'
    `);

    console.log(`✅ Total staff members with collect_payments permission: ${finalCount[0].count}`);

  } catch (error) {
    console.error('❌ Error adding collect_payments permission:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the script
if (require.main === module) {
  addCollectPaymentsPermission()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { addCollectPaymentsPermission };
