import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StyleSheet,
  TextInput,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ModernCard } from "@/components/ui/ModernCard";
import { ModernButton } from "@/components/ui/ModernButton";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { StaffEditButton, StaffPermissionsEdit } from "@/src/components/rbac";
import { useRBAC } from "@/src/hooks/useRBAC";

interface StaffMember {
  id: string;
  username: string;
  name: string;
  email?: string;
  role: string;
  commissionRate: number;
  isActive: boolean;
  lastLogin?: string;
  permissions: string[];
}

interface PerformanceData {
  totalSales: number;
  totalCommission: number;
  orderCount: number;
  averageOrderValue: number;
  commissionRate: number;
}

export default function StaffDetailsScreen() {
  const router = useRouter();
  const { staffId } = useLocalSearchParams<{ staffId: string }>();
  const { setCurrentTitle } = useNavigation();
  const { canManageStaff, canViewStaffPerformance } = useRBAC();

  const [staffMember, setStaffMember] = useState<StaffMember | null>(null);
  const [performance, setPerformance] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingCommission, setEditingCommission] = useState(false);
  const [newCommissionRate, setNewCommissionRate] = useState("");

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = "#4CAF50";
  const warningColor = "#FF9500";
  const errorColor = "#FF3B30";

  useEffect(() => {
    if (staffId) {
      loadStaffDetails();
    }
  }, [staffId]);

  const loadStaffDetails = async () => {
    if (!staffId) return;

    try {
      setLoading(true);
      const apiClient = getAPIClient();

      // Load staff member details
      const staffResponse = await apiClient.getStaffById(staffId);
      if (staffResponse.success && staffResponse.data) {
        const staff = staffResponse.data.staffMember;
        setStaffMember(staff);
        setCurrentTitle(staff.name);
        setNewCommissionRate(staff.commissionRate.toString());

        // Load performance data if user has permission
        if (canViewStaffPerformance) {
          const performanceResponse = await apiClient.getStaffPerformance(
            staffId
          );
          if (performanceResponse.success && performanceResponse.data) {
            setPerformance(performanceResponse.data.performance);
          }
        }
      } else {
        Alert.alert(
          "Error",
          staffResponse.error || "Failed to load staff details"
        );
        router.back();
      }
    } catch (error) {
      console.error("Load staff details error:", error);
      Alert.alert("Error", "Failed to load staff details");
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCommission = async () => {
    if (!staffMember || !canManageStaff) return;

    const rate = parseFloat(newCommissionRate);
    if (isNaN(rate) || rate < 0 || rate > 100) {
      Alert.alert("Error", "Commission rate must be between 0 and 100");
      return;
    }

    try {
      const apiClient = getAPIClient();
      const response = await apiClient.updateStaffCommissionRate(
        staffMember.id,
        rate
      );

      if (response.success) {
        setStaffMember({ ...staffMember, commissionRate: rate });
        setEditingCommission(false);
        Alert.alert("Success", "Commission rate updated successfully");
      } else {
        Alert.alert(
          "Error",
          response.error || "Failed to update commission rate"
        );
      }
    } catch (error) {
      console.error("Update commission error:", error);
      Alert.alert("Error", "Failed to update commission rate");
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "super_admin":
        return errorColor;
      case "manager":
        return warningColor;
      case "cashier":
        return successColor;
      default:
        return textSecondary;
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role.toLowerCase()) {
      case "super_admin":
        return "Super Admin";
      case "manager":
        return "Manager";
      case "cashier":
        return "Cashier";
      default:
        return role;
    }
  };

  const formatCurrency = (amount: number) => {
    return `KSh ${amount.toLocaleString("en-KE", {
      minimumFractionDigits: 2,
    })}`;
  };

  const formatLastLogin = (lastLogin?: string) => {
    if (!lastLogin) return "Never";
    return new Date(lastLogin).toLocaleString();
  };

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary,
    primaryColor
  );

  if (loading) {
    return (
      <ScreenWrapper title="Loading..." showBackButton>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: textSecondary }]}>
            Loading staff details...
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  if (!staffMember) {
    return (
      <ScreenWrapper title="Staff Not Found" showBackButton>
        <View style={styles.errorContainer}>
          <Ionicons name="person-remove" size={64} color={textSecondary} />
          <Text style={[styles.errorText, { color: textSecondary }]}>
            Staff member not found
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper title={staffMember.name} showBackButton>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Staff Profile Card */}
        <ModernCard
          style={[styles.profileCard, { backgroundColor: surfaceColor }]}
        >
          <View style={styles.profileHeader}>
            <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
              <Text style={styles.avatarText}>
                {staffMember.name.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={[styles.profileName, { color: textColor }]}>
                {staffMember.name}
              </Text>
              <Text style={[styles.profileUsername, { color: textSecondary }]}>
                @{staffMember.username}
              </Text>
              {staffMember.email && (
                <Text style={[styles.profileEmail, { color: textSecondary }]}>
                  {staffMember.email}
                </Text>
              )}
            </View>
            <View style={styles.profileStatus}>
              <View
                style={[
                  styles.roleTag,
                  { backgroundColor: getRoleColor(staffMember.role) + "20" },
                ]}
              >
                <Text
                  style={[
                    styles.roleText,
                    { color: getRoleColor(staffMember.role) },
                  ]}
                >
                  {getRoleDisplayName(staffMember.role)}
                </Text>
              </View>
              <View
                style={[
                  styles.statusIndicator,
                  {
                    backgroundColor: staffMember.isActive
                      ? successColor
                      : errorColor,
                  },
                ]}
              />
            </View>
          </View>

          <View style={styles.profileDetails}>
            <View style={styles.detailRow}>
              <Ionicons name="time" size={20} color={textSecondary} />
              <Text style={[styles.detailLabel, { color: textSecondary }]}>
                Last Login:
              </Text>
              <Text style={[styles.detailValue, { color: textColor }]}>
                {formatLastLogin(staffMember.lastLogin)}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Ionicons
                name="shield-checkmark"
                size={20}
                color={textSecondary}
              />
              <Text style={[styles.detailLabel, { color: textSecondary }]}>
                Permissions:
              </Text>
              <Text style={[styles.detailValue, { color: textColor }]}>
                {staffMember.permissions.length} permissions
              </Text>
            </View>
          </View>
        </ModernCard>

        {/* Commission Rate Card */}
        <ModernCard
          style={[styles.commissionCard, { backgroundColor: surfaceColor }]}
        >
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: textColor }]}>
              Commission Rate
            </Text>
            <StaffEditButton>
              <TouchableOpacity
                onPress={() => setEditingCommission(!editingCommission)}
                style={styles.editButton}
              >
                <Ionicons
                  name={editingCommission ? "close" : "pencil"}
                  size={20}
                  color={primaryColor}
                />
              </TouchableOpacity>
            </StaffEditButton>
          </View>

          {editingCommission ? (
            <View style={styles.editCommissionContainer}>
              <TextInput
                style={[
                  styles.commissionInput,
                  {
                    borderColor: primaryColor,
                    color: textColor,
                  },
                ]}
                value={newCommissionRate}
                onChangeText={setNewCommissionRate}
                placeholder="Enter commission rate"
                placeholderTextColor={textSecondary}
                keyboardType="numeric"
                maxLength={5}
              />
              <Text style={[styles.percentSymbol, { color: textSecondary }]}>
                %
              </Text>
              <ModernButton
                title="Update"
                onPress={handleUpdateCommission}
                style={styles.updateButton}
              />
            </View>
          ) : (
            <View style={styles.commissionDisplay}>
              <Text style={[styles.commissionValue, { color: primaryColor }]}>
                {staffMember.commissionRate}%
              </Text>
              <Text style={[styles.commissionLabel, { color: textSecondary }]}>
                Commission on sales
              </Text>
            </View>
          )}
        </ModernCard>

        {/* Performance Card */}
        {performance && canViewStaffPerformance && (
          <ModernCard
            style={[styles.performanceCard, { backgroundColor: surfaceColor }]}
          >
            <Text style={[styles.cardTitle, { color: textColor }]}>
              Performance
            </Text>

            <View style={styles.performanceGrid}>
              <View style={styles.performanceItem}>
                <Text
                  style={[styles.performanceValue, { color: primaryColor }]}
                >
                  {formatCurrency(performance.totalSales)}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Total Sales
                </Text>
              </View>

              <View style={styles.performanceItem}>
                <Text
                  style={[styles.performanceValue, { color: successColor }]}
                >
                  {formatCurrency(performance.totalCommission)}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Total Commission
                </Text>
              </View>

              <View style={styles.performanceItem}>
                <Text style={[styles.performanceValue, { color: textColor }]}>
                  {performance.orderCount}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Orders
                </Text>
              </View>

              <View style={styles.performanceItem}>
                <Text style={[styles.performanceValue, { color: textColor }]}>
                  {formatCurrency(performance.averageOrderValue)}
                </Text>
                <Text
                  style={[styles.performanceLabel, { color: textSecondary }]}
                >
                  Avg Order Value
                </Text>
              </View>
            </View>
          </ModernCard>
        )}

        {/* Permissions Card */}
        <StaffPermissionsEdit>
          <ModernCard
            style={[styles.permissionsCard, { backgroundColor: surfaceColor }]}
          >
            <Text style={[styles.cardTitle, { color: textColor }]}>
              Permissions
            </Text>

            <View style={styles.permissionsList}>
              {staffMember.permissions.map((permission, index) => (
                <View
                  key={index}
                  style={[
                    styles.permissionTag,
                    { backgroundColor: primaryColor + "20" },
                  ]}
                >
                  <Text
                    style={[styles.permissionText, { color: primaryColor }]}
                  >
                    {permission
                      .replace(/_/g, " ")
                      .replace(/\b\w/g, (l) => l.toUpperCase())}
                  </Text>
                </View>
              ))}
            </View>
          </ModernCard>
        </StaffPermissionsEdit>
      </ScrollView>
    </ScreenWrapper>
  );
}

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string,
  primaryColor: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    loadingText: {
      fontSize: 16,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 40,
    },
    errorText: {
      fontSize: 16,
      textAlign: "center",
      marginTop: 16,
    },
    profileCard: {
      margin: 20,
      padding: 20,
    },
    profileHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 20,
    },
    avatar: {
      width: 64,
      height: 64,
      borderRadius: 32,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 16,
    },
    avatarText: {
      color: "white",
      fontSize: 24,
      fontWeight: "bold",
    },
    profileInfo: {
      flex: 1,
    },
    profileName: {
      fontSize: 20,
      fontWeight: "bold",
      marginBottom: 4,
    },
    profileUsername: {
      fontSize: 16,
      marginBottom: 4,
    },
    profileEmail: {
      fontSize: 14,
    },
    profileStatus: {
      alignItems: "flex-end",
    },
    roleTag: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginBottom: 8,
    },
    roleText: {
      fontSize: 14,
      fontWeight: "600",
    },
    statusIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    profileDetails: {
      borderTopWidth: 1,
      borderTopColor: textSecondary + "20",
      paddingTop: 16,
    },
    detailRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
    },
    detailLabel: {
      fontSize: 14,
      marginLeft: 12,
      marginRight: 8,
      minWidth: 100,
    },
    detailValue: {
      fontSize: 14,
      fontWeight: "500",
      flex: 1,
    },
    commissionCard: {
      marginHorizontal: 20,
      marginBottom: 20,
      padding: 20,
    },
    cardHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 16,
    },
    cardTitle: {
      fontSize: 18,
      fontWeight: "bold",
    },
    editButton: {
      padding: 8,
    },
    editCommissionContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    commissionInput: {
      flex: 1,
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      fontSize: 16,
      marginRight: 8,
    },
    percentSymbol: {
      fontSize: 16,
      marginRight: 12,
    },
    updateButton: {
      paddingHorizontal: 16,
    },
    commissionDisplay: {
      alignItems: "center",
    },
    commissionValue: {
      fontSize: 32,
      fontWeight: "bold",
      marginBottom: 4,
    },
    commissionLabel: {
      fontSize: 14,
    },
    performanceCard: {
      marginHorizontal: 20,
      marginBottom: 20,
      padding: 20,
    },
    performanceGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginTop: 16,
    },
    performanceItem: {
      width: "50%",
      alignItems: "center",
      marginBottom: 20,
    },
    performanceValue: {
      fontSize: 18,
      fontWeight: "bold",
      marginBottom: 4,
    },
    performanceLabel: {
      fontSize: 12,
      textAlign: "center",
    },
    permissionsCard: {
      marginHorizontal: 20,
      marginBottom: 20,
      padding: 20,
    },
    permissionsList: {
      flexDirection: "row",
      flexWrap: "wrap",
      marginTop: 16,
    },
    permissionTag: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginRight: 8,
      marginBottom: 8,
    },
    permissionText: {
      fontSize: 12,
      fontWeight: "500",
    },
  });
