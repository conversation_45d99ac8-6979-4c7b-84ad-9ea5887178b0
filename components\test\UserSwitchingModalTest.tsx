/**
 * User Switching Modal Test Component
 *
 * Test component to verify the UserSwitchingModal functionality
 * and ensure the two-step flow is working correctly.
 */

import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { UserSwitchingModal } from "@/components/ui/UserSwitchingModal";
import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useUserSwitching } from "@/src/contexts/UserSwitchingContext";
import React, { useState } from "react";
import { Alert, StyleSheet, Text, View } from "react-native";

export const UserSwitchingModalTest: React.FC = () => {
  const theme = useTheme();
  const { user, hasPermission } = useSession();
  const { sessionContext, availableStaff, isLoading } = useUserSwitching();
  const [showModal, setShowModal] = useState(false);

  // Check if user can switch users
  const canSwitchUsers =
    hasPermission("switch_users") ||
    user?.role === "super_admin" ||
    user?.role === "manager";

  const handleTestModal = () => {
    if (!canSwitchUsers) {
      Alert.alert(
        "Permission Denied",
        "You do not have permission to switch users.",
        [{ text: "OK" }]
      );
      return;
    }

    console.log("🧪 Testing User Switching Modal");
    console.log("📊 Available Staff:", availableStaff.length);
    console.log(
      "🔐 Session Context:",
      sessionContext ? "Available" : "Not Available"
    );
    console.log("👤 Current User:", user?.name);
    console.log("🔑 Permissions:", canSwitchUsers ? "Granted" : "Denied");

    setShowModal(true);
  };

  const handleModalClose = () => {
    console.log("🚪 Modal closed");
    setShowModal(false);
  };

  const handleSwitchComplete = (success: boolean) => {
    console.log("✅ Switch completed:", success ? "Success" : "Failed");
    if (success) {
      setShowModal(false);
      Alert.alert(
        "Test Complete",
        "User switching modal test completed successfully!",
        [{ text: "OK" }]
      );
    }
  };

  return (
    <ModernCard style={styles.container}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        User Switching Modal Test
      </Text>

      <View style={styles.infoSection}>
        <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
          Current User:
        </Text>
        <Text style={[styles.infoValue, { color: theme.colors.text }]}>
          {user?.name || "Not logged in"}
        </Text>

        <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
          Role:
        </Text>
        <Text style={[styles.infoValue, { color: theme.colors.text }]}>
          {user?.role || "Unknown"}
        </Text>

        <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
          Can Switch Users:
        </Text>
        <Text
          style={[
            styles.infoValue,
            {
              color: canSwitchUsers ? theme.colors.success : theme.colors.error,
            },
          ]}
        >
          {canSwitchUsers ? "Yes" : "No"}
        </Text>

        <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
          Available Staff:
        </Text>
        <Text style={[styles.infoValue, { color: theme.colors.text }]}>
          {isLoading ? "Loading..." : `${availableStaff.length} staff members`}
        </Text>

        <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
          Staff with PINs:
        </Text>
        <Text style={[styles.infoValue, { color: theme.colors.text }]}>
          {availableStaff.filter((s) => s.has_pin).length} with PINs set
        </Text>

        {sessionContext && (
          <>
            <Text
              style={[styles.infoLabel, { color: theme.colors.textSecondary }]}
            >
              Active Switch:
            </Text>
            <Text
              style={[
                styles.infoValue,
                {
                  color: sessionContext.hasActiveSwitch
                    ? theme.colors.warning
                    : theme.colors.success,
                },
              ]}
            >
              {sessionContext.hasActiveSwitch ? "Yes" : "No"}
            </Text>
          </>
        )}
      </View>

      <ModernButton
        title="Test User Switching Modal"
        onPress={handleTestModal}
        variant="primary"
        style={styles.testButton}
        disabled={!canSwitchUsers}
      />

      <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
        This will open the user switching modal with the two-step flow:
        {"\n"}1. Select a staff member from the list
        {"\n"}2. Enter their PIN to complete the switch
      </Text>

      <UserSwitchingModal
        visible={showModal}
        onClose={handleModalClose}
        onSwitchComplete={handleSwitchComplete}
      />
    </ModernCard>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  infoSection: {
    marginBottom: 20,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginTop: 8,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    marginBottom: 4,
  },
  testButton: {
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: "center",
  },
});
