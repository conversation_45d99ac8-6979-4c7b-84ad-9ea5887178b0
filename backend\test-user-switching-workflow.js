#!/usr/bin/env node

/**
 * Test User Switching Workflow
 *
 * Tests the complete user switching flow to identify issues:
 * 1. <PERSON><PERSON> as initial user
 * 2. Get session context
 * 3. Switch to another user
 * 4. Verify switch worked
 * 5. Switch back
 */

const axios = require("axios");
const crypto = require("crypto");

const BASE_URL = "http://localhost:3020";

async function testUserSwitchingWorkflow() {
  console.log("🧪 Testing User Switching Workflow\n");

  try {
    // Step 1: Login as cashier1
    console.log("1️⃣ Logging in as cashier1...");
    const loginResponse = await axios.post(`${BASE_URL}/api/pos/login`, {
      username: "cashier1",
      password: "password123",
    });

    if (!loginResponse.data.success) {
      console.log("❌ Login failed:", loginResponse.data.error);
      return;
    }

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log("✅ Login successful");
    console.log(`   Token: ${token.substring(0, 50)}...`);

    // Step 2: Get initial session context
    console.log("\n2️⃣ Getting initial session context...");
    try {
      const contextResponse = await axios.get(
        `${BASE_URL}/api/pos/user-switching/session-context`,
        { headers }
      );

      if (contextResponse.data.success) {
        console.log("✅ Session context retrieved");
        console.log(
          `   Current user: ${contextResponse.data.data.sessionContext.currentUser.name}`
        );
        console.log(
          `   Session ID: ${contextResponse.data.data.sessionContext.sessionId}`
        );
      } else {
        console.log("❌ Session context failed:", contextResponse.data.error);
        return;
      }
    } catch (error) {
      console.log(
        "❌ Session context error:",
        error.response?.data?.error || error.message
      );
      return;
    }

    // Step 3: Get available staff
    console.log("\n3️⃣ Getting available staff...");
    try {
      const staffResponse = await axios.get(
        `${BASE_URL}/api/pos/user-switching/available-staff`,
        { headers }
      );

      if (staffResponse.data.success) {
        console.log("✅ Available staff retrieved");
        const staff = staffResponse.data.data.staff;
        console.log(`   Found ${staff.length} staff members:`);
        staff.forEach((s) => {
          console.log(
            `     - ${s.name} (${s.username}) - PIN: ${
              s.has_pin ? "Set" : "Not set"
            }`
          );
        });

        // Find manager1 to switch to
        const manager = staff.find((s) => s.username === "manager1");
        if (!manager) {
          console.log("❌ Manager1 not found in available staff");
          return;
        }

        if (!manager.has_pin) {
          console.log("❌ Manager1 does not have PIN set");
          return;
        }

        // Step 4: Test PIN validation first
        console.log("\n4️⃣ Testing PIN validation for manager1...");
        try {
          const pinResponse = await axios.post(
            `${BASE_URL}/api/pos/user-switching/validate-pin`,
            {
              staffId: manager.id,
              pin: "5678",
            },
            { headers }
          );

          if (pinResponse.data.success) {
            console.log("✅ PIN validation successful");
          } else {
            console.log("❌ PIN validation failed:", pinResponse.data.error);
            return;
          }
        } catch (error) {
          console.log(
            "❌ PIN validation error:",
            error.response?.data?.error || error.message
          );
          return;
        }

        // Step 5: Switch user
        console.log("\n5️⃣ Switching to manager1...");
        try {
          const switchResponse = await axios.post(
            `${BASE_URL}/api/pos/user-switching/switch-user`,
            {
              targetStaffId: manager.id,
              pin: "5678",
              reason: "testing_workflow",
            },
            { headers }
          );

          if (switchResponse.data.success) {
            console.log("✅ User switch successful");
            console.log(
              `   New current user: ${switchResponse.data.data.currentUser.name}`
            );
            console.log(`   Switch ID: ${switchResponse.data.data.switchId}`);

            // Step 6: Verify session context after switch
            console.log("\n6️⃣ Verifying session context after switch...");
            const newContextResponse = await axios.get(
              `${BASE_URL}/api/pos/user-switching/session-context`,
              { headers }
            );

            if (newContextResponse.data.success) {
              console.log("✅ Session context verified after switch");
              console.log(
                `   Current user: ${newContextResponse.data.data.sessionContext.currentUser.name}`
              );
              console.log(
                `   Has active switch: ${newContextResponse.data.data.hasActiveSwitch}`
              );
              console.log(
                `   Can switch back: ${newContextResponse.data.data.canSwitchBack}`
              );
            } else {
              console.log(
                "❌ Session context verification failed:",
                newContextResponse.data.error
              );
            }
          } else {
            console.log("❌ User switch failed:", switchResponse.data.error);
          }
        } catch (error) {
          console.log(
            "❌ User switch error:",
            error.response?.data?.error || error.message
          );
          console.log("   Status:", error.response?.status);
          console.log("   Full response:", error.response?.data);
        }
      } else {
        console.log("❌ Available staff failed:", staffResponse.data.error);
      }
    } catch (error) {
      console.log(
        "❌ Available staff error:",
        error.response?.data?.error || error.message
      );
    }
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Run the test
testUserSwitchingWorkflow();
