import { useTheme } from "@/src/contexts/ThemeContext";
import React from "react";
import { TouchableOpacity, View, ViewStyle } from "react-native";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

interface ModernCardProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  variant?: "default" | "elevated" | "outlined";
  disabled?: boolean;
}

export function ModernCard({
  children,
  onPress,
  style,
  variant = "default",
  disabled = false,
}: ModernCardProps) {
  const theme = useTheme();
  const { spacingMultiplier } = useResponsiveLayout();

  const getVariantStyle = (): ViewStyle => {
    const responsivePadding = theme.spacing.lg * spacingMultiplier;

    switch (variant) {
      case "elevated":
        return {
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.lg,
          padding: responsivePadding,
          ...theme.shadows.md,
        };
      case "outlined":
        return {
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.lg,
          padding: responsivePadding,
          borderWidth: 1,
          borderColor: theme.colors.border,
        };
      default:
        return {
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.lg,
          padding: responsivePadding,
        };
    }
  };

  const cardStyles = [getVariantStyle(), disabled && { opacity: 0.6 }, style];

  if (onPress && !disabled) {
    return (
      <TouchableOpacity
        style={cardStyles}
        onPress={onPress}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return <View style={cardStyles}>{children}</View>;
}

// Styles are now handled by the theme system
