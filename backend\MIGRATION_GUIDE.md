# MySQL Migration Guide

This guide provides step-by-step instructions to migrate the Dukalink POS system from in-memory storage to MySQL database on any server.

## Prerequisites

1. **MySQL Server** running and accessible
2. **Node.js** and npm installed
3. **Environment variables** configured in `.env` file

## Step 1: Configure Environment Variables

Create or update your `.env` file with the following database configuration:

```bash
# Database Configuration
DB_HOST=your_mysql_host
DB_USER=your_mysql_user  
DB_PASSWORD=your_mysql_password
DB_NAME=dukalink_shopify_pos
DB_PORT=3306

# For root user setup (if using root)
MYSQL_ROOT_PASSWORD=your_root_password

# Enable MySQL mode
MIGRATION_MODE=true
ENABLE_MOCK_DATA=false
```

## Step 2: Run the Migration Script

Execute the automated migration script:

```bash
npm run migrate:mysql
```

This script will:
- ✅ Create databases and user accounts
- ✅ Create all required tables with proper schema
- ✅ Seed initial staff and sales agent data
- ✅ Update authentication middleware imports
- ✅ Verify the migration was successful

## Step 3: Default Users Created

After migration, these users will be available:

| Username | Password | Role | Permissions |
|----------|----------|------|-------------|
| `admin1` | `admin123` | super_admin | All permissions (15) |
| `manager1` | `manager123` | manager | Management permissions (10) |
| `cashier1` | `password123` | cashier | Basic POS permissions (4) |

## Step 4: Test the Migration

1. **Start the server:**
   ```bash
   npm run dev
   ```

2. **Test authentication:**
   ```bash
   curl -X POST http://localhost:3020/api/pos/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin1","password":"admin123"}'
   ```

3. **Test staff endpoint:**
   ```bash
   curl -X GET http://localhost:3020/api/staff \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
   ```

4. **Test sales agents endpoint:**
   ```bash
   curl -X GET http://localhost:3020/api/sales-agents \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
   ```

## Step 5: Verify Database Tables

Check that all tables were created:

```sql
SHOW TABLES;
```

Expected tables:
- `pos_staff` - Staff members with authentication
- `staff_permissions` - Role-based permissions
- `pos_sessions` - Active user sessions
- `sales_agents` - Sales agent information
- `pos_terminals` - POS terminal management

## Troubleshooting

### Issue: "User not found or inactive"
**Solution:** Ensure `MIGRATION_MODE=true` in `.env` and restart the server.

### Issue: Database connection failed
**Solution:** Verify database credentials and ensure MySQL server is running.

### Issue: Permission denied
**Solution:** Check that the database user has proper privileges on the database.

### Issue: Tables not created
**Solution:** Run the migration script again or check MySQL error logs.

## Manual Database Setup (Alternative)

If the automated script fails, you can set up manually:

1. **Create database:**
   ```sql
   CREATE DATABASE dukalink_shopify_pos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Create user (if not using root):**
   ```sql
   CREATE USER 'dukalink'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON dukalink_shopify_pos.* TO 'dukalink'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Run table creation:**
   ```bash
   npm run setup:fresh
   ```

## Production Deployment

For production deployment:

1. Use strong passwords for database users
2. Enable SSL connections to MySQL
3. Configure proper firewall rules
4. Set up database backups
5. Monitor database performance

## Rollback Plan

To rollback to in-memory storage:

1. Set `MIGRATION_MODE=false` in `.env`
2. Restart the server
3. The system will use in-memory data again

## Support

If you encounter issues:

1. Check the server logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure MySQL server is accessible from the application server
4. Test database connectivity manually

---

**Note:** This migration is designed to be zero-downtime and maintains 100% API compatibility.
