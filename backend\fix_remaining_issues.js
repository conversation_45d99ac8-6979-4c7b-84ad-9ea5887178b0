/**
 * Fix Remaining Database Issues
 */

require("dotenv").config();
const mysql = require("mysql2/promise");

async function fixRemainingIssues() {
  let connection;
  
  try {
    console.log("🔗 Connecting to MySQL database...");
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "dukalink",
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || "dukalink_shopify_pos",
      charset: "utf8mb4",
    });

    console.log("✅ Connected to MySQL database");

    // Add missing last_auto_save column to pos_tickets
    console.log("🔧 Adding last_auto_save column to pos_tickets...");
    try {
      await connection.execute("ALTER TABLE pos_tickets ADD COLUMN last_auto_save DATETIME NULL");
      console.log("✅ last_auto_save column added");
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log("⚠️ last_auto_save column already exists");
      } else {
        console.error("❌ Failed to add last_auto_save column:", error.message);
      }
    }

    // Check pos_tickets structure
    console.log("🔍 Checking pos_tickets structure...");
    const [ticketCols] = await connection.execute("DESCRIBE pos_tickets");
    const ticketColumnNames = ticketCols.map(col => col.Field);
    console.log("pos_tickets columns:", ticketColumnNames.join(", "));

    // Test the problematic query with proper parameters
    console.log("🔧 Testing tickets query...");
    try {
      const staffId = "pos-003";
      const status = "active";
      const limit = 50;
      const offset = 0;

      const query = `
        SELECT 
          t.*,
          s.name as staff_name,
          sa.name as sales_agent_name,
          COUNT(ti.id) as item_count
        FROM pos_tickets t
        LEFT JOIN pos_staff s ON t.staff_id = s.id
        LEFT JOIN sales_agents sa ON t.sales_agent_id = sa.id
        LEFT JOIN ticket_items ti ON t.id = ti.ticket_id
        WHERE t.staff_id = ? AND t.status = ? AND (t.expires_at IS NULL OR t.expires_at > NOW())
        GROUP BY t.id
        ORDER BY t.updated_at DESC
        LIMIT ? OFFSET ?
      `;

      const [tickets] = await connection.execute(query, [staffId, status, limit, offset]);
      console.log(`✅ Tickets query test successful - found ${tickets.length} tickets`);
    } catch (error) {
      console.error("❌ Tickets query test failed:", error.message);
    }

    // Check user switching tables
    console.log("🔍 Checking user switching tables...");
    const [userSwitchTables] = await connection.execute("SHOW TABLES LIKE 'pos_user_switches'");
    console.log(`pos_user_switches table exists: ${userSwitchTables.length > 0}`);

    if (userSwitchTables.length > 0) {
      const [switchCols] = await connection.execute("DESCRIBE pos_user_switches");
      console.log("pos_user_switches columns:", switchCols.map(col => col.Field).join(", "));
    }

    console.log("\n✅ Remaining issues fix completed!");

  } catch (error) {
    console.error("❌ Fix failed:", error);
  } finally {
    if (connection) {
      await connection.end();
      console.log("🔌 Database connection closed");
    }
  }
}

fixRemainingIssues();
