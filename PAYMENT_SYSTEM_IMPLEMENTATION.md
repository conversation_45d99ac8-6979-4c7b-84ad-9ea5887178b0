# 💳 Payment System Implementation Plan

## 🎯 **CRITICAL PRIORITY: Payment Processing (BLOCKING ALL SALES)**

### **Current Status**: ❌ 0% Complete - COMPLETELY MISSING
### **Impact**: BLOCKS ALL REAL TRANSACTIONS
### **Timeline**: 1 Week (Critical Path)

---

## 🏗️ **IMPLEMENTATION ARCHITECTURE**

### **Phase 1: Cash Payment System (Days 1-3)**

#### **Backend Payment Service**
```javascript
// backend/src/services/payment-service.js
class PaymentService {
  async processCashPayment(orderData, paymentData) {
    try {
      const { amountDue, amountReceived } = paymentData;
      
      // Validate payment
      if (amountReceived < amountDue) {
        throw new Error('Insufficient payment amount');
      }
      
      const change = amountReceived - amountDue;
      
      // Create payment record
      const payment = {
        id: generatePaymentId(),
        orderId: orderData.id,
        method: 'cash',
        amountDue: amountDue,
        amountReceived: amountReceived,
        change: change,
        status: 'completed',
        processedAt: new Date(),
        processedBy: paymentData.staffId
      };
      
      // Record transaction
      await this.recordPayment(payment);
      
      return {
        success: true,
        payment: payment,
        change: change
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  async recordPayment(paymentData) {
    // Store payment in database
    // Update order status to paid
    // Trigger receipt generation
    // Log transaction for audit
  }
}
```

#### **Payment API Endpoints**
```javascript
// backend/src/routes/payment-api.js
const express = require('express');
const router = express.Router();
const PaymentService = require('../services/payment-service');

// Process cash payment
router.post('/cash', authenticateToken, async (req, res) => {
  try {
    const { orderId, amountDue, amountReceived } = req.body;
    
    const paymentService = new PaymentService();
    const result = await paymentService.processCashPayment(
      { id: orderId },
      { 
        amountDue, 
        amountReceived, 
        staffId: req.user.id 
      }
    );
    
    if (result.success) {
      res.json({
        success: true,
        data: {
          payment: result.payment,
          change: result.change
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Payment processing failed'
    });
  }
});

module.exports = router;
```

### **Phase 2: Mobile Payment UI (Days 2-4)**

#### **Payment Screen Component**
```typescript
// app/payment.tsx
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Alert } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { getAPIClient } from '@/src/services/api/dukalink-client';

interface PaymentData {
  orderId: string;
  total: number;
  currency: string;
}

export default function PaymentScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash');
  const [amountReceived, setAmountReceived] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  
  const paymentData: PaymentData = {
    orderId: params.orderId as string,
    total: parseFloat(params.total as string),
    currency: params.currency as string || 'KES'
  };
  
  const calculateChange = () => {
    const received = parseFloat(amountReceived) || 0;
    return Math.max(0, received - paymentData.total);
  };
  
  const handleCashPayment = async () => {
    const received = parseFloat(amountReceived);
    
    if (received < paymentData.total) {
      Alert.alert('Error', 'Amount received is less than total due');
      return;
    }
    
    setIsProcessing(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.processCashPayment({
        orderId: paymentData.orderId,
        amountDue: paymentData.total,
        amountReceived: received
      });
      
      if (response.success) {
        // Navigate to receipt screen
        router.push({
          pathname: '/receipt',
          params: {
            orderId: paymentData.orderId,
            paymentId: response.data.payment.id,
            change: response.data.change.toString()
          }
        });
      } else {
        Alert.alert('Payment Failed', response.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Payment processing failed');
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1a1a1a" />
        </TouchableOpacity>
        <Text style={styles.title}>Payment</Text>
        <View style={{ width: 24 }} />
      </View>
      
      <View style={styles.orderSummary}>
        <Text style={styles.totalLabel}>Total Due</Text>
        <Text style={styles.totalAmount}>
          {paymentData.currency} {paymentData.total.toFixed(2)}
        </Text>
      </View>
      
      <View style={styles.paymentMethods}>
        <TouchableOpacity
          style={[
            styles.methodButton,
            paymentMethod === 'cash' && styles.methodButtonActive
          ]}
          onPress={() => setPaymentMethod('cash')}
        >
          <Ionicons name="cash" size={24} color={paymentMethod === 'cash' ? '#fff' : '#2ecc71'} />
          <Text style={[
            styles.methodText,
            paymentMethod === 'cash' && styles.methodTextActive
          ]}>Cash</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.methodButton,
            paymentMethod === 'card' && styles.methodButtonActive,
            styles.methodButtonDisabled
          ]}
          disabled
        >
          <Ionicons name="card" size={24} color="#ccc" />
          <Text style={[styles.methodText, { color: '#ccc' }]}>Card (Coming Soon)</Text>
        </TouchableOpacity>
      </View>
      
      {paymentMethod === 'cash' && (
        <View style={styles.cashPayment}>
          <Text style={styles.inputLabel}>Amount Received</Text>
          <TextInput
            style={styles.amountInput}
            value={amountReceived}
            onChangeText={setAmountReceived}
            placeholder="0.00"
            keyboardType="numeric"
            autoFocus
          />
          
          {amountReceived && (
            <View style={styles.changeDisplay}>
              <Text style={styles.changeLabel}>Change</Text>
              <Text style={styles.changeAmount}>
                {paymentData.currency} {calculateChange().toFixed(2)}
              </Text>
            </View>
          )}
          
          <TouchableOpacity
            style={[
              styles.processButton,
              (!amountReceived || parseFloat(amountReceived) < paymentData.total || isProcessing) && 
              styles.processButtonDisabled
            ]}
            onPress={handleCashPayment}
            disabled={!amountReceived || parseFloat(amountReceived) < paymentData.total || isProcessing}
          >
            <Text style={styles.processButtonText}>
              {isProcessing ? 'Processing...' : 'Complete Payment'}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e8ed',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  orderSummary: {
    backgroundColor: '#fff',
    padding: 24,
    margin: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  totalAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  paymentMethods: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  methodButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#2ecc71',
    backgroundColor: '#fff',
  },
  methodButtonActive: {
    backgroundColor: '#2ecc71',
  },
  methodButtonDisabled: {
    borderColor: '#ccc',
    backgroundColor: '#f5f5f5',
  },
  methodText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#2ecc71',
  },
  methodTextActive: {
    color: '#fff',
  },
  cashPayment: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  amountInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e1e8ed',
    borderRadius: 8,
    padding: 16,
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 16,
  },
  changeDisplay: {
    backgroundColor: '#f0f9f4',
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  changeLabel: {
    fontSize: 16,
    color: '#2ecc71',
    fontWeight: '600',
  },
  changeAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2ecc71',
  },
  processButton: {
    backgroundColor: '#2ecc71',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  processButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  processButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
```

### **Phase 3: API Client Integration (Day 3)**

#### **Payment API Methods**
```typescript
// src/services/api/dukalink-client.ts - Add payment methods
async processCashPayment(paymentData: {
  orderId: string;
  amountDue: number;
  amountReceived: number;
}): Promise<APIResponse<{
  payment: Payment;
  change: number;
}>> {
  return this.request('/payment/cash', {
    method: 'POST',
    data: paymentData,
  });
}

async processCardPayment(paymentData: {
  orderId: string;
  amount: number;
  cardToken: string;
}): Promise<APIResponse<{
  payment: Payment;
  transaction: Transaction;
}>> {
  return this.request('/payment/card', {
    method: 'POST',
    data: paymentData,
  });
}
```

---

## 🔄 **INTEGRATION WITH EXISTING CART FLOW**

### **Updated Cart Checkout Process**
```typescript
// app/(tabs)/cart.tsx - Updated checkout handler
const handleCheckout = async () => {
  if (cartItems.length === 0) {
    Alert.alert("Error", "Cart is empty");
    return;
  }

  if (!selectedCustomer?.firstName || !selectedCustomer?.lastName) {
    Alert.alert("Error", "Please select a customer before completing the sale");
    return;
  }

  setIsProcessing(true);
  try {
    const apiClient = getAPIClient();

    // 1. Create order in Shopify (existing logic)
    const orderResponse = await apiClient.createStoreOrder(orderData);
    
    if (orderResponse.success) {
      // 2. Navigate to payment screen instead of showing success
      router.push({
        pathname: '/payment',
        params: {
          orderId: orderResponse.data.order.id,
          total: total.toString(),
          currency: 'KES'
        }
      });
    } else {
      Alert.alert("Error", orderResponse.error || "Failed to create order");
    }
  } catch (error) {
    Alert.alert("Error", "Failed to process checkout");
  } finally {
    setIsProcessing(false);
  }
};
```

---

## 🧪 **TESTING PLAN**

### **Day 4: Payment Testing**
1. **Cash Payment Tests**
   - Test exact amount payment
   - Test overpayment with change calculation
   - Test underpayment rejection
   - Test payment processing errors

2. **Integration Tests**
   - Test cart to payment flow
   - Test payment to receipt flow
   - Test order status updates
   - Test error handling

3. **User Experience Tests**
   - Test payment UI responsiveness
   - Test input validation
   - Test navigation flow
   - Test error messages

---

## 📊 **SUCCESS METRICS**

### **Completion Criteria**
- [ ] Cash payment processing functional
- [ ] Change calculation accurate
- [ ] Payment validation working
- [ ] Order status updates correctly
- [ ] Error handling comprehensive
- [ ] UI/UX intuitive and responsive

### **Performance Targets**
- Payment processing: < 2 seconds
- Change calculation: Instant
- Error recovery: < 5 seconds
- UI responsiveness: < 100ms

---

## 🚀 **DEPLOYMENT PLAN**

### **Day 5: Production Deployment**
1. **Backend Deployment**
   - Deploy payment service
   - Add payment API routes
   - Configure environment variables
   - Test API endpoints

2. **Mobile App Update**
   - Build with payment screens
   - Test on physical devices
   - Validate payment flow
   - Deploy to app stores

3. **Integration Testing**
   - End-to-end transaction testing
   - Multi-device testing
   - Performance validation
   - Security verification

This implementation plan provides the critical payment processing capability needed to enable real sales transactions in the Dukalink POS system. The cash payment system can be implemented in 3-5 days and will immediately unblock sales operations.
