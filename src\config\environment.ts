/**
 * Environment Configuration
 * Centralized configuration for different environments
 */

import { Platform } from "react-native";

// Environment Types
export type Environment = "development" | "staging" | "production";

// API Configuration for different environments
export const ENVIRONMENT_CONFIG = {
  development: {
    API_BASE_URL:
      Platform.OS === "web"
        ? "http://localhost:3020/api"
        : "http://***********:3020/api",
    API_TIMEOUT: 30000,
    DEBUG_MODE: true,
    LOG_LEVEL: "debug",
  },
  staging: {
    API_BASE_URL: "https://staging.shopify.dukalink.com/api",
    API_TIMEOUT: 30000,
    DEBUG_MODE: true,
    LOG_LEVEL: "info",
  },
  production: {
    API_BASE_URL: "https://shopify.dukalink.com/api",
    API_TIMEOUT: 30000,
    DEBUG_MODE: false,
    LOG_LEVEL: "error",
  },
};

// Current Environment Detection
export const getCurrentEnvironment = (): Environment => {
  // You can customize this logic based on your needs
  if (process.env.NODE_ENV === "development" || __DEV__) {
    return "development";
  }

  // You could also check for staging environment here
  // For example, by checking a build configuration or environment variable

  return "production";
};

// Current Configuration
export const CURRENT_ENV = getCurrentEnvironment();
export const ENV_CONFIG = ENVIRONMENT_CONFIG[CURRENT_ENV];

// Feature Flags
export const FEATURE_FLAGS = {
  // Enable/disable features based on environment
  ENABLE_ANALYTICS: CURRENT_ENV === "production",
  ENABLE_CRASH_REPORTING: CURRENT_ENV !== "development",
  ENABLE_DEBUG_MENU: CURRENT_ENV === "development",
  ENABLE_MOCK_DATA: false, // Set to true for testing without backend
  ENABLE_OFFLINE_MODE: true,
  ENABLE_BIOMETRIC_AUTH: true,
  ENABLE_THERMAL_PRINTING: true,
};

// App Configuration
export const APP_CONFIG = {
  APP_NAME: "Dukalink POS",
  APP_VERSION: "1.0.0",
  COMPANY_NAME: "Dukalink",
  SUPPORT_EMAIL: "<EMAIL>",
  PRIVACY_POLICY_URL: "https://dukalink.com/privacy",
  TERMS_OF_SERVICE_URL: "https://dukalink.com/terms",
};

// API Endpoints (relative to base URL)
export const API_ENDPOINTS = {
  // Authentication
  AUTH_LOGIN: "/pos/login",
  AUTH_VERIFY: "/pos/verify",
  AUTH_LOGOUT: "/pos/logout",
  AUTH_REFRESH: "/auth/refresh",

  // Staff Management
  STAFF_LIST: "/staff",
  STAFF_CREATE: "/staff",
  STAFF_UPDATE: (id: string) => `/staff/${id}`,
  STAFF_DELETE: (id: string) => `/staff/${id}`,

  // Sales Agents
  SALES_AGENTS_LIST: "/sales-agents",
  SALES_AGENTS_CREATE: "/sales-agents",
  SALES_AGENTS_UPDATE: (id: string) => `/sales-agents/${id}`,
  SALES_AGENTS_DELETE: (id: string) => `/sales-agents/${id}`,

  // Customers
  CUSTOMERS_LIST: "/customers",
  CUSTOMERS_CREATE: "/customers",
  CUSTOMERS_SEARCH: "/customers/search",

  // Products
  PRODUCTS_LIST: "/products",
  PRODUCTS_SEARCH: "/products/search",

  // Orders
  ORDERS_LIST: "/orders",
  ORDERS_CREATE: "/orders",

  // Store
  STORE_INFO: "/store/info",

  // Health Check
  HEALTH: "/health",
};

// Storage Keys
export const STORAGE_KEYS = {
  SESSION_TOKEN: "session_token",
  USER_PREFERENCES: "user_preferences",
  CART_DATA: "cart_data",
  OFFLINE_ORDERS: "offline_orders",
  PRINTER_SETTINGS: "printer_settings",
  LAST_SYNC: "last_sync",
};

// Default Values
export const DEFAULTS = {
  PAGINATION_LIMIT: 20,
  SEARCH_DEBOUNCE_MS: 300,
  RETRY_ATTEMPTS: 3,
  CACHE_DURATION_MS: 5 * 60 * 1000, // 5 minutes
};

export default ENV_CONFIG;
