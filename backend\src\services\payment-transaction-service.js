/**
 * Payment Transaction Service
 *
 * Handles all payment transaction operations including:
 * - Transaction initiation and validation
 * - Payment method processing
 * - Split payment management
 * - Transaction completion and status tracking
 * - Integration with existing M-Pesa and payment services
 */

const { v4: uuidv4 } = require("uuid");
const { databaseManager } = require("../config/database-manager");
const MpesaIntegrationService = require("./mpesa-integration-service");
const ShopifyPaymentIntegrationService = require("./shopify-payment-integration-service");

class PaymentTransactionService {
  constructor() {
    if (PaymentTransactionService.instance) {
      return PaymentTransactionService.instance;
    }

    // Use centralized database manager
    this.databaseManager = databaseManager;

    this.mpesaService = new MpesaIntegrationService();
    this.shopifyService = new ShopifyPaymentIntegrationService();

    console.log(
      "✅ Payment Transaction Service initialized with centralized database manager"
    );

    PaymentTransactionService.instance = this;
  }

  static getInstance() {
    if (!PaymentTransactionService.instance) {
      PaymentTransactionService.instance = new PaymentTransactionService();
    }
    return PaymentTransactionService.instance;
  }

  /**
   * Execute query using centralized database manager
   */
  async executeQuery(query, params = []) {
    return await this.databaseManager.executeQuery(query, params);
  }

  /**
   * Safely parse JSON string, handling various edge cases
   */
  safeParseJSON(jsonString) {
    if (!jsonString) return {};
    if (typeof jsonString === "object") return jsonString;
    if (typeof jsonString !== "string") return {};
    if (jsonString === "[object Object]") return {};

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn("Failed to parse JSON:", jsonString, error.message);
      return {};
    }
  }

  /**
   * Initiate a new payment transaction
   */
  async initiateTransaction(transactionData) {
    try {
      const {
        orderId,
        shopifyOrderId,
        staffId,
        customerId,
        terminalId,
        locationId,
        totalAmount,
        currency = "KES",
        paymentMethods = [],
        notes,
        metadata = {},
      } = transactionData;

      // Validate required fields
      if (!staffId || !totalAmount || totalAmount <= 0) {
        throw new Error("Staff ID and valid total amount are required");
      }

      // Validate payment methods if provided
      if (paymentMethods.length > 0) {
        const totalMethodAmount = paymentMethods.reduce(
          (sum, method) => sum + method.amount,
          0
        );
        if (Math.abs(totalMethodAmount - totalAmount) > 0.01) {
          throw new Error("Payment method amounts must equal total amount");
        }
      }

      const transactionId = uuidv4();
      const isSplitPayment = paymentMethods.length > 1;
      const remainingAmount = paymentMethods.length > 0 ? 0 : totalAmount;

      // Debug: Log parameters to identify undefined values
      const params = [
        transactionId,
        orderId || null,
        shopifyOrderId || null,
        staffId,
        customerId || null,
        terminalId || null,
        locationId || null,
        totalAmount,
        currency,
        "pending",
        isSplitPayment,
        paymentMethods.length,
        remainingAmount,
        notes || null,
        JSON.stringify(metadata),
      ];

      console.log(
        "Transaction parameters:",
        params.map((p, i) => `${i}: ${p === undefined ? "UNDEFINED" : p}`)
      );

      // Create main transaction record using connection pool
      await this.executeQuery(
        `
        INSERT INTO payment_transactions (
          id, order_id, shopify_order_id, staff_id, customer_id, terminal_id, location_id,
          total_amount, currency, payment_status, is_split_payment, payment_count,
          remaining_amount, notes, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        params
      );

      // Create payment method records if provided
      for (const method of paymentMethods) {
        await this.addPaymentMethod(transactionId, method, staffId);
      }

      // Log transaction initiation
      await this.logPaymentAction(
        transactionId,
        null,
        staffId,
        "initiated",
        null,
        "pending",
        totalAmount,
        {
          isSplitPayment,
          paymentMethodCount: paymentMethods.length,
          metadata,
        }
      );

      return {
        success: true,
        transactionId,
        status: "pending",
        totalAmount,
        isSplitPayment,
        remainingAmount,
        paymentMethods: paymentMethods.length,
      };
    } catch (error) {
      console.error("Error initiating payment transaction:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Add a payment method to an existing transaction
   */
  async addPaymentMethod(transactionId, methodData, staffId) {
    try {
      const {
        methodType,
        methodName,
        amount,
        amountTendered,
        referenceCode,
        transactionCode,
        metadata = {},
      } = methodData;

      // Validate method data
      if (!methodType || !amount || amount <= 0) {
        throw new Error("Method type and valid amount are required");
      }

      // Prevent split payment method type from being added directly
      if (methodType === "split") {
        throw new Error(
          "Split payments should be added as individual payment methods (cash, credit, etc.), not as a single 'split' method"
        );
      }

      // Validate method type against allowed values
      const allowedMethodTypes = [
        "cash",
        "mpesa",
        "absa_till",
        "card",
        "credit",
      ];
      if (!allowedMethodTypes.includes(methodType)) {
        throw new Error(
          `Invalid method type: ${methodType}. Allowed types: ${allowedMethodTypes.join(
            ", "
          )}`
        );
      }

      const methodId = uuidv4();
      const changeAmount = amountTendered
        ? Math.max(0, amountTendered - amount)
        : 0;

      // Debug: Log parameters to identify undefined values
      const methodParams = [
        methodId,
        transactionId,
        methodType,
        methodName || methodType,
        amount,
        amountTendered || null,
        referenceCode || null,
        transactionCode || null,
        "pending",
        JSON.stringify(metadata),
      ];

      console.log(
        "Payment method parameters:",
        methodParams.map((p, i) => `${i}: ${p === undefined ? "UNDEFINED" : p}`)
      );

      // Insert payment method record using connection pool
      await this.executeQuery(
        `
        INSERT INTO payment_methods_used (
          id, transaction_id, method_type, method_name, amount, amount_tendered,
          reference_code, transaction_code, status, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        methodParams
      );

      // Update transaction remaining amount
      await this.updateTransactionRemainingAmount(transactionId);

      // Log method addition
      await this.logPaymentAction(
        transactionId,
        methodId,
        staffId,
        "method_added",
        null,
        "pending",
        amount,
        {
          methodType,
          methodName,
          referenceCode,
        }
      );

      return {
        success: true,
        methodId,
        status: "pending",
      };
    } catch (error) {
      console.error("Error adding payment method:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Process a specific payment method
   */
  async processPaymentMethod(methodId, staffId, processingData = {}) {
    try {
      return await this.databaseManager.executeTransaction(
        async (connection) => {
          // Get payment method details
          const [methodRows] = await connection.execute(
            "SELECT * FROM payment_methods_used WHERE id = ?",
            [methodId]
          );

          if (methodRows.length === 0) {
            throw new Error("Payment method not found");
          }

          const method = methodRows[0];
          const oldStatus = method.status;

          // Update method status to processing
          await connection.execute(
            "UPDATE payment_methods_used SET status = ?, updated_at = NOW() WHERE id = ?",
            ["processing", methodId]
          );

          // Process based on method type
          let processResult;
          switch (method.method_type) {
            case "cash":
              processResult = await this.processCashPayment(
                method,
                processingData
              );
              break;
            case "mpesa":
              processResult = await this.processMpesaPayment(
                method,
                processingData
              );
              break;
            case "absa_till":
              processResult = await this.processAbsaTillPayment(
                method,
                processingData
              );
              break;
            case "card":
              processResult = await this.processCardPayment(
                method,
                processingData
              );
              break;
            case "credit":
              processResult = await this.processCreditPayment(
                method,
                processingData,
                connection
              );
              break;
            case "split":
              // Split payments should not reach this point as they should be processed as individual methods
              // This is a fallback to prevent errors
              throw new Error(
                "Split payments should be processed as individual payment methods, not as a single 'split' method"
              );
            default:
              throw new Error(
                `Unsupported payment method: ${method.method_type}`
              );
          }

          // Update method status based on processing result and payment method type
          let newStatus;
          if (!processResult.success) {
            newStatus = "failed";
          } else if (method.method_type === "credit") {
            // Credit payments should be marked as authorized (reserved) not completed (paid)
            newStatus = "authorized";
          } else {
            // All other payment methods are completed immediately
            newStatus = "completed";
          }
          const processedAt = processResult.success ? new Date() : null;

          await connection.execute(
            `
          UPDATE payment_methods_used
          SET status = ?, processed_at = ?, error_message = ?, metadata = ?
          WHERE id = ?
        `,
            [
              newStatus,
              processedAt,
              processResult.error || null,
              JSON.stringify({
                ...this.safeParseJSON(method.metadata),
                ...processResult.metadata,
              }),
              methodId,
            ]
          );

          // Audit logging will be done asynchronously after transaction

          // Update transaction status if needed
          await this.updateTransactionStatus(method.transaction_id);

          return {
            success: processResult.success,
            status: newStatus,
            error: processResult.error,
            metadata: processResult.metadata,
            transactionId: method.transaction_id,
            oldStatus: method.status,
            amount: method.amount,
          };
        }
      );
    } catch (error) {
      console.error("Error processing payment method:", error);

      // Update method status to failed using executeQuery for proper connection management
      try {
        await this.databaseManager.executeQuery(
          "UPDATE payment_methods_used SET status = ?, error_message = ? WHERE id = ?",
          ["failed", error.message, methodId]
        );
      } catch (updateError) {
        console.error("Error updating failed payment method:", updateError);
      }

      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Process cash payment
   */
  async processCashPayment(method, processingData) {
    try {
      // Cash payments are processed immediately
      const { amountTendered } = processingData;

      if (amountTendered && amountTendered < method.amount) {
        throw new Error("Insufficient cash amount");
      }

      return {
        success: true,
        metadata: {
          processedAt: new Date().toISOString(),
          amountTendered: amountTendered || method.amount,
          change: Math.max(
            0,
            (amountTendered || method.amount) - method.amount
          ),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Process M-Pesa payment with STK Push and manual fallback
   */
  async processMpesaPayment(method, processingData) {
    try {
      const {
        paymentMethod = "stk_push",
        phoneNumber,
        transactionCode: originalTransactionCode,
        checkoutRequestId,
        accountReference,
        transactionDesc,
      } = processingData;

      let result;

      if (paymentMethod === "stk_push") {
        // Initiate STK Push
        if (!phoneNumber) {
          throw new Error("Phone number is required for STK Push");
        }

        result = await this.mpesaService.initiateSTKPush({
          phoneNumber,
          amount: method.amount,
          accountReference:
            accountReference || `ORDER_${method.transaction_id}`,
          transactionDesc:
            transactionDesc || `Payment for order ${method.transaction_id}`,
        });

        if (result.success) {
          return {
            success: true,
            metadata: {
              ...result.metadata,
              processedAt: new Date().toISOString(),
              checkoutRequestId: result.checkoutRequestId,
              merchantRequestId: result.merchantRequestId,
              customerMessage: result.customerMessage,
              method: "stk_push",
            },
          };
        } else {
          return {
            success: false,
            error: result.error,
            metadata: result.metadata,
          };
        }
      } else if (paymentMethod === "manual_code") {
        // Manual transaction code validation - now optional
        let transactionCode = originalTransactionCode;
        if (!transactionCode) {
          console.log(
            "⚠️ M-Pesa manual validation proceeding without transaction code"
          );
          // Generate a placeholder transaction code for processing
          transactionCode = `PENDING_MPESA_${Date.now()}`;
        }

        // For manual transaction codes, phone number is preferred but not strictly required in development
        // Try to extract phone number from various sources
        let validationPhoneNumber = phoneNumber;

        if (!validationPhoneNumber) {
          // Try to extract from method metadata
          const methodMetadata = this.safeParseJSON(method.metadata) || {};
          validationPhoneNumber =
            methodMetadata.phoneNumber ||
            methodMetadata.customerPhone ||
            "************"; // Default fallback for development

          console.log(
            `⚠️ Phone number not provided for M-Pesa manual validation, using fallback: ${validationPhoneNumber}`
          );
        }

        result = await this.mpesaService.validateManualTransaction(
          transactionCode,
          validationPhoneNumber,
          method.amount
        );

        if (result.success) {
          return {
            success: true,
            metadata: {
              ...result.metadata,
              processedAt: new Date().toISOString(),
              mpesaReceiptNumber: result.mpesaReceiptNumber,
              transactionCode,
              phoneNumber: result.phoneNumber,
              method: "manual_code",
            },
          };
        } else {
          return {
            success: false,
            error: result.error,
            metadata: result.metadata,
          };
        }
      } else if (paymentMethod === "stk_status_check") {
        // Check STK Push status
        if (!checkoutRequestId) {
          throw new Error("Checkout request ID is required for status check");
        }

        result = await this.mpesaService.checkSTKPushStatus(checkoutRequestId);

        if (result.success) {
          return {
            success: true,
            metadata: {
              ...result.metadata,
              processedAt: new Date().toISOString(),
              mpesaReceiptNumber: result.mpesaReceiptNumber,
              transactionDate: result.transactionDate,
              phoneNumber: result.phoneNumber,
              method: "stk_status_check",
            },
          };
        } else {
          return {
            success: false,
            error: result.error,
            metadata: result.metadata,
          };
        }
      } else {
        throw new Error(`Unsupported M-Pesa payment method: ${paymentMethod}`);
      }
    } catch (error) {
      console.error("Error processing M-Pesa payment:", error);
      return {
        success: false,
        error: error.message,
        metadata: {
          error: error.message,
          processedAt: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Process ABSA Till payment with enhanced validation
   */
  async processAbsaTillPayment(method, processingData) {
    try {
      const { transactionCode, tillNumber, customerPhone, customerName } =
        processingData;

      console.log("ABSA Till payment processing data:", {
        transactionCode,
        tillNumber,
        customerName,
        customerPhone,
        amount: method?.amount,
        methodId: method?.id,
      });

      // Transaction code is now optional for ABSA Till payments

      // Transaction code is now optional - validate only if provided
      let cleanTransactionCode = "";

      if (transactionCode && transactionCode.trim()) {
        cleanTransactionCode = transactionCode.trim().toUpperCase();

        // Validate format only if transaction code is provided
        if (cleanTransactionCode.length < 6) {
          throw new Error(
            "Invalid ABSA Till transaction code format. Must be at least 6 characters."
          );
        }

        if (cleanTransactionCode.length > 25) {
          throw new Error(
            "Invalid ABSA Till transaction code format. Must be no more than 25 characters."
          );
        }

        // Allow alphanumeric characters and some common special characters for flexibility
        if (!/^[A-Z0-9]+$/.test(cleanTransactionCode)) {
          throw new Error(
            "Invalid ABSA Till transaction code format. Must contain only letters and numbers."
          );
        }
      } else {
        // No transaction code provided - generate a placeholder for processing
        cleanTransactionCode = `PENDING_${Date.now()}`;
        console.log("⚠️ ABSA Till payment proceeding without transaction code");
      }

      console.log(
        `✅ ABSA Till transaction code validation passed: ${cleanTransactionCode} (${cleanTransactionCode.length} characters)`
      );

      // Validate amount matches (basic validation)
      if (method.amount <= 0) {
        throw new Error("Invalid payment amount");
      }

      // In production, you would validate the transaction code with ABSA API
      // For now, we'll do basic validation and simulate success

      // Simulate validation delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Basic validation - reject codes ending with '0000' for testing
      if (cleanTransactionCode.endsWith("0000")) {
        return {
          success: false,
          error:
            "Transaction code validation failed. Please verify the code and try again.",
          metadata: {
            transactionCode: cleanTransactionCode,
            tillNumber: tillNumber || "123456",
            validationError: "Invalid transaction code",
            processedAt: new Date().toISOString(),
          },
        };
      }

      // Simulate successful validation
      return {
        success: true,
        metadata: {
          processedAt: new Date().toISOString(),
          transactionCode: cleanTransactionCode,
          tillNumber: tillNumber || "123456",
          customerPhone: customerPhone || null,
          customerName: customerName || null,
          amount: method.amount,
          currency: "KES",
          paymentMethod: "absa_till",
          validatedAt: new Date().toISOString(),
          // In production, these would come from ABSA API response
          absaReceiptNumber: `ABSA${Date.now()}`,
          transactionDate: new Date().toISOString(),
          status: "completed",
        },
      };
    } catch (error) {
      console.error("Error processing ABSA Till payment:", error);
      return {
        success: false,
        error: error.message,
        metadata: {
          error: error.message,
          processedAt: new Date().toISOString(),
          paymentMethod: "absa_till",
        },
      };
    }
  }

  /**
   * Process card payment with manual confirmation workflow
   */
  async processCardPayment(method, processingData) {
    try {
      const {
        confirmed,
        cardType,
        lastFourDigits,
        authorizationCode,
        customerName,
        customerPhone,
        notes,
      } = processingData;

      // Validate confirmation
      if (!confirmed) {
        throw new Error("Card payment confirmation is required");
      }

      // Validate amount
      if (method.amount <= 0) {
        throw new Error("Invalid payment amount");
      }

      // Optional card details validation
      if (lastFourDigits && !/^\d{4}$/.test(lastFourDigits)) {
        throw new Error("Last four digits must be exactly 4 numbers");
      }

      if (authorizationCode && authorizationCode.length < 4) {
        throw new Error("Authorization code must be at least 4 characters");
      }

      // Simulate processing delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Simulate successful card payment processing
      return {
        success: true,
        metadata: {
          processedAt: new Date().toISOString(),
          confirmed: true,
          paymentMethod: "card_manual",
          cardType: cardType || "Unknown",
          lastFourDigits: lastFourDigits || "****",
          authorizationCode: authorizationCode || `AUTH${Date.now()}`,
          customerName: customerName || null,
          customerPhone: customerPhone || null,
          amount: method.amount,
          currency: "KES",
          transactionDate: new Date().toISOString(),
          // In production, these would come from payment processor
          cardReceiptNumber: `CARD${Date.now()}`,
          processorResponse: "APPROVED",
          notes: notes || null,
          status: "completed",
        },
      };
    } catch (error) {
      console.error("Error processing card payment:", error);
      return {
        success: false,
        error: error.message,
        metadata: {
          error: error.message,
          processedAt: new Date().toISOString(),
          paymentMethod: "card_manual",
        },
      };
    }
  }

  /**
   * Process credit payment with customer profile validation
   */
  async processCreditPayment(method, processingData, connection = null) {
    try {
      const {
        customerId,
        customerName,
        customerPhone,
        creditLimit,
        dueDate,
        paymentTerms,
        notes,
        existingBalance,
      } = processingData;

      // Validate required fields
      if (!customerName) {
        throw new Error("Customer name is required for credit payment");
      }

      // Handle phone number with fallback for development
      let cleanPhone = "************"; // Default fallback
      if (customerPhone) {
        cleanPhone = customerPhone.replace(/\D/g, "");
        if (cleanPhone.length < 9 || cleanPhone.length > 12) {
          console.log(
            `⚠️ Invalid phone number format for credit payment, using fallback: ${customerPhone}`
          );
          cleanPhone = "************";
        }
      } else {
        console.log(
          "⚠️ Customer phone number not provided for credit payment, using fallback"
        );
      }

      // Validate amount
      if (method.amount <= 0) {
        throw new Error("Invalid payment amount");
      }

      // Validate credit limit if provided
      const customerCreditLimit = creditLimit || 0;
      const currentBalance = existingBalance || 0;
      const newBalance = currentBalance + method.amount;

      if (customerCreditLimit > 0 && newBalance > customerCreditLimit) {
        throw new Error(
          `Credit limit exceeded. Available credit: KES ${(
            customerCreditLimit - currentBalance
          ).toFixed(2)}`
        );
      }

      // Calculate due date if not provided (default 30 days)
      const calculatedDueDate =
        dueDate ||
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0];

      // Create credit payment record
      const creditId = uuidv4();

      // Validate all parameters before creating SQL array
      const validatedParams = {
        creditId: creditId || uuidv4(),
        methodId: method?.id || null,
        customerId: customerId || null,
        customerName: customerName || "Unknown Customer",
        cleanPhone: cleanPhone || "************",
        customerCreditLimit: customerCreditLimit || 0,
        methodAmount: method?.amount || 0,
        calculatedDueDate:
          calculatedDueDate ||
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
        paymentTerms: paymentTerms || "Net 30",
        notes: notes || null,
        status: "active",
      };

      console.log("Credit payment validated parameters:", validatedParams);

      // Create SQL parameters array with validated values
      const sqlParams = [
        validatedParams.creditId,
        validatedParams.methodId,
        validatedParams.customerId,
        validatedParams.customerName,
        validatedParams.cleanPhone,
        validatedParams.customerCreditLimit,
        validatedParams.methodAmount,
        validatedParams.calculatedDueDate,
        validatedParams.paymentTerms,
        validatedParams.notes,
        validatedParams.status,
      ];

      console.log("Credit payment SQL parameters:", sqlParams);

      // Final check for undefined values
      sqlParams.forEach((param, index) => {
        if (param === undefined) {
          console.error(`❌ Parameter at index ${index} is still undefined:`, {
            index,
            value: param,
            parameterNames: [
              "creditId",
              "method.id",
              "customerId",
              "customerName",
              "cleanPhone",
              "customerCreditLimit",
              "method.amount",
              "calculatedDueDate",
              "paymentTerms",
              "notes",
              "status",
            ][index],
          });
          throw new Error(
            `SQL parameter at index ${index} (${
              [
                "creditId",
                "method.id",
                "customerId",
                "customerName",
                "cleanPhone",
                "customerCreditLimit",
                "method.amount",
                "calculatedDueDate",
                "paymentTerms",
                "notes",
                "status",
              ][index]
            }) is undefined`
          );
        }
      });

      // Use provided connection or fallback to executeQuery
      if (connection) {
        await connection.execute(
          `
          INSERT INTO credit_payments (
            id, payment_method_id, customer_id, customer_name, customer_phone,
            credit_limit, outstanding_balance, due_date, payment_terms, notes, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
          sqlParams
        );
      } else {
        await this.executeQuery(
          `
          INSERT INTO credit_payments (
            id, payment_method_id, customer_id, customer_name, customer_phone,
            credit_limit, outstanding_balance, due_date, payment_terms, notes, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
          sqlParams
        );
      }

      return {
        success: true,
        metadata: {
          processedAt: new Date().toISOString(),
          creditId,
          customerId: customerId || null,
          customerName,
          customerPhone: cleanPhone,
          amount: method.amount,
          currency: "KES",
          creditLimit: customerCreditLimit,
          outstandingBalance: method.amount,
          newTotalBalance: newBalance,
          dueDate: calculatedDueDate,
          paymentTerms: paymentTerms || "Net 30",
          notes: notes || null,
          paymentMethod: "credit",
          status: "active",
          createdAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error("Error processing credit payment:", error);
      return {
        success: false,
        error: error.message,
        metadata: {
          error: error.message,
          processedAt: new Date().toISOString(),
          paymentMethod: "credit",
        },
      };
    }
  }

  /**
   * Update transaction remaining amount
   */
  async updateTransactionRemainingAmount(transactionId) {
    try {
      // Calculate total completed payment amount
      const [amountRows] = await this.executeQuery(
        `
        SELECT COALESCE(SUM(amount), 0) as completed_amount
        FROM payment_methods_used
        WHERE transaction_id = ? AND status = 'completed'
      `,
        [transactionId]
      );

      const completedAmount = amountRows[0].completed_amount;

      // Get transaction total
      const [transactionRows] = await this.executeQuery(
        "SELECT total_amount FROM payment_transactions WHERE id = ?",
        [transactionId]
      );

      if (transactionRows.length === 0) {
        throw new Error("Transaction not found");
      }

      const totalAmount = transactionRows[0].total_amount;
      const remainingAmount = Math.max(0, totalAmount - completedAmount);

      // Update transaction
      await this.executeQuery(
        `
        UPDATE payment_transactions
        SET remaining_amount = ?, updated_at = NOW()
        WHERE id = ?
      `,
        [remainingAmount, transactionId]
      );

      return remainingAmount;
    } catch (error) {
      console.error("Error updating transaction remaining amount:", error);
      throw error;
    }
  }

  /**
   * Update transaction status based on payment method statuses
   */
  async updateTransactionStatus(transactionId) {
    try {
      // Get payment method statuses
      const [methodRows] = await this.executeQuery(
        `
        SELECT status, COUNT(*) as count
        FROM payment_methods_used
        WHERE transaction_id = ?
        GROUP BY status
      `,
        [transactionId]
      );

      const statusCounts = {};
      let totalMethods = 0;

      methodRows.forEach((row) => {
        statusCounts[row.status] = row.count;
        totalMethods += row.count;
      });

      let newStatus = "pending";

      if (statusCounts.failed && statusCounts.failed === totalMethods) {
        newStatus = "failed";
      } else if (
        statusCounts.completed &&
        statusCounts.completed === totalMethods
      ) {
        newStatus = "completed";
      } else if (
        statusCounts.authorized &&
        statusCounts.authorized === totalMethods
      ) {
        newStatus = "authorized";
      } else if (
        (statusCounts.completed || 0) + (statusCounts.authorized || 0) === totalMethods
      ) {
        // Mixed completed and authorized payments - transaction is processing
        newStatus = "processing";
      } else if (statusCounts.processing > 0) {
        newStatus = "processing";
      }

      // Update transaction status
      const completedAt = newStatus === "completed" ? new Date() : null;

      await this.executeQuery(
        `
        UPDATE payment_transactions
        SET payment_status = ?, completed_payments = ?, completed_at = ?, updated_at = NOW()
        WHERE id = ?
      `,
        [newStatus, statusCounts.completed || 0, completedAt, transactionId]
      );

      return newStatus;
    } catch (error) {
      console.error("Error updating transaction status:", error);
      throw error;
    }
  }

  /**
   * Get transaction details with payment methods
   */
  async getTransactionDetails(transactionId) {
    try {
      // Get transaction
      const [transactionRows] = await this.executeQuery(
        "SELECT * FROM payment_transactions WHERE id = ?",
        [transactionId]
      );

      if (transactionRows.length === 0) {
        throw new Error("Transaction not found");
      }

      const transaction = transactionRows[0];

      // Get payment methods
      const [methodRows] = await this.executeQuery(
        "SELECT * FROM payment_methods_used WHERE transaction_id = ? ORDER BY created_at",
        [transactionId]
      );

      // Get credit payment details if any
      const [creditRows] = await this.executeQuery(
        `
        SELECT cp.* FROM credit_payments cp
        JOIN payment_methods_used pmu ON cp.payment_method_id = pmu.id
        WHERE pmu.transaction_id = ?
      `,
        [transactionId]
      );

      return {
        success: true,
        transaction: {
          ...transaction,
          metadata: this.safeParseJSON(transaction.metadata),
        },
        paymentMethods: methodRows.map((method) => ({
          ...method,
          metadata: this.safeParseJSON(method.metadata),
        })),
        creditPayments: creditRows,
      };
    } catch (error) {
      console.error("Error getting transaction details:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Log payment action for audit trail (async, non-blocking)
   */
  async logPaymentAction(
    transactionId,
    paymentMethodId,
    staffId,
    action,
    oldStatus,
    newStatus,
    amount,
    details = {}
  ) {
    // Make audit logging asynchronous and non-blocking
    setImmediate(async () => {
      try {
        const logId = uuidv4();

        // Use a separate connection for audit logging to avoid transaction locks
        const connection = await this.databaseManager.getConnection();
        try {
          await connection.execute(
            `
            INSERT INTO payment_audit_log (
              id, transaction_id, payment_method_id, staff_id, action,
              old_status, new_status, amount, details
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `,
            [
              logId,
              transactionId,
              paymentMethodId,
              staffId,
              action,
              oldStatus,
              newStatus,
              amount,
              JSON.stringify(details),
            ]
          );
        } finally {
          connection.release();
        }
      } catch (error) {
        console.error("Error logging payment action:", error);
        // Don't throw error for logging failures - just log it
      }
    });
  }

  /**
   * Cancel transaction
   */
  async cancelTransaction(transactionId, staffId, reason = "") {
    try {
      // Update transaction status
      await this.executeQuery(
        `
        UPDATE payment_transactions
        SET payment_status = 'cancelled', updated_at = NOW()
        WHERE id = ? AND payment_status IN ('pending', 'processing')
      `,
        [transactionId]
      );

      // Cancel all pending payment methods
      await this.executeQuery(
        `
        UPDATE payment_methods_used
        SET status = 'cancelled', updated_at = NOW()
        WHERE transaction_id = ? AND status IN ('pending', 'processing')
      `,
        [transactionId]
      );

      // Log cancellation
      await this.logPaymentAction(
        transactionId,
        null,
        staffId,
        "cancelled",
        null,
        "cancelled",
        0,
        { reason }
      );

      return {
        success: true,
        status: "cancelled",
      };
    } catch (error) {
      console.error("Error cancelling transaction:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate split payment amounts
   */
  async validateSplitPayment(totalAmount, paymentMethods) {
    try {
      if (!Array.isArray(paymentMethods) || paymentMethods.length === 0) {
        throw new Error("Payment methods array is required");
      }

      const totalMethodAmount = paymentMethods.reduce((sum, method) => {
        const amount = parseFloat(method.amount);
        if (isNaN(amount) || amount <= 0) {
          throw new Error(
            `Invalid amount for payment method: ${method.methodType}`
          );
        }
        return sum + amount;
      }, 0);

      const difference = Math.abs(totalMethodAmount - totalAmount);
      if (difference > 0.01) {
        // Allow for small rounding differences
        throw new Error(
          `Payment method amounts (${totalMethodAmount}) must equal total amount (${totalAmount})`
        );
      }

      // Validate individual payment methods
      for (const method of paymentMethods) {
        if (!method.methodType) {
          throw new Error("Payment method type is required");
        }

        if (
          !["cash", "mpesa", "absa_till", "card", "credit"].includes(
            method.methodType
          )
        ) {
          throw new Error(
            `Unsupported payment method type: ${method.methodType}`
          );
        }
      }

      return {
        success: true,
        totalAmount,
        methodCount: paymentMethods.length,
        isValid: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        isValid: false,
      };
    }
  }

  /**
   * Calculate remaining balance for split payment
   */
  async calculateRemainingBalance(transactionId) {
    try {
      // Get transaction total
      const [transactionRows] = await this.executeQuery(
        "SELECT total_amount FROM payment_transactions WHERE id = ?",
        [transactionId]
      );

      if (transactionRows.length === 0) {
        throw new Error("Transaction not found");
      }

      const totalAmount = parseFloat(transactionRows[0].total_amount);

      // Get completed payment amounts
      const [completedRows] = await this.executeQuery(
        `
        SELECT COALESCE(SUM(amount), 0) as completed_amount
        FROM payment_methods_used
        WHERE transaction_id = ? AND status = 'completed'
      `,
        [transactionId]
      );

      // Get pending payment amounts
      const [pendingRows] = await this.executeQuery(
        `
        SELECT COALESCE(SUM(amount), 0) as pending_amount
        FROM payment_methods_used
        WHERE transaction_id = ? AND status IN ('pending', 'processing')
      `,
        [transactionId]
      );

      const completedAmount = parseFloat(completedRows[0].completed_amount);
      const pendingAmount = parseFloat(pendingRows[0].pending_amount);
      const remainingAmount = Math.max(
        0,
        totalAmount - completedAmount - pendingAmount
      );

      return {
        success: true,
        totalAmount,
        completedAmount,
        pendingAmount,
        remainingAmount,
        isFullyPaid: remainingAmount === 0 && pendingAmount === 0,
        isPartiallyPaid: completedAmount > 0,
      };
    } catch (error) {
      console.error("Error calculating remaining balance:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get split payment summary
   */
  async getSplitPaymentSummary(transactionId) {
    try {
      // Get transaction details
      const transactionResult = await this.getTransactionDetails(transactionId);
      if (!transactionResult.success) {
        throw new Error(transactionResult.error);
      }

      const transaction = transactionResult.transaction;
      const paymentMethods = transactionResult.paymentMethods;

      // Calculate balance
      const balanceResult = await this.calculateRemainingBalance(transactionId);
      if (!balanceResult.success) {
        throw new Error(balanceResult.error);
      }

      // Group payment methods by status
      const methodsByStatus = {
        completed: paymentMethods.filter((m) => m.status === "completed"),
        pending: paymentMethods.filter((m) => m.status === "pending"),
        processing: paymentMethods.filter((m) => m.status === "processing"),
        failed: paymentMethods.filter((m) => m.status === "failed"),
        cancelled: paymentMethods.filter((m) => m.status === "cancelled"),
      };

      return {
        success: true,
        transaction: {
          id: transaction.id,
          totalAmount: parseFloat(transaction.total_amount),
          status: transaction.payment_status,
          isSplitPayment: transaction.is_split_payment,
          createdAt: transaction.created_at,
          completedAt: transaction.completed_at,
        },
        balance: balanceResult,
        paymentMethods: {
          total: paymentMethods.length,
          byStatus: {
            completed: methodsByStatus.completed.length,
            pending: methodsByStatus.pending.length,
            processing: methodsByStatus.processing.length,
            failed: methodsByStatus.failed.length,
            cancelled: methodsByStatus.cancelled.length,
          },
          details: paymentMethods.map((method) => ({
            id: method.id,
            type: method.method_type,
            name: method.method_name,
            amount: parseFloat(method.amount),
            status: method.status,
            processedAt: method.processed_at,
            metadata: this.safeParseJSON(method.metadata),
          })),
        },
      };
    } catch (error) {
      console.error("Error getting split payment summary:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Complete transaction with Shopify order creation (FIXED: No locks during API calls)
   */
  async completeTransactionWithShopify(transactionId, orderData) {
    try {
      // Get transaction details (no locks held)
      const transactionResult = await this.getTransactionDetails(transactionId);
      if (!transactionResult.success) {
        throw new Error(transactionResult.error);
      }

      const transaction = transactionResult.transaction;
      const paymentMethods = transactionResult.paymentMethods;

      // Debug: Log payment methods before Shopify call
      console.log('🔍 Payment methods before Shopify call:', paymentMethods.map(m => ({
        id: m.id,
        method_type: m.method_type,
        status: m.status,
        amount: m.amount
      })));

      // Check if all payment methods have been processed (not necessarily completed)
      const unprocessedMethods = paymentMethods.filter(
        (m) => m.status === "pending"
      );
      if (unprocessedMethods.length > 0) {
        console.log('❌ Found unprocessed methods:', unprocessedMethods.map(m => ({
          id: m.id,
          method_type: m.method_type,
          status: m.status
        })));
        throw new Error(
          `Cannot create Shopify order: ${unprocessedMethods.length} payment method(s) still pending processing`
        );
      }

      // Check if Shopify order already exists
      if (transaction.shopify_order_id) {
        return {
          success: true,
          shopifyOrderId: transaction.shopify_order_id,
          message: "Shopify order already exists for this transaction",
        };
      }

      // FIXED: Call Shopify API WITHOUT holding any database locks
      const shopifyResult = await this.shopifyService.completeOrderWithPayments(
        orderData,
        transaction,
        paymentMethods
      );

      if (!shopifyResult.success) {
        throw new Error(`Shopify integration failed: ${shopifyResult.error}`);
      }

      // FIXED: Only update database AFTER successful API call, with minimal lock time
      await this.executeQuery(
        "UPDATE payment_transactions SET shopify_order_id = ?, updated_at = NOW() WHERE id = ?",
        [shopifyResult.shopifyOrderId, transactionId]
      );

      // Update transaction status based on payment method statuses
      console.log('🔄 Updating transaction status after Shopify order creation...');
      const updatedStatus = await this.updateTransactionStatus(transactionId);
      console.log(`✅ Transaction status updated to: ${updatedStatus}`);

      // Process loyalty points automatically if customer is present
      let loyaltyResult = null;
      console.log("🔍 Checking loyalty processing conditions:", {
        hasCustomer: !!orderData.customer?.id,
        customerId: orderData.customer?.id,
        totalAmount: transaction.total_amount,
        shouldProcess: !!(
          orderData.customer?.id && transaction.total_amount > 0
        ),
      });

      if (orderData.customer?.id && transaction.total_amount > 0) {
        try {
          console.log(
            `🔄 Processing loyalty points for order ${shopifyResult.shopifyOrderId}...`
          );

          const loyaltyService = require("./loyalty-service");
          loyaltyResult = await loyaltyService.addPoints(
            orderData.customer.id,
            parseFloat(transaction.total_amount),
            shopifyResult.shopifyOrderId,
            transaction.staff_id,
            orderData.salesAgentId || null
          );

          if (loyaltyResult.success) {
            console.log(
              `✅ Loyalty points added automatically: +${loyaltyResult.pointsAdded} points for customer ${orderData.customer.id}`
            );
            console.log(
              "🔍 Full loyalty result:",
              JSON.stringify(loyaltyResult, null, 2)
            );
          } else {
            console.error(
              `❌ Failed to add loyalty points automatically:`,
              loyaltyResult.error
            );
          }
        } catch (loyaltyError) {
          console.error(
            `❌ Error processing loyalty points automatically:`,
            loyaltyError
          );
          // Don't fail the entire transaction if loyalty processing fails
        }
      }

      // Log completion
      await this.logPaymentAction(
        transactionId,
        null,
        transaction.staff_id,
        "completed",
        "completed",
        "completed",
        transaction.total_amount,
        {
          shopifyOrderId: shopifyResult.shopifyOrderId,
          orderNumber: shopifyResult.orderNumber,
          paymentsCreated: shopifyResult.paymentsCreated,
          financialStatus: shopifyResult.financialStatus,
          loyaltyProcessed: loyaltyResult?.success || false,
          loyaltyPointsAdded: loyaltyResult?.pointsAdded || 0,
        }
      );

      const response = {
        success: true,
        shopifyOrderId: shopifyResult.shopifyOrderId,
        orderNumber: shopifyResult.orderNumber,
        paymentsCreated: shopifyResult.paymentsCreated,
        financialStatus: shopifyResult.financialStatus,
        paymentDetails: shopifyResult.paymentDetails,
        loyaltyResult: loyaltyResult, // Include loyalty result in response
      };

      console.log(
        "🔍 Final response being returned:",
        JSON.stringify(response, null, 2)
      );
      return response;
    } catch (error) {
      console.error("Error completing transaction with Shopify:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get transaction summary for reporting
   */
  async getTransactionSummary(filters = {}) {
    try {
      const connection = await this.databaseManager.getConnection();
      const { staffId, dateFrom, dateTo, status } = filters;

      let whereClause = "1=1";
      const params = [];

      if (staffId) {
        whereClause += " AND staff_id = ?";
        params.push(staffId);
      }

      if (dateFrom) {
        whereClause += " AND created_at >= ?";
        params.push(dateFrom);
      }

      if (dateTo) {
        whereClause += " AND created_at <= ?";
        params.push(dateTo);
      }

      if (status) {
        whereClause += " AND payment_status = ?";
        params.push(status);
      }

      const [summaryRows] = await connection.execute(
        `
        SELECT
          payment_status,
          COUNT(*) as transaction_count,
          SUM(total_amount) as total_amount,
          AVG(total_amount) as average_amount,
          SUM(CASE WHEN is_split_payment = 1 THEN 1 ELSE 0 END) as split_payment_count
        FROM payment_transactions
        WHERE ${whereClause}
        GROUP BY payment_status
      `,
        params
      );

      return {
        success: true,
        summary: summaryRows,
      };
    } catch (error) {
      console.error("Error getting transaction summary:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

// Export singleton instance
const paymentTransactionService = PaymentTransactionService.getInstance();

module.exports = PaymentTransactionService;
module.exports.paymentTransactionService = paymentTransactionService;
