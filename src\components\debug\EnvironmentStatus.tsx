import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { API_CONFIG, API_UTILS, ENV_CONFIG } from '@/constants/Api';
import { useTheme } from '@/hooks/useTheme';

interface EnvironmentStatusProps {
  showDetails?: boolean;
  onPress?: () => void;
}

export const EnvironmentStatus: React.FC<EnvironmentStatusProps> = ({
  showDetails = false,
  onPress,
}) => {
  const { theme } = useTheme();
  const envInfo = API_UTILS.getCurrentEnvironment();

  const getEnvironmentColor = () => {
    switch (envInfo.environment) {
      case 'local':
        return '#4CAF50'; // Green
      case 'development':
        return '#FF9800'; // Orange
      case 'production':
        return '#F44336'; // Red
      default:
        return theme.colors.text;
    }
  };

  const getEnvironmentIcon = () => {
    switch (envInfo.environment) {
      case 'local':
        return '🏠';
      case 'development':
        return '🚧';
      case 'production':
        return '🚀';
      default:
        return '❓';
    }
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      // Show detailed environment info
      const allEnvs = API_UTILS.getAllEnvironments();
      const envList = allEnvs.map(env => 
        `${env.key === envInfo.environment ? '✅' : '⚪'} ${env.name}: ${env.baseURL}`
      ).join('\n');

      Alert.alert(
        'API Environment Status',
        `Current: ${envInfo.name}\nURL: ${envInfo.baseURL}\n\nAll Environments:\n${envList}`,
        [{ text: 'OK' }]
      );
    }
  };

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: getEnvironmentColor(),
    },
    icon: {
      fontSize: 16,
      marginRight: 8,
    },
    text: {
      color: theme.colors.text,
      fontSize: 12,
      fontWeight: '500',
    },
    detailsContainer: {
      marginTop: 8,
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    detailText: {
      color: theme.colors.textSecondary,
      fontSize: 10,
      marginBottom: 2,
    },
    urlText: {
      color: getEnvironmentColor(),
      fontSize: 10,
      fontFamily: 'monospace',
    },
  });

  return (
    <TouchableOpacity onPress={handlePress}>
      <View style={styles.container}>
        <Text style={styles.icon}>{getEnvironmentIcon()}</Text>
        <View style={{ flex: 1 }}>
          <Text style={styles.text}>
            {envInfo.name}
          </Text>
          {showDetails && (
            <View style={styles.detailsContainer}>
              <Text style={styles.detailText}>
                Platform: {ENV_CONFIG.platform}
              </Text>
              <Text style={styles.detailText}>
                Secure: {API_UTILS.isSecureEnvironment() ? 'Yes' : 'No'}
              </Text>
              <Text style={styles.urlText}>
                {envInfo.baseURL}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

// Debug component that shows environment info in development
export const DebugEnvironmentBanner: React.FC = () => {
  const { theme } = useTheme();
  
  // Only show in development
  if (!__DEV__) {
    return null;
  }

  const envInfo = API_UTILS.getCurrentEnvironment();

  const styles = StyleSheet.create({
    banner: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      backgroundColor: envInfo.isProduction ? '#F44336' : '#FF9800',
      paddingVertical: 4,
      paddingHorizontal: 8,
      zIndex: 1000,
    },
    bannerText: {
      color: 'white',
      fontSize: 10,
      textAlign: 'center',
      fontWeight: 'bold',
    },
  });

  return (
    <View style={styles.banner}>
      <Text style={styles.bannerText}>
        {envInfo.name.toUpperCase()} - {envInfo.baseURL}
      </Text>
    </View>
  );
};

// Hook for accessing environment info in components
export const useEnvironment = () => {
  return {
    ...API_UTILS.getCurrentEnvironment(),
    config: API_CONFIG,
    utils: API_UTILS,
    isSecure: API_UTILS.isSecureEnvironment(),
  };
};
