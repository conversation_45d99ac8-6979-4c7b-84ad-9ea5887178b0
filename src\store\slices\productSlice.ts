import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getAPIClient } from "../../services/api/dukalink-client";
import { Product } from "../../types/shopify";

interface ProductState {
  products: Product[];
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  pagination: {
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
    endCursor?: string;
    startCursor?: string;
  };
}

const initialState: ProductState = {
  products: [],
  isLoading: false,
  error: null,
  searchQuery: "",
  pagination: {
    page: 1,
    limit: 20,
    hasNext: false,
    hasPrev: false,
    endCursor: undefined,
    startCursor: undefined,
  },
};

// Async thunks
export const invalidateProductCache = createAsyncThunk(
  "products/invalidateCache",
  async (_, { dispatch, getState }) => {
    // Force refresh of products by clearing cache and refetching
    dispatch(clearProducts());

    // Get current search query to maintain search state
    const state = getState() as { products: ProductState };
    const currentSearchQuery = state.products.searchQuery;

    // Refetch products with current search query
    return dispatch(
      fetchProducts({
        search: currentSearchQuery,
        page: 1,
        limit: 50,
      })
    );
  }
);

export const updateProductInventory = createAsyncThunk(
  "products/updateInventory",
  async (
    updates: { variantId: string; newQuantity: number }[],
    { getState }
  ) => {
    // Update inventory quantities in the local cache
    // This provides immediate UI feedback before backend sync
    return updates;
  }
);

export const fetchProducts = createAsyncThunk(
  "products/fetchProducts",
  async (
    {
      page = 1,
      limit = 50,
      search = "",
      cursor = null,
    }: {
      page?: number;
      limit?: number;
      search?: string;
      cursor?: string | null;
    } = {},
    { rejectWithValue, getState }
  ) => {
    try {
      const apiClient = getAPIClient();

      // Use search endpoint if search query is provided
      if (search && search.trim()) {
        const response = await apiClient.request("/store/products/search", {
          params: { query: search.trim(), limit },
        });

        if (!response.success) {
          return rejectWithValue(response.error || "Failed to search products");
        }

        return {
          products: response.data?.products || [],
          pagination: response.meta?.pagination || {
            page: 1,
            limit,
            hasNext: false,
            hasPrev: false,
            total: 0,
          },
          isSearch: true,
        };
      } else {
        // Use regular products endpoint for listing
        const response = await apiClient.getStoreProducts({
          page,
          limit,
          cursor,
        });

        if (!response.success) {
          return rejectWithValue(response.error || "Failed to fetch products");
        }

        return {
          products: response.data?.products || [],
          pagination: response.data?.pagination ||
            response.meta?.pagination || {
              page,
              limit,
              hasNext: false,
              hasPrev: false,
              total: 0,
            },
          isSearch: false,
        };
      }
    } catch (error: any) {
      return rejectWithValue(error.message || "Network error");
    }
  }
);

// Simplified search that uses the same fetchProducts thunk
export const searchProducts = createAsyncThunk(
  "products/searchProducts",
  async (
    {
      query,
      limit = 50,
    }: {
      query: string;
      limit?: number;
    },
    { dispatch, rejectWithValue }
  ) => {
    // Use the main fetchProducts thunk with search parameter
    return dispatch(fetchProducts({ search: query, limit, page: 1 }));
  }
);

const productSlice = createSlice({
  name: "products",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    clearProducts: (state) => {
      state.products = [];
      state.pagination = {
        page: 1,
        limit: 20,
        hasNext: false,
        hasPrev: false,
      };
    },
    resetPagination: (state) => {
      state.pagination.page = 1;
      state.pagination.hasNext = false;
      state.pagination.hasPrev = false;
    },
    updateInventoryQuantities: (
      state,
      action: PayloadAction<{ variantId: string; newQuantity: number }[]>
    ) => {
      // Update inventory quantities for specific variants
      action.payload.forEach(({ variantId, newQuantity }) => {
        state.products.forEach((product) => {
          product.variants.forEach((variant) => {
            if (variant.id === variantId) {
              variant.inventoryQuantity = Math.max(0, newQuantity);
            }
          });
        });
      });
    },
    markProductsStale: (state) => {
      // Mark products as potentially stale to trigger refresh
      state.error = "inventory_stale";
    },
  },
  extraReducers: (builder) => {
    // Fetch Products (handles both regular fetch and search)
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        const response = action.payload;

        // Handle both direct response and nested response from searchProducts
        const actualResponse = response.payload || response;
        const products = actualResponse.products || [];
        const pagination = actualResponse.pagination || {
          page: 1,
          limit: 50,
          hasNext: false,
          hasPrev: false,
          total: 0,
        };

        // For search results or first page, replace products
        // For pagination, append products (avoiding duplicates)
        // Check if this is a fresh load (no existing products or search)
        const isFirstPage =
          actualResponse.isSearch ||
          state.products.length === 0 ||
          (pagination.page === 1 && !state.pagination.endCursor);

        if (__DEV__) {
          console.log("📦 Product slice - processing response:", {
            isFirstPage,
            currentProductsCount: state.products.length,
            newProductsCount: products.length,
            responsePage: pagination.page,
            currentPage: state.pagination.page,
            hasNext: pagination.hasNext,
            isSearch: actualResponse.isSearch,
            currentEndCursor: state.pagination.endCursor,
            newEndCursor: pagination.endCursor,
          });
        }

        if (isFirstPage) {
          state.products = products;
          if (__DEV__) console.log("🔄 Replaced products (first page/search)");
        } else {
          // Filter out any products that already exist to prevent duplicates
          const existingIds = new Set(state.products.map((p: Product) => p.id));
          const newProducts = products.filter(
            (p: Product) => !existingIds.has(p.id)
          );

          if (newProducts.length > 0) {
            state.products = [...state.products, ...newProducts];
            if (__DEV__) {
              console.log(
                `➕ Appended ${newProducts.length} new products (total: ${state.products.length})`
              );
            }
          } else {
            if (__DEV__) {
              console.log(
                "⚠️ No new products to append - all products already exist"
              );
            }
          }
        }

        // Update pagination state with the response pagination
        state.pagination = {
          ...state.pagination,
          ...pagination,
        };

        if (__DEV__)
          console.log("📊 Updated pagination state:", state.pagination);
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Search Products (now just delegates to fetchProducts)
    builder
      .addCase(searchProducts.pending, () => {
        // Loading state is handled by fetchProducts
      })
      .addCase(searchProducts.fulfilled, () => {
        // Result is handled by fetchProducts
      })
      .addCase(searchProducts.rejected, () => {
        // Error is handled by fetchProducts
      });

    // Invalidate Product Cache
    builder
      .addCase(invalidateProductCache.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(invalidateProductCache.fulfilled, (state) => {
        state.isLoading = false;
        // Products are refreshed by the fetchProducts call
      })
      .addCase(invalidateProductCache.rejected, (state, action) => {
        state.isLoading = false;
        state.error = "Failed to refresh product data";
      });

    // Update Product Inventory
    builder.addCase(updateProductInventory.fulfilled, (state, action) => {
      // Update inventory quantities immediately for UI responsiveness
      action.payload.forEach(({ variantId, newQuantity }) => {
        state.products.forEach((product) => {
          product.variants.forEach((variant) => {
            if (variant.id === variantId) {
              variant.inventoryQuantity = Math.max(0, newQuantity);
            }
          });
        });
      });
    });
  },
});

export const {
  clearError,
  setSearchQuery,
  clearProducts,
  resetPagination,
  updateInventoryQuantities,
  markProductsStale,
} = productSlice.actions;
export default productSlice.reducer;
