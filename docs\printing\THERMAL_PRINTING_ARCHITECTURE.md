# Thermal Printing System Architecture

## System Overview

The thermal printing system follows a layered architecture with clear separation of concerns, supporting multiple printer types and providing robust error handling with offline capabilities.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        UI Layer                                 │
├─────────────────────────────────────────────────────────────────┤
│  CartScreen  │ PrinterSetupScreen │ TestReceiptScreen │ Settings │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Service Layer                              │
├─────────────────────────────────────────────────────────────────┤
│           PrintService          │      EnhancedPrintService     │
│     (Core Functionality)        │    (Enhanced Features)        │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Utility Layer                               │
├─────────────────────────────────────────────────────────────────┤
│ BluetoothPermissionHelper │ ReceiptGenerator │ QRCodePrinter    │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Hardware Layer                               │
├─────────────────────────────────────────────────────────────────┤
│  Bluetooth ESC/POS  │    USB Printer    │   Network Printer    │
│     Printer         │                   │                      │
└─────────────────────────────────────────────────────────────────┘
```

## Data Flow Architecture

### 1. Print Request Flow

```
User Action (Print Button)
        │
        ▼
UI Component (CartScreen/TestScreen)
        │
        ▼
Service Layer (PrintService/EnhancedPrintService)
        │
        ▼
Configuration Check (AsyncStorage)
        │
        ▼
Printer Connection Validation
        │
        ▼
Receipt Data Formatting
        │
        ▼
Hardware Communication
        │
        ▼
Physical Print Output
```

### 2. Configuration Flow

```
User Setup (PrinterSetupScreen)
        │
        ▼
Printer Type Selection (BLE/USB/NET)
        │
        ▼
Device Discovery/Scanning
        │
        ▼
Connection Testing
        │
        ▼
Configuration Persistence (AsyncStorage)
        │
        ▼
Service Initialization
```

## Core Service Interactions

### PrintService Class Structure

```javascript
class PrintService {
  // Configuration Management
  - printerType: 'ble' | 'usb' | 'net'
  - printerModel: string
  - connectedDevice: object
  
  // Core Methods
  + init(): Promise<boolean>
  + setPrinterType(type): Promise<boolean>
  + scanDevices(): Promise<array>
  + connectToPrinter(address): Promise<boolean>
  + printReceipt(saleData): Promise<void>
  + disconnect(): Promise<void>
  
  // Bluetooth Specific
  + requestBluetoothPermissions(): Promise<boolean>
  + isBluetoothEnabled(): Promise<boolean>
  + enableBluetooth(): Promise<boolean>
}
```

### EnhancedPrintService Integration

```javascript
class EnhancedPrintService {
  // Enhanced Features
  + printReceipt(saleId): Promise<object>
  + ensurePrinterConnection(): Promise<void>
  + handlePrintError(error, saleId): Promise<object>
  + storeOfflineReceipt(saleId): Promise<void>
  + printWithKRACompliance(data, options): Promise<void>
  
  // Configuration Detection
  + detectPrinterConfiguration(): Promise<boolean>
  + validatePrinterConnection(): Promise<boolean>
}
```

## State Management

### AsyncStorage Keys and Data Structure

```javascript
// Primary Configuration Keys
'@dukalink_printer_address': string    // Printer MAC/IP address
'@dukalink_printer_type': string       // 'ble', 'usb', 'net'
'@dukalink_printer_model': string      // 'xprinter', 'epson', etc.

// Legacy Compatibility
'saved_printer_address': string        // Backward compatibility

// Offline Storage
'offline_receipt_${saleId}': string    // JSON receipt data
```

### Service State Management

```javascript
// PrintService Internal State
{
  isConnected: boolean,
  pairedDevices: array,
  connectedDevice: object,
  printerType: string,
  printerModel: string,
  printer: object  // Hardware driver instance
}
```

## Hardware Abstraction Layer

### Printer Type Strategy Pattern

```javascript
// Strategy Implementation
switch (printerType) {
  case 'ble':
    this.printer = BluetoothEscposPrinter;
    break;
  case 'usb':
    this.printer = USBPrinter;
    await USBPrinter.init();
    break;
  case 'net':
    this.printer = NetPrinter;
    await NetPrinter.init();
    break;
}
```

### Hardware Driver Interface

```javascript
// Common Interface for All Printer Types
interface PrinterDriver {
  init(): Promise<void>
  connect(address: string): Promise<boolean>
  disconnect(): Promise<void>
  printText(text: string): Promise<void>
  printImage(base64: string): Promise<void>
  printerAlign(alignment: number): Promise<void>
}
```

## Receipt Formatting Architecture

### Receipt Data Structure

```javascript
// Standard Receipt Data Format
{
  id: string,
  date: Date,
  customer_name: string,
  customer_pin: string,
  items: [
    {
      name: string,
      quantity: number,
      unit_price: number,
      total_price: number
    }
  ],
  subtotal: number,
  tax_amount: number,
  total_amount: number,
  payment_method: string,
  payment_reference: string,
  cashier_name: string,
  receipt_type: string
}
```

### Formatting Pipeline

```
Receipt Data Input
        │
        ▼
Business Header Formatting
        │
        ▼
Customer Information Section
        │
        ▼
Items List with Calculations
        │
        ▼
Totals and Tax Breakdown
        │
        ▼
Payment Information
        │
        ▼
KRA Compliance Section
        │
        ▼
Footer and Branding
        │
        ▼
ESC/POS Command Generation
```

## Error Handling Architecture

### Error Classification

```javascript
// Error Types and Handling
{
  ConnectionError: {
    handler: 'retry_connection',
    fallback: 'offline_storage'
  },
  PermissionError: {
    handler: 'request_permissions',
    fallback: 'user_guidance'
  },
  HardwareError: {
    handler: 'device_diagnostics',
    fallback: 'alternative_method'
  },
  ConfigurationError: {
    handler: 'setup_wizard',
    fallback: 'manual_configuration'
  }
}
```

### Error Recovery Flow

```
Error Detected
        │
        ▼
Error Classification
        │
        ▼
Automatic Recovery Attempt
        │
        ▼
User Notification (if needed)
        │
        ▼
Fallback Strategy Execution
        │
        ▼
Offline Storage (last resort)
```

## Permission Management Architecture

### Android Permission Flow

```
App Launch
        │
        ▼
Check Android Version (API Level)
        │
        ▼
Request Appropriate Permissions
        │
        ├─ API 31+: BLUETOOTH_CONNECT, BLUETOOTH_SCAN
        └─ API <31: BLUETOOTH, BLUETOOTH_ADMIN
        │
        ▼
Permission Grant Status
        │
        ▼
Service Initialization
```

### Permission Helper Integration

```javascript
// BluetoothPermissionHelper Flow
requestBluetoothPermissions() {
  if (Platform.Version >= 31) {
    // Request new permissions
    await requestPermission(BLUETOOTH_CONNECT);
    await requestPermission(BLUETOOTH_SCAN);
  } else {
    // Use legacy permissions
    return true;
  }
}
```

## Offline Capabilities Architecture

### Offline Storage Strategy

```
Print Request Failed
        │
        ▼
Store Receipt Data Locally
        │
        ▼
User Notification
        │
        ▼
Background Retry Logic
        │
        ▼
Successful Print → Remove from Storage
```

### Offline Data Management

```javascript
// Offline Receipt Storage
{
  key: `offline_receipt_${saleId}`,
  data: {
    receiptData: object,
    timestamp: Date,
    retryCount: number,
    lastError: string
  }
}
```

This architecture provides a robust, scalable foundation for thermal printing with clear separation of concerns and comprehensive error handling.
