# Multi-User Session Management System

## Overview

The Multi-User Session Management System enables PIN-based user switching within a single POS terminal session, allowing multiple staff members to use the same terminal without requiring full logout/login cycles. This system maintains session context, provides comprehensive audit trails, and ensures security through PIN authentication.

## Key Features

### 🔐 **PIN-Based Authentication**
- Secure 4-6 digit PIN system with bcrypt hashing
- Failed attempt tracking with automatic lockout
- PIN expiration and reset capabilities
- Rate limiting to prevent brute force attacks

### 👥 **User Context Switching**
- Seamless switching between staff members
- Maintains primary session while allowing temporary user switches
- User stack management for nested switches
- Automatic switch-back functionality with configurable timeouts

### 📊 **Session Context Management**
- Real-time session state tracking
- User permission inheritance during switches
- Terminal and location context preservation
- Session health monitoring and cleanup

### 🔍 **Comprehensive Audit Trail**
- All user switches logged with timestamps
- Security event tracking (PIN attempts, failures, etc.)
- IP address and user agent logging
- Searchable audit logs with filtering capabilities

## Architecture

### Database Schema

#### Enhanced `pos_staff` Table
```sql
ALTER TABLE pos_staff ADD COLUMN (
  pin VARCHAR(255) NULL,                    -- Hashed PIN
  pin_set_at DATETIME NULL,                -- When P<PERSON> was set
  pin_set_by VARCHAR(255) NULL,            -- Who set the PIN
  pin_attempts INT DEFAULT 0,              -- Failed attempt counter
  pin_locked_until DATETIME NULL,          -- Lockout expiration
  last_pin_used DATETIME NULL              -- Last successful PIN use
);
```

#### `pos_user_switches` Table
```sql
CREATE TABLE pos_user_switches (
  id VARCHAR(255) PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  from_staff_id VARCHAR(255),
  to_staff_id VARCHAR(255) NOT NULL,
  terminal_id VARCHAR(255),
  location_id VARCHAR(255),
  switch_reason VARCHAR(255),
  switched_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  switched_back_at DATETIME NULL,
  ip_address VARCHAR(45),
  user_agent TEXT
);
```

#### `pos_security_events` Table
```sql
CREATE TABLE pos_security_events (
  id VARCHAR(255) PRIMARY KEY,
  staff_id VARCHAR(255),
  event_type VARCHAR(100) NOT NULL,
  event_data JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Core Services

#### `MultiUserSessionService`
- **PIN Management**: Initialize, validate, and reset PINs
- **User Switching**: Handle context switches with validation
- **Session Context**: Maintain and retrieve session state
- **Security**: Log events and enforce security policies

#### `AuditService`
- **Event Logging**: Batch processing of security events
- **Audit Trail**: Searchable logs with filtering
- **Statistics**: Security and usage analytics
- **Cleanup**: Automated old log removal

## API Endpoints

### PIN Management

#### Initialize User PIN
```http
POST /api/pos/user-switching/initialize-pin
Authorization: Bearer <token>
Content-Type: application/json

{
  "staffId": "pos-001",
  "pin": "1234",
  "confirmPin": "1234"
}
```

#### Validate PIN
```http
POST /api/pos/user-switching/validate-pin
Authorization: Bearer <token>
Content-Type: application/json

{
  "staffId": "pos-001",
  "pin": "1234"
}
```

### User Switching

#### Switch User
```http
POST /api/pos/user-switching/switch-user
Authorization: Bearer <token>
Content-Type: application/json

{
  "targetStaffId": "pos-002",
  "pin": "5678",
  "reason": "manual_switch"
}
```

#### Switch Back
```http
POST /api/pos/user-switching/switch-back
Authorization: Bearer <token>
Content-Type: application/json

{
  "switchId": "switch-uuid"
}
```

### Session Management

#### Get Session Context
```http
GET /api/pos/user-switching/session-context
Authorization: Bearer <token>
```

#### Get Available Staff
```http
GET /api/pos/user-switching/available-staff
Authorization: Bearer <token>
```

### Audit and Monitoring

#### Get Switch History
```http
GET /api/pos/user-switching/switch-history?limit=50
Authorization: Bearer <token>
```

#### Get Session Statistics
```http
GET /api/pos/user-switching/session-stats
Authorization: Bearer <token>
```

#### Get Audit Trail
```http
GET /api/pos/user-switching/audit-trail?eventTypes=PIN_VALIDATED,USER_SWITCHED&limit=100
Authorization: Bearer <token>
```

## Security Features

### PIN Security
- **Hashing**: All PINs stored using bcrypt with 10 rounds
- **Lockout**: 3 failed attempts trigger 15-minute lockout
- **Rate Limiting**: 10 PIN attempts per 15 minutes per IP
- **Expiration**: Configurable PIN expiration policies

### Session Security
- **Context Isolation**: Each session maintains separate context
- **Permission Inheritance**: Switched users inherit their own permissions
- **Timeout**: Automatic switch-back after 30 minutes of inactivity
- **Validation**: Continuous session health checks

### Audit Security
- **Immutable Logs**: All events logged with tamper-evident timestamps
- **IP Tracking**: Source IP logged for all security events
- **User Agent**: Browser/device fingerprinting
- **Retention**: Configurable log retention policies

## Configuration

### Environment Variables
```env
# PIN Security
PIN_HASH_ROUNDS=10
MAX_PIN_ATTEMPTS=3
PIN_LOCKOUT_DURATION=900000  # 15 minutes in ms

# Session Management
AUTO_SWITCH_BACK_TIMEOUT=1800000  # 30 minutes in ms
SESSION_CONTEXT_CACHE_TTL=3600000  # 1 hour in ms

# Audit
AUDIT_BATCH_SIZE=50
AUDIT_FLUSH_INTERVAL=5000  # 5 seconds
AUDIT_RETENTION_DAYS=90
```

### Rate Limiting
```javascript
// PIN operations: 10 attempts per 15 minutes
const pinRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 10
});

// User switching: 20 attempts per 5 minutes
const switchRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000,
  max: 20
});
```

## Usage Examples

### Basic User Switching Flow

1. **Initialize PINs** (Admin only)
```javascript
// Set PIN for staff member
await fetch('/api/pos/user-switching/initialize-pin', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    staffId: 'pos-001',
    pin: '1234',
    confirmPin: '1234'
  })
});
```

2. **Switch User**
```javascript
// Switch to another user
const response = await fetch('/api/pos/user-switching/switch-user', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    targetStaffId: 'pos-002',
    pin: '5678',
    reason: 'customer_assistance'
  })
});

const { sessionContext, switchId } = response.data;
```

3. **Switch Back**
```javascript
// Return to previous user
await fetch('/api/pos/user-switching/switch-back', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    switchId: switchId
  })
});
```

### Session Context Usage

```javascript
// Get current session context
const response = await fetch('/api/pos/user-switching/session-context', {
  headers: { 'Authorization': `Bearer ${token}` }
});

const { sessionContext } = response.data;

console.log('Current User:', sessionContext.currentUser);
console.log('Primary User:', sessionContext.primaryUser);
console.log('Can Switch Back:', sessionContext.canSwitchBack);
console.log('User Stack:', sessionContext.userStack);
```

## Error Handling

### Common Error Scenarios

#### Invalid PIN
```json
{
  "success": false,
  "error": "Invalid PIN. 2 attempts remaining"
}
```

#### Account Locked
```json
{
  "success": false,
  "error": "Account locked. Try again in 14 minutes"
}
```

#### Permission Denied
```json
{
  "success": false,
  "error": "Insufficient permissions to set user PINs"
}
```

#### Rate Limited
```json
{
  "success": false,
  "error": "Too many PIN attempts. Please try again later."
}
```

## Monitoring and Analytics

### Key Metrics
- **Session Statistics**: Active sessions, switches per day
- **Security Events**: Failed PINs, lockouts, suspicious activity
- **User Activity**: Most active users, switch patterns
- **Performance**: Response times, cache hit rates

### Alerts
- Multiple failed PIN attempts from same IP
- Unusual switching patterns
- Account lockouts
- Session anomalies

## Best Practices

### Security
1. **Regular PIN Changes**: Enforce periodic PIN updates
2. **Strong PINs**: Encourage non-sequential, non-obvious PINs
3. **Monitor Logs**: Regular review of security events
4. **Access Control**: Limit PIN management to authorized personnel

### Performance
1. **Cache Management**: Monitor session context cache efficiency
2. **Batch Processing**: Ensure audit events are processed efficiently
3. **Database Optimization**: Regular cleanup of old logs
4. **Rate Limiting**: Adjust limits based on usage patterns

### Operational
1. **Staff Training**: Ensure staff understand switching procedures
2. **Backup Procedures**: Document PIN reset processes
3. **Incident Response**: Establish procedures for security events
4. **Regular Audits**: Periodic review of user access and permissions

## Troubleshooting

### Common Issues

#### Session Context Not Found
- Check token validity
- Verify session hasn't expired
- Ensure database connectivity

#### PIN Validation Failures
- Verify PIN is set for user
- Check for account lockout
- Confirm PIN format (4-6 digits)

#### Switch Back Failures
- Verify switch ID is valid
- Check if switch has already been reversed
- Ensure user stack is not empty

### Debug Commands

```sql
-- Check session context
SELECT * FROM pos_sessions WHERE id = 'session-id';

-- View user switches
SELECT * FROM pos_user_switches WHERE session_id = 'session-id';

-- Check security events
SELECT * FROM pos_security_events WHERE staff_id = 'staff-id' ORDER BY created_at DESC;

-- Verify PIN status
SELECT id, username, pin IS NOT NULL as has_pin, pin_attempts, pin_locked_until 
FROM pos_staff WHERE id = 'staff-id';
```
