#!/usr/bin/env node

/**
 * Test script to verify PIN validation fix
 * Tests that PIN attempts don't go negative
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";

async function testPinValidationFix() {
  console.log("🧪 Testing PIN validation fix...\n");

  try {
    // Step 1: Login as admin to get token
    console.log("1️⃣ Logging in as admin...");
    const loginResponse = await axios.post(`${BASE_URL}/api/pos/login`, {
      username: "admin1",
      password: "admin123",
    });

    if (!loginResponse.data.success) {
      throw new Error("Login failed");
    }

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log("✅ Login successful\n");

    // Step 2: Get a test user (cashier1)
    console.log("2️⃣ Getting test user info...");
    const staffResponse = await axios.get(
      `${BASE_URL}/api/pos/user-switching/available-staff`,
      { headers }
    );

    if (!staffResponse.data.success) {
      throw new Error("Failed to get staff list");
    }

    const testUser = staffResponse.data.data.find(
      (staff) => staff.username === "cashier1"
    );
    if (!testUser) {
      throw new Error("Test user cashier1 not found");
    }

    console.log(
      `✅ Found test user: ${testUser.name} (${testUser.username})\n`
    );

    // Step 3: Reset PIN attempts to 0 (directly in database for testing)
    console.log("3️⃣ Resetting PIN attempts to 0...");
    // We'll use a wrong PIN to test the logic

    // Step 4: Test PIN validation with wrong PIN multiple times
    console.log("4️⃣ Testing PIN validation with wrong PIN...\n");

    const wrongPin = "9999"; // Wrong PIN
    const attempts = [];

    for (let i = 1; i <= 5; i++) {
      try {
        console.log(`   Attempt ${i}: Testing with wrong PIN (${wrongPin})`);

        const response = await axios.post(
          `${BASE_URL}/api/pos/user-switching/validate-pin`,
          {
            staffId: testUser.id,
            pin: wrongPin,
          },
          { headers }
        );

        // This should not succeed
        console.log(`   ❌ Unexpected success on attempt ${i}`);
        attempts.push({ attempt: i, success: true, error: null });
      } catch (error) {
        const errorMessage = error.response?.data?.error || error.message;
        console.log(`   ❌ Attempt ${i} failed: ${errorMessage}`);
        attempts.push({ attempt: i, success: false, error: errorMessage });

        // Check if the error message contains negative attempts
        if (errorMessage.includes("-1 attempts remaining")) {
          console.log(
            `   🚨 BUG DETECTED: Negative attempts in error message!`
          );
        } else if (errorMessage.includes("attempts remaining")) {
          console.log(`   ✅ Attempts remaining message looks correct`);
        } else if (errorMessage.includes("Account locked")) {
          console.log(`   🔒 Account locked as expected`);
        }
      }
    }

    // Step 5: Summary
    console.log("\n📊 Test Summary:");
    attempts.forEach((attempt) => {
      const status = attempt.success ? "✅ SUCCESS" : "❌ FAILED";
      console.log(
        `   Attempt ${attempt.attempt}: ${status} - ${
          attempt.error || "No error"
        }`
      );
    });

    // Check for negative attempts bug
    const hasNegativeAttempts = attempts.some(
      (attempt) =>
        attempt.error && attempt.error.includes("-1 attempts remaining")
    );

    if (hasNegativeAttempts) {
      console.log(
        "\n🚨 BUG STILL EXISTS: Found negative attempts in error messages"
      );
    } else {
      console.log(
        "\n✅ FIX SUCCESSFUL: No negative attempts found in error messages"
      );
    }
  } catch (error) {
    console.error(
      "❌ Test failed:",
      error.response?.data?.error || error.message
    );
  }
}

// Run the test
testPinValidationFix()
  .then(() => {
    console.log("\n🏁 PIN validation test completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Test script failed:", error);
    process.exit(1);
  });
