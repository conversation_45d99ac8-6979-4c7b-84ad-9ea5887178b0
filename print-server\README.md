# T220MD Thermal Printer Server

This Node.js server enables web browsers to print to USB-connected T220MD thermal printers (80mm paper width, ESC/POS compatible).

## Features

- **USB Printing**: Direct printing to USB-connected T220MD printers
- **Network Printing**: Support for network-connected T220MD printers
- **WebSocket Support**: Real-time printing via WebSocket connections
- **ESC/POS Commands**: Full ESC/POS command support optimized for T220MD
- **Auto-Detection**: Automatic detection of thermal printer ports
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Web Integration**: CORS-enabled for web app integration

## Hardware Requirements

- **T220MD Thermal Printer** (80mm paper width)
- **USB Connection** or **Network Connection**
- **Computer** with Node.js 16+ installed

## Installation

### 1. Install Dependencies

```bash
cd print-server
npm install
```

### 2. Connect T220MD Printer

**USB Connection:**
- Connect T220MD printer to computer via USB cable
- Install printer drivers if required
- Printer should appear as a serial port (COM port on Windows, /dev/ttyUSB on Linux)

**Network Connection:**
- Connect T220MD to your network
- Note the printer's IP address
- Ensure port 9100 is accessible

### 3. Start Print Server

```bash
npm start
```

The server will start on `http://localhost:3001` and automatically detect your T220MD printer.

## API Endpoints

### Print Receipt
```http
POST /print
Content-Type: application/json

{
  "store": {
    "name": "Your Store Name",
    "address": "Store Address",
    "phone": "+254 700 000 000"
  },
  "orderNumber": "ORD-001",
  "orderDate": "2024-01-01T10:00:00Z",
  "items": [
    {
      "title": "Product Name",
      "variantTitle": "Variant",
      "quantity": 2,
      "price": "15.00"
    }
  ],
  "total": 30.00,
  "paymentMethod": "Cash",
  "customer": {
    "name": "Customer Name"
  }
}
```

### Test Print
```http
POST /test
```

### Get Printer Status
```http
GET /status
```

### List Available Printers
```http
GET /printers
```

### Health Check
```http
GET /health
```

## Configuration

### Web App Configuration

In your Dukalink POS web app, configure the T220MD service:

```typescript
import { T220MDPrintService } from './services/T220MDPrintService';

// Initialize with print server URL
await T220MDPrintService.init({
  printServerURL: 'http://localhost:3001',
  paperWidth: 80,
  charactersPerLine: 48,
  cutPaper: true,
  openDrawer: false
});

// Test printing
const result = await T220MDPrintService.testPrint();
console.log('Print result:', result);
```

### Network Printer Configuration

For network-connected T220MD printers:

```typescript
await T220MDPrintService.init({
  printerIP: '*************',
  printerPort: 9100,
  paperWidth: 80,
  charactersPerLine: 48
});
```

## Troubleshooting

### Printer Not Detected

1. **Check USB Connection**
   - Ensure T220MD is connected via USB
   - Install printer drivers if required
   - Check if printer appears in device manager

2. **Check Serial Ports**
   ```bash
   # List available ports
   curl http://localhost:3001/printers
   ```

3. **Manual Port Configuration**
   - Edit `server.js` to specify exact port path
   - Windows: `COM3`, `COM4`, etc.
   - Linux: `/dev/ttyUSB0`, `/dev/ttyUSB1`, etc.
   - macOS: `/dev/cu.usbserial-*`

### Print Server Connection Issues

1. **CORS Errors**
   - Ensure your web app URL is in the CORS whitelist
   - Update `cors` configuration in `server.js`

2. **Port Conflicts**
   - Change PORT variable in `server.js` if 3001 is in use
   - Update web app configuration accordingly

3. **Firewall Issues**
   - Allow Node.js through firewall
   - Ensure port 3001 is accessible

### Print Quality Issues

1. **Paper Width**
   - Ensure T220MD is configured for 80mm paper
   - Check `charactersPerLine` setting (should be 48 for 80mm)

2. **Character Encoding**
   - T220MD supports multiple character sets
   - Default is PC858_MULTILINGUAL

3. **Print Speed**
   - Adjust baud rate in printer configuration
   - Default is 9600 for T220MD

## Development

### Running in Development Mode

```bash
npm run dev
```

This uses `nodemon` for automatic restarts during development.

### Testing

```bash
# Test print server
curl -X POST http://localhost:3001/test

# Check printer status
curl http://localhost:3001/status

# List available printers
curl http://localhost:3001/printers
```

### Logging

The server provides detailed logging for:
- Printer connection status
- Print job execution
- Error diagnostics
- Port detection

## Production Deployment

### As a Service (Windows)

Use `node-windows` to run as a Windows service:

```bash
npm install -g node-windows
npm link node-windows
```

### As a Service (Linux)

Create a systemd service:

```bash
sudo nano /etc/systemd/system/t220md-print-server.service
```

```ini
[Unit]
Description=T220MD Print Server
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/print-server
ExecStart=/usr/bin/node server.js
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
sudo systemctl enable t220md-print-server
sudo systemctl start t220md-print-server
```

## License

MIT License - see LICENSE file for details.

## Support

For issues and support:
1. Check the troubleshooting section above
2. Review server logs for error messages
3. Test with the `/test` endpoint
4. Verify printer connection with `/status` endpoint
