import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ModernCard } from "@/components/ui/ModernCard";
import { useThemeColor } from "@/hooks/useThemeColor";
import { SalesAgentCreateButton } from "@/src/components/rbac";
import { SalesAgentActionButtons } from "@/src/components/rbac/ActionButtons";
import { SalesAgentManagementFAB } from "@/src/components/rbac/PermissionAwareFAB";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useRBAC } from "@/src/hooks/useRBAC";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect, useRouter } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  Alert,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface SalesAgent {
  id: string;
  name: string;
  email: string;
  phone: string;
  commissionRate: number;
  totalSales: number;
  customerCount: number;
  active: boolean;
  territory: string;
  region?: string;
  joinDate?: string;
  totalCommission?: number;
}

export default function SalesAgentListScreen() {
  const router = useRouter();
  const { setCurrentTitle } = useNavigation();
  const { canManageStaff } = useRBAC();

  const [salesAgents, setSalesAgents] = useState<SalesAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Theme colors
  const backgroundColor = useThemeColor({}, "background");
  const surfaceColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = "#4CAF50";
  const warningColor = "#FF9500";
  const errorColor = "#FF3B30";

  useEffect(() => {
    setCurrentTitle("Sales Agent Management");
    loadSalesAgents();
  }, [setCurrentTitle]);

  // Refresh data when screen comes into focus (after creating/editing sales agents)
  useFocusEffect(
    useCallback(() => {
      loadSalesAgents();
    }, [])
  );

  const loadSalesAgents = async () => {
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getSalesAgents();

      if (response.success && response.data) {
        setSalesAgents(response.data.salesAgents || []);
      } else {
        Alert.alert("Error", response.error || "Failed to load sales agents");
      }
    } catch (error) {
      console.error("Load sales agents error:", error);
      Alert.alert("Error", "Failed to load sales agents");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadSalesAgents();
  };

  const handleAgentPress = (agent: SalesAgent) => {
    router.push({
      pathname: "/sales-agent-details",
      params: { agentId: agent.id },
    });
  };

  const handleCreateAgent = () => {
    router.push("/sales-agent-create");
  };

  const handleEditAgent = (agent: SalesAgent) => {
    // TODO: Navigate to sales agent edit screen
    Alert.alert(
      "Edit Agent",
      `Edit functionality for ${agent.name} will be implemented soon.`
    );
  };

  const handleDeleteAgent = async (agent: SalesAgent) => {
    try {
      // TODO: Implement delete API call
      Alert.alert(
        "Delete Agent",
        `Delete functionality for ${agent.name} will be implemented soon.`
      );
    } catch (error) {
      Alert.alert("Error", "Failed to delete sales agent");
    }
  };

  const formatCurrency = (amount: number) => {
    return `KSh ${amount.toLocaleString("en-KE", {
      minimumFractionDigits: 0,
    })}`;
  };

  const formatJoinDate = (joinDate?: string) => {
    if (!joinDate) return "Unknown";
    return new Date(joinDate).toLocaleDateString();
  };

  const filteredAgents = salesAgents.filter(
    (agent) =>
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.territory.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderAgentCard = (agent: SalesAgent) => (
    <TouchableOpacity
      key={agent.id}
      onPress={() => handleAgentPress(agent)}
      style={styles.cardTouchable}
    >
      <ModernCard style={[styles.agentCard, { backgroundColor: surfaceColor }]}>
        <View style={styles.agentHeader}>
          <View style={[styles.avatar, { backgroundColor: primaryColor }]}>
            <Text style={styles.avatarText}>
              {agent.name
                .split(" ")
                .map((n) => n.charAt(0))
                .join("")
                .slice(0, 2)}
            </Text>
          </View>
          <View style={styles.agentInfo}>
            <Text style={[styles.agentName, { color: textColor }]}>
              {agent.name}
            </Text>
            <Text style={[styles.agentEmail, { color: textSecondary }]}>
              {agent.email}
            </Text>
            {agent.phone && (
              <Text style={[styles.agentPhone, { color: textSecondary }]}>
                {agent.phone}
              </Text>
            )}
          </View>
          <View style={styles.agentStatus}>
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: agent.active ? successColor : errorColor },
              ]}
            />
            <Text
              style={[
                styles.statusText,
                { color: agent.active ? successColor : errorColor },
              ]}
            >
              {agent.active ? "Active" : "Inactive"}
            </Text>
          </View>
        </View>

        <View style={styles.agentDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="location" size={16} color={textSecondary} />
            <Text style={[styles.detailText, { color: textSecondary }]}>
              {agent.territory} {agent.region && `• ${agent.region}`}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="calendar" size={16} color={textSecondary} />
            <Text style={[styles.detailText, { color: textSecondary }]}>
              Joined: {formatJoinDate(agent.joinDate)}
            </Text>
          </View>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: primaryColor }]}>
              {agent.commissionRate}%
            </Text>
            <Text style={[styles.statLabel, { color: textSecondary }]}>
              Commission
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: textColor }]}>
              {agent.customerCount}
            </Text>
            <Text style={[styles.statLabel, { color: textSecondary }]}>
              Customers
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: successColor }]}>
              {formatCurrency(agent.totalSales)}
            </Text>
            <Text style={[styles.statLabel, { color: textSecondary }]}>
              Total Sales
            </Text>
          </View>
        </View>

        <View style={styles.cardFooter}>
          <SalesAgentActionButtons
            onEdit={() => handleEditAgent(agent)}
            onDelete={() => handleDeleteAgent(agent)}
            onViewDetails={() => handleAgentPress(agent)}
            agentName={agent.name}
          />
        </View>
      </ModernCard>
    </TouchableOpacity>
  );

  const styles = createStyles(
    backgroundColor,
    surfaceColor,
    textColor,
    textSecondary,
    primaryColor
  );

  return (
    <ScreenWrapper title="Sales Agent Management" showBackButton>
      <View style={styles.container}>
        {/* Header with Create Button */}
        <View style={styles.header}>
          <View style={styles.headerInfo}>
            <Text style={[styles.headerTitle, { color: textColor }]}>
              Sales Agents
            </Text>
            <Text style={[styles.headerSubtitle, { color: textSecondary }]}>
              {salesAgents.length} total agents •{" "}
              {salesAgents.filter((a) => a.active).length} active
            </Text>
          </View>
          <SalesAgentCreateButton>
            <TouchableOpacity
              style={[styles.createButton, { backgroundColor: primaryColor }]}
              onPress={handleCreateAgent}
            >
              <Ionicons name="add" size={24} color="white" />
            </TouchableOpacity>
          </SalesAgentCreateButton>
        </View>

        {/* Search */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <TextInput
              style={[
                styles.searchInput,
                {
                  backgroundColor: surfaceColor,
                  borderColor: textSecondary + "40",
                  color: textColor,
                },
              ]}
              placeholder="Search agents by name, email, or territory..."
              placeholderTextColor={textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <Ionicons
              name="search"
              size={20}
              color={textSecondary}
              style={styles.searchIcon}
            />
          </View>
        </View>

        {/* Sales Agents List */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={primaryColor}
            />
          }
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={[styles.loadingText, { color: textSecondary }]}>
                Loading sales agents...
              </Text>
            </View>
          ) : filteredAgents.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="person-add" size={64} color={textSecondary} />
              <Text style={[styles.emptyText, { color: textSecondary }]}>
                {searchQuery ? "No agents found" : "No sales agents found"}
              </Text>
              <Text style={[styles.emptySubtext, { color: textSecondary }]}>
                {searchQuery
                  ? "Try a different search term"
                  : "Create your first sales agent to get started"}
              </Text>
              {!searchQuery && (
                <SalesAgentCreateButton>
                  <TouchableOpacity
                    style={[styles.emptyButton, { borderColor: primaryColor }]}
                    onPress={handleCreateAgent}
                  >
                    <Text
                      style={[styles.emptyButtonText, { color: primaryColor }]}
                    >
                      Create First Sales Agent
                    </Text>
                  </TouchableOpacity>
                </SalesAgentCreateButton>
              )}
            </View>
          ) : (
            <View style={styles.agentsList}>
              {filteredAgents.map(renderAgentCard)}
            </View>
          )}
        </ScrollView>

        {/* Floating Action Button */}
        <SalesAgentManagementFAB onPress={handleCreateAgent} />
      </View>
    </ScreenWrapper>
  );
}

const createStyles = (
  backgroundColor: string,
  surfaceColor: string,
  textColor: string,
  textSecondary: string,
  primaryColor: string
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: textSecondary + "20",
    },
    headerInfo: {
      flex: 1,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: "bold",
      marginBottom: 4,
    },
    headerSubtitle: {
      fontSize: 14,
    },
    createButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: "center",
      alignItems: "center",
    },
    searchContainer: {
      paddingHorizontal: 20,
      paddingVertical: 16,
    },
    searchInputContainer: {
      position: "relative",
    },
    searchInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 12,
      paddingRight: 40,
      fontSize: 16,
    },
    searchIcon: {
      position: "absolute",
      right: 16,
      top: "50%",
      marginTop: -10,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 100, // Space for FAB + extra padding
    },
    agentsList: {
      padding: 20,
    },
    cardTouchable: {
      marginBottom: 16,
    },
    agentCard: {
      padding: 16,
    },
    agentHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
    },
    avatarText: {
      color: "white",
      fontSize: 16,
      fontWeight: "bold",
    },
    agentInfo: {
      flex: 1,
    },
    agentName: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 2,
    },
    agentEmail: {
      fontSize: 14,
      marginBottom: 2,
    },
    agentPhone: {
      fontSize: 12,
    },
    agentStatus: {
      alignItems: "flex-end",
    },
    statusIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginBottom: 4,
    },
    statusText: {
      fontSize: 12,
      fontWeight: "500",
    },
    agentDetails: {
      marginBottom: 12,
    },
    detailRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    detailText: {
      fontSize: 14,
      marginLeft: 8,
    },
    statsContainer: {
      flexDirection: "row",
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: textSecondary + "20",
      marginBottom: 12,
    },
    statItem: {
      flex: 1,
      alignItems: "center",
    },
    statValue: {
      fontSize: 16,
      fontWeight: "bold",
      marginBottom: 2,
    },
    statLabel: {
      fontSize: 12,
    },
    cardFooter: {
      alignItems: "flex-end",
      justifyContent: "flex-end",
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: textSecondary + "10",
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: 40,
    },
    loadingText: {
      fontSize: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: 40,
      paddingHorizontal: 20,
    },
    emptyText: {
      fontSize: 16,
      marginTop: 16,
      marginBottom: 8,
      textAlign: "center",
    },
    emptySubtext: {
      fontSize: 14,
      textAlign: "center",
      marginBottom: 24,
    },
    emptyButton: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderWidth: 2,
      borderRadius: 8,
    },
    emptyButtonText: {
      fontSize: 16,
      fontWeight: "600",
    },
  });
