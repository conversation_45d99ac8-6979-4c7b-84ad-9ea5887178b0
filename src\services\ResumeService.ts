/**
 * Resume Service
 * 
 * Handles session restoration and ticket recovery functionality.
 * Provides seamless resume experience after app restart, login,
 * or network reconnection.
 */

import { store } from '@/src/store';
import { 
  getRecoveryTickets,
  loadTickets,
  setActiveTicket,
  createTicket 
} from '@/src/store/thunks/ticketThunks';
import { 
  selectAllTickets,
  selectActiveTicketId 
} from '@/src/store/slices/ticketSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ResumeConfig {
  enableAutoResume: boolean;
  maxRecoveryAge: number; // in hours
  showRecoveryPrompt: boolean;
  autoSelectLastActive: boolean;
}

export interface RecoverySession {
  userId: string;
  lastActiveTicketId: string | null;
  sessionTimestamp: Date;
  ticketCount: number;
  hasUnsavedChanges: boolean;
}

export interface ResumeResult {
  success: boolean;
  recoveredTickets: any[];
  restoredActiveTicket: string | null;
  message: string;
  requiresUserAction: boolean;
}

class ResumeService {
  private config: ResumeConfig;
  private storageKeys = {
    lastSession: 'dukalink_last_session',
    activeTicket: 'dukalink_active_ticket',
    resumePreferences: 'dukalink_resume_preferences',
  };

  constructor(config: Partial<ResumeConfig> = {}) {
    this.config = {
      enableAutoResume: true,
      maxRecoveryAge: 24, // 24 hours
      showRecoveryPrompt: true,
      autoSelectLastActive: true,
      ...config,
    };
  }

  /**
   * Initialize resume service and attempt automatic recovery
   */
  async initialize(): Promise<ResumeResult> {
    try {
      if (!this.config.enableAutoResume) {
        return {
          success: true,
          recoveredTickets: [],
          restoredActiveTicket: null,
          message: 'Auto-resume disabled',
          requiresUserAction: false,
        };
      }

      // Load last session data
      const lastSession = await this.getLastSession();
      
      if (!lastSession) {
        return {
          success: true,
          recoveredTickets: [],
          restoredActiveTicket: null,
          message: 'No previous session found',
          requiresUserAction: false,
        };
      }

      // Check if session is too old
      const sessionAge = Date.now() - new Date(lastSession.sessionTimestamp).getTime();
      const maxAge = this.config.maxRecoveryAge * 60 * 60 * 1000; // Convert to milliseconds

      if (sessionAge > maxAge) {
        await this.clearSession();
        return {
          success: true,
          recoveredTickets: [],
          restoredActiveTicket: null,
          message: 'Session too old, cleared',
          requiresUserAction: false,
        };
      }

      // Attempt recovery
      return await this.performRecovery(lastSession);

    } catch (error) {
      console.error('Resume service initialization failed:', error);
      return {
        success: false,
        recoveredTickets: [],
        restoredActiveTicket: null,
        message: `Recovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        requiresUserAction: false,
      };
    }
  }

  /**
   * Perform ticket recovery from server
   */
  private async performRecovery(lastSession: RecoverySession): Promise<ResumeResult> {
    try {
      // Get recovery tickets from server
      const recoveryResult = await store.dispatch(getRecoveryTickets()).unwrap();
      
      if (!recoveryResult || recoveryResult.length === 0) {
        await this.clearSession();
        return {
          success: true,
          recoveredTickets: [],
          restoredActiveTicket: null,
          message: 'No recoverable tickets found',
          requiresUserAction: false,
        };
      }

      // Filter tickets that are worth recovering
      const recoverableTickets = recoveryResult.filter(ticket => 
        this.isTicketRecoverable(ticket, lastSession)
      );

      if (recoverableTickets.length === 0) {
        await this.clearSession();
        return {
          success: true,
          recoveredTickets: [],
          restoredActiveTicket: null,
          message: 'No tickets worth recovering',
          requiresUserAction: false,
        };
      }

      // Load all tickets to merge with recovered ones
      await store.dispatch(loadTickets()).unwrap();

      let restoredActiveTicket: string | null = null;

      // Restore last active ticket if configured and available
      if (this.config.autoSelectLastActive && lastSession.lastActiveTicketId) {
        const lastActiveTicket = recoverableTickets.find(
          t => t.id === lastSession.lastActiveTicketId
        );
        
        if (lastActiveTicket) {
          await store.dispatch(setActiveTicket(lastActiveTicket.id));
          restoredActiveTicket = lastActiveTicket.id;
        }
      }

      // Update session
      await this.updateSession();

      return {
        success: true,
        recoveredTickets: recoverableTickets,
        restoredActiveTicket,
        message: `Recovered ${recoverableTickets.length} tickets`,
        requiresUserAction: this.config.showRecoveryPrompt && recoverableTickets.length > 1,
      };

    } catch (error) {
      console.error('Recovery failed:', error);
      return {
        success: false,
        recoveredTickets: [],
        restoredActiveTicket: null,
        message: `Recovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        requiresUserAction: false,
      };
    }
  }

  /**
   * Check if a ticket is worth recovering
   */
  private isTicketRecoverable(ticket: any, lastSession: RecoverySession): boolean {
    // Must have items or be recently active
    if (!ticket.items || ticket.items.length === 0) {
      return false;
    }

    // Check if ticket was recently modified
    const ticketAge = Date.now() - new Date(ticket.updatedAt || ticket.createdAt).getTime();
    const maxAge = this.config.maxRecoveryAge * 60 * 60 * 1000;

    if (ticketAge > maxAge) {
      return false;
    }

    // Check if ticket has meaningful content
    const hasValue = ticket.total > 0 || ticket.items.some((item: any) => item.quantity > 0);
    
    return hasValue;
  }

  /**
   * Save current session state
   */
  async saveSession(): Promise<void> {
    try {
      const state = store.getState();
      const tickets = state.tickets.tickets;
      const activeTicketId = state.tickets.activeTicketId;
      const userId = state.auth.user?.id;

      if (!userId) {
        return; // No user logged in
      }

      const session: RecoverySession = {
        userId,
        lastActiveTicketId: activeTicketId,
        sessionTimestamp: new Date(),
        ticketCount: tickets.length,
        hasUnsavedChanges: tickets.some(t => t.isDirty),
      };

      await AsyncStorage.setItem(
        this.storageKeys.lastSession,
        JSON.stringify(session)
      );

      // Save active ticket separately for quick access
      if (activeTicketId) {
        await AsyncStorage.setItem(
          this.storageKeys.activeTicket,
          activeTicketId
        );
      }

    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }

  /**
   * Update current session
   */
  async updateSession(): Promise<void> {
    await this.saveSession();
  }

  /**
   * Get last saved session
   */
  private async getLastSession(): Promise<RecoverySession | null> {
    try {
      const sessionData = await AsyncStorage.getItem(this.storageKeys.lastSession);
      
      if (!sessionData) {
        return null;
      }

      const session = JSON.parse(sessionData) as RecoverySession;
      
      // Validate session data
      if (!session.userId || !session.sessionTimestamp) {
        return null;
      }

      return session;

    } catch (error) {
      console.error('Failed to get last session:', error);
      return null;
    }
  }

  /**
   * Clear session data
   */
  async clearSession(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        this.storageKeys.lastSession,
        this.storageKeys.activeTicket,
      ]);
    } catch (error) {
      console.error('Failed to clear session:', error);
    }
  }

  /**
   * Get last active ticket ID
   */
  async getLastActiveTicketId(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.storageKeys.activeTicket);
    } catch (error) {
      console.error('Failed to get last active ticket:', error);
      return null;
    }
  }

  /**
   * Manual recovery trigger
   */
  async triggerManualRecovery(): Promise<ResumeResult> {
    try {
      const recoveryResult = await store.dispatch(getRecoveryTickets()).unwrap();
      
      return {
        success: true,
        recoveredTickets: recoveryResult || [],
        restoredActiveTicket: null,
        message: `Found ${recoveryResult?.length || 0} recoverable tickets`,
        requiresUserAction: true,
      };

    } catch (error) {
      return {
        success: false,
        recoveredTickets: [],
        restoredActiveTicket: null,
        message: `Manual recovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        requiresUserAction: false,
      };
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ResumeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): ResumeConfig {
    return { ...this.config };
  }

  /**
   * Check if resume is available
   */
  async isResumeAvailable(): Promise<boolean> {
    const lastSession = await this.getLastSession();
    
    if (!lastSession) {
      return false;
    }

    const sessionAge = Date.now() - new Date(lastSession.sessionTimestamp).getTime();
    const maxAge = this.config.maxRecoveryAge * 60 * 60 * 1000;

    return sessionAge <= maxAge && lastSession.hasUnsavedChanges;
  }

  /**
   * Get resume statistics
   */
  async getResumeStats(): Promise<{
    hasSession: boolean;
    sessionAge: number;
    ticketCount: number;
    hasUnsavedChanges: boolean;
  }> {
    const lastSession = await this.getLastSession();
    
    if (!lastSession) {
      return {
        hasSession: false,
        sessionAge: 0,
        ticketCount: 0,
        hasUnsavedChanges: false,
      };
    }

    const sessionAge = Date.now() - new Date(lastSession.sessionTimestamp).getTime();

    return {
      hasSession: true,
      sessionAge: Math.floor(sessionAge / (1000 * 60)), // in minutes
      ticketCount: lastSession.ticketCount,
      hasUnsavedChanges: lastSession.hasUnsavedChanges,
    };
  }
}

// Export singleton instance
export const resumeService = new ResumeService();
export default ResumeService;
