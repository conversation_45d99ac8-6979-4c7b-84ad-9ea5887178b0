import { BluetoothManager } from 'react-native-bluetooth-escpos-printer';

export class BluetoothDebugUtils {
  /**
   * Debug available methods in BluetoothManager
   */
  static debugBluetoothManager() {
    console.log('=== BluetoothManager Debug ===');
    console.log('Available methods:');
    
    const methods = Object.getOwnPropertyNames(BluetoothManager);
    methods.forEach(method => {
      console.log(`- ${method}: ${typeof BluetoothManager[method as keyof typeof BluetoothManager]}`);
    });

    // Check for common methods
    const commonMethods = [
      'isBluetoothEnabled',
      'enableBluetooth',
      'scanDevices',
      'getBondedDevices',
      'getPairedDevices',
      'connect',
      'disconnect',
      'getConnectedDevices',
    ];

    console.log('\nCommon method availability:');
    commonMethods.forEach(method => {
      const exists = method in BluetoothManager;
      const type = exists ? typeof BluetoothManager[method as keyof typeof BluetoothManager] : 'undefined';
      console.log(`- ${method}: ${exists ? '✅' : '❌'} (${type})`);
    });

    console.log('=== End Debug ===');
  }

  /**
   * Test Bluetooth scanning with different methods
   */
  static async testBluetoothScanning() {
    console.log('=== Testing Bluetooth Scanning ===');

    try {
      // Test 1: Check if Bluetooth is enabled
      console.log('1. Checking if Bluetooth is enabled...');
      const isEnabled = await BluetoothManager.isBluetoothEnabled();
      console.log(`   Bluetooth enabled: ${isEnabled}`);

      if (!isEnabled) {
        console.log('   Bluetooth is disabled, cannot proceed with scanning');
        return [];
      }

      // Test 2: Try scanDevices method
      console.log('2. Testing scanDevices method...');
      try {
        const scanResult = await BluetoothManager.scanDevices();
        console.log('   scanDevices result:', scanResult);
        console.log('   scanDevices type:', typeof scanResult);
        
        if (scanResult && typeof scanResult === 'object') {
          console.log('   scanResult keys:', Object.keys(scanResult));
          if (scanResult.found) {
            console.log('   found devices:', scanResult.found);
          }
        }
      } catch (scanError) {
        console.log('   scanDevices failed:', scanError);
      }

      // Test 3: Try getBondedDevices if it exists
      console.log('3. Testing getBondedDevices method...');
      if ('getBondedDevices' in BluetoothManager) {
        try {
          const bondedResult = await (BluetoothManager as any).getBondedDevices();
          console.log('   getBondedDevices result:', bondedResult);
        } catch (bondedError) {
          console.log('   getBondedDevices failed:', bondedError);
        }
      } else {
        console.log('   getBondedDevices method not available');
      }

      // Test 4: Try getPairedDevices if it exists
      console.log('4. Testing getPairedDevices method...');
      if ('getPairedDevices' in BluetoothManager) {
        try {
          const pairedResult = await (BluetoothManager as any).getPairedDevices();
          console.log('   getPairedDevices result:', pairedResult);
        } catch (pairedError) {
          console.log('   getPairedDevices failed:', pairedError);
        }
      } else {
        console.log('   getPairedDevices method not available');
      }

      // Test 5: Try getConnectedDevices if it exists
      console.log('5. Testing getConnectedDevices method...');
      if ('getConnectedDevices' in BluetoothManager) {
        try {
          const connectedResult = await (BluetoothManager as any).getConnectedDevices();
          console.log('   getConnectedDevices result:', connectedResult);
        } catch (connectedError) {
          console.log('   getConnectedDevices failed:', connectedError);
        }
      } else {
        console.log('   getConnectedDevices method not available');
      }

    } catch (error) {
      console.log('Testing failed:', error);
    }

    console.log('=== End Testing ===');
  }

  /**
   * Simple device scan that tries all available methods
   */
  static async simpleDeviceScan(): Promise<any[]> {
    console.log('=== Simple Device Scan ===');
    
    try {
      // Check Bluetooth state
      const isEnabled = await BluetoothManager.isBluetoothEnabled();
      if (!isEnabled) {
        console.log('Bluetooth is not enabled');
        return [];
      }

      // Try the basic scan method
      console.log('Trying basic scanDevices...');
      const scanResult = await BluetoothManager.scanDevices();
      console.log('Scan result:', scanResult);

      // Handle different response formats
      if (scanResult && scanResult.found && Array.isArray(scanResult.found)) {
        console.log(`Found ${scanResult.found.length} devices via scanResult.found`);
        return scanResult.found;
      } else if (Array.isArray(scanResult)) {
        console.log(`Found ${scanResult.length} devices via direct array`);
        return scanResult;
      } else {
        console.log('No devices found or unexpected format');
        return [];
      }

    } catch (error) {
      console.log('Simple scan failed:', error);
      return [];
    }
  }
}

export default BluetoothDebugUtils;
