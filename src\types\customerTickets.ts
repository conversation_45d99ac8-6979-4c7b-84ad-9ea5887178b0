/**
 * Customer Ticket Types
 *
 * TypeScript interfaces for customer ticket lookup and resume functionality.
 */

import { Customer, CartItem } from "./shopify";

export interface CustomerTicketItem {
  id: string;
  ticket_id: string;
  variant_id: string;
  product_id: string;
  title: string;
  variant_title?: string;
  sku?: string;
  price: number;
  quantity: number;
  line_total: number;
  discount_amount?: number;
  discount_type?: "percentage" | "fixed_amount";
  notes?: string;
  inventory_quantity: number;
  created_at: string;
  updated_at: string;
}

export interface CustomerTicket {
  id: string;
  staff_id: string;
  terminal_id?: string;
  location_id?: string;
  name: string;
  customer_id: string;
  sales_agent_id?: string;
  subtotal: number;
  tax: number;
  total: number;
  discount_total: number;
  note?: string;
  status:
    | "active"
    | "paused"
    | "completed"
    | "cancelled"
    | "expired"
    | "archived";
  expires_at?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  last_auto_save?: string;
  // Calculated fields
  item_count: number;
  calculated_total: number;
  // Related data
  items: CustomerTicketItem[];
}

export interface CustomerTicketsResponse {
  tickets: CustomerTicket[];
  count: number;
}

export interface CustomerTicketResumeOptions {
  customer: Customer;
  existingTickets: CustomerTicket[];
  currentTicketId?: string;
  currentCartItems: CartItem[];
}

export interface TicketResumeAction {
  type: "resume" | "merge" | "continue";
  ticketId?: string;
  customer: Customer;
}

export interface TicketMergeConflict {
  variantId: string;
  title: string;
  currentQuantity: number;
  ticketQuantity: number;
  suggestedQuantity: number;
}

export interface TicketMergeResult {
  success: boolean;
  conflicts?: TicketMergeConflict[];
  mergedItems: CartItem[];
  error?: string;
}
