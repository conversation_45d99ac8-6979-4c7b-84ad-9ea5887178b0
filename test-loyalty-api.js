/**
 * Test Loyalty API Endpoints
 *
 * Tests the loyalty API endpoints to verify they're working correctly
 */

const axios = require("axios");

const BASE_URL = "http://localhost:3020";
const TEST_CUSTOMER_ID = "8095960301705"; // Customer with existing loyalty data

// Authentication credentials from database setup
const AUTH_CREDENTIALS = {
  username: "manager1",
  password: "manager123",
};

let authToken = null;

const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use((config) => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

/**
 * Authenticate with POS credentials and get JWT token
 */
async function authenticate() {
  console.log("🔐 Authenticating with POS credentials...");

  try {
    const response = await apiClient.post("/api/pos/login", {
      username: AUTH_CREDENTIALS.username,
      password: AUTH_CREDENTIALS.password,
    });

    if (response.data && response.data.data && response.data.data.token) {
      authToken = response.data.data.token;
      console.log("   ✅ Authentication successful");
      console.log(
        `   👤 User: ${response.data.data.user?.name} (${response.data.data.user?.role})`
      );
      console.log(`   🔑 Token: ${authToken.substring(0, 20)}...`);
      return true;
    } else {
      console.log("   ❌ Authentication failed: No token received");
      console.log("   📋 Response:", JSON.stringify(response.data, null, 2));
      return false;
    }
  } catch (error) {
    console.log(
      "   ❌ Authentication failed:",
      error.response?.data?.error || error.message
    );
    return false;
  }
}

async function testLoyaltyEndpoints() {
  console.log("🧪 Testing Loyalty API Endpoints...\n");

  try {
    // Test 1: Health check
    console.log("\n1️⃣ Testing server health...");
    try {
      const healthResponse = await apiClient.get("/api/health");
      console.log("   ✅ Server is healthy:", healthResponse.data);
    } catch (error) {
      console.log("   ⚠️ Health check failed, but continuing...");
    }

    // Test 2: Get customer summary
    console.log(
      "\n2️⃣ Testing GET /api/loyalty/customers/:customerId/summary..."
    );
    try {
      const summaryResponse = await apiClient.get(
        `/api/loyalty/customers/${TEST_CUSTOMER_ID}/summary`
      );
      console.log("   ✅ Customer summary:", summaryResponse.data);
    } catch (error) {
      console.log(
        "   ❌ Customer summary failed:",
        error.response?.data || error.message
      );
    }

    // Test 3: Calculate loyalty discount
    console.log(
      "\n3️⃣ Testing POST /api/loyalty/customers/:customerId/discounts/calculate..."
    );
    try {
      const discountResponse = await apiClient.post(
        `/api/loyalty/customers/${TEST_CUSTOMER_ID}/discounts/calculate`,
        {
          orderTotal: 1000,
        }
      );
      console.log("   ✅ Discount calculation:", discountResponse.data);
    } catch (error) {
      console.log(
        "   ❌ Discount calculation failed:",
        error.response?.data || error.message
      );
    }

    // Test 4: Add loyalty points (simulate order completion)
    console.log(
      "\n4️⃣ Testing POST /api/loyalty/customers/:customerId/points/add..."
    );
    try {
      const addPointsResponse = await apiClient.post(
        `/api/loyalty/customers/${TEST_CUSTOMER_ID}/points/add`,
        {
          orderTotal: 500,
          orderId: `TEST_ORDER_${Date.now()}`,
          salesAgentId: "agent-001", // Use valid sales agent ID
        }
      );
      console.log("   ✅ Points added:", addPointsResponse.data);
    } catch (error) {
      console.log(
        "   ❌ Add points failed:",
        error.response?.data || error.message
      );

      // Log more details for debugging
      if (error.response) {
        console.log("   📋 Status:", error.response.status);
        console.log(
          "   📋 Response:",
          JSON.stringify(error.response.data, null, 2)
        );
      }
    }

    // Test 5: Get loyalty config
    console.log("\n5️⃣ Testing GET /api/loyalty/config...");
    try {
      const configResponse = await apiClient.get("/api/loyalty/config");
      console.log("   ✅ Loyalty config:", configResponse.data);
    } catch (error) {
      console.log(
        "   ❌ Loyalty config failed:",
        error.response?.data || error.message
      );
    }

    // Test 6: Test without authentication (should fail)
    console.log("\n6️⃣ Testing without authentication...");
    try {
      const noAuthClient = axios.create({
        baseURL: BASE_URL,
        timeout: 5000,
      });
      const noAuthResponse = await noAuthClient.get(
        `/api/loyalty/customers/${TEST_CUSTOMER_ID}/summary`
      );
      console.log("   ⚠️ No auth succeeded (unexpected):", noAuthResponse.data);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log("   ✅ Authentication required (expected)");
      } else {
        console.log(
          "   ❌ Unexpected error:",
          error.response?.data || error.message
        );
      }
    }
  } catch (error) {
    console.error("💥 Test suite failed:", error.message);
  }
}

// Test with different customer IDs
async function testMultipleCustomers() {
  console.log("\n🔄 Testing with multiple customers...\n");

  const customerIds = [
    "8095960301705", // Existing customer with points
    "7988940800137", // Customer with 0 points
    "7988940013705", // Another customer
    "NONEXISTENT123", // Non-existent customer
  ];

  for (const customerId of customerIds) {
    console.log(`📋 Testing customer ${customerId}:`);
    try {
      const response = await apiClient.get(
        `/api/loyalty/customers/${customerId}/summary`
      );
      const loyalty = response.data.data; // Fix response structure
      console.log(
        `   Points: ${loyalty.loyaltyPoints}, Tier: ${loyalty.tier}, Purchases: KSh ${loyalty.totalPurchases}`
      );
    } catch (error) {
      console.log(
        `   ❌ Failed: ${error.response?.data?.error || error.message}`
      );
    }
  }
}

// Test discount calculations with different amounts
async function testDiscountCalculations() {
  console.log("\n💰 Testing discount calculations...\n");

  const testAmounts = [100, 500, 1000, 2500, 5000];

  for (const amount of testAmounts) {
    console.log(`💵 Testing discount for KSh ${amount}:`);
    try {
      const response = await apiClient.post(
        `/api/loyalty/customers/${TEST_CUSTOMER_ID}/discounts/calculate`,
        {
          orderTotal: amount,
        }
      );

      const discounts = response.data.data; // Fix response structure
      console.log(
        `   Tier discount: KSh ${discounts.tier.amount} (${discounts.tier.percentage}%)`
      );
      console.log(`   Points available: ${discounts.points.availablePoints}`);
      console.log(
        `   Max points discount: KSh ${discounts.points.maxDiscount}`
      );
      console.log(
        `   Total possible discount: KSh ${
          discounts.tier.amount + discounts.points.maxDiscount
        }`
      );
    } catch (error) {
      console.log(
        `   ❌ Failed: ${error.response?.data?.error || error.message}`
      );
    }
  }
}

// Run all tests
async function runAllTests() {
  console.log("🚀 Starting Loyalty API Test Suite...\n");

  // Authenticate once for all tests
  const authSuccess = await authenticate();
  if (!authSuccess) {
    console.log("❌ Cannot proceed without authentication");
    return;
  }

  await testLoyaltyEndpoints();
  await testMultipleCustomers();
  await testDiscountCalculations();

  console.log("\n✅ All tests completed!");
}

// Execute tests
runAllTests().catch(console.error);
