/**
 * Theme Consistency Checker
 * 
 * Utility to validate theme consistency across ticket components
 * and ensure proper design system compliance
 */

import { Theme } from '@/src/contexts/ThemeContext';
import { ViewStyle, TextStyle } from 'react-native';

// =====================================================
// VALIDATION TYPES
// =====================================================

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface StyleValidationOptions {
  checkColors: boolean;
  checkSpacing: boolean;
  checkTypography: boolean;
  checkAccessibility: boolean;
}

// =====================================================
// COLOR VALIDATION
// =====================================================

export const validateColors = (theme: Theme): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Check required colors exist
  const requiredColors = [
    'primary', 'secondary', 'background', 'surface', 'text', 
    'textSecondary', 'border', 'success', 'warning', 'error'
  ];

  requiredColors.forEach(color => {
    if (!(color in theme.colors)) {
      errors.push(`Missing required color: ${color}`);
    }
  });

  // Check color contrast (simplified check)
  const contrastPairs = [
    ['text', 'background'],
    ['text', 'surface'],
    ['primary', 'background'],
    ['textSecondary', 'background'],
  ];

  contrastPairs.forEach(([foreground, background]) => {
    const fgColor = theme.colors[foreground as keyof typeof theme.colors];
    const bgColor = theme.colors[background as keyof typeof theme.colors];
    
    if (fgColor && bgColor) {
      // Simple contrast check (would need proper color parsing in real implementation)
      if (fgColor === bgColor) {
        errors.push(`No contrast between ${foreground} and ${background}`);
      }
    }
  });

  // Check for hardcoded opacity values
  Object.entries(theme.colors).forEach(([key, value]) => {
    if (typeof value === 'string' && value.includes('rgba') && !value.includes('10')) {
      warnings.push(`Color ${key} uses custom opacity, consider using standard opacity values`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions,
  };
};

// =====================================================
// SPACING VALIDATION
// =====================================================

export const validateSpacing = (theme: Theme): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Check required spacing values
  const requiredSpacing = ['xs', 'sm', 'md', 'lg', 'xl'];
  
  requiredSpacing.forEach(size => {
    if (!(size in theme.spacing)) {
      errors.push(`Missing required spacing: ${size}`);
    }
  });

  // Check spacing progression
  const spacingValues = Object.values(theme.spacing).filter(v => typeof v === 'number');
  const sortedSpacing = [...spacingValues].sort((a, b) => a - b);
  
  if (JSON.stringify(spacingValues.sort()) !== JSON.stringify(sortedSpacing)) {
    warnings.push('Spacing values are not in ascending order');
  }

  // Check for reasonable spacing values (4px increments)
  Object.entries(theme.spacing).forEach(([key, value]) => {
    if (typeof value === 'number' && value % 4 !== 0) {
      suggestions.push(`Spacing ${key} (${value}px) should be a multiple of 4 for better alignment`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions,
  };
};

// =====================================================
// TYPOGRAPHY VALIDATION
// =====================================================

export const validateTypography = (theme: Theme): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Check required typography variants
  const requiredVariants = ['h1', 'h2', 'h3', 'body', 'caption', 'button'];
  
  requiredVariants.forEach(variant => {
    if (!(variant in theme.typography)) {
      errors.push(`Missing required typography variant: ${variant}`);
    }
  });

  // Check typography structure
  Object.entries(theme.typography).forEach(([variant, style]) => {
    if (!style.fontFamily) {
      errors.push(`Typography variant ${variant} missing fontFamily`);
    }
    if (!style.fontSize) {
      errors.push(`Typography variant ${variant} missing fontSize`);
    }
    if (!style.lineHeight) {
      warnings.push(`Typography variant ${variant} missing lineHeight`);
    }
  });

  // Check font size progression
  const headingVariants = ['h1', 'h2', 'h3', 'h4'];
  const headingSizes = headingVariants
    .filter(variant => variant in theme.typography)
    .map(variant => theme.typography[variant as keyof typeof theme.typography].fontSize)
    .filter(size => typeof size === 'number');

  if (headingSizes.length > 1) {
    const isDescending = headingSizes.every((size, i) => 
      i === 0 || size <= headingSizes[i - 1]
    );
    
    if (!isDescending) {
      warnings.push('Heading font sizes should decrease from h1 to h4');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions,
  };
};

// =====================================================
// ACCESSIBILITY VALIDATION
// =====================================================

export const validateAccessibility = (theme: Theme): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Check minimum touch target sizes in theme
  if (theme.spacing.lg < 44) {
    warnings.push('Large spacing should be at least 44px for adequate touch targets');
  }

  // Check for proper focus indicators
  if (!theme.colors.inputFocus) {
    warnings.push('Missing inputFocus color for accessibility');
  }

  // Check shadow accessibility
  if (!theme.shadows || Object.keys(theme.shadows).length === 0) {
    warnings.push('Missing shadow system for visual hierarchy');
  }

  // Suggest accessibility improvements
  suggestions.push('Ensure all interactive elements have minimum 44px touch targets');
  suggestions.push('Test color combinations with accessibility tools');
  suggestions.push('Provide alternative indicators beyond color for status');

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions,
  };
};

// =====================================================
// STYLE VALIDATION
// =====================================================

export const validateStyle = (
  style: ViewStyle | TextStyle,
  theme: Theme,
  componentName: string
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Check for hardcoded values
  Object.entries(style).forEach(([property, value]) => {
    if (typeof value === 'number') {
      // Check spacing properties
      if (['padding', 'margin', 'paddingHorizontal', 'paddingVertical', 
           'marginHorizontal', 'marginVertical', 'paddingTop', 'paddingBottom',
           'paddingLeft', 'paddingRight', 'marginTop', 'marginBottom',
           'marginLeft', 'marginRight'].includes(property)) {
        
        const spacingValues = Object.values(theme.spacing);
        if (!spacingValues.includes(value)) {
          warnings.push(`${componentName}: ${property} uses hardcoded value ${value}, consider using theme.spacing`);
        }
      }

      // Check border radius
      if (['borderRadius', 'borderTopLeftRadius', 'borderTopRightRadius',
           'borderBottomLeftRadius', 'borderBottomRightRadius'].includes(property)) {
        
        const radiusValues = Object.values(theme.borderRadius);
        if (!radiusValues.includes(value)) {
          warnings.push(`${componentName}: ${property} uses hardcoded value ${value}, consider using theme.borderRadius`);
        }
      }
    }

    // Check for hardcoded colors
    if (typeof value === 'string' && (value.startsWith('#') || value.startsWith('rgb'))) {
      const themeColors = Object.values(theme.colors);
      if (!themeColors.includes(value)) {
        errors.push(`${componentName}: ${property} uses hardcoded color ${value}, use theme.colors instead`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions,
  };
};

// =====================================================
// COMPREHENSIVE VALIDATION
// =====================================================

export const validateThemeConsistency = (
  theme: Theme,
  options: Partial<StyleValidationOptions> = {}
): ValidationResult => {
  const defaultOptions: StyleValidationOptions = {
    checkColors: true,
    checkSpacing: true,
    checkTypography: true,
    checkAccessibility: true,
    ...options,
  };

  const results: ValidationResult[] = [];

  if (defaultOptions.checkColors) {
    results.push(validateColors(theme));
  }

  if (defaultOptions.checkSpacing) {
    results.push(validateSpacing(theme));
  }

  if (defaultOptions.checkTypography) {
    results.push(validateTypography(theme));
  }

  if (defaultOptions.checkAccessibility) {
    results.push(validateAccessibility(theme));
  }

  // Combine all results
  const combinedResult: ValidationResult = {
    isValid: results.every(r => r.isValid),
    errors: results.flatMap(r => r.errors),
    warnings: results.flatMap(r => r.warnings),
    suggestions: results.flatMap(r => r.suggestions),
  };

  return combinedResult;
};

// =====================================================
// DEVELOPMENT HELPERS
// =====================================================

export const logValidationResults = (result: ValidationResult, componentName?: string) => {
  const prefix = componentName ? `[${componentName}] ` : '';
  
  if (result.errors.length > 0) {
    console.error(`${prefix}Theme Validation Errors:`, result.errors);
  }
  
  if (result.warnings.length > 0) {
    console.warn(`${prefix}Theme Validation Warnings:`, result.warnings);
  }
  
  if (result.suggestions.length > 0) {
    console.info(`${prefix}Theme Validation Suggestions:`, result.suggestions);
  }
  
  if (result.isValid) {
    console.log(`${prefix}Theme validation passed ✅`);
  }
};

export const createValidationHook = (theme: Theme) => {
  return (componentName: string, style: ViewStyle | TextStyle) => {
    if (__DEV__) {
      const result = validateStyle(style, theme, componentName);
      logValidationResults(result, componentName);
    }
  };
};
