import { Platform } from "react-native";
import { ReceiptData } from "../components/receipt/ReceiptGenerator";
import { EnhancedThermalPrintService } from "./EnhancedThermalPrintService";
import { WebPrintService, WebPrintResult } from "./WebPrintService";
import { T220MDPrintService, T220MDPrintResult } from "./T220MDPrintService";
import {
  StandardizedReceiptService,
  StandardizedReceiptData,
} from "./StandardizedReceiptService";

export interface CrossPlatformPrintResult {
  success: boolean;
  error?: string;
  platform: "mobile" | "web";
  method?: "thermal" | "browser" | "pdf" | "fallback";
}

export interface PrintConfiguration {
  // Web-specific settings
  webPrinter?: {
    type: "thermal" | "standard" | "network";
    endpoint?: string; // For thermal printer API
    width?: number;
  };

  // Mobile-specific settings
  mobilePrinter?: {
    type: "ble" | "usb" | "net";
    autoConnect?: boolean;
  };

  // General settings
  fallbackToPDF?: boolean;
  showPrintDialog?: boolean;
}

/**
 * Cross-platform print service that automatically handles printing
 * based on the current platform (mobile vs web)
 *
 * @deprecated Use UnifiedReceiptManager instead
 * @see UnifiedReceiptManager for the modern, standardized receipt system
 *
 * Migration example:
 * ```typescript
 * // Old way:
 * const result = await CrossPlatformPrintService.printReceipt(receiptData);
 *
 * // New way:
 * const result = await UnifiedReceiptManager.generateReceipt(orderData, {
 *   format: "thermal",
 *   autoPrint: true,
 *   printerType: "thermal"
 * });
 * ```
 */
export class CrossPlatformPrintService {
  private static config: PrintConfiguration = {
    webPrinter: {
      type: "thermal", // Changed to thermal for better alignment
      width: 32,
    },
    mobilePrinter: {
      type: "ble",
      autoConnect: true,
    },
    fallbackToPDF: true,
    showPrintDialog: true,
  };

  /**
   * Initialize the cross-platform print service
   */
  static async init(config?: PrintConfiguration): Promise<boolean> {
    try {
      if (config) {
        this.config = { ...this.config, ...config };
      }

      if (Platform.OS === "web") {
        // Initialize T220MD print service
        await T220MDPrintService.init();

        // Initialize web print service
        return await WebPrintService.init(this.config.webPrinter);
      } else {
        // Initialize mobile thermal print service
        return await EnhancedThermalPrintService.init();
      }
    } catch (error) {
      console.error("Error initializing cross-platform print service:", error);
      return false;
    }
  }

  /**
   * Check if printing is available on current platform
   */
  static async isPrintingAvailable(): Promise<boolean> {
    try {
      if (Platform.OS === "web") {
        return WebPrintService.isAvailable();
      } else {
        return await EnhancedThermalPrintService.isThermalPrinterAvailable();
      }
    } catch (error) {
      console.error("Error checking print availability:", error);
      return false;
    }
  }

  /**
   * Print receipt using the appropriate method for current platform
   *
   * @deprecated Use UnifiedReceiptManager.generateReceipt() instead
   * @see UnifiedReceiptManager for the modern, standardized receipt system
   */
  static async printReceipt(
    receiptData: ReceiptData
  ): Promise<CrossPlatformPrintResult> {
    try {
      if (Platform.OS === "web") {
        return await this.printReceiptWeb(receiptData);
      } else {
        return await this.printReceiptMobile(receiptData);
      }
    } catch (error) {
      console.error("Cross-platform print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Print failed",
        platform: Platform.OS === "web" ? "web" : "mobile",
      };
    }
  }

  /**
   * Print standardized receipt using the new standardized format
   *
   * @deprecated Use UnifiedReceiptManager.generateReceipt() instead
   * @see UnifiedReceiptManager for the modern, standardized receipt system
   */
  static async printStandardizedReceipt(
    receiptData: StandardizedReceiptData
  ): Promise<CrossPlatformPrintResult> {
    try {
      console.log(
        `CrossPlatformPrintService: Starting standardized print on ${Platform.OS} platform`
      );

      if (Platform.OS === "web") {
        return await this.printStandardizedReceiptWeb(receiptData);
      } else {
        return await this.printStandardizedReceiptMobile(receiptData);
      }
    } catch (error) {
      console.error(
        "CrossPlatformPrintService standardized print error:",
        error
      );
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Standardized print failed",
        platform: Platform.OS === "web" ? "web" : "mobile",
      };
    }
  }

  /**
   * Print receipt on web platform - BROWSER PRINT ONLY
   */
  private static async printReceiptWeb(
    receiptData: ReceiptData
  ): Promise<CrossPlatformPrintResult> {
    try {
      console.log(
        "CrossPlatformPrintService: Using browser print dialog (web default)"
      );

      // WEB: Always use browser print dialog - no thermal printer connections
      const result: WebPrintResult = await WebPrintService.printReceipt(
        receiptData
      );

      return {
        success: result.success,
        error: result.error,
        platform: "web",
        method: result.method,
      };
    } catch (error) {
      console.error("Web print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Web print failed",
        platform: "web",
      };
    }
  }

  /**
   * Print receipt on mobile platform
   */
  private static async printReceiptMobile(
    receiptData: ReceiptData
  ): Promise<CrossPlatformPrintResult> {
    try {
      // Convert ReceiptData to OrderData format expected by EnhancedThermalPrintService
      const orderData = this.convertReceiptDataToOrderData(receiptData);

      const result = await EnhancedThermalPrintService.printReceipt(orderData);

      return {
        success: result.success,
        error: result.error,
        platform: "mobile",
        method: "thermal",
      };
    } catch (error) {
      console.error("Mobile print error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Mobile print failed",
        platform: "mobile",
      };
    }
  }

  /**
   * Print standardized receipt on web platform
   */
  private static async printStandardizedReceiptWeb(
    receiptData: StandardizedReceiptData
  ): Promise<CrossPlatformPrintResult> {
    try {
      console.log("CrossPlatformPrintService: Starting standardized web print");

      // Use the new standardized web printing method
      const result: WebPrintResult =
        await WebPrintService.printStandardizedReceipt(receiptData);

      return {
        success: result.success,
        error: result.error,
        platform: "web",
        method: result.method,
      };
    } catch (error) {
      console.error("Standardized web print error:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Standardized web print failed",
        platform: "web",
      };
    }
  }

  /**
   * Print standardized receipt on mobile platform
   */
  private static async printStandardizedReceiptMobile(
    receiptData: StandardizedReceiptData
  ): Promise<CrossPlatformPrintResult> {
    try {
      // Convert StandardizedReceiptData to OrderData format expected by EnhancedThermalPrintService
      const orderData =
        this.convertStandardizedReceiptDataToOrderData(receiptData);

      const result = await EnhancedThermalPrintService.printReceipt(orderData);

      return {
        success: result.success,
        error: result.error,
        platform: "mobile",
        method: "thermal",
      };
    } catch (error) {
      console.error("Standardized mobile print error:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Standardized mobile print failed",
        platform: "mobile",
      };
    }
  }

  /**
   * Convert ReceiptData to OrderData format
   */
  private static convertReceiptDataToOrderData(receiptData: ReceiptData): any {
    return {
      id: receiptData.orderNumber,
      orderNumber: receiptData.orderNumber,
      createdAt: receiptData.orderDate,
      total: receiptData.total,
      subtotal: receiptData.subtotal,
      tax: receiptData.tax,
      items: receiptData.items.map((item) => ({
        id: item.id,
        title: item.title,
        variantTitle: item.variantTitle,
        quantity: item.quantity,
        price: parseFloat(item.price),
        sku: item.sku,
      })),
      customer: receiptData.customer
        ? {
            id: receiptData.customer.id,
            name: receiptData.customer.name,
            email: receiptData.customer.email,
            phone: receiptData.customer.phone,
          }
        : null,
      salesAgent: receiptData.salesAgent
        ? {
            id: receiptData.salesAgent.id,
            name: receiptData.salesAgent.name,
          }
        : null,
      store: receiptData.store,
      // Enhanced payment information
      paymentMethod: receiptData.paymentMethod,
      paymentDetails: receiptData.paymentDetails,
      paymentBreakdown: receiptData.paymentBreakdown,
    };
  }

  /**
   * Convert StandardizedReceiptData to OrderData format
   */
  private static convertStandardizedReceiptDataToOrderData(
    receiptData: StandardizedReceiptData
  ): any {
    return {
      id: receiptData.receiptNumber,
      orderNumber: receiptData.receiptNumber,
      createdAt: new Date(
        `${receiptData.date} ${receiptData.time}`
      ).toISOString(),
      total: receiptData.totals.grandTotal,
      subtotal: receiptData.totals.subtotal,
      tax: receiptData.totals.tax,
      items: receiptData.items.map((item) => ({
        id: item.number,
        title: item.name,
        variantTitle: item.variant || undefined,
        quantity: item.quantity,
        price: item.unitPrice,
        sku: item.sku,
      })),
      customer: receiptData.customer
        ? {
            id: receiptData.customer.id || "unknown",
            name: receiptData.customer.name,
            email: receiptData.customer.email,
            phone: receiptData.customer.mobile,
          }
        : null,
      salesAgent: receiptData.salesAgent
        ? {
            id: receiptData.salesAgent.id || "unknown",
            name: receiptData.salesAgent.name,
          }
        : null,
      store: {
        name: receiptData.store.name,
        address: receiptData.store.address,
        phone: receiptData.store.mobile,
      },
      // Enhanced payment information
      paymentMethod: receiptData.payment.methods[0]?.name || "Cash",
      paymentDetails: {
        transactionId: receiptData.payment.methods[0]?.transactionId,
      },
      paymentBreakdown: receiptData.payment.methods,
      // Staff information
      staff: {
        name: receiptData.staff.name,
        role: receiptData.staff.role || "Staff",
      },
      // Loyalty information
      loyaltyPoints: receiptData.loyalty
        ? {
            earned: receiptData.loyalty.pointsEarned || 0,
            balance: receiptData.loyalty.totalPoints,
            membershipId: receiptData.customer?.id || "unknown",
          }
        : undefined,
    };
  }

  /**
   * Configure the print service
   */
  static configure(config: Partial<PrintConfiguration>): void {
    this.config = { ...this.config, ...config };

    // Apply web-specific configuration
    if (Platform.OS === "web" && config.webPrinter) {
      WebPrintService.configure(config.webPrinter);
    }
  }

  /**
   * Get current configuration
   */
  static getConfiguration(): PrintConfiguration {
    return { ...this.config };
  }

  /**
   * Get platform-specific printer status
   */
  static async getPrinterStatus(): Promise<{
    platform: "web" | "mobile";
    available: boolean;
    connected?: boolean;
    type?: string;
    device?: any;
  }> {
    try {
      if (Platform.OS === "web") {
        const available = WebPrintService.isAvailable();
        return {
          platform: "web",
          available,
          type: this.config.webPrinter?.type || "standard",
        };
      } else {
        const available =
          await EnhancedThermalPrintService.isThermalPrinterAvailable();
        const status = EnhancedThermalPrintService.getThermalPrinterStatus();

        return {
          platform: "mobile",
          available,
          connected: status.isConnected,
          type: "thermal",
          device: status.connectedDevice,
        };
      }
    } catch (error) {
      console.error("Error getting printer status:", error);
      return {
        platform: Platform.OS === "web" ? "web" : "mobile",
        available: false,
      };
    }
  }

  /**
   * Show platform-appropriate printer setup
   */
  static async showPrinterSetup(): Promise<boolean> {
    try {
      if (Platform.OS === "web") {
        // For web, show configuration modal or redirect to setup page
        console.log("Web printer setup - show configuration modal");
        return true;
      } else {
        // For mobile, use existing thermal printer setup
        console.log("Mobile printer setup - navigate to thermal printer setup");
        return true;
      }
    } catch (error) {
      console.error("Error showing printer setup:", error);
      return false;
    }
  }

  /**
   * Test print functionality
   */
  static async testPrint(): Promise<CrossPlatformPrintResult> {
    const testReceiptData: ReceiptData = {
      store: {
        name: "TREASURED SCENTS",
        address: "Greenhouse Mall, Ngong Road, Kenya",
        phone: "+***********-933",
      },
      orderNumber: "TEST-001",
      orderDate: new Date().toISOString(),
      items: [
        {
          id: "test-1",
          title: "Test Product",
          variantTitle: "Test Variant",
          quantity: 1,
          price: "10.00",
          sku: "TEST-SKU",
        },
      ],
      subtotal: 10.0,
      tax: 1.6,
      total: 11.6,
      paymentMethod: "Cash",
      paymentDetails: {
        transactionId: "TEST-TXN-001",
      },
      staff: {
        name: "Test Staff",
        role: "Cashier",
      },
      customer: {
        name: "Test Customer",
        email: "<EMAIL>",
        phone: "+*********** 001",
      },
      salesAgent: {
        name: "Test Sales Agent",
        territory: "Test Territory",
      },
    };

    return await this.printReceipt(testReceiptData);
  }
}
